# Utility functions

"""
Generate test data for development and testing
"""
function generate_test_data(num_draws::Int)::Vector{LotteryDraw}
    draws = LotteryDraw[]
    base_date = Date("2022-01-01")
    
    for i in 1:num_draws
        # Generate 5 unique random numbers between 1 and 39
        numbers = sort(sample(1:39, 5, replace=false))
        draw_date = base_date + Day(i-1)
        draw = LotteryDraw(numbers, draw_date, i)
        push!(draws, draw)
    end
    
    # Sort by date (newest first)
    sort!(draws, by = d -> d.draw_date, rev = true)
    return draws
end

"""
Load test data from file
"""
function load_test_data(filename::String)::Vector{LotteryDraw}
    fm = FileManager()
    return read_data5_file(fm, filename)
end

"""
Display strategy results in a readable format
"""
function display_strategy_results(result::StrategyResult)
    println("Wonder Grid Strategy Results")
    println("=" ^ 40)
    println("Key Number: $(result.key_number)")
    println("Total Combinations: $(length(result.combinations))")
    println("Generation Time: $(round(result.generation_time, digits=3)) seconds")
    println("Estimated Cost: \$$(result.estimated_cost)")
    println()
    println("Expected Efficiency Ratios:")
    for (tier, ratio) in result.expected_efficiency
        println("  $tier: $(ratio)x better than random")
    end
    println()
    println("First 10 combinations:")
    for (i, combo) in enumerate(result.combinations[1:min(10, length(result.combinations))])
        println("  $i: $(join(combo, "-"))")
    end
end

"""
Display skip chart information
"""
function display_skip_chart(chart::SkipChart)
    println("Skip Chart for Number $(chart.number)")
    println("=" ^ 30)
    println("Current Skip: $(chart.current_skip)")
    println("FFG Median: $(round(chart.ffg_median, digits=2))")
    println("Favorable Timing: $(chart.is_favorable ? "YES" : "NO")")
    println("Skip History: $(join(chart.skip_sequence[1:min(10, length(chart.skip_sequence))], ", "))")
    if length(chart.skip_sequence) > 10
        println("  ... and $(length(chart.skip_sequence) - 10) more")
    end
end

"""
Display pairing analysis results
"""
function display_pairing_analysis(engine::PairingEngine, number::Int)
    top_pairings = get_top_pairings(engine, number, 0.25)
    all_pairings = calculate_all_pairings(engine)
    
    println("Pairing Analysis for Number $number")
    println("=" ^ 35)
    println("Top 25% Pairings ($(length(top_pairings)) numbers):")
    
    for (i, paired_number) in enumerate(top_pairings)
        pair_key = number < paired_number ? (number, paired_number) : (paired_number, number)
        frequency = get(all_pairings, pair_key, 0)
        println("  $i. Number $paired_number (frequency: $frequency)")
    end
end