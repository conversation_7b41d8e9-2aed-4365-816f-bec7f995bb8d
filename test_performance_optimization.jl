#!/usr/bin/env julia

"""
Comprehensive test suite for performance optimization system
Tests optimized data structures, caching, memory management, and performance improvements
"""

using Dates

# Include required modules
include("src/wonder_grid_engine.jl")
include("src/backtesting.jl")
include("src/performance_optimization.jl")

"""
Test the performance optimization system
"""
function test_performance_optimization_system()
    println("🧪 Testing Performance Optimization System")
    println("=" ^ 70)
    
    # Test 1: Optimized data structures
    println("\n🏗️  Test 1: Optimized Data Structures")
    test_optimized_data_structures()
    
    # Test 2: Caching system
    println("\n💾 Test 2: Caching System")
    test_caching_system()
    
    # Test 3: Memory management
    println("\n🧠 Test 3: Memory Management")
    test_memory_management()
    
    # Test 4: Performance monitoring
    println("\n📊 Test 4: Performance Monitoring")
    test_performance_monitoring()
    
    # Test 5: Optimized algorithms
    println("\n⚡ Test 5: Optimized Algorithms")
    test_optimized_algorithms()
    
    # Test 6: Benchmark comparisons
    println("\n🏁 Test 6: Benchmark Comparisons")
    test_benchmark_comparisons()
    
    # Test 7: Memory profiling
    println("\n🔍 Test 7: Memory Profiling")
    test_memory_profiling()
    
    println("\n✅ Performance Optimization Tests Complete!")
    println("=" ^ 70)
end

"""
Test optimized data structures
"""
function test_optimized_data_structures()
    try
        # Test OptimizedCombination
        println("  Testing OptimizedCombination...")
        
        test_numbers = [1, 5, 12, 23, 39]
        opt_combo = OptimizedCombination(test_numbers)
        
        # Verify storage efficiency
        @assert sizeof(opt_combo.numbers) == 5  # 5 bytes vs 40+ bytes for Vector{Int}
        
        # Test conversion back to vector
        converted = convert(Vector{Int}, opt_combo)
        @assert converted == sort(test_numbers)
        
        println("  ✅ OptimizedCombination working correctly")
        
        # Test invalid combinations
        try
            OptimizedCombination([1, 2, 3, 4, 50])  # Invalid number > 39
            @assert false  # Should not reach here
        catch ArgumentError
            println("  ✅ Invalid combination detection working")
        end
        
        # Test OptimizedPairingCalculator
        println("  Testing OptimizedPairingCalculator...")
        
        calc = OptimizedPairingCalculator()
        @assert calc.total_pairs == 0
        @assert calc.cache_hits == 0
        @assert calc.cache_misses == 0
        
        # Add test combinations
        add_combination!(calc, [1, 5, 12, 23, 39])
        add_combination!(calc, [3, 7, 15, 28, 35])
        
        @assert calc.total_pairs == 20  # 2 combinations × 10 pairs each
        
        # Test frequency retrieval
        freq = get_pairing_frequency(calc, 1, 5)
        @assert freq >= 0
        
        println("  ✅ OptimizedPairingCalculator working correctly")
        
        # Test OptimizedLotteryData
        println("  Testing OptimizedLotteryData...")
        
        test_draws = [
            LotteryDraw(Date("2023-01-01"), [1, 5, 12, 23, 39]),
            LotteryDraw(Date("2023-01-02"), [3, 7, 15, 28, 35])
        ]
        
        opt_data = OptimizedLotteryData(test_draws)
        @assert length(opt_data.draws) == 2
        @assert length(opt_data.dates) == 2
        @assert length(opt_data.number_frequency) == 39
        
        # Test frequency lookup
        freq_1 = get_number_frequency(opt_data, 1)
        @assert freq_1 == 1  # Number 1 appears once
        
        freq_invalid = get_number_frequency(opt_data, 50)
        @assert freq_invalid == 0  # Invalid number
        
        println("  ✅ OptimizedLotteryData working correctly")
        
    catch e
        println("  ❌ Error in optimized data structures: $e")
        rethrow(e)
    end
end

"""
Test caching system
"""
function test_caching_system()
    try
        # Clear caches for clean test
        clear_all_caches!()
        
        println("  Testing FFG caching...")
        
        # Test FFG caching
        key_number = 13
        
        # First call should cache the result
        ffg1 = calculate_ffg_optimized(key_number)
        @assert !isempty(ffg1)
        
        # Second call should use cache
        ffg2 = calculate_ffg_optimized(key_number)
        @assert ffg1 == ffg2
        
        # Verify cache contains the result
        @assert haskey(FFG_CACHE, key_number)
        
        println("  ✅ FFG caching working correctly")
        
        # Test pairing frequency caching
        println("  Testing pairing frequency caching...")
        
        calc = OptimizedPairingCalculator()
        add_combination!(calc, [1, 5, 12, 23, 39])
        
        # First call should miss cache
        initial_misses = calc.cache_misses
        freq1 = get_pairing_frequency(calc, 1, 5)
        @assert calc.cache_misses == initial_misses + 1
        
        # Second call should hit cache
        initial_hits = calc.cache_hits
        freq2 = get_pairing_frequency(calc, 1, 5)
        @assert calc.cache_hits == initial_hits + 1
        @assert freq1 == freq2
        
        println("  ✅ Pairing frequency caching working correctly")
        
        # Test cache statistics
        println("  Testing cache statistics...")
        
        cache_stats = get_cache_stats()
        @assert haskey(cache_stats, "calculation_cache_size")
        @assert haskey(cache_stats, "ffg_cache_size")
        @assert haskey(cache_stats, "pairing_frequency_cache_size")
        @assert cache_stats["ffg_cache_size"] >= 1  # Should have at least our test entry
        
        println("  ✅ Cache statistics working correctly")
        
        # Test cache clearing
        println("  Testing cache clearing...")
        
        clear_all_caches!()
        cache_stats_after = get_cache_stats()
        @assert cache_stats_after["total_cached_items"] == 0
        
        println("  ✅ Cache clearing working correctly")
        
    catch e
        println("  ❌ Error in caching system: $e")
        rethrow(e)
    end
end

"""
Test memory management
"""
function test_memory_management()
    try
        # Test memory pool
        println("  Testing memory pool...")
        
        pool = MemoryPool{Vector{Int}}(() -> Vector{Int}(), empty!)
        
        # Acquire object from empty pool (should create new)
        obj1 = acquire!(pool)
        @assert isa(obj1, Vector{Int})
        @assert isempty(obj1)
        
        # Use and modify object
        push!(obj1, 1, 2, 3)
        @assert length(obj1) == 3
        
        # Return to pool
        release!(pool, obj1)
        @assert length(pool.pool) == 1
        
        # Acquire again (should reuse and reset)
        obj2 = acquire!(pool)
        @assert obj2 === obj1  # Same object
        @assert isempty(obj2)  # Should be reset
        
        println("  ✅ Memory pool working correctly")
        
        # Test OptimizedCombinationGenerator
        println("  Testing optimized combination generator...")
        
        generator = OptimizedCombinationGenerator(7, 100)  # Small batch size for testing
        @assert generator.key_number == 7
        @assert generator.batch_size == 100
        @assert !isempty(generator.ffg_numbers)
        
        # Test batch generation
        batch_count = 0
        total_combinations = 0
        
        for batch in generate_combinations_batch(generator)
            batch_count += 1
            total_combinations += length(batch)
            
            # Verify batch contents
            for opt_combo in batch
                @assert isa(opt_combo, OptimizedCombination)
                combo_vec = convert(Vector{Int}, opt_combo)
                @assert length(combo_vec) == 5
                @assert all(n -> 1 <= n <= 39, combo_vec)
            end
            
            # Limit test to avoid excessive processing
            if batch_count >= 3
                break
            end
        end
        
        @assert batch_count > 0
        @assert total_combinations > 0
        
        println("  ✅ Optimized combination generator working correctly")
        
    catch e
        println("  ❌ Error in memory management: $e")
        rethrow(e)
    end
end

"""
Test performance monitoring
"""
function test_performance_monitoring()
    try
        println("  Testing performance monitor...")
        
        monitor = PerformanceMonitor()
        @assert monitor.start_time > 0
        @assert isa(monitor.operation_counts, Dict)
        @assert isa(monitor.timing_data, Dict)
        
        # Test operation timing
        operation_name = "test_operation"
        start_time = start_operation!(monitor, operation_name)
        
        # Simulate work
        sleep(0.01)
        
        end_operation!(monitor, operation_name, start_time)
        
        # Verify timing was recorded
        @assert haskey(monitor.timing_data, operation_name)
        @assert haskey(monitor.operation_counts, operation_name)
        @assert monitor.operation_counts[operation_name] == 1
        @assert length(monitor.timing_data[operation_name]) == 1
        @assert monitor.timing_data[operation_name][1] > 0
        
        println("  ✅ Performance monitoring working correctly")
        
        # Test performance statistics
        println("  Testing performance statistics...")
        
        stats = get_performance_stats(monitor)
        @assert haskey(stats, "total_runtime")
        @assert haskey(stats, "memory_used_bytes")
        @assert haskey(stats, "operations")
        @assert haskey(stats["operations"], operation_name)
        
        op_stats = stats["operations"][operation_name]
        @assert haskey(op_stats, "count")
        @assert haskey(op_stats, "average_time")
        @assert op_stats["count"] == 1
        
        println("  ✅ Performance statistics working correctly")
        
    catch e
        println("  ❌ Error in performance monitoring: $e")
        rethrow(e)
    end
end

"""
Test optimized algorithms
"""
function test_optimized_algorithms()
    try
        println("  Testing optimized Wonder Grid engine...")
        
        # Create optimized engine
        opt_engine = OptimizedWonderGridEngine()
        @assert isa(opt_engine.base_engine, WonderGridEngine)
        @assert isa(opt_engine.pairing_calculator, OptimizedPairingCalculator)
        @assert isa(opt_engine.performance_monitor, PerformanceMonitor)
        
        # Test optimized combination generation
        key_number = 7
        combinations = generate_combinations_optimized(opt_engine, key_number)
        
        @assert isa(combinations, Vector{Vector{Int}})
        @assert !isempty(combinations)
        
        # Verify all combinations are valid
        for combo in combinations
            @assert length(combo) == 5
            @assert all(n -> 1 <= n <= 39, combo)
            @assert length(unique(combo)) == 5  # No duplicates
            @assert issorted(combo)  # Should be sorted
        end
        
        println("  ✅ Optimized combination generation working correctly")
        
        # Test optimized backtesting
        println("  Testing optimized backtesting...")
        
        test_draws = [
            LotteryDraw(Date("2023-01-01"), [1, 5, 12, 23, 39]),
            LotteryDraw(Date("2023-01-02"), [3, 7, 15, 28, 35])
        ]
        
        # Use smaller subset for testing
        test_combinations = combinations[1:min(10, length(combinations))]
        
        backtest_result = run_backtest_optimized(opt_engine, test_combinations, test_draws)
        
        @assert isa(backtest_result, BacktestResult)
        @assert haskey(backtest_result.hit_rates, "3/5")
        @assert haskey(backtest_result.hit_rates, "4/5")
        @assert haskey(backtest_result.hit_rates, "5/5")
        
        # Verify hit rates are valid probabilities
        for (tier, rate) in backtest_result.hit_rates
            @assert 0.0 <= rate <= 1.0
        end
        
        println("  ✅ Optimized backtesting working correctly")
        
        # Check performance monitoring was active
        perf_stats = get_performance_stats(opt_engine.performance_monitor)
        @assert haskey(perf_stats["operations"], "combination_generation")
        @assert haskey(perf_stats["operations"], "backtesting")
        
        println("  ✅ Performance monitoring integration working correctly")
        
    catch e
        println("  ❌ Error in optimized algorithms: $e")
        rethrow(e)
    end
end

"""
Test benchmark comparisons
"""
function test_benchmark_comparisons()
    try
        println("  Testing benchmark system...")
        
        # Run small benchmark for testing
        key_number = 7
        iterations = 5  # Small number for testing
        
        benchmark_results = benchmark_performance(key_number, iterations)
        
        # Verify benchmark structure
        @assert haskey(benchmark_results, "standard_engine")
        @assert haskey(benchmark_results, "optimized_engine")
        @assert haskey(benchmark_results, "improvement_percentage")
        @assert haskey(benchmark_results, "speedup_factor")
        @assert haskey(benchmark_results, "iterations")
        
        # Verify standard engine results
        standard_results = benchmark_results["standard_engine"]
        @assert haskey(standard_results, "average_time")
        @assert haskey(standard_results, "min_time")
        @assert haskey(standard_results, "max_time")
        @assert standard_results["average_time"] > 0
        
        # Verify optimized engine results
        optimized_results = benchmark_results["optimized_engine"]
        @assert haskey(optimized_results, "average_time")
        @assert haskey(optimized_results, "min_time")
        @assert haskey(optimized_results, "max_time")
        @assert optimized_results["average_time"] > 0
        
        # Verify improvement metrics
        @assert isa(benchmark_results["improvement_percentage"], Float64)
        @assert isa(benchmark_results["speedup_factor"], Float64)
        @assert benchmark_results["iterations"] == iterations
        
        println("  ✅ Benchmark system working correctly")
        println("  📊 Speedup factor: $(round(benchmark_results["speedup_factor"], digits=2))x")
        println("  📈 Improvement: $(round(benchmark_results["improvement_percentage"], digits=1))%")
        
    catch e
        println("  ❌ Error in benchmark comparisons: $e")
        rethrow(e)
    end
end

"""
Test memory profiling
"""
function test_memory_profiling()
    try
        println("  Testing memory profiling...")
        
        # Test memory profiling with a simple operation
        test_operation = function(n::Int)
            return [i^2 for i in 1:n]
        end
        
        profile_result = profile_memory_usage(test_operation, 1000)
        
        # Verify profile structure
        @assert haskey(profile_result, "memory_used_bytes")
        @assert haskey(profile_result, "memory_used_mb")
        @assert haskey(profile_result, "execution_time")
        @assert haskey(profile_result, "result_size")
        @assert haskey(profile_result, "memory_per_item")
        
        # Verify values are reasonable
        @assert profile_result["memory_used_bytes"] >= 0
        @assert profile_result["execution_time"] > 0
        @assert profile_result["result_size"] == 1000
        
        println("  ✅ Memory profiling working correctly")
        println("  💾 Memory used: $(round(profile_result["memory_used_mb"], digits=2)) MB")
        println("  ⏱️  Execution time: $(round(profile_result["execution_time"] * 1000, digits=2)) ms")
        
        # Test with Wonder Grid operation
        println("  Testing memory profiling with Wonder Grid operation...")
        
        wg_operation = function(key_num::Int)
            engine = WonderGridEngine()
            return generate_combinations(engine, key_num)
        end
        
        wg_profile = profile_memory_usage(wg_operation, 7)
        
        @assert wg_profile["memory_used_bytes"] >= 0
        @assert wg_profile["execution_time"] > 0
        @assert wg_profile["result_size"] > 0
        
        println("  ✅ Wonder Grid memory profiling working correctly")
        println("  📊 Combinations generated: $(wg_profile["result_size"])")
        println("  💾 Memory per combination: $(round(wg_profile["memory_per_item"], digits=0)) bytes")
        
    catch e
        println("  ❌ Error in memory profiling: $e")
        rethrow(e)
    end
end

"""
Test combinations function
"""
function test_combinations_function()
    println("\n🔢 Test: Combinations Function")
    
    try
        # Test basic combinations
        arr = [1, 2, 3, 4, 5]
        combos = combinations(arr, 3)
        
        @assert length(combos) == binomial(5, 3)  # Should be 10
        @assert all(combo -> length(combo) == 3, combos)
        @assert all(combo -> all(x -> x in arr, combo), combos)
        
        println("  ✅ Basic combinations working correctly")
        
        # Test edge cases
        empty_combos = combinations(arr, 0)
        @assert isempty(empty_combos)
        
        invalid_combos = combinations(arr, 6)  # k > n
        @assert isempty(invalid_combos)
        
        println("  ✅ Edge cases handled correctly")
        
        # Test with actual FFG numbers
        ffg_numbers = [1, 7, 14, 21, 28, 35]
        ffg_combos = combinations(ffg_numbers, 5)
        
        expected_count = binomial(6, 5)  # Should be 6
        @assert length(ffg_combos) == expected_count
        
        println("  ✅ FFG combinations working correctly")
        
    catch e
        println("  ❌ Error in combinations function: $e")
        rethrow(e)
    end
end

# Run the tests
if abspath(PROGRAM_FILE) == @__FILE__
    test_performance_optimization_system()
    test_combinations_function()
end