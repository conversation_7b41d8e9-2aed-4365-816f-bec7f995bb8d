# TWO Filter Tests
# TWO 過濾器測試

using Test
using Dates

# 引入必要的模組
include("../src/types.jl")
include("../src/filter_engine.jl")
include("../src/filters/two_filter.jl")

"""
創建測試用的彩票數據
"""
function create_test_lottery_data_for_pairs()::Vector{LotteryDraw}
    test_draws = [
        LotteryDraw([1, 2, 10, 15, 20], Date(2022, 1, 10), 10),  # 最新，包含配對 (1,2)
        LotteryDraw([3, 6, 11, 16, 21], Date(2022, 1, 9), 9),
        LotteryDraw([1, 2, 12, 17, 22], Date(2022, 1, 8), 8),   # 包含配對 (1,2)
        LotteryDraw([4, 8, 13, 18, 23], Date(2022, 1, 7), 7),
        LotteryDraw([5, 9, 14, 19, 24], Date(2022, 1, 6), 6),
        LotteryDraw([1, 3, 15, 20, 25], Date(2022, 1, 5), 5),   # 包含配對 (1,3)
        LotteryDraw([2, 7, 16, 21, 26], Date(2022, 1, 4), 4),
        LotteryDraw([6, 12, 17, 22, 27], Date(2022, 1, 3), 3),
        LotteryDraw([1, 2, 18, 23, 28], Date(2022, 1, 2), 2),   # 包含配對 (1,2)
        LotteryDraw([8, 14, 19, 24, 29], Date(2022, 1, 1), 1),  # 最舊
    ]
    return test_draws
end

"""
測試配對生成功能
"""
function test_pair_generation()
    @testset "Pair Generation" begin
        # 測試正常情況
        numbers = [1, 5, 10]
        pairs = generate_pairs(numbers)
        expected_pairs = [(1, 5), (1, 10), (5, 10)]
        @test length(pairs) == 3
        @test all(pair in expected_pairs for pair in pairs)
        
        # 測試邊界條件
        @test isempty(generate_pairs([1]))  # 只有一個號碼
        @test isempty(generate_pairs(Int[]))  # 空數組
        
        # 測試兩個號碼
        two_numbers = [5, 2]
        two_pairs = generate_pairs(two_numbers)
        @test length(two_pairs) == 1
        @test two_pairs[1] == (2, 5)  # 應該按升序排列
        
        println("✓ 配對生成測試通過")
    end
end

"""
測試配對頻率計算
"""
function test_pair_frequency_calculation()
    @testset "Pair Frequency Calculation" begin
        test_data = create_test_lottery_data_for_pairs()
        engine = FilterEngine(test_data)
        
        # 測試配對 (1,2) 的頻率 - 應該出現 3 次
        freq_1_2 = calculate_pair_frequency(engine, (1, 2))
        @test freq_1_2 == 3
        
        # 測試配對 (1,3) 的頻率 - 應該出現 1 次
        freq_1_3 = calculate_pair_frequency(engine, (1, 3))
        @test freq_1_3 == 1
        
        # 測試不存在的配對
        freq_5_6 = calculate_pair_frequency(engine, (5, 6))
        @test freq_5_6 == 0
        
        println("✓ 配對頻率計算測試通過")
    end
end

"""
測試配對 Skip 計算
"""
function test_pair_skip_calculation()
    @testset "Pair Skip Calculation" begin
        test_data = create_test_lottery_data_for_pairs()
        engine = FilterEngine(test_data)
        
        # 測試配對 (1,2) 的 Skip - 最後在第 10 次開獎出現
        skip_1_2 = calculate_pair_skip(engine, (1, 2))
        @test skip_1_2 == 0  # 最新開獎就有出現
        
        # 測試配對 (1,3) 的 Skip - 最後在索引 6 出現
        skip_1_3 = calculate_pair_skip(engine, (1, 3))
        @test skip_1_3 == 5  # 索引 6 - 1 = 5
        
        # 測試從未出現的配對
        skip_never = calculate_pair_skip(engine, (30, 35))
        @test skip_never == length(test_data)
        
        println("✓ 配對 Skip 計算測試通過")
    end
end

"""
測試 TWO 過濾器計算
"""
function test_two_filter_calculation()
    @testset "TWO Filter Calculation" begin
        test_data = create_test_lottery_data_for_pairs()
        engine = FilterEngine(test_data)
        
        # 測試包含多個配對的號碼組合
        test_numbers = [1, 2, 3]
        result = calculate_two_filter(engine, test_numbers)
        
        # 驗證結果結構
        @test result.filter_type == TWO_FILTER
        @test occursin("TWO_FILTER", result.filter_name)
        @test isa(result.current_value, Int)
        @test isa(result.expected_value, Float64)
        @test isa(result.is_favorable, Bool)
        @test 0.0 <= result.confidence_level <= 1.0
        @test isa(result.historical_values, Vector{Int})
        @test result.calculation_time >= 0.0
        
        # 驗證配對數量 - C(3,2) = 3
        @test result.current_value == 3
        
        println("✓ TWO 過濾器計算測試通過")
        println("  - 當前配對數: $(result.current_value)")
        println("  - 期望配對數: $(round(result.expected_value, digits=2))")
        println("  - 是否有利: $(result.is_favorable)")
        println("  - 信心水準: $(round(result.confidence_level, digits=2))")
    end
end

"""
測試期望配對數量計算
"""
function test_expected_pairs_calculation()
    @testset "Expected Pairs Calculation" begin
        test_data = create_test_lottery_data_for_pairs()
        engine = FilterEngine(test_data)
        
        # 測試不同數量號碼的期望配對數
        @test calculate_expected_pairs_count(engine, [1]) == 0.0  # 1個號碼無配對
        
        expected_2 = calculate_expected_pairs_count(engine, [1, 2])
        @test expected_2 > 0.0  # 2個號碼有期望配對
        
        expected_5 = calculate_expected_pairs_count(engine, [1, 2, 3, 4, 5])
        @test expected_5 > expected_2  # 更多號碼有更多期望配對
        
        println("✓ 期望配對數量計算測試通過")
    end
end

"""
測試特定配對分析
"""
function test_specific_pair_analysis()
    @testset "Specific Pair Analysis" begin
        test_data = create_test_lottery_data_for_pairs()
        engine = FilterEngine(test_data)
        
        # 分析配對 (1,2)
        analysis = analyze_specific_pair(engine, (1, 2))
        
        # 驗證分析結果結構
        @test haskey(analysis, "pair")
        @test haskey(analysis, "frequency")
        @test haskey(analysis, "skip")
        @test haskey(analysis, "expected_frequency")
        @test haskey(analysis, "score")
        @test haskey(analysis, "is_above_average")
        
        @test analysis["pair"] == (1, 2)
        @test analysis["frequency"] == 3
        @test analysis["skip"] == 0
        
        println("✓ 特定配對分析測試通過")
    end
end

"""
測試頂級配對識別
"""
function test_top_performing_pairs()
    @testset "Top Performing Pairs" begin
        test_data = create_test_lottery_data_for_pairs()
        engine = FilterEngine(test_data)
        
        # 獲取號碼 [1,2,3] 中表現最佳的配對
        test_numbers = [1, 2, 3]
        top_pairs = get_top_performing_pairs(engine, test_numbers, 3)
        
        @test length(top_pairs) == 3  # 應該有 3 個配對
        @test all(haskey(pair, "score") for pair in top_pairs)
        
        # 驗證排序（分數應該遞減）
        for i in 2:length(top_pairs)
            @test top_pairs[i-1]["score"] >= top_pairs[i]["score"]
        end
        
        println("✓ 頂級配對識別測試通過")
    end
end

"""
測試 TWO 過濾器統計摘要
"""
function test_two_filter_summary()
    @testset "TWO Filter Summary" begin
        test_data = create_test_lottery_data_for_pairs()
        engine = FilterEngine(test_data)
        
        # 計算多個號碼組合的 TWO 過濾器結果
        test_combinations = [
            [1, 2],
            [1, 2, 3],
            [1, 2, 3, 4],
            [5, 6, 7]
        ]
        
        results = FilterResult[]
        for combo in test_combinations
            result = calculate_two_filter(engine, combo)
            push!(results, result)
        end
        
        # 獲取統計摘要
        summary = get_two_filter_summary(results)
        
        # 驗證摘要結構
        @test haskey(summary, "total_combinations")
        @test haskey(summary, "favorable_combinations")
        @test haskey(summary, "average_confidence")
        @test haskey(summary, "average_pair_count")
        @test haskey(summary, "pair_count_distribution")
        
        @test summary["total_combinations"] == length(test_combinations)
        
        println("✓ TWO 過濾器統計摘要測試通過")
        println("  - 總組合數: $(summary["total_combinations"])")
        println("  - 有利組合數: $(summary["favorable_combinations"])")
        println("  - 平均信心水準: $(round(summary["average_confidence"], digits=2))")
    end
end

"""
測試快取功能
"""
function test_two_filter_caching()
    @testset "TWO Filter Caching" begin
        test_data = create_test_lottery_data_for_pairs()
        engine = FilterEngine(test_data, cache_enabled=true)
        
        test_numbers = [1, 2, 3]
        
        # 第一次計算
        result1 = calculate_two_filter(engine, test_numbers)
        cache_size_after_first = length(engine.filter_cache)
        
        # 第二次計算（應該使用快取）
        result2 = calculate_two_filter(engine, test_numbers)
        cache_size_after_second = length(engine.filter_cache)
        
        # 驗證快取工作
        @test cache_size_after_first == 1
        @test cache_size_after_second == 1  # 快取大小不變
        @test result1.current_value == result2.current_value
        @test result1.expected_value == result2.expected_value
        
        println("✓ TWO 過濾器快取功能測試通過")
    end
end

"""
測試錯誤處理
"""
function test_two_filter_error_handling()
    @testset "TWO Filter Error Handling" begin
        test_data = create_test_lottery_data_for_pairs()
        engine = FilterEngine(test_data)
        
        # 測試號碼數量不足
        @test_throws ArgumentError calculate_two_filter(engine, [1])
        @test_throws ArgumentError calculate_two_filter(engine, Int[])
        
        # 測試無效號碼範圍
        @test_throws ArgumentError calculate_two_filter(engine, [0, 1])
        @test_throws ArgumentError calculate_two_filter(engine, [1, 40])
        
        # 測試空數據
        empty_engine = FilterEngine(LotteryDraw[])
        @test_throws ArgumentError calculate_two_filter(empty_engine, [1, 2])
        
        println("✓ TWO 過濾器錯誤處理測試通過")
    end
end

"""
執行所有 TWO 過濾器測試
"""
function run_two_filter_tests()
    println("🧪 開始執行 TWO 過濾器測試...")
    
    try
        test_pair_generation()
        test_pair_frequency_calculation()
        test_pair_skip_calculation()
        test_two_filter_calculation()
        test_expected_pairs_calculation()
        test_specific_pair_analysis()
        test_top_performing_pairs()
        test_two_filter_summary()
        test_two_filter_caching()
        test_two_filter_error_handling()
        
        println("\n🎉 所有 TWO 過濾器測試通過！")
        return true
    catch e
        println("\n❌ TWO 過濾器測試失敗: $e")
        return false
    end
end

# 如果直接執行此文件，運行測試
if abspath(PROGRAM_FILE) == @__FILE__
    run_two_filter_tests()
end
