# Data validation functions

"""
DataValidator for lottery data validation
Validates DATA5 format files and lottery number ranges
"""
struct DataValidator
    min_number::Int
    max_number::Int
    numbers_per_draw::Int
    
    DataValidator() = new(1, 39, 5)
    DataValidator(min_num::Int, max_num::Int, per_draw::Int) = new(min_num, max_num, per_draw)
end

"""
Validate lottery numbers format and range
"""
function validate_lottery_numbers(numbers::Vector{Int})::ValidationResult
    if length(numbers) != 5
        return ValidationResult(false, "Must contain exactly 5 numbers")
    end
    
    if !all(n -> 1 <= n <= 39, numbers)
        return ValidationResult(false, "Numbers must be between 1 and 39")
    end
    
    if length(unique(numbers)) != 5
        return ValidationResult(false, "Numbers must be unique")
    end
    
    return ValidationResult(true, "Valid")
end

"""
Validate DATA5 file format with comprehensive error reporting
Checks for:
- File existence and readability
- Correct number of values per line (exactly 5 numbers)
- Number range validation (1-39 for Lotto 5/39)
- Format consistency
- Chronological order (newest draws first)
"""
function validate_data5_file(validator::DataValidator, filepath::String)::ValidationResult
    try
        if !isfile(filepath)
            return ValidationResult(false, "File does not exist: $filepath")
        end
        
        lines = readlines(filepath)
        if isempty(lines)
            return ValidationResult(false, "File is empty")
        end
        
        errors = String[]
        valid_lines = 0
        previous_date = nothing
        
        for (i, line) in enumerate(lines)
            line_stripped = strip(line)
            if isempty(line_stripped)
                continue
            end
            
            # Parse line - expect either "date,n1,n2,n3,n4,n5" or "n1,n2,n3,n4,n5"
            parts = split(line_stripped, ',')
            
            # Determine if first part is a date or number
            numbers_start_idx = 1
            current_date = nothing
            
            if length(parts) >= 6
                # Assume first part is date
                try
                    current_date = Date(strip(parts[1]))
                    numbers_start_idx = 2
                catch
                    # First part is not a valid date, treat as number
                    numbers_start_idx = 1
                end
            end
            
            # Extract numbers
            if length(parts) < numbers_start_idx + validator.numbers_per_draw - 1
                push!(errors, "Line $i: Expected $(validator.numbers_per_draw) numbers, got $(length(parts) - numbers_start_idx + 1)")
                continue
            end
            
            numbers_str = parts[numbers_start_idx:numbers_start_idx + validator.numbers_per_draw - 1]
            
            try
                numbers = [parse(Int, strip(n)) for n in numbers_str]
                
                # Validate numbers using existing function
                result = validate_draw_format(validator, numbers)
                if !result
                    number_result = validate_lottery_numbers(numbers)
                    push!(errors, "Line $i: $(number_result.message)")
                    continue
                end
                
                # Check chronological order if dates are available
                if current_date !== nothing && previous_date !== nothing
                    if current_date > previous_date
                        push!(errors, "Line $i: Chronological order violation - newer draw ($current_date) should come before older draw ($previous_date). DATA5 format expects newest draws first.")
                    end
                    previous_date = current_date
                elseif current_date !== nothing
                    previous_date = current_date
                end
                
                valid_lines += 1
                
            catch e
                push!(errors, "Line $i: Could not parse numbers - $e")
            end
        end
        
        if !isempty(errors)
            error_summary = "Found $(length(errors)) validation errors:\n" * join(errors[1:min(10, length(errors))], "\n")
            if length(errors) > 10
                error_summary *= "\n... and $(length(errors) - 10) more errors"
            end
            return ValidationResult(false, error_summary)
        end
        
        if valid_lines == 0
            return ValidationResult(false, "No valid lottery draws found in file")
        end
        
        return ValidationResult(true, "File format is valid - processed $valid_lines draws")
        
    catch e
        return ValidationResult(false, "Error reading file: $e")
    end
end

"""
Check if draws are in chronological order (newest first)
"""
function check_chronological_order(validator::DataValidator, draws::Vector{LotteryDraw})::Bool
    if length(draws) <= 1
        return true
    end
    
    for i in 1:(length(draws)-1)
        if draws[i].draw_date < draws[i+1].draw_date
            return false
        end
    end
    
    return true
end

"""
Validate draw format using validator parameters
"""
function validate_draw_format(validator::DataValidator, draw::Vector{Int})::Bool
    # Check number count
    if length(draw) != validator.numbers_per_draw
        return false
    end
    
    # Check number range
    if !all(n -> validator.min_number <= n <= validator.max_number, draw)
        return false
    end
    
    # Check for duplicates
    if length(unique(draw)) != length(draw)
        return false
    end
    
    return true
end
"""

Verify data integrity for a collection of lottery draws
Checks for duplicates, invalid numbers, and chronological issues
"""
function verify_data_integrity(draws::Vector{LotteryDraw})::IntegrityReport
    total_draws = length(draws)
    duplicates_found = 0
    invalid_numbers = 0
    chronology_issues = 0
    issues = String[]
    
    # Check for duplicate draws
    seen_draws = Set{Vector{Int}}()
    for (i, draw) in enumerate(draws)
        if draw.numbers in seen_draws
            duplicates_found += 1
            push!(issues, "Duplicate draw found at position $i: $(draw.numbers)")
        else
            push!(seen_draws, draw.numbers)
        end
    end
    
    # Check for invalid numbers
    validator = DataValidator()
    for (i, draw) in enumerate(draws)
        if !validate_draw_format(validator, draw.numbers)
            invalid_numbers += 1
            push!(issues, "Invalid numbers at position $i: $(draw.numbers)")
        end
    end
    
    # Check chronological order (newest first)
    for i in 1:(length(draws)-1)
        if draws[i].draw_date < draws[i+1].draw_date
            chronology_issues += 1
            push!(issues, "Chronological order issue between positions $i and $(i+1): $(draws[i].draw_date) should be after $(draws[i+1].draw_date)")
        end
    end
    
    is_valid = duplicates_found == 0 && invalid_numbers == 0 && chronology_issues == 0
    
    return IntegrityReport(
        total_draws,
        duplicates_found,
        invalid_numbers,
        chronology_issues,
        is_valid,
        issues
    )
end

"""
Verify data integrity for raw lottery data (Vector{Vector{Int}})
"""
function verify_data_integrity(draws::Vector{Vector{Int}})::IntegrityReport
    total_draws = length(draws)
    duplicates_found = 0
    invalid_numbers = 0
    chronology_issues = 0  # Cannot check chronology without dates
    issues = String[]
    
    # Check for duplicate draws
    seen_draws = Set{Vector{Int}}()
    for (i, draw) in enumerate(draws)
        if draw in seen_draws
            duplicates_found += 1
            push!(issues, "Duplicate draw found at position $i: $draw")
        else
            push!(seen_draws, draw)
        end
    end
    
    # Check for invalid numbers
    validator = DataValidator()
    for (i, draw) in enumerate(draws)
        if !validate_draw_format(validator, draw)
            invalid_numbers += 1
            push!(issues, "Invalid numbers at position $i: $draw")
        end
    end
    
    is_valid = duplicates_found == 0 && invalid_numbers == 0
    
    return IntegrityReport(
        total_draws,
        duplicates_found,
        invalid_numbers,
        chronology_issues,
        is_valid,
        issues
    )
end

"""
Validate format consistency across multiple files
"""
function validate_format_consistency(validator::DataValidator, filepaths::Vector{String})::ValidationResult
    if isempty(filepaths)
        return ValidationResult(false, "No files provided for validation")
    end
    
    errors = String[]
    total_files = length(filepaths)
    valid_files = 0
    
    for (i, filepath) in enumerate(filepaths)
        result = validate_data5_file(validator, filepath)
        if !result.is_valid
            push!(errors, "File $i ($filepath): $(result.message)")
        else
            valid_files += 1
        end
    end
    
    if !isempty(errors)
        error_summary = "Format consistency check failed for $(length(errors)) out of $total_files files:\n" * 
                       join(errors[1:min(5, length(errors))], "\n")
        if length(errors) > 5
            error_summary *= "\n... and $(length(errors) - 5) more file errors"
        end
        return ValidationResult(false, error_summary)
    end
    
    return ValidationResult(true, "All $total_files files have consistent DATA5 format")
end

"""
Custom exception types for validation errors
"""
struct InvalidDataFormatError <: Exception
    message::String
    line_number::Int
    expected_format::String
end

struct InsufficientDataError <: Exception
    required_draws::Int
    available_draws::Int
    analysis_type::String
end

"""
Validate minimum data requirements for analysis
"""
function validate_minimum_data_requirements(draws::Vector{Vector{Int}}, analysis_type::String)::ValidationResult
    min_requirements = Dict(
        "ffg_calculation" => 100,
        "pairing_analysis" => 200,
        "skip_analysis" => 50,
        "backtesting" => 500,
        "wonder_grid_strategy" => 300
    )
    
    required = get(min_requirements, analysis_type, 100)
    available = length(draws)
    
    if available < required
        error = InsufficientDataError(required, available, analysis_type)
        return ValidationResult(false, "Insufficient data for $analysis_type: need $required draws, have $available")
    end
    
    return ValidationResult(true, "Sufficient data available for $analysis_type: $available draws (minimum: $required)")
end

"""
Validate that numbers are sorted (for certain file formats)
"""
function validate_sorted_numbers(draws::Vector{Vector{Int}})::ValidationResult
    errors = String[]
    
    for (i, draw) in enumerate(draws)
        if !issorted(draw)
            push!(errors, "Draw $i is not sorted: $draw")
        end
    end
    
    if !isempty(errors)
        error_summary = "Found $(length(errors)) unsorted draws:\n" * 
                       join(errors[1:min(10, length(errors))], "\n")
        if length(errors) > 10
            error_summary *= "\n... and $(length(errors) - 10) more unsorted draws"
        end
        return ValidationResult(false, error_summary)
    end
    
    return ValidationResult(true, "All draws are properly sorted")
end
"""
C
heck chronological order and suggest corrections
Returns information about the order and suggestions for fixing
"""
function analyze_chronological_order(draws::Vector{LotteryDraw})::Dict{String, Any}
    if length(draws) <= 1
        return Dict(
            "is_correct_order" => true,
            "order_type" => "insufficient_data",
            "suggestion" => "Need at least 2 draws to analyze order"
        )
    end
    
    ascending_violations = 0
    descending_violations = 0
    
    for i in 1:(length(draws)-1)
        if draws[i].draw_date < draws[i+1].draw_date
            ascending_violations += 1
        elseif draws[i].draw_date > draws[i+1].draw_date
            descending_violations += 1
        end
    end
    
    total_comparisons = length(draws) - 1
    
    if ascending_violations == 0 && descending_violations == 0
        return Dict(
            "is_correct_order" => true,
            "order_type" => "all_same_date",
            "suggestion" => "All draws have the same date"
        )
    elseif ascending_violations == 0
        return Dict(
            "is_correct_order" => true,
            "order_type" => "descending",
            "suggestion" => "Data is correctly ordered (newest first)"
        )
    elseif descending_violations == 0
        return Dict(
            "is_correct_order" => false,
            "order_type" => "ascending",
            "suggestion" => "Data appears to be in ascending order (oldest first). Consider reversing the order to match DATA5 format (newest first)."
        )
    else
        return Dict(
            "is_correct_order" => false,
            "order_type" => "mixed",
            "ascending_violations" => ascending_violations,
            "descending_violations" => descending_violations,
            "suggestion" => "Data has mixed chronological order. $ascending_violations ascending violations and $descending_violations descending violations out of $total_comparisons comparisons."
        )
    end
end

"""
Validate DATA5 file with flexible chronological order checking
"""
function validate_data5_file_flexible(validator::DataValidator, filepath::String; strict_chronology::Bool=false)::ValidationResult
    try
        if !isfile(filepath)
            return ValidationResult(false, "File does not exist: $filepath")
        end
        
        lines = readlines(filepath)
        if isempty(lines)
            return ValidationResult(false, "File is empty")
        end
        
        errors = String[]
        warnings = String[]
        valid_lines = 0
        dates = Date[]
        
        for (i, line) in enumerate(lines)
            line_stripped = strip(line)
            if isempty(line_stripped)
                continue
            end
            
            # Parse line - expect either "date,n1,n2,n3,n4,n5" or "n1,n2,n3,n4,n5"
            parts = split(line_stripped, ',')
            
            # Determine if first part is a date or number
            numbers_start_idx = 1
            current_date = nothing
            
            if length(parts) >= 6
                # Assume first part is date
                try
                    current_date = Date(strip(parts[1]))
                    numbers_start_idx = 2
                    push!(dates, current_date)
                catch
                    # First part is not a valid date, treat as number
                    numbers_start_idx = 1
                end
            end
            
            # Extract numbers
            if length(parts) < numbers_start_idx + validator.numbers_per_draw - 1
                push!(errors, "Line $i: Expected $(validator.numbers_per_draw) numbers, got $(length(parts) - numbers_start_idx + 1)")
                continue
            end
            
            numbers_str = parts[numbers_start_idx:numbers_start_idx + validator.numbers_per_draw - 1]
            
            try
                numbers = [parse(Int, strip(n)) for n in numbers_str]
                
                # Validate numbers using existing function
                result = validate_draw_format(validator, numbers)
                if !result
                    number_result = validate_lottery_numbers(numbers)
                    push!(errors, "Line $i: $(number_result.message)")
                    continue
                end
                
                valid_lines += 1
                
            catch e
                push!(errors, "Line $i: Could not parse numbers - $e")
            end
        end
        
        # Analyze chronological order if dates are available
        if length(dates) > 1
            if strict_chronology
                # Check strict descending order
                for i in 1:(length(dates)-1)
                    if dates[i] <= dates[i+1]
                        push!(errors, "Chronological order violation: dates should be in descending order (newest first)")
                        break
                    end
                end
            else
                # Just provide analysis and warnings
                if !issorted(dates, rev=true)
                    if issorted(dates)
                        push!(warnings, "Dates are in ascending order (oldest first). DATA5 format typically expects newest first.")
                    else
                        push!(warnings, "Dates are not in consistent chronological order.")
                    end
                end
            end
        end
        
        if !isempty(errors)
            error_summary = "Found $(length(errors)) validation errors:\n" * join(errors[1:min(10, length(errors))], "\n")
            if length(errors) > 10
                error_summary *= "\n... and $(length(errors) - 10) more errors"
            end
            return ValidationResult(false, error_summary)
        end
        
        if valid_lines == 0
            return ValidationResult(false, "No valid lottery draws found in file")
        end
        
        success_message = "File format is valid - processed $valid_lines draws"
        if !isempty(warnings)
            success_message *= "\nWarnings:\n" * join(warnings, "\n")
        end
        
        return ValidationResult(true, success_message)
        
    catch e
        return ValidationResult(false, "Error reading file: $e")
    end
end