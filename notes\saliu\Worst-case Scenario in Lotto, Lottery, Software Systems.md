---
created: 2025-07-23T18:21:56 (UTC +08:00)
tags: [strategy,strategies,wonder,grid,wonder-grid,lotto,pairs,pairings,software,lottery,lotto,systems,frequency,statistics,numbers,lotto jackpot,combinations,random,analysis]
source: https://saliu.com/bbs/messages/720.html
author: 
---

# Worst-case Scenario in Lotto, Lottery, Software Systems

> ## Excerpt
> Lottery software to win at lotto with powerful strategies, systems such as the wonder grid: top lottery pairs. There are also caveats or worst case scenarios.

---
[![Lottery Software: Jackpot Lotto, Pick Lotteries, Powerball, Mega Millions, Euromillions, Keno.](https://saliu.com/images/lottery-software-systems.gif)](https://saliu.com/free-lotto-lottery.html)  

## By <PERSON>, ★ _Founder of Lotto Mathematics_

![Wonder grid is lotto strategy system based on pairs and can win the lotto jackpot.](https://saliu.com/bbs/messages/HLINE.gif)

Published on July 31, 2001; later updates.

The newest version of the lotto software Bright\* has a component that checks how _lotto wonder grids_ performed in the past. Menu #4, then _G = Grid Check_. It is highly recommended to use the latest version of all lottery software packages.

[![Lottery Software: Jackpot Lotto, Pick Lotteries, Powerball, Mega Millions, Euromillions, Keno.](https://saliu.com/ScreenImgs/wonder-grid-check.gif)](https://saliu.com/membership.html)

• Frank, your sample seems to fall into _worst case scenario_. It is possible to get less spectacular results sometimes. (Of course, I assumed your process was error-free. Later, you emailed me and I learned that your lotto data file was... upside down! The oldest draw was always at the top, instead of the bottom of the file. That's why your winning reports were always the same! No matter what real lottery draws you added at the bottom of the file, the report was always done for the oldest draws! Then you used UPDOWN to correct the order of draws and I haven't heard from you again...)

I just did a sampling one more time. It is for a lotto 5/39 game, here, in Pennsylvania. The individual probability is 5/39, approximately 1/8 (very close to 48/6 or 49/6 games). I deleted the top 39/2=20 real lottery drawings in the file PA-5 and saved it as DATA-5.2. I ran Super Utilities, the statistical module, for DATA-5.2. I saved a _wonder-grid_ file named BEST5.2 with the best 39 pairings. Then I ran UTIL-5, the _W – check for winners_ option. I checked the 39 combinations in BEST5.2 against the first (top) 20 drawings in PA-5. The top 20 draws in PA-5 act like _future draws_, because BEST5.2 has no relation to them.  
I got a far better report than yours: 2 2nd prizes and 12 3rd prize winners.

```
<span size="5" face="Courier New" color="#c5b358">                   LOTTO-5 Winning Number Checking
                   Files: BEST5.2 ( 39 ) against PA-5 ( 20 )

    Line    Combinations                          5         4         3
    no.       Checked                         Winners   Winners   Winners

      2     2 19 12 18  5          in draw #                         4
      6     6 36 21 17 22          in draw #                         15 
     12    12  7  2 19 14          in draw #                         11 
     15    15 14 16 25 13          in draw #               8 
     15    15 14 16 25 13          in draw #                         19 
     19    19  2 14 12 22          in draw #                         11 
     21    21 24  9  5  6          in draw #                         3 
     29    29  6 21 12 17          in draw #                         3 
     29    29  6 21 12 17          in draw #               13 
     32    32  9 11  7 18          in draw #                         7 
     34    34  2 25 28 32          in draw #                         5 
     35    35 23 24 39 34          in draw #                         20 
     39    39 14 35  9  4          in draw #                         1 
</span>
```

Let's compare with random play.  
Total number of combinations played: 39 x 20 (draws) = 780.  
The odds of winning '4 of 5' in a 5/39 lotto game are '1 in 3387'. Since 780 combinations were played, the odds were diminished to 780 / 3387= '1 in 4.3' = 0.23.  
The wonder grid had 2 hits '4 of 5' within the same range. The improvement was very clear: 2 / 0.23 = 8.7 times better. On March 9, 2002 the '4 of 5' winning ticket paid $315.  
The odds of winning '3 of 5' in a 5/39 lotto game are '1 in 103'. Since 780 combinations were played, the odds were diminished to 780 / 103 = 7.6.

The wonder grid had 12 hits '3 of 5' within the same range. The improvement was less spectacular: 12 / 7.6 = 1.6 times better. On March 9, 2002 the '3 of 5' winning ticket paid $10.  
The wonder grid is less effective for lower tier prizes.  
The wonder grid is clearly more effective for higher prizes, especially the jackpot.  
In the real case presented here, the lotto wonder grid cost was $780. The wins could amount to $315 X 2 = $630 for '4 of 5' hits; plus $10 x 12 = $120 for ' 3 of 5' hits; total wins: $750. It did not cover the cost, but it's very close, while offering a far better chance to hit the $100,000 jackpot.

Meanwhile, random play would win only $10 x 7.6 = $76—or almost 10 times less than the wonder grid.  
(I totally disregard the '2 of 5' prizes. They pay $2 in Pennsylvania's 5/39 lotto. The 'wonder grid' must have hit lots of them in 20 draws! I also disregard the '3 of 6' prizes. I strongly recommend that all the lotteries in the world cancel such offending prizes. The money thus freed should be added to the second and third prizes. Right now, “the 4th prize” is a way of cheating the players. At best, it is a placebo. Nobody cashes such meager prizes! People feel ashamed to ask for the cash. They play instead for the next lotto draw. The probability is excellent they'll lose their placebo “prizes”!)

As a matter of fact, I upgraded my winners-checking lotto software today. It checks now for '2 of 6' hits in lotto-5 games and '3 of 6' in lotto-6 games. The '2 of 5' combinations outnumber the '3 of 5' combinations by 6-8 times. I did further testing in the lotto 5/39. I tested two 200-combination groups. The group closest “in the future” to the inception of the wonder grid offered huge results. One lotto jackpot hit, plus 19 '4 of 5' hits, plus 183 of '3 of 5' and a big bunch of '2 of 5'! Over 1050 wins altogether! The “huge” in this case applies to the jackpot. It was over $150,000 on March 9, 2002.

The cost of playing $200 x 39 would be $7,800. That's the money I would have needed, had I started back in the summer of 2001. I didn't possess that amount upfront. But, there were 100 second prize wins! I can disregard the third and fourth prizes. The prizes were quite regularly distributed over the 200 days (the game is played daily). Then, there was the lotto jackpot (in around 130 draws)! The second 200-combination group was farther from the inception of the same wonder grid. No jackpot and only one '4 of 5' hits! Incredible difference! I am still to formularize the best timing of a lottery wonder grid. It is clear it has to be close to the moment a wonder grid is built.

I certainly believe that the 'wonder-grid' offers a better leverage than a static lotto wheel equal in size. There is another advantage of the 'wonder-grid'. It offers the best expectation to win the first prize, while playing at break-even or close.

As I said previously, the 'wonder-grid' offers a wide range of options. Sure, playing the 'top 25% pairings' will increase immensely the probability to hit the jackpot. At a higher cost. The cost can be diminished by eliminating also the 'worst pairings'. It happens that some 'top pairings' for some lotto numbers are also 'worst pairings' for other numbers. The worst pairings take precedence: they are eliminated first. At the same time, the 'best pairings' can contain numbers that go through a long skip. You know what FFG shows: the lottery numbers repeat more often after a skip longer than its median. So, we can replace numbers with long skip by 'top pairings' with shorter skips…

By the way, applying the concept to the pick-3 game: the behavior is different. Getting a 'wonder-grid' of the best pick-3 pairings results in 10 combinations. They offer acceptable results only as 'boxed' combinations. Playing the 'wonder-grid' to win 'straight' is inconsistent. The best I have gotten so far for pick-3 is ELIMINATING the 'worst 6' pairings. The result is some 50-70 lottery combinations with a lot of 'straight' hits. There is also a larger number of 'boxed' hits. The best result, however, is playing 'straight only' (disregard all the 'boxed' hits!) I eliminated also the 'worst 7 pairings'. For the most part, running POWER-3 results in 0 combinations. Sometimes, I get 1 or 2 straight combinations. I haven't hit yet, eliminating the 'worst 7' pairings. But, who knows! I'll check more draws.

• Things have become much clearer in 2003. Read the article: [The Wonder Grid Revisited: New Pairing Research.](https://saliu.com/bbs/messages/grid.html)  
Playing the best 3 pairs while eliminating the worst 7 pairs is one proven strategy at beating not only random play handily, but also beating the monstrous house edge of 50%!

Just hitting twice in 100 real lottery drawings will earn its money! Here is a report of eliminating the 'worst 6 pairings' in the pick-3 lottery:  

```
<span size="5" face="Courier New" color="#c5b358">                   PICK-3 Winning Combination Checking 
                   Files: OUT3 ( 70 ) against DATA-3 ( 100 )

    Line   Combination                        Straight   Boxed 
     no.     Checked                           Winner    Winner

      4       0 0 9             in draw #                  24 
      8       0 4 4             in draw #                  21 
      9       0 9 0             in draw #                  24 
     10       0 9 9             in draw #                  85 
     12       1 1 2             in draw #                  84 
     13       1 1 7             in draw #                  36 
     14       1 2 1             in draw #                  84 
     15       1 2 2             in draw #        6 
     16       1 7 1             in draw #                  36 
     17       1 7 7             in draw #                  23 
     18       2 1 1             in draw #        84 
     19       2 1 2             in draw #                  6 
...
     54       7 7 4             in draw #        10 
     55       7 7 6             in draw #        33 
     56       7 7 7             in draw #        47 
     58       8 2 8             in draw #                  62 
     58       8 2 8             in draw #                  98 
     61       8 8 2             in draw #                  62 
     61       8 8 2             in draw #        98 
     64       9 0 0             in draw #        24 
     65       9 0 9             in draw #                  85 
     68       9 9 0             in draw #        85 

         Total Hits:                             14         24 
</span>
```

\* What puzzles me most is the type of the winning pick-3 combinations. All the winners are 3-way or 1-way combinations (double-digit or triple-digit numbers)! A quick strategy could be named…patience! The…combined 3-way and 1-way combinations represent 27% of all 3-digit numbers. So, wait for one or two 6-way drawings, then apply the 'eliminate the worst 6 pairings' lottery strategy…

Ion Saliu

In article @newsserver.ip.pt, Franc Jose says...

Ion,

Thinking your time is little, I tested the _5 most frequent pairs wheel_ 49 lines in Totoloto (Portugal 6/49).  
I made a DATA6 file with 147 draws from which I made a frequency report with Super Utilities. From the FREQ6 file I cut/paste the five numbers more frequent with each number (from 1 to 49) to a new lotto wheel file WHEEL63.

With the 22 draws remaining (and posterior) in the Portuguese lottery file I made a new file DATA61 to check for winners in UTIL-6 (W).

I got this report:  

```
<span size="5" face="Courier New" color="#c5b358">                  LOTTO-6 Winning Number Checking 
                   Files: WHEEL-163 DATA-61

    Line    Numbers                              6         5         4
    no.     Checked                           Winners   Winners   Winners


     18    18  2 12 22 35 49       in line #                         14

</span>
```

As you see, if I played this 49 lines wheel, I would hit 4 numbers once (1 in 22 draws).  
Now let's ask: is this better or worse than the ODDS? To know which is the virtual ODDS of the 49 lines wheel (WHEEL63) I pasted it in CoverMaster and the probability of 4in6 is 4,47937 %. The expected numbers of 4 hits in 22 draws is 22\*4,47937/100= 0,9854614.

You may conclude that in this period of time this lotto wheel has been lightly superior to the ODDS (1 greater than .9854614).  
One important thing I may say now: every method/strategy that equals the odds is a good one because the so-called random selection of numbers/lines has to be tested too, till we can conclude it has been good enough in some period of time!

But is this difference statistically significant? I think not.  
However ... the draw that has been 4-hit has been the 14th. If the wheel odds is 1 in 22,32, 1 in 14 is very good. If we make another wheel, by the same method, and play it in and after the 15th draw, and some draws after (5 or 10 or 14 than 22,32!) we hit four numbers again, oh my dear!, we are hitting better than the odds!

Another way to test your lotto method/strategy is to calculate the position of the numbers drawn in the line (string) of the pairs. The 5 more hit numbers are the position 1, position 2, position 3, position 4, position 5. If you conclude that in e.g., 80% of draws the numbers drawn stays in the 12 first positions, it could be very good although the lotto wheel must grows up. Theoretically, it seems not possible because lottery has a circular behaviour! But, let's research.

Frank

## [<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>](https://saliu.com/content/lottery.html)

-   The Main [_**Lotto, Lottery, Software, Strategy Systems**_](https://saliu.com/LottoWin.htm) Page.  
    Presenting software to create free winning lotto, lottery strategies, systems based on mathematics. Get your lotto systems or wheels, the best lottery, lotto software, combinations, winning numbers.
-   [_**Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies**_](https://saliu.com/Newsgroups.htm).  
    Read a genuine analysis of Excel spreadsheets applied to lottery and lotto software development, systems, and strategies. Combining Excel analysis with powerful lottery and lotto software programmed by this author, _Parpaluck_.
-   [_**<u>MDIEditor Lotto WE</u>: Tutorial, Software User Guide, Manual, Book, ebook**_](https://saliu.com/MDI-lotto-guide.html).  
    ~ Also applicable to LotWon lottery, lotto software; plus Powerball/Mega Millions, Euromillions.
-   [_**Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps, Programs**_](https://saliu.com/forum/lotto-book.html).
-   [_**<u>Skip Systems</u> Software**_](https://saliu.com/skip-strategy.html): _**Lotto, Lottery, Powerball, Mega Millions, Euromillions**_.
-   [**<u>Lottery Utility Software</u>**](https://saliu.com/lottery-utility.html): _**Pick-3, 4 Lottery, Lotto-5, 6, Powerball, Mega Millions, Thunderball, Euromillions**_.
-   [Analysis of Best Ranges for Lotto Number Frequency, Lottery Pairs, Pairings](https://saliu.com/lottery-lotto-pairs.html).
-   Download [**lottery software, lotto software**](https://saliu.com/infodown.html).

![Lotto wonder grid was analyzed under the worst-case scenarios.](https://saliu.com/bbs/messages/HLINE.gif)

**[Home](https://saliu.com/index.htm) | [Search](https://saliu.com/Search.htm) | [New Writings](https://saliu.com/bbs/index.html) | [Software](https://saliu.com/infodown.html) | [Odds, Generator](https://saliu.com/calculator_generator.html) | [Contents](https://saliu.com/content/index.html) | [Forums](https://forums.saliu.com/) | [Sitemap](https://saliu.com/sitemap/index.html)**

![Check lotto wonder grids with Super Utilities for lottery software.](https://saliu.com/bbs/messages/HLINE.gif)
