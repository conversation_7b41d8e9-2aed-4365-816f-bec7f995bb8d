# Auto Tuner System
# 自動調優系統 - 動態優化系統性能參數

using Dates
using Statistics

# 引入必要的模組
include("../monitoring/performance_monitor.jl")
include("../cache/multi_level_cache.jl")
include("../memory/memory_pool.jl")

"""
調優參數結構
"""
mutable struct TuningParameters
    # 快取參數
    l1_cache_size::Int
    l2_cache_size::Int
    l3_cache_size::Int
    cache_cleanup_interval_minutes::Int
    
    # 記憶體池參數
    memory_pool_max_size::Int
    memory_pool_cleanup_threshold::Int
    memory_pool_cleanup_interval_minutes::Int
    
    # 並行計算參數
    max_parallel_tasks::Int
    batch_size::Int
    thread_pool_size::Int
    
    # 性能閾值
    max_acceptable_latency_ms::Float64
    target_cache_hit_rate::Float64
    target_memory_efficiency::Float64
    
    function TuningParameters()
        new(
            # 默認快取參數
            100,    # l1_cache_size
            1000,   # l2_cache_size
            10000,  # l3_cache_size
            15,     # cache_cleanup_interval_minutes
            
            # 默認記憶體池參數
            1000,   # memory_pool_max_size
            100,    # memory_pool_cleanup_threshold
            30,     # memory_pool_cleanup_interval_minutes
            
            # 默認並行計算參數
            nthreads() * 2,  # max_parallel_tasks
            50,     # batch_size
            nthreads(),      # thread_pool_size
            
            # 默認性能閾值
            100.0,  # max_acceptable_latency_ms
            0.8,    # target_cache_hit_rate
            0.7     # target_memory_efficiency
        )
    end
end

"""
性能指標結構
"""
struct PerformanceMetrics
    avg_latency_ms::Float64
    cache_hit_rate::Float64
    memory_efficiency::Float64
    throughput_ops_per_sec::Float64
    error_rate::Float64
    
    function PerformanceMetrics(
        avg_latency::Float64,
        cache_hit_rate::Float64,
        memory_efficiency::Float64,
        throughput::Float64,
        error_rate::Float64 = 0.0
    )
        new(avg_latency, cache_hit_rate, memory_efficiency, throughput, error_rate)
    end
end

"""
自動調優器
"""
mutable struct AutoTuner
    parameters::TuningParameters
    performance_history::Vector{PerformanceMetrics}
    tuning_history::Vector{Tuple{DateTime, TuningParameters, PerformanceMetrics}}
    last_tuning::DateTime
    tuning_interval_minutes::Int
    learning_rate::Float64
    
    function AutoTuner(tuning_interval::Int = 60, learning_rate::Float64 = 0.1)
        new(
            TuningParameters(),
            Vector{PerformanceMetrics}(),
            Vector{Tuple{DateTime, TuningParameters, PerformanceMetrics}}(),
            now(),
            tuning_interval,
            learning_rate
        )
    end
end

"""
收集當前性能指標
"""
function collect_performance_metrics(monitor::SystemPerformanceMonitor)::PerformanceMetrics
    # 獲取性能摘要
    summary = get_system_performance_summary(monitor)
    
    # 提取關鍵指標
    avg_latency = get(summary, "avg_skip_time_ms", 0.0) + get(summary, "avg_filter_time_ms", 0.0)
    cache_hit_rate = get(summary, "cache_hit_rate", 0.0)
    
    # 計算記憶體效率（簡化）
    pool_stats = get_all_pool_statistics(GLOBAL_MEMORY_POOL)
    memory_efficiency = pool_stats["summary"]["overall_reuse_rate"]
    
    # 計算吞吐量（簡化）
    runtime_hours = get(summary, "monitoring_duration_hours", 1.0)
    total_operations = length(monitor.monitor.metrics)
    throughput = total_operations / (runtime_hours * 3600)  # 每秒操作數
    
    # 計算錯誤率
    total_alerts = get(summary, "total_alerts", 0)
    error_rate = total_operations > 0 ? total_alerts / total_operations : 0.0
    
    return PerformanceMetrics(avg_latency, cache_hit_rate, memory_efficiency, throughput, error_rate)
end

"""
評估性能評分
"""
function evaluate_performance_score(metrics::PerformanceMetrics, params::TuningParameters)::Float64
    score = 0.0
    
    # 延遲評分（越低越好）
    latency_score = if metrics.avg_latency_ms <= params.max_acceptable_latency_ms
        1.0 - (metrics.avg_latency_ms / params.max_acceptable_latency_ms) * 0.5
    else
        0.5 - min(0.5, (metrics.avg_latency_ms - params.max_acceptable_latency_ms) / params.max_acceptable_latency_ms)
    end
    score += latency_score * 0.3
    
    # 快取命中率評分
    cache_score = min(1.0, metrics.cache_hit_rate / params.target_cache_hit_rate)
    score += cache_score * 0.3
    
    # 記憶體效率評分
    memory_score = min(1.0, metrics.memory_efficiency / params.target_memory_efficiency)
    score += memory_score * 0.2
    
    # 吞吐量評分（相對評分）
    throughput_score = min(1.0, metrics.throughput_ops_per_sec / 10.0)  # 假設目標是每秒10個操作
    score += throughput_score * 0.15
    
    # 錯誤率評分（越低越好）
    error_score = max(0.0, 1.0 - metrics.error_rate * 10)  # 錯誤率超過10%嚴重扣分
    score += error_score * 0.05
    
    return max(0.0, min(1.0, score))
end

"""
自動調優性能參數
"""
function auto_tune_performance!(tuner::AutoTuner, monitor::SystemPerformanceMonitor)::Bool
    current_time = now()
    
    # 檢查是否需要調優
    if current_time - tuner.last_tuning < Minute(tuner.tuning_interval_minutes)
        return false
    end
    
    # 收集當前性能指標
    current_metrics = collect_performance_metrics(monitor)
    push!(tuner.performance_history, current_metrics)
    
    # 評估當前性能
    current_score = evaluate_performance_score(current_metrics, tuner.parameters)
    
    # 記錄調優歷史
    push!(tuner.tuning_history, (current_time, deepcopy(tuner.parameters), current_metrics))
    
    # 決定是否需要調整參數
    needs_tuning = should_tune_parameters(tuner, current_metrics, current_score)
    
    if needs_tuning
        # 執行參數調優
        old_params = deepcopy(tuner.parameters)
        tune_parameters!(tuner, current_metrics, current_score)
        
        @info "自動調優執行" old_score=current_score new_params=tuner.parameters
        
        tuner.last_tuning = current_time
        return true
    end
    
    tuner.last_tuning = current_time
    return false
end

"""
判斷是否需要調優參數
"""
function should_tune_parameters(tuner::AutoTuner, metrics::PerformanceMetrics, score::Float64)::Bool
    # 如果性能評分低於 0.7，需要調優
    if score < 0.7
        return true
    end
    
    # 如果延遲超過閾值，需要調優
    if metrics.avg_latency_ms > tuner.parameters.max_acceptable_latency_ms
        return true
    end
    
    # 如果快取命中率太低，需要調優
    if metrics.cache_hit_rate < tuner.parameters.target_cache_hit_rate * 0.8
        return true
    end
    
    # 如果記憶體效率太低，需要調優
    if metrics.memory_efficiency < tuner.parameters.target_memory_efficiency * 0.8
        return true
    end
    
    # 如果錯誤率太高，需要調優
    if metrics.error_rate > 0.05  # 5% 錯誤率
        return true
    end
    
    return false
end

"""
調優參數
"""
function tune_parameters!(tuner::AutoTuner, metrics::PerformanceMetrics, current_score::Float64)
    params = tuner.parameters
    learning_rate = tuner.learning_rate
    
    # 調優快取參數
    if metrics.cache_hit_rate < params.target_cache_hit_rate
        # 增加快取大小
        params.l1_cache_size = Int(round(params.l1_cache_size * (1 + learning_rate)))
        params.l2_cache_size = Int(round(params.l2_cache_size * (1 + learning_rate)))
        params.l3_cache_size = Int(round(params.l3_cache_size * (1 + learning_rate)))
    elseif metrics.cache_hit_rate > params.target_cache_hit_rate * 1.1
        # 適度減少快取大小以節省記憶體
        params.l1_cache_size = Int(round(params.l1_cache_size * (1 - learning_rate * 0.5)))
        params.l2_cache_size = Int(round(params.l2_cache_size * (1 - learning_rate * 0.5)))
        params.l3_cache_size = Int(round(params.l3_cache_size * (1 - learning_rate * 0.5)))
    end
    
    # 調優記憶體池參數
    if metrics.memory_efficiency < params.target_memory_efficiency
        # 增加記憶體池大小
        params.memory_pool_max_size = Int(round(params.memory_pool_max_size * (1 + learning_rate)))
        params.memory_pool_cleanup_threshold = Int(round(params.memory_pool_cleanup_threshold * (1 + learning_rate)))
    end
    
    # 調優並行參數
    if metrics.avg_latency_ms > params.max_acceptable_latency_ms
        # 增加並行度
        params.max_parallel_tasks = min(nthreads() * 4, Int(round(params.max_parallel_tasks * (1 + learning_rate))))
        params.batch_size = max(10, Int(round(params.batch_size * (1 - learning_rate * 0.5))))
    elseif metrics.avg_latency_ms < params.max_acceptable_latency_ms * 0.5
        # 減少並行度以節省資源
        params.max_parallel_tasks = max(nthreads(), Int(round(params.max_parallel_tasks * (1 - learning_rate * 0.5))))
        params.batch_size = Int(round(params.batch_size * (1 + learning_rate * 0.5)))
    end
    
    # 調優清理間隔
    if metrics.memory_efficiency < 0.5
        # 更頻繁的清理
        params.cache_cleanup_interval_minutes = max(5, Int(round(params.cache_cleanup_interval_minutes * (1 - learning_rate))))
        params.memory_pool_cleanup_interval_minutes = max(10, Int(round(params.memory_pool_cleanup_interval_minutes * (1 - learning_rate))))
    end
    
    # 確保參數在合理範圍內
    clamp_parameters!(params)
end

"""
限制參數在合理範圍內
"""
function clamp_parameters!(params::TuningParameters)
    # 快取大小限制
    params.l1_cache_size = clamp(params.l1_cache_size, 50, 1000)
    params.l2_cache_size = clamp(params.l2_cache_size, 500, 10000)
    params.l3_cache_size = clamp(params.l3_cache_size, 5000, 100000)
    
    # 記憶體池限制
    params.memory_pool_max_size = clamp(params.memory_pool_max_size, 100, 10000)
    params.memory_pool_cleanup_threshold = clamp(params.memory_pool_cleanup_threshold, 50, 1000)
    
    # 並行參數限制
    params.max_parallel_tasks = clamp(params.max_parallel_tasks, nthreads(), nthreads() * 8)
    params.batch_size = clamp(params.batch_size, 10, 1000)
    
    # 清理間隔限制
    params.cache_cleanup_interval_minutes = clamp(params.cache_cleanup_interval_minutes, 5, 120)
    params.memory_pool_cleanup_interval_minutes = clamp(params.memory_pool_cleanup_interval_minutes, 10, 240)
end

"""
獲取調優報告
"""
function get_tuning_report(tuner::AutoTuner)::Dict{String, Any}
    if isempty(tuner.tuning_history)
        return Dict("error" => "沒有調優歷史記錄")
    end
    
    # 計算性能趨勢
    scores = Float64[]
    for (_, params, metrics) in tuner.tuning_history
        score = evaluate_performance_score(metrics, params)
        push!(scores, score)
    end
    
    # 最新性能指標
    latest_metrics = tuner.performance_history[end]
    latest_score = scores[end]
    
    # 性能改進
    performance_improvement = if length(scores) > 1
        latest_score - scores[1]
    else
        0.0
    end
    
    return Dict(
        "current_parameters" => tuner.parameters,
        "latest_performance" => latest_metrics,
        "latest_score" => latest_score,
        "performance_improvement" => performance_improvement,
        "tuning_sessions" => length(tuner.tuning_history),
        "avg_score" => mean(scores),
        "max_score" => maximum(scores),
        "min_score" => minimum(scores),
        "last_tuning" => tuner.last_tuning,
        "tuning_interval_minutes" => tuner.tuning_interval_minutes
    )
end

"""
重置調優器
"""
function reset_tuner!(tuner::AutoTuner)
    tuner.parameters = TuningParameters()
    empty!(tuner.performance_history)
    empty!(tuner.tuning_history)
    tuner.last_tuning = now()
end

"""
應用調優參數到系統
"""
function apply_tuning_parameters!(params::TuningParameters)
    # 這裡可以實際應用參數到系統組件
    # 由於我們的系統組件已經創建，這裡主要是記錄和建議
    
    @info "應用調優參數" l1_cache_size=params.l1_cache_size l2_cache_size=params.l2_cache_size memory_pool_size=params.memory_pool_max_size
    
    # 實際應用需要重新創建組件或動態調整
    # 這裡返回建議的配置
    return Dict(
        "cache_config" => Dict(
            "l1_max_items" => params.l1_cache_size,
            "l2_max_items" => params.l2_cache_size,
            "l3_max_items" => params.l3_cache_size
        ),
        "memory_pool_config" => Dict(
            "max_size" => params.memory_pool_max_size,
            "cleanup_threshold" => params.memory_pool_cleanup_threshold
        ),
        "parallel_config" => Dict(
            "max_tasks" => params.max_parallel_tasks,
            "batch_size" => params.batch_size
        )
    )
end

# 全局自動調優器實例
const GLOBAL_AUTO_TUNER = AutoTuner()

"""
便利函數：執行自動調優
"""
function perform_auto_tuning!()::Bool
    return auto_tune_performance!(GLOBAL_AUTO_TUNER, GLOBAL_PERFORMANCE_MONITOR)
end

"""
便利函數：獲取調優報告
"""
function get_global_tuning_report()::Dict{String, Any}
    return get_tuning_report(GLOBAL_AUTO_TUNER)
end

# 導出主要函數和結構
export TuningParameters, PerformanceMetrics, AutoTuner, GLOBAL_AUTO_TUNER
export collect_performance_metrics, evaluate_performance_score
export auto_tune_performance!, get_tuning_report, reset_tuner!
export apply_tuning_parameters!, perform_auto_tuning!, get_global_tuning_report
