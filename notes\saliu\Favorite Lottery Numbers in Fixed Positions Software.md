---
created: 2025-07-24T21:26:40 (UTC +08:00)
tags: [favorite lottery numbers,fixed positions,lotto,software,generate,combinations,reduce,odds,tickets,]
source: https://saliu.com/favorite-lottery-numbers-positions.html
author: 
---

# Favorite Lottery Numbers in Fixed Positions: Software

> ## Excerpt
> Generate lottery combinations with favorite lotto numbers in fixed positions. Numbers with the best positional frequency reduce the odds and tickets to play.

---
Published in November of year of grace 2018.  
First capture by the _WayBack Machine_ (_web.archive.org_) February 7, 2020.

-   _**<big><u>SkipSystem</u> - Bright / Ultimate Software</big>**_, menu II, function _K: **Skip System** (Create Systems)_
-   _**<big><u>PairGrid*</u> - Bright Software</big>**_, main menu, function _G: Pairing Reports, Custom Grids_
-   _**<big><u>PairGrid*</u> - Ultimate Software</big>**_, menu II, function _P: Pairing Reports (PairGrid6)_
-   _**<big><u>Super Utilities</u> - Bright / Ultimate Software</big>**_, main menu, function _U/S: Super Utilities (Software Lotto\*)_
-   _**<big><u>Range-6</u> - Ultimate Software</big>**_, menu III, function _E: Ranges of Numbers_
-   All software titles above generate combinations from any systems, including text files with **favorite lottery numbers in fixed positions**.

Super Axiomatics, this must be the proverbial case of _"Hidden in plain sight"_! And from there I coined the phrase: _“The unknown hides in plain sight on the path of the axiomatics.”_

It happened in February, this year of grace 2018. I made a strong commitment to lottery strategizing and playing. **BrianPA**, the most loyal and proficient user of my lottery software, asked me for one more favor. He wanted an overhaul of my cross-referencing software presented here:

-   [_**Cross-Reference, Combine Lottery Strategy Files Created by Various Types of Lottery Software**_](https://saliu.com/cross-lines.html).

I hesitated first as I really wanted to go full-steam ahead in playing the lottery like... full-time. But the idea was worthwhile and in a couple of weeks I turned <big>FileLines</big> into a great piece of software. I thanked Brian for the idea and insistence as the software upgrade made me a lot more productive.

But Brian didn't stop there! He emailed to me with an eye-catching subject: _"The Golden Egg Hiding in Plain Sight!"_ He asked for another big favor: Another major upgrade to a different piece of software: <big>Pair Grid</big>. I was hesitant again because the request would seriously delay my lottery plans. Also, my eyes were set on a casino in the region. I had to put both actions on the back burner.

The new project proved to be a whole lot harder than I first thought. But, in the end, again, I thanked Brian for his idea of a _golden egg_ lottery strategy. I took Brian's idea much further. I came up with a strategy that, for the first time ever, <u>guarantees when a lottery system should NOT be played</u>. In other words, _I will know for sure that the strategy will NOT hit the very next lottery drawing_. I made a reference to the concept in the _rec.gambling.lottery_ newsgroup and in this article:

-   [_**Neural Networking, Neural Networks, AI, Axiomatic Intelligence AxI in Lottery, Lotto**_](https://saliu.com/neural-networking-lottery.html).

In an _egg_ shell... er, nutshell, the strategy is based on frequency and setting the lotto numbers with the best frequencies as favorites. From the beginning, my lottery software generated combinations including with _favorite numbers_. The favorite numbers (a.k.a. _bankers_), however, could be set only _regardless of position_. Brian, nonetheless, would have liked another overhaul of my lottery software to generate combinations with favorite lotto numbers in _strict position_! I decided o take Edgar Allen Poe's path: _"Nevermore!"_

A few weeks thereafter, it struck me that, actually, my software had that special feature for years, and years, and years! And it was the most efficient method of generating lotto combinations with _bankers in fixed positions_. Multiple programs were capable of such great generating fixture for so many years! <u>And the software only required a simple input file in text format!</u>

You have known for more than a decade now that the universal software known as <big>SkipSystem</big> was capable of generating combinations from a large variety of **positional system files**. For example, the 6-number lotto module would take a text file consisting of _exactly_ 6 lines with _multiple_ numbers on each line. But the real condition was: _**at least** one number per line_. That was the moment when the proverbial case of _"Hidden in plain sight"_ sounded loud and clear!

I created a positional input file for <big>Skip System</big>. I had the reports created by <big>Frequency Rank</big>. I copied all the numbers from each frequency position and pasted to that input file. So, the skip input file had 6 lines with 49 numbers on every line. Then I took one line and deleted 48 of the numbers. Thus, the line had just one number: exactly like a _banker_ or _favorite_. I generated combinations, and, to my surprise, the process was really fast. I checked the combinations and the output was 100% accurate.

I experimented with one _favorite_ in different positions. The results were always 100% accurate. I moved to 2 _bankers_ - the results always accurate and totally correct. The same results with 3 _favorites_, four, etc. I took one step further and deleted some numbers from the full positions (say, based on low frequency). I named the input file _ThreeNumbersPos.txt_. You will see it appear in the screenshots that follow. The file had the following favorites/bankers: **1** in the 1st position, **16** in the 3rd position, **27** in the 4th position. I deleted:

-   position #2: eliminate numbers 4, 7, 10
-   position #5: eliminate numbers 35, 33, 46, 39
-   position #6: eliminate numbers 23, 41, 45

This is what the simple _ThreeNumbersPos.txt_ file looks like:

**1**  
_24 1 46 47 8 27 34 35 38 41 14 20 16 17 36 3 9 6 32 39 40 44 23 18 49 12 29 13 33 25 26 48 37 30 31 21 42 43 15 11 5 2 19 28 45 22_  
**16**  
**27**  
_24 1 47 8 27 34 38 41 14 20 16 17 36 3 9 6 32 40 10 44 23 18 49 12 29 13 4 25 26 48 37 7 30 31 21 42 43 15 11 5 2 19 28 45 22_  
_24 1 46 47 8 27 34 35 38 14 20 16 17 36 3 9 6 32 39 40 10 44 18 49 12 29 13 33 4 25 26 48 37 7 30 31 21 42 43 15 11 5 2 19 28 22_

### _Nota bene_: The text file has _exactly 6_ (six) lines for a 6-number lotto game! And so on for each game format (e.g. 5 lines for lotto-5, etc.) The multiple-number lines can be in any order: frequency-based, random, ascending or descending order.

I input the file with those settings to each of the programs listed at the beginning of this groundbreaking ebook. The output is always the same: totally accurate, correct, and exact. That is, _1804_ lotto combinations in a file you can freely [_download (right-click) or view here_](https://saliu.com/freeware/ThreeNumbersPosition.OUT). If you load the file in _**Notepad++**_, the editor shows also the line numbers. Press _Ctrl+End_ to go to _end-of-file_ and see _total number of lines_ (combinations).

Good candidates for favorite numbers in fixed (strict) positions:

-   most frequent lottery numbers in their respective positions
-   lotto numbers with short skips in positional form
-   hot pairings
-   noticeable pairings (e.g. _1, 2_ in the 1st two positions; _48, 49_ in the last two positons, etc.)

The **Pair Grid** application has a module dedicated to frequency. You will notice in the reports that the top-2, even top-3 lotto numbers in their positions appear together in some drawings. There is a waiting time (strategy skip) for the occurrence, but the result is a drastic reduction of combinations to play. Of course, enabling other filters is needed (especially the very effective _**LIE Elimination**_ lottery strategy (a.k.a. _**reversed lotto strategy**_)).

Let's take a look at the programs involved in this original and unique lottery method.

### <u>1. Using <i>SkipSystem</i> to Generate Lotto Combinations with Favorite Numbers in Fixed Positions</u>

-   First, select the _type of game_ in the main menu
-   For example: _S: Lotto 6 (6-number lotto)_
-   Select function _G: Generate Combinations from Systems_.

![Skip lottery software creates positional systems for Powerball, Mega Millions favorite numbers.](https://saliu.com/images/skips-generator.gif)

-   Enter the name of the input file, either by typing or by copy-and-paste
-   To avoid typing (and typos!) you can use this editing procedure:
    -   You already created the input file (e.g. _ThreeNumbersPos.txt_)
    -   _"Open File"_ or _"Save File As"_ in **Notepad++**; with the filename highlighted, press _Ctrl+C_ to copy; or right-click, then _"Copy"_
    -   The input line in my software asks you for a filename; find a blank yellow-highlighted input line, right-click, then _"Paste"_ (or press **P** when you see the context menu)
    -   Or, click the _command prompt_ icon in the upper-left corner; select _"Edit"_ then _"Paste"_.

![Generate lottery combinations with favorite lotto numbers or bankers strictly in-position.](https://saliu.com/images/skips-input.gif)

<big>SkipSystem</big> offers a great advantage: a multitude of lottery game formats, plus horse racing and roulette. This grandiose piece of software works with those multijurisdictional lotto games as well. Yes, it handles Euromillions, Mega Millions, Powerball, etc. Here is a sample input file I created for Euromillions. It is based on frequency.

<u>Favorites (bankers):</u>

-   position #1: regular number **4** (the most frequent in the last 100 draws)
-   position #3: regular number **23** (the most frequent)
-   position #5: regular number **50** (the most frequent)

<u>Restrictions:</u>

-   position #2: play only the _25_ most frequent regulars
-   position #4: play only the _25_ most frequent regulars
-   star position #1: play only the _6_ most frequent stars
-   star position #2: play only the _6_ most frequent stars

**4**  
_15 8 17 16 12 23 20 14 24 25 11 21 7 4 18 30 19 6 3 26 27 28 29 22 31 23_  
**23**  
_38 39 30 31 43 40 42 28 32 44 23 25 41 27 34 21 48 36 37 33 29 45 46 35 24 50_  
**50**  
_4 2 3 1 6 8_  
_12 11 8 7 10_

The output file is _ThreePosEU.OUT_, consists of 9016 combinations (the exact number), and you can [_download (right-click) or view here_](https://saliu.com/freeware/ThreePosEU.OUT).

### <u>2. <i>PairGrid</i>: Generate Lottery Combinations with Bankers in Fixed Positions</u>

-   First, select function _F: Frequency Systems & Combinations_ on the opening screen.

![The positional frequency systems are powerful lottery tools to crack the jackpot.](https://saliu.com/images/pair-grid.gif)

-   Select function _G: Generate Combinations from Systems_ in the next menu.

![Place the hottest lotto numbers in their positions as favorite bankers to greatly improve chances.](https://saliu.com/images/pair-grid-combinations.gif)

-   Enter the input file you already created (e.g. _ThreeNumbersPos.txt_).

![Simple text files can generate fast combinations with favorite lotto numbers in fixed positions.](https://saliu.com/images/pair-grid-input.gif)

### <u>3. <i>Range-*</i>: Generate Lotto Combinations with Bankers in Strict Positions</u>

-   First, select function _N: Non-contiguous Ranges (9 2 13)_ on the opening screen.

![Ranges in lottery refer to lotto numbers in ascending order or ranked by frequency.](https://saliu.com/images/ranges-non-contiguous.gif)

-   Enter the input file you already created (e.g. _ThreeNumbersPos.txt_).

![Apply positional ranges with fixed favorite numbers to win lottery big-time.](https://saliu.com/images/ranges-input.gif)

-   Select the type of generating procedure: _lexicographic order_ (all combos); _random_ generation; or, only _count_ the combinations without generating.

![One input file with favorites or banker numbers works across multiple lottery programs.](https://saliu.com/images/ranges-combinations.gif)

### <u>4. <i>Super Utilities</i>: Generate Lottery Combinations with Favorites in Strict Positions</u>

-   First, select function _M: Make/break/position_ on the opening screen.
-   Next, select function _4: Break 6+/Positional ranges_ in the next menu.
-   Finally, select procedure _3: Positional ranges_.
-   Enter the input file you already created (e.g. _ThreeNumbersPos.txt_).

![The super lottery utility application has dozens of very useful functions to increase chances.](https://saliu.com/images/ranges-generator.gif)

![Best software tools and utilities in lotto are the creation of lottery grand-master Ion Saliu.](https://saliu.com/HLINE.gif)

## [Resources in Lottery Software, Strategies, Systems, Lotto Wheels](https://saliu.com/content/lottery.html)

Click above for a comprehensive directory of the pages and materials on the subject of lottery, lotto, software, systems, strategies. Most relevant to this article:

-   The Universal [_**Combinations Generator: Lottery, Powerball, Mega Millions, Euromillions, Two-In-One Lotto Games**_](https://saliu.com/combinations.html).
-   [_**Calculate combination lexicographic order of lotto data files**_](https://saliu.com/combination.html).
-   [**<u>Skip Systems</u>** _**for Lottery, Powerball, Mega Millions, Euromillions**_](https://saliu.com/skip-strategy.html).
-   Practical [_**Lottery Filtering, Lotto Filters in Software**_](https://saliu.com/filters.html).
-   [_**Lottery Strategy, Systems Based on Number Frequency**_](https://saliu.com/frequency-lottery.html).
-   [_**Lottery Utility Software**_](https://saliu.com/lottery-utility.html): Pick-3, 4 Lottery, Lotto-5, 6, Powerball, Mega Millions, Euromillions.
-   [_**Lotto, Lottery <u>Strategy in Reverse</u>: Not-to-Win Leads to Not-to-Lose or WIN**_](https://saliu.com/reverse-strategy.html).
-   [_**<u>LIE Elimination</u>: Lottery, Lotto Strategy in Reverse for <u>Pairs, Number Frequency</u>**_](https://saliu.com/lie-lottery-strategies-pairs.html).
-   [_**<u>LIE Elimination, Reversed Lottery Strategy</u> for Lotto Decades, Last Digits, Odd Even, Low High, Skips**_](https://saliu.com/lie-lotto-strategies-decades.html).
-   [_**Strategy Lotto Software on Positional Frequency**_](https://forums.saliu.com/lotto-software-position-frequency.html).
-   [_**The Best Strategy for Lottery, Gambling, Sports Betting, Horse Racing, Blackjack, Roulette**_](https://saliu.com/strategy-gambling-lottery.html).
-   [_**The Best Lottery Strategies: Foundation, Application of the <u>Lotto Strategy</u> Concept**_](https://software.saliu.com/lottery-strategy-lotto-strategies.html).
-   _"The Start Is the Hardest Part"_: [_**Play a Lotto Strategy, Lotto Strategies**_](https://forums.saliu.com/lottery-strategies-start.html).
-   [_**<u>Bright Software</u>: Lottery, Lotto, Pick Digit Lotteries, Powerball, Mega Millions, Euromillions**_](https://saliu.com/bright-software-code.html).
-   [_**<u>Ultimate Software</u>: Lottery, Lotto, Pick Digit Lotteries, Powerball, Mega Millions, Euromillions**_](https://saliu.com/ultimate-software-code.html).
-   [_**Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps, Programs**_](https://saliu.com/forum/lotto-book.html).
-   [_**<u>MDIEditor Lotto WE</u>: Tutorial, Software User Guide, Manual, Book, ebook**_](https://saliu.com/MDI-lotto-guide.html).
-   _**Download tons of**_ [**<u>lottery software, lotto programs</u>**](https://saliu.com/infodown.html).

![Lotto numbers with the best positional frequency reduce the odds and volume of tickets to play.](https://saliu.com/HLINE.gif)

**[Home](https://saliu.com/index.htm) | [Search](https://saliu.com/Search.htm) | [New Writings](https://saliu.com/bbs/index.html) | [Fundamental Formula](https://saliu.com/formula.htm) | [Odds, Generator](https://saliu.com/calculator_generator.html) | [Contents](https://saliu.com/content/index.html) | [Forums](https://forums.saliu.com/) | [Sitemap](https://saliu.com/sitemap/index.html)**

![Exiting site of the most excellent lottery software, strategies, systems to win lotto jackpots.](https://saliu.com/HLINE.gif)
