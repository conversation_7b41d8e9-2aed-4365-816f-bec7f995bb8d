---
created: 2025-07-24T21:26:40 (UTC +08:00)
tags: [lottery,strategy,lotto strategies,start play,procrastination,win,Pennsylvania Lottery,]
source: https://forums.saliu.com/lottery-strategies-start.html
author: 
---

# Playing Lottery Strategies, Lotto Strategy Means Start

> ## Excerpt
> The founder of lottery mathematics presents real lotto strategies, lottery strategy to win. Procrastination is an obstacle in winning the lottery.

---
**_<PERSON><PERSON><PERSON><PERSON>, Da Super Lottery Programmer, <PERSON>, <PERSON><PERSON><PERSON>, Strategist_**

[![<PERSON> teaches you how to create the best lotto wheeling strategies.](https://saliu.com/AxiomIon.jpg)](https://saliu.com/membership.html)  

## <u>The <i>Start</i> Is the Hardest Part in <i>Lottery Strategies</i></u>  
★ ★ ★ ★ ★

## By <PERSON>, _Founder of Lotto Mathematics, Lottery Programming Science_

![Start playing lottery strategies, lotto strategies win real money, in actual play.](https://forums.saliu.com/HLINE.gif)

### I. [Lottery Strategy Head Start](https://forums.saliu.com/lottery-strategies-start.html#strategy)  
II. [Software to Create Reports of Lotto Strategies](https://forums.saliu.com/lottery-strategies-start.html#reports)  
III. [Procrastination: Enemy of Lottery Strategies](https://forums.saliu.com/lottery-strategies-start.html#procrastinate)  
IV. [Software to Manage Lottery Data Files](https://forums.saliu.com/lottery-strategies-start.html#software)  
V. [My Experience with Lottery Strategies (2013)](https://forums.saliu.com/lottery-strategies-start.html#experience)

![It is important to start working with lottery software to discover good playing strategies.](https://forums.saliu.com/HLINE.gif)

## <u>1. Head Start to Lottery Strategies</u>

Sometimes, I keep my sword sharp by jousting in newsgroups or other proving grounds in the virtual world. That's how I began my activity in cyber world, in the _rec.gambling.lottery_ newsgroup of Kotkoducs and Kokostirks and Kotskarrs and, yes, Psychosamas... _O tempora! O mores!_

We appreciate the present by rewarding the past, while always facing to the future forcefully. Somehow like in this famous quotation:

-   _"Who controls the past, controls the future: who controls the present controls the past"_ (George Orwell, _Nineteen Eighty-Four_).

The DYNAMIC aspect of lottery is founded on the changing reality. It is _variation_ that created the human species as well. As Heraclitus put it, _"Change is the only constant in Cosmos"_. I'll exemplify by analyzing a lotto 6-49 results-file for Pennsylvania Lottery. Analytical software used: MDIEditor And Lotto WE.

I'll publish here only the beginning of the report. The entire text file can be downloaded freely (the links at the end of this axiomatic post).

![The report shows the filters for the pick 3 lottery game.](https://forums.saliu.com/lotto-filter.gif)

_Any\_1_ denotes a repeating ONE number from a BLOC of past draws, regardless of position. For example, _Any\_1 = 2_ means that AT LEAST ONE NUMBER is NOT a repeat from the bloc constituting of the past 2 draws (i.e. 12 numbers, not necessarily unique). That number IS a repeat, however, from the last 3 drawings (i.e. 18 numbers, not necessarily unique).

The MEDIAN is a very important parameter. The median for _Any\_1_ is **0**. That means that in at least 50% of the cases, ONE number REPEATED from the LAST DRAW.

Any\_2 refers to 2 REPEAT numbers; Any\_3 refers to 3 REPEAT numbers, etc. The MEDIANS are, respectively, 2, 4, 6, 9, 18. That means that in at least 50% of the cases: TWO numbers REPEATED from the LAST 3 DRAWS; THREE numbers REPEATED from the LAST 5 DRAWINGS; FOUR numbers REPEATED from the LAST 7 DRAWS; FIVE numbers REPEATED from the LAST 10 DRAWINGS; SIX numbers REPEATED from the LAST 19 DRAWS.

A lotto strategy can be immediately derived in MDIEditor And Lotto WE:

**MAX\_Any\_1 = 1** (i.e. AT LEAST ONE number from the LAST draw will appear in every combination generated);

Minimum values for _Any\_2_ to _Any\_6_: 2, 4, 6, 9, 18, respectively. That's a very powerful lottery strategy! There will be situations when the strategy will NOT generate any lotto combinations (no combonation will satisfy the restriction). A HIT situation can be found in a draw (line) like #38:

38 225+ 9- 3- 1 13+ 0- 2- 5+ 7+ 12+ 19- 2- 5- 5- 7- 12- 62-

The real lotto drawing in that situation was: _3 10 19 21 38 39_.

Of course, several other filters (restrictions) can be implemented; E.G. Min\_Ver\_6 = 25.

Not all lottery strategies were born equal, however. There is an even more powerful strategy with an even higher winning frequency. Just look at Ver\_6. The median is 62. Every lotto strategy is far more potent when a combination of both MINIMUM and MAXIMUM filter values are implemented. Thusly, a very tight but good lotto 649 strategy would be:

**Min\_Ver\_6 = 62 AND MAX\_Ver\_6 = 63**

Many more filters can be SAFELY enabled. Look at that PAST DRAWS filter. If you have a large D6 file (as required by Bright6) you can safely set the PAST DRAWS filter to 100000... even 1000000 (one million)! Hold on, there is more! You can PURGE in Bright6 the output file generated by MDIEditor And Lotto WE. Even more: You can reduce many more lotto combosnations by implementing the _**LIE Elimination**_ feature in Bright software...

The vast majority of the combos in the lotto output file will have 4 and even 5 numbers in common. One quick strategy: _WHEEL-FROM-FILE_ the output in **_Super Utilities_** (main menu in Bright6). Look at all those common numbers in the lotto combinations generated by a strategy...

```
<span size="5" face="Courier New" color="#ff8040"> 1  3  32  34  35  37 
 1  3  32  34  35  38 
 1  3  32  34  35  39 
 1  3  32  34  35  40 
 1  3  32  34  35  41 
 1  3  32  34  35  42 
 1  3  32  34  35  43 
 1  3  32  34  35  49 
...
 23  29  32  44  45  47 
 23  29  32  44  45  48 
 23  29  32  44  45  49 
 23  29  32  44  46  47 
 23  29  32  45  46  48 
 23  29  32  45  48  49
</span>
```

-   [_**<u>MDIEditor Lotto WE</u>: Lottery Software Manual, Book, ebook, Help**_](https://saliu.com/MDI-lotto-guide.html).

The _**Command Prompt**_ lotto software KOs _GUI_!

No doubt, MDIEditor And Lotto WE looks good. As one user put it: _"It is a work of art"_. The super application works good as well, especially for quick statistical analysis. It can generate also lotto combinations for smaller data files.

The graphical interface, however, represents a serious limitation. It is an obstacle as far as performance is concerned. I tried the same analysis for the D6 data file created in Bright6. There is no contest. I lost my patience waiting for D6 (almost 14 million lines) to just open! That's why I added the filters in MDIEditor And Lotto WE to Bright6. I can work with millions of combinations in 4+4 layers. I do want even more speed, no doubt about that. Right now, my lotto-6 software pushes today's computers to their technological limits.

Also, the Bright software allows for longer ranges of analysis in faster times. Thus, many more lotto strategies can be discovered. I did a filter analysis for 500 real lotto drawings. I noticed very large values for _Ver6\_1: over 1000_. I ran the **strategy checking** program (main menu). I noticed also efficient values for several other filters that showed some consistency. I publish in this post the strategy reports for the layer #1. But first, the fragment of _MD6.1_ sorted by _Ver6\_1_ in descending order.

![The checking of the lotto strategy is a neat report.](https://forums.saliu.com/strategy-1.gif)

![The next report shows the filters in MDIEditor and Lotto.](https://forums.saliu.com/strategy-2.gif)

![ The reports are valuable tools to find new lotto strategies to win the lottery.](https://forums.saliu.com/strategy-3.gif)

![The reports are similar for every layer of the lotto data file of drawings.](https://forums.saliu.com/strategy-4.gif)

The **key filter** of a powerful lotto strategy is **Ver6\_1 = 1000**.

I can add _Ion5\_1 = 1000, MAX\_One\_1 = 1, MAX\_Any\_1 = 1_. There is also _Del6_ for all 4 layers. In most cases, I can set _Del6_ to _100000_; even _1000000_ (1 million) in some layers, depending on the most recent hits of the strategy. Del6 is a potent and safe filter. It eliminates all past drawings and simulated combosnations in delta format. For example, one of the past lotto draws or simulated combinations was 7, 12, 15, 23, 38, 47; that combination will be eliminated (0-delta), plus 6, 11, 14, 22, 37, 46 (-1 delta), plus 8, 13, 16, 24, 39, 48 (+1 delta), etc. That filter is also very demanding and slows down the combination generating.

There is also a second part in getting rid of unwanted lotto combinations: the _**LIE Elimination**_ feature.

It is important to generate synchronized reports. In this case, the W6, MD6, and the pairing reports (_Pair6.REP_) should be done for the same data file. You can then use FileLines to cross-check lottery strategies for different platforms. Location: Menu #2, _T = Cross-Checking Strategies_.

![The lotto pairs are essential in creating lottery strategies.](https://forums.saliu.com/lotto-pairs.gif)

There are situations when the strategy does NOT generate one single combination. Obviously, it won't hit the very next lotto draw. But that's saving money! But the strategy does hit - something like once a year, on average. One lotto jackpot, even in two years, is an excellent expectation, isn't it? That's why patience is such a special virtue! I am not the example to follow (although I've had the real excuse of writing the software and developing theories and systems)! The links to the full strategy reports (free):

-   [Sorted MD6 Winning Report](https://saliu.com/freeware/MD6.1-SOR)
-   [Lotto 649 Strategy Ver6\_1=1000](https://saliu.com/freeware/st6.rep).

## <u>2. Generate Reports for Lotto Strategies</u>

A correct lottery strategy looks like the report that follows. Only one key filter was applied: Ver6\_1 = 1000 (from file: MD6.1). But there are plenty of other filters to choose from across the 8 reports. It's easy to spot that One\_1 = 0 and Any1\_1 = 0. That means the MAX value of the filters will be enabled. Those two filters are not exactly the same, as in older versions of LotWon software. You can also notice that most One filters are 0 or less than 1. The _Del6_ (based on _deltas_) filters are frequently high.

-   The strategy skips can be a very efficient tool in drastically reducing the amount of combinations to play. Here is one easy tactic. You notice that the lowest skip is 10. You first play the strategy immediately after 10 lottery drawings. If the strategy didn't hit, wait a round amount of drawings such as 25. The second lowest skip was 27. If still no win, play for the next 40 – 55 lotto drawings. If still no hit (that would be a very rare event), then stop playing the lottery strategy. Wait until it hits again. Meanwhile, you always have at least one more lottery strategy.
-   Notice also that the winning lotto drawings pass the restrictions in the INNER FILTERS in MDIEditor And Lotto WE.

![We must check any lottery strategies against past drawings.](https://forums.saliu.com/check-strategy-1.gif)

![Back-testing a lottery strategy is done layer by layer in the draw file.](https://forums.saliu.com/check-strategy-2.gif)

![The reports show different data from layer to layer, which offers more choices.](https://forums.saliu.com/check-strategy-3.gif)

...

![ Procrastination is the main obstacle in winning the lottery with Ion lotto software programs.](https://forums.saliu.com/check-strategy-4.gif)

...

## <u>3. Procrastination: Enemy of Lottery Strategies</u>

Initially, I wanted to entitle this post _"Do NOT Procrastinate like Ion Saliu!"_ But it wouldn't have been fair to yours truly. In my defense, I have been really busy. First, I updated the _Skip System_ software, plus the positional range program.

From there, I upgraded the powerful LIE elimination software (which still remains reserved for now). It was during that time I mentioned I was chasing a strategy for the pick-3 lottery.

This very forum was viciously attacked, like many other forums run by the _vBulletin_ software. I believe I fixed the security issues. Unfortunately, the _vBulletin_ update introduced new issues. I decided not to update my forum software until they fix all the bugs and quirks.

Moreover, there were some issues with my website as warned in my webmaster tools accounts. I hope I won't do such changes to my webpages any more. I've had enough!

Finally, I got a break. I found the time and nerves to apply a lottery strategy to the pick-3 game in Pennsylvania. It happened yesterday, November 4, 2013. I bought also food and red wine. I cooked, had wine and watched a Netflix movie. It was a great film that triggered special memories. It was about life in Prague during the Soviet invasion: _"The Unbearable Lightness of Being"_. I was tipsy at the end of the movie. I saw the end of the TV night news when they announced the winning lottery numbers. I was convinced I won.

The winning daily number was **8-6-6**. In fact, one of the 3 numbers I played was _2-6-6_! I noticed it this morning. I was still tipsy and then I drank some more wine to lift my spirits... BRRRRRRRRAHAHAHAHA!!!!!!!

Point is I didn't play the lottery today. I never drink and drive... not even after a night of drinking. Today would have been an even better day to play. The lottery strategy is based on just one key filter: TOT\_1(no straight repeats in 4000 drawings). Yesterday, my strategy (I added a second safe filter) generated just 3 combosnations. Today, it generated 7 straight sets - a frequent amount when the lottery strategy hits. The minimum amount of combinations in the hit situations is 5. So, yesterday was not the right moment to play - but I was decided to win over procrastination!

I create a text file based on a strategy and name it meaningfully. In this case the name is _STR3-Tot14000.txt_. Here is a fragment:

![This is a real-life pick lottery strategy that hit the first prize.](https://forums.saliu.com/play-1.gif)

![The winning pick lottery strategy had a few tickets to play.](https://forums.saliu.com/play-2.gif)

When I started mentioning that strategy here, it was between PA Lottery drawings #137 and #104. The strategy hit in 10 drawings or so for the first time. Then it hit again after 38 drawings. The strategy should not be played immediately after a hit. This time, I looked at the situation as if I had saved 42 drawings! My consolation for today is that maybe the strategy will hit after a skip of 50 drawings.

But, I will play again tomorrow (no more drinking for a long while!) For today, the strategy generated 7 combinations. It is 5 PM when I'm writing this post. The Pennsylvania Lottery drawings are conducted at 8 PM. I will publish the pick-3 straight sets here rationalizing that no Pennsylvanian will read this post before 8 PM!

```
<span size="5" face="Courier New" color="#ff8040"> 1  9  9
 2  4  4 *
 2  6  6 *
 2  8  4 *
 5  7  2
 6  1  9
 7  6  4
</span>
```

The numbers marked by the asterisk are repeats from yesterday's strategy.

I just created a lottery software package that makes users' lives easier. The package updates the lottery data files in one common folder. Then, the data files are updated in their corresponding Bright folders. I present the new package - _LotteryData_ - in the next section.

## <u>4. Software to Manage Lottery Data Files</u>

Software package name: **LotteryData.zip**.

There is nothing really new here as far as the programs are concerned. Only the concept is new: The users can now update the lottery data files in one common folder. Then, the data files are updated in their corresponding _**Bright**_ folders. A screenshot of the main menu:

![Lottery Data Files is a collection of software to work with past winning lotto numbers.](https://forums.saliu.com/LotteryData.gif)

I strongly recommend creating a shortcut on your _Desktop_. You first create a folder in _Windows (File) Explorer_. Right-click **C:**, then _New_, then _Folder_, then type _LotteryData_ as folder name. You download the ZIP file to that folder and unzip it in the same directory. It will be easier for you to keep that folder name, as an important batch file uses that name. Of course, you can easily edit that file in Notepad++.

After you unzipped the package, you open it in Windows Explorer. Right-click on **LotteryData**, then _Send to_, then _Desktop (Create shortcut)_. Here is the shortcut on my desktop:

![The lottery software works best at the command prompt run by a desktop shortcut.](https://forums.saliu.com/LotteryDataShortcut.gif)

You configure the shortcut following the easy steps I presented on this page:

-   [_**Run Lottery Software at Command Prompt in Windows XP, Vista, Windows 7, 8, 10**_](https://saliu.com/gambling-lottery-lotto/command-prompt.htm).

This lottery software package relies on the best text editor out there: **Notepad++**. It is free to download and use for an unlimited time with no strings attached.

-   [_**Home of Notepad++**_](https://notepad-plus-plus.org/).
    
    If you are not a computer programmer, you should install the great application in one of the two locations:
    
    **C:\\Program Files (x86)\\Notepad++\\notepad++** - for 64-bit Windows, or -  
    **C:\\Program Files\\Notepad++\\notepad++** - for 32-bit Windows.
    
    Of course, if you are a programmer and have **PowerBasic Console Compiler**, you can edit the source code file I included in the package: **LotteryData.bas**.
    
    One useful feature of **Notepad++** is column selection. You can select a rectangular bloc of text and delete it easily. For example, delete the dates or other information you find on the lottery websites (e.g. _Payouts_). You hold down the _Alt_ key, then click at the beginning of the block of text, then move the mouse pointer to the end of the rectangular bloc of text. Here is how I do it with Pennsylvania Lottery data files I copy and paste from their Web site:
    
    ![My lottery software is enhanced nicely by the best text editor: Notepad plus plus.](https://forums.saliu.com/NotepadLottery.gif)
    
    Axiomatic one, I work with this lottery utility this way:
    
    1) Click the desktop shortcut  
    2) Press _6_ (or _3_) to open the editor  
    3) Open all data files in the editor (one after another)  
    4) Open a browser and open the bookmarked lottery website  
    5) Go to the results for each game, select the range of drawings that I am missing, then copy the results  
    6) Paste the results in the respective lottery data file  
    7) Edit data, if necessary (i.e. delete all unnecessary information and preserve only the lottery numbers;  
    make sure the numbers are separated by blank spaces or commas)  
    9) Repeat for every data file  
    10) Exit the editor and return to the main program \*  
    11) Press _S_ to sort the data files as required by my lottery software  
    12) I can do other operations (e.g. generate the _frequency reports_ for my lottery games)  
    13) I make sure I never leave the program without backing up (updating) all my files; always press _B_.
    
    \* I pinned the _Notepad++_ editor to the taskbar. I can start and work with the editor independently from the _LotteryData_ application. That way, I don't have to close the editor — it can stay open at all times. The open editor helps me also with managing other files, especially when they have long names.
    
    Again, you can edit the batch file that updates your files: **BkUpLotto.bat**. You open it in the editor and make changes to fit your PC configuration. My lottery data files are named **PA**\*; e.g. **PA-3** for the _pick 3 results in Pennsylvania Lottery_. In my case, I also backup to an external drive (named **J:** in my PC configuration). This are the contents of the backup batch file included in this software package:
    
    ECHO OFF  
    CLS  
    rem Be sure to surround by " " all directories with blank spaces in names; e.g. "C:\\My Web Pages\\\*.\*"  
    rem You can disable a command (line) by adding REM or rem in front of the line  
    REM You can add your own commands by editing the lines in this BATch file; or -  
    rem by writing your own lines based on your particular folders/files;  
    rem the only unchanged text is represented by:  
    REM XCOPY and the switches: /S /D /I /Y
    
    ECHO ON  
    XCOPY PA-3 C:\\Pick3 /S /D /I /Y  
    XCOPY PA-4 C:\\Pick4 /S /D /I /Y  
    XCOPY PA-5 C:\\Lotto5 /S /D /I /Y  
    XCOPY PA-6 C:\\Lotto6 /S /D /I /Y  
    REM XCOPY PA-Q C:\\Quinto5 /S /D /I /Y  
    REM XCOPY PA59-35 C:\\PBall /S /D /I /Y  
    REM XCOPY EURO.DAT C:\\EURO /S /D /I /Y  
    REM XCOPY MEGA.DAT C:\\MEGA /S /D /I /Y  
    XCOPY C:\\LotteryData _J:\\LotteryData_ /S /D /I /Y  
    PAUSE
    
    -   **WARNING!** The **Edge** browser that comes as the default in _Windows 10_ can be a serious hazard! I used it once to download the Pennsylvania Lottery results. The drawings are nicely presented in _tables_. As usually, I copied-and-pasted data into **Notepad++**. The data files turned into horrible content after _Sorting_, with lots and lots of zeros. I have absolutely no clue how the **Edge** browser handles copy-and-paste. Fortunately, I always have fresh backups of my lottery data files. You should try for yourself and see how the **Edge** browser works in your particular situation. Do it for one file only -- after you made sure you have an up-to-the date backup.
    
    Registered users can download the lottery data file software application directly from here:
    
    -   [_**Download LotteryData.zip**_](https://saliu.com/pub/LotteryData.zip).
-   I highly recommend also you visit this Web page that helps a lot with the backup of a computer up to its entirety. The backup software is the best and fastest there is, and it is totally FREE:
