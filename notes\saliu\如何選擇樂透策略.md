選擇樂透策略是 Ion Saliu 彩票軟體的核心功能之一，其方法論基於數學過濾器和數據分析，旨在縮減組合數量並提高中獎機會。Saliu 的軟體將樂透中獎視為一種隨機但有其規則的動態現象，而非盲目的隨機性。

以下是如何選擇樂透策略的綜合指南：

**一、策略的基礎概念** Saliu 的軟體主要支持兩種策略類型：

- **順向樂透策略 (Straight Lottery Strategy)**：直接生成並投注那些預期會中獎的組合。
- **逆向策略 (Reversed Strategy) 或 LIE 消除 (LIE Elimination)**：這是 Saliu 特別強調並建議的方法。其理念是**故意設定不會在下次開獎中中獎的樂透過濾器**，從而「從損失中獲利」。也就是說，軟體會產生「預計不會中獎」的組合，然後將這些組合從您的投注選項中排除，以大幅減少投注成本，並提高實際投注的中獎概率。Saliu 認為，由馬可夫鏈方法生成的組合通常是 LIE 消除的良好候選，因為它們預計在接下來的幾次開獎中不會贏得大獎。

**二、數據準備與報告分析** 選擇策略的第一步是準備充足的歷史開獎數據並生成分析報告。

1. **數據檔案準備**：確保您的彩票數據檔案是最新且正確的，並且包含足夠的歷史開獎記錄。Saliu 的軟體推薦使用非常大的數據檔案（例如，對於樂透 6/49 遊戲，需要至少 1200 萬行的數據，包括真實和模擬開獎數據）。您可以透過軟體中的 `Make D*` 功能來創建這些合併的數據檔案。
2. **生成報告**：
    - 執行 `R = Report Pairs, Frequency (Hot)` 功能。這會自動創建多個關鍵的文本文件，如 `MarkovNumbersL6`（熱門到冷門數字）、`MarkovPairsPivL6`（帶樞紐的配對）、`MarkovPairsNoPL6`（不帶樞紐的配對）和 `MarkovFollowersL6`（馬可夫鏈追隨者）等，這些文件是後續組合生成所必需的。
    - 運行統計報告，包括**頻率報告 (Frequency Reports)** 和 **跳躍報告 (Skip Reports)**。這些報告會顯示每個號碼或號碼組的出現頻率、跳躍值（兩次中獎之間的間隔）和相關統計數據（如中位數、平均值和標準差）。

**三、選擇過濾器與設定策略參數** 策略的核心是設定各種**過濾器**（或稱限制），以篩選掉不希望出現的組合。

1. **理解過濾器概念**：
    
    - 過濾器是消除樂透組合的參數，可以設定最小值和最大值來定義允許的組合範圍。
    - Saliu 區分**動態過濾器**（基於不斷變化的趨勢）和**靜態過濾器**（如奇偶數、大小數比例）。他主張動態過濾器更為有效。
2. **利用報告中的數據來設定過濾器**：
    
    - **中位數 (Median)**：這是設定過濾器最關鍵的指標之一。許多過濾器的最佳設定值與其統計中位數緊密相關，特別是 FFG 中位數（在 50% 確定性下事件發生的試驗次數）。通常，「低於中位數」的策略能帶來更多中獎機會且所需號碼較少。軟體會預先計算好中位數，方便使用者查看。
    - **趨勢分析 (Trend Analysis)**：觀察過濾器值在歷史開獎中的變動趨勢。例如，當某個過濾器連續三次顯示增加（+號）後，預期下一次開獎很可能會出現減少（-號），反之亦然。這表示趨勢反轉的機率很高（根據 FFG，趨勢反轉的機率為 90%）。
    - **號碼追隨者 (Number Followers) 和配對 (Pairs)**：利用馬可夫鏈分析器生成的 `MarkovFollowersL6` 和 `MarkovLikePairsL6` 文件。這些文件基於歷史數據識別出號碼之間的「追隨」關係或經常同時出現的「配對」。您可以選擇從這些「類似追隨者的配對」或「傳統馬可夫鏈」中隨機選取號碼來生成組合。
    - **跳躍 (Skips)**：分析每個號碼或號碼組的跳躍模式。跳躍值較低的號碼（表示最近頻繁出現）通常被認為是「熱門」號碼，適合「順向」投注；而跳躍值很高的號碼（很久未出現）則可能是「冷門」號碼，適合用於 LIE 消除。軟體會生成詳細的跳躍圖表和統計報告。
    - **「古怪」過濾器值 (Eccentric Filter Values)**：這些是歷史報告中極端高或極端低的值，超出正常統計範圍。設定這些極端值作為過濾器（例如，中位數乘以 3 或 4），可以大幅消除大量組合，儘管它們很少出現。
    - **其他常用過濾器**：
        - **ONE, TWO, THREE, FOUR, FIVE, SIX 過濾器**：用於消除過去開獎中重複的單個數字、配對、三連號等。
        - **Any / Ver 過濾器**：`Any` 指任意位置的重複號碼，`Ver` 指特定位置的重複號碼。
        - **Deltas (差值)**：分析開獎號碼之間差值的模式，例如，號碼 7 和 13 的差值是 6。
3. **組合過濾器**：通常會組合多個過濾器來制定更精確的策略。但 Saliu 也指出，有些過濾器彼此重疊，可能導致多餘設定。
    

**四、生成組合與策略測試** 在設定好過濾器後，您可以使用軟體生成組合，並對策略進行回測。

1. **組合生成**：
    - Saliu 的 `Markov Chains` 軟體提供五種組合生成方法：`H`（熱門數字）、`P`（帶樞紐的配對）、`N`（不帶樞紐的配對）、`M`（基於類似追隨者配對的馬可夫鏈風格組合）和 `C`（傳統馬可夫鏈組合）。
    - **馬可夫鏈風格組合 (`M` 和 `C` 功能)**：這兩種生成器都會去除重複的組合，確保生成獨特的彩票組合。號碼是從 `MarkovLikePairsL6`（M 功能）或 `MarkovFollowersL6`（C 功能）中隨機選取的。
2. **策略測試 (Strategy Checking)**：您可以刪除數據檔案頂部的一些歷史開獎（例如 100 期），然後使用修改後的數據檔案運行策略來生成組合。之後，將這些生成的組合與您之前刪除的真實開獎數據進行比對，以評估策略在過去的表現（即其「成果週期」或 `cycle of fruition`）。這有助於判斷策略的有效性和何時開始應用。
3. **LIE 消除應用**：對於透過各種過濾器（包括馬可夫鏈風格的生成器）產生的組合，Saliu 強烈建議將其用於 **LIE 消除**。這意味著這些組合被認為是預計不會中獎的「謊言」組合，因此可以從您的實際投注中剔除，從而節省成本。

**五、重要的提醒**

- **沒有絕對的確定性**：Saliu 強調，在整個宇宙中都沒有絕對的確定性。即便應用了最先進的數學理論和軟體，也無法保證 100% 的中獎率。他的軟體旨在提供更好的理解和基於機率論的優化決策，而非精確預測未來。
- **耐心與勤奮**：選擇和應用樂透策略需要耐心和勤奮，因為它涉及到大量的數據分析和參數設定。
- **軟體自動化**：Ion Saliu 的軟體套件（如 `MDIEditor Lotto WE`、`Bright` / `Ultimate Software`、`Super Utilities` 等）旨在自動化這些複雜的分析、報告生成和組合生成過程。

總之，選擇樂透策略是一個迭代的過程，它需要您深入了解歷史數據，利用軟體提供的各種數學過濾器和分析工具（特別是基於中位數、趨勢、追隨者和跳躍），然後透過回測來驗證和調整您的策略，最終以「LIE 消除」等方式來降低投注成本並增加獲利機會。