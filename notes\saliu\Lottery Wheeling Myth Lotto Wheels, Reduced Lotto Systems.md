---
created: 2025-07-24T22:10:03 (UTC +08:00)
tags: [lotto wheels,wheeling,abbreviated,lotto systems,reduced,wheel,lottery,software,system,play,loss,analysis,wheeling numbers,]
source: https://saliu.com/bbs/messages/11.html
author: 
---

# Lottery Wheeling Myth: Lotto Wheels, Reduced Lotto Systems

> ## Excerpt
> The ordinary lotto wheels, or reduced wheeling systems are simple myths of the lottery. The static wheels accelerate the loss to harm lottery players.

---
**_Da Super <PERSON>to Wheeler and <PERSON>ly<PERSON>_**

[![<PERSON> warns lottery players about the shortcomings of paying for lotto wheels.](https://saliu.com/AxiomIon.jpg)](https://saliu.com/membership.html)  

## <u>The Myth of the <i>Lotto Wheels</i> or Abbreviated Lotto Systems</u>  
★ ★ ★ ★ ★

## By <PERSON>, _Founder of Lottery Wheeling Mathematics_

![Generate the best lotto wheels, abbreviated systems with special lottery wheeling software.](https://saliu.com/bbs/messages/HLINE.gif)

## <u>1. Introductory Notes to the Myth of the Lotto Wheels</u>

-   This article will prove, through statistical analysis of real lottery data (drawings), the negative effect of playing static **lotto wheels**. The so-called lotto wheels, or reduced lotto systems are simple myths of the lottery. The static (traditional, ordinary) lotto wheels accelerate the loss to harm lottery players compared to playing random combinations.

I say in one of my posts ([_**Compare Lotto Software Programs, Lottery Wheeling, Gail Howard**_](https://saliu.com/bbs/messages/278.html)): _"The game of blackjack has its myth: **Counting cards**. The lottery has two myths: **frequency reports** (**cold or hot** numbers) and **lotto wheels**."_

Actually, the static (unbalanced) lotto wheels do more harm than good. The net results of the so-called abbreviated lotto systems or wheels: They accelerate the loss. (Well, isn't acceleration what the wheel is all about?) The so-called experts have always stressed the 'advantage' of wheeling the lotto numbers. (The stress always breaks the wheel, doesn't it?) Granted, many people honestly believe the lotto wheels create an advantage. But for a number of “experts” the lotto wheels really create an advantage: They sell wheels or wheeling software. (Nothing wrong with that: The wheel created big industries, such as wagon making, and tires, and Le Tour de France.)

## <u>2. Mathematical and Statistical Analysis of Lotto Wheels Performance</u>

All of a sudden, the ol' good LotWon lottery software showed how damaging the lotto wheels can be. It struck me that the combination lexicographical order can be a tool in creating lottery wheels. I used COLORDER to create lotto wheels based on the official lottery odds. For example, the probability of getting _3 of 6_ in a 6/49 lotto game is '1 in 57'. They say the “world record” for a 49-number wheel guaranteeing _3 of 6_ is 163 combinations. I say to myself: ”Self, why not 57 combinations?” I ran my 'combination lexicographical order' lotto wheel against a real draw file (UK lottery, 642 draws). The 57-line wheel very closely followed the lotto odds. But why did I need to put all that effort in creating a wheel? Wasn't it easier to simply play 57 random combinations?

I put <big>MDIEditor Lotto</big> to work. I generated several 57-combination sets. I checked all of them for winners against the same UK National Lottery draws file. The results were strikingly similar to the first case. Every 57-line set (wheel) very closely followed the lotto odds. Here is how the last random set fared:

Total Hits: 0 _6 of 6_; 0 _5 of 6_; 41 '4 in 6'; 666 '3 in 6'  
Total Cost: 642 x 57 = 36,594 units  
Total Winnings: (666 x 10) + (41 x 100) = 10,760 units  
Net Loss: 25,834 units.

How about the "world champion", the 163-combination lotto wheel guaranteeing 100%+ '3 in 6'? Well, that wheel, too, followed closely the official lotto odds:

Total Hits: 0 _6 of 6_; 1 _5 of 6_; 104 '4 in 6'; 1872 '3 in 6'  
Total Cost: 642 x 163 = 104,646 units  
Total Winnings: (1872 x 10) + (104 x 100) + (1 \* 3000) = 32,120 units  
Net Loss: 72,526 units.

The bottom line is far more significant than any of the elements leading to it. They say: "The more you play, the higher the winning probability". Well, think again! In most situations, the more you play, the more you lose! That's because the lottery 'house advantage' or the 'house edge' is applied to higher quantities. "The more you play, the higher the winning probability" is valid only in rare situations. It is the case of mathematically founded strategies. The player must overcome not only the odds, but also the 'percentage advantage'. The casinos LOVE when the players bet big, and then bigger. With one notable exception: The casino executives would love "to strangulate" the system players who bet big and bigger.

There is a plethora of terms describing the additional obstacle the players face. _House advantage, house edge, percentage advantage, takeout, vigourish (?)…_ I think of a unified term, defined with more precision. The lottery commissions “take out” 50% of ticket sales, therefore distributing 50% to prizes. But the _house edge_ is not necessarily 50%: It depends on a specific game and prize. The best term is _player's disadvantage_. For example, in a lotto 6/49 game, the odds of winning the lotto jackpot are _1 in 13,983,816_; if the winner is paid $1,000,000 for a $1 winning ticket, the _player's disadvantage_ is {1 – 1000000/13983816} = {1 – 0.0715) = 92.8%. At the double-zero roulette, the probability of winning a straight-up bet is _1 in 38_. The casino plays $36 for a $1 winning bet. The _player's disadvantage_ is {1 – 36/38} = {1 – 0.9474} = 5.26%.

The two reports (fragments) are at the end of this writing. You can replicate them for any data file (real lotto draws) and any wheel. Randomly generated or painfully designed. You can use great free lotto software to assist you. In addition to **MDIEditor Lotto WE**, **Winners** is a very helpful tool. It can check for winning numbers in just about any lottery game. Winners also reports the wins in any lotto wheel, including for the latest games of Powerball, Mega Millions, and Euromillions.

## <u>3. Honest Opinion on Lotto Wheels and Number Wheeling</u>

I put some trust in lotto wheeling. It happened quite some time ago, last millennium! I had at my fingertips a very serious wheeling book: _Swedish Lotto Systems_ by Thomas H. Olsson. The book was first published in Sweden by Chavez Nyman Trading, 1985. It was published in the USA by Intergalactic Publishing Company in 1986. The book presented a variety of abbreviated systems for 6-number lotto games. It also showed the winning guarantees or percentages for several situations: _N in 6_, _N in 5_, _N in 4_. But then again, those lotto wheels have extra lines, compared to the odds of the lottery game.

I even improved on some of the wheels in **Swedish Lotto Systems**, while playing with COLORDER. The book had a 10-number system in 5 lines; I did it in 3 combinations (exactly the lotto odds!) The book had an 11-number lotto wheel in 11 lines; I did it in 5 combinations. I even created an 18-number system in 19 combinations. While it is not 100% guaranteed for a particular lottery drawing, it does follow the 6/18 odds for _4 of 6_. If successful 10 times, it will hit _4 of 6_ around 10 times! Probably better than that!

Later, I have written specific lotto wheeling software based on lexicographical order or indexing: <big>LexicoWheels</big>.

[![Lottery Utilities Software, Tools, Lotto Wheels, Systems.](https://saliu.com/ScreenImgs/lexico-lottowheels.gif)](https://saliu.com/free-lotto-tools.html)

<big>• </big> Forget about designed loto wheels! Better still, forget about lottery wheels altogether! The wheels only make money for those who sell lottery software. The lotto wheels and wheeling are the essence of all lottery software, except for LotWon. Some pushed the envelope so hard, that everything in lotto is now a... wheel! It is the only justification left for most lotto software packages. They say now _full wheels_. That's an oxymoron, a contradiction in terms. The _lotto wheels_ are, in fact, _reduced lotto systems_. On the other hand, _full_ is a reference to _all_ the combinations of a lotto set. Wheeling lotto numbers is a way of reducing the number of combinations to play, instead of playing all lotto numbers in a set. They can't have it both ways: _full_ and _reduced_!

![You lose if playing static loto wheels, non-mathematical reduced lottery wheeling systems.](https://saliu.com/bbs/messages/HLINE.gif)

## <u>4. Essential Resources in Lotto Wheels and Lottery Wheeling</u>

Data analyses show beyond doubt that the lotto wheels are beaten by random play. The wheels are optimized for their specific guarantees. That is, a _4 of 6_ lotto wheel performs well as far as the _4 of 6_ prize is concerned. At the same time, the wheel performs worse at hitting higher prizes (5 of 6, for example).

But if one has a strategy that picks the best to play 18 numbers, one is better served if playing random combinations consisting of the 18 numbers. If the 18-number wheel consisted of 19 combinations (the odds of getting exactly _4 of 6_ for 18 lotto-6 numbers), it will hit the _4 of 6_ prize according to the odds. The player improves the chance of hitting _5 of 6_, even _6 of 6_ if playing 19 random combinations of the same 18 numbers.

A short explanation for this assessment founded on thorough data analysis: Lotto is random; wheeling is less random, since the combinations are hand-built. Randomization is the key explanation. My lotto freeware can be easily set to play only certain numbers. The numbers to be eliminated from the field can be written at the top of the _LEAST_ files. In MDIEditor and Lotto the numbers NOT to be played can be typed directly in the filter input form.

• There is also another major factor: the lexicographical order or the index of the combinations in the set. A wheeled set such as the wheel “champion” has a horrendous configuration as far as the indexes are concerned. Half of its combinations have an index of over 13 million! That is, half of the wheel lines are from the bottom million of the 6/49 set! Read this page to see the wheel-163 indexes compared to a 57-line set with very symmetrical lexicographical order indexes: _Software for Balanced Lotto Wheels Based on Lexicographic Order, Indexes_.

I do not recommend wheeling lotto numbers anymore. You still want to play lotto wheels from time to time? You can also improve your lotto wheeling by running first **Wheel6**. It is now option _W = Wheel On-the-Fly_ (menu #2) in the most powerful suite of lotto software to date (2011): <big><b>Bright</b></big>. Generate as many wheeled lotto combinations as until the program seems to have stopped. Use the partially wheeled output file as … input to **Wheel Check**. It is option _C = Check Systems_ in menu #4. The program will generate all combinations that are missing from the lotto wheel in order to assure the minimum guarantee.

![Lotto wheeling software generates on-the-fly randomized, balanced lotto wheels.](https://saliu.com/ScreenImgs/wheel-lotto.gif)

The wheels are _balanced_. Keep in mind that they are also _randomized_ — a keyword here. The randomized wheels perform better than ordinary wheels (_static lotto wheels_). The lexicographic indexes are very important. Read the article presenting the lotto system packages: _Lotto Wheels for Lotto Games Drawing 5, 6, Or 7 Numbers_. The systems and the software are free to run forever. Only downloading requires a reasonable one-time fee.

![Statistical analyses always show static lotto wheels to perform worse than random play.](https://saliu.com/bbs/messages/HLINE.gif)

## [<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>](https://saliu.com/content/lottery.html)

It lists the main pages on the subject of lottery, lotto, software, wheels and systems.

-   Download [_**Lottery Software Tools, Lotto Wheeling Software Wheels**_](https://saliu.com/free-lotto-tools.html):
-   Wheel-632, Wheel-532, the best on-the-fly wheeling software; applies real lottery filtering.  
    ~ Superseded by the most powerful integrated packages
-   Pick532, Pick532 and, especially, the Bright software packages.
-   Combinations, the universal combinations generator for any lotto, Keno, Powerball, Mega Millions, Euromillions;
-   LexicoWheels, generator of lotto wheels based on lexicographical indexing; applies to any lotto, Keno, Powerball, Mega Millions, Euromillions or two-in-one lotto games.
-   WheelCheck5, WheelCheck6, software to verify lotto wheels for missing combinations and generate lotto-5 and lotto6 systems.
-   LottoWheeler, lottery wheeling software: fill out lotto wheels with player lotto numbers. Supersedes the old-time favorite FillWheel (still offered). The two pieces of software replace the theoretical lotto numbers in the SYS/WHEEL files by your picks (the lotto numbers you want to play).
-   Shuffle, SuperFormula to shuffle (randomize, randomly arrange) your lotto picks. Repeat the randomizing procedure several times. The randomized wheels can be used directly, without shuffling your lottery picks first.
-   The Main [_**Lotto, Lottery, Software**_](https://saliu.com/LottoWin.htm), Strategy, Wheeling Page.  
    Presenting software to create free winning lotto, lottery strategies, systems based on mathematics. Get your lotto systems or wheels, the best lottery, lotto software, combinations, winning numbers.
-   **Free** [_**<u>Lotto Wheels</u>**_](https://saliu.com/lotto_wheels.html) _**for Lotto Games Drawing 5, 6, 7 Numbers**_.  
    The most advanced theory of lotto wheels or reduced lottery systems. Get also original lotto wheels for lotto games drawing 5, 6, or 7 numbers: Balanced, randomized, and free.
-   [_**Create, Make Lotto Wheels in Lottery Wheeling Software or Manually**_](https://saliu.com/lottowheel.html).
-   [_**<u>Positional</u> Lotto Wheels, Reduced Lottery Systems <u>Position-by-Position</u>**_](https://saliu.com/positional-lotto-wheels.html).
-   **Free** [_**Lottery Wheeling Software for Players of Lotto Wheels**_](https://saliu.com/bbs/messages/857.html).
-   Fill out lotto wheels with player's picks (numbers to play); presenting FillWheel, LottoWheeler lotto wheelers.
-   [_**Copyright Lottery Numbers, Combinations, Lotto Wheels, Mathematical Relations, Formulas, Equations**_](https://saliu.com/copyright.html).
-   WHEEL-632 available as the [_**Best On-The-Fly Wheeling Software**_](https://saliu.com/bbs/messages/wheel.html) — applies real lottery filtering.
-   [_**Software to Verify Lotto Wheels**_](https://saliu.com/check-wheels.html) for missing combinations and generate lotto 5, 6 abbreviated (reduced) systems.
-   [_**Balanced Lotto Wheels, Lexicographical Order Lottery Wheeling**_](https://saliu.com/bbs/messages/772.html).
-   [_**Check WHEEL System, Lotto Wheels Winners**_](https://saliu.com/bbs/messages/90.html).
    
    <u>The next two resources provide you with absolutely everything you need to play lotto-6 wheels at the highest level of efficiency:</u>
    
-   ☛ [_**The Best Lotto Wheels for 9, 12, 18, 21 Numbers**_](https://saliu.com/bbs/best-18-number-lotto-wheels.html)
-   ☛ [_**Lottery Triples: Super Strategy with Lotto Wheels**_](https://saliu.com/lotto-triples.html)  
    (based on the most frequent _3-number groups_; also, the best _pairs_ are very efficient).
-   [_**Powerball Wheels**_](https://saliu.com/powerball_wheels.html).
-   [_**Mega Millions Wheels**_](https://saliu.com/megamillions_wheels.html).
-   [_**Euromillions Wheels**_](https://saliu.com/euro_millions_wheels.html).

![The real-life statistical analytical charts always show a loss when playing lotto wheels.](https://saliu.com/bbs/messages/HLINE.gif)

**[Home](https://saliu.com/index.htm) | [Search](https://saliu.com/Search.htm) | [New Writings](https://saliu.com/bbs/index.html) | [Software](https://saliu.com/infodown.html) | [Odds, Generator](https://saliu.com/calculator_generator.html) | [Contents](https://saliu.com/content/index.html) | [Forums](https://forums.saliu.com/) | [Sitemap](https://saliu.com/sitemap/index.html)**

![Thanks for visiting the site of dynamic lotto wheels, lottery systems, plus wheeling software.](https://saliu.com/bbs/messages/HLINE.gif)
