using Test
using SaliuSystem0009.DataProcessor

@testset "DataProcessor.jl tests" begin
    # Test LotteryDrawing struct
    drawing = LotteryDrawing("2023-01-01", [1, 2, 3, 4, 5, 6])
    @test drawing.date == "2023-01-01"
    @test drawing.numbers == [1, 2, 3, 4, 5, 6]

    # Test parse_data_file function (requires a dummy data file)
    # Create a dummy data file for testing
    dummy_data_path = "dummy_data.csv"
    open(dummy_data_path, "w") do f
        write(f, "2023-01-01,1,2,3,4,5,6\n")
        write(f, "2023-01-02,7,8,9,10,11,12\n")
        write(f, "2023-01-03,13,14,15,16,17,18,19\n") # Invalid drawing
    end

    drawings = parse_data_file(dummy_data_path)
    @test length(drawings) == 3
    @test drawings[1].date == "2023-01-01"
    @test drawings[1].numbers == [1, 2, 3, 4, 5, 6]

    # Test validate_and_clean_data function
    validated_drawings = validate_and_clean_data(drawings)
    @test length(validated_drawings) == 2
    @test validated_drawings[1].numbers == [1, 2, 3, 4, 5, 6]
    @test validated_drawings[2].numbers == [7, 8, 9, 10, 11, 12]

    # Clean up dummy data file
    rm(dummy_data_path)
end
