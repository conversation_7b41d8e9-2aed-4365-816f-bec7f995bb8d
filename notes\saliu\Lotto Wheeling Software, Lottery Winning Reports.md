---
created: 2025-07-24T21:26:40 (UTC +08:00)
tags: [wheel,wheeling,wheels,software,lotto,loto,lottery,loterie,loteria,program,programs,filter,filters,filtering,lotto wheels,lotery wheels,lotto wheels software]
source: https://saliu.com/bbs/messages/wheel.html
author: 
---

# Lotto Wheeling Software, Lottery Winning Reports

> ## Excerpt
> WHEEL-6 lotto software is available as free lottery wheeling program to generate loto, loterie wheels from 3 of 6 to 6 of 6 using reduction filters, reports.

---
![The WHEEL-6 lotto software is available as freeware lottery wheeling.](https://saliu.com/bbs/messages/HLINE.gif)

### I. [Introductory Notes to the Best Lotto Wheeling and Wheels](https://saliu.com/bbs/messages/wheel.html#wheels)  
II. [The Best Software to Tackle Lotto Wheels and Wheeling](https://saliu.com/bbs/messages/wheel.html#software)  
III. [Advanced Tips on Using <PERSON> Saliu's Wheeling and Lotto, Lottery Software](https://saliu.com/bbs/messages/wheel.html#lottery)  
IV. [News and Updates Regarding <PERSON>'s Software](https://saliu.com/bbs/messages/wheel.html#lotto)  
V. [Essential Resources in Lotto, Lottery Wheeling](https://saliu.com/bbs/messages/wheel.html#resources)

![Primer on lotto wheels, lottery wheeling software.](https://saliu.com/images/lotto.gif)

Written on November 01, 2002; later updates.

• **WHEEL 632, SUPER632** version 9.0, October 2002.  
• **Strategy 632** version 8.5, November 2003 — the strategy checking utility.

The lotto software has been greatly upgraded in the packages known as **Bright / Ultimate**. On-the-fly lottery wheeling is available in menu #2, option _W = Lotto Wheels On-the-Fly_. The software is free to run forever, but downloading requires a most reasonable one-time membership fee.

![Generate lotto, lottery, loto, loterie wheels from 3 of 6 to 6 of 6 using powerful lottery filters and reports.](https://saliu.com/ScreenImgs/wheel-lotto.gif)

## <u>1. Introductory notes to the best FREE lotto wheeling &amp; wheels</u>

There has been a strong interest in my lotto software known as **LotWon**, especially the **WHEEL632** and **SUPER632**. Keywords at the search site, emails, requests in newsgroups, all indicated a strong desire for **LotWon632**. The package was available for licensing in 2001.

My research took a new strategic path in 2002. I discovered that _pairing_ is the most powerful lottery strategy. It is so powerful that it doesn't need any other lotto filter; nor does it require skipping potentially non-winning draws.

Writing lotto wheeling software is a paramount job! I avoid preserving old versions of the source code. I had a painful experience a couple of years ago. The old version of a lotto program overwrote the latest! I kept a copy of the old code of **LotWon** lotto software because a faithful user discovered a few bugs. I ironed out the bugs and I kept a special disk handy, just in case! Incidentally, I am grateful to all faithful users who report quirks in my software. But I do have a problem with them when they show too much interest in the workings of my lottery programs. I grow very suspicious when they aim at the source code or the algorithms!

Code maintenance is the main reason I don't release more free lottery software, or upgrade the old freeware. It's not that I like people to beg me! I detest that! Nor am I arrogant because of holding valuable tools to myself — it is normal human behavior. Nor do I suffer from a complex of superiority if I don't present more of my lottery strategies or participate in newsgroups when strategies are debated. I have plenty of things to keep busy. I haven't had even time to play a pick-3 pairing strategy. Playing pairings like '1-1', '2-2', etc. costs $10,000 for 100 draws. The winnings amount to $15,000 without any filter or tweaking, even playing draw by draw (some lottery drawings can be skipped)!

Things change, especially when it comes to change of heart! I have released a ton of new freeware: lotto, lottery, and gambling software. Be sure to check all the resources at this incredible web site.

![The best lotto, lottery wheeling software is free.](https://saliu.com/images/lotto.gif)

## <u>2. The Best Lottery Software to Tackle Lotto Wheels and Wheeling</u>

WHEEL632 uses complete lottery filtering; i.e. the lotto filters can be set to _minimum_ levels AND _maximum_ levels. A filter such as “Three” can be set to extremely tight levels. For example, eliminate all 3-number groups from the past 100 draws, but generate only lotto combinations with 3-number groups from the last 101 draws. Thus, the minimum level of “Three” is set to 100; the maximum level of “Three” is set to 101. This is just an example. You may not get any lotto combination with such a tight setting. But when the condition becomes reality, you may hit the jackpot with very few combinations!

The application generates wheeled lotto combinations, from _3 of 6_ to _5 of 6_; or it can generate un-wheeled combinations: _6 of 6_. The limit is 100,000 lotto combinations per run; filtering needs to be enforced! The filters can be stored in strategy files, in text format. The basic lottery strategy file is named ST6 and consists of zeroes; that is, all the filters are disabled. The older ST6 file is still available for download as ST6.0.

The best way to generate small-number wheels is to wheel, actually, all the numbers in a lotto game. For example, a 6/49 lotto; you want to wheel 18 lotto numbers. Write the discarded numbers at the top of the D6 file, in lines of 6 numbers each. In this example, write the 31 numbers you don't wheel in 6 lines of 6 numbers; line 6 consists of the 31st discarded number written 6 times. **LotWon** requires strictly that the data files consist of 6-number lines, with the fields separated by spaces or commas. This lotto wheeling method yields superior results to creating static lotto wheels, such as with numbers from 1 to 18, then substituting the theoretical numbers by real lotto picks. The static wheels do not take advantage of filtering.

SUPER632 is the report generator. It creates four _W6_ files, one per layer. The lottery filters show not only a historic performance, but also important statistical parameters, such as median, average and standard deviation. The filters show another important measure: direction. The filters increase or decrease, and the movement is regulated by the _**Fundamental Formula of Gambling (FFG)**_. The filters in **SUPER-632** are calculated as _number of draws minus 1_.

In the latest, much more powerful **Bright** winning reporting is on the main menu, option _W = Winning Reports (W, MD)_.

That's how **LotWon** started, trying to make it easier for users to comprehend the concept of lottery filter (or eliminating condition). _**FFG**_ calculates the median for a filter like “Three” to be 39.16 (closer to 39). The _3 of 6_ odds are '1 in 57' in a 6/49 lotto game. _**FFG**_ calculates the median as N for p=1/57 and DC=50%. The median in **SUPER 632** may be 38 or 37, or 40; you add 1 for compatibility with the fundamental formula of gambling (39, 38, 41). Playing the last 39 or 40 draws represents a most efficient '3 of 6 of 49' lotto wheel in a 6/49 game. It beats random play or static lottery wheeling any time!

An even better lotto wheeling method is to use a combination of filters and run **WHEEL 6** several times. A lotto field consists of a number of unique 'M of N' wheeled sub-systems. It is very unlikely that the jackpot sub-system will occur in the first run. As I showed in other posts, the winning combinations are more likely to be found in the FFG median zone. If you run **WHEEL-6** with the same filter setting a dozen times in a row before playing is equivalent to saving money a dozen times in a row.

The innate and external filters in WHEEL632 also avoid crass [_**Koffotzuntz patterns on lotto play slips**_.](https://saliu.com/bbs/messages/161.html)

The two programs require a data file _D6_ of at least 10,000 combinations. Of course, the bulk consists of simulated combinations in a _SIM-6_ file. You can use older versions of **LotWon** to create a 10,000-line combination file. You can also use **MDIEditor Lotto WE** for the job. The _D6_ file is created by the DOS (prompt) command:  
**COPY DATA-6+SIM-6 D6** (it is done automatically in **Bright**)

You may want to download MENU as well. It creates pretty DOS screens. Most importantly, you can run from a central menu all DOS (character mode) freeware you can download from here. Creating the _D6_ file will require you press a function key.

There are incompatibilities between the 16-bit and the 32-bit versions of LotWon (_command prompt_) lotto software. The filters are calculated differently, the strategy files are created differently. Mixing the two versions will lead to errors. But you'd figure out soon the root of the problem. The default strategy file for the 32-bit version of strategy checking is named now ST.632 (available also for downloading).

There are three steps in running the 32-bit lotto 6 software:

1) Run **SUPER 6** to generate the 4 WS6 files; analyze the files to set the filter levels;  
2) Run **Strategy 6** to check the frequency of a strategy (i.e. filter levels that you selected at step #1);  
3) Run **WHEEL 6** to generate combinations based on a strategy; the strategy can be screen input or an ST file.

Creating lotto strategies is the most important action. Setting many strategies and generating lotto wheel combinations several times will lead to better training. The more often one tries selecting strategies, any kind of filter levels — the more skillful one becomes. The odds are further improved if one runs the _same lottery strategy_ several times in a row in the combination generating program. The probability is lower you'll get the winning lotto combination in first run, or the second run, or the first few runs. That way, you can look at it as saving money. It's kind like you (would have) played and lost!

![Run the best lottery wheeling software to create efficient lotto wheels, including for all numbers.](https://saliu.com/ScreenImgs/lotto-b60.gif)

![Run Ion Saliu's lotto, lottery wheeling software.](https://saliu.com/images/lotto.gif)

## <u>3. Advanced Tips on Using Ion Saliu's Wheeling and Lotto, Lottery Software</u>

Many users view the Ion\_5 lotto filter as the key to a goldmine. They see values such as 417. They think immediately of setting Ion5 as follows:  
~ minimum level = 417  
~ maximum level = 418.

They run the program and get no combination at all after days and nights of continuous running! There is no bug in **MDIEditor Lotto**. A repeating value of 417 for Ion\_5 indicates an insufficient size of the lotto data file. The 417 value is not to be relied on as far a maximum level is concerned. Most likely, Ion5 goes a lot higher. You should not use _Max\_Ion5 = 418_ under these circumstances! That's why your computer doesn't generate any lotto combination. It is very rare for a value of Ion5 to reach EXACTLY 417. The maximum values should be used only with very large lottery data files. Any time you see a value higher than 100 (e.g. 417 or 1000) repeating more than a dozen times, it should raise a red flag. The D6 data file is too small (real lotto draws + SIMulated combinations).

Create a very large SIMulated data file — I recommend at least 100,000 (one hundred thousand) lotto combinations (lines). You can use the random modules in **MDIEditor and Lotto** or/and the DOS (command prompt) editions of **LotWon**. The same is valid for **WHEEL\_632**. I created SIM files of over 200,000 lines, then I purged them. It's best to work with clean data files (that is, files without duplicate lotto combinations). I encountered _FivS_ and _FivR_ values of over 100,000 (one hundred thousand). Such extreme levels of filtering have a devastating effect on the lotto odds.

![Updates to Ion Saliu's lotto, lottery wheeling software and lotto wheels.](https://saliu.com/images/lotto.gif)  

## <u>4. News and Updates Regarding Ion Saliu's Software</u>

I stated above:

_Code maintenance is the main reason I don't release more free software, or upgrade the old freeware._

The lotto wheeling software apps presented here, **WHEEL-632** and **SUPER-632**, have been greatly improved. They are now part of an integrated lotto-6 software package: Pick632 and especially **<u>Bright6</u>**. Power like you ain't seen before anywhere. There is also a counterpart for the lotto-5 games: Pick-532 and especially **<u>Bright5 / Ultimate Lotto 5</u>**.

The two integrated double-packages offer absolutely the best method of wheeling the lotto numbers. The key concepts are: randomization and balance. In addition to the unique feature named: TRUE LOTTERY FILTERING. You won't find it in any other lotto or lottery software package — regardless of price.

As I said numerous times, I recommend against playing lotto wheels. They increase the losing rate for the player. I recommend playing non-wheeled lotto combinations, as the type of lotto combinations generated by **Power 632**. You can make **Power632** (and other programs in my DOS **LotWon** lottery software) cooperate with **MDIEditor And Lotto WE**. My lotto software features a special function named _**PURGE**_. You can _purge_ an output file generated by one program by running another program. Purging means eliminating additional combinations by using a new set of true lottery filters.

![Run the best software to generate lotto, lottery wheels.](https://saliu.com/bbs/messages/HLINE.gif)

![Run Ion Saliu's lotto, lottery wheeling software.](https://saliu.com/images/lotto.gif)

## [<u>Resources in Lottery Software, Systems, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)

Lists the main pages on the subject of lottery, lotto, software, wheels and systems.

-   _**Download lottery, lotto wheel**_ [**<u>software</u>**](https://saliu.com/infodown.html) _**from the download site**_:
-   **Wheel-632, Wheel-532**, the best on-the-fly wheeling software; applies real lottery filtering.
-   Superseded by the most powerful integrated packages Pick532, Pick532 and, especially, the **<u>Bright</u>** software packages.
-   **Combinations**, the universal combinations generator for any lotto, Keno, Powerball, Mega Millions, Euromillions.
-   **LexicoWheels**, generator of lotto wheels based on lexicographical indexing; applies to any lotto, Keno, Powerball, Mega Millions, Euromillions or two-in-one lotto games.
-   **Wheel Check 5, Wheel Check 6**, software to verify lotto wheels for missing combinations and generate lotto-5 and lotto6 systems.
-   **<u>Lotto Wheeler</u>**, free wheeling software: fill out lotto wheels with player lotto numbers. Supersedes the old-time favorite **FillWheel** (still offered). The two pieces of software replace the theoretical lotto numbers in the _SYS/WHEEL_ files by your picks (the lotto numbers you want to play).
-   **Shuffle**, **<u>Super Formula</u>** to shuffle (randomize, randomly arrange) your lotto picks. Repeat the randomizing procedure several times. The randomized wheels can be used directly, without shuffling your lottery picks first.

![Lotto wheeling software requires a one-time payment to download and run for free.](https://saliu.com/bbs/messages/HLINE.gif)

-   The Main [_**Lotto, Lottery, Software, Strategy**_](https://saliu.com/LottoWin.htm), _Wheeling Page_.  
    Presenting software to create free winning lotto, lottery strategies, systems based on mathematics. Get your lotto systems or wheels, the best lottery, lotto software, combinations, winning numbers.
-   [**<u>Lotto wheels</u>**](https://saliu.com/lotto_wheels.html) _**for lotto games drawing 5, 6, or 7 numbers**_.  
    The most advanced theory of lotto wheels or reduced lottery systems. Get also original lotto wheels for lotto games drawing 5, 6, or 7 numbers: Balanced, randomized, and free.
-   [_**Myth of lotto wheels or abbreviated lotto systems**_](https://saliu.com/bbs/messages/11.html).  
    Statistical analysis of real lottery data proves the negative effect of playing static (unbalanced or split) lotto wheels.
-   Free lottery software for players of lotto wheels: [_**Fill out lotto wheels with player's picks (numbers to play)**_](https://saliu.com/bbs/messages/857.html).
-   [_**Software to verify lotto wheels**_](https://saliu.com/check-wheels.html) _for missing combinations and generate lotto 5, 6 abbreviated (reduced) systems_.
-   [_**Wheels, balanced lotto wheels, lexicographic order**_](https://saliu.com/bbs/messages/772.html), _index, number set_.
-   [_**Check WHEEL, lotto wheels for winners**_](https://saliu.com/bbs/messages/90.html).
-   [_**Powerball wheels**_](https://saliu.com/powerball_wheels.html).
-   [_**Mega Millions wheels**_](https://saliu.com/megamillions_wheels.html).
-   [_**Euromillions wheels**_](https://saliu.com/euro_millions_wheels.html).

![The best lotto wheeling software: Free lotto wheels generate.](https://saliu.com/bbs/messages/HLINE.gif)

**[Home](https://saliu.com/index.htm) | [Search](https://saliu.com/Search.htm) | [New Writings](https://saliu.com/bbs/index.html) | [Software](https://saliu.com/infodown.html) | [Odds, Generator](https://saliu.com/calculator_generator.html) | [Contents](https://saliu.com/content/index.html) | [Forums](https://forums.saliu.com/) | [Sitemap](https://saliu.com/sitemap/index.html)**

![Powerful lotto wheeling software available as freeware.](https://saliu.com/bbs/messages/HLINE.gif)
