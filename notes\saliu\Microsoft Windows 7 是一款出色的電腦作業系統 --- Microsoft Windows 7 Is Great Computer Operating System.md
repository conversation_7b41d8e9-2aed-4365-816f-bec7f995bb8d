---
created: 2025-07-24T22:49:44 (UTC +08:00)
tags: [Windows 7,Windows XP,Vista,Microsoft,operating system,boot,dual boot,software,DOS,computer,PC,]
source: https://saliu.com/gambling-lottery-lotto/windows-7.htm
author: <PERSON>
---

# Microsoft Windows 7 是一款出色的電腦作業系統 --- Microsoft Windows 7 Is Great Computer Operating System

> ## Excerpt
> Microsoft Windows 7 was a much needed upgrade to Windows Vista, discarding cumbersome features in Vista, resulting in a superior computer PC operating system.

---
<big>•</big> 2008 年，我曾對[微軟 Windows Vista](https://saliu.com/gambling-lottery-lotto/lotwon-software-vista.htm) 發表過貶低性的評論。文章開頭提出了當時的一個原罪： _現在每個人都想變得時髦！_

我的戴爾電腦裝的是 64 位元版的 _**Windows Vista。 Vista**_ 的 _Aero_ 主題介面確實看起來很棒。但我不得不相信，大多數擁有高超藝術鑑賞力的人都會欣賞這種介面模型中的「超級模特兒」。因為 Windows，即使是超級模特兒的化身，也不過是 DOS 上的漂亮介面。你可以把{Windows Vista}想像成_水管工喬_ （DOS）和_迪奧_廣告中美麗的超級模特兒的結合（ _Jadore……je t'adore……_ ）。我不認為這樣的結合能長久。它確實沒能持續下去！ Vista 的美感讓數百萬傻瓜望而卻步！你知道，就是那些商業客戶，正是他們讓微軟成為了史上最富有的公司之一。

微軟發布了這個新版本，命名為 _**Windows 7**_ 。實際上，它應該被命名為 _**Vista 1.1**_ 。 Vista 中令人驚豔的主題被更樸素的圖形所取代。令我沮喪的是，在最初的幾個小時裡，我感到很沮喪！我愛上了 Vista 的外觀（那個超級名模的比喻！）然而，Windows 7 的預設主題更加嚴肅。我們主要用電腦做嚴肅的事情，而不是為了視覺上的享受。而且，在啟動過程中，它也會顯示白鴿和橄欖枝的主題！

我在前面提到的文章中說過，Vista 的圖形設計師們可能曾經在狂歡般的環境中工作（例如酒精、毒品和性！）。但在 Windows 7 重製版 Vista 期間，他們終於清醒了！現在不再是為了藝術上的震撼而製造驚豔的圖像了。微軟的開發人員意識到，Windows 的本質在於嚴肅，在於生產力。把那些幼稚的東西留給 Mac 電腦和作業系統吧——孩子們不應該過早接受生產力的薰陶！

Vista 帶給使用者的第一個極度惱人的功能就是_使用者帳戶控制 (UAC)_ 。這個功能讓你什麼都做不了，除非你惱火地在兩個對話框中點擊「確定」！這個煩人的功能在蘋果電腦的廣告中出現過（至少是那些充滿美國電視台的廣告）。還好，Vista 中這個極度惱人的 UAC 功能可以在_控制面板_中關閉。然而，關閉它並不直觀。你得靠猜測才能開啟_控制台_中的 _「使用者」_ 功能！或者，搜尋幫助工具（Vista 的幫助工具是作業系統中最好的；Windows 7 的幫助工具甚至更勝一籌）。

Windows 7 中的_使用者帳戶控制 (_ UAC) 經過了徹底的重新設計。它現在有 4 個設定。預設設定不再令人抓狂。事實上，從安全角度來看，UAC 的預設設定非常合理……我是說，從安全角度來看！用戶還可以完全關閉 _UAC_ 。

XP 剛推出時，我最討厭的一件事就是預設_隱藏選單項目的底線_ 。這個功能在 XP 配置中被嚴重隱藏了。要用到很多右鍵步驟（我到現在都記不清整個過程了…但我說過這太瘋狂了，不是嗎？）你得找到一個名為 _「效果」_ 的按鈕，然後取消選取 _「隱藏下劃線選單項目_ 」…（或其他更瘋狂的動作…）。否則，XP 選單不會顯示底線，而下劃線代表加速鍵。例如，「檔案」選單會在字母 F 下方顯示一條底線…因此，同時按下字母 F 和 Alt 鍵即可開啟「檔案」選單。這真是作業系統提供的一項非凡的生產力功能！

這項與名為 _「下劃線」_ 的小字元相關的生產力功能遠不止於此。它還能保護使用者的雙手健康。許多作業系統（或軟體應用程式）只需單擊滑鼠右鍵即可立即啟動。所謂的上下文相關選單可以像主選單（在選單列上）一樣顯示加速鍵。但只有在 Windows XP 中預設停用「 _隱藏下劃線選單項目」的_情況下，該功能才會啟用。

禁用那個瘋狂的預設設置，打開了一堆效率快捷鍵的寶庫！我把它們用得淋漓盡致！我右鍵點擊一個資料夾，上下文相關選單就彈出來了。用了這麼多年，我記住了不少快捷鍵。不用右手把滑鼠移到「新建」上，我用左手按了 w 鍵。然後，不用右手把滑鼠移到「資料夾」上，我用左手按了 f 鍵。就這樣，我用雙手以最快的速度創建了一個新資料夾。使用者的右手（大多數使用者都是這樣；左撇子需要特殊設定；我這裡說的對他們仍然適用，只是需要換一下手）在進行大量密集的電腦工作時，最容易被利用。 DOS 同樣利用了雙手，但人們仍然以手臂和手部疾病（腕管綜合症之類的）為由提起訴訟。在 Mac 和 Windows 之後，一切都變得更加危險，因為慣用手需要更加痛苦地完成額外的工作。

Windows Vista completely eliminated the _Menu underlining_ feature. The default was even more insanely anti-productivity designed. The default hid the menus altogether! Very hard to unbury that feature! One must search thoroughly and expect quite a few misses. The user can only uncheck 'Hide Menu Items'. The underscore cannot be enabled. Only pressing the Alt key can show the acceleration keys of the menu items (on the menu bar). Big problem, though! If you right click and open up the context-sensitive menus, you won't be able to see the acceleration keys. If you press the Alt key to show the shortcuts, the context-sensitive menu boxes evaporate instantly! You must have an elephant memory and remember all the acceleration keys from your XP era (or past life)! I did remember the key sequences for creating a new folder and several other tasks.

Windows 7 also fixed that crazy miss in Vista. The fix is not very intuitive, however. The default is still _Hide menu items and underscore of acceleration keys._ You need to right-click on the _Desktop_ and choose _Personalize._ Then, _Ease of Access Center_, then _Make the keyboard easier to use_, then check _Underline keyboard shortcuts and access keys_.

The real Vista pain was triggered by issues with my software — I mean, software that I wrote.

All the 16-bit software I wrote is unusable in Vista, the 64-bit version. No doubt, the 64-bit software will be the future…soon, very soon! We'll be alive and well and kicking in the 64-bit era.

I tried to run my software as in my IonMenu setting. It no longer worked under 64-bit Windows 7. The border-creating program no longer works, as it must be 16-bit software. Ok! No big deal! But, then, the 'wait for a keypress DOS .COM application' is no longer working (WAITS.COM and/or WAITER.COM in my menus). No longer functional is QEdit, the best text-operating application ever, one of the best computer programs ever! The replacement, by Microsoft, EDIT part of QBasic, later part of CMD, no longer works, either! QEdit is already dead, the Microsoft henchmen roared!

Hey, even Microsoft's greatest programming tool ever, Visual Basic 6, does not work properly under 7! The programmer receives a serious warning, before attempting to write a new program in VB6! Yeah, right! Upgrade to the new Microsoft programming tools! It's about elimination, stupid! Don't you realize that Microsoft creates programming tools so that no mere mortals can program — ever?

It was clear I had to take radical actions. I needed to keep Windows XP. Problem: My old XP computer showed signs of age. It had serious difficulties running my Powerball software (it was too slow). The monitor also was aged (but not in the same category as wine). I did regret discarding my old PC, but I had no choice. Thus, I had to reconfigure my new computer in such a manner that my old PC would still be around.

My solution was the bid-headache-producing _dual boot._ The material quoted at the top of this article showed my painstaking process of enabling a dual-boot PC.

I heard many corporations faced even bigger problems because of incompatibilities running 16-bit software of 64-bit Vista. Microsoft was responsive this time. They offer a free feature in the _Professional_ and _Business_ editions of Windows 7. It is called _Windows XP Mode_. The process is not the simplest, again. The user must go to a Microsoft download site and install _Virtual PC_ and _Windows XP Mode_. There is about 300 MB of downloading. The user must turn OFF the PC after the installation. The user must read first PDF files at Microsoft downloading sites. This feature is not present in the Home editions of Windows 7. If I had the same option in Vista, I would have saved the cost I paid for a new copy Of Windows XP. I am just one individual; the savings for corporations can add up to huge sums by not having to buy new copies of XP!

The installation of Windows 7 goes pretty smoothly. I upgraded the 64-bit Vista, and most settings were preserved. My _Home Premium_ didn't have the _XP Mode_. Yet, I saved a few gigabytes of space by upgrading to _Windows 7 Professional!_

One caveat regarding the installation. It is not primarily a fault of Microsoft's. The installation requires several points of restarting the system. The ROM-BIOS chip in every PC has this message at boot-up, IF a CD/DVD is in: _Press any key to boot from the CD_… Nobody should ever touch any key! Let the process continue automatically! Microsoft could help by writing a warning in the printed materials that come with the software. My case could have been even more dangerous since my PC is still dual-booting. At one point, I still had to select an operating system — the process was no longer automated! But I looked for more than 10 seconds before touching the keyboard!

I didn't need any hardware upgrades for this version of…Vista 1.1! I only needed to upgrade two device drivers (modem diagnostics from Dell). I don't use dial-up Internet, anyway!

Some pundits say that Windows 7 loads and starts faster than Vista. I haven't noticed a speed increase; neither have I noticed any slowdown. I noticed for sure far fewer error messages. I did get some weird errors in Internet Explorer under Vista.

The weirdest experience I had with this upgrade from Vista to Windows 7 started … at Walmart! The store in my hometown (Gettysburg) had not received Windows 7 on October 22, noon time. I went to a Walmart store in a bigger town (Hanover). They said they had received Windows 7. They directed me to an aisle, but I couldn't see any software on the shelves! I was shocked! The salesman took a couple of keys and opened two big padlocks of a cage! It was like a national gold depository or a bank vault! Point is, I don't remember the last time I bought an operating system in brick-and-mortar store! The last time I bought (Windows XP) was on eBay (2008). It resembled an action of national security importance!

Hasta la vista, baby!

![Closely related Web pages on Microsoft Windows upgrades.](https://saliu.com/gambling-lottery-lotto/HLINE.gif)

**<u>Closely related</u>**

-   [Run Software at Command Prompt in Windows XP, Vista, Windows 7](https://saliu.com/gambling-lottery-lotto/command-prompt.htm) (the _Command Prompt_ is exactly the same, only harder to create a desktop shortcut).
-   [Microsoft Windows 8: Schizophrenic Computer Operating System](https://saliu.com/gambling-lottery-lotto/windows-8.htm)
-   [Lottery, Gambling Software and Windows Vista Dual Boot Windows XP](https://saliu.com/gambling-lottery-lotto/lotwon-software-vista.htm)
-   [_**Microsoft Windows 10 Upgrade Ridden by Serious Problems, Errors, Issues**_](https://forums.saliu.com/windows-10.htm).
-   [_**WINHLP32 in Windows 10 Help Files for Older Software Programs, Applications**_](https://saliu.com/gambling-lottery-lotto/windows-10-help.htm).

![Microsoft Windows upgrades are heavily determined by financial reasons viewed as survival totems.](https://saliu.com/gambling-lottery-lotto/HLINE.gif)

![Read about Windows 7 operating system: It is more productive and easier to configure.](https://saliu.com/gambling-lottery-lotto/HLINE.gif)

**[Home](https://saliu.com/index.htm) | [Search](https://saliu.com/Search.htm) | [Help](https://saliu.com/Help.htm) | [New Writings](https://saliu.com/bbs/index.html) | [Software](https://saliu.com/infodown.html) | [Odds, Generator](https://saliu.com/calculator_generator.html) | [Contents](https://saliu.com/content/index.html) | [Forums](https://forums.saliu.com/) | [Sitemap](https://saliu.com/sitemap/index.html)**

![Microsoft Windows 7 was a much needed upgrade to Windows Vista. Windows-7 discarded the cumbersome features in Vista, thus resulting in a far superior computer PC operating system.](https://saliu.com/gambling-lottery-lotto/HLINE.gif)
