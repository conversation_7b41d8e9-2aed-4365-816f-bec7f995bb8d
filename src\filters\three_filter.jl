# THREE Filter Implementation
# THREE 過濾器實現 - 分析三號組合的出現模式

using Dates
using Statistics

# 引入必要的模組
include("../types.jl")
include("../filter_engine.jl")

"""
生成號碼組合的所有三號組合
從給定的號碼列表中生成所有可能的三號組合
"""
function generate_triplets(numbers::Vector{Int})::Vector{Tuple{Int, Int, Int}}
    if length(numbers) < 3
        return Tuple{Int, Int, Int}[]
    end
    
    triplets = Tuple{Int, Int, Int}[]
    n = length(numbers)
    
    for i in 1:n-2
        for j in i+1:n-1
            for k in j+1:n
                # 確保號碼按升序排列
                triplet = (numbers[i], numbers[j], numbers[k])
                push!(triplets, triplet)
            end
        end
    end
    
    return triplets
end

"""
計算特定三號組合在歷史數據中的出現頻率
"""
function calculate_triplet_frequency(engine::FilterEngine, triplet::Tu<PERSON>{Int, Int, Int})::Int
    if isempty(engine.historical_data)
        return 0
    end
    
    count = 0
    for draw in engine.historical_data
        # 檢查三個號碼是否都在該次開獎中
        if triplet[1] in draw.numbers && triplet[2] in draw.numbers && triplet[3] in draw.numbers
            count += 1
        end
    end
    
    return count
end

"""
計算三號組合的 Skip 值
三號組合 Skip 是指該組合上次出現到現在的間隔
"""
function calculate_triplet_skip(engine::FilterEngine, triplet::Tuple{Int, Int, Int})::Int
    if isempty(engine.historical_data)
        return 0
    end
    
    # 尋找三號組合最後出現的位置
    last_occurrence = 0
    
    for (i, draw) in enumerate(engine.historical_data)
        if triplet[1] in draw.numbers && triplet[2] in draw.numbers && triplet[3] in draw.numbers
            last_occurrence = i
            break  # 找到最近的出現位置就停止
        end
    end
    
    if last_occurrence == 0
        # 組合從未出現過，返回總數據長度
        return length(engine.historical_data)
    else
        # 返回從最後出現到現在的間隔
        return last_occurrence - 1
    end
end

"""
計算歷史三號組合分佈
返回歷史上每次開獎中出現的三號組合數量
"""
function calculate_historical_triplet_distribution(engine::FilterEngine)::Vector{Int}
    if isempty(engine.historical_data)
        return Int[]
    end
    
    triplet_counts = Int[]
    
    for draw in engine.historical_data
        # 計算該次開獎中的三號組合數量
        triplets = generate_triplets(draw.numbers)
        push!(triplet_counts, length(triplets))
    end
    
    return triplet_counts
end

"""
計算期望的三號組合數量
基於給定號碼數量計算期望的三號組合數量
"""
function calculate_expected_triplet_count(engine::FilterEngine, number_count::Int)::Float64
    if number_count < 3
        return 0.0
    end
    
    # 計算可能的三號組合數量 C(n,3)
    possible_triplets = binomial(number_count, 3)
    
    # 對於 Lotto 5/39，任意三個號碼同時出現的理論機率
    # P(三個特定號碼都被選中) = C(36,2) / C(39,5)
    total_combinations = binomial(39, 5)
    favorable_combinations = binomial(36, 2)
    theoretical_probability = favorable_combinations / total_combinations
    
    # 期望的三號組合數量基於理論機率
    return possible_triplets * theoretical_probability
end

"""
計算三號組合信心水準
基於歷史分佈和統計特性
"""
function calculate_triplet_confidence(engine::FilterEngine, current_triplet_count::Int)::Float64
    if isempty(engine.historical_data)
        return 0.0
    end
    
    # 獲取歷史三號組合分佈
    historical_counts = calculate_historical_triplet_distribution(engine)
    
    if isempty(historical_counts)
        return 0.0
    end
    
    # 計算歷史統計
    mean_count = mean(historical_counts)
    std_count = length(historical_counts) > 1 ? std(historical_counts) : 1.0
    
    # 計算當前值的 Z 分數
    z_score = abs(current_triplet_count - mean_count) / max(std_count, 0.1)
    
    # 基於 Z 分數計算信心水準
    # Z 分數越小（越接近平均值），信心越高
    base_confidence = exp(-z_score / 2.0)
    
    # 基於樣本大小調整信心
    sample_confidence = min(1.0, length(historical_counts) / 50.0)
    
    return clamp(base_confidence * sample_confidence, 0.0, 1.0)
end

"""
分析特定三號組合的詳細資訊
"""
function analyze_specific_triplet(engine::FilterEngine, triplet::Tuple{Int, Int, Int})::Dict{String, Any}
    frequency = calculate_triplet_frequency(engine, triplet)
    skip_value = calculate_triplet_skip(engine, triplet)
    
    # 計算理論期望頻率
    total_combinations = binomial(39, 5)
    favorable_combinations = binomial(36, 2)
    theoretical_probability = favorable_combinations / total_combinations
    expected_frequency = theoretical_probability * length(engine.historical_data)
    
    return Dict(
        "triplet" => triplet,
        "frequency" => frequency,
        "skip" => skip_value,
        "expected_frequency" => expected_frequency,
        "is_above_average" => frequency > expected_frequency,
        "rarity_score" => frequency == 0 ? 1.0 : 1.0 / frequency
    )
end

"""
計算 THREE 過濾器結果
分析給定號碼組合的三號組合模式
"""
function calculate_three_filter(engine::FilterEngine, numbers::Vector{Int})::FilterResult
    start_time = time()
    
    if length(numbers) < 3
        throw(ArgumentError("THREE 過濾器至少需要 3 個號碼，得到: $(length(numbers))"))
    end
    
    if isempty(engine.historical_data)
        throw(ArgumentError("歷史數據不能為空"))
    end
    
    # 驗證號碼範圍
    if !all(1 <= n <= 39 for n in numbers)
        throw(ArgumentError("所有號碼必須在 1-39 範圍內"))
    end
    
    # 檢查快取
    cache_key = "three_filter_$(sort(numbers))"
    if engine.cache_enabled && haskey(engine.filter_cache, cache_key)
        cached_result = engine.filter_cache[cache_key]
        @info "使用快取結果: THREE 過濾器，號碼 $(numbers)"
        return cached_result
    end
    
    try
        # 生成所有三號組合
        triplets = generate_triplets(numbers)
        
        # 計算當前三號組合數量
        current_triplet_count = length(triplets)
        
        # 計算期望的三號組合數量
        expected_triplet_count = calculate_expected_triplet_count(engine, length(numbers))
        
        # 判斷是否有利（通常較少的三號組合更有利）
        is_favorable = current_triplet_count <= expected_triplet_count
        
        # 計算信心水準
        confidence = calculate_triplet_confidence(engine, current_triplet_count)
        
        # 獲取歷史三號組合數量
        historical_counts = calculate_historical_triplet_distribution(engine)
        
        # 計算執行時間
        calculation_time = time() - start_time
        
        # 創建結果
        result = FilterResult(
            "THREE_FILTER_$(length(numbers))_numbers",
            THREE_FILTER,
            current_triplet_count,
            expected_triplet_count,
            is_favorable,
            confidence,
            historical_counts,
            calculation_time
        )
        
        # 儲存到快取
        if engine.cache_enabled
            engine.filter_cache[cache_key] = result
            manage_cache_size!(engine)
        end
        
        return result
        
    catch e
        @error "計算 THREE 過濾器時發生錯誤" numbers=numbers error=e
        rethrow(e)
    end
end

"""
獲取號碼組合中最稀有的三號組合
"""
function get_rarest_triplets(engine::FilterEngine, numbers::Vector{Int}, top_n::Int = 5)::Vector{Dict{String, Any}}
    triplets = generate_triplets(numbers)
    triplet_analyses = [analyze_specific_triplet(engine, triplet) for triplet in triplets]
    
    # 按稀有度分數排序（頻率越低，稀有度越高）
    sort!(triplet_analyses, by = x -> x["rarity_score"], rev = true)
    
    return triplet_analyses[1:min(top_n, length(triplet_analyses))]
end

"""
計算 THREE 過濾器的統計摘要
"""
function get_three_filter_summary(results::Vector{FilterResult})::Dict{String, Any}
    if isempty(results)
        return Dict("error" => "無結果數據")
    end
    
    # 篩選 THREE 過濾器結果
    three_filter_results = filter(r -> r.filter_type == THREE_FILTER, results)
    
    if isempty(three_filter_results)
        return Dict("error" => "無 THREE 過濾器結果")
    end
    
    favorable_count = count(r -> r.is_favorable, three_filter_results)
    confidence_levels = [r.confidence_level for r in three_filter_results]
    triplet_counts = [r.current_value for r in three_filter_results]
    expected_values = [r.expected_value for r in three_filter_results]
    
    return Dict(
        "total_combinations" => length(three_filter_results),
        "favorable_combinations" => favorable_count,
        "average_confidence" => mean(confidence_levels),
        "average_triplet_count" => mean(triplet_counts),
        "average_expected_triplets" => mean(expected_values),
        "triplet_count_distribution" => Dict(
            "min" => minimum(triplet_counts),
            "max" => maximum(triplet_counts),
            "median" => median(triplet_counts)
        )
    )
end

"""
檢查三號組合的歷史表現
"""
function check_triplet_historical_performance(engine::FilterEngine, triplet::Tuple{Int, Int, Int})::Dict{String, Any}
    frequency = calculate_triplet_frequency(engine, triplet)
    skip_value = calculate_triplet_skip(engine, triplet)
    
    # 計算該組合的出現間隔
    occurrence_intervals = Int[]
    last_occurrence = 0
    
    for (i, draw) in enumerate(reverse(engine.historical_data))
        if triplet[1] in draw.numbers && triplet[2] in draw.numbers && triplet[3] in draw.numbers
            if last_occurrence > 0
                interval = i - last_occurrence
                push!(occurrence_intervals, interval)
            end
            last_occurrence = i
        end
    end
    
    return Dict(
        "triplet" => triplet,
        "total_frequency" => frequency,
        "current_skip" => skip_value,
        "occurrence_intervals" => occurrence_intervals,
        "average_interval" => isempty(occurrence_intervals) ? 0.0 : mean(occurrence_intervals),
        "is_overdue" => skip_value > (isempty(occurrence_intervals) ? 0 : mean(occurrence_intervals))
    )
end

# 導出函數
export calculate_three_filter, generate_triplets, calculate_triplet_frequency
export calculate_triplet_skip, analyze_specific_triplet, get_rarest_triplets
export get_three_filter_summary, check_triplet_historical_performance
