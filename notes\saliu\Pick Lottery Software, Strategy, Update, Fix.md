---
created: 2025-07-24T21:26:41 (UTC +08:00)
tags: [lottery,software,strategy,update,fix,strategy,strategies]
source: https://saliu.com/pick-software.html
author: 
---

# Pick Lottery Software, Strategy, Update, Fix

> ## Excerpt
> Bug fixes to the strategy checking programs for the pick-3 and pick-4 lotteries. Presentation of great methods to create pick lottery strategies.

---
Written on June 21, 22 & July 5, 2005 (5 WE).

Forever glorious be your names, o understanding guests, for they prove that errare humanum est!

Mea culpa, o magistrates of symmetrical judgment!

<big>•</big> I just discovered a few bugs in the strategy checking utilities for the pick-3, pick-4, and horse racing. Program names: <big>STRAT332, STRAT432, StrategyH</big>. The updated version is 12.2 June 2005.  
I strongly recommend that you download the new versions and replace all previous versions in all the packages on your PC. Notably, please replace the old strategy checking software in Pick332. As of LotWonH, check first to see if there is a strategy checking program StrategyH. Save it to a different directory first. The new program may work with different formats of the strategy files.

The real bug was in the MAXimum value of all the Any filters. The MAX\_Any was confused with MAX\_Bun! Other problems were in the presentation, although not errors in checking. The heading printed some filter values twice; other filter values were not printed at all. For example, Syn\_2 was printed correctly in its proper place of the heading; then it was printed again in the place labeled Syn\_3!

Honestly, I don't know when those bugs infiltrated my software! I know for sure everything was error-free at one point in time. I remember because I checked it thoroughly in the new millennium.

<big>••</big> Hoping that you will accept my apologies without bitterness, I would like to show you a pick-3 strategy. It is best to use a filter that shows a very good frequency. Add to the pivot condition (filter) a couple more filters with very safe settings, so that the frequency remains relatively high. I look at my Pennsylvania lottery pick-3 WS and MD reports. I go with the first one I see in the reports. It is the well-known Tot in layer 1. That filter is actually the straight combination (I named it Tot as in Total digits). The Fundamental Formula of Gambling (FFG) calculates the median for Tot to be around 693 (close to 700 it is). I pick Tot as the pivot filter with settings like: minimum = 1000 and MAXimum = 2000. The frequency is very high, so I can afford to skip or even lose some winning situations.

It is more efficient to use additional safe filters instead of just one with very tight values. First, the frequency of the strategy goes down. Next, the filter is not nearly as efficient beyond certain thresholds. That is so because of the pattern overlapping. But adding different filters eliminates combinations far more efficiently, even at very safe values. So, I added the following two filters: All\_1 minimum = 1 (very frequent, therefore very safe) and Vr\_1 MAXimum = 1 (very frequent, therefore very safe). The frequency is excellent; the elimination power is also very good. I am referring here to my in-house software. Some filters may differ. Tot however is the same; Vr is different; All possibly different from the freeware you are using. The principle nevertheless is the same. Furthermore, you can dramatically reduce the amount of combinations by setting other filters at run time. Look for those streaks of +/- in the filter columns. Look also for very large values that most likely will not increase; vice versa, look for very low values that most likely will not fall further. You don't need that many extra filters at runtime. Just a few filters will reduce the number of combinations to play to just a few.

<big>•••</big> The philosophy of playing strategies  
Philosophy is born from errare humanum est. You can read an early analysis on the [_**Lotto, Lottery Software, Excel Spreadsheets Programming, Strategies**_](https://saliu.com/Newsgroups.htm) page, chapter two.

_I received an insightful message from a Brazilian user. He found my strategy to be FLAWED. After further review, he was right! I rechecked the entire process. I miscalculated. Indeed, I should have counted also the times when the skip went 6 or higher. Actually, I looked at front skips which were already higher than 6. The right strategy is still based on the MEDIAN. Briefly now, if there are more than 5 skips below the median do not play. Also, after 1 or 2 skips above the median, there will follow skips lower than the median -- play the strategy!_

On this page of strategy checking, I show a pick 3 strategy that hits 100 times in 1000 drawings. Total combinations for HIT situations:  
61 46 44 44 45 44 45 53 52 45 ... Avg = 48 combos.  
Just playing the raw number of combinations in all 1000 drawings, the cost would amount to 48,000. The winnings would total 100 x 500 = 50,000. Too costly. The mission of a strategy is to minimize the cost, while maintaining a large winning potential. In the _Brazilian_ perspective, my plan was to play only the skips below the median. Something like this:

Under median+1 (skip under 7): cost = 250 x 48 = 12,000;  
Under median+1 winnings: 60 x 500 = 30,000; net profit = 18,000.

How about the other 40 situations when the strategy skip goes above the median? In truth, in 40% of the situations when we generate a strategy (by running STRAT332), the skip would be already over the median. It will be 8 or larger in 16 of the 40 situations. In 24 of the cases with skips over 8, we would have played — in vain — 24 x 8 x 48 = 9216. That's 9,216 in additional cost. Therefore, the profit listed above is diminished to 18,000 – 9216 = 8,784.

The strategy presented here has the following structure:  
\* HITS: 100 in 1000 draws  
\* Sorted Skips: 0 0 0 0 0 1 1 1 1 1 1 1 1 1 1 1 1  
2 2 2 2 2 2 2 2 2 2 2 3 3 3 3 3 3 3 4 4 4 4 4 4 4 4  
5 5 5 5 5 6 6 6 6 6 6 6 7 7 7 7 7 = 60 times; 249 draws  
8 8 8 9 10 10 10 10 10 11 11 12 12 12 12 13 13 13 14 14 15 15 16 17 17 18 18 19 19 20 20 21 21 22 24 24 33 34 39 72  
\* Median Skip: 6

The new philosophy is to play as many hit situations as possible. The only way to achieve that is to _reduce_ the number of the combinations to play. Adding quite safe filters at runtime is not that difficult. The amount of combos can be reduced down to 10. Let's say we reduce the amount to 20 combinations. In this case, we can play all 1000 drawings for a cost of some 20,000. Of course, the profit is much higher now: 30,000.

But does it make sense to play the skips of 30 or longer? If we play the 33-drawing skip, the cost goes up to some 600 for a win of 500. Even when we win those 4 long streaks — we still lose dinero, argent, soldi! Of course, we lose money, too! Should we play only the streaks shorter than 25 drawings? Those 4 skips longer than 25 still require a cost of 4 x 25 = 100 draws; 100 x 20 = 2000. But we save 8 + 9 + 14 + 47 = 78; 78 x 20 = 1560. The new cost is 20,000 – 1560 = 18,440. The total winnings are lower, too: 48,000. Overall, the profit is also lower. The only advantage is a lower cost. In this case, it is much more rare to have the starting skip of a strategy higher than 25. There are only 4 situations in 100. The lower the probability, the streakier the event. Conversely, the higher the probability, the more homogenous the distribution.

Read also the [_**Lottery strategy-in-reverse**_](https://saliu.com/reverse-strategy.html) page. It is easier to _miss_, especially when wacky filter settings are selected. The intentionally-losing combinations can be used to further purge a straight-up strategy, such as the one presented on this page.

<big>Better Approaches: My Theory &amp; Software — Or Other Methods?</big>  
In all honesty, I don't see anything out there that offers better ways to winning at gambling or lottery. I wish there was something easier and more efficient than my approach. Ain't nothing, honestly. IF my methodology doesn't win the lottery and gambling — there is absolutely nothing to accomplish such daunting tasks. Never will be, if my research has been futile. Never mind that they will continue to put huge efforts in playing the stock market or predicting the weather. The latter two phenomena are equally random to gambling or lottery. They have some advantages, but lottery/gambling have their advantages as well. In the end, it's all about streaks and skips, no matter what the phenomenon is. The skips (misses) are shorter and the streaks are longer if the probability is higher; and vice versa.

So, right now, I cannot see any better approach than mine. That's the only way, like my theory and software do: Track the winning and the losing streaks and their corresponding skips (misses). There are some no playing moments; other situations demand a higher bet. It is hard work, but nothing else works better.

In fact, the science of gambling is the science of the streaks. Theory of probability in general is the science of the streaks and skips. A statement such as "This event will always have the probability equal to zero point zero zero three four etc." is virtually meaningless. The probability represents the ratio of the favorable cases over total possible cases. So, it works with integers. In real-life we deal with integers (discrete values) such as numbers of elements and numbers of trials. The events will hit or miss in streaks pretty clearly predicted by rules and formulas of probability theory. If you play longer sessions at the blackjack table, for example, you will face a higher probability of some very long losing streaks. But play shorter sessions, and there is a far better chance that you'll escape with shorter losing streaks. The LotWon reports will show you filters with high probabilities to reverse trend. If the degree of certainty is 90% or higher, its hitting (winning) streak will be longer. Equivalently, the inverse outcome — no reverse in trend — is some 10% or lower. The lower the probability, the streakier the event. It's harder to see a low-probability event, even if some rule tells you that the event will occur once every ten cases.

For we must remember that there is NO such a thing as ABSOLUTE CERTAINTY in the entire Universe. That's way humans invented the gods; humans badly need the comfort of absolute certainty.

Read also a recent follow-up. It shows a comprehensive strategy analysis for the pick-3 lottery.  
<big>•</big> [_**Lottery, Lotto Utility Software**_](https://saliu.com/lottery-utility.html).  

Here is a fragment of my report. It shows also the new MD reports that I use in-house. They are a modification of the filters in **MDIEditor and Lotto WE**. There is 6 layers of the **MDIEditor Lotto WE**. Right now that software is for my use only. That's it and that's that.

![Run the best winning lotto 3 digits software, also pick-4 lottery.](https://saliu.com/ScreenImgs/lotto-lottery-software.gif)
