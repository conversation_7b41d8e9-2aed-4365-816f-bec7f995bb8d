---
created: 2025-07-24T21:26:40 (UTC +08:00)
tags: [lotto,software,software,strategy,lotto wheel,combination,number,lottery filters,draws,drawings,jackpot,]
source: https://saliu.com/12-number-lotto-combinations.html
author: 
---

# Lottery Software for 12 Numbers Lotto Combinations

> ## Excerpt
> Generate 12-number combinations for 6-number lotto games, then convert the combinations to 6 numbers using special 12-number lotto wheels created by <PERSON>.

---
Published in September 2010.

<big>• Bright12</big>: New lotto software generating 12-number combinations based on data files (real drawings) in 6-number lotto games.

-   This lotto software package is no longer updated. Instead, all programs specific to this package were moved to the most comprehensive 6-number lotto software suite: **Bright6**. You should always run **Bright 6** as it is always up-to-the-date, with all known errors ironed out.
-   The powerful loto-6 software package runs under 32-bit/64-bit Windows OS and [_**32 / 64-bit Windows Vista / Windows 7, 8, 10**_](https://saliu.com/gambling-lottery-lotto/command-prompt.htm) via a great-looking and highly functional interface.
-   The new lotto software requires paid [_**Permanent Membership**_](https://saliu.com/membership.html) in order to download it; the usage is totally free thereafter.

The options specific to this lotto 6-12 software package and different from **Bright-6** are _**F (filters)**_ and _**C (12-number lotto combination generating)**_. The filter report is generated by a lotto program named <big>Lotto Group Skips 6</big>. The 12-number lotto combinations are generated by the <big>Combine6-12</big> application (over 3 MB in size). I left in the bundle all the programs incorporated in Bright6. I thought it convenient to do some lotto calculations or combination generating in the same interface, instead of hunting in different software packages.

First, let's take an artistically educated peek at the main menus. After all, lottery is the artistic science of randomness and the scientific art of order (lexicographical).

![Lotto Strategy, Software: 12-Numbers Combinations in 6-Number Lotto Games.](https://saliu.com/ScreenImgs/lotto-6-12.gif)

![Presenting Ion's lotto strategy that hit the jackpot in Pennsylvania lottery, 1986.](https://saliu.com/HLINE.gif)

There was a mix of curiosity and skepticism (perhaps cynicism as well) expressed in my forums this year of grace 2010. It was related to an old lotto strategy that hit the jackpot but it wasn't played (1986)! I started the talk back in January 17, 2001. I wrote this message in my oldest forum: [_**History of my Lottery, Lotto Experience: Software, Systems**_](https://saliu.com/bbs/messages/532.html).

That post triggered questions in my previous forums and also from readers of my book. Why don't I present clearly and in detail that lotto strategy that hit a jackpot in 1986 (although it was not played)? I would start two threads in the forum, for open discussions on that lotto strategy. Then, I put together an article at **SALIU.COM** with human ad technical details (computer programming, software, probability theory).

-   _The big lotto strategy that did hit the jackpot_.
-   The concept of playing 12 lotto numbers only has always been so tempting to me. The fact is I never had much success with lottery number selection. So no wonder that as soon as Ion was so kind and shared his old strategy with us I embarked upon testing.
-   _Number Combinations Wheeled, Played in Lotto-6 Games_.
-   The 6-40 lotto game back then required to play at least two tickets for $1. We played a total of 36 combinations, for $6 each. I had won twice before with one of the Puerto Ricans (_4 of 6_). The three of us also won once another _4 of 6_ third lottery prize. That prize usually paid over $100 per winning ticket. The lotto program I wrote generated 12-number combinations randomly.
-   [_**Design Lotto Wheels: Manually, or in Lotto Wheeling Software**_](https://saliu.com/lottowheel.html)
-   We played 12-number lotto wheels. I used two wheels for the same 12-number group. The first lotto wheel used the group in lexicographic order, from 1 to 12, in 6 lottery tickets (combinations). Then, I shuffled the 12-number set and applied the wheel to it. I always used the same shuffled set to save time. In total, we played 3 groups of 12 lotto numbers each.
-   [_**Lotto Strategy: 12-Number Combinations Wheeled, Played in Lotto-6 Games**_](https://saliu.com/lotto-jackpot-lost.html)
-   First, I had intended to offer specific software, exactly like my original software of 1986: Generate 12-number combinations, in random manner. I got the programs started. The task is huge, however. I can implement now full filters to eliminate groups of 1, 2, 3, 4, 5, and 6 numbers from past drawings.

The request to write specific software for this type of lotto strategy was imminent. The delay was also imminent primarily because the task is daunting. Generating 12-number combinations implies a huge amount of logical conditions. A 12-number combination consists of 12 _single_\-number groups, 66 _pairs_ (2-number groups), 220 _triples_, 495 _quads_, _792_ quintets, and _924_ sextets. All those subgroups are logical constructs that must be checked against every past lottery drawing (or simulated lotto combination).

To my surprise, I discovered yet another lottery programming technique. I only needed one logical bloc from my lotto-6 software. That logical bloc almost cloned itself a number of times equal to all those subgroups for 12-number generation! It was quite easy and surprisingly fast. I discovered also new tools to check for precision. The probability is very high that my lotto code for 12 numbers is as accurate as it can be.

![The old lotto strategy is also…new: It applies to bookie lotteries as well.](https://saliu.com/HLINE.gif)

Writing started, however, with 6-number lotto, specifically bookie lotteries. The discussion began in my previous forum and was triggered by a judgement error in my lotto pairing software. From there, we went on to bookie lotteries where the player can play groups of 2, or 3, or 4 numbers (instead of 6-number tickets as in traditional lotto).

It became apparent that new type of lotto software was needed.

-   A member of my community, _Adam Krookootz_, wanted lotto software that would take a 6-number combination and break it into 15 pairs and 20 triples which finally could be turned into regular 6/49 lotto _combosnations_ (a favorite of my lottery-speak!) I greatly expanded the concept and created <big>BreakDownNumbers</big>. The lotto software program takes one or more lines consisting of numbers and breaks down each line into smaller number-groups: From 1-number groups to groups of 7 numbers per _combonation_ (line).
-   Another member of my community, _Attila David_, had written in-house software that reported the skip of every pair of a lotto drawing. To my surprise, some lotto pairs skip over 100 drawings, even 200 draws…how about more than 300 drawings sometimes! My lotto software (**Report6** in the **Bright 6** package) does similar reports, but it only shows the lowest skip of a subgroup of lotto numbers. That's the minimum level of the respective lottery filter.

I expanded the concept and I wrote <big>LottoGroupSkips6</big>. The lotto program generates a special report for lotto-6 regarding the SKIPS of the subgroups of numbers: _**Singles, Pairs, Triples, Quads, Quintets, Sextets**_. The report shows, draw by draw, when each subgroup last hit; that is, how many drawings each subgroup skipped in the most recent past. These 6 parameters will serve as filters in the lotto program that generates 12-number combinations while working with a 6-number lotto results file.

lotto-6of12 Here is the beginning of a report:

![Lotto strategy for 6 numbers in 12-number combinations of singles to sextets.](https://saliu.com/ScreenImgs/lotto-6of12.gif)

Look at a draw like #3. The _Quints_ can be over 200000 in 4 out of 6 (over half). The _sextet_ is over 9 million; _Quad_ is over 1000 (minimum) and _Trip_ is over 100 (minimum). Could be a very good strategy not only for bookie lotteries!

<big>•</big> The relationship between the above filters and the combination generators in **Bright6** (Lexicographic, Optimized random, Wheeling) and **MDIEditor Lotto WE**:

-   _**Ones (Singles)**_ is identical to _**ANY1\_1**_ in **Bright 6** AND in **MDIEditor Lotto**;
-   _**Pairs**_ is identical to _**TWOS**_ in **Bright 6**; inexistent in **MDIEditor Lotto WE**;
-   _**Triples**_ is identical to _**THREES**_ in **Bright 6**; inexistent in **MDIEditor Lotto**;
-   _**Quads**_ is identical to _**FOURS**_ in **Bright6**; inexistent in **MDIEditor Lotto WE**;
-   _**Quintets**_ is inexistent in **Bright-6** or **MDIEditor Lotto**; **Bright 6** applies a _Del5_ filter that is based on deltas for groups of _5 of 6_ numbers;
-   _**Sextet**_ is inexistent in **Bright-6**; **Bright 6** applies a _Del6_ filter that is based on deltas for groups of _6 of 6_ numbers; identical to _**Past Draws**_ in **MDIEditor And Lotto WE**.

![Analyzing groups: singles, pairs, triples, quadruples, quintuples, sextuples in lotto games.](https://saliu.com/HLINE.gif)

The filters reported in the first program will feed the 12-number lotto combination generator: <big>Combine6-12</big>. The lotto software generates random combinations for lotto-6, but 12 numbers per combination; the data file must still be in the 6-numbers-per-line format as in the **Bright6** programs. Only the minimum levels of the following filters are applicable in this type of software: _Ones, Pairs, Triples, Quads, Quintets, Sextet_.

Looking at the filters above, you might be inclined to apply the highest values to the 12-number lotto generator. Please keep in mind that 12-number games are very different from 6-number lotto games. Obviously, the odds are far higher as the number of total possible combinations is far larger. Furthermore, there is a limit imposed by the performance of the computers. Generating 12-number combinations with high filter levels can quickly come to a halt. The computers are not fast enough to find qualifying 12-number lotto combinations.

In addition to the performance limitations of the computers, one should be mindful also that a minimum number of subgroups are needed in order to build at least one combination. In _6 from 49_ lotto, for example, we can eliminate up to 43 single-number groups (the Ones filter). In that extreme case, 49 – 43 = 6 numbers left. In _12 from 49_ lotto, we can't eliminate more than 37 lotto numbers. What I want to say is that the filter levels for 12-number lotto must be significantly lower than for lotto-6 games. But there is still equivalency from 6 to 12 numbers. Say, we enable the Sextet filter in **Combine6-12**. The program will generate combinations that do not repeat any of the past 6-number draws set in Sextet. You can break the 12-number combo into 924 groups of 6 lotto numbers. None of the 924 will repeat any drawing from the range set by the Sextet filter.

Problem is, **Combine 6-12** will have a hard time finding all those gazillions of 12-number lotto combinations that satisfy the restrictions imposed by very high filters. There are 92263734836 (over 92 billion) combinations in a _49 numbers taken 12 at a time_ set. I did several tests on my PC. I could not get to generate any 12-number combination for a 49-ball lotto number. I came across maximum values for the filters that my computer can't climb.

First of all, I used a real data file with drawings from the 6-49 lotto game played in Romania. I visited last summer and created a data file from the beginning of that lotto game. A file of simulated lotto drawings is mandatory. I used the best SIMulated data file for _6 / 49_ lotto there is. I generated all 13983816 possible combinations in lexicographical order. Then, I shuffled (randomized) that file in **Shuffle** (part of the **Bright12** package). The result was _SIM-6_. I combined the real data file with _SIM-6_ and got _D6_, the big data file used by **Combine 6-12**. Both files (_SIM-6_ and lexicographical 6/49) are available as downloads to registered members (huge sizes: over 300 MB each!)

My personal computer has these performance specs (2011):

Processor Intel(R) Core(TM)2 Quad CPU Q6600 @ 2.40GHz, 2403 Mhz, 4 Core(s), 4 Logical Processor(s)  
Installed Physical Memory (RAM) 4.00 GB  
OS Name Microsoft Windows 7 Professional  
System Type x64-based PC

These are non-rocket-science maximum values of filters I encountered:

Triples: 75  
Quads: 500  
Quintets: 3000  
Sextet: 25000

That is, at each of those filter levels the program slowly, slowly generated lotto 12-49 combinations. It was so slow that I had the impression of back to the future! It felt like 1980s all over again, when I ran my _<u>Atari 800XL</u>_ for days and nights only to get a couple of 12-number lotto combinations! A lot depends on your computer. You will need a few trial-and-error runs. Run this software at Command Prompt only. Close down all applications, including Internet browsers.

-   Take a look at a run I did with only the _Triplet_ filter enabled. It was set to _133_ draws in the UK 6/59 lotto game. A few results of the strategy are published in the _rec.gambling.lottery_ newsgroup: [_**Lottery Strategy for 12-Number Lotto Wheeling**_](https://groups.google.com/forum/?hl=en#!topic/rec.gambling.lottery/bLt5Q3TXQMw).

My old strategy usually enabled the _Quad_, _Quintet_, and _Sextet_ filters simultaneously (only for a few past draws). I selected the last 3 combinations at the end of the run (2-3 days and nights). It was hard to get even 5 12-number combinations! That's why I chose 3 combinations; also, we were 3 players in the group. You might want to try lower settings for the _Quad_, _Quint_, and _Sextet_ filters. The generation process should be slow, however. A slow generation is a favorite of randomness. The chance is better you'll get the 6-number winner within a small amount of 12-number combinations. Repeat, a 12-number combination expands to 924 lotto combinations that did not repeat from the past drawings. Such combinations have a better chance to hit the lotto jackpot in the near future.

We all here know by now that everything is ruled by _**randomness**_. Furthermore, any random event does _**repeat**_ within a number of trials precisely calculated by mathematics (with a high degree of certainty, that is). Thusly, a _6 of 6_ lotto combination in a 649 lotto game will repeat from 4400 past drawings with a 50% degree of certainty. My probability software **Collisions** makes the calculations a breeze even for those hard-headed bullish, religious idiots who attack me (especially in the back). Read this eBook, will you:

-   _Applications uvda_ [_**Birthday Paradox: Lottery, Lotto, Roulette**_](https://saliu.com/birthday-paradox.html).

So, I chose 3 12-number combinations. I wheeled each combination twice by applying one of the special lotto wheels I created. The lotto wheels consisted of 4 groups of 3 numbers each, for a total of 12 numbers in the pool. First, I sorted a 12-number combination in lexicographical order, then I applied the lotto wheel. At that time, I was lowly aware of [**randomness**](https://saliu.com/bbs/messages/683.html) _**and its almighty presence**_. I no longer sort the number pools lexicographically. I randomize or shuffle the numbers. The more randomization iterations, the better the probability to come across the winning lotto combination.

![Applying the 12-number lotto strategy and checking for winners in a real-result lotto file.](https://saliu.com/HLINE.gif)

The best way to apply the 12-number lotto wheel is <u>randomization</u>. The 12-number lotto combinations are already in random order. Leave them as-is. If you want to apply the 12-lotto wheel twice, group the numbers by frequency. The Super Utilities option (**Software Lotto 6**) creates very good frequency reports for pairs and triples. Group lotto numbers that show good frequency together. Another option to consider is to wheel a 12-number lotto combination only once. That way you can choose to play twice as many 12-number combinations. Again, my lottery software is the best tool to wheel your lotto numbers: **Lotto Wheeler**.

You can see how a lotto strategy fared in the past by going back 100 drawings in your game. Delete the top 100 draws in your Data-6 file. Save it as _Data-6.2_. Recreate the new _D6_ (_Data-6.2+SIM-6 D6_). Run **Combine-6-12** with your filter settings (the slow generating process). Select your combinations from the bottom of the output file. Use **Winners** (also included in **Bright12**) to check for winners against your original _Data-6_. Repeat the process by deleting the top 99 draws from your _Data-6_. Save again as _Data-6.2_. Recreate _D6_, etc.

<u>How to use <b>Winners</b> to check for winners against your file with real lotto drawings.</u>  
Of course, you applied a 12-number lotto wheel, so you have a group of 6-number lotto combinations (with the numbers to play). Run **Winners** for such a group and against your original _Data-6_ (never against _Data-6.2_ or _D6_).

You can also use **Winners** for 12-number combinations without applying a lotto wheel first. You chose a number of 12-number lotto combinations. You don't wheel them. You want to check how that group of 12-number lines would have fared in the past. Because, by wheeling the lotto numbers, chances are you will lose the jackpot combination more often than not. In this case, you run **<u>Break Down Numbers</u>** (option _**B: Break Down Lines of Numbers**_). The program will break the 12-number lines (combinations) into groups of 6 lotto numbers each. Feed that group to **Winners** (as input file).

<u>Caveat:</u>  
**Break Down Numbers** will eliminate all duplicates and allow only unique 6-number combinations. You might notice, however, that 12-number lines generated by **Combine 6-12** have in common 6, or 7, or 8 groups of numbers. Therefore, multiple jackpot lotto combinations are possible, but **Winners** will report only one.

![The old lotto strategy is also…new: It applies to bookie lotteries as well.](https://saliu.com/HLINE.gif)

**Bright 12** is immediately available to download to registered members:

-   [_**Download**_ **<u>Bright12</u>** _**from this location.**_](https://saliu.com/code/BRIGHT12.exe)

Before downloading, create a folder (directory) for this software package; e.g. **BRIGHT12**. You can create a folder more easily in the GUI mode, in _Windows Explorer_ (_File Explorer_ in Windows 8, 10). Open Explorer and find your drive **C:**. You will probably find it under _My Computer_ (or simply _Computer_ in Vista / Windows 7; _This PC_ in Windows 10). Double click **C:** in the left pane. Move to the right pane, all the way down. Right click on the empty space. From the ensuing menu select _New_, then _Folder_. In the highlighted _New Folder_ type **BRIGHT12**. You download **Bright 12** from **saliu.com** to your **C:\\BRIGHT12** folder.

You can decompress **BRIGHT12** in Windows Explorer. Navigate to your **C:** drive, then to the **BRIGHT12** folder. Double click on the folder. Move to the right pane and now double click **BRIGHT12**. The self-decompression starts automatically. Select the same folder for the location of the decompressed files. You can do the same thing at the _**command prompt**_. At the **C:\\BRIGHT12** prompt, you type: **Bright12** and press _Enter_. The decompression starts automatically and you can select the same folder as the destination.

After decompression, read either _README.TXT_ or _README12.TXT_. Type **B12** (or **b12**) to start the application. Remember also, B12 is a vitamin extremely beneficial to the brain! I always take supplements with high dosage of vitamin B12, even though I am not vegetarian…

![Visit the best resources in lottery mathematics: Software, systems, strategies, lotto wheels.](https://saliu.com/HLINE.gif)

## [Resources in Lottery Software, Strategies, Systems, Lotto Wheels](https://saliu.com/content/lottery.html)

See a comprehensive directory of the pages and materials on the subject of lottery, lotto, software, systems, lotto wheeling.

-   [_**Lotto Software, Wheels: 10-Number Combinations in Lotto 5 Games**_](https://saliu.com/lotto-10-5-combinations.html).
-   [_**Lotto Software: <u>7-by-7-Number Matrices</u> (Perfect Squares) in 6-49 Lotto Games**_](https://saliu.com/7by7-lotto-6-49.html).
-   [_**Lotto Strategy: 12-Number Combinations Wheeled, Played in Lotto-6 Games**_](https://saliu.com/lotto-jackpot-lost.html).
-   [_**History of Lottery Experiences: Lotto Systems, Software, Strategies**_](https://saliu.com/bbs/messages/532.html).
-   [_**Randomness, Degree of Randomness, Absolute Certainty, True Random Numbers**_](https://saliu.com/bbs/messages/683.html).
-   [**<u>Play-All-Lotto-Numbers Analysis</u>**: _**Shuffle Software, Systems**_](https://saliu.com/all-lotto-numbers.html).  
    British professors won the UK National Lottery jackpot. They played all the numbers in the lotto game by shuffling or randomizing the lotto 6/49 numbers.
-   Are [_**lottery, gambling winnable**_](https://saliu.com/TRUTH.html) consistently — or is Ion Saliu just another crook or lunatic?
-   [**<u>Skip Systems</u>** _**for Lottery, Powerball, Mega Millions, Euromillions**_](https://saliu.com/skip-strategy.html).
-   [_**Lottery Utility Software**_](https://saliu.com/lottery-utility.html): Pick-3, 4 Lottery, Lotto-5, 6, Powerball, Mega Millions, Thunderball, Euromillions.
-   [_**Basics of Lottery, Lotto Strategy Based on: Sums (Sum-Totals); Odd / Even; Low / High Numbers**_](https://saliu.com/strategy.html).
-   [_**Lottery Strategy, Systems Based on Number Frequency**_](https://saliu.com/frequency-lottery.html).
-   [**<u>Lotto Decades</u>**: _**Analysis, Software, Systems**_](https://saliu.com/decades.html).
-   The Best Analysis Ranges for [_**Lotto Number Frequency, Lottery Pairs**_](https://saliu.com/lottery-lotto-pairs.html).
-   [_**<u>MDIEditor Lotto WE</u>: Tutorial, Software User Guide, Manual, Book, ebook**_](https://saliu.com/MDI-lotto-guide.html).  
    ~ Also applicable to LotWon lottery, lotto software; plus Powerball, Mega Millions, Euromillions.
-   [_**Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps, Programs**_](https://saliu.com/forum/lotto-book.html).
-   Practical [_**Lottery Filtering, Lotto Filters in Software**_](https://saliu.com/filters.html).
-   [_**Create Lotto Wheels: Manually, in Lotto Wheeling Software**_](https://saliu.com/lottowheel.html).
-   [_**Free lottery wheeling software for players of lotto wheels**_](https://saliu.com/bbs/messages/857.html).  
    ~ Fill out lotto wheels with player's picks (numbers to play); presenting **FillWheel, LottoWheeler** lottery wheeling software.
-   _**Download**_ [**<u>lottery software, lotto programs</u>**](https://saliu.com/infodown.html).

![Convert lotto combinations to 6 numbers with 12-number lottery wheels by Ion Saliu Parpaluck.](https://saliu.com/HLINE.gif)

**[Home](https://saliu.com/index.htm) | [Search](https://saliu.com/Search.htm) | [New Writings](https://saliu.com/bbs/index.html) | [Fundamental Formula](https://saliu.com/formula.htm) | [Odds, Generator](https://saliu.com/calculator_generator.html) | [Contents](https://saliu.com/content/index.html) | [Forums](https://forums.saliu.com/) | [Sitemap](https://saliu.com/sitemap/index.html)**

![Exiting site of the new best lotto software, strategies, systems to win lotto jackpots.](https://saliu.com/HLINE.gif)
