# Backtesting engine for strategy evaluation

"""
BacktestingEngine for evaluating strategy performance against historical data
"""
mutable struct BacktestingEngine
    historical_data::Vector{LotteryDraw}
    test_period::Tuple{Date, Date}
    validation_cache::Dict{String, Any}
    performance_metrics::Dict{String, Float64}
    
    function BacktestingEngine(historical_data::Vector{LotteryDraw}, start_date::Date, end_date::Date)
        # Validate date range
        if start_date > end_date
            throw(ArgumentError("Start date must be before end date"))
        end
        
        # Filter historical data to test period
        filtered_data = filter(draw -> start_date <= draw.draw_date <= end_date, historical_data)
        
        if isempty(filtered_data)
            @warn "No historical data found in the specified test period"
        end
        
        validation_cache = Dict{String, Any}()
        performance_metrics = Dict{String, Float64}(
            "total_backtests_run" => 0.0,
            "average_backtest_time" => 0.0,
            "last_backtest_time" => 0.0
        )
        
        new(filtered_data, (start_date, end_date), validation_cache, performance_metrics)
    end
end

"""
Create BacktestingEngine with automatic date range detection
"""
function BacktestingEngine(historical_data::Vector{LotteryDraw})
    if isempty(historical_data)
        throw(ArgumentError("Historical data cannot be empty"))
    end
    
    # Sort data by date to find range
    sorted_data = sort(historical_data, by = d -> d.draw_date)
    start_date = sorted_data[1].draw_date
    end_date = sorted_data[end].draw_date
    
    return BacktestingEngine(historical_data, start_date, end_date)
end

"""
Run comprehensive backtest for any strategy
"""
function run_backtest(engine::BacktestingEngine, strategy_combinations::Vector{Vector{Int}}, 
                     strategy_name::String = "Strategy")::BacktestResult
    start_time = time()
    
    if isempty(strategy_combinations)
        @warn "No strategy combinations provided for backtesting"
        return create_empty_backtest_result(strategy_name, engine.test_period)
    end
    
    if isempty(engine.historical_data)
        @warn "No historical data available for backtesting"
        return create_empty_backtest_result(strategy_name, engine.test_period)
    end
    
    # Calculate hit rates
    hit_rates = calculate_hit_rates(engine, strategy_combinations, engine.historical_data)
    
    # Calculate efficiency ratios compared to random play
    efficiency_comparison = compare_to_random(engine, hit_rates)
    
    # Calculate cost analysis
    cost_analysis = calculate_cost_analysis(strategy_combinations, efficiency_comparison)
    
    # Create result
    result = BacktestResult(
        strategy_name,
        engine.test_period,
        length(strategy_combinations),
        Dict("3/5" => hit_rates.three_of_five, "4/5" => hit_rates.four_of_five, "5/5" => hit_rates.five_of_five),
        efficiency_comparison.efficiency_ratios,
        cost_analysis
    )
    
    # Update performance metrics
    backtest_time = time() - start_time
    engine.performance_metrics["last_backtest_time"] = backtest_time
    engine.performance_metrics["total_backtests_run"] += 1
    
    # Update average time
    total_tests = engine.performance_metrics["total_backtests_run"]
    current_avg = engine.performance_metrics["average_backtest_time"]
    engine.performance_metrics["average_backtest_time"] = 
        (current_avg * (total_tests - 1) + backtest_time) / total_tests
    
    return result
end

"""
Run backtest for Wonder Grid strategy specifically
"""
function run_wonder_grid_backtest(engine::BacktestingEngine, wonder_grid_engine::WonderGridEngine, 
                                key_number::Int)::BacktestResult
    # Generate Wonder Grid combinations
    combinations = generate_combinations(wonder_grid_engine, key_number)
    
    # Run backtest
    return run_backtest(engine, combinations, "Wonder Grid (Key: $key_number)")
end

"""
Run backtest for Wonder Grid with LIE elimination
"""
function run_wonder_grid_lie_backtest(engine::BacktestingEngine, wonder_grid_engine::WonderGridEngine, 
                                    key_number::Int)::BacktestResult
    # Generate Wonder Grid combinations with LIE elimination
    lie_analysis = generate_combinations_with_lie_analysis(wonder_grid_engine, key_number)
    combinations = lie_analysis["filtered_combinations"]
    
    # Run backtest
    return run_backtest(engine, combinations, "Wonder Grid + LIE (Key: $key_number)")
end

"""
Run comparative backtest between multiple strategies
"""
function run_comparative_backtest(engine::BacktestingEngine, strategies::Dict{String, Vector{Vector{Int}}})::Dict{String, BacktestResult}
    results = Dict{String, BacktestResult}()
    
    for (strategy_name, combinations) in strategies
        results[strategy_name] = run_backtest(engine, combinations, strategy_name)
    end
    
    return results
end

"""
Create empty backtest result for error cases
"""
function create_empty_backtest_result(strategy_name::String, test_period::Tuple{Date, Date})::BacktestResult
    return BacktestResult(
        strategy_name,
        test_period,
        0,
        Dict("3/5" => 0.0, "4/5" => 0.0, "5/5" => 0.0),
        Dict("3/5" => 0.0, "4/5" => 0.0, "5/5" => 0.0),
        CostAnalysis(0, 1.0, 0.0, 0.0)
    )
end

"""
Calculate hit rates for different prize tiers with detailed analysis
"""
function calculate_hit_rates(engine::BacktestingEngine, combinations::Vector{Vector{Int}}, 
                           actual_draws::Vector{LotteryDraw})::HitRates
    if isempty(combinations) || isempty(actual_draws)
        return HitRates(0.0, 0.0, 0.0)
    end
    
    # Initialize counters
    three_hits = 0
    four_hits = 0
    five_hits = 0
    total_draws = length(actual_draws)
    total_combinations = length(combinations)
    
    # Track hits per draw for analysis
    hits_per_draw = Dict{Int, Dict{String, Int}}()
    
    for (draw_idx, draw) in enumerate(actual_draws)
        draw_numbers = Set(draw.numbers)
        draw_hits = Dict("3/5" => 0, "4/5" => 0, "5/5" => 0)
        
        for combo in combinations
            combo_set = Set(combo)
            matches = length(intersect(draw_numbers, combo_set))
            
            if matches == 3
                three_hits += 1
                draw_hits["3/5"] += 1
            elseif matches == 4
                four_hits += 1
                draw_hits["4/5"] += 1
            elseif matches == 5
                five_hits += 1
                draw_hits["5/5"] += 1
            end
        end
        
        hits_per_draw[draw_idx] = draw_hits
    end
    
    # Calculate rates per draw (more meaningful than per combination-draw pair)
    three_rate = total_draws > 0 ? three_hits / total_draws : 0.0
    four_rate = total_draws > 0 ? four_hits / total_draws : 0.0
    five_rate = total_draws > 0 ? five_hits / total_draws : 0.0
    
    # Store detailed analysis in engine cache
    engine.validation_cache["last_hit_analysis"] = Dict(
        "total_draws" => total_draws,
        "total_combinations" => total_combinations,
        "hits_per_draw" => hits_per_draw,
        "total_hits" => Dict("3/5" => three_hits, "4/5" => four_hits, "5/5" => five_hits)
    )
    
    return HitRates(three_rate, four_rate, five_rate)
end

"""
Calculate hit rates with combination-level detail
"""
function calculate_detailed_hit_rates(engine::BacktestingEngine, combinations::Vector{Vector{Int}}, 
                                    actual_draws::Vector{LotteryDraw})::Dict{String, Any}
    if isempty(combinations) || isempty(actual_draws)
        return Dict{String, Any}(
            "hit_rates" => HitRates(0.0, 0.0, 0.0),
            "combination_performance" => Dict{Vector{Int}, Dict{String, Int}}(),
            "draw_analysis" => Dict{Int, Any}()
        )
    end
    
    # Track performance for each combination
    combination_performance = Dict{Vector{Int}, Dict{String, Int}}()
    draw_analysis = Dict{Int, Any}()
    
    # Initialize combination tracking
    for combo in combinations
        combination_performance[combo] = Dict("3/5" => 0, "4/5" => 0, "5/5" => 0, "total_matches" => 0)
    end
    
    total_three = 0
    total_four = 0
    total_five = 0
    
    for (draw_idx, draw) in enumerate(actual_draws)
        draw_numbers = Set(draw.numbers)
        draw_hits = Dict("3/5" => 0, "4/5" => 0, "5/5" => 0)
        winning_combinations = Vector{Vector{Int}}()
        
        for combo in combinations
            combo_set = Set(combo)
            matches = length(intersect(draw_numbers, combo_set))
            
            if matches >= 3
                combination_performance[combo]["total_matches"] += 1
                push!(winning_combinations, combo)
                
                if matches == 3
                    combination_performance[combo]["3/5"] += 1
                    draw_hits["3/5"] += 1
                    total_three += 1
                elseif matches == 4
                    combination_performance[combo]["4/5"] += 1
                    draw_hits["4/5"] += 1
                    total_four += 1
                elseif matches == 5
                    combination_performance[combo]["5/5"] += 1
                    draw_hits["5/5"] += 1
                    total_five += 1
                end
            end
        end
        
        draw_analysis[draw_idx] = Dict(
            "draw_date" => draw.draw_date,
            "draw_numbers" => draw.numbers,
            "hits" => draw_hits,
            "winning_combinations" => winning_combinations,
            "total_winners" => length(winning_combinations)
        )
    end
    
    # Calculate rates
    total_draws = length(actual_draws)
    hit_rates = HitRates(
        total_draws > 0 ? total_three / total_draws : 0.0,
        total_draws > 0 ? total_four / total_draws : 0.0,
        total_draws > 0 ? total_five / total_draws : 0.0
    )
    
    return Dict{String, Any}(
        "hit_rates" => hit_rates,
        "combination_performance" => combination_performance,
        "draw_analysis" => draw_analysis,
        "summary" => Dict(
            "total_draws" => total_draws,
            "total_combinations" => length(combinations),
            "best_combination" => find_best_combination(combination_performance),
            "worst_combination" => find_worst_combination(combination_performance)
        )
    )
end

"""
Find the best performing combination
"""
function find_best_combination(combination_performance::Dict{Vector{Int}, Dict{String, Int}})::Tuple{Vector{Int}, Int}
    best_combo = Vector{Int}()
    best_score = -1
    
    for (combo, performance) in combination_performance
        # Score based on weighted hits (5/5 = 100 points, 4/5 = 10 points, 3/5 = 1 point)
        score = performance["5/5"] * 100 + performance["4/5"] * 10 + performance["3/5"] * 1
        
        if score > best_score
            best_score = score
            best_combo = combo
        end
    end
    
    return (best_combo, best_score)
end

"""
Find the worst performing combination
"""
function find_worst_combination(combination_performance::Dict{Vector{Int}, Dict{String, Int}})::Tuple{Vector{Int}, Int}
    worst_combo = Vector{Int}()
    worst_score = typemax(Int)
    
    for (combo, performance) in combination_performance
        # Score based on weighted hits
        score = performance["5/5"] * 100 + performance["4/5"] * 10 + performance["3/5"] * 1
        
        if score < worst_score
            worst_score = score
            worst_combo = combo
        end
    end
    
    return (worst_combo, worst_score)
end

"""
Compare strategy results to random play with accurate calculations
"""
function compare_to_random(engine::BacktestingEngine, hit_rates::HitRates)::EfficiencyComparison
    # Calculate exact theoretical random odds for Lotto 5/39
    total_combinations = binomial(39, 5)  # 575,757
    
    # Exact probabilities for random single combination
    prob_5_of_5 = 1.0 / total_combinations
    prob_4_of_5 = (binomial(5, 4) * binomial(34, 1)) / total_combinations  # 5 * 34 / 575,757
    prob_3_of_5 = (binomial(5, 3) * binomial(34, 2)) / total_combinations  # 10 * 561 / 575,757
    
    random_odds = Dict{String, Float64}(
        "3/5" => prob_3_of_5,
        "4/5" => prob_4_of_5,
        "5/5" => prob_5_of_5
    )
    
    strategy_odds = Dict{String, Float64}(
        "3/5" => hit_rates.three_of_five,
        "4/5" => hit_rates.four_of_five,
        "5/5" => hit_rates.five_of_five
    )
    
    # Calculate efficiency ratios
    efficiency_ratios = Dict{String, Float64}()
    for tier in ["3/5", "4/5", "5/5"]
        if random_odds[tier] > 0
            efficiency_ratios[tier] = strategy_odds[tier] / random_odds[tier]
        else
            efficiency_ratios[tier] = 0.0
        end
    end
    
    return EfficiencyComparison(strategy_odds, random_odds, efficiency_ratios)
end

"""
Compare strategy to random play using BacktestResult
"""
function compare_to_random(engine::BacktestingEngine, strategy_results::BacktestResult)::EfficiencyComparison
    hit_rates = HitRates(
        strategy_results.hit_rates["3/5"],
        strategy_results.hit_rates["4/5"],
        strategy_results.hit_rates["5/5"]
    )
    
    return compare_to_random(engine, hit_rates)
end

"""
Calculate comprehensive efficiency analysis
"""
function calculate_efficiency_analysis(engine::BacktestingEngine, strategy_results::BacktestResult)::Dict{String, Any}
    efficiency_comparison = compare_to_random(engine, strategy_results)
    
    # Calculate expected value analysis
    # Assuming prize structure: 3/5 = $10, 4/5 = $100, 5/5 = $100,000
    prize_structure = Dict{String, Float64}(
        "3/5" => 10.0,
        "4/5" => 100.0,
        "5/5" => 100000.0
    )
    
    # Calculate expected value per combination
    strategy_ev = 0.0
    random_ev = 0.0
    
    for tier in ["3/5", "4/5", "5/5"]
        strategy_ev += efficiency_comparison.strategy_odds[tier] * prize_structure[tier]
        random_ev += efficiency_comparison.random_odds[tier] * prize_structure[tier]
    end
    
    # Calculate cost-benefit analysis
    cost_per_combination = 1.0  # Assume $1 per combination
    strategy_profit_per_combo = strategy_ev - cost_per_combination
    random_profit_per_combo = random_ev - cost_per_combination
    
    return Dict{String, Any}(
        "efficiency_comparison" => efficiency_comparison,
        "expected_values" => Dict(
            "strategy_ev" => strategy_ev,
            "random_ev" => random_ev,
            "ev_improvement" => strategy_ev - random_ev
        ),
        "profit_analysis" => Dict(
            "strategy_profit_per_combo" => strategy_profit_per_combo,
            "random_profit_per_combo" => random_profit_per_combo,
            "profit_improvement" => strategy_profit_per_combo - random_profit_per_combo
        ),
        "recommendation" => strategy_ev > random_ev ? 
            "Strategy shows positive expected value improvement" : 
            "Strategy does not improve expected value over random play"
    )
end

"""
Calculate cost analysis for strategy
"""
function calculate_cost_analysis(combinations::Vector{Vector{Int}}, 
                               efficiency_comparison::EfficiencyComparison)::CostAnalysis
    total_combinations = length(combinations)
    cost_per_combination = 1.0  # $1 per combination
    total_cost = Float64(total_combinations) * cost_per_combination
    
    # Calculate cost savings vs random play
    # Random play would need to play all possible combinations for same coverage
    # This is a simplified calculation - in practice, random play comparison is complex
    random_combinations_needed = 210  # Assume same number for fair comparison
    random_cost = Float64(random_combinations_needed) * cost_per_combination
    cost_savings = random_cost - total_cost
    
    return CostAnalysis(total_combinations, cost_per_combination, total_cost, cost_savings)
end
"""

Run time-series backtest to analyze strategy performance over time
"""
function run_time_series_backtest(engine::BacktestingEngine, combinations::Vector{Vector{Int}}, 
                                 window_size::Int = 30)::Dict{String, Any}
    if isempty(engine.historical_data) || window_size <= 0
        return Dict{String, Any}("error" => "Invalid data or window size")
    end
    
    # Sort data chronologically
    sorted_data = sort(engine.historical_data, by = d -> d.draw_date)
    
    time_series_results = Dict{Date, Dict{String, Float64}}()
    window_results = Vector{Dict{String, Any}}()
    
    # Sliding window analysis
    for i in window_size:length(sorted_data)
        window_data = sorted_data[(i-window_size+1):i]
        window_start = window_data[1].draw_date
        window_end = window_data[end].draw_date
        
        # Calculate hit rates for this window
        hit_rates = calculate_hit_rates(engine, combinations, window_data)
        
        # Store results
        time_series_results[window_end] = Dict{String, Float64}(
            "3/5" => hit_rates.three_of_five,
            "4/5" => hit_rates.four_of_five,
            "5/5" => hit_rates.five_of_five
        )
        
        push!(window_results, Dict{String, Any}(
            "window_start" => window_start,
            "window_end" => window_end,
            "hit_rates" => hit_rates,
            "window_size" => window_size
        ))
    end
    
    return Dict{String, Any}(
        "time_series_results" => time_series_results,
        "window_results" => window_results,
        "analysis_summary" => analyze_time_series_trends(window_results)
    )
end

"""
Analyze trends in time series backtest results
"""
function analyze_time_series_trends(window_results::Vector{Dict{String, Any}})::Dict{String, Any}
    if length(window_results) < 2
        return Dict{String, Any}("error" => "Insufficient data for trend analysis")
    end
    
    # Extract hit rates over time
    three_rates = [result["hit_rates"].three_of_five for result in window_results]
    four_rates = [result["hit_rates"].four_of_five for result in window_results]
    five_rates = [result["hit_rates"].five_of_five for result in window_results]
    
    return Dict{String, Any}(
        "trends" => Dict{String, Any}(
            "3/5" => Dict(
                "mean" => mean(three_rates),
                "std" => std(three_rates),
                "trend" => calculate_trend(three_rates)
            ),
            "4/5" => Dict(
                "mean" => mean(four_rates),
                "std" => std(four_rates),
                "trend" => calculate_trend(four_rates)
            ),
            "5/5" => Dict(
                "mean" => mean(five_rates),
                "std" => std(five_rates),
                "trend" => calculate_trend(five_rates)
            )
        ),
        "stability_analysis" => Dict(
            "most_stable" => find_most_stable_tier(three_rates, four_rates, five_rates),
            "overall_stability" => calculate_overall_stability(three_rates, four_rates, five_rates)
        )
    )
end

"""
Calculate simple trend (positive, negative, or stable)
"""
function calculate_trend(values::Vector{Float64})::String
    if length(values) < 2
        return "insufficient_data"
    end
    
    # Simple linear trend calculation
    n = length(values)
    x = collect(1:n)
    
    # Calculate correlation coefficient
    if std(values) == 0
        return "stable"
    end
    
    correlation = cor(x, values)
    
    if correlation > 0.1
        return "improving"
    elseif correlation < -0.1
        return "declining"
    else
        return "stable"
    end
end

"""
Find the most stable prize tier
"""
function find_most_stable_tier(three_rates::Vector{Float64}, four_rates::Vector{Float64}, 
                              five_rates::Vector{Float64})::String
    # Calculate coefficient of variation for each tier
    cv_3 = std(three_rates) / max(mean(three_rates), 1e-10)
    cv_4 = std(four_rates) / max(mean(four_rates), 1e-10)
    cv_5 = std(five_rates) / max(mean(five_rates), 1e-10)
    
    min_cv = min(cv_3, cv_4, cv_5)
    
    if min_cv == cv_3
        return "3/5"
    elseif min_cv == cv_4
        return "4/5"
    else
        return "5/5"
    end
end

"""
Calculate overall stability score
"""
function calculate_overall_stability(three_rates::Vector{Float64}, four_rates::Vector{Float64}, 
                                   five_rates::Vector{Float64})::Float64
    # Average coefficient of variation across all tiers
    cv_3 = std(three_rates) / max(mean(three_rates), 1e-10)
    cv_4 = std(four_rates) / max(mean(four_rates), 1e-10)
    cv_5 = std(five_rates) / max(mean(five_rates), 1e-10)
    
    avg_cv = (cv_3 + cv_4 + cv_5) / 3
    
    # Convert to stability score (lower CV = higher stability)
    return max(0.0, 1.0 - min(1.0, avg_cv))
end

"""
Generate random combinations for comparison baseline
"""
function generate_random_combinations(num_combinations::Int = 210)::Vector{Vector{Int}}
    combinations = Vector{Vector{Int}}()
    
    while length(combinations) < num_combinations
        combo = sort(sample(1:39, 5, replace=false))
        if !(combo in combinations)  # Avoid duplicates
            push!(combinations, combo)
        end
    end
    
    return combinations
end

"""
Run backtest comparison between strategy and random play
"""
function run_strategy_vs_random_backtest(engine::BacktestingEngine, 
                                       strategy_combinations::Vector{Vector{Int}},
                                       strategy_name::String = "Strategy")::Dict{String, Any}
    # Generate random combinations for comparison
    random_combinations = generate_random_combinations(length(strategy_combinations))
    
    # Run backtests
    strategy_result = run_backtest(engine, strategy_combinations, strategy_name)
    random_result = run_backtest(engine, random_combinations, "Random Play")
    
    # Calculate comparative metrics
    improvement_metrics = Dict{String, Float64}()
    for tier in ["3/5", "4/5", "5/5"]
        strategy_rate = strategy_result.hit_rates[tier]
        random_rate = random_result.hit_rates[tier]
        
        if random_rate > 0
            improvement_metrics[tier] = (strategy_rate - random_rate) / random_rate * 100
        else
            improvement_metrics[tier] = strategy_rate > 0 ? 100.0 : 0.0
        end
    end
    
    return Dict{String, Any}(
        "strategy_result" => strategy_result,
        "random_result" => random_result,
        "improvement_metrics" => improvement_metrics,
        "summary" => Dict(
            "strategy_superior" => any(v > 0 for v in values(improvement_metrics)),
            "best_improvement_tier" => findmax(improvement_metrics)[2],
            "average_improvement" => mean(collect(values(improvement_metrics)))
        )
    )
end

"""
Get engine performance statistics
"""
function get_backtest_engine_stats(engine::BacktestingEngine)::Dict{String, Any}
    return Dict{String, Any}(
        "test_period" => engine.test_period,
        "historical_data_count" => length(engine.historical_data),
        "performance_metrics" => copy(engine.performance_metrics),
        "cache_entries" => length(engine.validation_cache),
        "data_date_range" => isempty(engine.historical_data) ? nothing : 
            (minimum(d -> d.draw_date, engine.historical_data), 
             maximum(d -> d.draw_date, engine.historical_data))
    )
end

"""
Clear engine caches to free memory
"""
function clear_backtest_caches!(engine::BacktestingEngine)
    empty!(engine.validation_cache)
    engine.performance_metrics["total_backtests_run"] = 0.0
    engine.performance_metrics["average_backtest_time"] = 0.0
    engine.performance_metrics["last_backtest_time"] = 0.0
end