[![Buy the best software: Lottery, Powerball, Mega Millions, gambling roulette, blackjack, sports.](https://saliu.com/lie-lotto-strategies-decades.htmlimages/software-lottery-gambling.gif)](membership.html)  

# _LIE Strategy, Reversed Lottery Strategy_ for Lotto Decades, Last Digits, Odd Even, Low High, Skips

## By <PERSON>, _Founder of Lotto Mathematics_

![Read an introduction to lotto strategies in reverse based on lotto decades, last digits.](https://saliu.com/lie-lotto-strategies-decades.htmlHLINE.gif)

### I. [Introduction to _LIE Elimination_, Reversed Lottery Strategy for Lotto Decades, Last Digits, Odd Even, Low High, Skips](#Mathematics)  
II. [Software for Lottery Strategy in Reverse Based on Lotto Decades, Last Digits, Odd Even, Low High, Skips](#Software)  
III. [Reports for Lottery Strategy in Reverse Based on Lotto Decades, Last Digits, Odd Even, Low High, Skips](#Reports)  
IV. [Generate Combinations for _LIE Elimination_ Strategy on Lotto Decades, Last Digits, Odd Even, Low High, Skips](#Combinations)  
V. [Strategies for _LIE Elimination_ on Lotto Decades, Last Digits, Odd Even, Low High, Skips](#Strategies)  
VI. [Real-Life _LIE Elimination_ Lotto Strategy with Jackpot Wins](#Jackpot)  
VII. [Resources in Lottery Lotto Software, Strategies, Systems](#Resources)

![Access the best resources in lottery, lotto strategies, algorithms, software, programs.](https://saliu.com/lie-lotto-strategies-decades.htmlHLINE.gif)

## 1. Introduction to _LIE Elimination_, Reversed Lottery Strategy for Lotto Decades, Last Digits, Odd Even, Low High, Skips

First captured by the _WayBack Machine_ (_web.archive.org_) on August 6, 2015.

Axiomatic one, the _**reversed lottery strategy**_ also known as _**LIE elimination**_ was first presented on this Web page: [_**Lotto, Lottery Strategy in Reverse: Not-to-Win Leads to Not-to-Lose or WIN**_](reverse-strategy.html). The idea crossed my mind in the 1990s. There are several versions of the _Reversed Lottery Strategy_, one of the latest being presented here: [_**Dedicated LIE Elimination: Lottery, Lotto Strategy in Reverse for Pairs, Number Frequency**_](lie-lottery-strategies-pairs.html).

_**Dedicated LIE Elimination**_, however, generates combinations to be played **directly**, without feeding them to the _**LIE**_ feature in the **Bright** generators. Meanwhile, the reversed strategy presented in this very e-book is a **purely reversed system**: The combinations generated must be fed to the **Bright** output generators.

In discussions in my former forums, users of my software and I noticed important facts regarding the **repeat** of static filters. The _skips_, _decades_, _last digits_, and the _3-group frequencies_ are good candidates for the _**LIE**_ option (_**reversed lottery strategy**_). Look at the reports created by SkipDecaFreq*. You will notice that the strings in a particular lottery drawing are no repeats from several previous draws. Well, then, generate lotto combinations using _skips_, _decades_, _last digits_, and the _3-group frequencies_ as filters. You know it is very rare for the output file to register 5, even 4, winning lotto numbers in the very next drawing. The same is true about the _odd even O E_, and _low high L H_ lotto numbers. Only the _skips_ are **dynamic** filters and they certainly behave very differently from the **static** ones.

Users of my software applied the _**LIE strategy**_ manually for years and years and years. However, they didn't know what levels the filters reached. They counted manually how many drawings the same strings elapsed between hits. They didn't see a repeat of this decade string in the last 20-30 lottery drawings: _1 0 4 0 1_. So, they would generate combinations for, say, the last 20 decade strings (groups of decades) — **one string at a time**. The user would concatenate all outputs to one LIE file and feed it to the _**LIE**_ feature in the **Bright** combination generators.

The process was obviously tedious and not very accurate. Thusly, I wrote specialized software in the year of grace 2014. The software has the _LieStrings_ radix in the program name. The software generates first the very necessary **reports** to aid the user in selecting filter values. The software generates combinations in one batch: The output files are concatenated in the background and also purged of duplicates. The resulting LIE files are fed to the _**LIE**_ feature in the **Bright** combination generators. Despite my serious efforts, I wasn't able to create the same type of software as for the _**Dedicated LIE Elimination: Lottery, Lotto Strategy in Reverse for Pairs, Number Frequency**_.

![This is the best lottery software to generate lotto strategies to NOT-NOT-WIN.](https://saliu.com/lie-lotto-strategies-decades.htmlHLINE.gif)

## 2. Software for Lottery Strategy in Reverse Based on Lotto Decades, Last Digits, Odd Even, Low High, Skips

- The ultimate lotto, lottery, horse racing software for applying _Reversed Lottery Strategy for Lotto Decades, Last Digits, Odd Even, Low High, Skips_:
    
    - **LieStrings3** ~ for pick-3 daily games;
    - **LieStrings4** ~ for pick-4 lotteries;
    - **LieStrings5** ~ for 5-number lotto games;
    - **LieStrings6** ~ for 6-number lottos;
    - **LieStringsH3** ~ for horse racing trifectas.
        
    - Please be advised that these programs, released in 2015, require a special form of _membership to download software_: _**Ultimate Lottery Software**_. Click on the top banner to learn the terms, conditions, and availability. All my software is always announced in the **Forums**, **New Writings**, **Software Downloads**. The presentation pages are always published first. Such Web resources are free to read and help the reader make a decision regarding the licensing of software.
    - The program for _LIE Elimination_: Lottery Strategy in Reverse Based on **Lotto Decades**, **Last Digits**, **Odd Even**, **Low High**, **Skips** runs from the main menu of the respective _**Ultimate Software**_ package, function _N = LIE StriNgs (LieStrings*)_.
    
    ![The ultimate lottery software has reverse strategy specialized programs, lotto applications.](https://saliu.com/lie-lotto-strategies-decades.htmlimages/ultimate-lotto-software-60.gif)
    
    The parameters in this type of software for reversed lottery strategy have unique behavior. Yet, I put them together in the same program instead of writing separate programs for decades, separate programs for last digits, etc. There is, however, a common function for _strategy checking_ (all reports in one batch) and a common function for _sorting_ the reports by column. The combinations are generated by separate modules as presented in the software menus.
    
- The following screenshot is the main menu of all programs in this type of _lottery strategies in reverse_ —
    
    ![The Lie reversed lottery strategy software is very well organized in 4 menus.](https://saliu.com/lie-lotto-strategies-decades.htmlimages/lie-lottery-decades-software.gif)
    
    ## 3. Reports for Lottery Strategy in Reverse Based on Lotto Decades, Last Digits, Odd Even, Low High, Skips
    
- The function _R = Generate String Reports (REP)_ is mandatory to run first as it creates auxiliary files. If not, the software triggers an error when trying to generate combinations.
    
    ![The software generates first comprehensive reports to aim at lotto jackpots.](https://saliu.com/lie-lotto-strategies-decades.htmlimages/lie-lottery-digits-report.gif)
    
    The software in this category generates **6 reports**, for each type of filter.
    
    ![The reports are dependent on ranges of lottery history or past lottery drawings.](https://saliu.com/lie-lotto-strategies-decades.htmlimages/lie-lottery-skips-draws.gif)
    
    #### Report for the _skips regardless of position_ (a.k.a. _ANY_ in the software); filename _LieAny5.REP_
    
    1 0 1 3 6  
    23 3 3 10 6  
    0 3 0 6 7  
    5 7 16 4 2  
    ...
    
    The figures in line #1 translated: lotto #1 in the lotto-5 draw skipped 1 drawing; lotto #2 in the draw skipped 0 drawings (i.e. hit in 2 consecutive drawings); lotto #3 in the draw skipped 1 drawing; lotto #4 in the draw skipped 3 lottery drawings; lotto #5 in the draw skipped 6 drawings (i.e. hit in draw #8: 8 – 1 – 1 = 6).
    
    #### Report for the _skips position by position_ (a.k.a. _POS_ in the software); filename _LiePos5.REP_
    
    1 24 94 3 23  
    54 3 3 18 6  
    38 3 22 22 7  
    21 7 64 145 2  
    ...
    
    The figures in line #1 interpreted: lotto #1 in the lotto-5 draw skipped 1 drawing; lotto #2 in the draw skipped 24 drawings (i.e. hit in draw #26: 26 – 1 – 1 = 24); lotto #3 in the draw skipped 94 drawings; lotto #4 in the draw skipped 3 lottery drawings; lotto #5 in the draw skipped 23 drawings.
    
    #### Report for the _high, low numbers_ (a.k.a. _HiLo_ in the software); filename _LieHiLo5.REP_
    
    1 1 1 2 2  
    1 2 2 2 2  
    1 1 1 2 2  
    1 1 1 1 2  
    ...
    
    Translation of the figures in line #1: lotto #1 in the lotto-5 draw was _low_; lotto #2 in the draw was _low_; lotto #3 in the draw was _low_; lotto #4 in the draw was _high_; lotto #5 in the draw was _high_.
    
    ![Lottery application generates 6 reports: decades, last digits, skips, odd even low high numbers.](https://saliu.com/lie-lotto-strategies-decades.htmlimages/lie-lottery-odd-even.gif)
    
    #### Report for the _odd, even numbers_ (a.k.a. _OdEv_ in the software); filename _LieOdEv5.REP_
    
    2 1 2 1 2  
    1 2 1 2 1  
    2 2 1 2 1  
    1 2 1 1 1  
    ...
    
    Interpretation of the figures in line #1: lotto #1 in the lotto-5 draw was _even_; lotto #2 in the draw was _odd_; lotto #3 in the draw was _even_; lotto #4 in the draw was _odd_; lotto #5 in the draw was _even_.
    
    #### Report for the _decades_; filename _LieDecade5.REP_
    
    2 2 2 4 4  
    2 3 3 4 5  
    2 2 3 3 5  
    1 2 2 3 4  
    ...
    
    Translation of the figures in line #1: lotto #1 in the draw was from decade _#2 (10 – 19)_; lotto #2 in the draw was from decade _#2 (10 – 19)_; lotto #3 in the draw was from decade _#2 (10 – 19)_; lotto #4 in the draw was from decade _#4 (30 – 39)_; lotto #5 in the draw was from decade _#4 (30 – 39)_.
    
    #### Report for the _last digits_; filename _LieLastDigit5.REP_
    
    2 3 4 1 2  
    3 2 3 8 1  
    2 4 1 4 3  
    9 2 9 1 7  
    ...
    
    Interpretation of the figures in line #1: lotto #1 in the draw had _2 as last digit_; lotto #2 in the draw had _3 as last digit_; lotto #3 in the draw had _4 as last digit_; lotto #4 in the draw had _1 as last digit_; lotto #5 in the draw had _2 as last digit_.
    
- The 6 reports above are strictly necessary for _generating combinations_ and also _verifying the viability_ of various strategies.
    
- This software generates also another type of reports. They have the **WS** extension and they serve the same purpose as the WS reports in **Bright** packages: _Select_ filter values (create strategies). The WS reporting will be analyzed in section #5 of this ebook: _Strategies_.
    
    ## 4. Generate Combinations for _LIE Elimination_ Strategy on Lotto Decades, Last Digits, Odd Even, Low High, Skips
    
- The function _G = Generate Combinations_ needs the files created automatically by _Reporting_. Instead of typing, the filters are fed automatically from the string file created by the corresponding _Report_ function.
    
    ![The lottery combinations generated by the strategy are fed to LIE in lexicographical software.](https://saliu.com/lie-lotto-strategies-decades.htmlimages/lie-lottery-decades-combinations.gif)
    
    You select, however, how many _filter lines_ (strings) to use inspired by the WS reports you studied. For example, you use one line from the _odd even_ report file _LieOdEv5.REP_: _2 1 2 1 2_. The software generates correctly the 5/43 lotto combosnations: 33649. (I decided to use the term _combosnations_ for lottery combinations that should not be played, that should be eliminated beforehand.) It is very rare for such a decade string of filter values to repeat in the very next drawing. Thus, for a vast majority of cases those 33649 _combosnations_ are wasteful (oops... tautology!)
    
    There are situations when the ID5, even the ID4, filters reach very high values in the WS reports. It is therefore recommended to run the _R = Report_ function for thousands of combinations – at least in the first run. Then, sort the reports by column (function _T = SorT WS Reports by Column_) to see the highest filter values at the top. You'll get an adequate picture of what to expect in subsequent report runs.
    
        *** WS LIE Report for DECADES *
        * Analyzed: 1000 of 2000 lines
    
     Line    Decades   Decades   Decades
      no.      ID3       ID4       ID5  
    
      209       0         33      1791
      428       9        473      1572
      497       3         23      1503
      281       1        106      1479
      592      27         74      1408
    ...
    
        * WS LIE Report for LAST DIGITS *
        * Analyzed: 2500 of 5000 lines
    
     Line    LastDig   LastDig   LastDig
      no.      ID3       ID4       ID5  
    
        1      29        925      4999
        2      43        183      4998
        4     243       4996      4996
        6      30       1886      4994
        7      11        666      4993
    ...
      958     105       3383       191
      889      28        161       187
     1176     100       3824       161
      851      69       2074       155
      439      61       2033       147
     1114     137       1822       141
      332      35       3253       141
     2042      21        264       134
     2123       7        317       126
       40      69       3339       117
     1634     156        484        44**
    
    You notice that the exact _last digit_ strings of filter levels do NOT repeat even in 5000 drawings! Only in 11 drawings out of 2500, _LastDig ID5_ repeated after fewer than 200 draws. The _last digits_ offer the most sure-fire reversed lotto strategy. The players waste serious money by playing lottery _combosnations_!
    
    This is your thinking when the generating modules present you with the prompt _How many LINES to use_.
    
    ![The reversed strategy combinations are safe to be eliminated for many past lottery drawings.](https://saliu.com/lie-lotto-strategies-decades.htmlimages/lie-lottery-digits-filters.gif)
    
    - Please be mindful that very high filter values represent processor-intensive operations. Patience and/or fast computers ought to be expected especially for the jackpot lottos (5- and 6-number games). I put my best into coding this type of software and these are the fastest algorithms I conceived.
        
    - The _**skips**_ (_ANY_ and _POS_) require very special attention. They are **dynamic** filters and do NOT behave like the **static** filters in these programs. Therefore, axiomatic, do NOT use filter values based on the respective WS reports. Nonetheless, those filters offer some sure-fire winning strategies and systems. See chapter 6 later on.
    
        *** WS LIE Report for ANY_MIN *
        * File: LieAny5.REP
        * Analyzed: 2500 of 5000 lines
    
     Line    Any_Min   Any_Min   Any_Min
      no.      ID3       ID4       ID5  
    
        1       1          2         0
        2       3          8       674
        3       0          2         4
        4       0          3        50
        5       0          9         2
    ...**
    
    - The reason for the _skips_ idiosyncrasy is quite simple. The same _skip_ string (e.g. _1 0 1 3 6_) is represented by different _**actual** lottery numbers_ depending on the point in the data file. A _skip_ of 0 at the beginning of the data file might be represented by the lotto numbers 3, 16, 23, 34, 41. However, 100 drawings back in data, the _skip_ of 0 might lead to different actual lottery numbers: 8, 16, 19, 24, 39.
        
    - In fact, setting a filter like _Any_Min ID5_ to 200 is likely to generate all possible combinations in the game. Therefore, there is nothing left to play after applying the _**LIE Elimination**_!
        
    - There is good news, however. In testing, I noticed something sensational. I applied filters like _Any_Min ID5_ equal to 5, even 10 (even 50 sometimes) — and I was _correcto mundo_! Look at line #1 in _LieAny5.REP_ above. I would enter _Any_Min ID5_ equal to 5 (_How many LINES to use_) and I would be right. Looks like the _skips_ offer **automatic** _combosnations_ for this type of reversed lottery strategy.
        
    - One more caveat, this time for the **static** filters. This condition must always apply if using the _ID4_ and/or _ID3_ filters:
    
    **ID3 < ID4 < ID5**
    
        *** WS LIE Report for DECADES *
        * File: LieDecade5.REP
    
     Line    Decades   Decades   Decades
      no.      ID3       ID4       ID5  
    
        1       2         55        21
        2       0          6         2
        3       1         77        72
        4       5          2        32
        5       0          3        99
        6       0         22        18
        7       3          2        18**
    
    - In the case above, you cannot play _ID3_ or _ID4_ in draw #3 (_ID4_ > _ID5_) or drawing #7 (_ID3_ > _ID4_). In such cases, you can only apply the _ID5_ filter.
    
    ## 5. Strategies for_LIE Elimination_ on Lotto Decades, Last Digits, Odd Even, Low High, Skips
    
    The **LieStrings** software applications generate a second type of reports necessary to select filter values (construct strategies). Function: _S = String Files Skips (WS Reports)_. This function needs the 6 reports (REP files) created by the _R = Generate String Reports_ fixture. The WS reporting will generate 8 reports, as the _ANY_ and _POS_ (_skips_) have separate reports for the _minimum_ and _maximum_ levels.
    
    ![Markov chains, followers, pairings apply also to pick-3, pick 4 daily lottery games.](https://saliu.com/lie-lotto-strategies-decades.htmlimages/lie-lottery-win-skips.gif)
    
    #### Report for the _skips regardless of position ~ minimum_; filename _LieAny5-Min.WS_
    
        *** WS LIE Report for ANY_MIN *
        * File: LieAny5.REP
        * Analyzed: 2500 of 5000 lines
    
     Line    Any_Min   Any_Min   Any_Min
      no.      ID3       ID4       ID5  
    
        1       1          2         0
        2       3          8       674
        3       0          2         4
        4       0          3        50
        5       0          9         2
        6       1        120      4994
    ...**
    
    #### Report for the _skips regardless of position ~ maximum_; filename _LieAny5-MAX.WS_
    
        *** WS LIE Report for ANY_MAX *
        * File: LieAny5.REP
        * Analyzed: 2500 of 5000 lines
    
     Line    Any_Max   Any_Max   Any_Max
      no.      ID3       ID4       ID5  
    
        1      10       1196      4999
        2       0          2        19
        3      18       4997      4997
        4       5          8       153
        5       4          7      1289
    ...**
    
    #### Report for the _skips position by position ~ minimum_; filename _LiePos5-Min.WS_
    
        *** WS LIE Report for POS_MIN *
        * File: LiePos5.REP
        * Analyzed: 2500 of 5000 lines
    
     Line    Pos_Min   Pos_Min   Pos_Min
      no.      ID3       ID4       ID5  
    
        1       3          4         6
        2       1          0         3
        3       0          2       147
        4       0         38       587
        5      10          0       121
        6       1        135      4994
    ...**
    
    #### Report for the _skips position by position ~ maximum_; filename _LiePos5-MAX.WS_
    
        *** WS LIE Report for POS_MAX *
        * File: LiePos5.REP
        * Analyzed: 2500 of 5000 lines
    
     Line    Pos_Max   Pos_Max   Pos_Max
      no.      ID3       ID4       ID5  
    
        1       0         46       560
        2      19         11       284
        3       1         10       235
        4       2          5        78
        5       1          7         8
        6      10          2         0
        7       4         85      1407
    ...**
    
    ![The reversed lottery strategy works with static and dynamic filters, restrictions to reduce combinations to play.](https://saliu.com/lie-lotto-strategies-decades.htmlimages/lie-lottery-win-high.gif)
    
    #### Report for the _high, low_; filename _LieHiLo5.WS_
    
        *** WS LIE Report for HIGH / LOW *
        * File: LieHiLo5.REP
        * Analyzed: 2500 of 5000 lines
    
     Line     L / H     L / H     L / H
      no.      ID3       ID4       ID5  
    
        1       0          2         1
        2       0          4         2
        3       1          0         4
        4       2          3         7
        5       2          1         0
        6       1          0        20
    ...**
    
    #### Report for the _odd, even_; filename _LieOdEv5.WS_
    
        *** WS LIE Report for ODD / EVEN *
        * File: LieOdEv5.REP
        * Analyzed: 2500 of 5000 lines
    
     Line     O / E     O / E     O / E
      no.      ID3       ID4       ID5  
    
        1       5          3        56
        2       3          0        75
        3       0         14        80
        4       2          1        12
        5       0         14        25
    ...**
    
    #### Report for the _decades_; filename _LieDecade5.WS_
    
        *** WS LIE Report for DECADES *
        * File: LieDecade5.REP
        * Analyzed: 2500 of 5000 lines
    
     Line    Decades   Decades   Decades
      no.      ID3       ID4       ID5  
    
        1       2         55        21
        2       0          6         2
        3       1         77        72
        4       5          2        32
        5       0          3        99
    ...**
    
    #### Report for the _last digits_; filename _LieLastDigit5.WS_
    
        *** WS LIE Report for LAST DIGITS *
        * File: LieLastDigit5.REP
        * Analyzed: 2500 of 5000 lines
    
     Line    LastDig   LastDig   LastDig
      no.      ID3       ID4       ID5  
    
        1      29        925      4999
        2      43        183      4998
        3      74         14       354
        4     243       4996      4996
        5     196       1545      4208
        6      30       1886      4994
    ...**
    
    As you learnt in Chapter 3, the software has a very useful function to aid the user in finding strategies: _T = SorT Filter Reports by Column_. The module will sort the 8 WS reports by any column. The columns are sorted in **descending** order, so that the **highest** filter values come up at the top.
    
    The main goal is to find high levels for the respective filter(s). The higher the value of a filter, the more combinations eliminated. All lottery strategies usually start with one filter (column) I name the _pivot filter_. We then look for other high values in the columns of the same report. Do NOT use the _ANY_ or _POS_ parameters as pivot filters you saw the reason.
    
    We want to see how often a strategy with those parameters hit in the past. We want to see what other reliable filter values we can employ additionally. There is another valuable function that does exactly that: _C = Strategy Checking of WS Files_. The useful strategy program compiles a report like this one (only a fragment here):
    
    ![The reversed lotto strategy can be checked against past lottery drawings.](https://saliu.com/lie-lotto-strategies-decades.htmlimages/check-lie-strategy.gif)
    
    We can see high filter values in other columns, especially the _last digit_ report _LieLastDigit5.WS_. Furthermore, we can correlate this strategy with other types of strategies in those great pieces of software known as _**Bright**_ and _**Ultimate Lottery Software**_. The functionality is presented in-depth on this e-book: [**Cross-Reference Lottery Strategy Files Created by _Command Prompt Software_ and _MDIEditor Lotto WE_**](cross-lines.html). The function is available from the second menu of the _**Ultimate Lottery Software**_ integrated software: _T = Cross-Checking Strategies_.
    
    ## 6. Real-Life _LIE Elimination_ Lotto Strategy with Jackpot Wins
    
    In the spring of 2014, my website experienced a difficult legal challenge. My entire site was taken down as a result of bogus complaints on the grounds of copyright infringements. Threatening emails sent to me unwarrantedly were considered copyrighted materials as if published Web pages or books! There are plenty of details starting in the **Forums** link in the footer. This type of lottery software was under development during that harsh time. My lottery forums closed for good because of that incident of law abuse. Before the shutdown, however, I had come up with a quick lotto strategy and posted it my forum.
    
    I worked first with _ID3_ for the _Skips_ANY_Min_ filters. I chose a conservative value: **ID3 = 3**; it occurs quite frequently. The program generated an output _LIE_ file of some 50000+ _combosnations_. I fed that _LIE_ file (created by the **ID3 = 3** filter) to **Lexico5** with no other filters enabled. The final output had 61982 lines (amount to play).
    
    I checked for winners in _**Super Utilities**_ for the top 12 lottery drawings (excluded from the _LIE_ processing). There were plenty of winners — as much as making a profit! Evidently, _random play_ was badly beaten; also, the _house edge_ was badly beaten. Here is the fragment that matters.
    
    ![The reversed lotto strategy consistently leads to winners for a profit in any lottery game.](https://saliu.com/lie-lotto-strategies-decades.htmlimages/winners.gif)
    
    I went to the Pennsylvania Lottery Web site to check past jackpot amounts for the 5/43 lotto game.
    
    **01/22/14** Winning Numbers: 05 11 15 39 40
    
    0 players matched 5, jackpot rolls to $325,000.00  
    47 players matched 4, each receiving $302.50  
    1,899 players matched 3, each receiving $12.50  
    25,577 players matched 2, each receiving $1.00
    
    **01/21/14** Winning Numbers: 06 11 12 15 31
    
    0 players matched 5, jackpot rolls to $225,000.00  
    48 players matched 4, each receiving $225.00  
    2,137 players matched 3, each receiving $8.50  
    23,474 players matched 2, each receiving $1.00
    
    **01/20/14** Winning Numbers: 09 19 24 30 42
    
    1 players matched 5, each receiving $1,000,000.00  
    163 players matched 4, each receiving $273.00  
    6,618 players matched 3, each receiving $11.00  
    80,034 players matched 2, each receiving $1.00
    
    Jackpot average: (325,000 + 225,000 + 1,000,000) / 3 = 516,667  
    '4 of 5' average: (302.50 + 225.00 + 273) / 3 = 267  
    '3 of 5' average: (12.50 + 8.50 + 11.00) / 3 = 10.67
    
    I disregarded the _2 of 5_ "fixed" prize of $1... although it totaled over $100,000!
    
    **Total cost: 61982 * 12 = 743,784  
    Total in prizes: ( 3 * 516,667) + (445 * 267) + (11,728 * 10.67) = 1,838,457  
    Net profit: over _1 million_**
    
    I had only expected 1 jackpot hit — the other two _5 of 5_ hits were extras. I don't know exactly how that happens, except that _ID3_ were also 3+ in those two instances. I checked also for other _ID3_s and one _ID4_ (_Low/High_ & _Odd/Even_). The same thing occurs. There are multiple hits in the ranges that act as “future” lottery drawings. A better method would be combination-generating in the traditional manner of **Bright** software; then purge the outcome by enabling _LIE_ files.
    
    - Apply this tactic to a combined lottery strategy that beats the odds badly: [_**Lottery Strategy, Systems Based on Number, Digit Frequency**_](frequency-lottery.html).
        
    
    [](content/lottery.html)
    
    ## [7. Resources in Lottery Lotto Software, Strategies, Systems](content/lottery.html)
    
    - The Main [_**Lotto, Lottery, Software, Strategy**_](LottoWin.htm) Page.  
        Presenting software to create free winning lotto, lottery strategies, systems based on mathematics. Get your lotto systems or wheels, the best lottery, lotto software, combinations, winning numbers.
    - [_**Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies**_](Newsgroups.htm).  
        Read a genuine analysis of Excel spreadsheets applied to lottery and lotto developing of software, systems, and strategies. Combining Excel analysis with powerful lottery and lotto software programmed by this author, _Parpaluck_.
        
    - [_**MDIEditor Lotto WE: Lottery Software Manual, Book, ebook, Help**_](MDI-lotto-guide.html).  
        ~ Also applicable to my entire lottery software; plus Powerball, Mega Millions, Euromillions.
    - [_**Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps, Programs**_](forum/lotto-book.html).
        
    - [**Lottery Mathematics, Lotto Mathematics, Math, Maths**](gambling-lottery-lotto/lottery-math.htm): Probability, Appearance, Repeat, Affinity, Number Affiliation, Lotto Wheels, Lottery Systems, Strategies.
    - Practical [_**Lottery and Lotto Filtering in Software**_](filters.html).
    - [**Skips Lottery, Lotto, Gambling, Systems, Strategy**](skip-strategy.html).
        
    - [_**Lotto, Lottery Strategy in Reverse: Not-to-Win Leads to Not-to-Lose or WIN**_](reverse-strategy.html).
    - [_**LIE Elimination: Lottery, Lotto Strategy in Reverse for Pairs, Number Frequency**_](lie-lottery-strategies-pairs.html).
        
    - [_**Lotto, Lottery Strategy on Sums, Root Sums, Skips, Odd Even, Low High, Deltas**_](strategy.html).
    - [_**Lottery Utility Software**_](lottery-utility.html)_**: Pick-3, 4 Lottery, Lotto-5, 6, Powerball, Mega Millions, Euromillions**_.
        
    - [**Theory, Analysis of _Deltas_ in Lotto, Lottery Software, Strategy, Systems**](delta-lotto-software.html).
    - [_**Lotto Decades, Last Digits, Systems, Strategies, Software**_](decades.html).
    - [_**Markov Chains, Followers, Pairs, Lottery, Lotto, Software**_](markov-chains-lottery.html).
        
    - [_**Lotto Software for Groups of Numbers: Odd, Even, Low, High, Sums, Frequencies, User's Groups**_](lotto-groups.html).
    - [_**The Best Strategy for Lottery, Gambling, Sports Betting, Horse Racing, Blackjack, Roulette**_](strategy-gambling-lottery.html).
    - _"The Start Is the Hardest Part"_ in [_**Lottery Strategies**_](https://forums.saliu.com/lottery-strategies-start.html).
    - [_**Strategy Lotto Software on Positional Frequency**_](https://forums.saliu.com/lotto-software-position-frequency.html).
        
    - Download [**Lottery Software, Lotto Software**](infodown.html).
    
    ![The first real lotto software to apply reversed lottery strategies for winning the jackpot.](https://saliu.com/lie-lotto-strategies-decades.htmlHLINE.gif)
    
    **[Home](index.htm) | [Search](Search.htm) | [New Writings](bbs/index.html) | [Odds, Generator](calculator_generator.html) | [Contents](content/index.html) | [Forums](https://forums.saliu.com/) | [Sitemap](sitemap/index.html)**
    
    ![You read an article on gambling, lottery, mathematics, programming strategy, strategy code.](https://saliu.com/lie-lotto-strategies-decades.htmlHLINE.gif)