---
created: 2025-07-24T21:26:41 (UTC +08:00)
tags: [filters,restrictions,lottery,lotto,formula,probability,software,mathematics,theory probability,system,statistics,drawings,filters,filtering,numbers,winning,combinations,]
source: https://saliu.com/filters.html
author: 
---

# Lotto Filters, Reduction Strategies in Lottery Software

> ## Excerpt
> The first and only lottery software, lotto programs based on mathematics of dynamic filters, filtering, restriction, and elimination reduce huge amounts of lotto combinations from play.

---
[![Lottery Software: Jackpot Lotto, Pick Lotteries, Powerball, Mega Millions, Euromillions, Keno.](https://saliu.com/images/lottery-software-systems.gif)](https://saliu.com/free-lotto-lottery.html)  

## <u>Theory of <i>Filters, Filtering, Reduction</i> in Lotto, Lottery Software</u>  
★ ★ ★ ★ ★

## By <PERSON>, _Founder of Lottery Filter Mathematics_

![Set filters in lottery software, filtering for reduction of combinations in lottery software.](https://saliu.com/HLINE.gif)

![Run the best lottery software, lotto software to win the lottery or lotto, based on filtering.](https://saliu.com/images/lotto.gif)

### I. [Introduction to Lottery Filters and Filtering in Software](https://saliu.com/filters.html#Filters)  
II. [Setting the Lottery Filters: Theory and Software Tools](https://saliu.com/filters.html#Software)  
III. [More Advanced Theories in Setting Filters and Lottery Strategies](https://saliu.com/filters.html#Theories)  
IV. [Older Writings in Setting Filters; Lottery Software Users Creating Strategies](https://saliu.com/filters.html#History)  
V. [Resources in Lotto, Lottery, Software, Filters, Strategies, Systems](https://saliu.com/filters.html#Links)

![Implement filtering to reduce lotto playing tickets with lottery software.](https://saliu.com/images/lotto.gif)

## <u>1. Introduction to Lottery Filters and Filtering in Dedicated Software</u>

## Please study also the complement of this publication: [_**The Best Ever Lottery Strategy, Lotto Strategies**_](https://software.saliu.com/lottery-strategy-lotto-strategies.html).

<big>• SortFilterReports*</big>  
The lotto software sorts the _W\*, MD\*, GR\*, DE\*, FR\*, SK\*_ reports by column. It helps the user see more easily the filters and more easily discovering lottery strategies.

![Column sorting software: program sorts by column lottery reports to discover lotto strategies.](https://saliu.com/ScreenImgs/sort-lottery-reports.gif)

The lottery filters are parameters that eliminate combinations in the generating processes by the LotWon lotto and lottery software. In other words, the filters are _restrictions_. One simple restriction, as an example: _Generate lotto combinations that do not repeat any past drawing that hit the jackpot_.

Ironically, mostly the minimum levels of the lottery filters are perceived as... filters! Actually, the filters in my lotto software had only minimum levels for years. That's how it started. It took me many years to realize that there is no minimum without maximum. Reason: The personal computers in the 1980s were not advanced. And another reason: I evolved as a programmer.

-   **The _minimum_ level allows only lottery combinations _above_ the level. It eliminates everything _within_ the range of combinations.**
-   **The _maximum_ level allows only lottery combinations _below_ the level. It eliminates everything that is _not within_ the range of combinations.**

Both levels of a filter are equally efficient. There are no universal formulas to calculate the efficiency of lottery filters. _By efficiency, we mean the amount of lottery combinations that a filter eliminates._

Let's say the lotto game consists of 6 winning numbers per drawing. We can calculate exactly, by formulas, how many combinations the filter _ONE_ would eliminate and the filter _SIX_ — But only if we set each level to **1**. That is, the calculations can be performed only if we consider **one** past drawing as a filter setting. That's because groups of lottery numbers do repeat — also according to probability theory.

The _minimum level_ of filter _ONE_ (the name is just intuitive) eliminates all single-number groups from past lotto drawings. If we set the minimum level of the _ONE_ filter to **1**, it will eliminate EACH lotto number from the previous draw. There are 6 numbers per combination. Thus, we will eliminate 6 numbers from the field of play (e.g. 49 numbers in the game). Only numbers from the remaining 49 – 6 = 43 lotto numbers will be part of the combinations generated.

Therefore, the _minimum level_ of _ONE_ will allow an amount of combinations equal to the formula of _combinations 43 taken 6 at a time_. C(43, 6) = 6,096,454 combinations. Total number of lotto combinations C(49, 6) = 13,983,816. In other words, the _minimum level_ of _ONE_ will <u>eliminate</u> _7,887,362_ combinations.

The _maximum level_ of _ONE_ does the opposite. If the _maximum level_ of _ONE_ is set to **1**, it eliminates 6,096,454 combinations. Equivalently, it <u>allows</u> _7,887,362_ lotto 6/49 combinations.

The other filter we can apply formulas to is a lotto parameter we could call _SIX_. It will apply only the complete 6-number groups as a restriction. We set the _minimum level_ of _SIX_ to **1** (the previous drawing only). There is only 1 possible 6-number group to match the past draw. This _minimum level_ of _SIX_ will eliminate exactly one lotto combination. The opposite: The _maximum level_ of _SIX_ will allow exactly one combination (i.e. it eliminates the rest of the combinations in the lotto game).

There are no formulas for other types of restrictions or lottery filters. The only way to determine the efficiency of a lottery filter is by running the software in lexicographical generating mode (L in the main menu of **Bright / Ultimate Software**). Set the minimum level of a particular filter to 1. Write down how many lottery combinations were generated. Set the maximum level of a particular filter to 1. The result should be the complementary of the amount of combinations generated by the minimum level. That is, if you add the amounts of lottery combinations generated by the two levels of the same filter, the result should be equal to total number of sets in the lottery game.

You might want to save to a document the efficiency figures for every filter in your particular software packages. Print the document as a good reference to aid you with my lotto and lottery software.

Lotto filters such as _Four, FivS, FivR_ have a special behavior. They are so high, that they reach levels beyond a small data file. If you analyze 10,000 draws, _FivS_ in layer 1 shows 10,000. Most likely the filter is higher than that. It can reach over 20,000, I know it for a fact. In my lotto 6/69 game, both _FivS_, and _FivR_ reached 100,000 easily. That's why the _FivS_, and _FivR_ filters do NOT have maximum levels! The _Four_ filter has a maximum level, but it requires caution. If the filter shows 10,000 in your _WS6.1_ report, it simply means there weren't enough draws to analyze.

## <u>2. Setting the Filters: Theory and Software Tools</u>

As a quick start — set one or very few lotto filters to levels outside their normal ranges. The normal range is determined by the median of the respective lottery software filter. The newest lottery software packages (the **Bright** and especially **Ultimate Software**) make it very easy to work with the median. The median is already calculated for the user. You can see it at the top of each filter (a column in the WS files). For other packages, you need to determine the median yourself.

You can use the WS files and the QEdit editor (it does NOT work in 64-bit Windows). QEdit has some nice features I did not bother to implement in my editors. Why should I reinvent the ... lotto wheel every time?!

One nice feature of QEdit is _column block_ or _column selecting_. Read the manual for the shortcut, it probably is _Alt+k_. Go to the first drawing in the _WS_ file. Move the cursor to the first digit of a filter (column). If a lotto software filter has four digits, make sure the cursor is four spaces from the rightmost digit. The column block must cover all the digits in the filter. Press simultaneously _Alt+K_ then press the right arrow key until you reach the last digit in the column. Press the down arrow key until you reach the last line in the WS file.

-   Axiomatic one, we always evolve, never a doubt about it! Please visit this website ever so often for the freshest information. Especially check out the footer of every Web page of this site.
-   Instead of the 16-bit Qedit, it is strongly recommended the usage of a free 32-bit editor named **Notepad++**. It works great, it is fast, and much easier to use. It is the recommended tool for [_**creating, updating lottery data files of past winning numbers**_](https://forums.saliu.com/lottery-strategies-start.html).
-   I wrote specialized software to automate the process of sorting the filters (or columns, or blocks of text). The program name is **SortFilterReports**. The lotto software sorts the _W5, MD5, GR5, DE5, FE5, SK5_ reports by column, helping the user see more easily the filters — e.g. filters of _wacky_ values. The sorting is done by type of winning reports. The lotto program also offers the correct choices (correct names) for filters to sort. There are now similar programs for the 6-number lotto games, pick 3, 4 lotteries, and horse racing.
-   There is also a general-purpose program (**Sorting**) that can sort columns of numbers in any ASCII (text format) file. **Sorting** is offered as standalone and is also part of the **Bright** and **Ultimate Software** lottery applications.
-   The most advanced lotto software applications have up-to-the-date presentation pages:
-   [_**Download Software, Source Code, Lotto Results, Statistics for Powerball, Mega Millions, Euromillions**_](https://saliu.com/bright-software-code.html).
-   [_**Download the <u>Ultimate</u> Software for Lotto, Pick 3 4 Lotteries, Horse Racing**_](https://saliu.com/ultimate-software-code.html).
-   Here is a very good method of looking for lottery strategies. I created folders for specific games: **Pick3**, **Pick4**, **Lotto5**, **Lotto6**, and **Horses**. Then, in every folder I created a subfolder I named **StratSorRep**. That's where I moved the sorted winning reports _W\*.\*, MD\*.\*, LieId\*.\*_, etc. I generated the winning reports for 1000 drawings so that I can spot a larger number of strategies. I'll use the reports a long time in the future. I might not need to sort the reports again.
-   Doing the winning reports for 1000 (even 2000) drawings makes a real difference. You might not see filter levels that come out very rarely — but they do appear and can make you money. Let's look at my W4 winning reports for pick-4, real data in Pennsylvania Lottery. There is one filter named _Tot_ — it is short for _Total straight sets that hit after N draws_. That N can be very high, even over 50,000 draws. But we are interested here in the lowest values, like 0 or 1:

![The lotto filters can eliminate huge amounts of lottery combinations or tickets, cost of play.](https://saliu.com/images/lotto-filters.gif)

-   Three of the 6 layers will show _1_ if at least 1000 drawings are analyzed. The other three W4 reports will not have a 1 in 1000 lottery drawings, but they will in the future, while the ones that have _Tot = 1_ will not. We will work with the reports that have not seen the _1_ value for the _Tot_ filter.
-   Setting _Tot\_min = 1_ AND _Tot\_MAX = 2_ will always generate 1 pick-4 straight set (combination to play). There will be 3 such sets as we have 3 W4 reports where _Tot = 1_ is missing. Absolutely no additional filters are needed.
-   Even if we were to play the next 1000 lottery draws, the cost would be 3000, while the payout is 5000. In general, however, the wait period could be just 500-600 lottery drawings. In my real-life reports, the latest hit came 40 draws back. Bottom line: we saved already 1000 drawings!
-   Granted, this type of lottery requires a lot of patience (about three years of waiting). The main point is: We can miss moneymaking lottery strategies if we analyze only a few draws (100-200) when we want to hunt for strategies.

Here is a screenshot of the folder organization:

![Create winning reports, sort them and use them for effective lottery strategies.](https://saliu.com/images/strategy-sorted-reports.gif)

There are lots and lots of reports!! The more reports the higher the chance to come up with a large number of efficient strategies. It took me a couple of hours to generate the winning reports, sort all of them on all filters and finally move them to the **StratSorRep** subfolder for each of the **Ultimate Software** packages. I had a couple of glasses of wine to make the tedium less _"oppressive"_! Virtually, the sorted reports will be good forever.

The sorted reports show the lowest filter figures at the top, while the largest column values go to the bottom. The report is ordered by the Any\_5 filter. I had once good success (when I played the lottery in earnest!) I set _Any\_5\_minimum = 400_. Sometimes, the strategy would not generate a single combonation. I mixed and matched _Any_ strategies from various layers in case one strategy was in a "slump". The strategies go _"cold"_ at times, and get _"hot"_ at other times. But them all lottery strategies have _**skip medians**_ that are mathematically consistent.

![Study advanced methods in setting lotto filters to create winning lottery strategies.](https://saliu.com/images/lottery-filter-strategies.gif)

So, when I say _wacky filter values_ I mean the top and the bottom of a sorted winning report for the key column. Of course, we always should set other filter values at runtime. We can see here that when _Any\_5\_minimum = 400_, other filters are usually non-zero (_Tot, Rev, Pr, Syn, Sum_). We can play aggressively and set _Tot = 1000_ as it occurs 14 times with _Any = 400_. And, certainly, we can also combine filters from different layers and from different programs.

We can also work with filter values in the _median_ area of the sorted winning reports WS. In this third type of lottery strategy, we can set _tight_ filter settings. In this particular example, we can see in the _reporty_ heading that the median is equal to 59. We can select _Any\_5\_minimum = 59_ AND _Any\_5\_MAXimum = 60_. This setting might be so tight that no combination will be generated sometimes. We can loosen it up with a setting like: _Any\_5\_minimum = 58_ AND _Any\_5\_MAXimum = 62_

![The lottery filters are founded on theory of probability, mathematical formulas.](https://saliu.com/images/filter-report.gif)

And there you have it, diligently axiomatic colleague of mine. These are the three principal types of lotto strategies based on the levels of one **pivot filter**:-   the **lowest** values: we apply the **MAXimum** level of the filter
-   the **highest** values: we apply the **minimum** level of the filter
-   the **median** values: we apply **both** the **minimum** level **and** the **MAXimum** level of the filter.
    -   The **minimum** **and** the **MAXimum** values can be applied at any level of the filter (e.g. _min_ = 1 AND _MAX_ = 2; we only want values that repeat several times).
-   Every type of game (pick 3, pick-4, lotto-5, lotto 6) has over 300 reports sorted on every filter. As per above, each report can lead to at least 3 lottery strategies. In fact, each game can have 2000 lottery strategies easily. Of course, nobody can devise and play even 1000 strategies… even 500! But the sorted reports make it very easy to come up with lottery strategies based on a single filter.
-   A single-filter strategy needs a few more filters to be set at runtime. No need for many filters, as they overlap one another. For example, you set _Filter\_M_ and _Filter\_N_ at runtime. Yet, the same amount of combinations would be generated by either lottery filter alone; one of them would be superfluous.

## <u>3. More Advanced Theories in Setting Filters and Lottery Strategies</u>

You have now the medians for every lottery software filter. Filters outside the normal ranges could be:

-   **_Median_ multiplied by 3 or 4, even 5;**
-   **_Median_ divided by 3 or 4, even 5.**

For example, if a median is 12, you can set a tight lottery filter to 12 \* 4 = 48 (or rounded up to 50) for the minimum value of the filter. Or, you can set an equally tight filter to 12 / 4 = 3+1 for the minimum value of the respective filter. (Remember, the maximum level of a filter must be at least (minimum level + 1). If a filter is set to 4 times the median, it slashes in half four times the total combinations. In the pick-3 lottery game example: 1000 lottery combinations reduced to 500 in the first step; 500 slashed to 250; 250 halved to 125; finally, 125 reduced to 60+.

A sorted-by-column WS file can show you even more valuable information. Say, you sorted _W3.1_ by the _Pairs-1_ column. The median was 32. The median divided by 4 = 8. Go to line 1 of the column and see how many _Pairs-1_ are lower than 8. You can see also what kind of levels other filters show for _Pairs-1_ less than 8. Other filters may show very low numbers as well. Other lotto filters may show bigger numbers. You can choose as a playing strategy _Max\_Pair\_1=8+1=9_, plus other filters at less tight levels. For example, _Max\_Vr\_1=4_, _Max\_TV\_1=6_, _Val\_1=5_. This is just an example. You can find similar numbers in your sorted WS files.

The median multiplied by 4 = 128. Go to the last line of the column and see how many _Pairs-1_ are larger than 128 (or 120 or 130; you can round up or down for more flexibility in your choices). You can see also what kind of levels other filters show for _Pairs-1_ greater than 128. Other filters may show very high numbers as well. Other filters may show lower numbers. You can choose as a playing lottery strategy _Min\_Pair\_1=130_, plus other filters at less tight levels. For example, _Min \_Vr\_1=1, Min \_TV\_1=5, Min\_Syn\_1=50_.

Using such tight levels for one or very few lotto filters eliminates a huge amount of lotto combinations. Such levels occur more rarely. You should not play them in every drawing. They skip a number of drawings between hits. The newest pick-3 software makes it even easier for you. The application has also a _**strategy checking**_ software utility. It shows the levels of all the filters and the skip chart of the strategy.

Please read also the [_**Pick-3 Lottery Strategy, System, Method, Play, Pairs**_](https://saliu.com/STR30.htm) page. It shows why you should only play a strategy if its current skip (the first number in the skip chart) is less than or equal to the median. For example, if the median of the strategy is 5, you should play it only if the first number in the string of skips is 0, or 1, or 2, or 3, or 4, or 5. If the current skip is larger, don't play the strategy; save the money. Since you can select a very, very large number of lottery strategies, look for another strategy. Look for a strategy that shows a current skip under the median.

### The other path considers a higher probability of a filter level to occur.

Look at the lottery filters, from line one, back to previous drawings. It is evident that the filters go up and down. It is a law.

Again, the pick-3 lottery software makes it easier for you. It shows more evidently the filter movement. When a filter is higher than in the previous drawing, the filter has the + sign at the right. If the filter is lower than in the previous draw, it has a - sign attached. It is more visible. You can notice that in most cases the filters go from one trend to the opposite after two or three drawings. That is, after 2 or 3 + signs, the - sign comes up; or vice versa. Based on that, we can look at each filter (column) in the WS files.

The key position is line #1. If the sign in line #1 is -, and also in line #2, and line #3, (3 decreases in a row), we should expect a + (increase) the very next draw. If the sign in line #1 is +, and also in line #2, and line #3, (3 increases in a row), we should expect a - (decrease) the very next draw. Let's take pick-3 lottery as an example. Pair-1 in line #1 is 12 and it shows -, the 3rd consecutive - (decrease). We should expect a + in the very next drawing. An increase in a filter requires the use of the minimum level of the respective filter. In this example, I'll set _Min\_Pair\_1=13_. If I want to increase the probability, I can set _Min\_Pair\_1=10_, for example. Of course, the program will generate a larger amount of pick-3 straight sets.

Let's say now _Pair-1_ in line #1 is 123 and it shows +, the 3rd consecutive +. We should expect a - in the very next drawing. A decrease in a lottery filter requires the use of the maximum level of the respective filter. In this example, I'll set _Max\_Pair\_1 = 124_. If I want to increase the probability, I can set _Max\_Pair\_1 = 130_, for example. Of course, the program will generate a larger amount of pick3 lottery sets.

You can look for longer streaks, of either + or -. Just go the line #1 in each WS file. There are situations when the current streak can be 4-long, or 5-long, even longer in rare situations. You may want to consider first the longer like-sign streaks. Keep in mind, however, that the streaks shift direction after up to 3 drawings in most cases. Actually, streaks of 1 or 2 consecutive like-signs are the most frequent. I will not go any further in this direction.

You can combine filters selected as in this path with the type of selection presented in path #1. You can set one tight filter (4 times the median, etc.). Then you set other filters as in path #2. For example, _Min\_Pair\_1=120_ (path #1), _Max\_Vr\_1=7_ (path #2), _Min\_TV\_1=10_ (path #1), _Min\_Syn\_1=100_ (path #1), _Max\_Bun\_2=6_ (path #2), _Max\_Tot\_3=1500_ (path #2), _Max\_Any\_5=300_ (path #2). And so on…

-   Axiomatic one, there is an interesting aspect of probability regarding the _consecutive increases/decreases_ of filters. Let's call it the **winning balance**. The probability of an increase (+) or decrease (-) is 50%. The probability of a _trend reverse_ is **90%**, based on the _**Fundamental Formula of Gambling (FFG)**_.
-   What is the best number (amount) of filters to set and still have a chance to a **winning balance**? More specifically, I look at many filters and their levels and select those that show 3 consecutive +/-. How many such filters can I select and have a good level of confidence?
-   We apply two fundamental [_**rules of probability**_: _multiplication_ and _addition_ of _non-mutually exclusive_ _events:_ _**<u>Bayes Theorem</u>**_](https://saliu.com/bayes-theorem.html). If I select 5 filters that show 3 consecutive +/-, the **winning** chance (degree of certainty) is **0.9 ^ 5 = 0.59 (59%)**. The chance to **lose** (by missing just one or more filters) is **0.1 + 0.1 + 0.1 + 0.1 + 0.1 = 0.5 (50%)**. That's the maximum amount of filters I can set and have a **winning balance** (9% in this case). For 6 filters, the balance shifts to the losing position (60% versus 53% or a 7% disadvantage).

![Combining more lottery strategies and playing the aggregate result increase the profits.](https://saliu.com/images/lotto.gif)

-   <u>The best way to apply the <b>lottery strategies</b>:</u> Instead of playing the strategy one at a time (layer by layer), play the strategies for all 6 (or 4) layers <u>at the same time</u>. Let's take as a real-life example the [_**pick-4 lottery super strategy based on digit <u>frequency</u> groups**_](https://saliu.com/frequency-lottery.html).
-   Combine/concatenate the 6 output files in one (e.g. _STR-FR4-BIG_). Do NOT purge the duplicates, as the strategies hit <u>simultaneously</u> sometimes. We slightly increase the cost, but we increase the winnings by thousands of dollars.
-   Generate... generously _**LIE**_ files to further reduce the amount of tickets to play. Some things are almost automatic _**LIE Elimination**_ candidates. The _top half_ of lotto numbers / lottery digits do not hit the very next drawing. The _bottom half_ based on frequency fares even worse. The same about the skip systems: The _FFG-SYS skips_ system files do NOT hit the very next lottery drawing; the _FFG-SYS skips_ systems based on _over median_ fare even worse in the very next draw.
-   If you look at the _WS_ files, you notice that no groups of 5 filters hit exactly in the next drawing. The combinations generated by those filters ought to be added to the final _**LIE**_ file. All filter reports should be analyzed to find groups of filters that will not hit, combined, in the very next lottery drawing.
-   As I have always stated, especially on this website, there is <u>NO absolute certainty in the entire Universe</u>. That's an undeniable mathematical law (_**FFG**_). Therefore, we can experience a wrong _**LIE Elimination**_ result sometimes. Personally, I haven't been wrong more than 1% of the time. The _**degree of certainty DC**_ that a losing _**LIE**_ situation hits at the same time with a hit of the **combined lottery strategy** is around _1/6 \* 1/100 = 1/600_. It is adequately <u>safe</u> in my book.
-   Combining lottery strategies in one, or playing several lottery strategies at the same time, is smoothing out the streaking. The streaks are shorter, AND the _**LIE Elimination**_ tactic is more efficient when applied to a larger bloc of combinations.

![There is a balance of confidence in the number of lottery filters we set in software.](https://saliu.com/images/lotto.gif)

-   Very important! If the lottery commission changes the game format, you must create a new lotto/lottery data file! Do not mix game formats in one data file! For example, Pennsylvania changed from lotto 6/69 to lotto 6/49. I discarded of all previous lotto 6/69 drawings. I renamed the old 6/69 file and preserved it for archiving and researching purposes. I started from scratch a Pennsylvania lotto 6/49 history file. I recreated also the SIMulated data file, specifically for a 6/49 lotto game. The Powerball game changed its format from 49 regular numbers to 53 regular numbers. I proceeded as above. I Changed the Powerball data files to contain only regular lotto numbers from 1 to 53. Please pay great attention to this requirement of **SuperPower**, **LotWon**, and **MDIEditor and Lotto** software. Do not mix various lottery game formats in the same data file — ever!

![Run lotto lottery data files, systems, strategies with filtering, filters in software.](https://saliu.com/HLINE.gif)

## <u>4. Older Writings in Setting Filters; Lottery Software Users Creating Strategies</u>

One user of LotWon creates and analyzes some 40 _D6_ files! Incredible effort, but it makes a lot of sense. He figured out correctly that Ion _Parpaluck_ Saliu created the D\* files and the layers for a good reason or two. Wacky is the operative keyword here. That is, a filter can record values that are way out of range. Way out of range refers to the statistical parameters of the filter: average, standard deviation, and, especially, MEDIAN. Real wacky filter values can go unnoticed for two reasons:

1) the range of analysis is too short (100 lottery drawings or so);  
2) a very low number of past lottery draws to use (way below 100,000).

And one more reason is an insufficient number of layers. The number of layers can be increased only by increasing the number of D\* files.

There is that much-talked-about ION5 filter, in both **MDIEditor and Lotto** and in the LotWon (Command Prompt). The filter is constructed absolutely the same way. That lotto filter can reach the skies, literally. It can grow huge, if the D\* file goes above 100,000 combinations. For a 6/49 lotto game, ION5 can reach TOTAL\_DRAWS/24. (BTW: 24 = INT(49/2)). If no Ion\_5 is above 4,167 in a 100,000-line D6 (MDIEditor file) or a layer in DOS Pick632, then that data file of yours is probably of a sufficient size. For a 5/39 game, ION5 can reach TOTAL\_DRAWS/20. If no Ion\_5 is above 5000 in a 100,000-line D5 (MDIEditor file) or a layer in **Bright5**, then your D\* file is probably of a sufficient size.

Yet, the _wackiness_ can go above those figures. I exemplify by my lotto 5/39 game. I generated all the lotto combinations in the game (575,757) by running that great piece of combinatorial software ****PermuteCombine****. Next I shuffled the all-5-39 combo file by running that great application **Shuffle**. I shuffled once and thus created my first _SIM-5_ file. I shuffled the all-5-39 lotto file a few more times, creating every time a different _SIM-5_ file.

I have seen _Ion\_5_ of 2500+, 5000+, 6000+, 7000+… Those are scary numbers… scary not to you, but to the odds! I did a few tests with min\_Ion\_5 = 2500. Well, for a majority of the cases: there is no output; no combinations generated, even if the inner filters are disabled.

I checked for a situation when _ION5_ was over 2500. I generated lotto combinations in lexicographical order. Total combinations generated: 1 (ONE), both with the innate filters enabled and disabled. You know, I like that! Had I figured it out five years ago, I would have hit the 5/39 lotto jackpot a dozen times or so. Granted, my PC of 5 years ago was kinduva snail (300 MZ PII).

So, now I take a look at _wacky-wacky_ lotto filters, such as the storied Ion-5. If it isn't over 2500 in a layer, look at another layer… or look at another _D\*_ lottery data file. _Wackiness_ is not the exclusivity of one layer or one drawings file. Wackiness repeats itself the same way that history repeats itself.

### <u>Older writings - but still valid</u>

_Posted by Ion Saliu on May 21, 2000._

Indeed, there is the need to present more facts on the filters in LotWon software. The documentation in SuperPower lottery software does not cover how to use the maximum values of the filters.

The _**minimum**_ value of a lottery filter is equivalent to _at least_. If we choose _Two-1_ = 3, we'll be correct if _Two-1_ was at least 3 (shown in the W6 files). That means, 3, 4, 5, ... 10, 11, etc. We would be losing if _Two-1_ was 2, or 1, or 0.

The _**maximum**_ value of a filter is equivalent to _no more than but not equal to_. If we choose _Max\_Two-1_ = 3, we'll be correct if _Two-1_ was no more than 2. Pay special attention to this: the _maximum_ must be at least _the minimum plus 1_. We would be correct if _Two-1_ was 2, or 1, or 0 (shown in the _W6_ files). If _Two-1_ was 3 or more, that would be a losing situation.

If the _W6_ files shows 0 for _Two-1_ we can only play the _maximum_ value for the lottery filters. Playing the _minimum_ value will have no effect (since it is equal to 0). In this case, the correct entry for _Max-Two-1_ is 1 (0+1=1).

The effect of a filter depends on the lotto game format. I will exemplify the effect of some filters for the lotto 6/49 game. It is the most popular around the globe and I received a real-life _W6_ lotto report (from the user named Guy). I will not accept any _W6_ files to look at any more.

-   **The _minimum_ value of a filter ELIMINATES an amount of combinations.  
    **
-   **The _maximum_ value of a filter LEAVES an amount of combinations to be played.**

1) Let's see how many lotto combinations the _minimum_ value of the filter _Two_ eliminates if we set it to 1. A 6-number winning combination can be broken down into _C6 taken 2 at a time_ C(6, 2) = 15 combinations. There are 49-6=43 remaining lotto numbers. The 43 remaining numbers can be broken down into _C43 taken 4 at a time_ = 123410 combinations of 4 lotto numbers each. Each of the 15 2-number lotto combinations can be attached to each of the 123410 4-number combinations. The result is 15 x 123410 = 1851150 total 6-number lotto combinations. Therefore, the filter _Two = 1_ eliminates 1,851,150 combinations.

Let's make _Two_ = 2. It means we eliminate all two-number combinations from the last two drawings. If the two past lottery drawings have no common numbers, the filter _Two_ = 2 eliminates 1,851,150 x 2 combinations. If _Two_ = 3, the filter should eliminate 1,851,150 x 3 combinations. And so on? NOT! In reality, some pairings (two-number lotto groups) are a lot more frequent than others. Some lotto pairings do not come out even within 200 drawings. So, the effect of _Two_ diminishes after two or three past lottery drawings. I also recommend the use of the _Least 6_ file: the file with the least frequent pairings in a lotto game.

If the filter _Three_ is set to 1, it eliminates all three-number groups from the most recent lotto drawing (the previous one). _C(6 taken 3 at a time)_ = 20 combinations. The 43 remaining numbers can be broken down into _C(43 taken 3 at a time)_ = 12341 combinations of 3 numbers each. Each of the 20 _3-number_ lotto combinations can be attached to each of the 12341 3-number combinations. The result is 20 x 12341 = 246820 total 6-number lotto combinations. Therefore, the filter _Three = 1_ eliminates 246,820 combinations.

Let's make _Three_ = 2. It means we eliminate all three-number combinations from the last two drawings. If the two past drawings have no common numbers, the filter _Three_ = 2 eliminates 246,820 x 2 lotto combinations. If _Three_ = 3, the filter should eliminate 246,820 x 3 combinations. And so on? NOT! In reality, some three-number combinations are a lot more frequently than others. The _Three_ filter will diminish its eliminating power after some 10 past lottery drawings.

2) Let's see how many combinations the _maximum_ value of the filter _Two_ LEAVES TO BE PLAYED if we set it to 1. A 6-number winning combinations can be broken down into _C(6 taken 2 at a time)_ = 15 combinations. There are 49-6=43 remaining numbers. The 43 remaining lotto numbers can be broken down into _C(43 taken 4 at a time)_ = 123410 combinations of 4 numbers each. Each of the 15 2-number combinations can be attached to each of the 123410 4-number combinations. The result is 15 x 123410 = 1851150 total 6-number combinations. Therefore, the filter _MAX\_Two_ = 1 LEAVES 1,851,150 combinations to be played. In other words, the lotto software will generate 1,851,150 combinations.

-   That doesn't happen, however, because some <u>number groups overlap</u>. For example, there are combinations that contain the group _12, 13_ and others the group _13, 12_. <u>The combination set is always sorted in ascending order.</u> Thusly, only the combinations with _12, 13_ will pass the filter; those with _13, 12_ will be discarded. That's what creates the discrepancy compared to the norm (formula).

Let's make _MAX\_Two_ = 2. The calculation is different now. Suppose the last two lotto drawings have no common numbers. Thus, the last two drawings consist of 12 unique numbers. _C(12 taken 2 at a time)_ = 66 combinations. There are 49-12=37 remaining numbers. The 37 remaining numbers can be broken down into _C(37 taken 4 at a time)_ = 66045 combinations of 4 numbers each. Each of the 66 _2-number_ combinations can be attached to each of the 66045 _4-number_ combinations. The result is 66 x 66045 = 4358970 total 6-number lotto combinations. Therefore, the filter _MAX\_Two_ = 2 LEAVES 4,358,970 combinations to be played. But the disclaimer above still applies and even widens the discrepancy.

If the filter _Three_ is set to 1, it LEAVES TO BE PLAYED all three number combinations from the most recent lotto drawing (the previous one). _C(6 taken 3 at a time)_ = 20 combinations. The 43 remaining numbers can be broken down into _C(43 taken 3 at a time)_ = 12341 combinations of 3 numbers each. Each of the 20 _3-number_ combinations can be attached to each of the 12341 3-number combinations. The result is 20 x 12341 = 246820 total 6-number lotto combinations. Therefore, the filter _Three_ = 1 LEAVES TO BE PLAYED 246,820 combinations. But beware of the phenomenon described for the case of _Two_.

-   The larger the filter value, the wider the discrepancy from the norms (formulas).
-   Only the filters _One_ and _Six_ (or _Five_ for 5-number lotto) abide by the formulae exactly — and only if the setting is equal to **1**.

-   **The HIGHER the _**minimum**_ value the HIGHER the efficiency of a filter (the MORE combinations it eliminates from play).**
-   **The LOWER the _**maximum**_ value the HIGHER the efficiency of a filter (the FEWER combinations it leaves for playing).**

Looking at the _W6_ files from Guy, I saw _Two_ of 20 or more. That's a high value for the minimum value of the lottery software filters in this category (_Two_ or _2-#s_). Such a high value has corresponding high values for other filters (_Three_ or _3-#s_ and _Four_). Using such high values for some filters will eliminate a huge number of lotto combinations.

In the same reports, I saw _Three_ filters of 150 or more. Such high values for the _minimum_ entry of _Three_ also eliminate a huge amount of lotto combinations. Also, _Four_ of 2000 or more (even 4000) and _Sum_ of 400 or more (even 700) do eliminate millions of combinations.

You will notice that high values for one filter are correlated with high values of other filters. But these high values occur from time to time, not very frequently. You will skip some lotto drawings in between situations of high values for the 'minimum' level of the filters. As in Guy's reports, in drawing #20, the filter _Sum-1_ was 828, then in drawing #13 it was 469. So, we played once setting _Sum-1_ = 400 for the minimum level, then skipped 5 lotto drawings and played again _Sum-1_ = 400. We would have lost 2 drawings, but had another winning situation in drawing 13.

In reverse, efficient _maximum_ levels of the lotto filters are LOWER ones. For the _Two_ filters, 0 is the most efficient value and it occurs quite frequently (more than 15 times in 100 drawings analyzed by Guy). In such cases, you set _MAX\_Two-1_ = 1. There are also situations are 0. For example, you set _MAX\_Two-1 = MAX\_Two-3_ = 1. The effect is quite dramatic: a substantially lower number of combinations will be generated. Or even more dramatically, you can set all six _Two_ filters to no higher than 5. Thus, _MAX\_Two-1 = MAX\_Two-2 = MAX\_Two-3 = MAX\_Two-4 = MAX\_Two-5” = MAX\_Two-6_ = 6.

For the filter _Three_, an efficient level of the _maximum_ would be 5. For the filter _Four_, you can set the _minimum_ = 50 and _MAX\_Four_ = 100.

From all these facts, you can deduce that there are values UNUSUALLY high or UNUSUALLY low that occur from time to time in your W6 files. Therefore, we can set highly efficient filters and expect to win with substantially fewer lotto combinations from time to time.

If you want to generate the winning lotto combinations every time you run WHEEL-6, you need to set SAFE values for the filters. The selection of safe values is not 100% guaranteed, but it is not rocket science either. You should not expect to select the right levels of the filters every time you run the programs.

You can notice in your _W6_ files that usually high values are followed by lower values (or vice versa). Normally, three increases in a row are followed by a decrease (or vice versa: three decreases followed by an increase). For example, in Kulai's report, _Three-1_ was, in three consecutive lottery drawings, 191, 29, 12, followed by 31. I would have set _Three-1_ = 13 and _MAX\_Three-1_ = 51 (an increase from 12 but no higher than 50).

Your accuracy in setting this type of _**safe**_ levels of the lottery filters will increase with usage: the more you work with the _W6_ files, the less erroneous your filter-setting will get.

-   Well, that's a lot on using the filters with Ion Saliu's lottery software. But, wait... wait... There is even more, axiomatic one! When I started this website, I was also involved in the premier lottery newsgroups: _rec.gambling.lottery_. Google groups archived almost everything published in that _Usenet_ group. You can search on queries regarding this author: _Ion Saliu, Parpaluck, saliu systems, saliu software, ion saliu strategies,_ etc.
-   I list here but a few threads I started in recent times –
-   [_**UK 6-59 Lotto: Statistics, Lottery Strategy, Hottest-24 Numbers**_](https://groups.google.com/forum/?hl=en#!topic/rec.gambling.lottery/_4vQk00mxzI).
-   [_**Results Draw #1, World Lottery Championship**_](https://groups.google.com/forum/?hl=en#!topic/rec.gambling.lottery/E-aM_zUsQFY).
-   [_**Filters, Restrictions, Reduction in Lotto, Lottery Software**_](https://groups.google.com/forum/?hl=en#!topic/rec.gambling.lottery/f_I6udi8raQ).
-   [_**Lottery Software, Strategies for Powerball, Mega Millions, Euromillions**_](https://groups.google.com/forum/?hl=en#!topic/rec.gambling.lottery/0P7xKzncan8).
-   Copy and paste copiously to a file meant to be your own <u>manual</u> or <u>textbook</u> on <u>setting filters and creating lottery strategies</u> that are guaranteed to win – mathematically and statistically.

## [<u>Resources in Lottery Software, Lotto Wheeling, Systems, Strategies</u>](https://saliu.com/content/lottery.html)

See a comprehensive directory of the pages and materials on the subject of lottery, lotto, software, systems, lotto wheels.

-   The Main [_**Lotto, Lottery, Software, Strategies**_](https://saliu.com/LottoWin.htm) Page.  
    Presenting software to create free winning lotto, lottery strategies, systems based on mathematics. Get your lotto systems or wheels, the best lottery, lotto software, combinations, winning numbers.
-   [_**Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies**_](https://saliu.com/Newsgroups.htm).
-   [_**<u>MDIEditor Lotto WE</u>: Lottery Software Manual, Book, eBook, Help**_](https://saliu.com/MDI-lotto-guide.html).  
    ~ Also applicable to LotWon lottery, lotto software; plus Powerball, Mega Millions, Euromillions.
-   [_**Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps, Programs**_](https://saliu.com/forum/lotto-book.html).
    
    _**Pages dedicated to help, instructions, filters, strategies for the best lotto programs and lottery software in the world:**_
    
-   [_**Documentation, Help: MDIEditor Lotto WE, Lottery Software, Strategy Tutorial**_](https://saliu.com/mdi_lotto.html).
-   [_**MDI Editor Lotto**_ Is the Best Lotto Lottery Software; You Be Judge](https://saliu.com/bbs/messages/623.html).
-   [_**Filters, Restrictions, Elimination, Reduction in Lotto, Lottery Software**_](https://saliu.com/bbs/messages/42.html).
-   [_**Step-By-Step Guide to Lotto, Lottery Filters in Software**_](https://saliu.com/bbs/messages/569.html).
-   [_**Basic Manual for Lotto Software, Lottery Software**_](https://saliu.com/bbs/messages/818.html).
-   [**Vertical or Positional** _**Filters in Lottery Software**_](https://saliu.com/bbs/messages/838.html).
-   [_**Beginner's Basic Steps to**_ **LotWon** _**Lottery Software, Lotto Software**_](https://saliu.com/bbs/messages/896.html).
-   [**Dynamic** or **Static** _**Filters: Lottery Software, Lotto Analysis, Mathematics**_](https://saliu.com/bbs/messages/919.html).
-   [**Skips Lottery, Lotto, Gambling, Systems, Strategy**](https://saliu.com/skip-strategy.html)
-   [_**Lottery Utility Software**_](https://saliu.com/lottery-utility.html): _**Pick-3, 4 Lottery, Lotto-5, 6, Powerball/Mega Millions/Thunderball, Euromillions**_.
-   [_**Lottery Strategy, Systems Based on Number, Digit Frequency**_](https://saliu.com/frequency-lottery.html)
-   [**Lotto Decades**: _**Software, Reports, Analysis, Strategies**_](https://saliu.com/decades.html)
-   [_**Analysis of Best Ranges for Lotto Number Frequency, Lottery Pairs, Pairings**_](https://saliu.com/lottery-lotto-pairs.html).
-   [_**Software to Generate Lotto Combinations with _Favorite Lottery Numbers in Fixed Positions_**_](https://saliu.com/favorite-lottery-numbers-positions.html)_._
-   _[_**Lotto Software for Groups of Numbers: Odd, Even, Low, High, Sums, Frequencies, User's Groups**_](https://saliu.com/lotto-groups.html)._
-   _[_**Theory, Analysis of**_ **Deltas** _**in Lotto, Lottery Software, Strategy, Systems**_](https://saliu.com/delta-lotto-software.html)._
-   _[_**The Best Strategy for Lottery, Gambling, Sports Betting, Horse Racing, Blackjack, Roulette**_](https://saliu.com/strategy-gambling-lottery.html)._
-   _[_**Pick-3 Lottery Strategy Software, System, Method, Play Pairs Last 100 Draws**_](https://saliu.com/STR30.htm)._
-   __"The Start Is the Hardest Part"_: [_**Play a Lotto Strategy, Lotto Strategies**_](https://forums.saliu.com/lottery-strategies-start.html)._
-   _[_**Lotto, Lottery <u>Strategy in Reverse</u>: Not-to-Win Leads to Not-to-Lose or WIN**_](https://saliu.com/reverse-strategy.html)._
-   _[_**<u>LIE Elimination</u>: Lottery, Lotto Strategy in Reverse for <u>Pairs, Number Frequency</u>**_](https://saliu.com/lie-lottery-strategies-pairs.html)._
-   _[_**<u>LIE Elimination, Reversed Lottery Strategy</u> for Lotto Decades, Last Digits, Odd Even, Low High, Skips**_](https://saliu.com/lie-lotto-strategies-decades.html)._
-   _**Download** [**Lottery Software, Lotto Programs**](https://saliu.com/infodown.html)_.

![Ion Saliu lottery software, lotto software is best to win with mathematics of filtering, filters.](https://saliu.com/HLINE.gif)

**[Home](https://saliu.com/index.htm) | [Search](https://saliu.com/Search.htm) | [New Writings](https://saliu.com/bbs/index.html) | [Fundamental Formula](https://saliu.com/formula.htm) | [Odds, Generator](https://saliu.com/calculator_generator.html) | [Contents](https://saliu.com/content/index.html) | [Forums](https://forums.saliu.com/) | [Sitemap](https://saliu.com/sitemap/index.html)**

![Effective lottery software must apply filters or restrictions to the huge amounts of combinations.](https://saliu.com/HLINE.gif)
