# Comprehensive Pairing Frequency Tests
# 配對頻率綜合測試 - 全面測試配對分析的準確性和性能

using Test
using Dates
using Statistics

# 引入必要的模組
include("../src/types.jl")
include("../src/pairing_engine.jl")
include("../src/filter_engine.jl")
include("test_data_manager.jl")
include("test_configuration.jl")

"""
配對頻率計算準確性測試
測試配對頻率計算的數學準確性
"""
function test_pairing_frequency_accuracy(test_data::Vector{LotteryDraw})::Dict{String, Any}
    @testset "Pairing Frequency Accuracy" begin
        pairing_engine = PairingEngine(test_data)
        results = Dict{String, Any}()
        accuracy_scores = Float64[]
        
        # 測試前 10 個號碼對的配對頻率
        test_pairs = [(1, 2), (1, 3), (2, 3), (5, 10), (10, 15), (15, 20), (20, 25), (25, 30), (30, 35), (35, 39)]
        
        for (num1, num2) in test_pairs
            try
                # 使用配對引擎計算
                engine_frequency = get_pairing_frequency(pairing_engine, num1, num2)
                
                # 直接計算驗證
                direct_frequency = 0
                for draw in test_data
                    if num1 in draw.numbers && num2 in draw.numbers
                        direct_frequency += 1
                    end
                end
                
                # 驗證一致性
                is_consistent = engine_frequency == direct_frequency
                
                if is_consistent
                    push!(accuracy_scores, 1.0)
                else
                    push!(accuracy_scores, 0.0)
                    @warn "配對 ($num1, $num2) 頻率計算不一致: 引擎=$engine_frequency, 直接=$direct_frequency"
                end
                
                # 驗證頻率的合理性
                @test engine_frequency >= 0
                @test engine_frequency <= length(test_data)
                
            catch e
                @warn "測試配對 ($num1, $num2) 時發生錯誤: $e"
                push!(accuracy_scores, 0.0)
            end
        end
        
        # 計算整體準確性
        overall_accuracy = mean(accuracy_scores)
        results["overall_accuracy"] = overall_accuracy
        results["tested_pairs"] = length(test_pairs)
        results["consistent_calculations"] = count(score -> score == 1.0, accuracy_scores)
        
        @test overall_accuracy >= 0.95  # 要求 95% 以上的準確性
        
        println("  ✅ 配對頻率計算準確性: $(round(overall_accuracy * 100, digits=1))%")
        
        return results
    end
end

"""
配對分佈分析測試
測試配對分佈的統計特性
"""
function test_pairing_distribution_analysis(test_data::Vector{LotteryDraw})::Dict{String, Any}
    @testset "Pairing Distribution Analysis" begin
        pairing_engine = PairingEngine(test_data)
        results = Dict{String, Any}()
        
        # 獲取配對分佈
        pairing_distribution = analyze_pairing_distribution(pairing_engine)

        # 驗證分佈的基本特性
        @test isa(pairing_distribution, Dict)
        @test !isempty(pairing_distribution)

        # 從分析結果中提取統計信息
        distribution_stats = Dict(
            "total_pairs" => pairing_distribution["total_pairs"],
            "mean_frequency" => pairing_distribution["mean_frequency"],
            "median_frequency" => pairing_distribution["median_frequency"],
            "std_frequency" => pairing_distribution["std_frequency"],
            "min_frequency" => pairing_distribution["min_frequency"],
            "max_frequency" => pairing_distribution["max_frequency"],
            "zero_frequency_pairs" => get(pairing_distribution, "zero_frequency_pairs", 0)
        )
        
        # 驗證統計的合理性
        @test distribution_stats["mean_frequency"] >= 0
        @test distribution_stats["min_frequency"] >= 0
        @test distribution_stats["max_frequency"] <= length(test_data)
        @test distribution_stats["total_pairs"] <= 741  # 最多 C(39,2) = 741 個配對
        
        results["distribution_stats"] = distribution_stats
        results["sample_pairs"] = collect(keys(pairing_distribution))[1:min(10, total_pairs)]
        
        println("  ✅ 配對分佈分析完成:")
        println("    - 總配對數: $(distribution_stats["total_pairs"])")
        println("    - 平均頻率: $(round(distribution_stats["mean_frequency"], digits=2))")
        println("    - 零頻率配對: $(distribution_stats["zero_frequency_pairs"])")
        
        return results
    end
end

"""
熱門配對識別測試
測試熱門配對的識別和排序
"""
function test_hot_pairs_identification(test_data::Vector{LotteryDraw})::Dict{String, Any}
    @testset "Hot Pairs Identification" begin
        pairing_engine = PairingEngine(test_data)
        results = Dict{String, Any}()
        
        # 獲取排序的配對（按頻率降序）
        sorted_pairings = get_sorted_pairings(pairing_engine)
        hot_pairs = []

        # 取前 10 個作為熱門配對
        for i in 1:min(10, length(sorted_pairings))
            pair, frequency = sorted_pairings[i]
            push!(hot_pairs, Dict("pair" => pair, "frequency" => frequency))
        end
        
        # 驗證熱門配對的特性
        @test isa(hot_pairs, Vector)
        @test length(hot_pairs) <= 10
        
        if !isempty(hot_pairs)
            # 驗證排序（頻率應該遞減）
            for i in 2:length(hot_pairs)
                @test hot_pairs[i-1]["frequency"] >= hot_pairs[i]["frequency"]
            end
            
            # 驗證配對格式
            for pair_info in hot_pairs
                @test haskey(pair_info, "pair")
                @test haskey(pair_info, "frequency")
                @test isa(pair_info["pair"], Tuple{Int, Int})
                @test isa(pair_info["frequency"], Int)
                @test pair_info["frequency"] >= 0
            end
        end
        
        results["hot_pairs"] = hot_pairs
        results["hot_pairs_count"] = length(hot_pairs)
        results["max_frequency"] = isempty(hot_pairs) ? 0 : hot_pairs[1]["frequency"]
        
        println("  ✅ 熱門配對識別完成:")
        println("    - 識別配對數: $(length(hot_pairs))")
        if !isempty(hot_pairs)
            println("    - 最高頻率: $(hot_pairs[1]["frequency"])")
            println("    - 最熱配對: $(hot_pairs[1]["pair"])")
        end
        
        return results
    end
end

"""
冷門配對識別測試
測試冷門配對的識別和分析
"""
function test_cold_pairs_identification(test_data::Vector{LotteryDraw})::Dict{String, Any}
    @testset "Cold Pairs Identification" begin
        pairing_engine = PairingEngine(test_data)
        results = Dict{String, Any}()
        
        # 獲取排序的配對（按頻率降序）
        sorted_pairings = get_sorted_pairings(pairing_engine)
        cold_pairs = []

        # 取後 10 個作為冷門配對（頻率最低的）
        start_idx = max(1, length(sorted_pairings) - 9)
        for i in start_idx:length(sorted_pairings)
            pair, frequency = sorted_pairings[i]
            push!(cold_pairs, Dict("pair" => pair, "frequency" => frequency))
        end

        # 反轉順序，使最冷的在前
        reverse!(cold_pairs)
        
        # 驗證冷門配對的特性
        @test isa(cold_pairs, Vector)
        @test length(cold_pairs) <= 10
        
        if !isempty(cold_pairs)
            # 驗證排序（頻率應該遞增）
            for i in 2:length(cold_pairs)
                @test cold_pairs[i-1]["frequency"] <= cold_pairs[i]["frequency"]
            end
            
            # 驗證配對格式
            for pair_info in cold_pairs
                @test haskey(pair_info, "pair")
                @test haskey(pair_info, "frequency")
                @test isa(pair_info["pair"], Tuple{Int, Int})
                @test isa(pair_info["frequency"], Int)
                @test pair_info["frequency"] >= 0
            end
        end
        
        results["cold_pairs"] = cold_pairs
        results["cold_pairs_count"] = length(cold_pairs)
        results["min_frequency"] = isempty(cold_pairs) ? 0 : cold_pairs[1]["frequency"]
        
        println("  ✅ 冷門配對識別完成:")
        println("    - 識別配對數: $(length(cold_pairs))")
        if !isempty(cold_pairs)
            println("    - 最低頻率: $(cold_pairs[1]["frequency"])")
            println("    - 最冷配對: $(cold_pairs[1]["pair"])")
        end
        
        return results
    end
end

"""
配對引擎性能測試
測試配對引擎的性能表現
"""
function test_pairing_engine_performance(test_data::Vector{LotteryDraw}, iterations::Int = 100)::Dict{String, Any}
    @testset "Pairing Engine Performance" begin
        results = Dict{String, Any}()
        
        # 預熱
        pairing_engine = PairingEngine(test_data)
        for _ in 1:10
            get_pairing_frequency(pairing_engine, 1, 2)
        end
        
        # 測試單個配對查詢性能
        single_query_times = Float64[]
        for _ in 1:iterations
            num1, num2 = rand(1:39), rand(1:39)
            if num1 != num2
                start_time = time()
                get_pairing_frequency(pairing_engine, num1, num2)
                execution_time = (time() - start_time) * 1000  # 轉換為毫秒
                push!(single_query_times, execution_time)
            end
        end
        
        # 測試配對分佈計算性能
        distribution_times = Float64[]
        for _ in 1:10  # 較少迭代，因為計算較重
            start_time = time()
            get_pairing_distribution(pairing_engine)
            execution_time = (time() - start_time) * 1000
            push!(distribution_times, execution_time)
        end
        
        # 測試排序配對查詢性能
        hot_pairs_times = Float64[]
        for _ in 1:20
            start_time = time()
            get_sorted_pairings(pairing_engine)
            execution_time = (time() - start_time) * 1000
            push!(hot_pairs_times, execution_time)
        end
        
        # 計算統計
        results["single_query"] = Dict(
            "mean_ms" => mean(single_query_times),
            "median_ms" => median(single_query_times),
            "max_ms" => maximum(single_query_times),
            "std_ms" => std(single_query_times)
        )
        
        results["distribution_calculation"] = Dict(
            "mean_ms" => mean(distribution_times),
            "median_ms" => median(distribution_times),
            "max_ms" => maximum(distribution_times)
        )
        
        results["hot_pairs_query"] = Dict(
            "mean_ms" => mean(hot_pairs_times),
            "median_ms" => median(hot_pairs_times),
            "max_ms" => maximum(hot_pairs_times)
        )
        
        # 性能要求檢查
        @test mean(single_query_times) < 1.0    # 單個查詢應該小於 1ms
        @test mean(distribution_times) < 100.0  # 分佈計算應該小於 100ms
        @test mean(hot_pairs_times) < 50.0      # 熱門配對查詢應該小於 50ms
        
        println("  ✅ 配對引擎性能:")
        println("    - 單個查詢: $(round(mean(single_query_times), digits=3))ms")
        println("    - 分佈計算: $(round(mean(distribution_times), digits=1))ms")
        println("    - 熱門配對查詢: $(round(mean(hot_pairs_times), digits=1))ms")
        
        return results
    end
end

"""
配對邊界條件測試
測試各種邊界條件下的配對計算
"""
function test_pairing_boundary_conditions()::Dict{String, Any}
    @testset "Pairing Boundary Conditions" begin
        results = Dict{String, Any}()
        boundary_tests = []
        
        # 測試 1：空數據
        empty_data = LotteryDraw[]
        try
            empty_engine = PairingEngine(empty_data)
            empty_freq = get_pairing_frequency(empty_engine, 1, 2)
            @test empty_freq == 0
            push!(boundary_tests, Dict("test" => "empty_data", "status" => "passed"))
        catch e
            @warn "空數據測試失敗: $e"
            push!(boundary_tests, Dict("test" => "empty_data", "status" => "failed"))
        end
        
        # 測試 2：單筆數據
        single_data = [LotteryDraw([1, 2, 3, 4, 5], Date(2022, 1, 1), 1)]
        try
            single_engine = PairingEngine(single_data)
            
            # 存在的配對
            existing_freq = get_pairing_frequency(single_engine, 1, 2)
            @test existing_freq == 1
            
            # 不存在的配對
            non_existing_freq = get_pairing_frequency(single_engine, 1, 6)
            @test non_existing_freq == 0
            
            push!(boundary_tests, Dict("test" => "single_data", "status" => "passed"))
        catch e
            @warn "單筆數據測試失敗: $e"
            push!(boundary_tests, Dict("test" => "single_data", "status" => "failed"))
        end
        
        # 測試 3：相同號碼配對
        test_data = [LotteryDraw([1, 2, 3, 4, 5], Date(2022, 1, 1), 1)]
        try
            test_engine = PairingEngine(test_data)
            same_number_freq = get_pairing_frequency(test_engine, 1, 1)
            # 相同號碼的配對應該返回 0 或拋出異常
            @test same_number_freq == 0
            push!(boundary_tests, Dict("test" => "same_number", "status" => "passed"))
        catch e
            # 拋出異常也是可接受的
            push!(boundary_tests, Dict("test" => "same_number", "status" => "passed"))
        end
        
        # 測試 4：超出範圍的號碼
        try
            test_engine = PairingEngine(test_data)
            # 這應該拋出異常或返回 0
            out_of_range_freq = get_pairing_frequency(test_engine, 0, 40)
            @test out_of_range_freq == 0
            push!(boundary_tests, Dict("test" => "out_of_range", "status" => "passed"))
        catch e
            # 拋出異常是預期的
            push!(boundary_tests, Dict("test" => "out_of_range", "status" => "passed"))
        end
        
        results["boundary_tests"] = boundary_tests
        results["total_boundary_tests"] = length(boundary_tests)
        results["passed_boundary_tests"] = count(test -> test["status"] == "passed", boundary_tests)
        
        println("  ✅ 邊界條件測試: $(results["passed_boundary_tests"])/$(results["total_boundary_tests"]) 通過")
        
        return results
    end
end

"""
執行完整的配對頻率綜合測試
"""
function run_comprehensive_pairing_tests(data_manager::TestDataManager)::Dict{String, Any}
    println("🧪 開始執行配對頻率綜合測試...")
    
    comprehensive_results = Dict{String, Any}()
    
    # 使用中等大小的測試數據
    test_data = get_test_data(data_manager, "medium")
    
    try
        # 執行各項測試
        comprehensive_results["frequency_accuracy"] = test_pairing_frequency_accuracy(test_data)
        comprehensive_results["distribution_analysis"] = test_pairing_distribution_analysis(test_data)
        comprehensive_results["hot_pairs"] = test_hot_pairs_identification(test_data)
        comprehensive_results["cold_pairs"] = test_cold_pairs_identification(test_data)
        comprehensive_results["performance"] = test_pairing_engine_performance(test_data)
        comprehensive_results["boundary_conditions"] = test_pairing_boundary_conditions()
        
        # 計算整體評分
        accuracy_score = comprehensive_results["frequency_accuracy"]["overall_accuracy"]
        boundary_score = comprehensive_results["boundary_conditions"]["passed_boundary_tests"] / 
                        comprehensive_results["boundary_conditions"]["total_boundary_tests"]
        
        # 其他測試的成功率（簡化評分）
        distribution_score = 1.0  # 如果沒有異常就算成功
        hot_pairs_score = 1.0
        cold_pairs_score = 1.0
        
        overall_score = (accuracy_score + boundary_score + distribution_score + hot_pairs_score + cold_pairs_score) / 5
        comprehensive_results["overall_score"] = overall_score
        
        println("\n📊 配對頻率綜合測試結果:")
        println("  - 頻率計算準確性: $(round(accuracy_score * 100, digits=1))%")
        println("  - 邊界條件測試: $(round(boundary_score * 100, digits=1))%")
        println("  - 分佈分析: $(round(distribution_score * 100, digits=1))%")
        println("  - 熱門配對識別: $(round(hot_pairs_score * 100, digits=1))%")
        println("  - 冷門配對識別: $(round(cold_pairs_score * 100, digits=1))%")
        println("  - 整體評分: $(round(overall_score * 100, digits=1))%")
        
        if overall_score >= 0.95
            println("🎉 配對頻率測試：優秀")
        elseif overall_score >= 0.90
            println("✅ 配對頻率測試：良好")
        elseif overall_score >= 0.80
            println("⚠️ 配對頻率測試：需要改進")
        else
            println("❌ 配對頻率測試：需要重大修復")
        end
        
        return comprehensive_results
        
    catch e
        @error "配對頻率綜合測試失敗: $e"
        comprehensive_results["error"] = string(e)
        comprehensive_results["overall_score"] = 0.0  # 設置默認評分
        return comprehensive_results
    end
end

# 導出主要函數
export test_pairing_frequency_accuracy, test_pairing_distribution_analysis
export test_hot_pairs_identification, test_cold_pairs_identification
export test_pairing_engine_performance, test_pairing_boundary_conditions
export run_comprehensive_pairing_tests
