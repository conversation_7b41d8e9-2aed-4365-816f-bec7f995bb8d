---
created: 2025-07-24T22:10:02 (UTC +08:00)
tags: [lotto,lottery,software,experience,history,programs,computer,strategy,strategies,system,systems,win,winning,combinations,formula,mathematics,math,]
source: https://saliu.com/bbs/messages/532.html
author: 
---

# Lottery History, Lotto Software, Systems, Strategies

> ## Excerpt
> Experiences of a great mathematician, author, developer of winning lotto software, lottery software programs, systems, strategies, Ion <PERSON>.

---
**_<PERSON><PERSON><PERSON><PERSON>, Da <PERSON>to Programmer, <PERSON>, Analyst, Strategist_**

[![<PERSON> has won the lottery multiple times since 1980s in Romania and United States.](https://saliu.com/AxiomIon.jpg)](https://saliu.com/membership.html)  

## <u>A Brief History of My Lottery, Lotto Experience with Software, Systems, Strategies</u>  
★ ★ ★ ★ ★

## Is Lottery Rigged or Government-Run Rip-Off?

## By <PERSON>, ★ _Founder of Lottery Mathematics_

![My lottery, lotto experience, history using systems, strategies, software.](https://saliu.com/bbs/messages/HLINE.gif)

Authored on January 17, 2001.

In Reply to: [_**Lottery player thinks the lotteries are rigged**_](https://saliu.com/bbs/messages/528.html) posted by <PERSON> Whittington on January 17, 2001.

• I came to the United States in April of 1985 WEB. I read for the first time my local newspaper a few days after my arrival. I saw then in the _Gettysburg Times_ the results of the Pennsylvania lotto 6/40. Curiously, some winning numbers had personal relevance, such as the street number and the apartment number I had left behind in Romania… Was that an omen?

I started work on a tree nursery and orchard. One day, a Porto Rican gave me some lottery materials and tickets. He said he had a hunch I was a lottery expert! I could not drive at that time and I rode a bicycle to work. The Porto Rican would give me sometimes a ride to the food store. But first we would stop by a lottery agency and buy lotto tickets. We won the very first two tries, _4 out of 6_ (pretty good third prize).

I used the same non-computer system I used in Romania. I worked as an economist in the early 1980s. _Communist economist_ was really an oxymoron. I didn't have serious work to do, or nobody took seriously an economist's job. So, I spent time, at work, developing gambling systems. The soccer pools represented the number one job.

-   Axiomaticule/axiomatic one, you can see the reduced lotto systems (i.e., _wheels_) I used, plus the _strategies_. You can apply them to your play without any restriction:
-   [_**The Best Lotto Wheels for 9, 12, 18, 21 Numbers; Super Strategies Based on Lotto Wheeling**_](https://saliu.com/bbs/best-18-number-lotto-wheels.html).

Secondary, I analyzed the lotto games as well. My system was based on the most frequent pairs, triads and 4-number groups. Two of my colleagues, an economist and an accountant, saw the numbers I came up with. They played the lotto numbers without even asking me to participate. They won serious money, by local standards. I was kind of peed off, as they didn't share a dime with me!

In America, I saw immediately the huge potential of the _mighty_ personal computers of those years. I started with a _Sinclair_ (16 KB!) I moved up to an _Atari 800 XL_ (64 KB of RAM!). Another Porto Rican joined our lotto club. He was born in 1925 and I saw him still working a couple of years ago. He is a songwriter extraordinaire. He inspired my [_**singing in Spanish**_](https://www.youtube.com/watch?v=pg10ONophXY) (it's on YouTube). I put my Atari to work and we won a few more times _4 out of 6_. Then, a day many others remember: February 12, 1986. My lottery programs had to be very short to fit in RAM. The programs were based on my soccer pools system. The Italian soccer resembles the lottery quite a bit. I noticed that patterns such as _X1X1_ tend to repeat, while patterns like _2222_ do not.

My lottery programs would generate lotto combinations of 9, or 12, or 13 numbers each. The programs would eliminate patterns as above. The computer would slowly but steadily run for days and nights. I would stop the program soon after coming back from work, about two hours before the lotto drawing. I selected the combinations at the bottom of the screen. Then I applied some of my lotto wheels.

The Porto Rican who started our lotto venture had a son. And that son had nothing better to do on February 12, 1986 but visit his father! So, my colleague said he did not have time to wait for me to fill out the lotto tickets for that drawing. We decided to play the previous combinations again. Had we played the new lotto combinations, we would have hit a 3-million dollar jackpot! The very last 12-number combination started with the 6 winners! To make it short, we suffered a shock. We were even mocked by our colleagues for weeks to come. Read the presentation of that incredible event: [_**Lotto Strategy, Software: 12-Numbers Combinations in 6-Number Lotto Games**_](https://saliu.com/12-number-lotto-combinations.html).

I realized then, however, how powerful computers were and how capable of amazing things. I took very seriously lottery programming, computer programming in general. I moved up to a PC and compiled BASIC. I won a few more times, playing on my own, or with others. Possibly I am a perfectionist, because I couldn't stop sharpening the programs. Maybe such tendency has stood in my way of playing the lottery more consistently. I don't think I played more than $50 a year in lottery in the past 3–4 years. I believe I am ahead some $2000 playing the lottery. But that's only around $200 a year! I have been also busy with other forms of gambling, where my success comes easier.

-   **_I envision the chain of success this way. The dreams must become goals. The goals must become elements in a plan. Even a perfectionist must sit down and write a plan. Goals not incorporated in a plan are just a pastime._**

My response to Barry Whittington's concern: I believe the lottery drawings are fairly conducted for all intents and purposes. But I remember there was fraud right here, regarding Pennsylvania Lottery. They rigged a couple of lottery drawings. They made a movie about those events, starring John Travolta. I think the movie will start playing soon. (Actually, it was cancelled! Fraud again, anyone — or just government pressure?!) As of the _demographics fix_ Barry implies, it sounds too shocking to me. That would constitute **_conspiracy_** and it would surface in a short time.

-   The traditional lottery is still an _unfair game_, nonetheless. Look at a comparison between a bookie lottery and a traditional lottery (state-run, New York). The bookie lottery reaches a _fairness_ of 87% (in other words, the _house edge_ is 13%). The traditional lottery has 2% _fairness_ for most prizes (i.e. a **_house edge_ of a monstrous 98%**!)
-   [_**Bookie Lotteries: Good Odds, Payouts, Special Software**_](https://saliu.com/bookie-lottery-software.htm).
-   **_Lottery is a government-protected rip-off: gigantic odds, huge house advantage, and unchallenged monopoly._**
-   On the other hand, there is a serious issue with the online betting sites: **TRUST, or lack thereof**. I started warning would-be gamblers about the dangers of gambling online as early as the 2000s: [_**Online Betting Sites Survive Only by Cheating the Gamblers**_](https://saliu.com/bbs/messages/844.html).
-   Read also reviews by real online gamblers regarding one of the largest and most advertised online betting operators in the world, _Bet365_: [_**Customer Service Reviews of Bet365**_](https://www.trustpilot.com/review/www.bet365.com). Most reviewers regard the online gambling giant (also lottery betting provider) as **terrible**!

![Read Ion's lottery, lotto experiences, history using systems, strategies, software.](https://saliu.com/bbs/messages/HLINE.gif)

[

## <u>Resources in Lottery Software, Systems, Lotto Wheeling</u>

](https://saliu.com/content/lottery.html)

-   [_**<u>MDIEditor Lotto WE</u>: Lottery Software Manual, Book, ebook, Help**_](https://saliu.com/MDI-lotto-guide.html).  
    ~ Also applicable to LotWon lottery, lotto software; plus Powerball, Mega Millions, CA SuperLotto, Euromillions.
-   [_**Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps, Programs**_](https://saliu.com/forum/lotto-book.html).
-   _"My kingdom for a good lotto tutorial!"_ [_**Lotto, Lottery Strategy Tutorial**_](https://saliu.com/bbs/messages/818.html).
-   [_**Documentation, Help: MDIEditor Lotto WE, Lottery Software, Strategy Tutorial**_](https://saliu.com/mdi_lotto.html).
-   [_**<u>Lottery Skip System Software</u>**_](https://saliu.com/skip-strategy.html): _**Lotto, Powerball, Mega Millions, Euromillions**_.
-   [_**Lottery Utility Software**_](https://saliu.com/lottery-utility.html): Pick-3, 4 Lottery, Lotto-5, 6, Powerball, Mega Millions, Euromillions.
-   [_**Practical lottery, lotto filtering in software**_](https://saliu.com/filters.html).
-   [_**Artificial Intelligence AI, Axiomatic Intelligence AxI, Neural Networks in Lottery: Strategies, Systems, Software**_](https://saliu.com/neural-networking-lottery.html).
-   [_**Software to Generate Lotto Combinations with Favorite Lottery Numbers in Fixed Positions**_](https://saliu.com/favorite-lottery-numbers-positions.html).
-   [_**Lotto wheels**_](https://saliu.com/lotto_wheels.html) for lotto games drawing 5, 6, or 7 numbers.  
    The most advanced theory of _lotto wheels_ or _reduced lottery systems_. Get also original lotto wheels for lotto games drawing 5, 6, or 7 numbers: Balanced, randomized, and free.
-   These type of lottery strategies keep working across time and space; e.g., year of grace 2021:
-   [_**Winning Lottery Strategy with Frequent Triples and Lotto Wheels**_](https://saliu.com/lotto-triples.html).
-   [_**Lottery, Software, Systems, Science, Mathematics**_](https://saliu.com/lottery.html).
-   Download [**lotto software, lottery software**](https://saliu.com/free-lotto-lottery.html).

**<u>Follow Ups</u>**  

-   [_**Lottery and Powerball rigged: Prize money distributed evenly in all regions**_](https://saliu.com/bbs/messages/534.html) **Barry Whittington** _1/18/2001._
-   [_**Lottery and Gambling Experience: Robbed of Prize Money**_](https://saliu.com/bbs/messages/535.html) **Ion Saliu** _1/18/2001._

![Historical presentation of lottery, lotto experience using systems, software.](https://saliu.com/bbs/messages/HLINE.gif)

**[Home](https://saliu.com/index.htm) | [Search](https://saliu.com/Search.htm) | [New Writings](https://saliu.com/bbs/index.html) | [Software](https://saliu.com/infodown.html) | [Odds, Generator](https://saliu.com/calculator_generator.html) | [Contents](https://saliu.com/content/index.html) | [Forums](https://forums.saliu.com/) | [Sitemap](https://saliu.com/sitemap/index.html)**

![Read the presentation of that lottery strategy, software: 12-number combinations in lotto 6 games.](https://saliu.com/bbs/messages/HLINE.gif)
