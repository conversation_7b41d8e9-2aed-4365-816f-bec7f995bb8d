include("src/WonderGridLotterySystem.jl")
using .WonderGridLotterySystem
using Statistics
using Dates

println("Testing Skip Analysis System")
println("=" ^ 35)

# Load lottery data
fm = FileManager()
data = read_data5_file(fm, "data/fan5.csv")
println("Loaded $(length(data)) lottery draws for skip analysis")

# Create skip analyzer
analyzer = SkipAnalyzer(data)

# Test skip analysis for several numbers
test_numbers = [1, 10, 14, 27, 39]  # Mix of frequent and infrequent numbers

println("\nSkip Analysis Results:")
println("=" ^ 50)

for number in test_numbers
    println("\nNumber $number Analysis:")
    
    # Calculate skips
    skips = calculate_skips(analyzer, number)
    current_skip = get_current_skip(analyzer, number)
    
    # Generate skip chart
    skip_chart = generate_skip_chart(analyzer, number)
    
    println("  Current skip: $(skip_chart.current_skip)")
    println("  FFG median: $(round(skip_chart.ffg_median, digits=2))")
    println("  Favorable timing: $(skip_chart.is_favorable ? "YES" : "NO")")
    println("  Total skip history: $(length(skips)) entries")
    
    if length(skips) > 1
        println("  Skip statistics:")
        println("    Mean skip: $(round(mean(skips), digits=2))")
        println("    Min skip: $(minimum(skips))")
        println("    Max skip: $(maximum(skips))")
        println("    Recent skips: $(join(skips[1:min(10, length(skips))], ", "))")
    end
end

# Find all favorable numbers
println("\n" * "=" ^ 50)
println("Favorable Timing Analysis")
println("=" ^ 50)

favorable_numbers = Int[]
unfavorable_numbers = Int[]

for number in 1:39
    skip_chart = generate_skip_chart(analyzer, number)
    if skip_chart.is_favorable
        push!(favorable_numbers, number)
    else
        push!(unfavorable_numbers, number)
    end
end

println("Numbers with favorable timing ($(length(favorable_numbers)) total):")
println("  $(join(favorable_numbers, ", "))")

println("\nNumbers with unfavorable timing ($(length(unfavorable_numbers)) total):")
println("  $(join(unfavorable_numbers, ", "))")

# Detailed analysis of most and least favorable
if !isempty(favorable_numbers)
    println("\nMost favorable numbers (lowest current skip relative to median):")
    favorable_details = []
    for number in favorable_numbers
        skip_chart = generate_skip_chart(analyzer, number)
        ratio = skip_chart.current_skip / skip_chart.ffg_median
        push!(favorable_details, (number, skip_chart.current_skip, skip_chart.ffg_median, ratio))
    end
    
    sort!(favorable_details, by = x -> x[4])  # Sort by ratio
    
    for i in 1:min(10, length(favorable_details))
        number, current, median, ratio = favorable_details[i]
        println("  $i. Number $number: skip $(current) vs median $(round(median, digits=1)) (ratio: $(round(ratio, digits=2)))")
    end
end

# Performance test
println("\n" * "=" ^ 50)
println("Performance Test")
println("=" ^ 50)

start_time = time()
for number in 1:39
    skips = calculate_skips(analyzer, number)
    skip_chart = generate_skip_chart(analyzer, number)
end
elapsed_time = time() - start_time

println("Analyzed all 39 numbers in $(round(elapsed_time, digits=3)) seconds")
println("Average time per number: $(round(elapsed_time / 39 * 1000, digits=2)) ms")

# Test dynamic updates
println("\nTesting Dynamic Updates:")
new_draw = LotteryDraw([5, 15, 25, 35, 39], Date("2025-07-22"), length(data) + 1)
println("Adding new draw: $(new_draw.numbers)")

# Get skip before update
old_skip_39 = get_current_skip(analyzer, 39)
old_skip_5 = get_current_skip(analyzer, 5)

# Update with new draw
update_skips(analyzer, new_draw)

# Get skip after update
new_skip_39 = get_current_skip(analyzer, 39)
new_skip_5 = get_current_skip(analyzer, 5)

println("Number 39 skip: $old_skip_39 → $new_skip_39 ($(39 in new_draw.numbers ? "appeared" : "increased"))")
println("Number 5 skip: $old_skip_5 → $new_skip_5 ($(5 in new_draw.numbers ? "appeared" : "increased"))")

# Test enhanced skip analysis functions
println("\n" * "=" ^ 50)
println("Enhanced Skip Analysis Functions")
println("=" ^ 50)

# Test with raw data
println("Testing with raw data...")
raw_data = read_data5_raw(fm, "data/fan5.csv")
raw_analyzer = SkipAnalyzer(raw_data)
println("Created analyzer from $(length(raw_data)) raw draws")

# Test skip statistics
println("\nSkip statistics for number 10:")
skip_stats = calculate_skip_statistics(raw_analyzer, 10)
for (key, value) in skip_stats
    println("  $key: $(round(value, digits=2))")
end

# Test favorable numbers
favorable_nums = get_favorable_numbers(raw_analyzer)
println("\nFavorable numbers: $(length(favorable_nums)) total")
println("  $(join(favorable_nums[1:min(15, length(favorable_nums))], ", "))$(length(favorable_nums) > 15 ? "..." : "")")

# Test longest and shortest skips
println("\nLongest current skips:")
longest_skips = find_longest_skips(raw_analyzer, 5)
for (i, (number, skip)) in enumerate(longest_skips)
    println("  $i. Number $number: $skip draws")
end

println("\nShortest current skips:")
shortest_skips = find_shortest_skips(raw_analyzer, 5)
for (i, (number, skip)) in enumerate(shortest_skips)
    println("  $i. Number $number: $skip draws")
end

# Test skip distribution analysis
println("\nSkip distribution analysis:")
skip_dist = analyze_skip_distribution(raw_analyzer)
println("  Total skips analyzed: $(skip_dist["total_skips"])")
println("  Mean skip: $(round(skip_dist["mean_skip"], digits=2))")
println("  Median skip: $(round(skip_dist["median_skip"], digits=2))")
println("  Most common skip: $(skip_dist["most_common_skip"]) (occurred $(skip_dist["most_common_count"]) times)")
println("  Skip range: $(skip_dist["min_skip"]) - $(skip_dist["max_skip"])")

# Test predictions
println("\nTop 10 favorable number predictions:")
predictions = predict_favorable_numbers(raw_analyzer, 10)
for (i, (number, score)) in enumerate(predictions)
    status = score <= 1.0 ? "FAVORABLE" : "UNFAVORABLE"
    println("  $i. Number $number: score $(round(score, digits=3)) ($status)")
end

# Test detailed skip report
println("\nDetailed skip report for number 27:")
report = generate_skip_report(raw_analyzer, 27)
println("  Current skip: $(report["current_skip"])")
println("  FFG median: $(round(report["ffg_median"], digits=2))")
println("  Is favorable: $(report["is_favorable"])")
println("  Current skip percentile: $(round(report["current_skip_percentile"], digits=1))%")
println("  Total occurrences: $(report["total_occurrences"])")
println("  Recommendation: $(report["recommendation"])")

# Test comparison between numbers
println("\nComparing skip patterns for numbers [10, 14, 27]:")
comparison = compare_skip_patterns(raw_analyzer, [10, 14, 27])
for number_str in ["10", "14", "27"]
    data = comparison[number_str]
    println("  Number $number_str:")
    println("    Current skip: $(data["current_skip"])")
    println("    FFG median: $(round(data["ffg_median"], digits=2))")
    println("    Is favorable: $(data["is_favorable"])")
    println("    Mean skip: $(round(data["mean_skip"], digits=2))")
end
println("  Best candidate: Number $(comparison["best_candidate"]) (score: $(round(comparison["best_score"], digits=3)))")

# Test skip summary
println("\nSkip summary for first 5 numbers:")
summary = get_skip_summary(raw_analyzer)
for number in 1:5
    data = summary[number]
    println("  Number $number: skip $(data["current_skip"]), median $(round(data["ffg_median"], digits=1)), $(data["is_favorable"] ? "FAVORABLE" : "unfavorable")")
end