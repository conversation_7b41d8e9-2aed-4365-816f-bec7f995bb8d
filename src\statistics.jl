# Statistical analysis functions

"""
StatisticsEngine for core statistical calculations
"""
struct StatisticsEngine
    precision::Float64
    
    StatisticsEngine() = new(1e-10)
end

"""
Calculate frequency distribution for lottery numbers from LotteryDraw data
"""
function calculate_frequency_distribution(engine::StatisticsEngine, data::Vector{LotteryDraw})::Dict{Int, Int}
    frequency_dist = Dict{Int, Int}()
    
    # Initialize all numbers 1-39 with zero frequency
    for i in 1:39
        frequency_dist[i] = 0
    end
    
    # Count occurrences of each number
    for draw in data
        for number in draw.numbers
            if 1 <= number <= 39
                frequency_dist[number] += 1
            else
                @warn "Invalid number $number found in draw $(draw.draw_id)"
            end
        end
    end
    
    return frequency_dist
end

"""
Calculate frequency distribution for lottery numbers from raw data
"""
function calculate_frequency_distribution(engine::StatisticsEngine, data::Vector{Vector{Int}})::Dict{Int, Int}
    frequency_dist = Dict{Int, Int}()
    
    # Initialize all numbers 1-39 with zero frequency
    for i in 1:39
        frequency_dist[i] = 0
    end
    
    # Count occurrences of each number
    for (draw_idx, draw) in enumerate(data)
        for number in draw
            if 1 <= number <= 39
                frequency_dist[number] += 1
            else
                @warn "Invalid number $number found in draw $draw_idx"
            end
        end
    end
    
    return frequency_dist
end

"""
Compute statistical summary from frequency distribution
"""
function compute_statistical_summary(engine::StatisticsEngine, frequencies::Dict{Int, Int})::StatisticalSummary
    total_draws = sum(values(frequencies)) ÷ 5  # Each draw has 5 numbers
    freq_values = collect(values(frequencies))
    
    mean_freq = mean(freq_values)
    median_freq = median(freq_values)
    std_dev = std(freq_values)
    
    return StatisticalSummary(
        total_draws,
        frequencies,
        mean_freq,
        median_freq,
        std_dev
    )
end

"""
Calculate probability distribution from frequency data
"""
function calculate_probability_distribution(engine::StatisticsEngine, frequencies::Dict{Int, Int})::Dict{Int, Float64}
    total_occurrences = sum(values(frequencies))
    prob_dist = Dict{Int, Float64}()
    
    for (number, freq) in frequencies
        prob_dist[number] = freq / total_occurrences
    end
    
    return prob_dist
end

"""
Perform correlation analysis between two sets of lottery numbers
"""
function calculate_correlation(engine::StatisticsEngine, data1::Vector{Vector{Int}}, data2::Vector{Vector{Int}})::Float64
    if length(data1) != length(data2)
        throw(ArgumentError("Data sets must have the same length"))
    end
    
    # Convert to frequency distributions
    freq1 = calculate_frequency_distribution(engine, data1)
    freq2 = calculate_frequency_distribution(engine, data2)
    
    # Extract frequency values in consistent order
    numbers = sort(collect(keys(freq1)))
    values1 = [freq1[n] for n in numbers]
    values2 = [freq2[n] for n in numbers]
    
    # Calculate Pearson correlation coefficient
    return cor(values1, values2)
end

"""
Calculate chi-square test for randomness
"""
function chi_square_test(engine::StatisticsEngine, frequencies::Dict{Int, Int})::Dict{String, Float64}
    total_occurrences = sum(values(frequencies))
    expected_freq = total_occurrences / 39  # Expected frequency for uniform distribution
    
    chi_square = 0.0
    for freq in values(frequencies)
        chi_square += (freq - expected_freq)^2 / expected_freq
    end
    
    # Degrees of freedom = number of categories - 1
    degrees_of_freedom = 38
    
    # Critical value for 95% confidence (approximate)
    critical_value_95 = 53.384  # Chi-square critical value for df=38, α=0.05
    
    return Dict{String, Float64}(
        "chi_square" => chi_square,
        "degrees_of_freedom" => Float64(degrees_of_freedom),
        "critical_value_95" => critical_value_95,
        "is_random_95" => chi_square <= critical_value_95 ? 1.0 : 0.0
    )
end

"""
Calculate variance and standard deviation for frequency distribution
"""
function calculate_variance_stats(engine::StatisticsEngine, frequencies::Dict{Int, Int})::Dict{String, Float64}
    freq_values = collect(values(frequencies))
    
    variance = var(freq_values)
    std_deviation = std(freq_values)
    coefficient_of_variation = std_deviation / mean(freq_values)
    
    return Dict(
        "variance" => variance,
        "standard_deviation" => std_deviation,
        "coefficient_of_variation" => coefficient_of_variation
    )
end

"""
Calculate percentiles for frequency distribution
"""
function calculate_percentiles(engine::StatisticsEngine, frequencies::Dict{Int, Int})::Dict{String, Float64}
    freq_values = sort(collect(values(frequencies)))
    
    return Dict(
        "min" => minimum(freq_values),
        "q1" => quantile(freq_values, 0.25),
        "median" => quantile(freq_values, 0.50),
        "q3" => quantile(freq_values, 0.75),
        "max" => maximum(freq_values),
        "p10" => quantile(freq_values, 0.10),
        "p90" => quantile(freq_values, 0.90)
    )
end

"""
Identify outliers in frequency distribution using IQR method
"""
function identify_outliers(engine::StatisticsEngine, frequencies::Dict{Int, Int})::Dict{String, Any}
    freq_values = collect(values(frequencies))
    numbers = collect(keys(frequencies))
    
    q1 = quantile(freq_values, 0.25)
    q3 = quantile(freq_values, 0.75)
    iqr = q3 - q1
    
    lower_bound = q1 - 1.5 * iqr
    upper_bound = q3 + 1.5 * iqr
    
    outlier_numbers = Int[]
    outlier_frequencies = Int[]
    
    for (number, freq) in frequencies
        if freq < lower_bound || freq > upper_bound
            push!(outlier_numbers, number)
            push!(outlier_frequencies, freq)
        end
    end
    
    return Dict(
        "outlier_numbers" => outlier_numbers,
        "outlier_frequencies" => outlier_frequencies,
        "lower_bound" => lower_bound,
        "upper_bound" => upper_bound,
        "iqr" => iqr,
        "outlier_count" => length(outlier_numbers)
    )
end

"""
Calculate hot and cold numbers based on frequency
"""
function analyze_hot_cold_numbers(engine::StatisticsEngine, frequencies::Dict{Int, Int}, threshold_percentile::Float64 = 0.8)::Dict{String, Any}
    freq_values = collect(values(frequencies))
    hot_threshold = quantile(freq_values, threshold_percentile)
    cold_threshold = quantile(freq_values, 1.0 - threshold_percentile)
    
    hot_numbers = Int[]
    cold_numbers = Int[]
    normal_numbers = Int[]
    
    for (number, freq) in frequencies
        if freq >= hot_threshold
            push!(hot_numbers, number)
        elseif freq <= cold_threshold
            push!(cold_numbers, number)
        else
            push!(normal_numbers, number)
        end
    end
    
    return Dict{String, Any}(
        "hot_numbers" => sort(hot_numbers),
        "cold_numbers" => sort(cold_numbers),
        "normal_numbers" => sort(normal_numbers),
        "hot_threshold" => hot_threshold,
        "cold_threshold" => cold_threshold
    )
end

"""
Calculate moving averages for frequency trends
"""
function calculate_moving_averages(engine::StatisticsEngine, data::Vector{Vector{Int}}, window_size::Int = 100)::Dict{Int, Vector{Float64}}
    if length(data) < window_size
        throw(ArgumentError("Data length must be at least window_size"))
    end
    
    moving_averages = Dict{Int, Vector{Float64}}()
    
    # Initialize for each number
    for i in 1:39
        moving_averages[i] = Float64[]
    end
    
    # Calculate moving averages
    for start_idx in 1:(length(data) - window_size + 1)
        window_data = data[start_idx:(start_idx + window_size - 1)]
        window_freq = calculate_frequency_distribution(engine, window_data)
        
        for number in 1:39
            avg_freq = window_freq[number] / window_size
            push!(moving_averages[number], avg_freq)
        end
    end
    
    return moving_averages
end

"""
Perform statistical tests for lottery data quality
"""
function perform_data_quality_tests(engine::StatisticsEngine, data::Vector{Vector{Int}})::Dict{String, Any}
    frequencies = calculate_frequency_distribution(engine, data)
    
    # Basic statistics
    summary = compute_statistical_summary(engine, frequencies)
    
    # Chi-square test for randomness
    chi_square_result = chi_square_test(engine, frequencies)
    
    # Variance analysis
    variance_stats = calculate_variance_stats(engine, frequencies)
    
    # Outlier analysis
    outliers = identify_outliers(engine, frequencies)
    
    # Hot/cold analysis
    hot_cold = analyze_hot_cold_numbers(engine, frequencies)
    
    return Dict(
        "total_draws" => summary.total_draws,
        "mean_frequency" => summary.mean_frequency,
        "median_frequency" => summary.median_frequency,
        "standard_deviation" => summary.standard_deviation,
        "chi_square_statistic" => chi_square_result["chi_square"],
        "is_random_distribution" => chi_square_result["is_random_95"] == 1.0,
        "coefficient_of_variation" => variance_stats["coefficient_of_variation"],
        "outlier_count" => outliers["outlier_count"],
        "hot_numbers_count" => length(hot_cold["hot_numbers"]),
        "cold_numbers_count" => length(hot_cold["cold_numbers"]),
        "data_quality_score" => calculate_data_quality_score(chi_square_result, variance_stats, outliers)
    )
end

"""
Calculate overall data quality score (0-100)
"""
function calculate_data_quality_score(chi_square_result::Dict{String, Float64}, variance_stats::Dict{String, Float64}, outliers::Dict{String, Any})::Float64
    score = 100.0
    
    # Penalize for non-random distribution
    if chi_square_result["is_random_95"] == 0.0
        score -= 20.0
    end
    
    # Penalize for high coefficient of variation
    cv = variance_stats["coefficient_of_variation"]
    if cv > 0.3
        score -= 15.0
    elseif cv > 0.2
        score -= 10.0
    end
    
    # Penalize for outliers
    outlier_count = outliers["outlier_count"]
    if outlier_count > 5
        score -= 20.0
    elseif outlier_count > 2
        score -= 10.0
    end
    
    return max(0.0, score)
end