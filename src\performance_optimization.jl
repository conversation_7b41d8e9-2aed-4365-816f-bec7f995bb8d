# Performance optimization module for Wonder Grid Lottery System

using DataStructures

"""
Optimized data structures and caching system for Wonder Grid
"""

# Global cache for frequently accessed calculations
const CALCULATION_CACHE = LRU{String, Any}(1000)  # LRU cache with 1000 entries
const PAIRING_FREQUENCY_CACHE = Dict{Tuple{Int, Int}, Int}()
const FFG_CACHE = Dict{Int, Vector{Int}}()

"""
Memory-efficient combination storage
"""
struct OptimizedCombination
    numbers::NTuple{5, UInt8}  # Use UInt8 since lottery numbers are 1-39
    
    function OptimizedCombination(numbers::Vector{Int})
        if length(numbers) != 5 || any(n -> n < 1 || n > 39, numbers)
            throw(ArgumentError("Invalid combination: must be 5 numbers between 1-39"))
        end
        sorted_numbers = sort(numbers)
        new(NTuple{5, UInt8}(sorted_numbers))
    end
end

# Convert back to regular vector when needed
Base.convert(::Type{Vector{Int}}, combo::OptimizedCombination) = [Int(n) for n in combo.numbers]

"""
Optimized pairing frequency calculator with caching
"""
mutable struct OptimizedPairingCalculator
    frequency_matrix::Matrix{Int}
    total_pairs::Int
    cache_hits::Int
    cache_misses::Int
    
    function OptimizedPairingCalculator()
        new(zeros(Int, 39, 39), 0, 0, 0)
    end
end

"""
Add combination to pairing frequency with optimized memory access
"""
function add_combination!(calc::OptimizedPairingCalculator, combination::Vector{Int})
    # Pre-sort to ensure consistent ordering
    sorted_combo = sort(combination)
    
    # Use optimized nested loop with minimal memory allocations
    @inbounds for i in 1:4
        num1 = sorted_combo[i]
        for j in (i+1):5
            num2 = sorted_combo[j]
            calc.frequency_matrix[num1, num2] += 1
            calc.total_pairs += 1
        end
    end
end

"""
Get pairing frequency with caching
"""
function get_pairing_frequency(calc::OptimizedPairingCalculator, num1::Int, num2::Int)::Int
    # Ensure consistent ordering
    if num1 > num2
        num1, num2 = num2, num1
    end
    
    # Check cache first
    cache_key = (num1, num2)
    if haskey(PAIRING_FREQUENCY_CACHE, cache_key)
        calc.cache_hits += 1
        return PAIRING_FREQUENCY_CACHE[cache_key]
    end
    
    # Calculate and cache
    calc.cache_misses += 1
    frequency = calc.frequency_matrix[num1, num2]
    PAIRING_FREQUENCY_CACHE[cache_key] = frequency
    
    return frequency
end

"""
Optimized FFG calculation with memoization
"""
function calculate_ffg_optimized(key_number::Int)::Vector{Int}
    # Check cache first
    if haskey(FFG_CACHE, key_number)
        return FFG_CACHE[key_number]
    end
    
    # Calculate FFG using optimized algorithm
    ffg_numbers = Vector{Int}()
    ffg_numbers = calculate_ffg_core(key_number)
    
    # Cache result
    FFG_CACHE[key_number] = ffg_numbers
    
    return ffg_numbers
end

"""
Core FFG calculation with optimized arithmetic
"""
function calculate_ffg_core(key_number::Int)::Vector{Int}
    ffg_numbers = Vector{Int}()
    sizehint!(ffg_numbers, 20)  # Pre-allocate expected size
    
    # Optimized calculation using bit operations where possible
    current = key_number
    
    while length(ffg_numbers) < 39  # Safety limit
        # Use optimized modular arithmetic
        next_num = (current * key_number) % 40
        if next_num == 0
            next_num = 40
        end
        
        # Convert to valid lottery range
        if next_num > 39
            next_num = next_num - 39
        end
        
        # Check for cycle completion
        if next_num == key_number
            break
        end
        
        # Add unique numbers only
        if !(next_num in ffg_numbers)
            push!(ffg_numbers, next_num)
        end
        
        current = next_num
    end
    
    return sort(ffg_numbers)
end

"""
Memory-efficient combination generator with streaming
"""
struct OptimizedCombinationGenerator
    key_number::Int
    ffg_numbers::Vector{Int}
    batch_size::Int
    
    function OptimizedCombinationGenerator(key_number::Int, batch_size::Int = 1000)
        ffg_numbers = calculate_ffg_optimized(key_number)
        new(key_number, ffg_numbers, batch_size)
    end
end

"""
Generate combinations in batches to reduce memory usage
"""
function generate_combinations_batch(generator::OptimizedCombinationGenerator)
    Channel{Vector{OptimizedCombination}}() do channel
        ffg_count = length(generator.ffg_numbers)
        
        if ffg_count < 5
            # Not enough numbers for combinations
            put!(channel, OptimizedCombination[])
            return
        end
        
        batch = Vector{OptimizedCombination}()
        sizehint!(batch, generator.batch_size)
        
        # Generate combinations using optimized algorithm
        for combo_indices in combinations(1:ffg_count, 5)
            combination_numbers = [generator.ffg_numbers[i] for i in combo_indices]
            push!(batch, OptimizedCombination(combination_numbers))
            
            # Send batch when full
            if length(batch) >= generator.batch_size
                put!(channel, copy(batch))
                empty!(batch)
            end
        end
        
        # Send remaining combinations
        if !isempty(batch)
            put!(channel, batch)
        end
    end
end

"""
Optimized combination iterator using combinations function
"""
function combinations(arr::AbstractVector, k::Int)
    n = length(arr)
    if k > n || k < 0
        return []
    end
    
    result = Vector{Vector{Int}}()
    indices = collect(1:k)
    
    while true
        push!(result, [arr[i] for i in indices])
        
        # Find rightmost index that can be incremented
        i = k
        while i > 0 && indices[i] == n - k + i
            i -= 1
        end
        
        if i == 0
            break
        end
        
        # Increment and adjust subsequent indices
        indices[i] += 1
        for j in (i+1):k
            indices[j] = indices[j-1] + 1
        end
    end
    
    return result
end

"""
Optimized data structure for historical lottery data
"""
struct OptimizedLotteryData
    draws::Vector{NTuple{5, UInt8}}
    dates::Vector{Date}
    number_frequency::Vector{Int}  # Frequency of each number 1-39
    
    function OptimizedLotteryData(draws::Vector{LotteryDraw})
        optimized_draws = [NTuple{5, UInt8}(sort(draw.numbers)) for draw in draws]
        dates = [draw.draw_date for draw in draws]
        
        # Calculate number frequencies efficiently
        frequency = zeros(Int, 39)
        for draw in optimized_draws
            for num in draw
                frequency[num] += 1
            end
        end
        
        new(optimized_draws, dates, frequency)
    end
end

"""
Fast lookup for number frequency
"""
function get_number_frequency(data::OptimizedLotteryData, number::Int)::Int
    if 1 <= number <= 39
        return data.number_frequency[number]
    end
    return 0
end

"""
Memory pool for reusing objects
"""
mutable struct MemoryPool{T}
    pool::Vector{T}
    create_func::Function
    reset_func::Function
    
    function MemoryPool{T}(create_func::Function, reset_func::Function = x -> nothing) where T
        new{T}(T[], create_func, reset_func)
    end
end

"""
Get object from pool or create new one
"""
function acquire!(pool::MemoryPool{T})::T where T
    if isempty(pool.pool)
        return pool.create_func()
    else
        obj = pop!(pool.pool)
        pool.reset_func(obj)
        return obj
    end
end

"""
Return object to pool
"""
function release!(pool::MemoryPool{T}, obj::T) where T
    push!(pool.pool, obj)
end

"""
Performance monitoring structure
"""
mutable struct PerformanceMonitor
    start_time::Float64
    memory_start::Int
    operation_counts::Dict{String, Int}
    timing_data::Dict{String, Vector{Float64}}
    
    function PerformanceMonitor()
        new(time(), Base.gc_bytes(), Dict{String, Int}(), Dict{String, Vector{Float64}}())
    end
end

"""
Start timing an operation
"""
function start_operation!(monitor::PerformanceMonitor, operation::String)
    if !haskey(monitor.timing_data, operation)
        monitor.timing_data[operation] = Float64[]
        monitor.operation_counts[operation] = 0
    end
    
    return time()
end

"""
End timing an operation
"""
function end_operation!(monitor::PerformanceMonitor, operation::String, start_time::Float64)
    elapsed = time() - start_time
    push!(monitor.timing_data[operation], elapsed)
    monitor.operation_counts[operation] += 1
end

"""
Get performance statistics
"""
function get_performance_stats(monitor::PerformanceMonitor)::Dict{String, Any}
    total_time = time() - monitor.start_time
    memory_used = Base.gc_bytes() - monitor.memory_start
    
    stats = Dict{String, Any}(
        "total_runtime" => total_time,
        "memory_used_bytes" => memory_used,
        "memory_used_mb" => memory_used / (1024 * 1024),
        "operations" => Dict{String, Any}()
    )
    
    for (operation, times) in monitor.timing_data
        if !isempty(times)
            stats["operations"][operation] = Dict{String, Any}(
                "count" => monitor.operation_counts[operation],
                "total_time" => sum(times),
                "average_time" => sum(times) / length(times),
                "min_time" => minimum(times),
                "max_time" => maximum(times)
            )
        end
    end
    
    return stats
end

"""
Cache management utilities
"""
function clear_all_caches!()
    empty!(CALCULATION_CACHE)
    empty!(PAIRING_FREQUENCY_CACHE)
    empty!(FFG_CACHE)
    GC.gc()  # Force garbage collection
end

"""
Get cache statistics
"""
function get_cache_stats()::Dict{String, Any}
    return Dict{String, Any}(
        "calculation_cache_size" => length(CALCULATION_CACHE),
        "calculation_cache_capacity" => CALCULATION_CACHE.maxsize,
        "pairing_frequency_cache_size" => length(PAIRING_FREQUENCY_CACHE),
        "ffg_cache_size" => length(FFG_CACHE),
        "total_cached_items" => length(CALCULATION_CACHE) + length(PAIRING_FREQUENCY_CACHE) + length(FFG_CACHE)
    )
end

"""
Optimized Wonder Grid engine with performance enhancements
"""
struct OptimizedWonderGridEngine
    base_engine::WonderGridEngine
    pairing_calculator::OptimizedPairingCalculator
    performance_monitor::PerformanceMonitor
    memory_pools::Dict{String, Any}
    
    function OptimizedWonderGridEngine()
        base_engine = WonderGridEngine()
        pairing_calculator = OptimizedPairingCalculator()
        performance_monitor = PerformanceMonitor()
        
        # Initialize memory pools
        memory_pools = Dict{String, Any}(
            "combinations" => MemoryPool{Vector{Int}}(() -> Vector{Int}(undef, 5), empty!),
            "temp_arrays" => MemoryPool{Vector{Int}}(() -> Vector{Int}(), empty!)
        )
        
        new(base_engine, pairing_calculator, performance_monitor, memory_pools)
    end
end

"""
Generate combinations with optimized performance
"""
function generate_combinations_optimized(engine::OptimizedWonderGridEngine, key_number::Int)::Vector{Vector{Int}}
    start_time = start_operation!(engine.performance_monitor, "combination_generation")
    
    try
        # Use cached FFG calculation
        ffg_numbers = calculate_ffg_optimized(key_number)
        
        if length(ffg_numbers) < 5
            return Vector{Vector{Int}}()
        end
        
        # Generate combinations using optimized algorithm
        combinations_list = Vector{Vector{Int}}()
        
        # Pre-allocate based on combinatorial calculation
        expected_count = binomial(length(ffg_numbers), 5)
        sizehint!(combinations_list, expected_count)
        
        # Use optimized combination generation
        for combo_indices in combinations(1:length(ffg_numbers), 5)
            combination = [ffg_numbers[i] for i in combo_indices]
            push!(combinations_list, combination)
        end
        
        return combinations_list
        
    finally
        end_operation!(engine.performance_monitor, "combination_generation", start_time)
    end
end

"""
Optimized backtesting with memory efficiency
"""
function run_backtest_optimized(engine::OptimizedWonderGridEngine, 
                               combinations::Vector{Vector{Int}}, 
                               test_draws::Vector{LotteryDraw})::BacktestResult
    start_time = start_operation!(engine.performance_monitor, "backtesting")
    
    try
        # Convert to optimized data structures
        optimized_combinations = [OptimizedCombination(combo) for combo in combinations]
        optimized_draws = OptimizedLotteryData(test_draws)
        
        # Initialize counters
        hit_counts = Dict("3/5" => 0, "4/5" => 0, "5/5" => 0)
        total_tests = length(optimized_combinations) * length(optimized_draws.draws)
        
        # Optimized matching algorithm
        for opt_combo in optimized_combinations
            combo_numbers = Set(opt_combo.numbers)
            
            for draw_numbers in optimized_draws.draws
                draw_set = Set(draw_numbers)
                matches = length(intersect(combo_numbers, draw_set))
                
                if matches >= 3
                    hit_counts["$(matches)/5"] += 1
                end
            end
        end
        
        # Calculate hit rates
        hit_rates = Dict{String, Float64}()
        for (tier, count) in hit_counts
            hit_rates[tier] = total_tests > 0 ? count / total_tests : 0.0
        end
        
        # Calculate efficiency ratios (simplified for performance)
        efficiency_ratios = Dict{String, Float64}()
        for tier in ["3/5", "4/5", "5/5"]
            efficiency_ratios[tier] = hit_rates[tier] > 0 ? hit_rates[tier] * 100 : 0.0
        end
        
        # Create cost analysis
        cost_analysis = CostAnalysis(
            length(combinations),
            1.0,  # Cost per combination
            Float64(length(combinations))
        )
        
        return BacktestResult(hit_rates, efficiency_ratios, cost_analysis)
        
    finally
        end_operation!(engine.performance_monitor, "backtesting", start_time)
    end
end

"""
Benchmark different approaches for performance comparison
"""
function benchmark_performance(key_number::Int, num_iterations::Int = 100)::Dict{String, Any}
    println("🔬 Running performance benchmark...")
    
    # Standard engine benchmark
    standard_engine = WonderGridEngine()
    standard_times = Float64[]
    
    for i in 1:num_iterations
        start_time = time()
        combinations = generate_combinations(standard_engine, key_number)
        elapsed = time() - start_time
        push!(standard_times, elapsed)
    end
    
    # Optimized engine benchmark
    optimized_engine = OptimizedWonderGridEngine()
    optimized_times = Float64[]
    
    for i in 1:num_iterations
        start_time = time()
        combinations = generate_combinations_optimized(optimized_engine, key_number)
        elapsed = time() - start_time
        push!(optimized_times, elapsed)
    end
    
    # Calculate statistics
    standard_avg = sum(standard_times) / length(standard_times)
    optimized_avg = sum(optimized_times) / length(optimized_times)
    improvement = (standard_avg - optimized_avg) / standard_avg * 100
    
    return Dict{String, Any}(
        "standard_engine" => Dict{String, Any}(
            "average_time" => standard_avg,
            "min_time" => minimum(standard_times),
            "max_time" => maximum(standard_times),
            "total_time" => sum(standard_times)
        ),
        "optimized_engine" => Dict{String, Any}(
            "average_time" => optimized_avg,
            "min_time" => minimum(optimized_times),
            "max_time" => maximum(optimized_times),
            "total_time" => sum(optimized_times)
        ),
        "improvement_percentage" => improvement,
        "speedup_factor" => standard_avg / optimized_avg,
        "iterations" => num_iterations,
        "cache_stats" => get_cache_stats()
    )
end

"""
Memory usage profiler
"""
function profile_memory_usage(operation::Function, args...)::Dict{String, Any}
    # Force garbage collection before measurement
    GC.gc()
    memory_before = Base.gc_bytes()
    
    # Run operation
    start_time = time()
    result = operation(args...)
    elapsed_time = time() - start_time
    
    # Measure memory after
    GC.gc()
    memory_after = Base.gc_bytes()
    memory_used = memory_after - memory_before
    
    return Dict{String, Any}(
        "memory_used_bytes" => memory_used,
        "memory_used_mb" => memory_used / (1024 * 1024),
        "execution_time" => elapsed_time,
        "result_size" => isa(result, Vector) ? length(result) : 1,
        "memory_per_item" => isa(result, Vector) && !isempty(result) ? memory_used / length(result) : memory_used
    )
end