# FFG Median Calculation Verification Test
# Tests FFG median calculation accuracy, DC value handling, and probability calculations

using Test
using Dates
using Statistics

# Include necessary types and functions
include("src/types.jl")
include("src/ffg_calculator.jl")

"""
Test theoretical FFG median calculation for Lotto 5/39
"""
function test_theoretical_ffg_median()
    println("=== Theoretical FFG Median Verification ===")
    
    calc = FFGCalculator()
    theoretical_median = calculate_theoretical_ffg_median(calc)
    
    # Manual calculation for verification
    # For Lotto 5/39: p = 5/39, DC = 0.5 (50%)
    p = 5.0 / 39.0
    dc = calc.degree_of_certainty
    expected_median = log(1 - dc) / log(1 - p)
    
    println("Probability of number appearing (p): $(round(p, digits=4))")
    println("Calculated theoretical median: $(round(theoretical_median, digits=2))")
    println("Expected median (manual): $(round(expected_median, digits=2))")
    
    @test abs(theoretical_median - expected_median) < 0.001
    println("✓ Theoretical FFG median calculation is correct")
    
    return theoretical_median
end

"""
Test different degree of certainty values
"""
function test_degree_of_certainty_values()
    println("\n=== Degree of Certainty Verification ===")
    
    # Test different DC values
    dc_values = [0.25, 0.5, 0.75, 0.9]
    p = 5.0 / 39.0
    
    for dc in dc_values
        calc = FFGCalculator(dc)
        theoretical_median = calculate_theoretical_ffg_median(calc)
        
        # Manual calculation for this DC
        expected_median = log(1 - dc) / log(1 - p)
        
        println("DC $(Int(dc*100))%: Calculated = $(round(theoretical_median, digits=2)), Expected = $(round(expected_median, digits=2))")
        
        @test abs(theoretical_median - expected_median) < 0.001
    end
    
    println("✓ All degree of certainty calculations are correct")
end

"""
Test FFG median with historical data
"""
function test_ffg_median_with_data()
    println("\n=== FFG Median with Historical Data Verification ===")
    
    # Create test data with known skip patterns
    test_draws = [
        LotteryDraw([1, 5, 10, 15, 20], Date(2022, 1, 1), 1),
        LotteryDraw([2, 6, 11, 16, 21], Date(2022, 1, 2), 2),
        LotteryDraw([1, 7, 12, 17, 22], Date(2022, 1, 3), 3),  # skip = 2
        LotteryDraw([3, 8, 13, 18, 23], Date(2022, 1, 4), 4),
        LotteryDraw([4, 9, 14, 19, 24], Date(2022, 1, 5), 5),
        LotteryDraw([1, 10, 15, 20, 25], Date(2022, 1, 6), 6), # skip = 3
        LotteryDraw([5, 11, 16, 21, 26], Date(2022, 1, 7), 7),
        LotteryDraw([6, 12, 17, 22, 27], Date(2022, 1, 8), 8),
        LotteryDraw([7, 13, 18, 23, 28], Date(2022, 1, 9), 9),
        LotteryDraw([1, 14, 19, 24, 29], Date(2022, 1, 10), 10), # skip = 4
    ]
    
    calc = FFGCalculator()
    ffg_median = calculate_ffg_median(calc, 1, test_draws)
    
    # Expected skips for number 1: [4, 3, 2] (SkipSystem method)
    expected_empirical_median = median([4, 3, 2])  # = 3.0
    theoretical_median = calculate_theoretical_ffg_median(calc)
    
    println("Number 1 skips: [4, 3, 2]")
    println("Empirical median: $expected_empirical_median")
    println("Theoretical median: $(round(theoretical_median, digits=2))")
    println("Calculated FFG median: $(round(ffg_median, digits=2))")
    
    # The calculated median should be a blend of empirical and theoretical
    # with data confidence based on number of skips
    data_confidence = min(1.0, 3 / 20.0)  # 3 skips / 20.0
    expected_weighted = data_confidence * expected_empirical_median + (1 - data_confidence) * theoretical_median
    
    println("Expected weighted median: $(round(expected_weighted, digits=2))")
    println("Data confidence: $(round(data_confidence, digits=2))")
    
    # Should be close to the weighted average
    @test abs(ffg_median - expected_weighted) < 0.5
    println("✓ FFG median with historical data calculation is reasonable")
    
    return ffg_median
end

"""
Test skip probability calculation
"""
function test_skip_probability_calculation()
    println("\n=== Skip Probability Calculation Verification ===")
    
    calc = FFGCalculator()
    
    # Test with known values
    median_skip = 5.0
    test_cases = [
        (0, "very favorable"),
        (2, "favorable"), 
        (5, "at median"),
        (8, "unfavorable"),
        (15, "very unfavorable")
    ]
    
    for (current_skip, description) in test_cases
        prob = compute_skip_probability(calc, current_skip, median_skip)
        println("Current skip $current_skip ($description): probability = $(round(prob, digits=3))")
        
        # Probability should be between 0 and 1
        @test 0.0 <= prob <= 1.0
        
        # Lower skips should have higher probabilities
        if current_skip <= median_skip
            @test prob >= 0.4  # Should be reasonably high
        end
    end
    
    println("✓ Skip probability calculations are within expected ranges")
end

"""
Test favorable timing logic
"""
function test_favorable_timing()
    println("\n=== Favorable Timing Logic Verification ===")
    
    # Create simple test data
    test_draws = [
        LotteryDraw([1, 5, 10, 15, 20], Date(2022, 1, 1), 1),
        LotteryDraw([2, 6, 11, 16, 21], Date(2022, 1, 2), 2),
        LotteryDraw([3, 7, 12, 17, 22], Date(2022, 1, 3), 3),
    ]
    
    calc = FFGCalculator()
    
    # Test favorable timing for number that never appeared
    is_favorable_new = is_favorable_timing(calc, 25, 2, test_draws)
    println("Number 25 (never appeared), current skip 2: favorable = $is_favorable_new")
    
    # Test favorable timing for number that appeared
    is_favorable_existing = is_favorable_timing(calc, 1, 1, test_draws)
    println("Number 1 (appeared), current skip 1: favorable = $is_favorable_existing")
    
    # Both should return boolean values
    @test isa(is_favorable_new, Bool)
    @test isa(is_favorable_existing, Bool)
    
    println("✓ Favorable timing logic works correctly")
end

"""
Test FFG median with different DC values
"""
function test_ffg_median_with_different_dc()
    println("\n=== FFG Median with Different DC Values ===")
    
    # Create test data
    test_draws = [
        LotteryDraw([1, 5, 10, 15, 20], Date(2022, 1, 1), 1),
        LotteryDraw([2, 6, 11, 16, 21], Date(2022, 1, 2), 2),
        LotteryDraw([1, 7, 12, 17, 22], Date(2022, 1, 3), 3),
    ]
    
    dc_values = [0.25, 0.5, 0.75, 0.9]
    medians = Float64[]
    
    for dc in dc_values
        median = calculate_ffg_median_with_dc(1, test_draws, dc)
        push!(medians, median)
        println("DC $(Int(dc*100))%: FFG median = $(round(median, digits=2))")
    end
    
    # Higher DC should generally result in higher medians (more conservative)
    for i in 2:length(medians)
        @test medians[i] >= medians[i-1] * 0.8  # Allow some tolerance
    end
    
    println("✓ FFG median increases with higher degree of certainty")
end

"""
Run all FFG median verification tests
"""
function run_ffg_median_verification_tests()
    println("Starting FFG Median Calculation Verification Tests...")
    
    try
        test_theoretical_ffg_median()
        test_degree_of_certainty_values()
        test_ffg_median_with_data()
        test_skip_probability_calculation()
        test_favorable_timing()
        test_ffg_median_with_different_dc()
        
        println("\n🎉 All FFG median verification tests passed!")
        return true
    catch e
        println("\n❌ FFG median verification tests failed: $e")
        return false
    end
end

# Run tests if this file is executed directly
if abspath(PROGRAM_FILE) == @__FILE__
    run_ffg_median_verification_tests()
end
