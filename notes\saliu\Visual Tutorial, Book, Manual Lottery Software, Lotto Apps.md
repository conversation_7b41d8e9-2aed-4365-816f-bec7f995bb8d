---
created: 2025-07-24T22:10:02 (UTC +08:00)
tags: [software,lottery,lotto,manual,tutorial,help,tips,visual,screen,Bright software,eBook,book,online,Windows,operating systems,Command Prompt,editor,]
source: https://saliu.com/forum/lotto-book.html
author: 
---

# Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps

> ## Excerpt
> Read the online book of lotto software, the best lottery software. The lotto manual consists of screen-by-screen visual lottery tutorial for command prompt.

---
There is a number of Web pages at SALIU.COM that offer more in-depth help and instructions on how to use LotWon, SuperPower, and that piece of software art named MDIEditor And Lotto WE. A few links you might want to consider allocating some time to reading:

[**XCOPY** _**Command: The Best Backup Procedure, Method, Software in Windows**_](https://saliu.com/best-backup.html).

-   Check out also:
    
    ### [Resources in Lottery, Software, Systems, Lotto Wheels, Strategies](https://saliu.com/content/lottery.html)
    
    Lists the main pages on the subject of lottery, lotto, software, and systems.
-   [_**Updates to the**_ **Bright** _**lotto, lottery, and horse racing software bundles**_](https://saliu.com/forum/software-updates.html).
-   Updates to several Bright software packages: pick-3 and pick-4 lotteries, horse racing, 5- and 6-number lotto games.

### Like father, like son

Bright6 and most programs in the lottery software LotWon, SuperPower, plus MDIEditor And Lotto WE represent 32-bit software. Moreover, LotWon and SuperPower are 32-bit _**lotto software**_ that run at the so-called Command Prompt in Windows 2000/XP/even _Hasta-La-Vista_ (Windows 7, 8, 10). The _**Command Prompt**_ was first named DOS Prompt in Windows 95/98.

Microsoft has nightmares when it comes to its own child, DOS! But even Windows is a DOS program! Windows is a little more than a graphical interface to DOS! Keep in mind, however, that the graphical interface (GUI) was the real father of Microsoft! Sounds strange to make these mythological references to father and child. Zeus had to have his father, Chronos, killed. Microsoft had to have his competitors killed. The competitors were such giants as Lotus, WordPerfect, Ashton-Tate (dBase III). The giants (DOS software titans) were smothering the little child Microsoft. But Microsoft fed his own father, Windoze, to become a giant who killed the DOS software titans. And the rest is history, if you will… not mythology!

I want you to believe me that I only want to be as unbiased as anyone. I am technical here. My _**Command Prompt lottery software**_ runs far faster than any Windows (GUI) programs, including my own pretty child, MDIEditor And Lotto WE. There has been documented on my _lotto message boards_ that my highly optimized MDIEditor And Lotto WE has been unable to perform a specific task of generating lotto combinations… even after continuous running in days and nights. On the other hand, my console software (another euphemism for _Command Prompt_) performs a similar task in a mere 20 minutes! Other Windows programs are farther behind than my lottery, gambling, and scientific software.

### Start me up!

The documentation that comes with Bright6 already showed you how to create a directory (folder) at the command prompt. You already installed my lotto software in that directory; e.g. **C:\\BRIGHT6**  
You can download _Bright6_ directly into the **C:\\BRIGHT6** folder. Then, in _Windows Explorer_, click on _**Bright6**_ to decompress the executable file.

You already know how to start the software — a very large percentage of lottery users know it well. If you are in the C:\\BRIGHT6 folder, just type 6 (better still _**B6**_, lately) and press \[Enter\]. Otherwise, you need to find the _**Command prompt (CMD)**_ in your system. Usually, it is under _Start_, _Programs_, _Accessories_. As per above, Microsoft tries very hard to hide DOS from users.

Microsoft$ are simply paranoid. The US government had a very strong case in their antitrust legal case against Microsoft. Monopoly destroys not only competition, but also development. Just look at me: I discovered that the best way of handling processes like lotto generating, or statistical and probability generating of combinations, or mathematical calculations… much more… are significantly better served by non-graphical software. Graphical software can bear a huge burden, imposed by the graphical interface. That is the truth — monopolies have strong tendencies to kill the truth, unfortunately!

So, bear with me and hunt for that highly secretive command prompt on your own PC, will you? It's worth the effort (talking to that small percentage of users frightened by the word _DOS_).

Best Command Prompt techniques  
-   Create a shortcut to %SystemRoot%\\system32\\cmd on your desktop;  
    (CMD is the real Command prompt);  
    
-   Right-click on the shortcut, then select _Properties_;  
    
-   In _Options_ select _Full screen_ (Full screen can also be achieved by holding down \[Alt\] and \[Enter\] simultaneously);  
    
-   In _Layout_, _Window size_ select Width = 80, Height = 25;  
    
-   In _Colors_ select screen text = yellow and screen background = blue;  
    
-   Save and choose Apply the settings to all applications.

If you follow the information files that accompany my software, you can also create a batch file (.BAT) to add to your _AUTOEXEC.BAT_ file. The _L6.BAT_ file can automate even further the starting of Bright6.

Then, each and every screen in my lotto software is self-explanatory. I embedded very informative text in every screen. You already know that after your first visits to your _**membership**_ pages.

The **Bright** software packages were introduced before the 64-bit incarnations of Windows — Vista, Windows 7, 8 ,10. The lotto software bundles had a few 16-bit programs that do not run in 64-bit operating systems. The first versions of **Bright** software were started via _batch files_ (e.g. 6 to start Bright6). That procedure does not work under _64-bit Vista or Windows 7_. Not to worry! I created even more capable interfaces to run the _**Bright lotto software**_ suites under _64-bit Vista or Windows 7_. The new interfaces still run under 32-bit Windows.

The new starting command is _**B6**_ (for lotto 6; _**B5**_ for lotto 5, etc.) It is highly recommended that you use the new interface (**B**\*) to start the new **Bright** software packages. The new interface provides access to 4 menus chock-full of applications (compared to only 2 in the first version of **Bright** software). Instead of pressing function keys, the new interface functions by pressing a mnemonic key to start a function (e.g. _**E**_ to start editing with the _Notepad_).

You might want to use the old starting command (e.g. _**6**_ for lotto 6) only to use one of the best text editors: QEdit — alas, a 16-bit software application! _O tempora, O mores!_

Always read the... _README.TXT_ files that accompany all my **Bright** software packages. They offer up-to-the-date instructions regarding the latest versions of the **Bright** software collections.

-   I created a specialized piece of software to maximize the efficiency of working with data files: **LotteryData**. Please visit the following link, as it also deals with the important topics of lottery _strategies_ and the _winning reports_:
-   [_**Software to Manage Lottery Data Files**_](https://forums.saliu.com/lottery-strategies-start.html#software).

![The lottery manual consists of a screen-by-screen lotto tutorial.](https://saliu.com/forum/HLINE.gif)

### 1.- The Main Menu

[![Lottery Software: Jackpot Lotto, Pick Lotteries, Powerball, Mega Millions, Euromillions, Keno.](https://saliu.com/images/ultimate-lotto-software-60.gif)](https://saliu.com/free-lotto-lottery.html)

![The lottery data is a file of past lotto results, drawings, draws.](https://saliu.com/forum/HLINE.gif)

### 2a.- The Data Files

You can't do much without a _**data file**_: A text file where you record the lottery drawings. The data files enjoy in-depth coverage at SALIU.COM. You only enter the numbers in a draw (or drawing, depending where you live). No other information is needed (such as the date of the draw, or the day of the week).

In my case, I use a great 16-bit DOS text editor _QEdit_ — press function key F2. Lately, _**Bright lottery software**_ employs even _Notepad_ (press E or I). My lotto-6 data file is named _PA-6_ (for Pennsylvania lottery, namely the lotto 6 game). You can create and use as many data files as you want, for any game, with any of my lottery programs. I know, the rest of the lottery software vendors actually cheat. If you want lotto software for two states lotteries (or national lotteries), they ask you to pay twice. They confuse you that a data file is a lottery software package! NOT! A lotto data file is simply a collection of lottery numbers — nothing more!

![Lottery software book #2: Create files of past results, drawings, winning number.](https://saliu.com/ScreenImgs/lotto2.gif)

### 2b.- Update Data Files

Again, I press the function key F2 (E or I) to start the text editor. I enter the latest lotto results. The latest lottery drawing always goes to the top of the data file (line #1). Therefore, the oldest drawing is always the last line in the text file. NO empty lines are allowed in the lotto/lottery data files!

Meanwhile, be sure not to mix different game formats in your data file. If the game format changes, you always start a new lotto results file. For example, my lotto-6 game changed from '6 of 48' numbers to '6 of 49' numbers. I renamed the PA-6 file PA-6.OLD and saved it for archiving purposes (especially since I do all kinds of analyses). I created a new PA-6 data file (from scratch) for the _6 of 49_ lotto game.

In this example, I entered 6 hypothetical numbers. As you can see on the screen, I separated the 6 numbers by commas and spaces. You can also use only commas or only spaces. I recommend only spaces.

After you finished editing (updating) your data file, you may want to sort it and thus format it nicely. Sorting (a standalone program in my lottery software) does a very good job at formatting the lottery results files. You can also use the sorting function in Util632 (better still, the newest and more powerful Super Utilities in the main menu).

![Lottery software book #3: edit, maintain files of past results, drawings, winning number.](https://saliu.com/ScreenImgs/lotto2.gif)

### 3a.- The Winning Reports: W6 and MD6

This is the cornerstone of Ion Saliu's software: Filtering. The filters are simply conditions, or parameters, or restrictions that can reduce the amount of lotto combinations. The filters are listed as columns in special reports that look a lot like spreadsheets. The reports are named W6.1 to W6.4 and MD6.1 to MD6.4 (a total of 8 reports).

The winning reports show what values (levels) the filters had within a number of past drawings. The concept has a solid mathematical foundation. The past and the future are the same thing. What happened in the past will happen in the future as well. Of course, there is no such thing as absolute certainty (the cornerstone of Ion Saliu's categorical philosophy). We can only discern trends of various degrees of certainty.

The user should not waste her time by questioning what the lottery filters mean, what their mathematical foundation is, what their algorithms are, how they are coded in the software, etc. Those are the headaches that this super programmer deserves to have! The user should not torture his head with such problems. Unless, of course, the user is actually a pirate who wants to copycat my ideas, theories and, especially, my lotto software!

![There is no better lottery manual than this page.](https://saliu.com/ScreenImgs/lotto4.gif)

### 3b.- The Winning Reports: The minimum size of the data file

The versions of Bright lotto software require a D6 data file of at least 12000000 (12 million) lotto combinations (lines). For 5-number lotto: a D5 data file of at least 4000000 (4 million) lotto combinations (lines).

Not to worry! No lottery in the world would have ever conducted that many drawings! You simulate a data file (usually named SIM-6). Then, the software automatically combines a data file of real lotto drawings (named DATA-6 or, in my case, PA-6) with SIM-6.

![Bright lotto software require data files of 12 million lotto combinations. For 5-number lotto: a drawings file of at least 4 million lottery combinations.](https://saliu.com/ScreenImgs/lotto5.gif)

![Lottery software manual: Use all draws in your lotto winning numbers file.](https://saliu.com/ScreenImgs/lotto6.gif)

![Software book requests to enter the biggest number in lotto game; e.g. 45, 49, 54, 59.](https://saliu.com/ScreenImgs/lotto7.gif)

![Lotto software saves the winning reports or filter files to disk.](https://saliu.com/ScreenImgs/lotto7.gif)

![You too can write a good lotto book and lottery manual.](https://saliu.com/ScreenImgs/lotto9.gif)

![Lottery winning reports for the special filters named Ion: Lotto sums, sum-totals, root sums.](https://saliu.com/ScreenImgs/lotto10.gif)

![Lotto software informs on the success of generating the reports for past lottery drawings.](https://saliu.com/ScreenImgs/lotto11.gif)

### 4a.- The Winning Reports: Analysis

You analyze the winning reports simply by loading them in a text editor (function key F2, or E, or I). You view them reports with the goal to find strategies. A lottery strategy is a collection of filter settings. Setting strategies (i.e. setting filter levels) is a science in itself that could easily fill thousands of pages in a series of big books.

If you follow the links listed on this page, you will find plenty of information on how to set the lotto filters (i.e. select lottery strategies).

![The program shuffles the deck of lotto cards and arranges the lotto numbers.](https://saliu.com/ScreenImgs/lotto12.gif)

![An MD past draw lotto report: Lottery sum-totals, sums, root sums.](https://saliu.com/ScreenImgs/lotto13.gif)

### <u>5a.- Strategy Checking</u>

Press the function key F3 (better still, C) to start the Strategy6 program.

You already established a lottery strategy in the previous step. For more details, please follow carefully the following links, in addition to the previous list of links. This super psychologist recommends the following technique. Copy the relevant text in the Web pages (in the list of links). Paste the text in a word processor or text editor document (name it as you want). Then, try to edit the document using as much as possible your own style (words, wording, and sentences). We are inducing our brains into believing that the knowledge they acquired is the creation of their own.

No learning tool is more effective in acquiring knowledge and developing knowledge of your own. That's how I have done it. I have read, I have studied, I have copied and pasted, and then I have kept editing. I have acquired a huge volume of information — and I couldn't help it but discover new knowledge.

-   _"Filters extra! Filters extra!"_ The philosophical science of the art of [_**lottery filtering or combination reduction**_](https://saliu.com/filters.html).
-   [_**Lottery Utility, Lotto Software**_](https://saliu.com/lottery-utility.html).
-   [_**Cross-reference strategy files**_](https://saliu.com/cross-lines.html) created by LotWon, SuperPower, MDIEditor Lotto WE.
-   [_**Strategy-in-reverse for lottery and lotto**_](https://saliu.com/reverse-strategy.html) - Turn _Lose_ into _Win_!
-   [_**Basics of a Lottery, Lotto Strategy Based on Sums (Sum-Totals), Odd / Even, Low / High Numbers**_](https://saliu.com/strategy.html).

![The function of lotto software that checks a lottery strategy in the past.](https://saliu.com/ScreenImgs/lotto14.gif)

![The lottery strategy checking software is based on the W winning reports.](https://saliu.com/ScreenImgs/lotto15.gif)

![The lottery strategy checking software input: screen prompt or disk files.](https://saliu.com/ScreenImgs/lotto16.gif)

![Skip all input screens for lotto filters. Generate lotto combinations with no favorite numbers.](https://saliu.com/ScreenImgs/lotto17.gif)

![Type the filter values for each filter in your lotto strategy: minimum and maximum levels.](https://saliu.com/ScreenImgs/lotto18.gif)

![Ion Saliu's book of lottery: Lotto filters work with sums, sum-totals, root sums.](https://saliu.com/ScreenImgs/lotto19.gif)

![Nobody has ever written better visuallotto book, lottery ebook for software use.](https://saliu.com/ScreenImgs/lotto20.gif)

![Lotto software saves the data of a lottery strategy to a disk file ST.](https://saliu.com/ScreenImgs/lotto21.gif)

![Lottery software also creates a useful file to show how many lotto combinations in the past.](https://saliu.com/ScreenImgs/lotto22.gif)

### 6a.- Strategy Checking: Analysis of the report

Usually, the strategy report was named ST6.REP in the step above. You simply open the report in a text editor to see how your strategy fared in the past. That is, did your strategy register any hit? If yes, when did the lotto strategy hit? The hit (winning) situation is identified by a drawing number. For example, the strategy hit 17 lottery drawings back (that is, in past draw #18; we consider the skips as the number of drawings between hits).

The report also shows the levels of all the filters in the software (for all W6 and MD6 reports). That gives you a lot of info on the filters. When you want to tighten your lotto strategy for runtime, you can select additional filters. Again, you do not need to select a lot of lotto filters in your strategy. Just set a few filters (in addition to the key or pivot filter(s)) that give you the highest level of confidence.

![Your lotto software informs of successfully creating the lottery strategy.](https://saliu.com/ScreenImgs/lotto23.gif)

![The lottery strategy report shows the filters and parameters of your lotto strategy.](https://saliu.com/ScreenImgs/lotto24.gif)

![A loto strategy has numbers of hits in the past, frequency, skips of loterie drawings between hits.](https://saliu.com/ScreenImgs/lotto25.gif)

![The software shows lottery strategy reports for inter-related filters: W, MD, 1 to 6-number groups, sums.](https://saliu.com/ScreenImgs/lotto26.gif)

### 7a.- Strategy Hits: How many lotto combinations the strategy would have generated in the past

Press the function key F4 to start the StrategyHit6 program.

The application shows how many combinations the lottery strategy you analyzed with Strategy6 would have generated in past winning situations. This step relies on the strategy files and reports created by Strategy6. Therefore, you must run StrategyHit6 before StrategyHit6 and using the same D6 lotto data file (Data-6+SIM-6).

Synchronization is of the essence. You always make sure your D6 file is up-to-date. Next, you generate the winning reports (F5). Next, you check a lotto strategy (F3). And, then, you check a strategy for hits (F4). The final step is the lotto combination generating (F6, F7, or F8).

You can always skip the F4 step. Strategy in the past (StrategyHit6) is only an indicator. It shows you how many lotto combinations to expect, with a good approximation. Beware that this step can be very time-consuming. This process can be very, very slow. If you don't have a fast computer and you selected a very tough strategy, it is best to skip this step. Go directly to the process of generating lotto combinations.

StrategyHit6 does NOT display the combinations to screen or save them to disk. Otherwise, the process would be even slower…much slower. The application only generates the combinations internally and counts them.

![Lottery software checking how the strategy would have fared in the past lotto drawings.](https://saliu.com/ScreenImgs/lotto27.gif)

![The same lottery data file is required to check the strategy combinations in the past.](https://saliu.com/ScreenImgs/lotto28.gif)

![The same biggest lotto is required to check the lottery strategy hits in the past.](https://saliu.com/ScreenImgs/lotto29.gif)

![The lotto software can enable special inner or innate filters.](https://saliu.com/ScreenImgs/lotto30.gif)

![Lottery software can screen input filters or use a filter file created by lotto strategy checking.](https://saliu.com/ScreenImgs/lotto31.gif)

![The best lotto software can use the hits file created by the check function.](https://saliu.com/ScreenImgs/lotto32.gif)

![The strategy hits in the past generates and counts lotto combinations.](https://saliu.com/ScreenImgs/lotto33.gif)

### 8a.- Generate Lotto Combinations

Press the function keys F6 or F7 or F8 to start the lotto combination generating applications: Lexicographic6, Combine6. Wheel6.

This is what is all about! Generate those lotto combinations to hit me the jackpot!

You can use a strategy file created in the steps above and you added some filters to it (the disk input option). You can also type the filter values at the prompt (the screen input option). You can display the lotto combinations to screen only (the screen output option — doesn't make a lot of sense, though). You can generate the combinations while saving them to a disk file (the disk output option). You only display to screen in order to have a pre-idea on how many lotto combinations to expect. You don't want to save to disk too many combos that would gobble up your disk (the headache of yester years)!

Again, your D6 data file must be up-to-date and in synchronicity with the steps F5 and F3.

![The greatest feature of the best lottery software: generate winning lotto combinations.](https://saliu.com/ScreenImgs/lotto34.gif)

![Lottery software uses the same results file, with all past winning numbers.](https://saliu.com/ScreenImgs/lotto35.gif)

![Best lottery software generates combinations for the same lotto game format as W reports.](https://saliu.com/ScreenImgs/lotto36.gif)

### 8b.- The Inner Filters

Those (top secret) inner lottery filters have a good hit ratio while eliminating extra combinations. Hey, all lotto combinations have an equal probability of appearance, but a discriminating degree of certainty. Briefly and bluntly, you must be prepared to live thousands of years to reasonably expect the drawing of lotto-6 combinations like 1-2-3-4-5-6. Many happy returns!

Nota bene. The inner filters are NOT analyzed in the W6 and MD6 winning reports. You may want to disable the inner filters (press N) at least in the beginning. As you gain LotWon expertise, you may want to enable the inner filters. Perhaps future versions of this incredulously great lottery software incorporate the inner filters in the winning reports.

![Generating lotto combinations can also employ special inner filters.](https://saliu.com/ScreenImgs/lotto37.gif)

![Software to generate lottery combinations can manually input filters or use ST filters files.](https://saliu.com/ScreenImgs/lotto38.gif)

![The lotto combinations can be displayed to screen or generated to a disk file.](https://saliu.com/ScreenImgs/lotto39.gif)

### 8c.- Generate Lotto Combinations: Additional options

More options, and then some more options! I used to hate too many options. In fact, it is far better to have many choices, rather than no choice at all! Choices can be ignored easily — but what can we choose if nothing is available?!

You can select to generate lotto combinations with no favorite numbers. Or, you can make sure that one of your favorite lotto numbers appears in each and every lotto combination. You can also select two favorite lotto numbers.

The Purge File option is an extraordinary feature. It is a case of expandability and interoperability. Say, you already have a file of lotto combinations previously generated. That output file must be in text format (6 lotto numbers per line separated by spaces or commas). The file of combinations could have been generated by any lotto software, not only software signed _Ion Saliu_. The platform does not matter (DOS or Windows). You can surely purge output lottery files generated by MDIEditor And Lotto WE.

Purging a file means that you will apply new filters in order to further reduce the amount of lotto combinations to play.

The Shuffle option is another lottery function that attracts accolades. Say, your lotto-6 game has 49 numbers. This option considers the lotto numbers as playing cards. The program shuffles the deck of cards and arranges the lotto numbers in 9 lines of 6 numbers each. The numbers are unique except for the last line (line #9 fills the line to 6 lotto numbers). This option was applied by a group of UK college professors and staff to win the [_**lotto jackpot**_](https://saliu.com/all-lotto-numbers.html).

![The lotto software has multiple options: favorite numbers, shuffle all lotto numbers.](https://saliu.com/ScreenImgs/lotto40.gif)

![The user of lotto software can see the combination generating process.](https://saliu.com/ScreenImgs/lotto41.gif)

![Lottery software informs the player when all combinations were generated by the lotto strategy.](https://saliu.com/ScreenImgs/lotto42.gif)

### 9a.- Great Lotto Utilities

Press the function key F9 to start Util632. Better still, use the _**S = Super Utilities**_ function of _**B6**_ — the latest version of Bright lotto software packages. It runs the much more powerful SoftwareLotto.

This application performs lots of auxiliary tasks. You name it, you have it! You may call Util632 the Swiss Army knife in the lottery war against the odds.

Simulate data file is the place where you create those gigantic SIM files (simulated files to make sure D6 data file contains at least 12,000,000 combinations).

You can sort here your data files. This option also sums up the numbers combinations by combination (or draw by draw).

Check for winners does exactly that. You can check any lotto output file against the latest drawing or a number of past drawings to see if your output file has any winners (from '3 of 6' to '6 of 6' winners).

The Frequency reporting performs the most comprehensive statistical analysis in the lottery world — period.

The Strip duplicates/Wheel does some extraordinary feats. For starters, it makes sure an output lotto combination file consists only of unique combinations. Usually, duplicate means two combinations that consist exactly of the same 6 numbers. We pushed the concept further. We can consider a duplicate two lotto combinations with '3 of 6' common numbers. This is the best way to play lotto wheels from output files.

The Make/Break/Position is the place where you combine DATA-6 with SIM-6 to create that huge D6 lotto data file with at least 12000000 lines. This option is so powerful that it would require its own big book.

For much more detailed information read:  
[_**Utility Software: Pick-3, 4 Lottery, Lotto-5, 6, Powerball/Mega Millions/Thunderball, Euromillions**_](https://saliu.com/lottery-utility.html).

![The lotto manual consists of a screen-by-screen lottery tutorials.](https://saliu.com/ScreenImgs/best-lotto-utilities.gif)

![The Lottery Software Utility is an important part of Bright lotto software.](https://saliu.com/ScreenImgs/lotto44.gif)

### 10.- Extra Software Packages

Press the function key F12 to open the floodgates to numerous extra applications for lotto, lottery, mathematics, probability theory, combinatorics… You name it, you got it...

I threw in a ton of extra software programs... and then some. Just run them. They are so perfectly coded for ease of use that even I can run them hands off! I mean, I write so many pieces of software that I sometimes forget who the author is!

Just run those special programs and you'll get to squeeze the most benefit from them in a reasonably short time. The programs also recommend further worthy reading on this site.

![Lotto software has very useful additional programs: sums, check winners, frequency, skip systems.](https://saliu.com/ScreenImgs/lotto45.gif)

![Lottery software additional programs: calculate odds, lotto wheels, lexicographic order generation.](https://saliu.com/ScreenImgs/lotto46.gif)

[![Lottery Utilities Software, Tools, Lotto Wheels, Systems.](https://saliu.com/ScreenImgs/lotto-b62.gif)](https://saliu.com/free-lotto-tools.html)

<big>For the latest updates, including screen images, of the <i><b>Bright</b></i> and <i><b>Ultimate</b></i> lottery software, please follow the links to most recent Web pages:</big>

-   [_**Download Software, Source Code, Lotto Results, Statistics for Powerball, Mega Millions, Euromillions**_](https://saliu.com/bright-software-code.html).
-   [_**Download the <u>Ultimate</u> Software for Lotto, Pick 3 4 Lotteries, Horse Racing**_](https://saliu.com/ultimate-software-code.html).

![Read a concise book, tutorial of best lotto software, lottery software programs visual tutorial.](https://saliu.com/forum/HLINE.gif)

**[Home](https://saliu.com/index.htm) | [Search](https://saliu.com/Search.htm) | [Help](https://saliu.com/Help.htm) | [New Writings](https://saliu.com/bbs/index.html) | [Software](https://saliu.com/infodown.html) | [Odds, Generator](https://saliu.com/calculator_generator.html) | [Contents](https://saliu.com/content/index.html) | [Forums](https://forums.saliu.com/) | [Sitemap](https://saliu.com/sitemap/index.html)**

![The lottery manual consists of screen-by-screen lotto software tutorial in Windows Command Prompt.](https://saliu.com/forum/HLINE.gif)
