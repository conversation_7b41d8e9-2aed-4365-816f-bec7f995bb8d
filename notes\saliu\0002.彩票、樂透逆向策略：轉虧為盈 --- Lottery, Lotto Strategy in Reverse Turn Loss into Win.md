---
created: 2024-12-29T13:34:30 (UTC +08:00)
tags: [strategy,reverse strategy,strategies,lottery,lotto,hit,miss,win,lose,drawing,file,software,LIE files,]
source: https://saliu.com/reverse-strategy.html
author: 
---

# 彩票、樂透逆向策略：轉虧為盈 --- Lottery, Lotto Strategy in Reverse: Turn Loss into Win

> ## Excerpt
> Lottery missing is more frequent than hitting in lotto. We reverse the strategy: Intentionally set lotto or lottery filters that will not win next drawing, thus making a profit from losing.

---
![Reversed lottery systems, strategies aka LIE elimination are the creation of <PERSON> in the 1990s.](https://saliu.com/images/lottery.gif)

### 一、[_反向_彩券策略簡史](https://saliu.com/reverse-strategy.html#history)  
二.[反向樂透策略_Redivivus_](https://saliu.com/reverse-strategy.html#strategy)  
三．[反向樂透策略範例：消除配對、三元、四元](https://saliu.com/reverse-strategy.html#reverse)  
四．[反向彩票策略範例：跳過、十年、頻率](https://saliu.com/reverse-strategy.html#skips)  
五、[彩券策略_三位一體_：_順式_、_清除式_、_逆式_](https://saliu.com/reverse-strategy.html#trinity)  
六．[彩票 Lotto 軟體、策略、系統資源](https://saliu.com/reverse-strategy.html#links)

![In lie or NOT elimination lottery strategy we rely on having non-winners in the first output.](https://saliu.com/images/lotto.gif)

## <u data-immersive-translate-walked="c01cc6f4-c9d0-456a-9160-12564fbf3b63" data-immersive-translate-paragraph="1"><span data-immersive-translate-translation-element-mark="1" lang="zh-TW"><span data-immersive-translate-translation-element-mark="1"><span data-immersive-translate-translation-element-mark="1">一、<i data-immersive-translate-walked="c01cc6f4-c9d0-456a-9160-12564fbf3b63">反向</i>彩券策略簡史</span></span></span></u>

_WayBack Machine_ ( _web.archive.org_ ) 於 2004 年 6 月 26 日首次捕獲。

我偶然發現了一些舊報紙。這些文件描述了舊的pick-3彩票策略。我記得我和加州一位優秀的彩票合作者一起計算了這些結果。有趣的是，輸出檔案被命名為 LIE.3、LIE.4……然後，在紙上提醒我：「過濾器 TOT 總是清除 LIE.3 彩票檔案。樂透過濾器 ANY 總是清除 ANY 和 SUM 產生的 LIE 檔案。

這個策略，其實就是抽獎策略的反面！我想選擇彩票過濾器以確保我設定錯誤！例如，我會設定 VR\_1 = 5 和 VR\_2 = 5。

兩個過濾器產生的輸出（我命名為 LIE 的文件）將添加到 D3 文件（位於頂部）。我將消除該 LIE 文件中的所有樂透組合，因為我_知道_它們不會中獎！我會這樣選擇一些 25-30 個過濾器。很多時候，如果只有一個過濾器，我就會_錯_！也就是說，在 30 個選 3 彩票過濾器中，我希望錯誤地設定其中一個！因此，整個抽獎策略將會失敗！有道理嗎？

![New lotto strategy in reverse, lottery 2010, 2011.](https://saliu.com/HLINE.gif)

## <u data-immersive-translate-walked="c01cc6f4-c9d0-456a-9160-12564fbf3b63" data-immersive-translate-paragraph="1"><span data-immersive-translate-translation-element-mark="1" lang="zh-TW"><span data-immersive-translate-translation-element-mark="1"><span data-immersive-translate-translation-element-mark="1">二.反向樂透策略 Redivivus</span></span></span></u>

我在 2010 年的恩典之年又回到了_**反向彩票策略。**_彩票組合：

-   [_**刪除樂透組合**_](https://saliu.com/lie-lottery-strategies-pairs.html)。

這是一個較長的故事，我在之前的樂透論壇和2010年的樂透超級論壇中都寫過。多位論壇成員參與了討論。這次討論也促成了我的 lotto-5 軟體的最重要升級： **Bright5** 。

我將在這裡重新發布新策略（也稱為_**LIE**_ ）分析的最重要部分，這也是我最新的樂透軟體的功能（功能）。

彩票策略和文件的謊言名稱聽起來很奇怪。我擁有的更好的_**不是**_文件。因為在_該輸出檔中下一次抽獎將不會有中獎者_。 Problem was _NOT_是電腦程式設計中廣泛使用的關鍵字。這就是為什麼我選擇了_「謊言」_ ——仍然是一個具有相似含義的簡短單字。就像_如果該輸出檔案將在下一次抽獎中中獎一樣，那將是一個 LIE_ 。

這是邏輯的基本定律之一：_**否定的否定就是肯定。**_這就是我的逆向彩券策略的開始。

您注意到，您的樂透輸出檔案通常沒有中獎號碼：不是 6 個中獎者，不是 5 個，不是 4 個，不是 3 個，有時甚至是 2 個。您可以開啟 LIE 檔案並產生不包含 LIE 檔案中的數字群組的樂透組合。例如，您不需要任何“5 of 5”組；即每5個數字的組合都會被消除。您可以應用更嚴格的設置，例如“4 of 5”或“3 of 5”，甚至“2 of 5”。對於此消除功能，您一次只能套用一個過濾器。以下過濾器適用：單、對、旅行、四、五（6 號樂透）。

_**LIE**_選項（_**反向彩票策略**_）在以下彩票軟體包中實現：Bright5.exe 和 Bright6。另外還有選秀軟體集Bright3和Bright4.exe；以及賽馬軟體 BrightH3 中。預設情況下， _**LIE 過濾器**_未啟用 - 使用者必須按_**Y/y**_ （表示「是」）才能啟用它。

![Code name of this reversed lottery strategy is LIE lotto strategy.](https://saliu.com/ScreenImgs/reverse-strategy.gif)

## <u data-immersive-translate-walked="c01cc6f4-c9d0-456a-9160-12564fbf3b63" data-immersive-translate-paragraph="1"><span data-immersive-translate-translation-element-mark="1" lang="zh-TW"><span data-immersive-translate-translation-element-mark="1"><span data-immersive-translate-translation-element-mark="1">三．反向樂透策略範例：消除配對、三元、四元等。</span></span></span></u>

我重複一下我的新彩票論壇（從 2010 年開始）中的一個例子。我運行了_**Super Utilities**_ ，創建了一個 TOP5 文件，其中每個數字都有最好的 10 個配對。然後我使用 TOP5 作為 Make/Break/Position 的輸入，選項 4 = Break 5+/Positional 範圍，然後選項 1 = 1 Number + 4 Pairs。我將輸出檔命名為 LIE5.2，因為我想使用過濾器 ID 2。

我針對 LIE5.2 運行了帶有 LIE 選項的**Joint5-10** 。過濾器ID=2產生12種組合。其中一個組合有 3 位得獎者。我以為_5 中的 5_ 。我仔細查了一下TOP5。其中一個未抽中的號碼與抽籤中的 3 個號碼是前 10 號配對：

5 3 16 10 13 1 19 33 34 40 42

那真是個糟糕的情況！按照我的邏輯，我沒想到抽獎中的樂透號碼會超過一個與遊戲中任何其他號碼配對的前 10 名！例如，只要 5 和 3 成為前 10 名配對就可以了。

我針對另一種情況重複了這個過程：  
繪圖#203：12、24、25、37、38

ID = 2 的過濾器僅產生一種組合，其中也有 3 個獲勝者。我又查了一下TOP5：  
15 38 32 24 30 31 1 36 22 39 41 20 30 42 27 5 32 37 24 13 14 15

我嘗試了 ID = 3，它產生了頭獎組合，但有超過 45,000 個組合！另一方面，45,000 只佔 5-43 樂透組合總數的不到 5%。換句話說， _LIE_過濾器僅用一個_LIE_檔案就消除了 95% 的總組合，而且頻率很高。

我們不僅限於配對。我們在_**超級實用程式**_中運行組合產生器（通常沒有最喜歡的號碼）。我們可以使用_Least53_作為輸入來啟用_最小三元組_。我們現在可以肯定，在下一次抽獎時，輸出將不會包含頭獎組合。輸出中僅獲得 3 個中獎號碼的情況非常罕見。該輸出檔案也是 LIE 函數的一個非常好的候選文件。對於 Quadruples 和 Least54 作為輸入也是如此。

![This is a powerful lottery strategy: reverse the loss to win.](https://saliu.com/HLINE.gif)

## <u data-immersive-translate-walked="c01cc6f4-c9d0-456a-9160-12564fbf3b63" data-immersive-translate-paragraph="1"><span data-immersive-translate-translation-element-mark="1" lang="zh-TW"><span data-immersive-translate-translation-element-mark="1"><span data-immersive-translate-translation-element-mark="1">四．反向彩票策略範例：跳過、十年、頻率</span></span></span></u>

_跳過_、_十年_、_最後一位數字_和_3 組頻率_也是_**LIE**_選項（_**反向彩票策略**_）的良好候選者。查看**SkipDecaFreq**所建立的報表。您會注意到，特定彩票抽獎中的字串與之前的幾次抽獎沒有重複。那麼，使用跳躍、十年和 3 組頻率作為過濾器來產生樂透組合。您知道輸出檔案在下一次抽獎中記錄 5 個甚至 4 個中獎樂透號碼的情況非常罕見。

主要焦點是pick 3 抽籤，澄清了一名會員（也是激烈的競爭對手）的故意誤解。

我為 pick-3 繪製了 3 份報告。您可以看到，某些 3 參數字串（1-0-2 等）在一些繪圖中不會重複；其他字串確實比其他字串重複得更頻繁。您可以看到 1-1-1 類型的字串顯示出更好的頻率。

較低頻率： _**LIE**_的良好候選者；  
更高的頻率： _**PURGE**_的良好候選者（如**直接**玩，而不是**反向玩**）。

![This is a LIE strategy for pick-3 digit lottery.](https://saliu.com/ScreenImgs/loss-win-pick3.gif)

我為十進位字串 1-0-2（報告中的第一行）產生一個輸出檔（LIE3.1 或 OUT3.1）的組合。產生直盤數：81。

我為頻率字串 0-2-1（報告中的第一行）產生一個輸出檔（LIE3.2 或 OUT3.2）的組合。產生的直子數：135。

我為低/高字串 2-2-1（報告中的第一行）產生一個輸出檔（LIE3.3 或 OUT3.3）的組合。產生的直子數：125。

我為奇數/偶數字串 1-2-1（報告中的第一行）產生一個輸出檔（LIE3.4 或 OUT3.4）的組合。產生的直子數：100。

我可以連接到包含 441 個組合的 LIE3 檔案（有一些重複）。我很有可能經常成功。當然，LIE 功能是其他過濾器的補充，也是 pick-3 策略的一部分。

![A lotto strategy is many reductions in one: Straight software, purge, reversed.](https://saliu.com/HLINE.gif)

## <u data-immersive-translate-walked="c01cc6f4-c9d0-456a-9160-12564fbf3b63" data-immersive-translate-paragraph="1"><span data-immersive-translate-translation-element-mark="1" lang="zh-TW"><span data-immersive-translate-translation-element-mark="1"><span data-immersive-translate-translation-element-mark="1">五、彩券策略<i data-immersive-translate-walked="c01cc6f4-c9d0-456a-9160-12564fbf3b63">三位一體</i>：順式、清除式、逆式</span></span></span></u>

~ 1：_**順子**_代表傳統的彩票策略類型。您可以根據 W、MD、SK、DE、FR 報告的分析來選擇策略。您選擇具有較高確定性 (DC) 的過濾器，以便很快就會命中。您也可以查看該特定彩票策略過去的表現。您也可以查看該策略為命中情況產生了多少個樂透組合（_檢查策略命中_功能）。

您可以在頻率報告中看到兩次出現明顯的 pick-3 策略。它們以 \* 標示（第 6 行和第 16 行）。頻率字串 2-1-0 始終產生 36 種組合（通常每次抽獎都不同）。如果在最後 20 次抽獎中進行，則成本為 720。 7200 的成本將產生 9000。

該策略也會跳過——因此，許多圖畫將不會被播放。還有其他過濾器可供應用。 LIE策略也可以同時應用。

更嚴格的策略在 200 張圖中出現 2-4 次：3-0-0（頻率組）。它產生 8 種組合。該策略很難減少到更少的組合。但每次點擊之間可以跳過 50 張圖畫。費用為8 \* 100 = 800。

~ 2：_**清除輸出文件**_  
在這個現實案例中，我們產生 36 pick 3 集到傳統輸出檔（例如 OUT3）。我們在 Lexico.exe 程式中選擇_**Purge**_ 。我們輸入正常的彩票資料檔（D\*）。然後，我們輸入 OUT\* 作為要清除的檔案。最後，我們可以套用一個或多個 LIE\* 檔案。

如果我們建立與不同過濾器 ID 一起使用的 LIE 文件，我們可以使用字典產生器中可用的_PURGE_函數的強大功能： Lexico5.exe和**Joint5-10** 。例如，我們從 LIE5.1 開始並產生（在正常字典模式下）輸出檔案 OUT5.L1。接下來，我們套用 LIE5.2 並對 OUT5.L1 執行 PURGE ...等等。

~ 3：_**逆轉策略**_  
新的 LIE 功能有很多可能性。我們還有很多——我的意思是，很多——過濾器可以使用！更不用說我們有策略檢查實用程式。我想我們還需要一些額外的耐心和勤奮。

此外，可以建立多個 LIE 檔案來使用相同的過濾器 ID。當然，還有許多其他樂透過濾器可以與傳統的彩票策略方式配合使用。

然而，應用 LIE 功能時啟用的過濾器 ID 很重要。此外，我們可以連接多個 LIE 文件，在其中套用相同的過濾器 ID。使用 Bright 彩票包選單功能中的特殊_**串聯**_功能。

保留您的彩票輸出檔案。您可能想要連接其中一些。_**檢查獲勝者**_是_**Super Utilities**_中的另一個有用工具。我們還可以運行**Winners** ，這是一個獨立的程序，用於根據真實的彩票抽獎檢查中獎號碼。我們可以注意到，一個大的輸出檔案有時甚至連 2 個中獎彩券號碼都沒有命中。使其成為一個 LIE 檔案 - 並應用特定的過濾器 ID！

![We reverse the strategy: Intentionally set lotto or lottery filters that will not win next drawing.](https://saliu.com/HLINE.gif)

-   我在致力於_反向策略的_樂透軟體的理論和軟體程式設計方面取得了重大進展。我的 5 號樂透軟體的一位忠實用戶做出了重大貢獻。平心而論，此時向大眾發布新軟體是不合適的。
-   它將被視為**新**軟體，**而不是**_升級_軟體。新的_**LIE 消除**_策略軟體（2 個類別），加上擴展的_**馬可夫鏈彩票**_，加上_**最後一位數位**_和_**delta**_彩票程式將被添加到新的**Bright**軟體包中。如果新軟體發布，該決定將透過新著作和軟體下載頁面公開。最新的：
-   [_**LIE消除：彩券、配對反向彩券策略、號碼頻率**_](https://saliu.com/lie-lottery-strategies-pairs.html)。
-   [_**樂透十年、最後一位、奇偶、低高、跳過的反轉彩券策略**_](https://saliu.com/lie-lotto-strategies-decades.html)。

  _「告訴我謊言...  
告訴我甜蜜的小謊言…”_

<iframe src="https://www.youtube.com/embed/BVUOuwEeVX0" frameborder="0" allowfullscreen=""></iframe>

![This program applies the LIE ELIMINATION reversed lottery strategy feature introduced in Bright lotto software, in combination generators.](https://saliu.com/ScreenImgs/LieIDLotto.gif)

[

## <u data-immersive-translate-walked="c01cc6f4-c9d0-456a-9160-12564fbf3b63" data-immersive-translate-paragraph="1"><span data-immersive-translate-translation-element-mark="1" lang="zh-TW"><span data-immersive-translate-translation-element-mark="1"><span data-immersive-translate-translation-element-mark="1">6. 彩券軟體、策略、系統資源</span></span></span></u>

](https://saliu.com/content/lottery.html)

-   主要[_**樂透、樂透、軟體、策略**_](https://saliu.com/LottoWin.htm)頁面。  
    展示創建免費中獎樂透、彩票策略、基於數學的系統的軟體。取得您的彩券系統或輪盤、最好的彩券、彩券軟體、組合、中獎號碼。
-   [_**樂透、彩票軟體、Excel 電子表格：程式設計、策略**_](https://saliu.com/Newsgroups.htm)。  
    閱讀應用於彩票和彩票軟體、系統和策略開發的 Excel 電子表格的真實分析。將 Excel 分析與作者_Parpaluck_編寫的強大彩票和樂透軟體結合。
-   [_**<u data-immersive-translate-walked="c01cc6f4-c9d0-456a-9160-12564fbf3b63">MDIEditor Lotto WE</u> ：彩票軟體手冊、書籍、電子書、幫助**_](https://saliu.com/MDI-lotto-guide.html)。  
    ~ 也適用於我的整個彩券軟體；加上強力球、超級百萬、歐洲百萬。
-   [_**視覺教程、書籍、手冊：彩票軟體、彩票應用程式、程式**_](https://saliu.com/forum/lotto-book.html)。
-   [**彩票數學、樂透數學、數學、數學**](https://saliu.com/gambling-lottery-lotto/lottery-math.htm)：機率、外觀、重複、親和力、數字從屬關係、樂透輪盤、彩票系統、策略。
-   [_**軟體中的實用彩票和樂透過濾**_](https://saliu.com/filters.html)。
-   [**跳過樂透、樂透、賭博、系統、策略**](https://saliu.com/skip-strategy.html)；包括軟體。
-   [_**樂透、總和、根和、跳過、奇偶、低高、德爾塔的彩票策略**_](https://saliu.com/strategy.html)。
-   [_**彩票實用軟體**_](https://saliu.com/lottery-utility.html)_**：Pick-3、4 Lottery、Lotto-5、6、Powerball、Mega Millions、Euromillions**_ 。
-   [_**彩票策略、基於數字的系統、數位頻率**_](https://saliu.com/frequency-lottery.html)
-   [_**<u data-immersive-translate-walked="c01cc6f4-c9d0-456a-9160-12564fbf3b63">LIE 消除</u>：彩票、配對反向<u data-immersive-translate-walked="c01cc6f4-c9d0-456a-9160-12564fbf3b63">彩票策略、號碼頻率</u>**_](https://saliu.com/lie-lottery-strategies-pairs.html)。
-   [_**<u data-immersive-translate-walked="c01cc6f4-c9d0-456a-9160-12564fbf3b63">LIE消除，樂透幾十年的反向彩票策略</u>，最後一位數字，奇偶，低高，跳過**_](https://saliu.com/lie-lotto-strategies-decades.html)。
-   [_**用於在固定位置產生最喜歡的彩票號碼的樂透組合的軟體**_](https://saliu.com/favorite-lottery-numbers-positions.html)。
-   [**理論、_樂透增量_分析、彩券軟體、策略、系統**](https://saliu.com/delta-lotto-software.html)。
-   [_**樂透十年、最後一位數字、系統、策略、軟體**_](https://saliu.com/decades.html)。
-   [_**馬可夫鏈、追隨者、配對、彩票、樂透、軟體**_](https://saliu.com/markov-chains-lottery.html)。
-   [_**數位群組的樂透軟體：奇數、偶數、低數、高數、總和、頻率、使用者群組**_](https://saliu.com/lotto-groups.html)。
-   [_**彩票、賭博、運動博彩、賽馬、二十一點、輪盤賭的最佳策略**_](https://saliu.com/strategy-gambling-lottery.html)。
-   [_**彩票策略**_](https://forums.saliu.com/lottery-strategies-start.html)中的_「開始是最困難的部分」_ 。
-   [_**關於位置頻率的策略樂透軟體**_](https://forums.saliu.com/lotto-software-position-frequency.html)。
-   下載[**彩券軟體、樂透軟體應用程式**](https://saliu.com/infodown.html)。

![Apply the lottery strategy in reverse: Not-lose to win.](https://saliu.com/HLINE.gif)

**[首頁](https://saliu.com/index.htm)|[搜尋](https://saliu.com/Search.htm)|[新作](https://saliu.com/bbs/index.html)|[賠率，發電機](https://saliu.com/calculator_generator.html)|[內容](https://saliu.com/content/index.html)|[論壇](https://forums.saliu.com/)|[網站地圖](https://saliu.com/sitemap/index.html)**

![This impressive lottery software and strategy are based on the science of logic.](https://saliu.com/images/HLINE.gif)
