---
created: 2025-07-24T21:26:40 (UTC +08:00)
tags: [best,lotto wheels,18 numbers,4in6 guarantee,wheeling,lottery,lotto,software,abbreviated,reduced,systems,numbers,]
source: https://saliu.com/bbs/best-18-number-lotto-wheels.html
author: 
---

# Lotto Wheels 18 Numbers: 4in6, 3 of 3, Best Guarantee Prizes

> ## Excerpt
> These efficient 9,12, 18-number lotto wheels are the best reduced lottery systems, statistically balanced and offering 4 of 6, 3 in 3 minimum guarantee.

---
**_Da Super Lotto Wheeler and Strategist_**

[![<PERSON>, creator of the best lotto wheels and lottery wheeling strategies: 9, 12, 18, 21 numbers.](https://saliu.com/AxiomIon.jpg)](https://saliu.com/membership.html)  

## <u>The Best <i>Lotto Wheels for 9, 12, 18, 21 Numbers</i>: 4-in-6 Minimum Guarantee</u>  
★ ★ ★ ★ ★

## [<u>Super Strategies</u> Based on Lotto Wheeling](https://saliu.com/bbs/best-18-number-lotto-wheels.html#strategy)

By <PERSON>, _Founder of Lottery Wheeling-Software Programming_

![These are the best three lotto wheels for 18 numbers, 4 of 6 guarantee, highly balanced.](https://saliu.com/bbs/HLINE.gif)

-   First captured by the [_WayBack Machine_ (_web.archive.org_)](https://web.archive.org/web/20190527000000*/https://saliu.com/bbs/best-18-number-lotto-wheels.html) on October 6, 2021.

Axiomatic one, I am not a big fan of the lotto wheels. Reason is, the _abbreviated systems_ do just that: They <u>abbreviate</u> or <u>reduce</u> the chances of hitting the jackpot. The lottery wheels perform well when it comes to fulfilling the minimum guarantee. But it must be frustrating to have all 6 winners in your system (pool of numbers to play)... and lose the 6-number lotto jackpot!

I did recommend, however, a few lotto wheels I created myself! They offer a leverage superior to any reduced lottery systems out there. <u>The numbers to play are grouped by frequency (e.g., pairs, triplets).</u> Please read:

-   [_**Create, Make Lotto Wheels in Lottery Wheeling Software or Manually**_](https://saliu.com/lottowheel.html).
-   [_**Lotto Wheeling Software: Fill Lottery Wheels with Player's Picks, Own Numbers**_](https://saliu.com/bbs/messages/857.html).

I present here the three recommended lotto wheels for 18 numbers, _4 in 6_ minimum guarantee. The abbreviated lottery systems are analyzed statistically regarding the frequency of every number in the wheeling. The frequency describes the **balance**: a very important characteristic of a lottery wheel.

A good balance should assure that the system numbers are as <u>evenly distributed</u> as possible. The _9-number_ and _12-number_ systems I created by combinatorial mathematics are **perfectly balanced**. All pointers appear an equal number of times in the system. It was very hard to reach the same feast for 18 numbers, except for the 48-line system. Still, the wheels I publish here are the best-balanced lottery systems for 18 numbers.

The software to analyze the number frequency as you see here was superseded. The old-timers remember it as **Util-6.exe**, part of my 16-bit lottery software distributed in the 1980s and 1990s. _O tempora! O mores!_ The replacement is known now as **SoftwareLotto6.exe**, or best known as **Super Utilities** (main menu of the **Bright** and **Ultimate Software** packages).

### <u>I. 18-Number Lotto Wheel in 45 Lines</u>

Instead of the tedious and error-prone copy-and-paste procedure, download the wheel to your computer via right-click. If you click, the reduced system opens directly in your browser for viewing.

-   [_**Lotto wheel: 18 numbers, 45 tickets**_](https://saliu.com/freeware/Wheel-18-45.46)
-   Minimum assurance: 100% _4 if 6_

![This 18 numbers lottery wheel is built in 45 lines or combinations.](https://saliu.com/ScreenImgs/wheel-18-45.gif)

Each number appears at least 14 times and 17 times at most. The small spread denotes a really good balance. Each number is paired 5 or 6 times with another number (_Hits most-drawn with_). The worst-pairing for each number is a constant: 4 times. The small differences between the hits of the _best pairing_ and the _worst pairing_ (_LEAST drawn with #_) are also a sign of a good balance.

-   This lottery wheel also guarantees _3 of 3_ with a 96% degree of certainty. That is, if you pick correctly only 3 numbers, the system will give you one line with the 3 winners 96% of the time.

### <u>II. 18-Number Lotto Wheel in 48 Lines</u>

-   [_**Lotto Wheel: 18 numbers, 48 lines**_](https://saliu.com/freeware/Wheel-18-48.46)
-   Minimum guarantee: 100% _4 of 6_ and _3 if 3_

![This 18-numbers wheel wheeling system in 48 lines is the very best in the lotto world.](https://saliu.com/ScreenImgs/Wheel-18-48.gif)

The numbers are equally distributed: 16 times each. Each number is paired 6 times with another number (_Hits most-drawn with_). The worst-pairing for each number is also a constant: 4 times. It is a perfect balance. The small difference (2) between the hits of the _best pairing_ and the _worst pairing_ (_LEAST drawn with #_) is also a sign of an excellent balance.

-   This is a workaround of the 45-line lotto wheel you just studied. It now guarantees 100% the condition _3 of 3_. That is, if you pick correctly 3 numbers out of 18, the system will give you <u>at least one line with the 3 winners</u>.
-   Even if you **randomly** pick 18 out of 49 numbers, you hit _at least 3 winners_ once every 2 or 3 lottery drawings.
-   Axios, I'll gift you three <u>great lottery strategies</u> based on this wheel. You might want to consider them, especially if you are in a _syndicate_ (group of players). The systems require 3 \* 48 lottery tickets, so there is a <u>cost</u> involved. The winning chance at winning big is greatly enhanced, however. Otherwise, you might want to play just one strategy at a time. Probably _Strategy A_ is the best.

## <u>Three Strategies with the 18-number Lotto Wheel</u>

### <u>A. Lottery Strategy Based on Frequency and Pairing</u>

-   Run _**FrequencyRank.exe**_ to get the frequency report for your lotto game. You can do the reporting for the past 50 drawings or so.
-   You see the best pairs in the _Hits most drawn with_ header: _1, 2, 3, 4, 5, 8_.
-   We exclude, however, #8 as it appears also in the group of the _**least frequent**_ lotto numbers. We replace it by #6.
-   Those lotto numbers will be your _foundation picks_: the _**most frequent**_ lotto numbers.
-   Here is a real-life example of the 6 most frequent lotto numbers: _1 5 12 27 31 36_ (file _Stats6.REP_).
-   In your _Picks.txt_ file, you place such numbers in the respective positions (_1, 2, 3, 4, 5, 6_); i.e., the first 6 positions in the file.
-   On the main menu of the **_Bright / Ultimate_** software packages: Run function _U = Super Utilities_, then option _F = Frequency reporting by number_. Open your up-to-the date data file and accept the default range of analysis. In my case of a 49-number lotto game, the _parpaluck_ (i.e., range of analysis) is 54.
-   While in the same function _U = Super Utilities_, run now option _2 = Pairs rundown_. The same data file, the same range of analysis (54 drawings).
-   The program creates several files, including _PAIRS6_. It lists all lotto numbers and their respective pairings from the best to the worst.
-   You fill the remaining positions with the _**top 2 pairings**_ of the respective _foundation picks_.
-   That is, positions _7_ and _8_ will be filled with the top-2 pairs of _#1_ (_the most frequent lotto number_ in your analysis). The positions _9_ and _10_ will be filled with the top-2 pairs of _#2_ (the _2nd most frequent lotto number_ in your report); etc.
-   If there are duplicates, move to the 3rd best pairing, where the case; or 4th best pair, or 5th, etc.
-   Make sure you have exactly 18 <u>unique numbers</u> in your _Picks.txt_ file.
-   Here is a real-life file I named _BestFreqPair6_ (instead of _Picks.txt_ since I work with three different strategies). I needed to delete several duplicates. The result was an input file with exactly 18 unique lotto numbers that serve as my picks:
-   _1 5 12 27 31 36 19 23 14 24 9 43 4 38 7 44 10 39_

### <u>B. Lottery Strategy Based on the Best Lotto Pairs</u>

-   While in the same function _U = Super Utilities_, you already ran option _2 = Pairs rundown_. The program created several files, including _Pairing6.SOR_. It lists all lotto pairings in descending order, from the most frequent to the pairs with the worst frequency.
-   I needed to copy and paste some 15 best pairings to get exactly 18 unique numbers. In my case, the top-3 pairs had one number in common:
-   _1 12, 1 19, 1 25_; I needed to delete _#1_ twice.
-   Here is a real-life file I named _BestPair6_ (instead of _Picks.txt_ since I work with three different strategies). I needed to delete several duplicates. The result was an input file with exactly 18 unique lotto numbers that serve as my picks applied to the 48-line lottery wheel. I also sorted the file (not necessary) to better notice the duplicates:
-   _1 3 5 7 8 9 12 14 19 24 25 27 30 33 35 39 40 43_

### <u>C. Lottery Strategy Based on the Best Lotto Triples</u>

-   While in the same function _U = Super Utilities_, you run now option _3 = Triplets rundown_.
-   Open your up-to-the date data file and accept the default range of analysis. In my case of a 49-number lotto game, the _parpaluck_ applicable to triples (i.e., range of analysis) is 638 drawings. If your lotto game doesn't have such a long history, you might try to analyze some 300 draws. Or, use the _D6_ file instead (with real lottery draws on top of simulated drawings). In that case, you can apply the default _parpaluck_.
-   The program creates several files, including _Triple6.SOR_. It lists all lotto triples in descending order, from the most frequent to the triplet with the worst frequency.
-   I needed to copy and paste some 9-10 best trebles to get exactly 18 unique numbers. Of course, some triples always have repeated numbers.
-   Here is a real-life file I named _BestTrip6_ (instead of _Picks.txt_ since I work with three different strategies). I needed to delete several duplicates. The result was an input file with exactly 18 unique lotto numbers that serve as my picks. I also sorted the file (not necessary) to better notice the duplicates:
-   _2 4 6 8 9 11 16 18 22 27 32 34 36 39 40 43 44 47_

-   Be mindful of the _probability to win_ in a random selection of 18 numbers:
-   _6 of 6 in 18 from 49: **1 in 753**_
-   However, [_**selecting lotto numbers based on skips can improve the odds**_](https://saliu.com/bbs/messages/923.html) by a factor of 7. Lottery strategies founded on number _frequency_ and _pairing_ are even more efficient.
-   You can save the costs by _compressing the time_. You can go back some 100 drawings (approx. 750/7) in your history file (_Data-6_). Save the file as _Data-6.2_ Create the reports for frequency, pairings, and triplets with the new file.
-   In _U = Super Utilities_, select option _W = Check for Winners_, then option _2 = Check groups of numbers_. Check your three lotto-picks files, one at a time: _BestFreqPair6_, _BestPair6_, _BestTrip6_.
-   Write down the most recent hit of: _6 of 6_, if not, _5 of 6_. The average of random selection is approximately _50_. The situations may differ among the three files. Let's say, the most recent hits for the three pick files were: 40, 50, 60 drawings back, respectively.
-   You will tweak your _Data-6_ file three times. Delete the last 40 draws and save as _Data-6.21_; use it with _Strategy A_. Delete 10 more drawings and save as _Data-6.22_ (i.e., deleting a total of 50 drawings from your latest _Data-6_ file); use it with _Strategy B_. Delete 10 more draws and save as _Data-6.23_ (i.e., deleting a total of 60 drawings from your latest _Data-6_ file); use it with _Strategy C_.
-   These are only examples; you may register different figures.
-   Mathematically and statistically, you should hit sooner _5 of 6_, even _6 of 6_.
-   Still, the main drawback of lotto wheeling is always haunting the player. Even if you hit _6 of 6 in 18_, you won't necessarily win the jackpot. The minimum guarantee occurs more frequently. Nevertheless, playing the best _pairs_ or _triples_ places the lotto numbers in more favorable situations to get together in groups that beat the minimum guarantee.
-   Be sure to read the instructions regarding the [_**LottoWheeler**_ software](https://saliu.com/bbs/messages/857.html).
-   You might want also to read the info regarding _**FrequencyRank.exe**_, plus the pairing strategy, one of the pillars of this website.
-   The [**_Bright_**](https://saliu.com/bright-software-code.html) and [**_Ultimate_**](https://saliu.com/ultimate-software-code.html) software packages embed all necessary information.
-   Don't mention it!

### <u>III. 18-Number Lotto Wheel in 51 Lines</u>

-   [_**Lotto Wheel: 18 numbers, 51 tickets**_](https://saliu.com/freeware/Wheel-18-51.46)
-   Minimum guarantee: 100% _4 if 6_

![This 18-numbers lotto wheel in 51 lines is one of the very best for lottery wheeling players.](https://saliu.com/ScreenImgs/lotto-system-51.gif)

Each number appears at least 16 times and 19 times at most in 51 combinations. The small spread denotes a very good balance. Each number is paired 6 or 7 times with another number (_Hits most-drawn with_). The worst-pairing for each number hits 3 or 4 times. The small differences between the hits of the _best pairing_ and the _worst pairing_ (_LEAST drawn with #_) are also a sign of a good balance.

The balance always increases the chance of hitting the first prize since there is no bias toward certain numbers. There are pairings with more hits compared to the first lotto wheel. That factor also improves the degree of certainty DC of hitting the top lotto prize.

-   This lotto wheel does not guarantee _3 of 3_. It is strongly biased toward the _4 of n_ winning situations.

![The lotto wheels are text files with system or point numbers that offer a minimum prize guarantee.](https://saliu.com/bbs/HLINE.gif)

You can download these wheels and other systems from the freeware area of this grandiose site. Right-click to download; left-clicking will open the text files in your browser for viewing.

-   [_**Lottery Wheel: 9 numbers, 3 lines**_](https://saliu.com/freeware/Wheel-9-3.46) — Ion Saliu's [_first winning lotto system_](https://saliu.com/bbs/messages/532.html) played in the United States
-   Minimum assurance: 100% _4 if 6_ and, better still, _4 if 5_; 67% _3 in 3_
-   [_**Combinatorial Lotto Wheel: 12 numbers, 6 tickets**_](https://saliu.com/freeware/Wheel-12-6.46)
-   Minimum guarantee: 100% _4 if 6_; 55% _3 in 3_
-   Also known as _**the perfect lotto wheel**_ (possible only in 6-number lottery games; e.g., _6/49_, _6-59_)
-   [_**Perkis-LottoLogix Wheel: 18 numbers, 48 combinations**_](https://saliu.com/freeware/WheelPerkis-18-48.46)
-   Minimum assurance: 100% _4 if 6_ and _3 of 3_
-   [_**Perkis-LottoLogix Wheel: 21 numbers, 96 lines**_](https://saliu.com/freeware/WheelPerkis-21-96.46)
-   An abbreviated lottery system for 21 numbers for 6-number lotto games.
-   Minimum guarantee: 100% _4 if 6_ and _3 of 3_
-   Modified versions of the _**C**_ strategy appear in the _RGL_ lottery newsgroup. They apply to two different lotto wheels which have even better balances statistically.
-   _https://groups.google.com/g/rec.gambling.lottery/c/O6MrMoK0Pwg_
-   _Super 18,6,4,6 42 Wheel_. Scroll down till you see my strategies, plus the statistical reports (dated in the year of grace 2021).
    -   A version of the effective strategy is also published on this very site:
    -   [_**Lottery Triples Strategies with Lotto Wheels**_](https://saliu.com/lotto-triples.html).
-   <u>This is all you ever need to play wheels in 6-number lotto games.</u>
-   Do not go beyond 21 numbers. It is the limit of the cost-revenue balance. The _4 of 6_ prize can cover the cost, plus it can offer a small profit.
-   The best cost-profit balance is offered by the 12-number wheel I constructed by the combinatorial method of 4 3-number groups.
-   The best _reduced lottery systems_ to always consider are _**Saliu-Barbayev-type**_ wheels. That is, for _12-numbers-in-6-lines_ and _18-numbers-in-48-tickets_.
-   The _**Saliu-Lotto-Wheel**_ is characterized by all parameters being _**multiples of 3 and 6**_: _12-6_, _18-48_. By the same token, the 21-number wheel is not _**Saliu-Barbayev**_ as _21_ is not divisible by _6_.
-   You'll be best served by playing the two _**Saliu-Lotto-Wheels**_ aforementioned. Furthermore, the _12-numbers-in-6-lines_ abbreviated system is the only _**perfect lotto wheel**_; the 6 combinations to play are very close to the odds of hitting _4 of 6 in 12_ (_1 in 5_). The same odds are _1 in 19_ for 18 numbers | _1 in 35_ for 21 numbers. As you notice, the discrepancies grow (between the _number of lines required_ and the _odds_ expressed as _1 in N_).
-   On the other hand, there are more two 3-number groups when wheeling 21 lotto numbers compared to 18: 21 versus 15. The 21-number wheel consists of 7 groups, versus 6 groups in the case of wheeling 18 lotto numbers. C(7, 2) = 21; C(6, 2) = 15; the difference is 40% in favor of the 21-number wheel. A higher cost, for sure, but a significantly better chance for the big jackpot.
-   The 3-number groups are crucial in improving the chance to hit the jackpot. If the 6 winning numbers in the drawing fall in any 2 groups, the player wins the lotto jackpot automatically!

And don't forget to download the best software for lottery wheeling: **LottoWheeler**. It easily, quickly and accurately converts the _theoretical wheel_ to _tickets_ based on your _picks_ (your chosen lotto numbers for actual play). Don't do it manually (a.k.a. the _rgl Kotkoduck-Perkis way_). The paper-and-pencil method is prone to costly errors!

![Lotto Wheeler is the best software to convert wheels or reduced systems to real lottery picks.](https://saliu.com/ScreenImgs/lotto-wheeler.gif)

## [<u>Resources in Lottery Software, Systems, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)

It lists the main pages on the subject of lottery, lotto, software, wheels and systems.

-   [**Lotto Wheels**](https://saliu.com/lotto_wheels.html) _**for Lotto Games Drawing 5, 6, 7 Numbers**_.  
    The most advanced theory of lotto wheels or reduced lottery systems. Get also original lotto wheels for lotto games drawing 5, 6, or 7 numbers: Balanced and randomized.
-   The myth of [_**Lotto Wheels, Abbreviated, Reduced Lottery Systems**_](https://saliu.com/bbs/messages/11.html).  
    Statistical analysis of real lottery data proves the negative effect of playing static (unbalanced or split) lotto wheels.
-   WHEEL-632 available as the [_**Best On-The-Fly Wheeling Software**_](https://saliu.com/bbs/messages/wheel.html) — applies real lottery filtering.
-   [_**Software to Verify Lotto Wheels**_](https://saliu.com/check-wheels.html) for missing combinations and generate lotto 5, 6 abbreviated (reduced) systems.
-   [_**Copyright Lottery Numbers, Combinations, Lotto Wheels, Mathematical Relations, Formulas, Equations**_](https://saliu.com/copyright.html).
-   [_**Balanced Lotto Wheels, Lexicographical Order Lottery Wheeling**_](https://saliu.com/bbs/messages/772.html).
-   [_**Check WHEEL System, Lotto Wheels Winners**_](https://saliu.com/bbs/messages/90.html).
-   [_**Powerball Wheels**_](https://saliu.com/powerball_wheels.html).
-   [_**Mega Millions Wheels**_](https://saliu.com/megamillions_wheels.html).
-   [_**Euromillions Wheels**_](https://saliu.com/euro_millions_wheels.html).

![The best 18 number lotto wheels are free to download from this web site with hundreds of systems.](https://saliu.com/bbs/HLINE.gif)

**[Home](https://saliu.com/index.htm) | [Search](https://saliu.com/Search.htm) | [New Writings](https://saliu.com/bbs/index.html) | [Software](https://saliu.com/infodown.html) | [Odds, Generator](https://saliu.com/calculator_generator.html) | [Contents](https://saliu.com/content/index.html) | [Forums](https://forums.saliu.com/) | [Sitemap](https://saliu.com/sitemap/index.html)**

![The balanced 18-numbers lotto wheel can hit the lotto jackpot better than any other systems.](https://saliu.com/bbs/HLINE.gif)
