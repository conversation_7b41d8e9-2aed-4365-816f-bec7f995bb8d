include("src/WonderGridLotterySystem.jl")
using .WonderGridLotterySystem

println("Testing File Management System")
println("=" ^ 40)

# Test reading DATA5 file
fm = FileManager()
data = read_data5_file(fm, "data/fan5.csv")
println("Successfully read $(length(data)) lottery draws from fan5.csv")
println("Date range: $(data[end].draw_date) to $(data[1].draw_date)")

# Test writing DATA5 file
println("\nTesting file writing...")
test_data = data[1:10]  # Take first 10 draws
test_file = "test_output.csv"
write_data5_file(fm, test_file, test_data)
println("Successfully wrote $(length(test_data)) draws to $test_file")

# Verify the written file
verification_data = read_data5_file(fm, test_file)
println("Verification: Read back $(length(verification_data)) draws")

# Check if data matches
matches = true
for i in 1:length(test_data)
    if test_data[i].numbers != verification_data[i].numbers || 
       test_data[i].draw_date != verification_data[i].draw_date
        matches = false
        break
    end
end
println("Data integrity check: $(matches ? "PASSED" : "FAILED")")

# Test generating simulated data for merging
println("\nTesting simulated data generation...")
simulated_data = generate_test_data(100)
sim_file = "simulated_data.csv"
write_data5_file(fm, sim_file, simulated_data)
println("Generated and wrote $(length(simulated_data)) simulated draws")

# Test merging files
println("\nTesting file merging...")
merged_file = merge_data_files(fm, test_file, sim_file)
merged_data = read_data5_file(fm, merged_file)
println("Merged file contains $(length(merged_data)) total draws")
println("Expected: $(length(test_data) + length(simulated_data)) draws")

# Clean up test files
rm(test_file, force=true)
rm(sim_file, force=true)
rm(merged_file, force=true)
println("\nTest files cleaned up successfully")

# Test large dataset handling
println("\nTesting large dataset performance...")
large_data = generate_test_data(10000)
large_file = "large_test.csv"

start_time = time()
write_data5_file(fm, large_file, large_data)
write_time = time() - start_time

start_time = time()
read_large_data = read_data5_file(fm, large_file)
read_time = time() - start_time

println("Large dataset performance:")
println("  Write $(length(large_data)) draws: $(round(write_time, digits=3)) seconds")
println("  Read $(length(read_large_data)) draws: $(round(read_time, digits=3)) seconds")
println("  Data integrity: $(length(large_data) == length(read_large_data) ? "PASSED" : "FAILED")")

# Clean up
rm(large_file, force=true)
println("Large test file cleaned up")