#!/usr/bin/env julia

"""
Demonstration script for Wonder Grid Configuration System
Shows how to use the configuration and parameter management features
"""

include("src/configuration.jl")

"""
Demonstrate configuration system features
"""
function demo_configuration_system()
    println("🎯 Wonder Grid Configuration System Demo")
    println("=" ^ 60)
    
    # Demo 1: Create default configuration
    println("\n📋 Demo 1: Default Configuration")
    println("-" ^ 40)
    
    config = WonderGridConfig()
    println("✅ Default configuration created")
    display_configuration(config)
    
    # Demo 2: Configuration presets
    println("\n🎛️  Demo 2: Configuration Presets")
    println("-" ^ 40)
    
    presets = ["beginner", "standard", "advanced", "performance"]
    
    for preset in presets
        println("\n$preset Configuration:")
        preset_config = get_configuration_preset(preset)
        
        println("  Analysis Depth: $(preset_config.analysis_depth)")
        println("  LIE Elimination: $(preset_config.include_lie_elimination)")
        println("  Parallel Processing: $(preset_config.enable_parallel_processing)")
        println("  Output Formats: $(join(preset_config.output_format, ", "))")
    end
    
    # Demo 3: Configuration validation
    println("\n✅ Demo 3: Configuration Validation")
    println("-" ^ 40)
    
    # Valid configuration
    valid_config = get_configuration_preset("standard")
    is_valid, errors = validate_configuration(valid_config)
    println("Valid configuration check: $(is_valid ? "✅ PASSED" : "❌ FAILED")")
    
    # Invalid configuration
    invalid_config = WonderGridConfig()
    invalid_config.key_number = 50  # Invalid
    invalid_config.lie_threshold = 1.5  # Invalid
    
    is_valid, errors = validate_configuration(invalid_config)
    println("Invalid configuration check: $(is_valid ? "❌ UNEXPECTED PASS" : "✅ CORRECTLY FAILED")")
    
    if !isempty(errors)
        println("Validation errors found:")
        for (i, error) in enumerate(errors)
            println("  $i. $error")
        end
    end
    
    # Demo 4: Configuration file operations
    println("\n💾 Demo 4: Configuration File Operations")
    println("-" ^ 40)
    
    demo_config_file = "demo_config.txt"
    
    # Create configuration manager
    manager = ConfigurationManager(demo_config_file)
    
    # Customize configuration
    manager.config.key_number = 13
    manager.config.analysis_depth = "comprehensive"
    manager.config.output_format = ["csv", "json"]
    manager.config.include_lie_elimination = true
    manager.config.lie_threshold = 0.05
    
    # Save configuration
    save_configuration!(manager)
    println("Configuration saved to: $demo_config_file")
    
    # Load configuration in new manager
    manager2 = ConfigurationManager(demo_config_file)
    println("Configuration loaded from: $demo_config_file")
    
    # Verify loaded values
    println("Verification:")
    println("  Key Number: $(manager2.config.key_number)")
    println("  Analysis Depth: $(manager2.config.analysis_depth)")
    println("  Output Formats: $(join(manager2.config.output_format, ", "))")
    println("  LIE Threshold: $(manager2.config.lie_threshold)")
    
    # Demo 5: Key number selection methods
    println("\n🔑 Demo 5: Key Number Selection Methods")
    println("-" ^ 40)
    
    # Random selection
    random_config = WonderGridConfig()
    random_config.key_selection_method = "random"
    
    println("Random key selection:")
    for i in 1:3
        key_num = interactive_key_selection(random_config)
        println("  Attempt $i: Key $key_num")
    end
    
    # Optimal selection
    optimal_config = WonderGridConfig()
    optimal_config.key_selection_method = "optimal"
    
    println("\nOptimal key selection:")
    key_num = interactive_key_selection(optimal_config)
    println("  Selected key: $key_num")
    
    # Sequential selection
    sequential_config = WonderGridConfig()
    sequential_config.key_selection_method = "sequential"
    
    println("\nSequential key selection:")
    key_num = interactive_key_selection(sequential_config)
    println("  Selected key: $key_num")
    
    # Demo 6: Configuration comparison
    println("\n📊 Demo 6: Configuration Comparison")
    println("-" ^ 40)
    
    configs = [
        ("Beginner", get_configuration_preset("beginner")),
        ("Advanced", get_configuration_preset("advanced")),
        ("Performance", get_configuration_preset("performance"))
    ]
    
    println("Configuration Comparison Table:")
    println("Setting                | Beginner  | Advanced      | Performance")
    println("-" ^ 65)
    
    settings = [
        ("Analysis Depth", :analysis_depth),
        ("LIE Elimination", :include_lie_elimination),
        ("Parallel Processing", :enable_parallel_processing),
        ("Show Progress", :show_progress),
        ("Cache Enabled", :cache_enabled)
    ]
    
    for (setting_name, field) in settings
        values = [string(getfield(config[2], field)) for config in configs]
        padded_values = [rpad(val, 13) for val in values]
        println("$(rpad(setting_name, 22)) | $(join(padded_values, " | "))")
    end
    
    # Clean up demo file
    if isfile(demo_config_file)
        rm(demo_config_file)
        println("\n🧹 Demo configuration file cleaned up")
    end
    
    println("\n✅ Configuration System Demo Complete!")
    println("=" ^ 60)
end

"""
Interactive configuration demo
"""
function interactive_demo()
    println("\n🎮 Interactive Configuration Demo")
    println("=" ^ 50)
    
    println("This demo will show you how to:")
    println("1. Create custom configurations")
    println("2. Validate settings")
    println("3. Save and load configurations")
    println("4. Use different presets")
    
    print("\nWould you like to run the interactive demo? (y/n): ")
    response = strip(readline())
    
    if lowercase(response) == "y"
        println("\n🚀 Starting interactive configuration...")
        
        # This would normally call interactive_configuration_setup()
        # but for demo purposes, we'll simulate it
        println("📝 Simulating interactive setup...")
        
        config = get_configuration_preset("standard")
        config.key_number = 7
        config.analysis_depth = "comprehensive"
        
        println("✅ Configuration created with:")
        println("  Key Number: $(config.key_number)")
        println("  Analysis Depth: $(config.analysis_depth)")
        
        # Validate
        is_valid, errors = validate_configuration(config)
        if is_valid
            println("✅ Configuration is valid!")
        else
            println("❌ Configuration has errors:")
            for error in errors
                println("  - $error")
            end
        end
        
        println("\n💡 In a real scenario, you would call:")
        println("   config = interactive_configuration_setup()")
        println("   This provides a guided setup process.")
        
    else
        println("👋 Skipping interactive demo")
    end
end

# Run the demo
if abspath(PROGRAM_FILE) == @__FILE__
    demo_configuration_system()
    interactive_demo()
end