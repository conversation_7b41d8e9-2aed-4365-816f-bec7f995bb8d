---
created: 2025-07-24T21:26:40 (UTC +08:00)
tags: [bookie,lottery,software,bookie lotteries,odds,payouts,combinations,filters,lotto,pairs,programs,quadruples,quintets,sextet,singles,strategy,triples,]
source: https://saliu.com/bookie-lottery-software.htm
author: 
---

# Bookie <PERSON>: Odds, Payouts, Systems, Software

> ## Excerpt
> The bookie lotteries offer straight bets on 1 to 5 lotto numbers. The lottery bookies have better odds or payouts compared to traditional lotteries.

---
**_Da 'Homo Computing Beast' substituted sports for wars_**

[![<PERSON> is the founder of football betting mathematics, sports philosophical science.](https://saliu.com/AxiomaticIon.jpg)](https://saliu.com/membership.html)  \*  

## <u>An Analysis of <i>Bookie Lottery</i>: Odds and Payouts, Special Software</u>

## By <PERSON>, ★ _Founder of Lotto Mathematics_

![Bookie lotteries, lottery bookies pay better than state lotteries but playing online is unreliable.](https://saliu.com/HLINE.gif)

Published in September, year of grace 2010; last update February 2019.  
First captured by the _WayBack Machine_ (_web.archive.org_) on January 12, 2020.

I started off this topic incidentally in July this year, on the previous message board. It was my response to a discovery by a loyal member of my online communities and knowledgeable user of my lotto software: Attila David.

Since I decided to close the old message board, I relocated material from it to my new vBulletin forums. (That forum was also closed in 2014.) The thread, remembered by quite a few members, was entitled: _"Lotto Strategy Error: Pairs, Triples, Quads, Quints"_.

New lotto software was requested and I responded by writing and offering new lottery software: _"Breaking Lotto Combinations into Pairs and Triples"_. The new software at that time was named **BreakDownNumbers.exe**.

Attila David had written in-house software that reported the skip of every pair of a lotto drawing. To my surprise, some lotto pairs skip over 100 draws, even 200 draws... how about more than 300 drawings sometimes! My lotto software (**Report6.exe** in the **Bright6** package) does similar reports, but it only shows the lowest skip of a (sub)group of lotto numbers. That's the minimum level of the respective lottery filter.

For your info, these are the filter names in my lotto software and their corresponding subgroups of numbers –  
~ _TWO_, representing the lotto pairs;  
~ _THREE_, representing the lotto triples;  
~ _FOUR_, representing the lotto quadruples.

I noticed in Attila's reports that 4 to 8 pairs, in each draw, had skips larger than 80. So, I surmised it would be a good lottery strategy to set the minimum level of the TWO filter to 80. Let the sequential lotto generator **Lexico6.exe** generate all combinations for that filter setting. I used in my test a results file (real draws) from Romania's 6-49 lotto game.

In other words, the program eliminated ALL pairs that came out in the last 80 draws. That is, it generated only combinations with pairs that skipped over 80 draws. The data file with the results in Romania 6-49 lotto generated 6 combinations that passed the restriction (filter). Setting the filter to 100 doesn't generate anything. Does it mean the next draw will have short skips for all pairs? Possible...

I noticed also pairing skips above 200. Obviously, there should be even more triples with skips longer than 200 draws. Therefore, setting the _THREE\_1_ filter to a minimum of 200 would be pretty safe. However, only one lotto combination was generated!

That first test failed. Actually, we didn't have all correct lotto results at that time. We noticed that only 5 lotto pairs had skips larger than 80 in the next drawing. Obviously, such a filter setting (_TWO\_1_\_minimum = 80) cannot be successful each and every lottery drawing!

Then, another loyal member of my previous forums, Adam, came up with another idea.

_“Here is the idea I'm thinking of trying out, however I got no software to implement it. Nearly anything is worth trying when it comes to shooting for a jackpot._

_I would like to be able to break combinations I generated for lotto-6 after some preliminary filtering into lotto pairs and triples. Then be able to strip them of duplicate ones. Finally make all 6-number combinations that might come from them. For instance quite a simplistic example where it could be done by hand: I got 1 6-number combination that would be broken into 15 pairs and 20 triples which finally could be turned into regular 6/49 combos.”_

It made a lot of sense. An output of multiple lotto combinations will, most likely, have duplicate subgroups. We can control costs better by generating the subgroups first (pairs, trips, quads), and then purging the duplicates. Thus we will play only unique groups of numeros. That's exactly what my new lotto software, **BreakDownNumbers.exe**, does.

Next, it was my turn to inquire on new lotto software. I wanted to expand the concept from pairs to triples and quadruples. As I said, I wanted:

_“Lotto software to plot the skip of every pair, draw by draw, as in Attila David's software. But that should be only the first vertical bar of the report. Next to it should be the triples with the skips, and then the quads._

_That's what I started to do. The concept is easy, but it takes attention to detail._

_First, do the pairs report and save the results in an array or to a disk file. Second, do the triples report and save the results in an array or to a disk file. Third, do the quads report and save the results in an array or to a disk file._

_The most difficult task is to place the three reports in a single one. Thus, you can see that draw #1 had 5 pairs with skips larger than 80, had 4 triples with skips larger than 150, and had quads with skips larger than 500 (or so; I don't know the exact results yet for trips and quads)._

_The winning reports in **Bright6** only show the shortest skip for pairs, trips, quads, etc._

_There are skilled programmers here, to be sure. I'd be happy if someone beat me in this regard and come up with software like I am talking about here.”_

The same Attila David was the first to write such lotto software. But, then again, Attila doesn't write commercial software. He doesn't even compile his programs to standalone executables. But the reports of his new lotto software looked correct. Attila even sent me the source code (in C). I was taken by some surprises after my return from Romania this past July. All of a sudden, among other things, I needed a new car. I spent a long time with that chore... The lotto software that I wanted to write was pushed to the back burner.

Today, however, I was able to write the new lotto software to my liking. The new program is named **PairsTripsQuads6.EXE** ~ version 1.0 ~ September 2010. The report looked exactly like I wanted... and then I upgraded them. I'll show screenshots only for the latest versions.

I noticed quite a few situations when the _quads_ (the _FOUR_ filter) were larger than 10000, even 20000. I did another test using the quad as the lotto strategy. Again, I ran _**Lexico6.exe**_ with this filter setting: _FOUR-1_\_minimum = 10000. The run takes longer than the TWO case. It took 2 hours for my adequately fast PC to generate 772 6-number combinations. I didn't generate the combos to disk, so we was unable to break the 6-number lines into pairs, trips, or quads. But it should be an easy task for anybody.

I know, many will start strategizing right away. I think now that choosing _TWO\_1_\_minimum = 80 was too tight. It is very important that more than half of the subgroups (e.g. _pairs_) should pass the filter setting. The occasions are rare when 8 of the 15 lotto pairs in a draw satisfy that filter setting (_TWO\_1_\_minimum = 80). If more than half of the subgroups pass the filter setting, chances are good that we get at least one of the winning subgroups.

From what I saw (cursory look), I noticed that _FOUR-1_\_minimum = 10000 occurs in quite a few lotto drawings. In a good number of cases, at least 8 of the quads of each draw show levels above 10000.

And, of course, a strategy is better when we combine filters. And, finally, this type of lotto strategy is better suited for bookie lotteries.

I nicely upgraded **PairsTripsQuads6.exe**. In fact, there is a new program, named ****LottoGroupSkips6****. It analyzes ALL 6 number groups in a lotto-6 game: _Ones, pairs, triples, quadruples, quintets,_ and the _sextet_. Incidentally, these 6 parameters should also serve as filters in a lotto program that generates 12-number combinations while working with a 6-number lotto results file.

Here is the beginning of a report:  

![Lotto strategy for 6 numbers in 12-number combinations of singles to sextets.](https://saliu.com/ScreenImgs/lotto-6of12.gif)

Look at a draw like #3. The _quints_ can be over 200000 in 4 out of 6 (over half). The _sextet_ is over 9 million; _Quad_ is over 1000 (minimum) and _Trip_ is over 100 (minimum). Could be a very good strategy not only for bookie lotteries!

The lotto programs belong to software category 5.2 on this Web page: [_**Lottery Software: Utilities, Tools, Lotto Wheels**_](https://saliu.com/free-lotto-tools.html).

I created a new program for 5-number lottos: ****LottoGroupSkips5.exe****. It analyzes <u>all</u> 5 number groups in a lotto-5 game: _Ones_, _pairs_, _triples_, _quadruples_, and the _quintet_. Incidentally, these 5 parameters should also serve as filters in a lotto program that generates 10-number combinations while working with a 5-number lotto results file.

An important clarification: _ONES_ in **LottoGroupSkips5** is calculated differently from the _ONE_ filter in **Bright5.exe**. _ONE_ filter in _**Bright5**_ adds 1 (+1) and deducts 1 (-1) from each number in a lotto draw. The _ONES_ in **LottoGroupSkips5** is identical to _Any1_ (in the _MD_ filter section of _**Bright5**_; also in **MDIEditor and Lotto WE**).

The last parameter (_Quintet_) does not appear in **Bright5.exe**. There is a filter in **MDIEditor and Lotto WE** named _Past Draws_ (right bottom corner on the input form).

Here is the beginning of a report:  

![Lotto Strategy for 5 numbers in 10-number combinations of singles to quintets.](https://saliu.com/ScreenImgs/lotto-quintets.gif)

The lotto-5 program belongs to software category 5.2 on this Web page:

[_**Lottery Software: Utilities, Tools, Lotto Wheels**_](https://saliu.com/free-lotto-tools.html).

## <u>New Lotto Software That Generates 12-Number Combinations</u>

Finally, it's done! I succeeded to write that complex lotto software that generates 12-number combinations based on data files (real drawings) in 6-number lotto games. The 12-number lotto combinations are then wheeled to 6 numbers with a special 12-number lotto wheel (_4 of 6_ minimum guarantee).

The new lotto generating program is named **Combine6-12.exe**. The programs are bundled in a package named **Bright12.exe**. I left in the bundle all the programs incorporated in **_Bright6_**. I thought it convenient to do some lotto calculations or combination generating in the same interface, instead of hunting in different software packages.

I wrote also one of my best ever Web pages dedicated to lottery software strategies. It is the most thorough presentation of a lotto software package. Please read: [_**Lotto Strategy, Software: 12-Numbers Combinations in 6-Number Lotto Games**_](https://saliu.com/12-number-lotto-combinations.html).

After I succeeded to write that complex lotto software that generates 12-number combinations based on data files (real drawings) in 6-number lotto games... I expanded the concept to pick-5 lotto games. I finalized complex lotto software that generates 10-number combinations based on data files (real drawings) in 5-number lotto games. The 10-number lotto combinations are then wheeled to 5 numbers with a special 10-number lotto wheel (_˜3 of_ 5 minimum guarantee).

The new lotto generating program is named **Combine5-10.exe**. The programs are bundled in a package named **Bright15.exe**. I left in the bundle all the programs incorporated in **Bright5**. I thought it convenient to do some lotto calculations or combination generating in the same interface, instead of hunting in different software packages.

Why _Bright15_? Well, 5 + 10 = 15. Remember also, **_B15_** is a vitamin extremely beneficial to humans. It is known as the _vitality_ or _longevity vitamin_.

Please visit the Web page dedicated to this new lotto 5 software package: [_**Lotto Strategy, Software: 10-numbers Combinations Wheeled to 5-number Lotto Games**_](https://saliu.com/lotto-10-5-combinations.html).

I thought it might be of interest to those of you who play 6/49. I've found the following filter settings quite promising:

_Quads_: 200  
_Quintets_: 2,500  
_Sextets_: 50,000

You have to let the program run for quite some time, for example for the night. Chances are good that you will have generated 10-15 combinations by the morning.

## <u>Important Update to Bright12.EXE, Bright15.EXE</u>

This update fixes a serious error in **Combine6-12.exe** and **Combine5-10.exe**. The error occurred when the user wanted to generate the lotto combinations to disk files. I overlooked something of great importance: The output file was opened, then closed before the generating even started!

I already uploaded the update (the entire **Bright12** and **Bright15** packages). They can be downloaded immediately by the registered members of my software download site.

Again, as I wrote about before, Attila David thought that he wanted a clustering program like the **_shuffle_** option in my **Combine6.exe** (in _**Bright6**_). Attila wanted the program to generate square clusters or matrices. That works only with lotto games where the largest number is a perfect square (25, 36, 49, 64). Of course, there are only two viable options: 6-by-6 = 36 and, especially, 7-by-7 = 49.

I did write that lotto program easily. I only had to delete lots of statements from **Combine6-12**. I did a better job at testing. That's how I discovered the errors in **Combine6-12.exe** and **Combine5-10.exe**. The program only works with the 6 / 49 lotto game and nothing else. The filters are the same as in **Combine6-12.exe**, except for the _ONES_ (_Singles_) filter. That's because we must play all 49 lotto numbers in the game. This new lotto program, **Cluster49-7.exe**, appears to be really faster compared to the shuffle generating in **Combine6.exe**.

For now, I'll post here a few clusters generated by **Cluster49-7** for the _SEXTET_ set to 200000 (two hundred thousand). I'll do some more testing and then package everything in a _**Bright49**_. There is only one new program, really. But, again, it is convenient to have other programs handy. Besides, I wanna make it harder for some registered users who are too generous with my efforts (they share with friends my software!)

```
<span size="5" face="Courier New" color="#c5b358"> 23  28  38  36   4  39  44
 37   8  17  43  31  15  42
 12  33   6  11  40  29  30
 18  49   9  45  35   1  13
 21  47   2  22   3  20   7
 26  48  16  10  41  14  46
 32  19  24  34   5  25  27

 35  28  39  38  47   9  26
 17   7  19  45  40  11  34
 10  24  14  12   1  18  44
 46  15  29   4  30  20  23
  8   5  49  16  27  32  21
 48  36  43  33   6   3  25
 22  42  13  41   2  37  31
 
 48  17  20   4  10  33  37
 44  41  26  38  24  29  39
 49   5  47  42  13  31   6
 35   8  36  23  22   7   3
 16  14   9  43  34  27  11
 40  15  46  21   1  19  25
 32  18  45   2  12  30  28

 22  39   7  20  40  17  21
 30  38  13   9  14  37  49
 19   1  26   5  18  31  41
 27  42  28   3  32  16  25
 45  35  33   2  23  12   4
 29   6   8  15  43  34  46
 24  44  48  47  36  10  11
</span>
```

I succeeded to write that complex lotto software that generates 7-by-7-number matrices (perfect squares) based on data files (real drawings) in 6 / 49 lotto games.

The new lotto generating program is named **Cluster49-7.exe**. The programs are bundled in a package named **_Bright49.exe_**. I left in the bundle all the programs incorporated in **Bright6.exe**. I thought it convenient to do some lotto calculations or combination generating in the same interface, instead of hunting in different software packages.

Please read the Web page dedicated to this 6-49 lotto software package: [_**Special Lotto Software: 7-by-7-Number Combinations in Lotto 6 / 49 Games**_](https://saliu.com/7by7-lotto-6-49.html).

From there, registered members can download the _**Bright49**_ lotto software.  

-   <u><b>2019 Update</b></u>
-   The **_Bright12, Bright15, Bright49_** packages are no longer offered as standalone. They are incorporated into their respective **_Bright / Ultimate Software_** integrated collections. They are accessed via **menu III**.
-   The LottoGroupSkips programs are shown as function **R: Generate BRIGHT-12/(BRIGHT-15) Report**. These reports are needed for the discovery of number-groups with very <u>high skips</u> AND how many times such skips occur.
-   Instead of generating full 5/6-number lotto combinations, the player can more easily use the _**Super Utilities**_ functions of the **_Bright / Ultimate Software_** packages.
-   For example, a _triplet_ (3-number group) skips even 5000 draws (including simulated ones) in a 6/49 lotto game. I saw occurrences of 15 in 100 lottery drawings. The function _3: Triplets Rundown_ generated a report showing around 65 _triplets_ that came out 0 times (no appearance) in the "last 5000 draws". Go to the end of the _SOR_ (sorted) report file to count the 3-number groups with 0 frequencies.
-   Playing those _triplets_, without any additional filtering, assures a good profit with every reputable bookie lottery operator. You need first to check out as many bookie lotteries as possible!

Indubitably, the bookies offer superior odds or payouts. From another perspective, the bookie lotteries offer far better payouts for the same odds.

### Straight Bets

<u>39 Ball Game – 6 numbers drawn</u>

1 ball correct 4/1  
2 balls correct 40/1  
3 ball correct 375/1  
4 balls correct 3750/1  
5 balls correct 50,000/1

<u>39 Ball Game – 7 numbers drawn</u>

1 ball correct 3/1  
2 balls correct 29/1  
3 ball correct 199/1  
4 balls correct 1599/1  
5 balls correct 14,999/1

<u>42 Ball Game – 6 numbers drawn</u>

1 ball correct 6/1  
2 balls correct 56/1  
3 ball correct 573/1  
4 balls correct 7461/1  
5 balls correct 7,461/1

<u>45 Ball Game – 6 numbers drawn</u>

1 ball correct 5/1  
2 balls correct 54/1  
3 ball correct 575/1  
4 balls correct 6,500/1  
5 balls correct 100,000/1

<u>45 Ball Game – 7 numbers drawn</u>

1 ball correct 4/1  
2 balls correct 35/1  
3 ball correct 300/1  
4 balls correct 3,000/1  
5 balls correct 30,000/1  

<u>49 Ball Game – 5 numbers drawn</u>

1 ball correct 7/1  
2 balls correct 89/1  
3 ball correct 1,299/1  
4 balls correct 22,499/1  
5 balls correct 99,999/1

<u>49 Ball Game – 6 numbers drawn</u>

1 ball correct 6/1  
2 balls correct 58/1  
3 ball correct 650/1  
4 balls correct 8,000/1  
5 balls correct 150,000/1

<u>49 Ball Game – 7 numbers drawn</u>

1 ball correct 5/1  
2 balls correct 38/1  
3 ball correct 329/1  
4 balls correct 4,500/1  
5 balls correct 50,000/1

<u>59 Ball Game – 6 numbers drawn</u>

1 ball correct 7/1  
2 balls correct 85/1  
3 ball correct 1,250/1  
4 balls correct 17,000/1  
5 balls correct 150,000/1

<u>59 Ball Game – 7 numbers drawn</u>

1 ball correct 6/1  
2 balls correct 55/1  
3 ball correct 600/1  
4 balls correct 7,500/1  
5 balls correct 75,000/1

I made a comparison between the _Bet365_ bookie (one of the largest betting operators in the world) and the 6/59 lotto game of New York State Lottery (NYSL). It is the only jackpot game with 2 tickets for $1. But, please read the warning after this payout table.

• 1 of 1 in 6 from 59 bookie odds: 1 in 9.83  
• Bet365 pays 8/1, 81% _fairness_ (i.e. 19% _house edge_)

• 1 of 6 in 6 from 59 lottery odds: 1 in 2.62  
• NYSL pays 0, 0% _fairness_ (i.e. 100% _house edge_)

• 2 of 2 in 6 from 59 bookie odds: 1 in 114.07  
• Bet365 pays 100/1, 87% _fairness_ (i.e. 13% _house edge_)

• 2 of 6 in 6 from 59 lottery odds: 1 in 10.26  
• NYSL pays 0, 0% _fairness_ (i.e. 100% _house edge_)

• 3 of 3 in 6 from 59 bookie odds: 1 in 1625.45  
• Bet365 pays 1300/1, 80% _fairness_ (i.e. 20% _house edge_)

• 3 of 6 in 6 from 59 lottery odds: 1 in 48.08  
• NYSL pays 1, 2% _fairness_ (i.e. 98% _house edge_)

• 4 of 4 in 6 from 59 bookie odds: 1 in 30,341.73  
• Bet365 pays 25,000/1, 82% _fairness_ (i.e. 18% _house edge_)

• 4 of 6 in 6 from 59 lottery odds: 1 in 1,089.92  
• NYSL pays 30, 3% _fairness_ (i.e. 97% _house edge_)

• 5 of 5 in 6 from 59 bookie odds: 1 in 834,397.67  
• Bet365 pays 250,000/1, 30% _fairness_ (i.e. 70% _house edge_)

• 5 of 6 in 6 from 59 lottery odds: 1 in 72,207.49  
• NYSL pays 1600, 2% _fairness_ (i.e. 98% _house edge_).

-   WARNING! There is a serious issue with the online betting sites: **TRUST, or lack thereof**. I started warning would-be gamblers about the dangers of gambling online as early as the 2000s. Read: [_**Online Betting Sites Survive Only by Cheating the Gamblers**_](https://saliu.com/bbs/messages/844.html).
-   Read also reviews by real online gamblers regarding one of the largest and most advertised online betting operators in the world, _Bet365_: [_**Customer Service Reviews of Bet365**_](https://www.trustpilot.com/review/www.bet365.com). Most reviewers regard the online gambling giant as **terrible**!
-   The payouts are great alright. But is it the classical <u>'too good to be true'</u> scenario?
-   If one could only find _window betting_ offering the same super odds/payouts...

![The bookie lotteries accept straight bets, including online, for 1 to 5 lottery numbers.](https://saliu.com/HLINE.gif)

[

## Resources in Lottery, Software, Systems, Lotto Wheels, Strategies

](https://saliu.com/content/lottery.html)

-   The Main [_**Lotto, Lottery, Software, Strategy, Systems**_](https://saliu.com/LottoWin.htm) Page.  
    Presenting software to create free winning lotto, lottery strategies, systems based on mathematics. Get your lotto systems or wheels, the best lottery, lotto software, combinations, winning numbers.
-   [_**Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies**_](https://saliu.com/Newsgroups.htm).  
    Read a genuine analysis of Excel spreadsheets applied to lottery and lotto software development, systems, and strategies. Combining Excel analysis with powerful lottery and lotto software.
-   [_**<u>MDIEditor Lotto WE</u>: Lottery Software Manual, Book, ebook, Help**_](https://saliu.com/MDI-lotto-guide.html).  
    ~ Also applicable to LotWon lottery, lotto software; plus Powerball/Mega Millions, Euromillions.
-   [_**Manual, Tutorial of <u>Lottery Software</u>, Lotto Apps**_](https://saliu.com/forum/lotto-book.html).
-   [**Lottery Mathematics, Lotto Mathematics, Math, Maths**](https://saliu.com/gambling-lottery-lotto/lottery-math.htm): Probability, Appearance, Repeat, Affinity, Number Affiliation, Lotto Wheels, Lottery Systems, Strategies.
-   [_**The Best Strategy in Lottery, Gambling**_](https://saliu.com/strategy-gambling-lottery.html).
-   [_**Neural Networking, Artificial Intelligence AI, Axiomatic Intelligence AxI in Lottery: Strategies, Systems, Software**_](https://saliu.com/neural-networking-lottery.html).
-   [_**Lottery, Software, Systems, Science, Mathematics**_](https://saliu.com/lottery.html)
-   [_**Lottery Numbers: Loss, Cost, Drawings, House Advantage, Edge**_](https://saliu.com/lottery-numbers-loss.html)
    -   Playing random lottery numbers or favorite numbers guarantees losses because of the house edge. Only lottery strategies, systems, special software can win with consistency and make a profit.
-   [_**The Best Lottery Strategies: Foundation, Application of the Lotto Strategy Concept**_](https://software.saliu.com/lottery-strategy-lotto-strategies.html).
-   [_**Software, Formulas to Calculate Lottery Odds**_](https://saliu.com/oddslotto.html) using the hypergeometric distribution probability.
-   Download [**Lottery Software: Lotto, Pick 3 4, Powerball, Mega Millions, Euromillions, Keno**](https://saliu.com/free-lotto-lottery.html).
    
    ![Special lotto software analyses past drawings based on singles, pairs, triples, quads, quintets, sextets.](https://saliu.com/HLINE.gif)
    
    **[Home](https://saliu.com/index.htm) | [Search](https://saliu.com/Search.htm) | [New Writings](https://saliu.com/bbs/index.html) | [Fundamental Formula](https://saliu.com/formula.htm) | [Odds, Generator](https://saliu.com/calculator_generator.html) | [Contents](https://saliu.com/content/index.html) | [Forums](https://forums.saliu.com/) | [Sitemap](https://saliu.com/sitemap/index.html)**
    
    ![The only drawback to playing bookie lotteries is the absence of huge jackpots, or even none.](https://saliu.com/HLINE.gif)
