# Manual Skip Calculation Verification

# Test data: Number 1 appears in draws 1, 3, 6, 10
# Draw positions: 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11
# Number 1 hits:   ✓, ✗, ✓, ✗, ✗, ✓, ✗, ✗, ✗, ✓,  ✗

println("=== Manual Skip Calculation Verification ===")
println("Number 1 hits in draws: 1, 3, 6, 10")
println("Total draws: 11")
println()

# SkipSystem method: skip = total draws between hits (including the last hit)
println("SkipSystem method:")
println("- Current skip: 11 - 10 = 1 (from last hit to current)")
println("- Skip 10→6: 10 - 6 = 4")
println("- Skip 6→3: 6 - 3 = 3") 
println("- Skip 3→1: 3 - 1 = 2")
println("Expected SkipSystem skips: [1, 4, 3, 2]")
println()

# MDIEditor method: skip = draws completely missed between hits
println("MDIEditor method:")
println("- Current skip: 11 - 10 = 1 (same as SkipSystem for current)")
println("- Skip 10→6: 10 - 6 - 1 = 3 (draws 7,8,9 were missed)")
println("- Skip 6→3: 6 - 3 - 1 = 2 (draws 4,5 were missed)")
println("- Skip 3→1: 3 - 1 - 1 = 1 (draw 2 was missed)")
println("Expected MDIEditor skips: [1, 3, 2, 1]")
println()

# Verify the relationship
println("Relationship verification:")
println("SkipSystem = MDIEditor + 1 (for historical skips)")
println("4 = 3 + 1 ✓")
println("3 = 2 + 1 ✓") 
println("2 = 1 + 1 ✓")
println()

# Now let's test our actual implementation
using Dates

# Simple types for testing
struct LotteryDraw
    numbers::Vector{Int}
    draw_date::Date
    draw_id::Int
end

struct SkipAnalyzer
    historical_data::Vector{LotteryDraw}
    skip_cache::Dict{Int, Vector{Int}}
    
    SkipAnalyzer(data::Vector{LotteryDraw}) = new(data, Dict{Int, Vector{Int}}())
end

function SkipAnalyzer(raw_data::Vector{Vector{Int}})
    draws = LotteryDraw[]
    for (i, numbers) in enumerate(raw_data)
        date = Date(2022, 1, 1) + Day(length(raw_data) - i)
        draw = LotteryDraw(numbers, date, i)
        push!(draws, draw)
    end
    return SkipAnalyzer(draws)
end

# SkipSystem method implementation (updated with reverse)
function calculate_skips_skip_system(analyzer::SkipAnalyzer, number::Int)::Vector{Int}
    skips = Int[]
    last_occurrence = 0

    for (i, draw) in enumerate(analyzer.historical_data)
        if number in draw.numbers
            if last_occurrence > 0
                skip = i - last_occurrence  # SkipSystem: include last hit
                push!(skips, skip)
            end
            last_occurrence = i
        end
    end

    # Reverse to get most recent skips first
    reverse!(skips)

    # Add current skip
    if last_occurrence > 0
        current_skip = length(analyzer.historical_data) - last_occurrence
        pushfirst!(skips, current_skip)
    end

    return skips
end

# MDIEditor method implementation (updated with reverse)
function calculate_skips_mdi_method(analyzer::SkipAnalyzer, number::Int)::Vector{Int}
    skips = Int[]
    last_occurrence = 0

    for (i, draw) in enumerate(analyzer.historical_data)
        if number in draw.numbers
            if last_occurrence > 0
                skip = i - last_occurrence - 1  # MDIEditor: exclude last hit
                push!(skips, skip)
            end
            last_occurrence = i
        end
    end

    # Reverse to get most recent skips first
    reverse!(skips)

    # Add current skip
    if last_occurrence > 0
        current_skip = length(analyzer.historical_data) - last_occurrence
        pushfirst!(skips, current_skip)
    end

    return skips
end

# Test with our data
test_draws = [
    [1, 5, 10, 15, 20],  # Draw 1: number 1 hits
    [2, 6, 11, 16, 21],  # Draw 2: number 1 misses
    [1, 7, 12, 17, 22],  # Draw 3: number 1 hits
    [3, 8, 13, 18, 23],  # Draw 4: number 1 misses
    [4, 9, 14, 19, 24],  # Draw 5: number 1 misses
    [1, 10, 15, 20, 25], # Draw 6: number 1 hits
    [5, 11, 16, 21, 26], # Draw 7: number 1 misses
    [6, 12, 17, 22, 27], # Draw 8: number 1 misses
    [7, 13, 18, 23, 28], # Draw 9: number 1 misses
    [1, 14, 19, 24, 29], # Draw 10: number 1 hits
    [8, 15, 20, 25, 30], # Draw 11: number 1 misses (current)
]

analyzer = SkipAnalyzer(test_draws)

println("=== Actual Implementation Test ===")
skips_skip_system = calculate_skips_skip_system(analyzer, 1)
skips_mdi = calculate_skips_mdi_method(analyzer, 1)

println("SkipSystem method result: $skips_skip_system")
println("MDIEditor method result: $skips_mdi")
println()

# Verify expectations
expected_skip_system = [1, 4, 3, 2]
expected_mdi = [1, 3, 2, 1]

println("=== Verification ===")
if skips_skip_system == expected_skip_system
    println("✓ SkipSystem method matches expected: $expected_skip_system")
else
    println("✗ SkipSystem method mismatch!")
    println("  Expected: $expected_skip_system")
    println("  Got: $skips_skip_system")
end

if skips_mdi == expected_mdi
    println("✓ MDIEditor method matches expected: $expected_mdi")
else
    println("✗ MDIEditor method mismatch!")
    println("  Expected: $expected_mdi")
    println("  Got: $skips_mdi")
end

# Verify relationship
println()
println("=== Relationship Verification ===")
all_correct = true
for i in 2:length(skips_skip_system)
    if skips_skip_system[i] != skips_mdi[i] + 1
        println("✗ Relationship failed at position $i: $(skips_skip_system[i]) ≠ $(skips_mdi[i]) + 1")
        all_correct = false
    end
end

if all_correct
    println("✓ All historical skips follow: SkipSystem = MDIEditor + 1")
end

println("\n🎉 Manual verification complete!")
