---
created: 2025-07-24T21:26:40 (UTC +08:00)
tags: [strategy,lottery,lotto,software,algorithms,number,pairings,pairs,frequency,reports,jackpot,]
source: https://saliu.com/lie-lottery-strategies-pairs.html
author: 
---

# Lottery Strategy in Reverse for Pairs, Number Frequency

> ## Excerpt
> Reversed lottery strategy is based on lotto number pairings, frequency, hot, cold, the best lottery software, algorithms, systems are provided.

---
![Read an introduction to reduction of lotto tickets with reversed lottery strategy on pairs.](https://saliu.com/HLINE.gif)

### I. [Introduction to _Dedicated LIE Elimination_, Reversed Lottery Strategy for _Pairs_, _Number Frequency_](https://saliu.com/lie-lottery-strategies-pairs.html#Mathematics)  
II. [Software for Lottery Strategy in Reverse Based on Pairs, Number Frequency](https://saliu.com/lie-lottery-strategies-pairs.html#Software)  
III. [Reports for Lottery Strategy in Reverse Based on Pairs, Number Frequency](https://saliu.com/lie-lottery-strategies-pairs.html#Reports)  
IV. [Generate Combinations or Purge Files for _Dedicated LIE Elimination_ Strategies](https://saliu.com/lie-lottery-strategies-pairs.html#Combinations)  
V. [Strategies for _Dedicated LIE Elimination_ on Pairs, Number Frequency](https://saliu.com/lie-lottery-strategies-pairs.html#Strategies)  
VI. [Resources in Lottery Lotto Software, Strategies, Systems](https://saliu.com/lie-lottery-strategies-pairs.html#Resources)

![We know that all lottery strategies miss more often than hit winners therefore we eliminate output.](https://saliu.com/HLINE.gif)

## <u>1. Introduction to <i>Dedicated LIE Elimination</i>, Reversed Lottery Strategy for <i>Pairs</i>, <i>Number Frequency</i></u>

First captured by the _WayBack Machine_ (_web.archive.org_) on August 6, 2015.-   The ultimate lotto, lottery, horse racing software for applying _Reversed Lottery Strategy for Pairs, Number Frequencies_:
    -   **LieId3** ~ for pick-3 daily games;
    -   **LieId4** ~ for pick-4 lotteries;
    -   **LieId5** ~ for 5-number lotto games;
    -   **LieId6** ~ for 6-number lottos;
    -   **LieIdH3** ~ for horse racing trifectas.
    -   Latest version: **7.1**, April 2016.
    -   Version **7.0** added two very important features:
        -   _**Strategy Checking**_
        -   _**layers of data analysis**_ as in the mainstream **LotWon** software.
-   Version **7.1** added the _**inner LIE strategy filtering**_ as in the mainstream **LotWon** software; improvements in functionality.
-   Please be advised that these programs, released in 2015, require a special form of _membership to download software_: _**Ultimate Lottery Software**_. Click on the top banner to learn the terms, conditions, and availability. All my software is always announced in the **Forums**, **New Writings**, **Software Downloads**. The presentation pages are always published first. Such Web resources are free to read and help the reader make a decision regarding the licensing of software.
-   The program for _LIE Elimination_: Lottery, Lotto Strategy in Reverse Based on **Pairs**, **Number Frequency** runs from the main menu of the respective _**Ultimate Software**_ package, function _D = Dedicated LIE Elimination (LieID)_.
    
    Axiomatic one, the _**reversed lottery strategy**_ also known as _**LIE elimination**_ was first presented on this Web page: [_**Lotto, Lottery <u>Strategy in Reverse</u>: Not-to-Win Leads to Not-to-Lose or WIN**_](https://saliu.com/reverse-strategy.html). The idea crossed my mind in the 1990s. Then, in the year of grace 2010, a faithful member of my (now closed) forums (see footer) revived the idea. He asked this simple question for a feature in my lottery software:
    
    _"Here is my situation: I have a file with possible combinations that were generated with BRIGHT5. Now I also have a file with combinations (that were generated from the make/break option) that I want these combos removed from the first file. Example:_
    
    _#1 File = Possible Combos_  
    _#2 File = Combinations that I want removed from file #1_
    
    _I know there must be a way to do this, but for the life of me, I just can't figure this out. (BrianPA)"_
    
    I did figure out the answer right then, end of 2010. I programmed a function in my lottery and horse racing software I baptized _**LIE Elimination**_. Just read how that feature works on the aforementioned page.
    
    _BrianPA+_ and I didn't stop there. We kept looking for more and more features added to the _**LIE Elimination**_ workhorse. There were open discussions in my (now closed) lottery forum. The _WayBack Machine_ (_web.archive.org_) "captured" fragments from the original discussions. Programming the software was a work in progress for about three years. The software was not made public: Only BrianPA and I worked with it. BrianPA advised and I agreed that I should never distribute such software. I finalized the _**LIE Elimination**_ software in the year of grace 2013. It has the **LieId** radix in the program names.
    
    Meanwhile, Brian and I had started another type of reversed lotto strategy. The programs in that category have the **LieStrings** radix in the filenames. That type of _**LIE Elimination**_ theory and software is presented on the sibling page: [_**LIE Strategy Lotto Decades, Last Digits, Odd Even, Low High, Skips**_](https://saliu.com/lie-lotto-strategies-decades.html).
    
    There are significant differences between the two types of _**lottery strategies in reverse**_. We discuss here the _**LIE Elimination**_ lottery strategy based on number **pairings** and **frequencies**.
    
    Users of my lottery software and I noticed that sets of combinations created by _top N_ pairings miss more often than they hit. Thus, create grids for the _top N_ or the _bottom N_ pair ranks and generate combinations to _LIE_ files. We have a high degree of certainty that the combinations in those files will not hit the very next lottery drawings and likely the next few draws. We don't want to play them combosnations as it is wasteful. (I decided to use the term _combosnations_ for lottery combinations that should not be played, that should be eliminated beforehand.)
    
    The main goal now is to determine the _rank N_ of pairings or number frequencies that leads to LIE file combinations. In other words, I want to determine the N value that would lead me to the wrong strategy. Put it another way: I want to create a _wonder grid_ for the top 10 pairings in the lotto-6 game. I am almost certain that my _wonder grid_ won't hit the very next draw. I generate the combinations and I discover that there was no _6 of 6_ winner in the next lottery drawing... sometimes not even a _5 of 6_ winner. Thus, it was the right move to eliminate those _combosnations_. I was right because I intentionally wanted to be wrong — the essence of the _**LIE Elimination**_ strategy.
    
    Evidently, the _N_ factor varies from drawing to drawing. We need software to create reports that show the _N_ values for any range of lotto draws. It is a difficult task but I beat the challenge. The reports show what values for _N_ I would choose and be assured I'll be "wrong" — that is, I'll be right as far as the _**LIE Elimination**_ strategy is concerned. Look at the following example of a _6/49_ lotto report:
    
    ![Lie lottery strategies are designed for any parpalucks, both for lotto pairs and frequencies.](https://saliu.com/images/parpaluck-report.gif)
    
    I decide to play a lotto strategy based on _hot_ numbers. You see that fixture in many lottery software packages or books, including Gail's. They advise something like this: _"Play the top half of the hot lotto numbers."_ I decide to play the _top 30 hot_ numbers. I would not hit _6 of 6_, I would not hit _5 of 6_... I wouldn't even hit _4 of 6_! Bad for the Gails... but good for my _**LIE Elimination**_ strategy! I would enable the _ID4_ in the _**LIE**_ function of the **Bright/Ultimate** combinations generators and get a handful of combinations that will win. And I will sing that nice song of Fleetwood Mac:  
    _"Tell me lies...  
    Tell me sweet little lies..."_
    
    <iframe src="https://www.youtube.com/embed/BVUOuwEeVX0" frameborder="0" allowfullscreen=""></iframe>
    
    The **Reports** and **Strategies** sections will provide more in-depth coverage.
    
    ## <u>2. Software for Reversed Lottery Strategy Based on Pairs, Number Frequency</u>
    
    ![The ultimate lottery software has reverse strategy specialized programs.](https://saliu.com/images/ultimate-lotto-software-60.gif)
    
-   The following screenshot is the main menu of all programs in this type of _lottery strategies in reverse_ —
    
    ![The reversed lotto software is based on number frequencies and lottery pairings to win the jackpot.](https://saliu.com/images/lie-lottery-pairs-software.gif)
    
    This software does something very special. It generates combinations to be played directly, after applying the _**LIE eliminating**_ **automatically**. In section 1, the user would select a lotto strategy that would go wrong: Playing the _top 30 hot_ numbers. The combinations would have been saved to a _LIE_ file, eventually fed to the combination generators in **Bright**. This type of software does all the steps without user's intervention. In addition, the **LieId** software is amazingly fast — the fastest in my entire lotto software! Unfortunately, I haven't been able to apply the same programming techniques to the rest of my programs.
    
    As per the example above, I only enter 1 of the many filters named _ID_ in the screen prompt (the entry form). It is named _ID4 for HOT6_ in this case. In the Report, it appears in the section _FREQUENCIES 'ANY POSITION', HOT 6_. That is the 1st report and deals with the hot numbers. _ID4_ eliminates more than half of total 6/49 lotto combinations; _ID3_ leaves around 2 million combos to play; _ID2 = 28_ generates some 600000 combinations to play.
    
    The ID filters in the section of the report named _PAIRINGS 'ALL', TOP 6_ have even more eliminating power. _ID3 for TOP6 = 12_ generates some 100000 combinations to play for _6 of 49_ lotto.
    
    If we combine _LieID_ filters from both _PAIRINGS_ and _FREQUENCIES_ AND from both reports, the difference in output reduction is dramatic. _ID3 for TOP6_, _ID3 for TOP62_, _ID4 for HOT6_, _ID4 for HOT6_ together generate around 30,000 combinations to play. Such strategy has good frequency and the output will be easily _**Purged**_ further by other functions or other programs in the _**Ultimate Lottery Software**_ bundles.
    
    ![The lotto pairs and hot cold numbers act as filters, restrictions to eliminate bad combinations.](https://saliu.com/images/lie-lottery-pairs-filters.gif)
    
-   The function _I = Generate LIE ID Reports_ is mandatory to run first as it creates auxiliary files. If not, the software triggers an error when trying to generate combinations.
    
    ![The reverse lotto strategy software generates first comprehensive reports to select filters from.](https://saliu.com/images/lie-lottery-pairs-report.gif)
    
    The program offers defaults for the _ranges of analysis_ or _parpalucks_.
    
    ![There are special ranges of lottery draws for analyses and report generating.](https://saliu.com/images/lie-lottery-pairs-draws.gif)
    
    Following is a fragment of the report **LieID6.4** for _Layer 4_ of the D6 data file of real lottery drawings and simulated combinations.
    
    ![The pairing and frequency parpalucks can be calculated by math.](https://saliu.com/images/parpaluck-report.gif)
    
    The frequency of numbers feature is identified as _Hot_ and _Cold_ — the most commonly used terminology in _lottospeak_.
    
    The pairings are identified as _TOP_ and _BOTtom_ to maintain compatibility with previous theories of reversed strategies. In 2010 we applied this type of strategy manually by generating combinations for various ranges of _Top_ pairings or _Bottom_ pairings. It can still be done manually in _**Super Utilities**_, the _F = Frequency_ function; the combinations are generated in the _M = Make/Break/Position_ functions. The output files are used as LIE files to feed the combination generators in **Ultimate Lottery Software**.
    
    ## <u>4. <i>Generate</i> Combinations or <i>Purge</i> Files for <i>Dedicated LIE Elimination</i> Strategies</u>
    
-   The function _G = Generate Combinations_ needs the files created automatically by _Reporting_. Also, the user will type filter values at the prompt screens; or, input filters from a strategy file (_LieID.STR_). These are the files generated automatically by the _Report_ module:
    
    -   **HOT6.1-4** = for _frequencies regardless of position_, the numbers sorted in _descending order_ (i.e. from _hot_ to _cold_), layer by layer;
    -   **COLD6.1-4** = for _frequencies regardless of position_, the numbers sorted in _ascending order_ (i.e. from _cold_ to _hot_), one for each layer.
        
        _22 44 31 37 38 5 46 24 39 42 43 19 23 47 29 14 33 36 2 20 21 4 1 6 25 28 49 18 32 8 34 35 9 10 11 13 41 3 15 26 16 17 30 40 45 7 12 48 27_
        
    -   **HoP6.1-4** = for _frequencies position by position_, the numbers sorted in _descending order_ (i.e. from _hot_ to _cold_), one for each layer;
    -   **CoP6**.1-4 = for _frequencies position by position_, the numbers sorted in _ascending order_ (i.e. from _cold_ to _hot_), layer by layer.
        
        _5 2 1 4 3 6 8 9 11 15 17 23 13 14 10 16 7 18 19 20 21 22 12 24 25 26 27 28 29 30 31 32 33 34 35 36 37 38 39 40 41 42 43 44 45 46 47 48 49  
        22 19 14 13 4 16 18 5 21 6 25 12 1 2 15 3 17 7 8 20 9 10 23 24 11 26 27 28 29 30 31 32 33 34 35 36 37 38 39 40 41 42 43 44 45 46 47 48 49  
        20 22 24 31 19 23 10 30 21 33 35 38 13 14 15 16 17 18 2 3 4 5 6 7 25 26 27 28 29 8 9 32 1 34 11 36 37 12 39 40 41 42 43 44 45 46 47 48 49  
        37 42 25 26 28 29 31 32 33 22 38 24 44 14 15 16 17 18 19 20 21 1 23 2 3 4 27 5 6 30 7 8 9 34 35 36 10 11 39 40 41 12 43 13 45 46 47 48 49  
        43 38 39 36 44 28 29 34 23 46 11 12 13 14 15 16 17 18 19 20 21 22 1 24 25 26 27 2 3 30 31 32 33 4 35 5 37 6 7 40 41 42 8 9 45 10 47 48 49  
        44 46 47 49 42 31 37 39 41 10 11 12 13 14 15 16 17 18 19 20 21 22 23 24 25 26 27 28 29 30 1 32 33 34 35 36 2 38 3 40 4 5 43 6 45 7 8 48 9_
        
    
    When you see _48_ in the _ID6_ column in the _Frequency, Hot_ section of the reports, it means that one of the 6 lotto numbers in the drawing had a frequency rank of 49. You can generate combinations from the "hottest" 48 numbers in the game and safely eliminate them all via the _**LIE elimination**_ feature. Just enter _ID6 = 48_ and the program will generate the right combinations by eliminating 12,271,512 of the 13,983,816 combinations in the 6/49 lotto set. That filter value occurs more often than once ever three drawings.
    
    -   **TOP6.1-4** = for _pairings_, the pairs sorted in _descending order_ (i.e. from _hot_ to _cold_), layer by layer;
    -   **BOT6.1-4** = for _pairings_, the pairs sorted in _ascending order_ (i.e. from _cold_ to _hot_), one for each layer.
        
        _1 30 10 12 14 8 31 34 35 38 42 43 20 22 29 2 3 33 4 6 16 40 41 18 19 44 46 48 49 7 17 32 11 5 13 36 37 21 39 9 23 24 25 26 45 27 47 28 15  
        2 3 4 32 40 18 22 23 24 29 30 31 6 35 36 38 39 7 8 9 26 27 28 10 11 13 14 33 34 15 1 37 19 21 5 42 43 44 45 49 41 17 12 16 20 46 47 48 25  
        3 2 4 8 14 22 24 28 30 43 45 29 1 34 36 38 39 26 12 49 21 5 23 9 25 10 27 11 6 13 31 32 33 7 35 15 37 16 17 40 41 42 18 44 19 46 47 48 20  
        4 14 10 2 20 38 43 21 29 32 35 36 9 41 3 1 23 26 5 30 31 11 33 34 7 15 16 39 18 42 8 44 46 47 48 6 37 27 28 40 19 12 13 22 45 17 24 25 49  
        ...  
        46 23 33 31 14 34 43 44 47 19 21 22 4 25 26 30 5 9 11 38 39 41 42 13 1 15 7 27 28 29 8 16 32 17 18 35 36 37 2 20 40 10 3 12 24 45 6 48 49  
        47 20 46 11 13 15 18 19 4 23 25 30 32 33 36 39 43 44 6 7 8 21 22 9 24 10 26 27 28 29 3 31 12 1 34 35 14 37 38 5 40 41 42 16 17 45 2 48 49  
        48 9 4 1 10 11 12 16 28 29 32 33 38 42 49 15 7 17 18 19 20 21 22 23 24 25 26 27 8 3 30 31 2 5 34 35 36 37 6 39 40 41 13 43 44 45 46 47 14  
        49 28 31 3 8 9 11 16 18 22 24 26 1 2 32 35 37 44 48 19 20 21 5 23 10 25 6 27 12 29 30 13 14 33 34 15 36 7 38 39 40 41 42 43 17 45 46 47 4_
        
        The lotto numbers in front of each pairing line are to be used by the _1+5_ procedure, as in the _Make/Break_ generating functions of _**Super Utilities**_.
        
    
    The _**Purge**_ feature is present in just about every piece of software I've created. The function takes a text file of previously generated combinations and **purges** it; i.e. it removes combinations based on filter settings.
    
    The output file can be:  
    
    -   a file of combinations previously generated by **MDIEditor Lotto**;
    -   a file of combinations previously generated by the _**command prompt LotWon**_ lotto/lottery programs;
    -   a lotto wheel (that needs to be reduced probably because of its large size);
    -   a file of combinations previously generated by another application (other than **MDIEditor Lotto** or _**LotWon / SuperPower**_).
        
        The output file must be compliant with the _**LotWon**_ or **MDIEditor Lotto** format:
        
        -   the file must be in **text format** (no special formatting, no blank lines);
        -   the file must be **compliant with the format of the data file** for the respective lotto/lottery game; e.g. an output file to purge in the lotto-6 game must have exactly 6 numbers per line (combination);
        -   the _name_ of the new output file must be **different** from the _purge_ filename.
    
    ![Reverse lie elimination strategy generates lottery combinations to play right away.](https://saliu.com/images/lie-lottery-pairs-combinations.gif)
    
    ## <u>5. Lottery Strategies for <i>LIE Elimination</i> Based on Pairs, Number Frequency</u>
    
    Again, although this lottery strategy is named _**LIE Elimination**_, it generates combinations that do NOT need to be fed to the _LIE_ functions in the traditional generators in the **Bright** or **Ultimate Lottery Software** packages. It takes just minutes to run a strategy in this type of software. By contrast, when this software generated _Lie_ files first it took long hours to get the final lotto combinations to be played. It was a real breakthrough in my programming career. But, then again, I haven't been able to apply the same programming algorithms to my entire lottery software.
    
    There is a very useful function to aid the user in finding strategies: _T = SorT Filter Reports by Column_. The module will sort the two main reports by any column the player chooses. The columns are sorted in **descending** order, so that the **highest** filter values come up at the top.
    
    The main goal is to find high levels for the respective filter(s). The higher the value of a filter, the more combinations eliminated. All lottery strategies usually start with one filter (column) I name the _pivot filter_. In this particular example, it is _ID5\_1_ for _PAIRINGS ALL_ (marked in red). We want a strategy to start with that _ID5\_1 = 25_. We then look for other high values in the columns of the same report. We notice that _ID6\_1_ for _PAIRINGS 1+5_ is at least 28; _BOT6\_1 = 14_ in the same _PAIRINGS 1+5_ zone; _ID6\_1 = 31_ in the _PAIRINGS ALL_ area; _ID6\_1_ for _HOT6_ is at least 40; etc.
    
    We want to see how often a strategy with those parameters hit in the past. We want to see what other reliable filter values we can employ additionally. There is another valuable function that does exactly that: _S = Strategy Checking of ID Reports_. The useful strategy program compiles a report like this one (only a fragment here):
    
    ```
    <span size="5" face="Courier New" color="#c5b358">    Lotto-6 LIE Strategy Report
        Reports checked: LieID6.1, LieID6.2,
                         LieID6.3, LieID6.4
        * Strategy Composition *
    
     TopId6 '1+4'_1    31
     BotId6 '1+4'_1    16
    
     TopId2 'ALL'_1     2
     TopId3 'ALL'_1     4
     TopId4 'ALL'_1     8
     TopId5 'ALL'_1    25
     TopId6 'ALL'_1    29
    
     BotId2 'ALL'_1     0
     BotId3 'ALL'_1     0
     BotId4 'ALL'_1     0
     BotId5 'ALL'_1     0
     BotId6 'ALL'_1     0
    ...
    </span>
    ```
    
    \* Frequency of the LIE6 Strategy in 1000 Draws \*\*\*
    
    \* HITS: 26
    
    \* Skips: 63 2 11 117 16 43 22 3 18 69 63 17 31 17 146 22 101 25 27 0 64 16 19 8 29 15
    
    \* Sorted Skips: 0 2 3 8 11 15 16 16 17 17 18 19 22 22 25 27 29 31 43 63 63 64 69 101 117 146
    
    \* Median Skip: 22
    
    We can see high filter values in other columns, including the reports for the other 3 layers. Furthermore, we can correlate this strategy with other types of strategies in those great pieces of software known as _**Bright**_ and especially _**Ultimate Lottery Software**_. The functionality is presented in-depth on this e-book: [**Cross-Reference Lottery Strategy Files Created by _Command Prompt Software_ and _MDIEditor Lotto WE_**](https://saliu.com/cross-lines.html). The function is available from the second menu of the _**Ultimate Lottery Software**_ integrated software: _T = Cross-Checking Strategies_.
    
    The LieID styrategy file is clean and intuitive, with easy to identify place holders. I always take advantage of the great **Notepad++** free text editor. I pinned the editor on the taskbar. I open it with the LieID reports. I copy multiple filters from the reports and paste them into the _LieID.STR_ strategy file. At the end of a run, I do not end the LieID software. I go to the **Notepad++** text editor and edit the strategy file. It works like a charm — that is, very efficiently!
    
    -   While testing the software, I played casually with conservative filter settings. I had situations with only one 6/49 lotto combination to play! Even if I am right only once in 100 drawings, I still get a jackpot win.
    
    ## [<u>6. Resources in Lottery Lotto Software, Strategies, Systems</u>](https://saliu.com/content/lottery.html)
    
    -   The Main [_**Lotto, Lottery, Software, Strategy**_](https://saliu.com/LottoWin.htm) Page.  
        Presenting software to create free winning lotto, lottery strategies, systems based on mathematics. Get your lotto systems or wheels, the best lottery, lotto software, combinations, winning numbers.
    -   [_**Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies**_](https://saliu.com/Newsgroups.htm).  
        Read a genuine analysis of Excel spreadsheets applied to lottery and lotto developing of software, systems, and strategies. Combining Excel analysis with powerful lottery and lotto software programmed by this author, _Parpaluck_.
    -   [_**<u>MDIEditor Lotto WE</u>: Lottery Software Manual, Book, ebook, Help**_](https://saliu.com/MDI-lotto-guide.html).  
        ~ Also applicable to my entire lottery software; plus Powerball, Mega Millions, Euromillions.
    -   [_**Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps, Programs**_](https://saliu.com/forum/lotto-book.html).
    -   [**Lottery Mathematics, Lotto Mathematics, Math, Maths**](https://saliu.com/gambling-lottery-lotto/lottery-math.htm): Probability, Appearance, Repeat, Affinity, Number Affiliation, Lotto Wheels, Lottery Systems, Strategies.
    -   Practical [_**Lottery and Lotto Filtering in Software**_](https://saliu.com/filters.html).
    -   [**Skips Lottery, Lotto, Gambling, Systems, Strategy**](https://saliu.com/skip-strategy.html); software included.
    -   [_**Lottery Strategy, Systems Based on Number, Digit Frequency**_](https://saliu.com/frequency-lottery.html)
    -   [_**Lotto, Lottery <u>Strategy in Reverse</u>: Not-to-Win Leads to Not-to-Lose or WIN**_](https://saliu.com/reverse-strategy.html).
    -   [_**<u>LIE Elimination, Reversed Lottery Strategy</u> for Lotto Decades, Last Digits, Odd Even, Low High, Skips**_](https://saliu.com/lie-lotto-strategies-decades.html).
    -   [_**Lotto, Lottery Strategy on Sums, Root Sums, Skips, Odd Even, Low High, Deltas**_](https://saliu.com/strategy.html).
    -   [_**Lottery Utility Software**_](https://saliu.com/lottery-utility.html)_**: Pick-3, 4 Lottery, Lotto-5, 6, Powerball, Mega Millions, Euromillions**_.
    -   [**Theory, Analysis of _Deltas_ in Lotto, Lottery Software, Strategy, Systems**](https://saliu.com/delta-lotto-software.html).
    -   [_**Lotto Decades, Last Digits, Systems, Strategies, Software**_](https://saliu.com/decades.html).
    -   [_**Markov Chains, Followers, Pairs, Lottery, Lotto, Software**_](https://saliu.com/markov-chains-lottery.html).
    -   [_**Reversed Lottery Strategy for Lotto Decades, Last Digits, Odd Even, Low High, Skips**_](https://saliu.com/lie-lotto-strategies-decades.html).
    -   [_**Lotto Software for Groups of Numbers: Odd, Even, Low, High, Sums, Frequencies, User's Groups**_](https://saliu.com/lotto-groups.html).
    -   [_**The Best Strategy for Lottery, Gambling, Sports Betting, Horse Racing, Blackjack, Roulette**_](https://saliu.com/strategy-gambling-lottery.html).
    -   _"The Start Is the Hardest Part"_ in [_**Lottery Strategies**_](https://forums.saliu.com/lottery-strategies-start.html).
    -   [_**Strategy Lotto Software on Positional Frequency**_](https://forums.saliu.com/lotto-software-position-frequency.html).
    -   Download [**Lottery Software, Lotto Software**](https://saliu.com/infodown.html).
    
    ![The first lotto software to apply reversed lottery strategies with the best chance to win.](https://saliu.com/HLINE.gif)
    
    **[Home](https://saliu.com/index.htm) | [Search](https://saliu.com/Search.htm) | [New Writings](https://saliu.com/bbs/index.html) | [Fundamental Formula](https://saliu.com/formula.htm) | [Odds, Generator](https://saliu.com/calculator_generator.html) | [Contents](https://saliu.com/content/index.html) | [Forums](https://forums.saliu.com/) | [Sitemap](https://saliu.com/sitemap/index.html)**
    
    ![You read about strategy, lottery, lotto, software, algorithms, number, pairings, pairs, frequency.](https://saliu.com/HLINE.gif)
