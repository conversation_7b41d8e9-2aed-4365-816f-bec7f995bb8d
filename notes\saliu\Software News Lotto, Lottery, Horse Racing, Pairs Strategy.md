---
created: 2025-07-24T21:07:10 (UTC +08:00)
tags: [software,lottery,lotto,horse racing,update,program,lottery pairs,reversed lottery strategy,LIE elimination,]
source: https://saliu.com/software-news.html
author: 
---

# Software News: Lotto, Lottery, Horse Racing, Pairs Strategy

> ## Excerpt
> Updates to Bright lotto software, lottery software, horse racing programs. Pairings, LIE elimination, reduction, reversed lotto strategies are emphasized.

---
Published in May 2012; later updates.

## <u>I. <i>PairGrid</i>: Lottery Software for Pairings and Lotto Grids</u>

The software packages known **Bright** have been updated. A few quirks were discovered by users and have been fixed by this software author. Also, the updates add the important feature of _**best / worst**_ pairing reporting and combination generating. Furthermore, such feature leads to some powerful _**LIE lottery strategies**_ (or _**reversed lotto strategy**_).

There are five **Bright software** packages affected. The filenames are indicative of the game: Bright3, Bright4, BrightH3, Bright5, Bright6. Bright H3 refers to horse racing, mostly trifecta generating.

One of the quirks affected the _**strategy-checking**_ functions. An error was triggered when a particular strategy had a hit in the very last lottery drawing analyzed. The software shows now, correctly, all lottery drawings that recorded a win (hit) for that particular strategy.

I fixed also the reporting for horse racing. I discovered a nuisance for races such as the famous Kentucky Derby. That race is extremely biased: From 3 horses per race to 25 horses (in one year, 84 or 85 years ago). By the way, you should avoid that race (year) in your reporting. That is, you should do the _WH/MH_ reporting for 80 or 82 races. A data file like the Kentucky Derby history has extremely high values for standard deviations or some filters. Strategy-checking should not lead to errors anymore, hopefully. I believe I did my best in this regard.

## <u><i>II. Reversed</i> Pick 3 Lottery Strategy Based on Pairings and Grids</u>

A loyal user of my software (_BrianPA_) played a very important role in implementing what I call the _**LIE**_ or _**reversed lottery strategy**_. He discovered that the _**BEST/WORST PAIRS reporting**_ did not work accurately when choosing ranges. For example, the user wanted to generate the reports for the 10 BEST pairings starting at position #5. That is, the user wanted the best pairs with the following ranks: #5, #6, ... #13, #14. By error, the software had always started at position #1 (e.g. from rank #1 to rank #10). The affected function appears as _**S = Super Utilities**_ in the main menu. The function works accurately now.

The program name is **PairGrid**. I had intentionally disabled two functions, but I had not mentioned in the tutorials. I decided to enable all functions in the **PairGrid** programs, as they play a very good role in devising _**LIE**_ lottery strategies.

![Pair grid software for lottery creates winning lotto strategy systems.](https://saliu.com/ScreenImgs/LIE-lottery-strategy.gif)

Always start with the function _**R = Pairing Reports**_. The main report is named _Pair\*.REP_ (e.g. _Pair6.REP_ for 6-number lotto).

Here is a sample for 5-number lotto as reported by a user and incorporated in a _LIE_ strategy.

![Powerful lottery strategies eliminates combinations with the worst lotto pairs.](https://saliu.com/ScreenImgs/eliminate-pairs.gif)

Let's take one line at a time, and I will explain how to make a devastating _**LIE**_ file from the information.

LINE #1: As you can see, the highest pair rankings from each winning number is <big>magnified</big>. This is what we will be concentrating on for this example.

The lowest pair ranking among ... the highest ranks is <big>36</big>. So what does this mean? It means we can make a _TOP5_ file with the top-35 pairs of every number, and using _M = Make/Break/Position_ in _**S = Super Utilities**_ (in the main menu of Bright), break that file down using _1 number + 4 pairs_, for a devastating _**LIE**_ file!

You may think that 35 top pairs is a good enough lie file. Well, hold on there!

Look at line #14. You will see that the lowest _High Ranking_ is <big>40</big>. So now you can use a _TOP5_ file with 39 pairs for a LIE file. THAT is HUGE!!!

Please NOTE: The numbers in the report do not reflect actual lottery numbers. They only reflect the **ranking** of a number's pairing frequency in relation to a main number. When using the above method for lie files, you must use filter _ID5_ (for _5 of 5 numbers_ elimination).

The _TOP5_ file is created also in _**S = Super Utilities**_, function _F = Frequency Reporting by Number_. For _TOP5_ select _Number of pairs_ = 35 and _Starting position_ = 1.

_**Super Utilities**_, function _F = Frequency Reporting by Number_ also creates two more important files: _BEST_ and _WORST_. Now, the _**LIE**_ lotto strategy is implemented differently. We apply _Screen\_0_ in the combination generating programs (e.g. Lexico, Combine, Wheel).

As an example, we create _BEST_ and _WORST_ for 1 pairing each, starting at position #1 and position #42 (last for 5/43 lotto), respectively. Each file will have 43 lines (numbers in the lotto game). We concatenate _BEST_ and _WORST_ to, say _LEAST5_ (with 86 lines). We recreate _D5_ with _LEAST5_ on top of _Data-5_. Use _**Super Utilities**_, function _M = Make/Break/Position_, then _2 = Make D5 with LEAST5_.

Next, in _Screen\_0_, we enable the filter named _Enter LEAST-PAIRINGS_ = 86. The software will generate a number of lotto combinations far lower than total number of combinations in the game. The output file will make a good _LIE strategy candidate_. Explanation: We will miss either the best pair rank (#1) or the worst pair rank (#42) on several occasions (marked by \*).

![Software generates reports for the best and the worst lotto pairs, pairings.](https://saliu.com/ScreenImgs/best-lotto-pairs.gif)

We can increase the winning frequency if we create _BEST_ and _WORST_ files for 2 pairs each (i.e. ranks #1 & #2, #42 & #41).

Another report created by PairGrid is _Pair.WS_.

```
<span size="5" face="Courier New" color="#c5b358"> Line                        Four
 no.

 Median:                     250

 Average:                    243

    1     2  7  8 14 31      250
    2    17 25 26 29 32      250
    3     8 14 23 27 35      250
    4    33 34 35 40 43      250
    5    13 17 21 25 41      250
    6     9 17 26 32 41      250
    7     6 24 28 32 41      250
    8     3 24 30 31 33      250
    9     3  7 17 39 43       82
   10     6  8 11 18 31      250
....
   35     5  7 33 39 42      250
   36     8 11 14 23 42      250
   37     6 16 19 22 35      250
   38     3 19 32 33 41      250
   39    10 14 15 30 43        0 *
   40     3  5 14 30 33      250
</span>
```

The _Four_ filter is frequently between 250-500 (around 95% of the time). That is, if we set _MAX\_Four = 250_, we will be wrong frequently. Therefore, the output file makes for a good _**LIE**_ (with hundreds of lotto combinations sometimes).

But look at line #39: It is a case of _**direct lottery strategy**_ (as opposed to _**LIE or reversed**_ strategy. If we set _MAX\_Four = 1_, the function _**P = Create File of Missing Pairs**_ will generate one line (with the 4 pairing ranks that repeated from the previous lotto drawing). Output file name: _Pair5.OUT_. We then run the function _**G = Generate Combosnations from Missing Pairs**_, with _Pair5.OUT_ as the input file. Total combinations generated: 43, evidently. Such a lotto strategy hits very rarely (long skips), so we can wait many lotto drawings until we play the strategy.

I noticed an extraordinary case in the Pick-3 lottery software (PairGrid3). The _Two_ filter can be between 10-20 some 15-20% of the time. I set _Min\_Two = 10_ and _MAX\_Two = 20_. The output file has some 400 pick 3 straight sets. But the file is wrong (_LIE_) around 80% of the time!

## <u>III. <i>Direct</i> Pick 3 Lottery Strategy Based on Pairings and Grids</u>

Axiomatic one, the pairs are always a good **direct** lottery strategy. That is, we generate combinations to play, not combinations for _**LIE elimination**_. The function in this type of software only works for pic lotteries and horse racing trifectas. The lotto programs deal with way too many top pairings necessary to constucting a _lotto grid_. For example, a 5/43 lotto set requires _C(42, 4) = 111930_ pairings to build the basic lotto wonder-grid (each lotto number plus its top-4 pairings).

We work with Pair Grid3 for pick-3 lotteries. There is one additional report: _PAIR3.SK_. It is created by the function _R = Pairing Report_. The report shows the frequency of all 55 pairings in pick-3. The pairings consist of 2 elements, as the _pick 3 wonder grid_ consists, at minimum, of each digit plus 2 of its pairings.

PICK-3 _String Pairing_ Frequency and Skips Draws Analyzed: 1000 Pairings Draw-Range (_Parpaluck_): 24

<u>Pair: <i>1 - 1</i></u>  
Skips: 37 28 37 7 53 59 46 6 83 71 16 61 3 55 7 9 10 22 9 21 20 34 33 63 40 44 60 13  
Sorted Skips: 3 6 7 7 9 9 10 13 16 20 21 22 28 33 34 37 37 40 44 46 53 55 59 60 61 63 71 83  
\* Median Skip: 33  
\* Total Hits: 28 = 2.8%

<u>Pair: <i>1 - 2</i></u>  
Skips: 11 2 11 2 4 24 5 4 2 31 63 53 21 5 0 17 4 12 10 81 8 0 0 14 24 126 6 44 13 25 9 27 9 13 6 104 0 8 38 26 9 23 23 11 2 0 0 0 1  
Sorted Skips: 0 0 0 0 0 0 0 1 2 2 2 2 4 4 4 5 5 6 6 8 8 9 9 9 10 11 11 11 12 13 13 14 17 21 23 23 24 24 25 26 27 31 38 44 53 63 81 104 126  
\* Median Skip: 10  
\* Total Hits: 49 = 4.9% ...

<u>Pair: <i>9 - 10</i></u>  
Skips: 35 3 43 68 26 9 52 40 8 11 38 26 6 26 30 2 42 7 2 13 1 36 4 31 18 4 12 26 41 18 23 47 5 2 17 35 34 10 67 11 28  
Sorted Skips: 1 2 2 2 3 4 4 5 6 7 8 9 10 11 11 12 13 17 18 18 23 26 26 26 26 28 30 31 34 35 35 36 38 40 41 42 43 47 52 67 68  
\* Median Skip: 23  
\*\* Total Hits: 41 = 4.1%

<u>Pair: <i>10 - 10</i></u>  
Skips: 42 37 26 10 19 63 36 45 57 56 78 84 32 66 47 31 28 0 2 14 2 13 38 16 28 31 12  
Sorted Skips: 0 2 2 10 12 13 14 16 19 26 28 28 31 31 32 36 37 38 42 45 47 56 57 63 66 78 84  
\* Median Skip: 31  
\* Total Hits: 27 = 2.7%

We can see that the pairings register a variety of frequencies. Evidently, the double pairings (from _1 - 1_ to _10 - 10_) show the lowest frequencies. We look for pairings with the _highest frequency_ and the _lowest median skip_. Usually, such pairings can be found somewhere in the middle region of the _PAIR-3.SK_ report.

<u>Pair: <i>5 - 8</i></u>  
Skips: 3 1 4 10 26 0 21 4 7 23 11 16 11 27 9 3 44 12 41 6 3 4 45 0 14 3 3 30 4 6 7 26 51 29 0 7 16 33 10 8 10 3 8 10 33 3 0 0 10 5 66 20 24 6 9 4 19 5 7 9 18 1 0 5 26 1 2  
Sorted Skips: 0 0 0 0 0 0 1 1 1 2 3 3 3 3 3 3 3 4 4 4 4 4 5 5 5 6 6 6 7 7 7 7 8 8 9 9 9 10 10 10 10 10 11 11 12 14 16 16 18 19 20 21 23 24 26 26 26 27 29 30 33 33 41 44 45 51 66  
\* Median Skip: 8  
\* Total Hits: _67_ = 6.7%

<u>Pair: <i>6 - 8</i></u>  
Skips: 3 75 3 0 8 10 43 24 1 22 10 6 0 10 3 0 1 29 4 6 13 26 29 16 0 32 1 3 3 7 32 3 4 7 9 27 3 17 44 71 5 31 7 22 11 3 11 0 2 35 19 13 25 27 5 5 23 18 4 2 24 2 24 11  
Sorted Skips: 0 0 0 0 0 1 1 1 2 2 2 3 3 3 3 3 3 3 3 4 4 4 5 5 5 6 6 7 7 7 8 9 10 10 10 11 11 11 13 13 16 17 18 19 22 22 23 24 24 24 25 26 27 27 29 29 31 32 32 35 43 44 71 75  
\* Median Skip: 9 \* Total Hits: _64_ = 6.4%

The _5 - 8_ and _6 - 8_ pairs show _high frequency_ and _low median skip values_. The current skips are below the median (_3_ and _3_ in this case). Let's say I choose the _6 - 8_ pair. I <u>Pair Grid3</u> again, function _U = Make Custom Grid from User Pairs_. The program will build a grid with pairing rank #5 and pairing rank #8 named _TopGrid3_.

![Pair grid software for lottery creates winning lotto strategy systems.](https://saliu.com/ScreenImgs/pair-grid-pick.gif)

I select generating function _1 = 1 Digit + 2 Pairs_. The program generates 54 straight sets. Evidently, I need to reduce further the amount of tickets to play. There are plenty of choices to do that effectively and safely, including _LIE elimination_ options presented above.

If I want a higher winning frequency, I combine the two pairings above. I'll create now a _TopGrid3_ consisting of each pick-3 digit and the 3 pairings: _5, 6, 8_. The function _1 = 1 Digit + 2 Pairs_ generates now some 150 straight sets. Again, I _**Purge**_ the amount generated by using several elimination methods in my hugely comprehensive software. (Let's just not get intimidated by the sheer amount of choices!)

![Missing lotto pairs can reduce drastically total combinations to play.](https://saliu.com/HLINE.gif)

## [<u>Resources in Lottery Software, Systems, Strategies, Lotto <i>Wonder Grid</i></u>](https://saliu.com/content/lottery.html)

It lists the main pages on the subject of lottery, lotto, software, systems, strategies, lotto wheels.

Here are some useful links for you, starting with the pages that introduced the **Bright software** integrated packages.

-   _**New BRIGHT3: High-Powered Integrated**_ [**Pick-3 Lottery Software**](https://saliu.com/lottery3-software.html).
-   _**New BRIGHT4: High-Powered Integrated**_ [**Pick-4 Lottery Software**](https://saliu.com/lottery4-software.html).
-   _**New BRIGHTh3: High-Powered Integrated**_ [**Horse Racing Trifecta Software**](https://saliu.com/horseracing-software.html).
-   _**New BRIGHT5: High-Powered Integrated**_ [**5-Number Lotto Software**](https://saliu.com/lotto5-software.html).
-   _**New BRIGHT6: High-Powered Integrated**_ [**6-Number Lotto Software**](https://saliu.com/lotto6-software.html).
-   _**Visual Tutorial to Bright6 and the**_ [_**Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps, Programs**_](https://saliu.com/forum/lotto-book.html).
-   [_**<u>MDIEditor Lotto WE</u>: Lottery Software Manual, Book, ebook, Help**_](https://saliu.com/MDI-lotto-guide.html)  
    ~ Also applicable to LotWon lottery, lotto software (command prompt); plus Powerball, Mega Millions, Euromillions.
-   [_**Utility Software: Pick-3, 4 Lottery, Lotto-5, 6, Powerball, Mega Millions, Thunderball, Euromillions**_](https://saliu.com/lottery-utility.html).
-   [_**Lottery, Lotto Strategy in Reverse: Win, Lose, Hit, Miss**_](https://saliu.com/reverse-strategy.html).
-   [_**Software Analysis, Lotto Number Frequency, Lottery Pairs**_](https://saliu.com/lottery-lotto-pairs.html).
-   **Lotto, Powerball, Mega Millions, Euromillions, Horse Racing, Roulette, Football**: [**<u>Software, Systems, Lottery Skips</u>**](https://saliu.com/skip-strategy.html).
-   [_**Lotto, Decades: Systems, Software, Analysis, Strategy**_](https://saliu.com/decades.html).
-   [_**Lottery, Lotto Strategy Based On Sums (Sum-Totals), Odd – Even, Low - High Numbers**_](https://saliu.com/strategy.html).
-   [_**Lottery Strategy, Systems Based on Number Frequency**_](https://saliu.com/frequency-lottery.html).
-   [_**Lotto Software for Groups of Numbers: Odd, Even, Low, High, Sums, Frequencies, User's Groups**_](https://saliu.com/lotto-groups.html).
-   [_**The Best Ever Lottery Strategy, Lotto Strategies**_](https://software.saliu.com/lottery-strategy-lotto-strategies.html).

![The lottery software groups pick lotteries, horse racing, jackpot lotto games.](https://saliu.com/HLINE.gif)

**[Home](https://saliu.com/index.htm) | [Search](https://saliu.com/Search.htm) | [New Writings](https://saliu.com/bbs/index.html) | [Fundamental Formula](https://saliu.com/formula.htm) | [Odds, Generator](https://saliu.com/calculator_generator.html) | [Contents](https://saliu.com/content/index.html) | [Forums](https://forums.saliu.com/) | [Sitemap](https://saliu.com/sitemap/index.html)**

![Get source code to generate lotto combinations from 3 groups of numbers.](https://saliu.com/HLINE.gif)
