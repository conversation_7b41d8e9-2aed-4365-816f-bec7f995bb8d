---
created: 2025-07-24T23:08:11 (UTC +08:00)
tags: [lotto,software,lottery,UPDOW,UPDOWN,ASCII,text,reverse order,data,files,top,bottom,programming,programs,]
source: https://saliu.com/bbs/messages/539.html
author: 
---

# UpDown Software Program Reverse Order Files Lottery Lotto

> ## Excerpt
> UpDown is lottery software to reverse the order in lotto, lottery data, draws, drawings, history data text files, huge ASCII files, top to bottom.

---
Posted on January 21, 2001; later updates.

In Reply to: [_**Software to sort lotto, lottery drawings files in descending order**_](https://saliu.com/bbs/messages/413.html) posted by <PERSON> on November 12, 2000.

• I ran into hardship myself when I needed to update my lottery data files. There is no _consistent_ Internet format in presenting the lottery results. Moreover, the order is _bottom-recent, top-oldest_. It's exactly the opposite of the required _**LotWon**_ data format. So, I had to write a little program that reverses the order in ASCII files: **UpDown**. The bottom of the file becomes the top of a new file. You can press {Enter} at the second prompt to name the result file as the original (source) file. Or you can give it a different name, for increased safety.

Editor's Note  
**UpDown** ~ Version 3.2 ~ April 2009  
32-bit software running at the command prompt under Windows 95/98/2k/XP/Vista/Windows 7, 8, 10 (including 64-bit)  
The application accepts huge lottery data files (or any file in text format, including long filenames). It is highly recommended that you check the correctness of all your data files reasonably frequently. Errors are facts of life.

The program is a component of all **Bright / Ultimate Software** packages. You can run it, and all other lottery programs, from well-designed menus.

[![Pay Membership, Download Software: Lottery, Gambling, Roulette, Sports, Powerball, Mega Millions.](https://saliu.com/ScreenImgs/reverse-file.gif)](https://saliu.com/membership.html)

**Up Down** can be downloaded from the Software Download site (see the footer of this page, or check the following lottery resources). Check the main download site for all that's available for free to run forever. Only downloading requires a most reasonable membership fee (one-time payment). There is no better software deal anywhere else in the world.

Ion Saliu

: Do you have any time to write a lottery program like your _sort-34_, only I need to have it sort a column from new at top/old at bottom, to old at top to new at bottom. I need this so I can put into _Brainmaker_'s neural networking program. Any help anyone, please let me know the charge for this. :

![The UpDown lottery program reverses the order in lotto files, any ASCII text files.](https://saliu.com/bbs/messages/HLINE.gif)

Follow Ups:  

-   **CoolRevGui**: The Definitive [_**File Reverser and Text Viewer Software**_](https://saliu.com/programming.html) - **Ion Saliu** _12/06/2002._

![UpDown is software to reverse order in lottery, lotto results, drawings files.](https://saliu.com/bbs/messages/HLINE.gif)

[

## Resources in Lottery Software, Lotto Wheeling

](https://saliu.com/content/lottery.html)

-   The Main [_**Lotto, Lottery, Software, Strategy Systems**_](https://saliu.com/LottoWin.htm) Page.
-   Presenting software to create free winning lotto, lottery strategies, systems based on mathematics. Get your lotto systems or wheels, the best lottery, lotto software, combinations, winning numbers.
-   [_**Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies**_](https://saliu.com/Newsgroups.htm).
-   Read a genuine analysis of Excel spreadsheets applied to lottery and lotto software development, systems, and strategies. Combining Excel analysis with powerful lottery and lotto software programmed by this author, _Parpaluck_.
-   [_**<u>MDIEditor Lotto WE</u>: Tutorial, Software User Guide, Manual, Book, ebook**_](https://saliu.com/MDI-lotto-guide.html).
-   Also applicable to LotWon lottery, lotto software; plus Powerball/Mega Millions, Euromillions.
-   [_**Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps, Programs**_](https://saliu.com/forum/lotto-book.html).
-   [_**<u>Skip Systems</u> Software**_](https://saliu.com/skip-strategy.html): _**Lotto, Lottery, Powerball, Mega Millions, Euromillions**_.
-   [**<u>Lottery Utility Software</u>**](https://saliu.com/lottery-utility.html): _**Pick-3, 4 Lottery, Lotto-5, 6, Powerball, Mega Millions, Thunderball, Euromillions**_.
-   [**Lotto wheels**](https://saliu.com/lotto_wheels.html) for lottery games drawing 5, 6, or 7 numbers.
-   The most advanced theory of lotto wheels or reduced lottery systems. Get also original lotto wheels for lotto games drawing 5, 6, or 7 numbers: Balanced, randomized, and <u>free</u>.
-   [_**Neural Networking, Artificial Intelligence AI in Lottery, Lotto**_](https://saliu.com/neural-networking-lottery.html).
-   Download [**lottery software, lotto software**](https://saliu.com/infodown.html).

![UpDown is free lottery software to reverse the order in lotto or lottery data, draw, drawing, history files.](https://saliu.com/bbs/messages/HLINE.gif)

Comments:  

![UPDOWN software to reverse lotto, lottery files.](https://saliu.com/bbs/messages/HLINE.gif)

**[Home](https://saliu.com/index.htm) | [Search](https://saliu.com/Search.htm) | [New Writings](https://saliu.com/bbs/index.html) | [Software](https://saliu.com/infodown.html) | [Odds, Generator](https://saliu.com/calculator_generator.html) | [Contents](https://saliu.com/content/index.html) | [Forums](https://forums.saliu.com/) | [Sitemap](https://saliu.com/sitemap/index.html)**

![Ion Saliu: Software, Programs, Apps, Systems, Strategies.](https://saliu.com/bbs/messages/HLINE.gif)
