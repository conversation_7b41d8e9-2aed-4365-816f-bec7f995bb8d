# Integration Test Suite
# 整合測試套件 - 測試系統各組件之間的整合

using Test
using Dates
using Statistics

# 引入所有必要的模組
include("../src/types.jl")
include("../src/filter_engine.jl")
include("../src/filters/one_filter.jl")
include("../src/filters/two_filter.jl")
include("../src/filters/three_filter.jl")
include("../src/filters/four_filter.jl")
include("../src/filters/five_filter.jl")
include("../src/skip_analyzer.jl")
include("../src/ffg_calculator.jl")
include("../src/pairing_engine.jl")
include("../src/statistics/basic_stats.jl")
include("test_data_manager.jl")
include("test_configuration.jl")

"""
端到端工作流程測試
測試從數據載入到結果輸出的完整工作流程
"""
function test_end_to_end_workflow(test_data::Vector{LotteryDraw})::Dict{String, Any}
    @testset "End-to-End Workflow" begin
        results = Dict{String, Any}()
        workflow_steps = []
        
        try
            # 步驟 1：創建過濾器引擎
            engine = FilterEngine(test_data)
            push!(workflow_steps, Dict("step" => "filter_engine_creation", "status" => "success"))
            
            # 步驟 2：創建 Skip 分析器
            skip_analyzer = SkipAnalyzer(test_data)
            push!(workflow_steps, Dict("step" => "skip_analyzer_creation", "status" => "success"))
            
            # 步驟 3：創建 FFG 計算器
            ffg_calculator = FFGCalculator()
            push!(workflow_steps, Dict("step" => "ffg_calculator_creation", "status" => "success"))
            
            # 步驟 4：創建配對引擎
            pairing_engine = PairingEngine(test_data)
            push!(workflow_steps, Dict("step" => "pairing_engine_creation", "status" => "success"))
            
            # 步驟 5：執行所有過濾器
            test_numbers = [1, 2, 3, 4, 5]
            filter_results = Dict{String, Any}()
            
            filter_results["ONE"] = calculate_one_filter(engine, 1)
            filter_results["TWO"] = calculate_two_filter(engine, test_numbers[1:3])
            filter_results["THREE"] = calculate_three_filter(engine, test_numbers[1:4])
            filter_results["FOUR"] = calculate_four_filter(engine, test_numbers)
            filter_results["FIVE"] = calculate_five_filter(engine, test_numbers)
            
            push!(workflow_steps, Dict("step" => "filter_calculations", "status" => "success"))
            
            # 步驟 6：執行 Skip 分析
            skip_results = Dict{Int, Int}()
            for number in 1:5
                skip_results[number] = get_current_skip(skip_analyzer, number)
            end
            push!(workflow_steps, Dict("step" => "skip_analysis", "status" => "success"))
            
            # 步驟 7：執行 FFG 計算
            ffg_results = Dict{Int, Float64}()
            for number in 1:5
                ffg_results[number] = calculate_ffg_median(ffg_calculator, number, test_data)
            end
            push!(workflow_steps, Dict("step" => "ffg_calculations", "status" => "success"))
            
            # 步驟 8：執行配對分析
            pairing_results = Dict{String, Any}()
            pairing_results["distribution"] = analyze_pairing_distribution(pairing_engine)
            pairing_results["sorted_pairs"] = get_sorted_pairings(pairing_engine)[1:min(10, length(get_sorted_pairings(pairing_engine)))]
            push!(workflow_steps, Dict("step" => "pairing_analysis", "status" => "success"))
            
            # 步驟 9：整合所有結果
            integrated_results = Dict{String, Any}(
                "filters" => filter_results,
                "skips" => skip_results,
                "ffg" => ffg_results,
                "pairings" => pairing_results
            )
            push!(workflow_steps, Dict("step" => "result_integration", "status" => "success"))
            
            # 驗證結果的一致性
            @test length(filter_results) == 5
            @test length(skip_results) == 5
            @test length(ffg_results) == 5
            @test haskey(pairing_results, "distribution")
            @test haskey(pairing_results, "sorted_pairs")
            
            results["workflow_steps"] = workflow_steps
            results["integrated_results"] = integrated_results
            results["success_rate"] = 1.0
            
        catch e
            push!(workflow_steps, Dict("step" => "error", "status" => "failed", "error" => string(e)))
            results["workflow_steps"] = workflow_steps
            results["success_rate"] = 0.0
            @warn "端到端工作流程測試失敗: $e"
        end
        
        println("  ✅ 端到端工作流程測試: $(round(results["success_rate"] * 100, digits=1))%")
        return results
    end
end

"""
組件間數據一致性測試
測試不同組件之間數據的一致性
"""
function test_component_data_consistency(test_data::Vector{LotteryDraw})::Dict{String, Any}
    @testset "Component Data Consistency" begin
        results = Dict{String, Any}()
        consistency_checks = []
        
        # 創建所有組件
        engine = FilterEngine(test_data)
        skip_analyzer = SkipAnalyzer(test_data)
        pairing_engine = PairingEngine(test_data)
        
        # 檢查 1：數據長度一致性
        engine_data_length = length(engine.historical_data)
        skip_data_length = length(skip_analyzer.historical_data)
        pairing_data_length = length(pairing_engine.historical_data)
        
        length_consistent = (engine_data_length == skip_data_length == pairing_data_length == length(test_data))
        push!(consistency_checks, Dict(
            "check" => "data_length_consistency",
            "status" => length_consistent ? "passed" : "failed",
            "details" => Dict(
                "engine" => engine_data_length,
                "skip_analyzer" => skip_data_length,
                "pairing_engine" => pairing_data_length,
                "original" => length(test_data)
            )
        ))
        
        # 檢查 2：數據內容一致性
        content_consistent = true
        for i in 1:min(length(test_data), 10)  # 檢查前 10 筆數據
            if (engine.historical_data[i].numbers != test_data[i].numbers ||
                skip_analyzer.historical_data[i].numbers != test_data[i].numbers ||
                pairing_engine.historical_data[i].numbers != test_data[i].numbers)
                content_consistent = false
                break
            end
        end
        
        push!(consistency_checks, Dict(
            "check" => "data_content_consistency",
            "status" => content_consistent ? "passed" : "failed"
        ))
        
        # 檢查 3：Skip 計算一致性（比較不同方法）
        skip_consistency_results = []
        for number in [1, 5, 10]
            try
                # 方法 1：Skip 分析器
                analyzer_skip = get_current_skip(skip_analyzer, number)
                
                # 方法 2：ONE 過濾器
                filter_result = calculate_one_filter(engine, number)
                filter_skip = filter_result.current_value
                
                is_consistent = analyzer_skip == filter_skip
                push!(skip_consistency_results, Dict(
                    "number" => number,
                    "analyzer_skip" => analyzer_skip,
                    "filter_skip" => filter_skip,
                    "consistent" => is_consistent
                ))
            catch e
                push!(skip_consistency_results, Dict(
                    "number" => number,
                    "error" => string(e),
                    "consistent" => false
                ))
            end
        end
        
        skip_consistency_rate = count(r -> get(r, "consistent", false), skip_consistency_results) / length(skip_consistency_results)
        push!(consistency_checks, Dict(
            "check" => "skip_calculation_consistency",
            "status" => skip_consistency_rate >= 0.8 ? "passed" : "failed",
            "consistency_rate" => skip_consistency_rate,
            "details" => skip_consistency_results
        ))
        
        # 計算整體一致性評分
        passed_checks = count(check -> check["status"] == "passed", consistency_checks)
        total_checks = length(consistency_checks)
        overall_consistency = passed_checks / total_checks
        
        results["consistency_checks"] = consistency_checks
        results["overall_consistency"] = overall_consistency
        results["passed_checks"] = passed_checks
        results["total_checks"] = total_checks
        
        @test overall_consistency >= 0.8  # 要求 80% 以上的一致性
        
        println("  ✅ 組件數據一致性: $(round(overall_consistency * 100, digits=1))%")
        return results
    end
end

"""
系統負載測試
測試系統在不同負載下的表現
"""
function test_system_load(test_data::Vector{LotteryDraw})::Dict{String, Any}
    @testset "System Load Test" begin
        results = Dict{String, Any}()
        load_tests = []
        
        # 負載測試 1：大量過濾器計算
        try
            engine = FilterEngine(test_data)
            start_time = time()
            
            for _ in 1:50  # 執行 50 次計算
                calculate_one_filter(engine, rand(1:39))
                calculate_two_filter(engine, sort(rand(1:39, 3)))
            end
            
            filter_load_time = time() - start_time
            push!(load_tests, Dict(
                "test" => "filter_load",
                "execution_time" => filter_load_time,
                "status" => filter_load_time < 10.0 ? "passed" : "failed"  # 應該在 10 秒內完成
            ))
        catch e
            push!(load_tests, Dict(
                "test" => "filter_load",
                "status" => "failed",
                "error" => string(e)
            ))
        end
        
        # 負載測試 2：大量 Skip 分析
        try
            skip_analyzer = SkipAnalyzer(test_data)
            start_time = time()
            
            for _ in 1:100  # 執行 100 次 Skip 查詢
                get_current_skip(skip_analyzer, rand(1:39))
            end
            
            skip_load_time = time() - start_time
            push!(load_tests, Dict(
                "test" => "skip_load",
                "execution_time" => skip_load_time,
                "status" => skip_load_time < 5.0 ? "passed" : "failed"  # 應該在 5 秒內完成
            ))
        catch e
            push!(load_tests, Dict(
                "test" => "skip_load",
                "status" => "failed",
                "error" => string(e)
            ))
        end
        
        # 負載測試 3：大量配對查詢
        try
            pairing_engine = PairingEngine(test_data)
            start_time = time()
            
            for _ in 1:100  # 執行 100 次配對查詢
                num1, num2 = rand(1:39), rand(1:39)
                if num1 != num2
                    get_pairing_frequency(pairing_engine, num1, num2)
                end
            end
            
            pairing_load_time = time() - start_time
            push!(load_tests, Dict(
                "test" => "pairing_load",
                "execution_time" => pairing_load_time,
                "status" => pairing_load_time < 3.0 ? "passed" : "failed"  # 應該在 3 秒內完成
            ))
        catch e
            push!(load_tests, Dict(
                "test" => "pairing_load",
                "status" => "failed",
                "error" => string(e)
            ))
        end
        
        # 計算負載測試成功率
        passed_tests = count(test -> test["status"] == "passed", load_tests)
        total_tests = length(load_tests)
        load_success_rate = passed_tests / total_tests
        
        results["load_tests"] = load_tests
        results["load_success_rate"] = load_success_rate
        results["passed_tests"] = passed_tests
        results["total_tests"] = total_tests
        
        @test load_success_rate >= 0.8  # 要求 80% 以上的負載測試通過
        
        println("  ✅ 系統負載測試: $(round(load_success_rate * 100, digits=1))%")
        return results
    end
end

"""
錯誤處理和恢復測試
測試系統的錯誤處理能力
"""
function test_error_handling()::Dict{String, Any}
    @testset "Error Handling and Recovery" begin
        results = Dict{String, Any}()
        error_tests = []
        
        # 錯誤測試 1：空數據處理
        try
            empty_data = LotteryDraw[]
            engine = FilterEngine(empty_data)
            
            # 嘗試計算過濾器（應該優雅地處理或拋出合理的異常）
            try
                result = calculate_one_filter(engine, 1)
                push!(error_tests, Dict("test" => "empty_data_filter", "status" => "handled"))
            catch e
                # 拋出異常也是可接受的
                push!(error_tests, Dict("test" => "empty_data_filter", "status" => "handled"))
            end
        catch e
            push!(error_tests, Dict("test" => "empty_data_filter", "status" => "handled"))
        end
        
        # 錯誤測試 2：無效號碼處理
        test_data = [LotteryDraw([1, 2, 3, 4, 5], Date(2022, 1, 1), 1)]
        try
            engine = FilterEngine(test_data)
            
            # 測試超出範圍的號碼
            try
                result = calculate_one_filter(engine, 0)  # 無效號碼
                push!(error_tests, Dict("test" => "invalid_number", "status" => "not_handled"))
            catch e
                # 應該拋出異常
                push!(error_tests, Dict("test" => "invalid_number", "status" => "handled"))
            end
        catch e
            push!(error_tests, Dict("test" => "invalid_number", "status" => "handled"))
        end
        
        # 錯誤測試 3：記憶體壓力測試
        try
            # 創建大量對象來測試記憶體處理
            large_data = [LotteryDraw(rand(1:39, 5), Date(2022, 1, i), i) for i in 1:1000]
            engine = FilterEngine(large_data)
            
            # 執行一些計算
            result = calculate_one_filter(engine, 1)
            push!(error_tests, Dict("test" => "memory_pressure", "status" => "handled"))
        catch e
            push!(error_tests, Dict("test" => "memory_pressure", "status" => "failed", "error" => string(e)))
        end
        
        # 計算錯誤處理成功率
        handled_tests = count(test -> test["status"] == "handled", error_tests)
        total_tests = length(error_tests)
        error_handling_rate = handled_tests / total_tests
        
        results["error_tests"] = error_tests
        results["error_handling_rate"] = error_handling_rate
        results["handled_tests"] = handled_tests
        results["total_tests"] = total_tests
        
        @test error_handling_rate >= 0.6  # 要求 60% 以上的錯誤被正確處理
        
        println("  ✅ 錯誤處理測試: $(round(error_handling_rate * 100, digits=1))%")
        return results
    end
end

"""
執行完整的整合測試套件
"""
function run_integration_test_suite(data_manager::TestDataManager)::Dict{String, Any}
    println("🧪 開始執行整合測試套件...")
    
    integration_results = Dict{String, Any}()
    
    # 使用中等大小的測試數據
    test_data = get_test_data(data_manager, "medium")
    
    try
        # 執行各項整合測試
        integration_results["end_to_end"] = test_end_to_end_workflow(test_data)
        integration_results["data_consistency"] = test_component_data_consistency(test_data)
        integration_results["system_load"] = test_system_load(test_data)
        integration_results["error_handling"] = test_error_handling()
        
        # 計算整體評分
        end_to_end_score = integration_results["end_to_end"]["success_rate"]
        consistency_score = integration_results["data_consistency"]["overall_consistency"]
        load_score = integration_results["system_load"]["load_success_rate"]
        error_score = integration_results["error_handling"]["error_handling_rate"]
        
        overall_score = (end_to_end_score + consistency_score + load_score + error_score) / 4
        integration_results["overall_score"] = overall_score
        
        println("\n📊 整合測試套件結果:")
        println("  - 端到端工作流程: $(round(end_to_end_score * 100, digits=1))%")
        println("  - 數據一致性: $(round(consistency_score * 100, digits=1))%")
        println("  - 系統負載: $(round(load_score * 100, digits=1))%")
        println("  - 錯誤處理: $(round(error_score * 100, digits=1))%")
        println("  - 整體評分: $(round(overall_score * 100, digits=1))%")
        
        if overall_score >= 0.95
            println("🎉 整合測試：優秀")
        elseif overall_score >= 0.85
            println("✅ 整合測試：良好")
        elseif overall_score >= 0.75
            println("⚠️ 整合測試：需要改進")
        else
            println("❌ 整合測試：需要重大修復")
        end
        
        return integration_results
        
    catch e
        @error "整合測試套件失敗: $e"
        integration_results["error"] = string(e)
        integration_results["overall_score"] = 0.0
        return integration_results
    end
end

# 導出主要函數
export test_end_to_end_workflow, test_component_data_consistency
export test_system_load, test_error_handling
export run_integration_test_suite
