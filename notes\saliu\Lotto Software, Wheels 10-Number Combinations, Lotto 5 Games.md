---
created: 2025-07-24T21:26:40 (UTC +08:00)
tags: [5 number lotto,10-number lottery combinations,software,software,strategy,lotto wheel,combination,lottery filters,draws,drawings,jackpot,]
source: https://saliu.com/lotto-10-5-combinations.html
author: 
---

# Lotto Software, Wheels 10-Number Combinations, Lotto 5 Games

> ## Excerpt
> Lotto strategy applies to 5-number lotto games to generate 10-number combinations converted to 5 numbers with a 10-number lotto wheel assuring 3 of 5 winners.

---
Published in October 2010.

<big>• Bright15</big>: New lotto software generating 10-number combinations based on data files (real drawings) in 5-number lotto games.

-   This lotto software package is no longer updated. Instead, all programs specific to this package were moved to the most comprehensive 5-number lotto software suite: **Bright5**. You should always run **Bright 5** as it is always up-to-the-date, with all known errors ironed out.
-   The powerful loto-5 software package runs under 32-bit/64-bit Windows OS and [_**32 / 64-bit Windows Vista / Windows 7, 8, 10**_](https://saliu.com/gambling-lottery-lotto/command-prompt.htm) via a great-looking and highly functional interface.
-   The new lottery software requires paid [_**Permanent Membership**_](https://saliu.com/membership.html) in order to download it; the usage is totally free thereafter.

The options specific to this lotto 5-10 software package and different from **Bright5** are _**F (filters)**_ and _**C (10-number lotto combination generating)**_. The filter report is generated by a lotto program named <big>LottoGroupSkips5exe</big>. The 10-number lotto combinations are generated by the <big>Combine5-10</big> application. I left in the bundle all the programs incorporated in **Bright-5**. I thought it convenient to do some lotto calculations or combination generating in the same interface, instead of hunting in different software packages.

First, let's take an artistically educated peek at the main menus.

![Lotto Strategy, Software: 10-numbers Combinations in 5-number Lotto Games.](https://saliu.com/ScreenImgs/lotto-5-10.gif)

There was a mix of curiosity and skepticism (perhaps cynicism as well) expressed in my forums this year of grace 2010. It was related to an old lotto 6/40 strategy that hit the jackpot but it wasn't played (1986)! I started the talk back in January 17, 2001. I wrote this message in my oldest forum: [_**History of my Lottery, Lotto Experience: Software, Systems**_](https://saliu.com/bbs/messages/532.html).

That post triggered questions in my previous forums and also from readers of my book. Why don't I present clearly and in detail that lotto strategy that hit a jackpot in 1986 (although it was not played)? I would start two threads in the forum, for open discussions on that lotto strategy. Then, I put together an article at SALIU.COM with human ad technical details (computer programming, software, probability theory).

Also, the request to write specific software for this type of lotto strategy was imminent. The delay was also imminent primarily because the task is daunting.

To my surprise, I discovered yet another lottery programming technique. I only needed one logical bloc from my lotto-6 software. That logical bloc almost cloned itself a number of times equal to all those subgroups for 12-number generation! It was quite easy and surprisingly fast. I discovered also new tools to check for precision. The probability is very high that my lotto code for 12 numbers is as accurate as it can be. The 10-number software for lotto 5 generation was even easier; I only had to delete logical branches that didn't apply to 5-number combinations. It took me around two hours to complete the final version of the software!

![Convert the combinations to 5 numbers using a special 10-number lotto wheel assuring '3 of 5' winners.](https://saliu.com/HLINE.gif)

Writing started, however, with 6-number lotto, specifically _bookie lotteries_. The discussion began in my previous forum and was triggered by a judgment error in my lotto pairing software. From there, we went on to bookie lotteries where the player can play groups of 2, or 3, or 4 numbers (instead of 6-number tickets as in traditional lotto).

At last, I wrote the software for the lotto-6 games generating 12-number combinations. It is all presented here:

-   [_**Lotto Strategy, Software: 12-numbers Combinations in 6-number Lotto Games**_](https://saliu.com/12-number-lotto-combinations.html).

I expanded the concept and I wrote <big>Lotto Group Skips 5</big>. The lotto program generates a special report for lotto-5 regarding the SKIPS of the subgroups of numbers: _**Singles (ones), Pairs, Triples, Quads, and Quintet**_. The report shows, draw by draw, when each subgroup last hit; that is, how many drawings each subgroup skipped in the most recent past. These 5 parameters will serve as filters in the lotto program that generates 10-number combinations while working with a 5-number lotto results file.

Here is the beginning of a report:

![Lotto Strategy for 5 numbers in 10-number combinations of singles to quintets.](https://saliu.com/ScreenImgs/lotto-quintets.gif)

<big>•</big> The relationship between the above filters and the combination generators in **Bright-5** (Lexicographic, Optimized random, Wheeling) and **MDIEditor Lotto WE**:

-   _**Ones (Singles)**_ is identical to _**ANY1\_1**_ in **Bright5** AND in **MDIEditor And Lotto**;
-   _**Pairs**_ is identical to _**TWOS**_ in **Bright 5**; inexistent in **MDIEditor Lotto WE**;
-   _**Triples**_ is identical to _**THREES**_ in **Bright-5**; inexistent in **MDIEditor Lotto**;
-   _**Quads**_ is inexistent in Bright5 or **MDIEditor And Lotto WE**; **Bright 5** applies a Del4 filter that is based on deltas for groups of _4 of 5_ numbers;
-   _**Quintet**_ is inexistent in **Bright 5**; **Bright5** applies a _Del5_ filter that is based on deltas for groups of _5 of 5_ numbers; identical to _**Past Draws**_ in **MDIEditor Lotto**.

![Analyzing groups: singles, pairs, triples, quadruples, quintuples in lotto 5-of-43 games.](https://saliu.com/HLINE.gif)

The filters reported in the first program will feed the 10-number lotto combination generator: <big>Combine5-10</big>. The lotto software generates random combinations for lotto-5, but 10 numbers per combination; the data file must still be in the 5-numbers-per-line format as in the **Bright5** programs. Only the minimum levels of the following filters are applicable in this type of software: _Ones, Pairs, Triples, Quads,_ and _Quintet_.

Looking at the filters above, you might be inclined to apply the highest values to the 10-number lotto generator. Please keep in mind that 10-number games are very different from 5-number lotto games. Obviously, the odds are far higher as the number of total possible combinations is far larger. Furthermore, there is a limit imposed by the performance of the computers. Generating 10-number combinations with high filter levels can quickly come to a halt. The computers are not fast enough to quickly find qualifying 10-number lotto combinations.

In addition to the performance limitations of the computers, one should be mindful also that a minimum number of subgroups are needed in order to build at least one combination. In _5 from 43_ lotto, for example, we can eliminate up to 38 single-number groups (the Ones filter). In that extreme case, 43 – 38 = 5 numbers left. In _10 from 43_ lotto, we can't eliminate more than 33 lotto numbers. What I want to say is that the filter levels for 10-number lotto must be significantly lower than for lotto-5 games. But there is still equivalency from 5 to 10 numbers per combination. Say, we enable the _Quintet_ filter in **Combine 5-10**. The program will generate combinations that do not repeat any of the past 5-number draws set in _Quintet_. You can break the 10-number combo into 252 groups of 5 lotto numbers. None of the 252 will repeat any drawing from the range set by the _Quint_ filter.

Problem is, **Combine-5-10** will have a hard time finding all those gazillions of 10-number lotto combinations that satisfy the restrictions imposed by very high filters. There are 1917334783 (almost 2 billion) combinations in a '43 numbers taken 10 at a time' set. Still, substantially lower than the previous case of generating 12-number combinations for a 6/49 lotto games (over 92 billion _combosnations_ — a favorite term of mine!)

I did several tests on my PC. I could not get to generate any 10-number combination for a 43-ball lotto number. I came across maximum values for the filters that my computer can't climb.

First of all, I used a real data file with drawings from the 5-43 lotto game played in Pennsylvania (named _Cash 5_). I created a data file from the beginning of that lotto game. A file of simulated lotto drawings is mandatory. I used the best SIMulated data file for 5 / 43 lotto there is. I generated all 962598 possible combinations in lexicographical order. My special software, **PermuteCombine**, is best suited for the task. Then, I shuffled (randomized) that file in **Shuffle** (part of the **Bright15** package). The result was _SIM-5_. I combined the real data file with _SIM-5_ and the result was _D5_, the big data file used by **Combine 5-10**.

My personal computer has these performance specs:

Processor Intel(R) Core(TM)2 Quad CPU Q6600 @ 2.40GHz, 2403 Mhz, 4 Core(s), 4 Logical Processor(s)  
Installed Physical Memory (RAM) 4.00 GB  
OS Name Microsoft Windows 7 Professional  
System Type x64-based PC

These are non-rocket-science maximum values of filters I encountered:

Triples: 150  
Quads: 60000  
Quintet: 250000

That is, at each of those filter levels the program slowly, slowly generated lotto 10-43 combinations. A lot depends on your computer. You will need a few trial-and-error runs. Run this software at Command Prompt only. Close down all applications, including Internet browsers.

You might want to try lower settings for the _Triple, Quad,_ and _Quintet_ filters. The generation process should be slow, however. A slow generation is a favorite of randomness. The chance is better you'll get the 5-number winner within a small amount of 10-number combinations. Repeat, a 10-number combination expands to 252 lotto combinations that did not repeat from the past drawings. Such combinations have a better chance to hit the lotto jackpot in the near future.

We all here know by now that everything is ruled by _**randomness**_. Furthermore, any random event does _**repeat**_ within a number of trials precisely calculated by mathematics (with a high degree of certainty, that is). Thusly, a _5 of 5_ lotto combination in a 5/43 lotto game will repeat from 1156 past drawings with a 50% degree of certainty. My probability software **Collisions** makes the calculations a breeze. Read this eBook, will you:

-   _Applications uvda_ [_**<u>Birthday Paradox</u>: Lottery, Lotto, Roulette**_](https://saliu.com/birthday-paradox.html).

So, I chose three 10-number combinations. I wheeled each combination twice by applying one lotto wheel I created. I just added it to the Web page dedicated to special lotto wheeling:

-   [_**Wheeling Lotto Numbers, Playing Lotto Wheels, Make Your Own Lottery Systems**_](https://saliu.com/lottowheel.html).

The lotto wheel consists of 4 groups of 2 or 3 numbers each, for a total of 10 numbers in the pool. Two of the groups consist of 3 numbers each: 1-2-3 and 6-7-8. The other two groups consist of 2 numbers apiece: 4-5 and 9-10. Each number appears 3 times in the 5-number lotto wheel (6 lines, '3 of 5' minimum guarantee). I no longer sort the number pools lexicographically. I randomize or shuffle the numbers. The more randomization iterations, the better the probability to come across the winning lotto combination.

![Applying the 10-number lotto strategy and checking for winners in a real-result lotto file.](https://saliu.com/HLINE.gif)

The best way to apply the 10-number lotto wheel is randomization. The 10-number lotto combinations are already in random order. Leave them as-is. If you want to apply the 10-lotto wheel twice, group the numbers by frequency. The _**Super Utilities**_ option (**Software Lotto 5**) creates very good frequency reports for pairs and triples. Group lotto numbers that show good frequency together. Another option to consider is to wheel a 10-number lotto combination only once. That way you can choose to play twice as many 10-number combinations. Again, my lottery software is the best tool to wheel your lotto numbers: **Lotto Wheeler**.

You can see how a lotto strategy fared in the past by going back 100 drawings in your game. Delete the top 100 draws in your _Data-5_ file. Save it as _Data-5.2_. Recreate the new _D5_ (_Data-5.2+SIM-5 D5_). Run **Combine 5-10** with your filter settings (the slow generating process). Select your combinations from the bottom of the output file. Use **Winners** (also included in **Bright 15**) to check for winners against your original _Data-5_. Repeat the process by deleting the top 99 draws from your _Data-5_. Save again as _Data-5.2_. Recreate _D5_, etc.

<u>How to use <b>Winners</b> to check for hits against your file with real lotto drawings.</u>  
Of course, you applied a 10-number lotto wheel, so you have a group of 5-number lotto combinations (with the numbers to play). Run **Winners** for such a group and against your original _Data-5_ (never against _Data-5.2_ or _D5_).

You can also use Winners for 10-number combinations without applying a lotto wheel first. You chose a number of 10-number lotto combinations. You don't wheel them. You want to check how that group of 10-number lines would have fared in the past. Because, by wheeling the lotto numbers, chances are you will lose the jackpot combination more often than not. In this case, you run **Break Down Numbers** (option _**B: Break Down Lines of Numbers**_). The program will break the 10-number lines (combinations) into groups of 5 lotto numbers each. Feed that group to **Winners** (as input file).

<u>Caveat:</u>  
**BreakDownNumbers** will eliminate all duplicates and allow only unique 5-number combinations. You might notice, however, that 10-number lines generated by **Combine5-10** have in common 5, or 6, or 7 groups of numbers. Therefore, multiple jackpot lotto combinations are possible, but **Winners** will report only one.

![The old lotto strategy is also…new: It applies to bookie lotteries as well.](https://saliu.com/HLINE.gif)

**Bright 15** is immediately available to download to registered members:

-   [_**Download**_ **<u>Bright15</u>** _**from this location.**_](https://saliu.com/code/BRIGHT15.exe)

Before downloading, create a folder (directory) for this software package; e.g. **BRIGHT15**. You can create a folder more easily in the GUI mode, in _Windows Explorer_ (_File Explorer_ in Windows 8, 10). Open Explorer and find your drive **C:**. You will probably find it under _My Computer_ (or simply _Computer_ in Vista / Windows 7; _This PC_ in Windows 10). Double click **C:** in the left pane. Move to the right pane, all the way down. Right click on the empty space. From the ensuing menu select _New_, then _Folder_. In the highlighted _New Folder_ type **BRIGHT15**. You download **Bright 15** from **saliu.com** to your **C:\\BRIGHT15** folder.

You can decompress **BRIGHT15** in Windows Explorer. Navigate to your **C:** drive, then to the **BRIGHT15** folder. Double click on the folder. Move to the right pane and now double click **BRIGHT15**. The self-decompression starts automatically. Select the same folder for the location of the decompressed files. You can do the same thing at the _**command prompt**_. At the **C:\\BRIGHT15** prompt, you type: **BRIGHT15** and press _Enter_. The decompression starts automatically and you can select the same folder as the destination.

After decompression, read either _README.TXT_ or _README15.TXT_. Type **B15** (or **b15**) to start the application. Remember also, B15 is a vitamin extremely beneficial to stamina and energy. It is not allowed in the United States as a vitamin, but there are supplements based on _calcium pangamate_.

![Run lotto software to generate 10-number combinations for Cash 5 lotto games.](https://saliu.com/HLINE.gif)

[

## Resources in Lottery Software, Strategies, Systems, Lotto Wheels

](https://saliu.com/content/lottery.html)See a comprehensive directory of the pages and materials on the subject of lottery, lotto, software, systems, lotto wheeling.

-   [_**Lotto Strategy, Software: 12-numbers Combinations in 6-number Lotto Games**_](https://saliu.com/12-number-lotto-combinations.html).
-   [_**Lotto Software: <u>7-by-7-Number Matrices</u> (Perfect Squares) in 6-49 Lotto Games**_](https://saliu.com/7by7-lotto-6-49.html).
-   [_**Lotto Strategy: 12-Number Combinations Wheeled, Played in Lotto-6 Games**_](https://saliu.com/lotto-jackpot-lost.html).
-   [_**History of Lottery Experiences: Lotto Systems, Software, Strategies**_](https://saliu.com/bbs/messages/532.html).
-   [_**Randomness, Degree of Randomness, Absolute Certainty, True Random Numbers**_](https://saliu.com/bbs/messages/683.html).
-   [**<u>Play-All-Lotto-Numbers Analysis</u>**: _**Shuffle Software, Systems**_](https://saliu.com/all-lotto-numbers.html).  
    British professors won the UK National Lottery jackpot. They played all the numbers in the lotto game by shuffling or randomizing the lotto 6/49 numbers.
-   Are [_**lottery, gambling winnable**_](https://saliu.com/TRUTH.html) consistently — or is Ion Saliu just another crook or lunatic?
-   [**<u>Skip Systems</u>** _**for Lottery, Powerball, Mega Millions, Euromillions**_](https://saliu.com/skip-strategy.html).
-   [_**Lottery Utility Software**_](https://saliu.com/lottery-utility.html): Pick-3, 4 Lottery, Lotto-5, 6, Powerball, Mega Millions, Thunderball, Euromillions.
-   [_**Basics of Lottery, Lotto Strategy Based on: Sums (Sum-Totals); Odd / Even; Low / High Numbers**_](https://saliu.com/strategy.html).
-   [_**Lottery Strategy, Systems Based on Number Frequency**_](https://saliu.com/frequency-lottery.html).
-   [**<u>Lotto Decades</u>**: _**Analysis, Software, Systems**_](https://saliu.com/decades.html).
-   The Best Analysis Ranges for [_**Lotto Number Frequency, Lottery Pairs**_](https://saliu.com/lottery-lotto-pairs.html).
-   [_**<u>MDIEditor Lotto WE</u>: Tutorial, Software User Guide, Manual, Book, ebook**_](https://saliu.com/MDI-lotto-guide.html).  
    ~ Also applicable to LotWon lottery, lotto software; plus Powerball, Mega Millions, Euromillions.
-   [_**Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps, Programs**_](https://saliu.com/forum/lotto-book.html).
-   Practical [_**Lottery Filtering, Lotto Filters in Software**_](https://saliu.com/filters.html).
-   [_**Create Lotto Wheels: Manually, in Lotto Wheeling Software**_](https://saliu.com/lottowheel.html).
-   [_**Free lottery wheeling software for players of lotto wheels**_](https://saliu.com/bbs/messages/857.html).  
    ~ Fill out lotto wheels with player's picks (numbers to play); presenting **FillWheel, LottoWheeler** lottery wheeling software.
-   _**Download**_ [**<u>lottery software, lotto programs</u>**](https://saliu.com/infodown.html).

![Lotto Strategy, Software: 10-numbers Combinations in 5-number Lotto Games.](https://saliu.com/HLINE.gif)

**[Home](https://saliu.com/index.htm) | [Search](https://saliu.com/Search.htm) | [New Writings](https://saliu.com/bbs/index.html) | [Fundamental Formula](https://saliu.com/formula.htm) | [Odds, Generator](https://saliu.com/calculator_generator.html) | [Contents](https://saliu.com/content/index.html) | [Forums](https://forums.saliu.com/) | [Sitemap](https://saliu.com/sitemap/index.html)**

![You exit the site of the new best lotto software and strategies to win the jackpot.](https://saliu.com/HLINE.gif)
