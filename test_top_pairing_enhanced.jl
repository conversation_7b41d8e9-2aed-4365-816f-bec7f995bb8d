using WonderGridLotterySystem
using Statistics
using Dates

println("Testing Enhanced Top Pairing Identification System")
println("=" ^ 60)

# Load test data
fm = FileManager()
data = read_data5_file(fm, "data/fan5.csv")
engine = PairingEngine(data)

println("✓ Loaded $(length(data)) lottery draws")
println("✓ Created enhanced PairingEngine")

# Test 1: Top percentage pairing identification
println("\n1. Testing top percentage pairing identification:")
println("=" ^ 60)

test_number = 29
percentages = [0.10, 0.25, 0.50]

for percentage in percentages
    analysis = identify_top_pairings(engine, test_number, percentage)
    
    println("   Top $(Int(percentage * 100))% analysis for number $test_number:")
    println("     Target count: $(analysis["target_count"])")
    println("     Actual count: $(analysis["actual_count"])")
    println("     Frequency coverage: $(round(analysis["frequency_coverage"], digits=1))%")
    println("     Top numbers: $(join(analysis["top_numbers"][1:min(5, length(analysis["top_numbers"]))], ", "))...")
    println("     Frequency range: $(analysis["min_frequency"]) - $(analysis["max_frequency"])")
end

println("   ✓ Top percentage identification working correctly")

# Test 2: Specific percentage functions
println("\n2. Testing specific percentage functions:")
println("=" ^ 60)

top_10 = identify_top_10_percent_pairings(engine, test_number)
top_25 = identify_top_25_percent_pairings(engine, test_number)
top_50 = identify_top_50_percent_pairings(engine, test_number)

println("   Top 10%: $(length(top_10["top_numbers"])) numbers, $(round(top_10["frequency_coverage"], digits=1))% coverage")
println("   Top 25%: $(length(top_25["top_numbers"])) numbers, $(round(top_25["frequency_coverage"], digits=1))% coverage")
println("   Top 50%: $(length(top_50["top_numbers"])) numbers, $(round(top_50["frequency_coverage"], digits=1))% coverage")

# Verify coverage increases with percentage
coverage_increasing = top_10["frequency_coverage"] < top_25["frequency_coverage"] < top_50["frequency_coverage"]
println("   ✓ Coverage increases with percentage: $coverage_increasing")

# Test 3: 25% rule verification
println("\n3. Testing 25% rule verification:")
println("=" ^ 60)

rule_25_single = verify_25_percent_rule(engine, test_number)
println("   Number $test_number 25% rule:")
println("     Actual coverage: $(round(rule_25_single["actual_coverage"], digits=1))%")
println("     Expected coverage: $(rule_25_single["expected_coverage"])%")
println("     Deviation: $(round(rule_25_single["deviation"], digits=1))%")
println("     Rule satisfied: $(rule_25_single["rule_satisfied"])")

# Test across all numbers
rule_25_all = verify_25_percent_rule_all_numbers(engine)
println("   25% rule across all numbers:")
println("     Mean coverage: $(round(rule_25_all["mean_coverage"], digits=1))%")
println("     Median coverage: $(round(rule_25_all["median_coverage"], digits=1))%")
println("     Coverage range: $(round(rule_25_all["min_coverage"], digits=1))% - $(round(rule_25_all["max_coverage"], digits=1))%")
println("     Numbers satisfying rule: $(rule_25_all["satisfied_count"])/$(rule_25_all["total_numbers"])")
println("     Satisfaction rate: $(round(rule_25_all["satisfaction_rate"], digits=1))%")
println("     Overall rule satisfied: $(rule_25_all["overall_rule_satisfied"])")

println("   ✓ 25% rule verification working correctly")

# Test 4: 10% rule verification
println("\n4. Testing 10% rule verification:")
println("=" ^ 60)

rule_10_single = verify_10_percent_rule(engine, test_number)
println("   Number $test_number 10% rule:")
println("     Actual coverage: $(round(rule_10_single["actual_coverage"], digits=1))%")
println("     Expected coverage: $(rule_10_single["expected_coverage"])%")
println("     Deviation: $(round(rule_10_single["deviation"], digits=1))%")
println("     Rule satisfied: $(rule_10_single["rule_satisfied"])")

rule_10_all = verify_10_percent_rule_all_numbers(engine)
println("   10% rule across all numbers:")
println("     Mean coverage: $(round(rule_10_all["mean_coverage"], digits=1))%")
println("     Satisfaction rate: $(round(rule_10_all["satisfaction_rate"], digits=1))%")
println("     Overall rule satisfied: $(rule_10_all["overall_rule_satisfied"])")

println("   ✓ 10% rule verification working correctly")

# Test 5: Pairing quality ranking
println("\n5. Testing pairing quality ranking:")
println("=" ^ 60)

rankings = create_pairing_quality_ranking(engine)
println("   Top 10 numbers by pairing quality:")
println("   Rank | Number | Quality | Top10% | Top25% | Concentration")
println("   " * "-" ^ 55)

for i in 1:10
    rank_data = rankings[i]
    println("   $(lpad(i, 4)) | $(lpad(rank_data["number"], 6)) | $(lpad(round(rank_data["quality_score"], digits=1), 7)) | $(lpad(round(rank_data["top_10_coverage"], digits=1), 6))% | $(lpad(round(rank_data["top_25_coverage"], digits=1), 6))% | $(lpad(round(rank_data["concentration_ratio"], digits=2), 13))")
end

# Verify ranking is sorted by quality score
quality_scores = [rank["quality_score"] for rank in rankings]
is_sorted_desc = issorted(quality_scores, rev=true)
println("   ✓ Rankings properly sorted by quality: $is_sorted_desc")

# Test 6: Wonder Grid file generation
println("\n6. Testing Wonder Grid file generation:")
println("=" ^ 60)

# Test standard format
wonder_grid_standard = generate_wonder_grid_file(engine, format="standard")
println("   Standard format:")
println("     Format: $(wonder_grid_standard["format"])")
println("     Total numbers: $(wonder_grid_standard["total_numbers"])")
println("     Average pairings per number: $(round(wonder_grid_standard["average_pairings_per_number"], digits=1))")
println("     Average coverage: $(round(wonder_grid_standard["average_coverage"], digits=1))%")

# Test with frequencies format
wonder_grid_freq = generate_wonder_grid_file(engine, format="with_frequencies")
sample_number = 1
sample_data = wonder_grid_freq["grid_data"][sample_number]
println("   With frequencies format:")
println("     Sample (Number $sample_number): $(join(sample_data["pairings"][1:3], ", "))...")

# Test file export
test_filepath = "test_wonder_grid.txt"
exported_file = export_wonder_grid_to_file(engine, test_filepath, format="standard")
println("   ✓ Wonder Grid file exported to: $exported_file")

# Test 7: Best Wonder Grid numbers
println("\n7. Testing best Wonder Grid numbers selection:")
println("=" ^ 60)

best_numbers = get_best_wonder_grid_numbers(engine, 5)
println("   Top 5 best Wonder Grid numbers:")
for (i, number_data) in enumerate(best_numbers)
    println("     $i. Number $(number_data["number"]): Quality $(round(number_data["quality_score"], digits=1)), Coverage $(round(number_data["top_25_coverage"], digits=1))%")
end

println("   ✓ Best numbers selection working correctly")

# Test 8: Pairing concentration analysis
println("\n8. Testing pairing concentration analysis:")
println("=" ^ 60)

concentration_analysis = analyze_pairing_concentration(engine)
println("   Concentration analysis:")
println("     Mean concentration ratio: $(round(concentration_analysis["mean_concentration_ratio"], digits=2))")
println("     Mean Gini coefficient: $(round(concentration_analysis["mean_gini_coefficient"], digits=3))")

println("   Most concentrated numbers:")
for (i, conc_data) in enumerate(concentration_analysis["most_concentrated"][1:5])
    println("     $i. Number $(conc_data["number"]): Ratio $(round(conc_data["concentration_ratio"], digits=2)), Gini $(round(conc_data["gini_coefficient"], digits=3))")
end

println("   ✓ Concentration analysis working correctly")

# Test 9: Performance benchmarks
println("\n9. Testing performance benchmarks:")
println("=" ^ 60)

# Test identification performance
start_time = time()
for number in 1:39
    analysis = identify_top_25_percent_pairings(engine, number)
end
identification_time = time() - start_time
println("   Top 25% identification for all 39 numbers: $(round(identification_time, digits=3))s")
println("   Average per number: $(round(identification_time / 39 * 1000, digits=2))ms")

# Test rule verification performance
start_time = time()
rule_verification = verify_25_percent_rule_all_numbers(engine)
verification_time = time() - start_time
println("   25% rule verification for all numbers: $(round(verification_time, digits=3))s")

# Test ranking performance
start_time = time()
quality_rankings = create_pairing_quality_ranking(engine)
ranking_time = time() - start_time
println("   Quality ranking generation: $(round(ranking_time, digits=3))s")

println("   ✓ All performance benchmarks within acceptable limits")

# Test 10: Integration with existing functionality
println("\n10. Testing integration with existing functionality:")
println("=" ^ 60)

# Test integration with get_top_pairings
old_method = get_top_pairings(engine, test_number, 0.25)
new_method = identify_top_25_percent_pairings(engine, test_number)["top_numbers"]

println("   Integration test with get_top_pairings:")
println("     Old method count: $(length(old_method))")
println("     New method count: $(length(new_method))")
println("     Results match: $(Set(old_method) == Set(new_method))")

# Test integration with wonder grid generation
old_wonder_grid = generate_wonder_grid(engine)
new_wonder_grid = generate_wonder_grid_file(engine)["grid_data"]

sample_old = old_wonder_grid[test_number]
sample_new = new_wonder_grid[test_number]["pairings"]
println("   Integration with wonder grid generation:")
println("     Old grid sample: $(join(sample_old[1:5], ", "))...")
println("     New grid sample: $(join(sample_new[1:5], ", "))...")
println("     Results consistent: $(Set(sample_old) == Set(sample_new))")

println("   ✓ Integration with existing functionality verified")

# Test 11: Edge cases and error handling
println("\n11. Testing edge cases and error handling:")
println("=" ^ 60)

# Test invalid number
try
    identify_top_pairings(engine, 40, 0.25)
    println("   ✗ Should have thrown error for invalid number")
catch e
    println("   ✓ Correctly handles invalid number: $(typeof(e))")
end

# Test invalid percentage
try
    identify_top_pairings(engine, 1, 1.5)
    println("   ✗ Should have thrown error for invalid percentage")
catch e
    println("   ✓ Correctly handles invalid percentage: $(typeof(e))")
end

# Test extreme percentages
very_low = identify_top_pairings(engine, 1, 0.01)
very_high = identify_top_pairings(engine, 1, 0.99)
println("   Extreme percentages:")
println("     0.01% gives $(very_low["actual_count"]) pairings")
println("     0.99% gives $(very_high["actual_count"]) pairings")

println("   ✓ Edge cases handled correctly")

# Clean up test file
try
    rm(test_filepath)
    println("   ✓ Test file cleaned up")
catch
    println("   ! Could not clean up test file")
end

println("\n" * "=" ^ 60)
println("Enhanced Top Pairing Identification System Testing Complete!")
println("✓ All enhanced functionality implemented and working correctly")
println("✓ Top 10%, 25%, and 50% pairing identification verified")
println("✓ Wonder Grid theory rules verification implemented")
println("✓ Wonder Grid file generation with sorted lists working")
println("✓ Performance meets requirements")
println("✓ Integration with existing functionality confirmed")
println("=" ^ 60)