這份文件介紹了 **SuperFormula 軟體**，一個專為機率、統計和賭博數學設計的綜合工具。它詳細闡述了軟體的核心功能，特別是基於 **「賭博基本公式 (FFG)」** 的計算，該公式揭示了 **確定度 (DC)**、**機率 (p)** 和 **試驗次數 (N)** 之間的根本關係。此外，文件也涵蓋了二項式分佈、標準差、樂透機率的計算，以及一系列實用的統計和數字處理功能，強調其在理解和預測隨機事件，特別是賭博情境中的應用。

「賭博基本公式」（Fundamental Formula of Gambling, FFG）是一種數學公式，它能**量化彩券或任何機會遊戲中的不確定性**，透過建立**機率 (p)**、**試驗次數 (N)** 和 **確定性程度 (DC)** 之間的關係來實現。

以下是 FFG 如何量化不確定性的解釋：

- **三個核心要素**：
    
    - **機率 (p)**：指單一事件發生的可能性。例如，擲硬幣得到正面的機率是 1/2。
    - **試驗次數 (N)**：指進行某個事件的嘗試次數。
    - **確定性程度 (DC)**：指在給定 N 次試驗中，某個機率為 p 的事件至少發生一次的確定性百分比。例如，擲硬幣 10 次至少出現一次正面的確定性程度是 99.902%。
- **量化不確定性的方式**：
    
    - FFG 超越了僅僅考慮單一事件機率 (p) 的限制，它引入了「確定性程度」的概念，使人們能夠在**一系列試驗中評估不確定性**。
    - 它可以**計算在一定確定性程度下，事件所需的試驗次數**。例如，要以 99% 的確定性得到至少一個正面（機率 1/2），需要擲硬幣 7 次。
    - 它也能**計算在給定試驗次數下，事件發生的確定性程度**。
    - FFG 被譽為**遊戲理論中「最精確的工具」**。
    - FFG 解決了**「Ion Saliu 的 N 次試驗悖論」**，該悖論指出，如果一個事件的機率是 1/N，那麼在 N 次試驗中，它並不能 100% 保證會發生。實際上，當 N 趨於無限時，確定性程度的極限約為 **63.2% (1 - 1/e)**。這直接量化了在看似「確定」的情況下，仍然存在的**不確定性**，或反過來，提供了事件實際發生的**確定性**。
    - 它**揭示了宇宙中不存在絕對的確定性**。人類創造神話和神祇，部分原因正是因為他們極度需要絕對確定性的慰藉。
    - FFG 是彩票策略的基礎，例如在彩票過濾器和跳躍系統中的應用。它能協助玩家篩選掉大量不可能中獎的組合，從而**減少遊戲中的不確定性**。
- **軟體支援**：
    
    - **SuperFormula** 軟體自動執行 FFG 的計算，讓用戶能夠輕鬆地進行機率、統計和賭博數學的相關計算。
- **與馬可夫鏈的比較**：
    
    - 來源指出，FFG 比馬可夫鏈更為精確，因為 **FFG 認為先前的事件對未來的事件至關重要，並且事件會根據 FFG 精確地重複**。這表明 FFG 在建模和量化隨機事件的不確定性方面具有更高的效能。