---
created: 2025-07-24T22:10:03 (UTC +08:00)
tags: [software,utilities,tools,programs,Powerball,Mega Millions,lotto,loto,lottery,pairs,pairing,Euromillions,Thunderball,generator,combination,number]
source: https://saliu.com/lottery-utility.html
author: 
---

# Lottery Software Utilities, Lotto Tools Programs

> ## Excerpt
> Run the best lottery software with a wide variety of utilities, statistical tools for lotto, pick lotteries, Powerball, Mega Millions, Euromillions.

---
![This is software for lottery, lotto tools, number generators, statistics, many functions.](https://saliu.com/HLINE.gif)

### I. [Overview of Lottery, Lotto Software Utilities](https://saliu.com/lottery-utility.html#Utility)  
II. [Simulate Lotto, Lottery Data Files](https://saliu.com/lottery-utility.html#Simulate)  
III. [Strip Duplicates and Wheel Lotto, Lottery Numbers](https://saliu.com/lottery-utility.html#Wheel)  
IV. [Count Lines in Lotto, Lottery Files, or Any Text Files](https://saliu.com/lottery-utility.html#Count)  
V. [Check for Winning Combinations in Lotto, Lottery Output Files](https://saliu.com/lottery-utility.html#Winners)  
VI. [Statistical Reporting: Frequency, Skips, Pairing](https://saliu.com/lottery-utility.html#Statistics)  
VII. [Calculating Lotto Odds, Generating Random Numbers](https://saliu.com/lottery-utility.html#Odds)  
VIII. [Sort, Add-Up Lottery, Lotto Data Files](https://saliu.com/lottery-utility.html#Sort)  
IX. [Make/Break/Position](https://saliu.com/lottery-utility.html#combinations)  
~ Make the final D\* lottery/lotto data files  
~ Convert (break) strings of numbers into lottery combinations  
~ Create lotto, lottery combinations position by position  
X. [Resources in Lotto, Lottery, Software, Programs, Utilities](https://saliu.com/lottery-utility.html#Lottery)

![See the overview of lottery, lotto software utilities, tools.](https://saliu.com/images/lotto.gif)

## <u>1. Overview of Lottery, Lotto Software Utilities</u>

Two dozen software packages have been greatly upgraded. The lotto, lottery utility software consists of a group of programs with the Util radical in the file names. The six applications are:

-   **Util332** handles the pick-3 lottery game
-   **Util432** works with the pick-4 lottery game
-   **Util-H32** handles horseracing trifectas à la pick-3 lottery
-   **Util532** applies to the lotto-5 (drawing 5 winning lotto numbers)
-   **Util632** crunches the lotto-6 (drawing 6 winning lotto numbers)
-   **Util732** crunches the lotto 7 (drawing 7 winning lotto numbers)
-   **UtilPB** handles the Powerball, Mega Millions, CA SuperLotto type of lotto games (drawing 5 winning regular numbers plus one _Power Ball_ or _Mega Ball_, etc.)
-   **UtilEU** applies to the Euromillions type of lotto games (drawing 5 winning regular numbers plus 2 _Star_ numbers).

It is recommended to delete the Pick.INI files before running the utilities for the first time, or IF errors occur. Incorrect formats of the data files create also errors. Run **PARSEL** from time to time.

**UtilPB, UtilEU** are presented in more detail on a separate page: [_**Lottery Software for Powerball, Mega Millions, Euromillions**_](https://saliu.com/forum/powerball-mega-millions.html).

The lottery utility software for the 5- and 6-number lotto games has been greatly upgraded. The two new applications are named **ToolsLotto5, ToolsLotto6**. You might want to read these articles for substantial presentations:

-   [**ToolsLotto5**: _**Special upgrade to the lottery utility applications for 5-number lotto games**_](https://saliu.com/gambling-lottery-lotto/toolslotto5.htm)
-   [**ToolsLotto6**: _**Special upgrade to the lottery utility software for 6-number lotto games**_](https://saliu.com/gambling-lottery-lotto/toolslotto6.htm)
-   [**SoftwareLotto**: _**Special upgrades to the lottery utility applications for 5, 6, and 7-number lotto games**_](https://saliu.com/gambling-lottery-lotto/lotto-software.htm)
-   [_**Special upgrades to the utility software for two-in-one (5+1) lotto games: Powerball, Mega Millions, Euromillions, SuperLotto**_](https://saliu.com/gambling-lottery-lotto/power-mega-software.htm).

The lottery programmes, as many other programs created by Ion _Parpaluck_ Saliu, are available from the download site; software categories: 5.1, 5.2 and 5.3. The only thing that is required to download: _Paid membership for a nominal fee_. It is about 10 US cents per program! Keep in mind that many lottery software vendors charge hundreds of US dollars... per program!

The programs in this category perform common tasks. The broad function categories of the lotto, lottery utility software are:

### 1\. Simulate lottery data files

### 2\. Strip off duplicates

### 3\. Count lines in files

### 4\. Check for lottery winners

### 5\. Statistical reporting (frequency, pairing, skipping, etc.)

### 6\. Calculate odds, generate random numbers

### 7\. Sort or add-up lottery data files

### 8\. Make/break/position.

A picture is worth a thousand words, right? Let's look at the main screen of the latest version of the software tool for 6-number lotto games. It is called _**Super Utilities**_ in the very potent **Bright / Ultimate** integrated software package. The programs known as _**Super Lottery Utilities**_ are named **SoftwareLotto\*** or **SoftwarePick\*.exe**.

[![Buy the best software: Lottery, Powerball, Mega Millions, gambling roulette, blackjack, sports.](https://saliu.com/ScreenImgs/super-utilities-lottery.gif)](https://saliu.com/membership.html)

![For starters, you need simulate lotto, lottery data files to use the software.](https://saliu.com/gambling-lottery-lotto/lottery-book.gif)

## <u>2. Simulate Lotto, Lottery Data Files</u>

The Command Prompt LotWon and **MDIEditor and Lotto WE** lottery software has an appetite for huge data files. Yet, the history of lotto or lottery games is limited in size. The results (drawings, or history) files are quite small. On the other hand, Command Prompt LotWon and **MDIEditor Lotto** is better served by very large lottery, lotto data files. The software is founded on the concept of _**filtering**_, which represents ingenious mathematics. The _lottery filters_ (or _eliminating conditions_, or _reduction parameters_, or _restrictions_) act in layers or slices of the data files. Also, some filters reach very high levels; such levels can be accurate only when applied to very large lotto, lottery files.

This function of the lotto, lottery utility software comes in handy. The users (lottery players) can easily enlarge their data files via simulation. This function generates the necessary amounts of combinations to complement the data files of real lottery, lotto drawings. The files generated via this method should be named SIM, plus a lotto/lottery game identifier. For example, SIM-6 as the simulated combination file for a lotto-6 game.

Other functions in the UTILity software will combine the real lottery drawings file and the SIMulated file to create the final lotto/lottery data file: D\*. For example: D6 for a 6-number lotto game. D6 consists of a real drawings file in the lotto 6 (e.g. DATA-6) on top of a SIM-6 file.

The simulation generating routine applies very intelligent filtering. The program employs clever inner filters to generate combinations closer to the degree of randomness in real lotto, lottery drawings.

There is a convincing fact in the optimized simulated lotto draws as implemented in lottery, lotto Utility software. Accept the default for amount of draws to simulate (10200). Next, purge the resulting SIM file, using the _**Strip Duplicates**_ modules in the applications. I repeated the procedure numerous times. In the case of the lotto 6/49 game, the SIM-6 file has an average of 2-3 duplicate combinations (all 6 numbers are repeats). In the case of the 5/39 lotto game, the SIM-5 file has an average of 100 duplicate combinations (all 5 numbers are repeats).

The optimized combinations as generated by _**Util532 / Util632**_ have a hugely superior jackpot-hit rate. I noticed in my own data files. I also remember two users of LotWon who complained that my programs missed the jackpot-winning combinations, despite the correct setting of the lotto filters! The problem is not obvious and is determined by the existence of the winning combination in the SIM file!

By default, LotWon eliminates from the output ALL 6-number groups that exist in the DATA-6 file! (Or, ALL 5-number groups that exist in the DATA-5 file; the lotto-5 game offer an even more drastic repeat!) An output of 10000 lotto combinations offers a jackpot chance equal to millions of purely random combinations. One good strategy is to keep handy a 10000-combination optimized simulated lotto file that has not yet hit the jackpot in your lotto game. The latest version of **MDIEditor and Lotto** no longer eliminates automatically all the combinations in a data file. The lottery user has the option to enable the _inner filters_.

_Simulator, you're my friend:  
Don't make my hope bend!_

![The software strips duplicates and wheels the lotto, lottery numbers.](https://saliu.com/gambling-lottery-lotto/lottery-book.gif)

## <u>3. Strip Duplicates and Wheel Lotto, Lottery Numbers</u>

There is so much more in a picture than meets the eye. This function proves it.

The hugely upgraded function _**D = Duplicates: Strip or Wheel**_ still does what it did previously. For example, you generated an output file in **MDIEditor Lotto**. The file contains some duplicate combinations. Some of the lotto 6 combinations are identical. They consist of the same 6 lotto numbers. Many users ran this feature of Util632 to strip-off the duplicates. The result is a clean file: all combinations are unique.

But why not expand the _duplicate_ concept to incorporate lower groups of common numbers? Again, the same output files as per the example above. Instead of purging only the combinations that have _6 of 6_ common lotto numbers, make sure also that none of the combinations have _4 of 6_ common numbers, or _5 of 6_ common numbers, or _3 of 6_ common numbers! In the case of the lotto-5 game, the combinations are determined to be _duplicates_ by groups of common numbers from _1 of 5_ to _5 of 5_.

This is a very ingenious form of wheeling lotto numbers! I believe it to be totally unique at the time of this writing. Lottery players have demanded such kind of wheeling software for many years. There was no answer to their prayers - up until now, that is. Suppose you have a 5-number lotto file of combinations. If that file guaranteed _5 of 5_, you can use the new _Util-532_ feature to strip-wheel the file. If you wanted 5-number lotto combinations with NO _3 of 5_ common numbers, the resulting lotto wheel will guarantee the _3 of 5 minimum assurance_!

Setting tight filters in the _**Command Prompt**_ LotWon lottery/lotto software or **MDIEditor and Lotto WE** leads to a high repeat rate of combinations. You can stop the combination-generating process and take a look at the output file. You can sort the file in ascending order by line (combination). You will notice that a few combinations repeat. That's why this function is so handy: it controls the _cost of playing_ (_COP_). You don't have to play the same lotto combination several times.

_Stripper, stripper, you look good:  
Wheel me in the perfect mood!  
Stripper, stripper, I feel good..._

The melody is the same as in _Federal Lottery_ on my YouTube:

<iframe src="//www.youtube.com/embed/Qbk5ZgXBZm0" frameborder="0" allowfullscreen=""></iframe>

![The software count total lines in lotto, lottery files, or any text files.](https://saliu.com/images/lotto.gif)

## <u>4. Count Lines in Lotto, Lottery Files, or Any Text Files</u>

Normally, the _**Command Prompt**_ LotWon lottery software and _**MDIEditor and Lotto**_ automatically count the number of combinations in the lottery / lotto data files. The software also warns the user if the results files are of insufficient size. There are situations when the user wants to know the size (number of lines) of other text files, such as output combination files, or pairing files (_wonder-grids_), or worst-pairs files, etc.

_Counter, you help a lot  
If the size is right or not._

![The software programs check for winners in lotto, lottery combinations.](https://saliu.com/gambling-lottery-lotto/lottery-mathematics.gif)

## <u>5. Check for Winning Combinations in Lotto, Lottery Output Files</u>

Obviously, playing the lotto or lottery has a clear purpose: WIN. Winning is more than anything, it is more than everything: Winning is the thing.

Randomness is the rule of the Universe. There is no plan, or planner, or designer to determine how the Universe works. It is all RANDOM, axiomatic one! All things have an equal chance at hitting IT. Randomness is the excellent form of fairness. Only the Humans have the capacity to attempt to control Randomness. They dare to get the best of randomness, but the success is only relative.

Ask people what random is and one of the very first words you hear is lottery. You also hear just about immediately lotto or gambling. All those things are random, but most other things aren't. God takes care of most things, but can't do anything about lottery (paraphrasing Einstein: _“God doesn't play dice with the universe.”_). _“Don't waste your time praying to win the lottery,”_ God says; _“I've got no control over it. You better try the services of humans, like this very author, who write excellent lotto software!”_

The purpose of lotto/lottery software is to challenge randomness and bring about success at a higher rate than purely random play. To my knowledge, only the lottery and lotto software created by this very programmer has the power to meet such a demanding promise. You can find at SALIU.COM plenty of materials that painstakingly detail the truthful foundation of said software. Read it, if you haven't already done so.

This particular function of lotto utility software does very well a very useful job. It checks if your lotto/lottery combinations won. That is, you can see if the output combinations won in the most recent drawing, or for the entire length of a lottery or lotto drawings file. There are two methods of checking for winners.

#### 5.1.

The most common method of checking for winners is combination by combination. You had an output OUT-6 file of combinations generated by the _**Command Prompt**_ LotWon lottery software or _**MDIEditor and Lotto**_. The file has 100 combinations. Your data file of real 6-number lotto drawings is Data-6. It doesn't matter if you played or not. You want to see how the output lotto combination file would have fared in the most recent (e.g. 10) draws of your real Data-6 file. Those are the options asked for by the first method of this function: _Output file, real lotto data file, number of combinations in the output file, numbers of drawings to check in the real data file_. The function will display a comprehensive number of prize situations, provided that the output file did hit some prizes.

The input files must be compliant with the game format; e.g. exactly 5 numbers per combination (line) in a lotto-5 game.

#### 5.2.

There is a situation when you select a group or pool of lotto numbers to play, instead of direct combinations. You analyze the skip reports of the statistical functions of the _**Command Prompt**_ LotWon lottery software or _**MDIEditor and Lotto WE**_. The skips lead you believe that a group of lotto, lottery numbers have the best chance to hit soon. You don't need to convert the group of numbers into combinations or to wheel the respective lotto numbers. You can check directly if the pool of numbers had any hits in the real drawings lottery file. This method offers huge time-savings. It is the only software method out there, to the best of my knowledge. It had been insistently requested by lottery players and lotto software users.

The pool of numbers must be first saved in an input file (text format). The file consists of one line of numbers in the regular lotto and lottery games. The input file must have two lines of numbers for Powerball, Mega Millions, Euromillions. The regular numbers are in the first line; the _Power Balls (Mega Balls, Star Numbers)_ are in the second line.

_It is swell to hit a winner:  
Pays for fun, wine, and dinner._

![The lottery software generates statistical reports for frequency, pairing, skips or misses.](https://saliu.com/images/lotto.gif)

## <u>6. Statistical Reporting: Frequency, Skips, Pairs</u>

The frequency of lotto, lottery numbers is one of the favorite pastimes of many lottery players. This function of the lottery, lotto utility software is thorough unlike anything else. It won't show you meaningless graphs in various colors and bars and pies and shapes. It gives you what lottery is about: **Numbers**.

Here is a sample of the statistical parameters. Nothing can be more meaningful than that, statistics-wise.

```
<span size="5" face="Courier New" color="#c5b358">                   Lotto-6 Statistics

   Lotto     Total    MOST drawn  Hits most    LEAST drawn  Hits least
   Number    Hits     with #      drawn with:  with #       drawn with:
     1         26         44           8             8           0
...
</span>
```

The _skips_ will show how many lottery drawings a number skipped before hitting again. For example, lotto number 13 hit, then waited (skipped) 7 draws before it hit again. The first number in the skip report (string) for a number represents the current or running skip. It is a very important parameter. The main lotto, lottery strategy page presents strategies based on the current skip of every lotto number.

The statistical function also plots the pairings of every lotto or lottery number. It shows the pairing preferences of every lottery or lotto number. This can be a very powerful method of selecting the best lotto or lottery numbers to play soon. The _wonder grid_ concept is based on the statistical pairing of lotto or lottery number. The very important parameters of this feature are: _range of pairing analysis; skips of the most frequent pairs_.

_Skipper, watch out the grid -  
With no pair, we might skid._

![Odds and random numbers are generated by these lotto programs.](https://saliu.com/gambling-lottery-lotto/lottery-book.gif)

## <u>7. Calculating Lotto Odds, Generating Random Numbers</u>

This function plainly does what it says. It will tell what the total number of possible combinations in a lotto game is. For example, the lotto 6/49 game has a total of 13,983,816 combinations. The odds in the lotto 6/49 game are said to be equal to _1 in 13,983,816_.

As soon as the program calculates the odds, it generates a number of combinations of that lotto/lottery type. The user chooses how many combinations to generate.

The combinations are absolutely random, very much like the _quick picks_ or _lucky dips_ offered by the computers of the lottery commissions. No internal filters are employed whatsoever.

_Randomness is my great joy:  
We create, and we destroy._

![The lottery software programs sort or add-up lottery, lotto data files.](https://saliu.com/gambling-lottery-lotto/lotto-book.gif)

## <u>8. Sort, Add-Up Lottery, Lotto Data Files</u>

Many functions of the _**Command Prompt**_ LotWon lottery software or _**MDIEditor and Lotto WE**_ assumes that the real drawing files are sorted in _ascending order per line_. The user should never apply the software to combinations in drawn order. Always write the drawings as _1,2,3,4,5,6_; never as _5,2,6,3,1,4_. If the drawings are not sorted, always run this function of the lotto, lottery utility software. Exception: the pick-3, pick-4 lottery games: _The order counts_.

This function automatically backs up the original results file for increased safety.

The add-up function will sum-up every drawing in a lotto drawings file. There is a much more potent program, however. Get **SUMS** for a comprehensive summation of lotto, lottery drawings files. SUMS calculates the _sum, root sum, standard deviation, average of the deviations (regardless of sign), average of the deltas_ (_delta_ is the absolute value of the difference between two neighboring numbers).

_Sorter, keep me in good shape -  
And thus disorder I escape._

![Lottery software breaks strings of numbers in combinations; create lotto combinations by position.](https://saliu.com/gambling-lottery-lotto/lottery-book.gif)

## <u>9. Make/Break/Position</u>

~ Make the final D\* lottery/lotto data files  
~ Convert (break) strings of lottery numbers into combinations  
~ Create lotto, lottery combinations position by position

**9.1**. As described at #2 (_**Simulate**_), the _**Command Prompt**_ LotWon lottery software and _**MDIEditor and Lotto**_ need special lottery data files. This function will create such files for the user. The utility can create three types of D\* files:

**~ Make D3 without LEAST\* or BEST\*  
~ Make D3 with LEAST\*/BEST\*  
~ Make D3 with LEAST\* & BEST\***

These processes are absolutely straightforward. The user provides the file names, and the program does the rest. Defaults are also in place so that the operation is even easier.

**9.2**. _**Convert (break) strings of lottery numbers into combinations**_.  
You can have a string of lotto, lottery numbers saved in a text file; for example, 10 numbers. This function will generate all the combinations of 10 numbers for the respective lotto game. For example, 252 combinations in a lotto-5 game; or 210 combinations of 6 numbers apiece.

Lotto players are tempted to wheel a group of lottery numbers. I recommend instead generating all the combinations for that pool of number. Then, shuffle the output file by running that great probability program of mine: **Shuffle**. Go to the FFG median area of the shuffled file (somewhere in the middle will be fine). Select an amount of combinations equal to the lotto wheel you had in mind. More often than not, the random combinations selected perform better than the lotto wheel of equal size.

If you still want to wheel now and then, use the function listed at #3 (Strip and Wheel). The resulting wheels are better than ordinary lottery wheels. The wheels created by the lotto, lottery utility software (_**Util\***_) are balanced (homogenous).

**9.3**. _**Create lotto, lottery combinations position by position**_.  
The lotto numbers do not come out equally when the position in the combinations is considered. The remarkable lotto page [_**Excel Spreadsheets in Lotto, Lottery**_](https://saliu.com/Newsgroups.htm) analyzes in great detail the concept of ranges in lotto games. The tool of choice was _Microsoft Excel_, the number one spreadsheet software. The user inputs a file of lotto drawings. Excel calculates the frequency and the median (among other parameters) position by position. I figured out that the numbers below the median perform better than the rest. Thus, I came up with the concept of ranges by position. For example, the range for position 1 in a lotto 5/39 game was 1 to 9. This is the congruent type of lotto ranges: all the numbers between 1 and 9.

The lottery ranges can be incongruent as well. For example, this range for the Powerball numbers: 1, 2, 31, 35. That great piece of gambling and lottery software **SkipSystem** is the best tool to create lotto, lottery ranges. The ranges are founded on _frequency_ AND _skips_.

It is best to create first a text file of lottery, lotto ranges. There is one important requirement for the range files. They must have a number of lines equal to the numbers per combination in the corresponding lotto/lottery game. That is, 3 lines for pick-3; 4 lines for pick-4; 5 lines in the lotto-5 games; 6 lines in lotto-6; 6 numbers for Powerball, Megamillions games. Here is a range sample for the Powerball games:

1 2 4 5 6 8 9 10  
11 14 16 17 20  
17 19 26 27 30  
32 35 37 39 41 42  
44 47 48 49 50 51 52 53  
1 2 31 35

The first 5 lines contain the regular Powerball numbers for the corresponding position; the _Power Balls_ are always in the last (6th) line.

The function **9.3** takes the input range file and generates all the combinations in the lotto/lottery game, making sure the numbers remain in their original positions.

Of special interest to you is the _M option_: _**Make/Break/Position**_. More specifically, _option 4_: Break/Position, option 1 (also option 2). The new options are flexible to an extreme. You can have any number of lines, of any length larger than numbers per combination in your game. You can have just one line of numbers (e.g. a lotto number and its best pairs, any number of pairs). Or, you can have 7 lines with the best pairs of any 7 lotto numbers. The new functions will generate every combination there is. Then, the set of combinations will be purged of duplicates, if any. In the lotto games, the combinations will be also sorted in-line.

Here is a good _TOP3_ file for my pick-3 game:

4 2 6 7  
6 3 8 7 0 9  
7 6 3 9  
9 6 7

Not only that, but how about eliminating the worst pairings? That's a very efficient feature. You don't have to run a _**Command Prompt**_ LotWon program to generate combinations while eliminating the worst pairings. It takes long sometimes. Now it only takes seconds: Generate combinations of the best pairings (of certain numbers only, if you wish) WHILE eliminating the worst pairings of the entire lottery game. The worst pairings are in the file LEAST\*. It is created by the _F option_ (_**Frequency and Stats**_) of the _UTILity_ programs. It is recommended to set the upper limit to zero. That is, only the pairings with no appearance (no hits) will be saved.

The _Frequency option_ also creates a new PAIRS\* file. It has a new header, but also a very useful feature. Every lotto/lottery number shows now a line consisting of the particular number plus its pairs with frequencies greater than 0. You would copy and paste such lines to a file named TOP\*. TOP is the input file for the two _Break options_ of UTIL\*. I won't tell you what numbers to copy and paste, how many of them, or for what range of drawings to do the Frequency report. I want you to use your brains, since my lottery software is free to run forever for registered members.

_Maker-breaker, you got power -  
You turn the seeds into flour;  
You can offer so much dough  
Though my answer won't be no!_

![These are special resources in lotto software, lottery software, mathematics, statistics.](https://saliu.com/gambling-lottery-lotto/lottery-mathematics.gif)[](https://saliu.com/content/lottery.html)

## [<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>](https://saliu.com/content/lottery.html)

See a comprehensive directory of the pages and materials on the subject of lottery, software, systems, strategies, lotto wheels.

-   The incipient [_**Lotto, Lottery, Software, Strategy**_](https://saliu.com/LottoWin.htm) _page_.  
    Presenting software to create winning lotto, lottery strategies, systems based on mathematics. Get your lotto systems or wheels, the best lottery, lotto software, combinations, winning numbers.
-   [_**Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies**_](https://saliu.com/Newsgroups.htm).  
    Read a genuine analysis of Excel spreadsheets applied to lottery and lotto developing of software, systems, and strategies. Combining Excel analysis with powerful lottery and lotto software programmed by this author, _Parpaluck_.
-   [_**<u>MDIEditor Lotto WE</u>: Lottery Software Manual, Book, ebook, Help**_](https://saliu.com/MDI-lotto-guide.html).  
    ~ Also applicable to LotWon lottery, lotto software; plus Powerball, Mega Millions, Euromillions.
-   [_**Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps, Programs**_](https://saliu.com/forum/lotto-book.html).
-   [_**<u>Lottery Skip System Software</u>**_](https://saliu.com/skip-strategy.html): _**Lotto, Powerball, Mega Millions, Euromillions**_.
-   [_**Lottery Filters, Filtering in Software**_](https://saliu.com/filters.html).
-   [_**Filters, Restrictions, Elimination, Reduction in Lotto, Lottery Software**_](https://saliu.com/bbs/messages/42.html).
-   [_**Vertical (Positional) Filters in Lottery Software**_](https://saliu.com/bbs/messages/838.html).
-   [_**Software to Generate Lotto Combinations with Favorite Lottery Numbers in Fixed Positions**_](https://saliu.com/favorite-lottery-numbers-positions.html).
-   [**Lotto wheels**](https://saliu.com/lotto_wheels.html) _**for lotto games drawing 5, 6, or 7 numbers**_.  
    The most advanced theory of lotto wheels or reduced lottery systems. Get also original lotto wheels for lotto games drawing 5, 6, or 7 numbers: Balanced, randomized, and free.
-   _"The Start Is the Hardest Part"_ in [_**Lottery Strategy, Lotto Strategies**_](https://forums.saliu.com/lottery-strategies-start.html).
-   Download [**lottery software, lotto software**](https://saliu.com/infodown.html).

![Run very useful lottery statistical tools, utilities, apps, lotto combination generators.](https://saliu.com/HLINE.gif)

**[Home](https://saliu.com/index.htm) | [Search](https://saliu.com/Search.htm) | [New Writings](https://saliu.com/bbs/index.html) | [Fundamental Formula](https://saliu.com/formula.htm) | [Odds, Generator](https://saliu.com/calculator_generator.html) | [Contents](https://saliu.com/content/index.html) | [Forums](https://forums.saliu.com/) | [Sitemap](https://saliu.com/sitemap/index.html)**

![Thanks for visiting the site of lottery, lotto, best software programs, utilities.](https://saliu.com/HLINE.gif)
