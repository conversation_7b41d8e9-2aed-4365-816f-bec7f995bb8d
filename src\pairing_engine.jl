# Pairing frequency analysis engine

using Statistics

# 簡單的 countmap 實現
function simple_countmap(items)
    counts = Dict()
    for item in items
        counts[item] = get(counts, item, 0) + 1
    end
    return counts
end

"""
PairingEngine for calculating number pairing frequencies with efficient storage and retrieval
"""
mutable struct PairingEngine
    historical_data::Vector{LotteryDraw}
    pairing_cache::Dict{Tuple{Int,Int}, Int}
    frequency_sorted_cache::Vector{Tuple{Tuple{Int,Int}, Int}}  # Sorted by frequency
    total_pairings::Int
    last_update_size::Int  # Track data size for cache invalidation
    
    function PairingEngine(data::Vector{LotteryDraw})
        new(data, Dict{Tuple{Int,Int}, Int}(), Tuple{Tuple{Int,Int}, Int}[], 0, 0)
    end
end

"""
Calculate all pairing frequencies with efficient storage and caching
"""
function calculate_all_pairings(engine::PairingEngine)::Dict{Tuple{Int,Int}, Int}
    # Check if cache is valid (data hasn't changed)
    current_data_size = length(engine.historical_data)
    if !isempty(engine.pairing_cache) && engine.last_update_size == current_data_size
        return engine.pairing_cache
    end
    
    # Clear existing cache
    empty!(engine.pairing_cache)
    empty!(engine.frequency_sorted_cache)
    engine.total_pairings = 0
    
    # Initialize all possible pairs with zero frequency for efficient access
    for i in 1:39
        for j in (i+1):39
            engine.pairing_cache[(i, j)] = 0
        end
    end
    
    # Count pairing occurrences efficiently
    for draw in engine.historical_data
        numbers = sort(draw.numbers)
        # Generate all C(5,2) = 10 pairs from the 5 numbers in this draw
        for i in 1:4
            for j in (i+1):5
                pair = (numbers[i], numbers[j])
                engine.pairing_cache[pair] += 1
                engine.total_pairings += 1
            end
        end
    end
    
    # Update cache metadata
    engine.last_update_size = current_data_size
    
    return engine.pairing_cache
end

"""
Get pairing frequencies sorted by frequency (descending order)
"""
function get_sorted_pairings(engine::PairingEngine)::Vector{Tuple{Tuple{Int,Int}, Int}}
    # Ensure pairings are calculated
    calculate_all_pairings(engine)
    
    # Check if sorted cache is valid
    if !isempty(engine.frequency_sorted_cache)
        return engine.frequency_sorted_cache
    end
    
    # Create sorted list of pairings by frequency
    engine.frequency_sorted_cache = [(pair, freq) for (pair, freq) in sort(collect(engine.pairing_cache), by = x -> x[2], rev = true)]
    
    return engine.frequency_sorted_cache
end

"""
Get pairing frequency for a specific pair
"""
function get_pairing_frequency(engine::PairingEngine, number1::Int, number2::Int)::Int
    if !(1 <= number1 <= 39) || !(1 <= number2 <= 39)
        throw(ArgumentError("Numbers must be between 1 and 39"))
    end
    
    if number1 == number2
        return 0  # A number cannot pair with itself
    end
    
    # Ensure consistent pair ordering
    pair = number1 < number2 ? (number1, number2) : (number2, number1)
    
    # Calculate pairings if not cached
    all_pairings = calculate_all_pairings(engine)
    
    return get(all_pairings, pair, 0)
end

"""
Get all pairing frequencies for a specific number efficiently
"""
function get_number_pairings(engine::PairingEngine, number::Int)::Dict{Int, Int}
    if !(1 <= number <= 39)
        throw(ArgumentError("Number must be between 1 and 39"))
    end
    
    all_pairings = calculate_all_pairings(engine)
    number_pairings = Dict{Int, Int}()
    
    # Find all pairings involving this number
    for other_number in 1:39
        if other_number != number
            pair = number < other_number ? (number, other_number) : (other_number, number)
            frequency = get(all_pairings, pair, 0)
            number_pairings[other_number] = frequency
        end
    end
    
    return number_pairings
end

"""
Get pairing statistics and distribution analysis
"""
function analyze_pairing_distribution(engine::PairingEngine)::Dict{String, Any}
    all_pairings = calculate_all_pairings(engine)
    frequencies = collect(values(all_pairings))
    
    if isempty(frequencies)
        return Dict{String, Any}(
            "total_pairs" => 0,
            "total_occurrences" => 0,
            "mean_frequency" => 0.0,
            "median_frequency" => 0.0,
            "std_frequency" => 0.0,
            "min_frequency" => 0,
            "max_frequency" => 0
        )
    end
    
    return Dict{String, Any}(
        "total_pairs" => length(all_pairings),
        "total_occurrences" => sum(frequencies),
        "mean_frequency" => mean(frequencies),
        "median_frequency" => median(frequencies),
        "std_frequency" => std(frequencies),
        "min_frequency" => minimum(frequencies),
        "max_frequency" => maximum(frequencies),
        "frequency_distribution" => simple_countmap(frequencies)
    )
end

"""
Get top pairings for a specific key number
"""
function get_top_pairings(engine::PairingEngine, key_number::Int, percentage::Float64)::Vector{Int}
    all_pairings = calculate_all_pairings(engine)
    
    # Find all pairings involving the key number
    key_pairings = Tuple{Int, Int}[]
    for (pair, freq) in all_pairings
        if pair[1] == key_number || pair[2] == key_number
            push!(key_pairings, pair)
        end
    end
    
    # Sort by frequency (descending)
    sort!(key_pairings, by = pair -> all_pairings[pair], rev = true)
    
    # Calculate how many pairings to return (percentage of 38 other numbers)
    num_pairings = max(1, round(Int, 38 * percentage))
    num_pairings = min(num_pairings, length(key_pairings))
    
    # Extract the other numbers from top pairings
    top_numbers = Int[]
    for i in 1:num_pairings
        pair = key_pairings[i]
        other_number = pair[1] == key_number ? pair[2] : pair[1]
        if !(other_number in top_numbers)
            push!(top_numbers, other_number)
        end
    end
    
    return top_numbers
end

"""
Generate wonder grid file data
"""
function generate_wonder_grid(engine::PairingEngine)::Dict{Int, Vector{Int}}
    wonder_grid = Dict{Int, Vector{Int}}()
    
    for number in 1:39
        top_pairings = get_top_pairings(engine, number, 0.25)  # Top 25%
        wonder_grid[number] = top_pairings
    end
    
    return wonder_grid
end

"""
Update pairings with new draw data efficiently
"""
function update_pairings(engine::PairingEngine, new_draw::LotteryDraw)
    # Add new draw to historical data
    pushfirst!(engine.historical_data, new_draw)
    
    # If cache exists, update incrementally for efficiency
    if !isempty(engine.pairing_cache)
        numbers = sort(new_draw.numbers)
        # Add new pairings from this draw
        for i in 1:4
            for j in (i+1):5
                pair = (numbers[i], numbers[j])
                if haskey(engine.pairing_cache, pair)
                    engine.pairing_cache[pair] += 1
                    engine.total_pairings += 1
                end
            end
        end
        
        # Clear sorted cache as it's now invalid
        empty!(engine.frequency_sorted_cache)
        
        # Update metadata
        engine.last_update_size = length(engine.historical_data)
    else
        # If no cache exists, just mark for recalculation
        engine.last_update_size = 0
    end
end

"""
Update pairings with multiple new draws efficiently
"""
function update_pairings(engine::PairingEngine, new_draws::Vector{LotteryDraw})
    # Add all new draws to historical data
    for draw in reverse(new_draws)  # Add in reverse to maintain chronological order
        pushfirst!(engine.historical_data, draw)
    end
    
    # If cache exists, update incrementally
    if !isempty(engine.pairing_cache)
        for draw in new_draws
            numbers = sort(draw.numbers)
            # Add new pairings from this draw
            for i in 1:4
                for j in (i+1):5
                    pair = (numbers[i], numbers[j])
                    if haskey(engine.pairing_cache, pair)
                        engine.pairing_cache[pair] += 1
                        engine.total_pairings += 1
                    end
                end
            end
        end
        
        # Clear sorted cache
        empty!(engine.frequency_sorted_cache)
        
        # Update metadata
        engine.last_update_size = length(engine.historical_data)
    else
        # Mark for recalculation
        engine.last_update_size = 0
    end
end

"""
Update pairings with raw draw data
"""
function update_pairings(engine::PairingEngine, new_draw::Vector{Int})
    # Convert to LotteryDraw format
    new_date = isempty(engine.historical_data) ? Date(2025, 1, 1) : engine.historical_data[1].draw_date + Day(1)
    new_id = isempty(engine.historical_data) ? 1 : engine.historical_data[1].draw_id + 1
    lottery_draw = LotteryDraw(new_draw, new_date, new_id)
    
    update_pairings(engine, lottery_draw)
end

"""
Get all pairings for a specific number with their frequencies (enhanced version)
"""
function get_all_pairings_for_number(engine::PairingEngine, number::Int)::Vector{Tuple{Int, Int}}
    if !(1 <= number <= 39)
        throw(ArgumentError("Number must be between 1 and 39"))
    end
    
    number_pairings_dict = get_number_pairings(engine, number)
    
    # Convert to vector of tuples and sort by frequency
    number_pairings = [(other_num, freq) for (other_num, freq) in number_pairings_dict]
    sort!(number_pairings, by = x -> x[2], rev = true)
    
    return number_pairings
end

"""
Get top N most frequent pairs overall
"""
function get_top_pairs(engine::PairingEngine, n::Int = 20)::Vector{Tuple{Tuple{Int,Int}, Int}}
    sorted_pairings = get_sorted_pairings(engine)
    return sorted_pairings[1:min(n, length(sorted_pairings))]
end

"""
Get bottom N least frequent pairs overall
"""
function get_bottom_pairs(engine::PairingEngine, n::Int = 20)::Vector{Tuple{Tuple{Int,Int}, Int}}
    sorted_pairings = get_sorted_pairings(engine)
    start_idx = max(1, length(sorted_pairings) - n + 1)
    return sorted_pairings[start_idx:end]
end

"""
Get pairs within a specific frequency range
"""
function get_pairs_in_frequency_range(engine::PairingEngine, min_freq::Int, max_freq::Int)::Vector{Tuple{Tuple{Int,Int}, Int}}
    all_pairings = calculate_all_pairings(engine)
    
    filtered_pairs = Tuple{Tuple{Int,Int}, Int}[]
    for (pair, freq) in all_pairings
        if min_freq <= freq <= max_freq
            push!(filtered_pairs, (pair, freq))
        end
    end
    
    # Sort by frequency (descending)
    sort!(filtered_pairs, by = x -> x[2], rev = true)
    
    return filtered_pairs
end

"""
Get pairing percentile information
"""
function get_pairing_percentiles(engine::PairingEngine)::Dict{String, Float64}
    all_pairings = calculate_all_pairings(engine)
    frequencies = collect(values(all_pairings))
    
    if isempty(frequencies)
        return Dict{String, Float64}()
    end
    
    return Dict{String, Float64}(
        "p10" => quantile(frequencies, 0.10),
        "p25" => quantile(frequencies, 0.25),
        "p50" => quantile(frequencies, 0.50),
        "p75" => quantile(frequencies, 0.75),
        "p90" => quantile(frequencies, 0.90),
        "p95" => quantile(frequencies, 0.95),
        "p99" => quantile(frequencies, 0.99)
    )
end

"""
Verify pairing distribution rules (10% = 25%, 25% = 50%)
"""
function verify_pairing_distribution_rules(engine::PairingEngine)::Dict{String, Any}
    sorted_pairings = get_sorted_pairings(engine)
    total_frequency = sum(pair[2] for pair in sorted_pairings)
    
    if total_frequency == 0
        return Dict{String, Any}(
            "total_pairs" => 0,
            "total_frequency" => 0,
            "top_10_percent_rule" => false,
            "top_25_percent_rule" => false
        )
    end
    
    total_pairs = length(sorted_pairings)
    
    # Calculate top 10% and 25% thresholds
    top_10_count = max(1, round(Int, total_pairs * 0.10))
    top_25_count = max(1, round(Int, total_pairs * 0.25))
    
    # Calculate frequency sums for top percentages
    top_10_frequency = sum(pair[2] for pair in sorted_pairings[1:top_10_count])
    top_25_frequency = sum(pair[2] for pair in sorted_pairings[1:top_25_count])
    
    # Calculate percentages
    top_10_percentage = (top_10_frequency / total_frequency) * 100
    top_25_percentage = (top_25_frequency / total_frequency) * 100
    
    return Dict{String, Any}(
        "total_pairs" => total_pairs,
        "total_frequency" => total_frequency,
        "top_10_count" => top_10_count,
        "top_25_count" => top_25_count,
        "top_10_frequency" => top_10_frequency,
        "top_25_frequency" => top_25_frequency,
        "top_10_percentage" => top_10_percentage,
        "top_25_percentage" => top_25_percentage,
        "top_10_percent_rule" => abs(top_10_percentage - 25.0) < 5.0,  # Within 5% tolerance
        "top_25_percent_rule" => abs(top_25_percentage - 50.0) < 5.0   # Within 5% tolerance
    )
end

"""
Get memory usage statistics for the pairing engine
"""
function get_memory_usage_stats(engine::PairingEngine)::Dict{String, Any}
    pairing_cache_size = length(engine.pairing_cache) * (sizeof(Tuple{Int,Int}) + sizeof(Int))
    sorted_cache_size = length(engine.frequency_sorted_cache) * sizeof(Tuple{Tuple{Int,Int}, Int})
    historical_data_size = length(engine.historical_data) * sizeof(LotteryDraw)
    
    total_size = pairing_cache_size + sorted_cache_size + historical_data_size
    
    return Dict{String, Any}(
        "pairing_cache_bytes" => pairing_cache_size,
        "sorted_cache_bytes" => sorted_cache_size,
        "historical_data_bytes" => historical_data_size,
        "total_bytes" => total_size,
        "pairing_cache_kb" => round(pairing_cache_size / 1024, digits=2),
        "sorted_cache_kb" => round(sorted_cache_size / 1024, digits=2),
        "historical_data_kb" => round(historical_data_size / 1024, digits=2),
        "total_kb" => round(total_size / 1024, digits=2),
        "pairs_count" => length(engine.pairing_cache),
        "draws_count" => length(engine.historical_data)
    )
end

"""
Clear all caches to free memory
"""
function clear_caches!(engine::PairingEngine)
    empty!(engine.pairing_cache)
    empty!(engine.frequency_sorted_cache)
    engine.total_pairings = 0
    engine.last_update_size = 0
end

"""
Rebuild caches from scratch
"""
function rebuild_caches!(engine::PairingEngine)
    clear_caches!(engine)
    calculate_all_pairings(engine)
    get_sorted_pairings(engine)
end

# ===== TOP PAIRING IDENTIFICATION SYSTEM =====

"""
Identify top percentage pairings for a specific number with detailed analysis
"""
function identify_top_pairings(engine::PairingEngine, number::Int, percentage::Float64)::Dict{String, Any}
    if !(1 <= number <= 39)
        throw(ArgumentError("Number must be between 1 and 39"))
    end
    
    if !(0.0 < percentage <= 1.0)
        throw(ArgumentError("Percentage must be between 0 and 1"))
    end
    
    # Get all pairings involving this number (using same logic as original get_top_pairings)
    all_pairings = calculate_all_pairings(engine)
    
    # Find all pairings involving the number
    key_pairings = Tuple{Tuple{Int,Int}, Int}[]
    for (pair, freq) in all_pairings
        if pair[1] == number || pair[2] == number
            push!(key_pairings, (pair, freq))
        end
    end
    
    # Sort by frequency (descending) - same as original
    sort!(key_pairings, by = x -> x[2], rev = true)
    
    # Calculate how many pairings to return (same logic as original)
    total_other_numbers = 38  # 39 total - 1 (the number itself)
    target_count = max(1, round(Int, total_other_numbers * percentage))
    actual_count = min(target_count, length(key_pairings))
    
    # Extract the other numbers from top pairings (same logic as original)
    top_numbers = Int[]
    top_pairings_with_freq = Tuple{Int, Int}[]
    
    for i in 1:actual_count
        pair, freq = key_pairings[i]
        other_number = pair[1] == number ? pair[2] : pair[1]
        if !(other_number in top_numbers)
            push!(top_numbers, other_number)
            push!(top_pairings_with_freq, (other_number, freq))
        end
    end
    
    # Calculate frequency statistics
    top_frequencies = [pair[2] for pair in top_pairings_with_freq]
    
    # Calculate total frequency for this number
    total_frequency = 0
    for (pair, freq) in key_pairings
        total_frequency += freq
    end
    
    top_frequency_sum = sum(top_frequencies)
    
    coverage_percentage = total_frequency > 0 ? (top_frequency_sum / total_frequency) * 100 : 0.0
    
    return Dict{String, Any}(
        "number" => number,
        "percentage_requested" => percentage,
        "target_count" => target_count,
        "actual_count" => actual_count,
        "top_numbers" => top_numbers,
        "top_pairings_with_freq" => top_pairings_with_freq,
        "frequency_coverage" => coverage_percentage,
        "total_frequency" => total_frequency,
        "top_frequency_sum" => top_frequency_sum,
        "min_frequency" => length(top_frequencies) > 0 ? minimum(top_frequencies) : 0,
        "max_frequency" => length(top_frequencies) > 0 ? maximum(top_frequencies) : 0,
        "mean_frequency" => length(top_frequencies) > 0 ? mean(top_frequencies) : 0.0
    )
end

"""
Identify top 10% pairings for a number
"""
function identify_top_10_percent_pairings(engine::PairingEngine, number::Int)::Dict{String, Any}
    return identify_top_pairings(engine, number, 0.10)
end

"""
Identify top 25% pairings for a number
"""
function identify_top_25_percent_pairings(engine::PairingEngine, number::Int)::Dict{String, Any}
    return identify_top_pairings(engine, number, 0.25)
end

"""
Identify top 50% pairings for a number
"""
function identify_top_50_percent_pairings(engine::PairingEngine, number::Int)::Dict{String, Any}
    return identify_top_pairings(engine, number, 0.50)
end

"""
Verify that top 25% pairings account for approximately 50% of frequency
"""
function verify_25_percent_rule(engine::PairingEngine, number::Int)::Dict{String, Any}
    if !(1 <= number <= 39)
        throw(ArgumentError("Number must be between 1 and 39"))
    end
    
    top_25_analysis = identify_top_25_percent_pairings(engine, number)
    coverage = top_25_analysis["frequency_coverage"]
    
    # Wonder Grid theory states top 25% should account for ~50% of frequency
    expected_coverage = 50.0
    deviation = abs(coverage - expected_coverage)
    tolerance = 10.0  # Allow 10% tolerance
    
    rule_satisfied = deviation <= tolerance
    
    return Dict{String, Any}(
        "number" => number,
        "actual_coverage" => coverage,
        "expected_coverage" => expected_coverage,
        "deviation" => deviation,
        "tolerance" => tolerance,
        "rule_satisfied" => rule_satisfied,
        "top_25_count" => top_25_analysis["actual_count"],
        "total_frequency" => top_25_analysis["total_frequency"],
        "top_frequency_sum" => top_25_analysis["top_frequency_sum"]
    )
end

"""
Verify the 25% rule across all numbers
"""
function verify_25_percent_rule_all_numbers(engine::PairingEngine)::Dict{String, Any}
    results = Dict{Int, Dict{String, Any}}()
    coverages = Float64[]
    satisfied_count = 0
    
    for number in 1:39
        result = verify_25_percent_rule(engine, number)
        results[number] = result
        push!(coverages, result["actual_coverage"])
        
        if result["rule_satisfied"]
            satisfied_count += 1
        end
    end
    
    return Dict{String, Any}(
        "individual_results" => results,
        "mean_coverage" => mean(coverages),
        "median_coverage" => median(coverages),
        "min_coverage" => minimum(coverages),
        "max_coverage" => maximum(coverages),
        "std_coverage" => std(coverages),
        "satisfied_count" => satisfied_count,
        "total_numbers" => 39,
        "satisfaction_rate" => (satisfied_count / 39) * 100,
        "overall_rule_satisfied" => satisfied_count >= 30  # At least 77% of numbers should satisfy
    )
end

"""
Verify that top 10% pairings account for approximately 25% of frequency
"""
function verify_10_percent_rule(engine::PairingEngine, number::Int)::Dict{String, Any}
    if !(1 <= number <= 39)
        throw(ArgumentError("Number must be between 1 and 39"))
    end
    
    top_10_analysis = identify_top_10_percent_pairings(engine, number)
    coverage = top_10_analysis["frequency_coverage"]
    
    # Wonder Grid theory states top 10% should account for ~25% of frequency
    expected_coverage = 25.0
    deviation = abs(coverage - expected_coverage)
    tolerance = 8.0  # Allow 8% tolerance
    
    rule_satisfied = deviation <= tolerance
    
    return Dict{String, Any}(
        "number" => number,
        "actual_coverage" => coverage,
        "expected_coverage" => expected_coverage,
        "deviation" => deviation,
        "tolerance" => tolerance,
        "rule_satisfied" => rule_satisfied,
        "top_10_count" => top_10_analysis["actual_count"],
        "total_frequency" => top_10_analysis["total_frequency"],
        "top_frequency_sum" => top_10_analysis["top_frequency_sum"]
    )
end

"""
Verify the 10% rule across all numbers
"""
function verify_10_percent_rule_all_numbers(engine::PairingEngine)::Dict{String, Any}
    results = Dict{Int, Dict{String, Any}}()
    coverages = Float64[]
    satisfied_count = 0
    
    for number in 1:39
        result = verify_10_percent_rule(engine, number)
        results[number] = result
        push!(coverages, result["actual_coverage"])
        
        if result["rule_satisfied"]
            satisfied_count += 1
        end
    end
    
    return Dict{String, Any}(
        "individual_results" => results,
        "mean_coverage" => mean(coverages),
        "median_coverage" => median(coverages),
        "min_coverage" => minimum(coverages),
        "max_coverage" => maximum(coverages),
        "std_coverage" => std(coverages),
        "satisfied_count" => satisfied_count,
        "total_numbers" => 39,
        "satisfaction_rate" => (satisfied_count / 39) * 100,
        "overall_rule_satisfied" => satisfied_count >= 25  # At least 64% of numbers should satisfy
    )
end

"""
Create comprehensive pairing quality ranking for all numbers
"""
function create_pairing_quality_ranking(engine::PairingEngine)::Vector{Dict{String, Any}}
    rankings = Dict{String, Any}[]
    
    for number in 1:39
        top_10_analysis = identify_top_10_percent_pairings(engine, number)
        top_25_analysis = identify_top_25_percent_pairings(engine, number)
        top_50_analysis = identify_top_50_percent_pairings(engine, number)
        
        # Calculate concentration ratio (how concentrated the top pairings are)
        max_frequency = top_25_analysis["max_frequency"]
        mean_frequency = top_25_analysis["total_frequency"] / 38  # Average across all 38 other numbers
        concentration_ratio = mean_frequency > 0 ? max_frequency / mean_frequency : 0.0
        
        # Calculate quality score (higher is better)
        # Factors: 25% coverage (40%), 10% coverage (30%), concentration (20%), consistency (10%)
        coverage_25_score = min(100, top_25_analysis["frequency_coverage"]) / 100
        coverage_10_score = min(100, top_10_analysis["frequency_coverage"]) / 100
        concentration_score = min(3.0, concentration_ratio) / 3.0  # Cap at 3x average
        
        # Consistency score based on frequency distribution
        top_25_frequencies = [pair[2] for pair in top_25_analysis["top_pairings_with_freq"]]
        consistency_score = length(top_25_frequencies) > 1 ? 
            1.0 - (std(top_25_frequencies) / mean(top_25_frequencies)) : 1.0
        consistency_score = max(0.0, min(1.0, consistency_score))
        
        quality_score = (coverage_25_score * 0.4 + 
                        coverage_10_score * 0.3 + 
                        concentration_score * 0.2 + 
                        consistency_score * 0.1) * 100
        
        ranking_data = Dict{String, Any}(
            "number" => number,
            "quality_score" => quality_score,
            "top_10_coverage" => top_10_analysis["frequency_coverage"],
            "top_25_coverage" => top_25_analysis["frequency_coverage"],
            "top_50_coverage" => top_50_analysis["frequency_coverage"],
            "concentration_ratio" => concentration_ratio,
            "consistency_score" => consistency_score * 100,
            "max_pair_frequency" => max_frequency,
            "total_frequency" => top_25_analysis["total_frequency"],
            "top_10_count" => top_10_analysis["actual_count"],
            "top_25_count" => top_25_analysis["actual_count"],
            "top_50_count" => top_50_analysis["actual_count"]
        )
        
        push!(rankings, ranking_data)
    end
    
    # Sort by quality score (descending)
    sort!(rankings, by = x -> x["quality_score"], rev = true)
    
    return rankings
end

"""
Generate wonder-grid file with sorted pairing lists
"""
function generate_wonder_grid_file(engine::PairingEngine; 
                                  format::String = "standard",
                                  include_frequencies::Bool = false)::Dict{String, Any}
    
    wonder_grid_data = Dict{Int, Dict{String, Any}}()
    
    for number in 1:39
        top_25_analysis = identify_top_25_percent_pairings(engine, number)
        
        # Create sorted pairing list
        sorted_pairings = top_25_analysis["top_pairings_with_freq"]
        
        if format == "numbers_only"
            pairing_list = top_25_analysis["top_numbers"]
        elseif format == "with_frequencies" || include_frequencies
            pairing_list = ["$(pair[1]):$(pair[2])" for pair in sorted_pairings]
        else  # standard format
            pairing_list = top_25_analysis["top_numbers"]
        end
        
        wonder_grid_data[number] = Dict{String, Any}(
            "pairings" => pairing_list,
            "count" => length(pairing_list),
            "frequency_coverage" => top_25_analysis["frequency_coverage"],
            "total_frequency" => top_25_analysis["total_frequency"]
        )
    end
    
    return Dict{String, Any}(
        "format" => format,
        "include_frequencies" => include_frequencies,
        "grid_data" => wonder_grid_data,
        "generation_timestamp" => Dates.now(),
        "total_numbers" => 39,
        "average_pairings_per_number" => mean([data["count"] for data in values(wonder_grid_data)]),
        "average_coverage" => mean([data["frequency_coverage"] for data in values(wonder_grid_data)])
    )
end

"""
Export wonder-grid file to text format
"""
function export_wonder_grid_to_file(engine::PairingEngine, filepath::String; 
                                   format::String = "standard",
                                   include_frequencies::Bool = false)
    
    wonder_grid = generate_wonder_grid_file(engine, format=format, include_frequencies=include_frequencies)
    
    open(filepath, "w") do file
        # Write header
        println(file, "Wonder Grid Lottery System - Pairing Analysis")
        println(file, "Generated: $(wonder_grid["generation_timestamp"])")
        println(file, "Format: $(wonder_grid["format"])")
        println(file, "Average Coverage: $(round(wonder_grid["average_coverage"], digits=1))%")
        println(file, "=" ^ 60)
        println(file)
        
        # Write grid data
        grid_data = wonder_grid["grid_data"]
        for number in 1:39
            data = grid_data[number]
            coverage = round(data["frequency_coverage"], digits=1)
            
            if format == "with_frequencies" || include_frequencies
                println(file, "Number $number ($(coverage)% coverage): $(join(data["pairings"], ", "))")
            else
                println(file, "Number $number ($(coverage)% coverage): $(join(data["pairings"], ", "))")
            end
        end
        
        # Write summary
        println(file)
        println(file, "=" ^ 60)
        println(file, "Summary:")
        println(file, "Total numbers analyzed: $(wonder_grid["total_numbers"])")
        println(file, "Average pairings per number: $(round(wonder_grid["average_pairings_per_number"], digits=1))")
        println(file, "Average frequency coverage: $(round(wonder_grid["average_coverage"], digits=1))%")
    end
    
    return filepath
end

"""
Get best numbers for Wonder Grid strategy based on pairing quality
"""
function get_best_wonder_grid_numbers(engine::PairingEngine, count::Int = 10)::Vector{Dict{String, Any}}
    rankings = create_pairing_quality_ranking(engine)
    return rankings[1:min(count, length(rankings))]
end

"""
Analyze pairing concentration patterns
"""
function analyze_pairing_concentration(engine::PairingEngine)::Dict{String, Any}
    concentration_data = Dict{String, Any}[]
    
    for number in 1:39
        number_pairings = get_number_pairings(engine, number)
        frequencies = collect(values(number_pairings))
        
        if !isempty(frequencies)
            total_freq = sum(frequencies)
            max_freq = maximum(frequencies)
            mean_freq = mean(frequencies)
            
            # Calculate Gini coefficient for concentration measurement
            sorted_freq = sort(frequencies)
            n = length(sorted_freq)
            gini = (2 * sum(i * freq for (i, freq) in enumerate(sorted_freq))) / (n * sum(sorted_freq)) - (n + 1) / n
            
            concentration_info = Dict{String, Any}(
                "number" => number,
                "total_frequency" => total_freq,
                "max_frequency" => max_freq,
                "mean_frequency" => mean_freq,
                "concentration_ratio" => max_freq / mean_freq,
                "gini_coefficient" => gini,
                "frequency_range" => maximum(frequencies) - minimum(frequencies),
                "std_deviation" => std(frequencies)
            )
            
            push!(concentration_data, concentration_info)
        end
    end
    
    # Sort by concentration ratio
    sort!(concentration_data, by = x -> x["concentration_ratio"], rev = true)
    
    return Dict{String, Any}(
        "concentration_data" => concentration_data,
        "mean_concentration_ratio" => mean([x["concentration_ratio"] for x in concentration_data]),
        "mean_gini_coefficient" => mean([x["gini_coefficient"] for x in concentration_data]),
        "most_concentrated" => concentration_data[1:min(10, length(concentration_data))],
        "least_concentrated" => concentration_data[max(1, end-9):end]
    )
end