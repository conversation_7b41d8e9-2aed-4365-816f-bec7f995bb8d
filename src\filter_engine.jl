# Filter Engine for Ion Saliu Lottery System
# 過濾器引擎 - 實現 ONE, TWO, THREE, FOUR, FIVE, SIX 過濾器

using Dates
using Statistics

# 引入必要的類型定義
include("types.jl")

"""
過濾器類型枚舉
定義所有支援的過濾器類型
"""
@enum FilterType begin
    ONE_FILTER      # 單號過濾器 - 分析單個號碼的出現模式
    TWO_FILTER      # 雙號過濾器 - 分析號碼對的配對模式
    THREE_FILTER    # 三號過濾器 - 分析三號組合的出現模式
    FOUR_FILTER     # 四號過濾器 - 分析四號組合的統計特性
    FIVE_FILTER     # 五號過濾器 - 分析完整五號組合的重複性
    SIX_FILTER      # 六號過濾器 - 擴展分析（如果適用）
end

"""
過濾器統計結構
儲存過濾器計算的統計資訊
"""
struct FilterStatistics
    median::Float64                           # 中位數
    mean::Float64                            # 平均值
    std_dev::Float64                         # 標準差
    min_value::Int                           # 最小值
    max_value::Int                           # 最大值
    frequency_distribution::Dict{Int, Int}    # 頻率分佈
    sample_size::Int                         # 樣本大小
    
    function FilterStatistics(values::Vector{Int})
        if isempty(values)
            return new(0.0, 0.0, 0.0, 0, 0, Dict{Int, Int}(), 0)
        end
        
        sorted_values = sort(values)
        n = length(values)
        
        # 計算基礎統計
        median_val = n % 2 == 0 ? (sorted_values[n÷2] + sorted_values[n÷2 + 1]) / 2.0 : Float64(sorted_values[(n+1)÷2])
        mean_val = sum(values) / n
        std_val = n > 1 ? sqrt(sum((x - mean_val)^2 for x in values) / (n - 1)) : 0.0
        min_val = minimum(values)
        max_val = maximum(values)
        
        # 計算頻率分佈
        freq_dist = Dict{Int, Int}()
        for value in values
            freq_dist[value] = get(freq_dist, value, 0) + 1
        end
        
        new(median_val, mean_val, std_val, min_val, max_val, freq_dist, n)
    end
end

"""
過濾器結果結構
儲存單個過濾器的計算結果
"""
struct FilterResult
    filter_name::String                      # 過濾器名稱
    filter_type::FilterType                  # 過濾器類型
    current_value::Int                       # 當前值
    expected_value::Float64                  # 期望值
    is_favorable::Bool                       # 是否有利
    confidence_level::Float64                # 信心水準 (0.0 - 1.0)
    historical_values::Vector{Int}           # 歷史值序列
    statistics::FilterStatistics             # 統計資訊
    calculation_time::Float64                # 計算時間（秒）
    
    function FilterResult(
        filter_name::String,
        filter_type::FilterType,
        current_value::Int,
        expected_value::Float64,
        is_favorable::Bool,
        confidence_level::Float64,
        historical_values::Vector{Int},
        calculation_time::Float64 = 0.0
    )
        stats = FilterStatistics(historical_values)
        new(filter_name, filter_type, current_value, expected_value, 
            is_favorable, confidence_level, historical_values, stats, calculation_time)
    end
end

"""
過濾器引擎主結構
管理所有過濾器的計算和快取
"""
mutable struct FilterEngine
    historical_data::Vector{LotteryDraw}     # 歷史開獎數據
    filter_cache::Dict{String, Any}         # 過濾器計算快取
    statistics_cache::Dict{String, FilterStatistics}  # 統計快取
    enabled_filters::Set{FilterType}        # 啟用的過濾器類型
    cache_enabled::Bool                      # 是否啟用快取
    max_cache_size::Int                      # 最大快取大小
    
    function FilterEngine(
        historical_data::Vector{LotteryDraw};
        enabled_filters::Set{FilterType} = Set([ONE_FILTER, TWO_FILTER, THREE_FILTER]),
        cache_enabled::Bool = true,
        max_cache_size::Int = 10000
    )
        new(
            historical_data,
            Dict{String, Any}(),
            Dict{String, FilterStatistics}(),
            enabled_filters,
            cache_enabled,
            max_cache_size
        )
    end
end

"""
獲取過濾器引擎的基本資訊
"""
function get_engine_info(engine::FilterEngine)::Dict{String, Any}
    return Dict(
        "data_points" => length(engine.historical_data),
        "enabled_filters" => collect(engine.enabled_filters),
        "cache_size" => length(engine.filter_cache),
        "cache_enabled" => engine.cache_enabled,
        "date_range" => if !isempty(engine.historical_data)
            (engine.historical_data[end].draw_date, engine.historical_data[1].draw_date)
        else
            (nothing, nothing)
        end
    )
end

"""
清除過濾器快取
"""
function clear_cache!(engine::FilterEngine)
    empty!(engine.filter_cache)
    empty!(engine.statistics_cache)
    @info "過濾器快取已清除"
end

"""
檢查快取大小並在必要時清理
"""
function manage_cache_size!(engine::FilterEngine)
    if length(engine.filter_cache) > engine.max_cache_size
        # 簡單的 LRU 策略：清除一半的快取
        cache_keys = collect(keys(engine.filter_cache))
        keys_to_remove = cache_keys[1:length(cache_keys)÷2]
        
        for key in keys_to_remove
            delete!(engine.filter_cache, key)
        end
        
        @info "快取大小超過限制，已清理 $(length(keys_to_remove)) 個項目"
    end
end

"""
啟用特定過濾器類型
"""
function enable_filter!(engine::FilterEngine, filter_type::FilterType)
    push!(engine.enabled_filters, filter_type)
    @info "已啟用過濾器: $filter_type"
end

"""
停用特定過濾器類型
"""
function disable_filter!(engine::FilterEngine, filter_type::FilterType)
    delete!(engine.enabled_filters, filter_type)
    @info "已停用過濾器: $filter_type"
end

"""
檢查過濾器是否啟用
"""
function is_filter_enabled(engine::FilterEngine, filter_type::FilterType)::Bool
    return filter_type in engine.enabled_filters
end

"""
更新歷史數據
"""
function update_historical_data!(engine::FilterEngine, new_data::Vector{LotteryDraw})
    engine.historical_data = new_data
    # 清除快取，因為數據已更新
    clear_cache!(engine)
    @info "歷史數據已更新，共 $(length(new_data)) 筆記錄"
end

"""
添加新的開獎數據
"""
function add_draw!(engine::FilterEngine, new_draw::LotteryDraw)
    # 插入到開頭（保持最新數據在前的順序）
    pushfirst!(engine.historical_data, new_draw)
    # 清除快取
    clear_cache!(engine)
    @info "已添加新開獎數據: $(new_draw.draw_date)"
end

"""
獲取過濾器引擎的統計摘要
"""
function get_engine_statistics(engine::FilterEngine)::Dict{String, Any}
    if isempty(engine.historical_data)
        return Dict("error" => "無歷史數據")
    end
    
    # 計算基本統計
    total_draws = length(engine.historical_data)
    date_range = engine.historical_data[end].draw_date, engine.historical_data[1].draw_date
    
    # 計算號碼出現頻率
    number_frequencies = Dict{Int, Int}()
    for draw in engine.historical_data
        for number in draw.numbers
            number_frequencies[number] = get(number_frequencies, number, 0) + 1
        end
    end
    
    # 找出最熱和最冷的號碼
    sorted_frequencies = sort(collect(number_frequencies), by=x->x[2], rev=true)
    hot_numbers = [x[1] for x in sorted_frequencies[1:min(5, length(sorted_frequencies))]]
    cold_numbers = [x[1] for x in sorted_frequencies[max(1, end-4):end]]
    
    return Dict(
        "total_draws" => total_draws,
        "date_range" => date_range,
        "hot_numbers" => hot_numbers,
        "cold_numbers" => reverse(cold_numbers),  # 最冷的在前
        "cache_statistics" => Dict(
            "filter_cache_size" => length(engine.filter_cache),
            "statistics_cache_size" => length(engine.statistics_cache)
        ),
        "enabled_filters" => collect(engine.enabled_filters)
    )
end

# 導出主要結構和函數
export FilterEngine, FilterType, FilterResult, FilterStatistics
export ONE_FILTER, TWO_FILTER, THREE_FILTER, FOUR_FILTER, FIVE_FILTER, SIX_FILTER
export calculate_one_filter, calculate_two_filter, calculate_three_filter, calculate_four_filter, calculate_five_filter
export get_engine_info, clear_cache!, enable_filter!, disable_filter!
export is_filter_enabled, update_historical_data!, add_draw!
export get_engine_statistics
