# Core data types for the Wonder Grid Lottery System

using Dates

"""
Basic lottery draw representation
"""
struct LotteryDraw
    numbers::Vector{Int}
    draw_date::Date
    draw_id::Int
end

"""
Wonder Grid configuration
"""
struct WonderGridConfig
    key_number::Int
    top_pairings::Vector{Int}
    combination_count::Int
    ffg_median::Float64
    current_skip::Int
end

"""
Pairing frequency data
"""
struct PairingFrequency
    pair::Tuple{Int,Int}
    frequency::Int
    percentage::Float64
    rank::Int
end

"""
Strategy execution result
"""
struct StrategyResult
    key_number::Int
    combinations::Vector{Vector{Int}}
    generation_time::Float64
    estimated_cost::Float64
    expected_efficiency::Dict{String, Float64}
end

"""
Statistical analysis results
"""
struct StatisticalSummary
    total_draws::Int
    frequency_distribution::Dict{Int, Int}
    mean_frequency::Float64
    median_frequency::Float64
    standard_deviation::Float64
end

"""
Skip analysis data
"""
struct SkipChart
    number::Int
    skip_sequence::<PERSON>ector{Int}
    current_skip::Int
    ffg_median::Float64
    is_favorable::Bool
end

"""
Cost analysis for strategy
"""
struct CostAnalysis
    total_combinations::Int
    cost_per_combination::Float64
    total_cost::Float64
    cost_savings_vs_random::Float64
end

"""
Backtesting results
"""
struct BacktestResult
    strategy_name::String
    test_period::Tuple{Date, Date}
    total_combinations::Int
    hit_rates::Dict{String, Float64}  # "3/5", "4/5", "5/5"
    efficiency_ratios::Dict{String, Float64}
    cost_analysis::CostAnalysis
end

"""
Validation result
"""
struct ValidationResult
    is_valid::Bool
    message::String
end

"""
DATA5 file format specification
"""
struct DATA5Format
    numbers_per_line::Int
    number_range::UnitRange{Int}
    separator::String
    line_ending::String
    chronological_order::Bool
    
    DATA5Format() = new(5, 1:39, ",", "\n", true)
end

"""
Wonder Grid file format specification
"""
struct WonderGridFormat
    lines_per_number::Int
    pairings_per_line::Int
    format::String
    
    WonderGridFormat() = new(39, 10, "number: pairing1 pairing2 ... pairing10")
end

"""
Data integrity report
"""
struct IntegrityReport
    total_draws::Int
    duplicates_found::Int
    invalid_numbers::Int
    chronology_issues::Int
    is_valid::Bool
    issues::Vector{String}
end

"""
Hit rates for different prize tiers
"""
struct HitRates
    three_of_five::Float64
    four_of_five::Float64
    five_of_five::Float64
end

"""
Efficiency comparison with random play
"""
struct EfficiencyComparison
    strategy_odds::Dict{String, Float64}
    random_odds::Dict{String, Float64}
    efficiency_ratios::Dict{String, Float64}
end

"""
Game type enumeration
"""
abstract type GameType end

struct Lotto5_39 <: GameType
    total_numbers::Int
    numbers_per_draw::Int
    
    Lotto5_39() = new(39, 5)
end