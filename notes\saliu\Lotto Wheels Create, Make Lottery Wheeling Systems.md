---
created: 2025-07-24T21:26:40 (UTC +08:00)
tags: [lotto wheels,lotto wheeling,abbreviated,reduced,lotto,systems,lottery wheel,free,software,lottery,wheeler,Powerball,Mega Millions,Euromillions,create,make,]
source: https://saliu.com/lottowheel.html
author: 
---

# Lotto Wheels: Create, Make Lottery Wheeling Systems

> ## Excerpt
> Make, create lotto wheels manually or use the best lottery wheeling software to create efficient lotto wheels, reduced lottery systems, all free, balanced.

---
**_Da Super Lottery Wheeler and Strategist_**

[![<PERSON> teaches you how to create the best lotto wheels mathematically with combinatorics.](https://saliu.com/AxiomIon.jpg)](https://saliu.com/free-lotto-tools.html)  

## <u>Wheeling Lotto Numbers, Playing <i>Lotto Wheels</i>, Make Your Own Lottery Systems</u>

## By <PERSON>, ★ _Founder of Lotto Wheeling Mathematics_

![You make lotto wheels manually or use the best lottery wheeling software to create lotto wheels.](https://saliu.com/HLINE.gif)

## <u>I. Introductory Notes to Lotto Wheels: Mathematical Foundation of Lottery Wheeling</u>

First captured by the _WayBack Machine_ (_web.archive.org_) on November 21, 2002.

-   Wheeling lotto numbers or playing lotto wheels (_abbreviated systems_ or _reduced lottery systems_) is the main tool of the so-called _expert lottery players_. The concept has been around for over two hundred years. The mathematicians were the first ones to study that matter long before lottery became available (especially from a legal standpoint). I will show you the easiest way — and the most sound mathematically — method of creating lotto wheels manually.

Let's take the two most basic sets and wheel their elements: **9** numbers, and **12** numbers, respectively. I sold (very cheaply!) this wheeling method and also a good set of lotto wheels in 1986-1987. I discontinued the offer because I was able to buy an IBM-compatible PC and started to write serious lottery software, including for lotto wheeling. I did a Samaritan thing. I had a number of copies of my cheap lotto-wheel brochure left. I gave it away at a gas station in Gettysburg, Pennsylvania. The station was also a licensed lottery agent (I played there sometimes, when I fueled my car). I returned to the same station in a couple of weeks. The lottery agent asked me if I had more copies of my lotto wheels brochure. She said: _"It went like hot cakes! Got more copies?"_ NOT! Unfortunately, I didn't even save one copy for myself!!! The lotto wheel leaflet was typed. I didn't have a personal computer at that time (only very few individuals had; by far, most of them were living in the United States).

You might be able to find someone who bought my 16-page lotto wheel brochure. I charged between $3 and $5 for the useful package. I was told (hush-hush!) that my material is a collectible now. If you are really interested, just google for a while… Also, you might have heard of one Robert Serotic. He too was an immigrant to the United States. He published books on the topic of lotto wheels. Robert Serotic published also a lotto wheeling newsletter. A user of my lottery software surprised me when he sent me Serotic's newsletter. Robert Serotic had published — without permission — the lotto wheeling method presented in my brochure. Granted, he gave me credit as the author. Problem was, he printed my method secretly, without asking for my permission (forget about pay!) Better Business Bureau of California took notice of the incident!

Having said that, the initial lotto wheels of mine created in the 1980s are bundled with **MDIEditor And Lotto WE**. A more comprehensive group is offered as **freeware** and covers also 5-number and 7-number lotto wheels. Not to mention that my lottery freeware also includes wheels for Powerball, Mega Millions, and Euromillions. Follow the links in the _Resources_ section.

### <u><i>9-number</i> lotto wheel for lottery games drawing 6 numbers</u>

We can divide the numbers in 3 groups of 3 numbers each: _A, B, C_. Group _A = 1,2,3_; group _B = 4,5,6_; group _C = 7,8,9_. We can combine the 3 groups 2 at a time in a total of C (3, 2) = 3 combinations of 6 numbers each: _AB, AC, BC_. Converting the groups to their initial compositions, we get:

1,2,3, 4,5,6  
1,2,3, 7,8,9  
4,5,6, 7,8,9  

The lottery commission draws 6 winning numbers. The drawing is random, therefore the 6 winning numbers will have a random spread among the 3 3-number groups. There are 3 distribution possibilities:

0-3-3 = 6 winners of 6 = 33%  
1-2-3 = 5 winners of 6 = 33%  
2-2-2 = 4 winners of 6 = 33%

Since all the groups are paired with one another, our 3-combination lotto wheel cannot have fewer than 4 winning numbers. They say, the lotto wheel assures a **minimum guarantee**: _4 of 6_. That is, if the 6 winning numbers are among the 9 we selected, the wheel guarantees that at least one combination will contain at least 4 of the winning numbers. In 33% of the winning distribution possibilities, the 9-number lotto wheel hits the jackpot (_6 winners of 6_)!

Moreover, this lotto-6 wheel for 9 numbers also guarantees <u>at least</u> _4 out of 5_. There are no worse distributions of the 5 winning numbers than _1-2-2_ or _1-1-3_. Here are the possible distributions among the 3 groups:

0-2-3 = 5 winners of 6 = 33%  
1-1-3 = 4 winners of 6 = 33%  
1-2-2 = 4 winners of 6 = 33%

### <u><i>12-number</i> lotto wheel for lottery games drawing 6 numbers</u>

Axiomatic one, this must be the best lottery wheel in history. It has a **good coverage** (12 numbers or a double-ticket); it is **perfectly balanced** (all numbers are wheeled equally — 3 times each); the reduced lotto system has an **excellent minimum guarantee**: _4 of 6_; the best results regarding **higher tier prizes** (_5 of 6_ and an excellent chance for _6 of 6_ or jackpot). Absolutely no lottery system can beat this lotto wheel that comes very close to the odds: **6** lines, as opposed to hypergeometric lottery odds of 5 lines.

We can divide the dozen in 4 groups of 3 numbers each: _A, B, C, D_. Group _A = 1,2,3_; group _B = 4,5,6_; group _C = 7,8,9_; group _D = 10,11,12_. We can combine the 4 groups 2 at a time in a total of C (4, 2) = 6 combinations of 6 numbers: _AB, AC, AD, BC, BD, CD_. Converting the groups to their initial compositions results in a magnificent creation:

1,2,3, 4,5,6  
1,2,3, 7,8,9  
1,2,3, 10,11,12  
4,5,6, 7,8,9  
4,5,6, 10,11,12  
7,8,9, 10,11,12

The lottery commission draws 6 winning numbers. The drawing is random therefore the 6 winning numbers will have a random spread among the 4 3-number groups. There are 5 distribution possibilities:

0-0-3-3 = 6 winners of 6 = 20%  
0-1-2-3 = 5 winners of 6 = 20%  
0-2-2-2 = 4 winners of 6 = 20%  
1-1-2-2 = 4 winners of 6 = 20%  
1-1-1-3 = 4 winners of 6 = 20%  

Since all the groups are paired with each other, our 12-combination lotto wheel can't have fewer than 4 winning numbers. Thusly, the lotto wheel assures a so-called 'minimum guarantee': _4 winners out of 6_. That is, if the 6 winning numbers are among the 12 we selected, the wheel guarantees that at least one combination will contain at least 4 of the winning numbers. In 20% of the winning distribution possibilities, the 12-number lotto wheel hits the jackpot (_6 winners of 6_)!

Those two reduced lottery systems are the only lotto wheels totally based on solid mathematics. The two systems offer tremendous leverage. Personally, I will never play any other lotto wheel. Actually, I settled on the 12-number wheel only. The 12-6 lotto wheel provides very efficient leverage. This extraordinary phenomenon in lotto wheeling is known as the **_Parpaluck effect_**.

The special advantage only occurs in lotto games that draw an **even** amount of winning numbers: Lotto-4, lotto-6, lotto-8 (if ever implemented, etc.) For example, lotto-4 games draw 4 winning numbers. The optimal wheel has 8 numbers (the double of the winning numbers). Divide the 8 numbers in groups of 2; a total of 4 groups results; wheel the 4 groups two at a time; the final result is a 6-line lotto wheel, with 4 numbers per combo. The distribution of the winning numbers in the 4 groups can be: 0-0-2-2 (first prize); 1-1-1-1 (the worst distribution = third prize); etc.

These are the optimal cases of wheeling lotto numbers. No, the lotto wheels cannot be created… equal! Take the 12-number situation. We can group the numbers 2 at a time, for a total of 6 groups of 2 numbers each. We combine the 6 groups 3 at a time, for a total of C(6, 3) = 20 combinations, 6 lotto numbers at a time. The distribution of the 6 winners gets worse, like in this case:

1-1-1-1-1-1 = 3 winners of 6!

Now, we constructed a larger lotto wheel — 20 6-number combinations — but it only guarantees _3 winners out of 6_! Pay more to get less! This negative phenomenon in lotto wheeling is known as the _**Kokostirk-Roberts effect**_.

We all know of another popular lotto format: Drawing 5 winning numbers. How about them **_5-number lotto wheels_**? Short answer: No, we can't construct the same high level of efficiency as in the case of 12 number lotto wheels drawing 6 winning numbers. But we can come acceptably close. Again, we must put the numbers together, in groups. I built a 5-number wheel on the same mathematical principles.

The double of the 5 winning lotto numbers is 10. Therefore, the optimal number pool must have 10 elements. Drawback: We can't arrange the numbers in equal-size groups (because 5 is a prime number). So, my best method was to create two groups of 3 numbers each, and two groups of 2 numbers each. Group A=1,2,3; group B=4,5; group C=6,7,8; group D=9,10. We can combine the 4 groups 2 at a time in a total of C (4, 2) = 6 combinations of 5 numbers: AB, AC, AD, BC, BD, CD. We deleted one number from the 6-number resulting combination; also, we added one number to the resulting 4-number _combonation_ (a favorite term of mine). Converting the groups to their initial compositions, we get:

1,2,3, 4,5  
1,2,3, 6,7  
1,2,3, 9,10  
4,5, 6,7,8  
4,5,8, 9,10  
6,7,8, 9,10

The random distribution of the 5 winning numbers in a lotto-5 draw is more complicated than in a lotto-6 game. We can't group the numbers equally. Thus, the 3-number group can have 3, or 2, or 1 of the winners in a drawing.

0-0-2-3 = 5 winners of 5 = 14+%  
0-1-2-2 = 4 winners of 5 = 14+%  
0-1-2-2 = 4 winners of 5 = 14+%  
0-1-2-2 = 4 winners of 5 = 14+%  
1-1-1-2 = 3 winners of 5 = 14+%  
1-1-1-2 = 3 winners of 5 = 14+%  
1-1-1-2 = 3 winners of 5 = 14+%  

I believe this 5-number lotto wheel has good leverage too. For starters, it assures the _3 of 5_ minimum guarantee alright. In fact, the _3 of 5_ prize is offered in 43% of the favorable cases (when our 10 picks contain the 5 lotto winners in a drawing). In 43% of the favorable cases, this lotto wheel assures at least one _4 of 5_ winners. Not to be neglected, this lotto wheel assures '5 of 5' winners with a 14% degree of certainty.

## <u>II. Tools of Lotto Wheeling: Mathematics, Theory, Software</u>

The above is a new sub-set, constructed from the inclusive set C(12,6)=924 combinations of 6 numbers each. In my theory of sets, if the inclusive set has a main completion rule, a sub-set can be constructed if and only if it has a specific completion rule derived from the main completion rule.

Knowledgeable players will not play combinations such as _1,2,3,4,5,6_. I detailed in another message how wasteful such play really is. The players will usually select their own numbers, also known as _picks_. For example: _7,13,24,8,33,35,42,28,19,37,40,49_. The next important step is wheeling the picks. That is, the player needs to replace the theoretical numbers _1,2,3,4,…, 12_ by the picks _7,13,24,8,…,49_. The process can be done manually, but it is tedious and error prone. The players use software to wheel their lotto numbers. Software wheeling cost money, up until I released freeware to wheel lotto numbers.

Previous software wheelers were also limited in scope. They could only wheel their own abbreviated lotto systems. Generally, they were unable to wheel third-party wheels or Powerball systems. My lottery wheeler software is the most comprehensive to date. It can wheel any kind of wheel, for up to 100 numbers per combination. It can also wheel Powerball, Mega Millions and Euromillions _lotto wheels_ or _abbreviated lottery systems_.

[![LottoWheeler is the best software to convert lotto wheels to real lottery picks.](https://saliu.com/ScreenImgs/lotto-wheeler.gif)](https://saliu.com/membership.html)

Enter the scene:  
• **FillWheel** - August 2002 - Freeware.  
• **LottoWheeler** - September 2006 - The best - Free 32-bit **lotto wheeling** software.  
That's the easiest way to convert the theoretical lotto wheels or systems (_1,2,3,4,5,6_ etc.) to combinations of real picks (_29,13,33,2,44,39_ etc.)

-   _****Lotto Wheeler****_ is your best choice, regardless of price. The lotto-wheeling software covers the Powerball, Mega Millions, CA SuperLotto, and Euromillions lottery games as well. That is, if you have lotto wheels for those games. Hint: You can find some at this Web site! Please check the _Resources_ section of this page.

Using my own software, I have been able to devise the most efficient possible lotto wheels. For example, my 10-number lotto wheel in 3 combinations offers the _4 of 6_ minimal guarantee, following exactly the lotto odds. The previous best wheel in that category consisted of 5 combinations.

A few more wheels from yours truly. They were created by my lotto software **WheelCheck6** (also available for 5-number lottos: **WheelCheck5**):

\* 10 numbers, 100% _4 of 6_, 3 combinations (exactly the lotto odds)  
1 2 3 4 5 6  
1 3 4 8 9 10  
2 5 6 7 9 10  

\* 11 numbers, 100% _4 of 6_, 5 combinations (20% over the lotto odds)  
1 2 3 4 5 6  
1 2 8 9 10 11  
1 3 5 8 10 11  
2 3 5 7 9 10  
4 6 7 8 9 11

Nobody had been able to construct such lotto wheels before my software. The two wheels, however, mushroomed all over the Internet, a few months _**after**_ I published them!

Be aware, however, that the lotto wheels can do more harm than good. Read my article _"The myth of lotto wheels or abbreviated lotto systems"_ (follow the _Resources_ section). The player must use a good strategy of picking lotto numbers, before using lotto wheels. Please read the main lotto lottery strategy page at this site.

My own software, _**LotWon**_, _**SuperPower**_ and _**MDIEditor and Lotto WE**_, comes with a number of lotto wheels or systems. Please keep in mind that those systems were not optimized according to my own set theory. The wheels contain more combinations than the respective lotto odds. Such systems aimed at hitting better prizes. They are meant to be used in conjunction with the lottery strategies I have presented at this website.

The 6-40 lotto game back then (1986) required to play at least two tickets for $1. We played a total of 36 combinations, for $6 each. I had won twice before with one of my lotto colleagues (_4 of 6_). That time, I applied my 9-number lotto system. The three of us in the lotto group also won once another _4 of 6_ third prize. That prize usually paid over $100 per winning ticket.

We played 12-number lotto wheels. I used two wheels for the same 12-number group. The first lotto wheel used the group in lexicographic order, from 1 to 12, in 6 combinations. Then, I shuffled the 12-number set and applied the wheel to it. I always used the same shuffled set to save time. In total, we played 3 groups of 12 numbers each.

The program I wrote generated 12-number combinations randomly. The numbers were not sorted: RAM, speed, and retyping were the main reasons. I wrote the last 20 lotto drawings at the beginning of the program, in the _READ/DATA_ section. I hadn't seen any 4-number group repeating from the last 20 drawings. Of course, the average no-repeat is much longer than 20 draws. Trying to eliminate all 4-number groups in 12-number combinations would be a daunting task (virtually, _mission impossible_, given the home-computing technology at that time). So, I had to be selective. I eliminated 1-2-3-4 and 9-10-11-12 for sure; then, a few more groups in between, like 4-5-6-7, 8-9-10-11.

The computer (Atari 800XL) had a real hard time! I let it run until I saw at least 10 combinations on screen (no disk to save the output!) I wrote down the last three 12-number combos (2 lines each on screen). Then, wheel each 12-number lotto combination twice (manually). It was laborious (you can try it for yourself) and it took some time. Then, fill out the play slips. There were some errors every time, especially filling out the lotto play-cards.

![These are the only winning strategies applicable to lotto wheels for 12 and 18 numbers.](https://saliu.com/HLINE.gif)

-   Axiomatic one, I created three great strategies that increase the efficiency of some lotto wheels by an order of magnitude. The super strategies are applicable to an 18-number lotto wheel in 48 lines (tickets).
-   The strategies, however, can be also applied to the 12-number wheel I presented on this very page.
-   The strategies are based on number frequency, best pairings, and best triples, respectively.
-   [_**<u>The Best Lotto Wheels for 18 Numbers</u>: 4-in-6 minimum guarantee; higher chances at jackpot**_](https://saliu.com/bbs/best-18-number-lotto-wheels.html).
-   Moreover, more reduced lottery systems are available, including links to freely download the 6-number lotto wheels presented on this very page. It's all you need to play lottery wheeling for real. Don't miss the opportunity — and the best of luck to you!

![Selecting the lotto numbers to play a wheel is the most important element. The best are the most frequent lottery numbers.](https://saliu.com/HLINE.gif)

## [<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)

It lists the main pages on the subject of lottery, lotto, software, wheels and systems.

See the applications in new and old lottery strategies of the lotto wheels and lotto wheeling techniques presented here:

-   [_**Lotto Strategy, Software: 12-Numbers Combinations in 6-Number Lotto Games**_](https://saliu.com/12-number-lotto-combinations.html).  
    
-   There is also software that applies this lottery strategy to pick-5 lotto:
-   [_**Lotto Software, Wheels, 10-Number Combinations in Lotto 5 Games**_](https://saliu.com/lotto-10-5-combinations.html).
-   _**The Main**_ [_**Lottery, Software, Strategies**_](https://saliu.com/LottoWin.htm), _**Lotto Wheeling Page**_.  
    
-   [_**Neural Networking, Artificial Intelligence AI, Axiomatic Intelligence AxI in Lottery: Strategies, Systems, Software**_](https://saliu.com/neural-networking-lottery.html).
-   [**<u>Lotto Wheels</u>**](https://saliu.com/lotto_wheels.html) for _**Lottery Games Drawing 5, 6, 7 Numbers**_.  
    
-   [_**Myth of Lotto Wheels, Abbreviated, Reduced Lottery Systems**_](https://saliu.com/bbs/messages/11.html).  
    Statistical analysis of real lottery data proves the negative effect of playing static (unbalanced or split) lotto wheels.
-   [_**<u>Positional</u> Lotto Wheels, Reduced Lottery Systems <u>Position-by-Position</u>**_](https://saliu.com/positional-lotto-wheels.html).
-   **Free** [_**Lottery Wheeling Software for Players of Lotto Wheels**_](https://saliu.com/bbs/messages/857.html).
-   Fill out lotto wheels with player's picks (numbers to play); presenting _FillWheel_, **LottoWheeler** lottery wheeling software.
-   _**WHEEL-632 available as the**_ [_**Best <u>On-The-Fly Wheeling</u> Software**_](https://saliu.com/bbs/messages/wheel.html) — applies real lottery filtering.
-   [_**Software to Verify Lotto Wheels**_](https://saliu.com/check-wheels.html) _for missing combinations and generate lotto 5, 6 abbreviated (reduced) systems._
-   [_**Copyright Lottery Numbers, Combinations, Lotto Wheels, Mathematical Relations, Formulas, Equations**_](https://saliu.com/copyright.html).
-   [_**Balanced Lotto Wheels, Lexicographical Order Lottery Wheeling**_](https://saliu.com/bbs/messages/772.html).
-   [_**Powerball Wheels**_](https://saliu.com/powerball_wheels.html).
-   [_**Mega Millions Wheels**_](https://saliu.com/megamillions_wheels.html).
-   [_**Euromillions Wheels**_](https://saliu.com/euro_millions_wheels.html).
    -   _**Download Lotto Wheeling Software,**_ [**<u>Lottery Software</u>**](https://saliu.com/infodown.html):
    -   **Wheel-632, Wheel-532**, the best on-the-fly wheeling software; applies real lottery filtering.
    -   ~ Superseded by the most powerful integrated packages Pick532, Pick532 (now demoted!) and, especially, the **<u>Bright / Ultimate</u>** software packages.  
        
    -   **Combinations**, the universal combinations generator for any lotto, Keno, Powerball, Mega Millions, Euromillions;  
        
    -   **LexicoWheels**, generator of lotto wheels based on lexicographical indexing; applies to any lotto, Keno, Powerball, Mega Millions, Euromillions or two-in-one lotto games.  
        
    -   **WheelCheck5, WheelCheck6**, software to verify lotto wheels for missing combinations and generate lotto-5 and lotto6 systems.  
        
    -   **LottoWheeler**, lottery wheeling software: fill out lotto wheels with player lotto numbers. Supersedes the old-time favorite **FillWheel** (still offered). The two pieces of software replace the theoretical lotto numbers in the SYS/WHEEL files by your picks (the lotto numbers you want to play).
    -   **Shuffle, SuperFormula** to shuffle (randomize, randomly arrange) your lotto picks. Repeat the randomizing procedure several times. The randomized wheels can be used directly, without shuffling your lotto picks first.

![Lottery wheeling master teaches how to create the best 12-number lotto wheel, 18 numbers 6 per line.](https://saliu.com/HLINE.gif)

**[Home](https://saliu.com/index.htm) | [Search](https://saliu.com/Search.htm) | [New Writings](https://saliu.com/bbs/index.html) | [Fundamental Formula](https://saliu.com/formula.htm) | [Odds, Generator](https://saliu.com/calculator_generator.html) | [Contents](https://saliu.com/content/index.html) | [Forums](https://forums.saliu.com/) | [Sitemap](https://saliu.com/sitemap/index.html)**

![The 12 and 9-number lotto wheels assure best chances for high lottery prizes, including the jackpot.](https://saliu.com/HLINE.gif)
