---
created: 2025-07-24T21:26:41 (UTC +08:00)
tags: [lotto,lottery,software,strategy,strategy checking,numbers,combinations,pick 3,pick-3,pick-4,lotto keno,Powerball,power ball,mega millions,free]
source: https://saliu.com/bbs/messages/623.html
author: <PERSON>
---

# Judge Best Lotto Software, Lottery Strategy, Gambling Theory

> ## Excerpt
> Users judge and verdict as the best in lotto software, MDIEditor Lotto WE, lottery strategy, gambling theory, formulas, fundamental universal laws.

---
Written on December 16, 2001; later updates, especially related to software upgrades.

I finalized the first licensed version of MDIEditor And Lotto WE. **_Version: 1\_WE, December 2001 (1 WE)_**. It was, by far, the toughest programming task I ever experienced. Now, as I fulfilled the goal, I feel very proud. The application looks great and performs very difficult jobs. I pushed the GUI as in 32-bit Windows to the limit. There is nothing left in 32-bit Windows performance-wise! I only want to say this. Windows is like a person hired as a rocket scientist based on her/his appearance!

MDIEditor And Lotto WE is the most comprehensive lottery gambling software in history. It handles: pick-3, pick-4, horseracing, lotto-5, lotto-6, lotto-7, and for the first time ever, _Powerball / Mega Millions 5+1_ and _Euromillions 5+2_. It includes also the keno from the freeware version. Keno is not fully supported, however. The game is huge and requires its own MDIEditor Keno Lotto. Since there is no attractive market for keno, let's forget about it!

The **filters** in MDIEditor Lotto WE are different from the filters in _**command prompt**_ **LotWon** lottery software. Suffice to say they are _spatial filters_, as opposed to _plane_.

The super application does statistical analyses and reporting, generates optimized combinations, or “purges” output files of combinations previously generated (by _**command prompt**_ **LotWon** lottery software or by MDIEditor Lotto itself). The program also checks for winning numbers and duplicate combinations. Many users of the free version of MDIEditor Lotto noticed that combinations start to repeat after running the program for a while. It is also a sign that the winning combination has already been generated. The user can save the combinations to a file, then strip off the duplicates.

A great feature imported from the _**command prompt**_ **LotWon** lottery software: Checking how various strategies fared in the past. The job is simplified in Windows. The user can simply copy and paste filter values from the strategy window to the input form.

![Read tutorial, instructions, manual of software for lottery, Powerball, Mega Millions, Euromillions.](https://saliu.com/ScreenImgs/powerball-lotto.gif)

I'll show fragments for Powerball-type lotto skip reports.

```
<span size="5" face="Courier New" color="#c5b358">                               * 'Regular-5' # 47 * Hits: 8 
 * Skips:   20  16  4  16  2  9  0  11 
* Sorted Skips:  0  2  4  9  11  16  16  20 
** Skip Median:  9 


                               * 'Regular-5' # 48 * Hits: 13 
 * Skips:   1  4  3  4  3  5  14  5  1  29  7  11  0 
* Sorted Skips:  0  1  1  3  3  4  4  5  5  7  11  14  29 
** Skip Median:  4 


                               * 'Regular-5' # 49 * Hits: 15 
 * Skips:   1  0  2  3  1  12  2  4  2  3  2  13  15  2  18 
* Sorted Skips:  0  1  1  2  2  2  2  2  3  3  4  12  13  15  18 
** Skip Median:  2 


                               <i>Powerball SKIP Chart</i>

                               * Powerball # 1 * Hits: 5 

* PB Skips:   10  14  28  33  10 
* Sorted PB Skips:  10  10  14  28  33 
** PB Skip Median:  14 

                               * Powerball # 2 * Hits: 3 

* PB Skips:   6  40  44 
* Sorted PB Skips:  6  40  44 
** PB Skip Median:  40 


                               * Powerball # 3 * Hits: 1 

* PB Skips:   21 
* Sorted PB Skips:  21 
** PB Skip Median:  21 

</span>
```

Here is how strategy checking works for a 49/5/42 Powerball game. The key filters in this strategy are:  
_Ion1 = 200_  
_Power Ball = 20_ (i.e. do not play any Powerball that also came out in the last 20 draws).

The strategy had 24 hits in the last 100 draws. At run time you can select other filters. You need to set them to _safe_ or conservative levels. For example, set Ion2=10, Ion5=2, Any3=3, Any4=5, Any5=10, etc. You can simply copy the filter in the strategy window and paste it in the new filter input form. In this case, the numbers go to the _minimum_ input boxes. You can type 2 in the _maximum_ input box of Any1 (since it shows 0 or 1 in most cases).

The current skip of the strategy is 1, i.e. _equal to the median_. It is the right time to play such a strategy! It is not recommended to play a strategy above its skip median; the waiting period can be too long and therefore more expensive.

```
<span size="5" face="Courier New" color="#c5b358">         *** Strategy Composition ***
         *** If the MAXIMUM level of a condition is set to 0 ***
         *** and therefore the condition was disabled ***
         *** the program sets as defaults very high values ***

 Ion_1:      <i>200</i>     99999
 Ion_2:        0      9999
 Ion_3:        0      9999
 Ion_4:        0      9999
 Ion_5:        0      9999

 PBall:       <i>20</i>      9999

 Any_1:        0      9999
 Any_2:        0      9999
 Any_3:        0      9999
 Any_4:        0      9999
 Any_5:        0      9999

 Ver_1:        0      9999
 Ver_2:        0      9999
 Ver_3:        0      9999
 Ver_4:        0      9999
 Ver_5:        0      9999


         *** Frequency of the Strategy in 100 Drawings ***

 * Times:  24 
 * Skips:  1  4  0  1  1  0  2  0  0  0  0  3  1  0  0  2  1  3  1  0  2  0  1  0 
 * Sorted Skips:  0  0  0  0  0  0  0  0  0  0  0  <i>1</i>  1  1  1  1  1  1  2  2  2  3  3  4 
 * Median Skip:  1 


              * Powerball Strategy in the Past
              File: C:\PROGRAM FILES\MDIEDITORLOTTO\CHECK.PB5 
              Date: 12-16-2001

 Draw   Ion   Ion   Ion   Ion   Ion    Power  Any   Any   Any   Any    Any   Ver   Ver   Ver   Ver   Ver
  #      1     2     3     4     5     Ball    1     2     3     4      5     1     2     3     4     5 

   2    248+   35+    6+    6+   11+     85+  0       2+     3    4-    78+    0-    7+   79+  248+  248+
   7    243+    3-    3-    1+    5-     40+  0       1-     3-  21+    21+    1-    3-    8-   30-   64-
   8    242+  154+    7     0-   11+     33   0       4+     4+   7      8-    6+   11    49-   61-  202+
  10    240+  108+    7-    5-   10+     33+  0       1      3+   7     17+    1    11+   66+  108+  124+
  12    238+   48-   17+    9+    6-     21-  0-      1-     1-   7+    12+    1-    4-   12-   22-   27-
  13    237+   70+    0-    0    10+    117+  1       3+     5+   6+     7-   16+   32+   69+   71+  237+
  16    234+   29-   14+    0     0-     42-  1       2-     3-   4-    11+    2-    3-    4-   11-   11-
  17    233+   36+    2+    0-    4+     62-  1-      7+     8+   9+     9-    7+    9+    9+   28+   33+
...
  37    213+  153+    2-    3+    9+     64-  1-      3      7+  21+    43+   22+   27+   43+  145+  213+
  39    211+  114+    3+    2+    2+    104+  2       3      6+  10+    18+    2     3     7+   10+   26+
  40    210+   14+    0-    0-    0-     30+  2-      3-     5-   5-     5-    2-    3-    5-    8-   10-
  43    207+    4-    8+    2     9+     26-  8+     12+    13-  13-    16-    8+   13+   13-   20-   29-
  44    206+   18-    4-    2+    4-     36-  0       1+    19+  20+    33+    0     7+   20-   31-   53+
  46    204+   43-   15-    0-    9+    132+  0-      0-     3+   4      5-    0-    3-   24-   33-   51-
  47    203+   63+   18+    4+    4+    108+  1+      1+     1+   4+    23+    1+   15+   34+   51+  102+

</span>
```

![Well-educated visitors show high appreciation for Ion Saliu's theory, lottery software, systems.](https://saliu.com/bbs/messages/HLINE.gif)

-   **ATTITUDE: That's what is all about in creating.** And the right **attitude** is:
    
    **_Do not lie to yourself first of all, for the closest judge to you is your self._**
    
-   All misery that comes out in industrial quantities is the result of lying to oneself.
-   I do my best to apply the right attitude when I create, all that I create. I know I do not lie to myself. Therefore, I have a clean conscious when facing those who have contact with my creation. Most people view me in that light.
-   There are still others who'd do their best to shoot all the lights out and see me in the deepest darkness. I receive messages and I notice facts.

Messages I've received (actually, the vast majority of them are **positive**):

-   _"A while back I overheard some people talking about **[the lottery system](https://saliu.com/lottery.html)** and how it's all one big joke and it is either rigged, or you can easily predict the following numbers, I have been searching the net over time and time again and have not found anything remotely true about any of this until I found your page, it looks all important but I do not understand a word of your winning pattern, but it is very interesting I will be showing some people to see if they can pick up the message that you are trying to show...._
-   _Also I am just really curious to know if predicting a pattern of winning numbers with the lottery is really possible?  I am asking you because you are the closest I have come to something like that, I have asked other math teachers educators before, they all tell me the same thing ...that it is random and there is no way you could use math to do anything like that....I am asking for your opinion on this...I am very curious to know, thanks a lot sir...please mail me back thanks."_
-   _"I'd got your jobs about lotto and it systems for myself application. I'd understood yours explanations about **[gambling formula mathematics](https://saliu.com/formula.htm)** and so. All contents are to myself. For your brilliant work and good discovers about lotto/lotteries I send the congratulations. All the best to you and many successful.  
    Sorry, my english is not good, but I'm happy to contact you."_
-   _"Hello...I'm just coming from your website: I wish it could be published all over the internet that most if not all software driven casino -- esp. roulette -- is fraudulent and preprogramed to make every system a big loser. My friends and I employ a **[roulette strategy system](https://saliu.com/best-roulette-systems.html)** that's NEVER failed yet to make us at least $30/hr in real casinos at Vegas. I guess out of all of us, I was the only one stupid enough to try my luck at online casinos. Sure enough, when I downloaded software and played for free my system had me consistently turning $50 into $300 in an hour's time._
-   _So like a sucker, I hand over my credit card info and start playing for real. Sure enough, the computer let me win on many successive $1 bets, but I would always lose when I went up to $5. Also, at one point, within less than 30 spins, the wheel gave me a string of number that defied every sense of mathematical probability. I mean something that had less than a 1 in 10,000 chance of happening._
-   _I chalked this up to a single streak of bad luck and tried again with another $50. Needless to say, it wasn't long before I lost this too when something else "strange" happened. To cut a long story short, on four consecutive tries, a system that has never failed me once at Vegas or when playing online with fake money failed me the instant I started playing with real money._
-   _I'm still pissed that I was forced to part with $200 of my money, but had I not come across your site, I would have kept believing that it was just a string of extremely bad luck defying 1 in a million probabilities, and possibly just kept coughing up $50 pops to different **[online casinos fraud](https://saliu.com/bbs/messages/844.html)**. Sorry to be so long winded, but your advice is right on point. Thank You."_
-   _"I was just back on your website and I have to commend you on everything I've pretty much read so far. You are a true Philosopher. I was a Philosophy major, and computer science minor at Columbia University in NYC, but Columbia's style of lecturing on Philosophy had me convinced that these times are still one where Socrates for all his genius would be considered to be no more than another far out cult leader._
-   _There was no encouraging of philosophic thought on one's own. We simply had to study Hegel, Marx, Nietzsche, Heidegger, Leibniz, Camus etc., and spit out their words on paper with no room at all for one's own innovation. The world needs more people who can exist outside the system one is compelled into from birth._
-   _Currently, I'm a second year law student fighting my own battles with the system. I suppose I was/am out looking for a surefire gambling system so that I can escape the rat race for money, "societal prestige", etc..."_
-   _"I take it your reference is to the inane way some forum members seem to derive an almost twisted pleasure in deriding the views of others. I'm a new light to 'roulette' gambling and searching for something genuine, without hype and most of all integrity._
-   _I visit many web forums in the search for truth but everyone has something to sell, someone to deride or an axe to grind - typical human nature I suppose. I've always been the opposite if I can be positive and moving forward it helps me and others on this insignificant globe spinning in the vast infinity of the universe. Ah! repeating patterns in nature and mathematics - the nautillus shell, complex number theory, fractalization of the universe. Number theory and all its derivatives and the mother of all probability theory._
-   _Your site reference took me to your Web page which was a fascinating journey - I can see you're a scholar. My mathematics background is University level - Engineering and we only ever touched on **[probability theory](https://saliu.com/theory-of-probability.html)** so it's new to me. I love your references to ancient mathematic and quotes - I've always been fascinated in its history and fantastic richness. Ah! the **[golden ratio](https://saliu.com/bbs/messages/958.html)** and other delights._
-   _Now the question burning in my mind are your theories correct ? Or just some lofty idealistic notions of beating the impossible. I fear sometimes that in the history of mathematics no-one seems to have discovered, conjectured yes, but never even come close to a method of beating the wheel 'that spinning phantasm'._
-   _Yes, theories and propositions abound and grow like grass in the meadow but where's the solid real world living and breathing proof of beating the randomness of a 37/38 random number stream. Yes talking of infinity and repeating patterns are superbly fascinating but what about here and now in the present._
-   _I for my part have been involved, as an electronics engineer on various projects involving the generation of **[pseudo-random binary sequences](https://saliu.com/random-numbers.html)** (PRBS) for the testing of military communication and radio equipment. Encryption and coding techniques mainly at bit stream level i.e. generation of Maximum length sequences using Nth degree polynomials all conceived in hardware with a little low level assembly language coding._
-   _Ion - here's the questions - your thinking seems radically different from the masses on the web and would love to see you succeed in your endeavors but how can 'gambler's luck ' be added to the equation. I've noticed that Maximum length binary sequences although creating seemingly random ones and zeros exhibit a wave pattern within a burst. Even an M-sequence stream as short as 2 power 16 shows definite tendencies towards chunking i.e. sequential one zero patterns followed by long patterns of ones or zeros._
-   _Identifying these types of patterns within a number stream generated by a roulette wheel must have some validity. If one could identify and ride the wave wouldn't this point towards some means of giving the player the edge to possibly win. I don't know these are just initial thoughts on the problem - many experienced gambler's must intuitively feel this process as it's said some are very successful._
-   _Sorry to ramble on but I wish you every success in your endeavors - you seem a bit of light in a dark tunnel of human nature."_

-   I answered that, in fact, everything is **random** in the Universe. Pardon me, some things are not easy to understand immediately by everybody. It is NOT a disparaging statement. Traditionally, people think of one and only one element in theory of probability: well, **probability, p**. In truth, I discovered and emphasized that randomness consists of THREE fundamental elements:
    
    -   **Degree of certainty DC** for and event of **probability p** to appear within a **number of trials N**.
    -   The undeniably valid mathematical relation is known as the [Fundamental Formula of Gambling](https://saliu.com/Saliu2.htm) (FFG):
    
    [![Science Software: Statistics, Probability, Odds, Combinatorial Mathematics, Algorithms.](https://saliu.com/ScreenImgs/FFG1.jpg)](https://saliu.com/free-science.html)
    
    -   A second point of misunderstanding is produced by the absurdity of the _**infamous**_ [gambler's fallacy](https://saliu.com/gamblers-fallacy.html): The gambler will lose indefinitely.
    -   In fact, gambling can be defined as _streaks probability_, or _mathematics of streaks_. Only the streaks are backed by mathematical formulas. Every _streak-length_ occurs as calculated by theory of probability: with a _**degree of certainty**_ within a _**number of trials**_ (e.g. BJ hands or roulette spins). See my “feared-by-casinos” [streaks gambling system based on progressions](https://saliu.com/occult-science-gambling.html).
    
    Most gambling or lottery systems are garbage for the reason I explained: The authors lie to themselves knowingly. Other systems only serve as means to destroy real systems. I touched the topic a while ago. It seems that [_**gambling systems are mushrooming**_](https://saliu.com/bbs/messages/749.html) lately. There is a flood of gambling systems all over the Internet. _Freedom of speech_ is always agreed upon on this side — my freedom of expression included. What do them gambling systems have in common? Many of them are casino _schemes_. What better way to discredit and discourage the use of gambling systems than advertising OUTRAGEOUS systems?! Especially persons with financial means are more attracted to more expensive things… like thousands of dollars for a bottle of old wine!
    
    That is, outrageous prices, outrageous claims, absurd mathematical underpinning. Stress those “features” in advertisements. Make the system authors look like outrageous bloodsuckers, in addition to appearing as cuckoos. Do it insistently for a period of time and perhaps 99.99% of all players get disgusted with the word _system_. They might as well play with their guts only, never thinking of bankrolls, let alone systems! No persons in their own minds even remotely consider buying such aberrations. But that's not the goal of system advertising. The only purpose is _killing the very idea of gambling systems_.
    
    _"The foolish man, the silly woman, the infatuated child draw beverage from the swamp, when crystalline water is aplenty in the rock nearby."  
    __(Tladouque, "Steps to Maya Pyramid")._
    
    Still there are some who resort to outrageous aberrations just to contradict me, or to annoy me, even to disgust me! Thus they hope I would fold it up shop, go away, and keep quiet. NOT!  
    _"He who resents the fall of the wall expects the rise of demise."  
    __(Tladouque, "Steps to Maya Pyramid")._
    
    _“A trustworthy man is an axiomatic man; an axiomatic man is a good man. Be axiomatic, Homo Sapiens!”_ – Ion Saliu (royalty name: _Parpaluck_)
    
    ![The best ever, most comprehensive, powerful lotto lottery gambling software was created by Ion.](https://saliu.com/bbs/messages/HLINE.gif)
    
    [
    
    ## <u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>
    
    ](https://saliu.com/content/lottery.html)
    
    -   The Main [_**Lotto, Lottery, Software, Strategies**_](https://saliu.com/LottoWin.htm) Page.  
        Presenting software to create free winning lotto, lottery strategies, systems based on mathematics. Get your lotto systems or wheels, the best lottery, lotto software, combinations, winning numbers.
    -   [_**Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies**_](https://saliu.com/Newsgroups.htm).  
        Read a genuine analysis of Excel spreadsheets applied to lottery and lotto developing of software, systems, and strategies. Combining Excel analysis with powerful lottery and lotto software programmed by this author, _Parpaluck_.
    -   [_**User's Guide to MDIEditor Lotto WE**_](https://saliu.com/MDI-lotto-guide.html).  
        ~ Also applicable to LotWon lottery, lotto software; plus Powerball, Mega Millions, Euromillions.
    -   [_**Book, Tutorial, Manual of Lotto, Lottery Software, Programs**_](https://saliu.com/forum/lotto-book.html).
        
        _**<u>Pages dedicated to help, instructions, filters, strategies for the best lotto programs and lottery software in the world</u>**_
        
    -   [_**Documentation, Help: MDIEditor Lotto WE, Lottery Software, Strategy Tutorial**_](https://saliu.com/mdi_lotto.html).
    -   [_**Filters, Restrictions, Elimination, Reduction in Lotto, Lottery Software**_](https://saliu.com/bbs/messages/42.html).
    -   [_**Step-By-Step Guide to Lotto, Lottery Filters in Software**_](https://saliu.com/bbs/messages/569.html).
    -   [_**Basic Manual for Lotto Software, Lottery Software**_](https://saliu.com/bbs/messages/818.html).
    -   [_Vertical or Positional_ _**Filters in Lottery Software**_](https://saliu.com/bbs/messages/838.html).
    -   [_**Beginner's Basic Steps to**_ _LotWon_ _**Lottery Software, Lotto Software**_](https://saliu.com/bbs/messages/896.html).
    -   [**Skips Lottery, Lotto, Gambling, Systems, Strategy**](https://saliu.com/skip-strategy.html).
    -   [_**Lotto Strategy Based on: Sums (Sum-Totals); Odd Even; Low High Numbers**_](https://saliu.com/strategy.html).
    -   [_**Lottery Utility Software**_](https://saliu.com/lottery-utility.html): Pick-3, 4 Lottery, Lotto-5, 6, Powerball/Mega Millions/Thunderball, Euromillions.
    -   [_**Filtering, Filters in Lottery Software, Lotto Software**_](https://saliu.com/filters.html).
    -   [_**Artificial Intelligence AI, Axiomatic Intelligence AxI, Neural Networks in Lottery: Strategies, Systems, Software**_](https://saliu.com/neural-networking-lottery.html).
    -   [_**A Practical Essay on Artificial Intelligence, AI Chatbots Regarding Ion Saliu**_](https://saliu.com/ai-chatbots-ion-saliu.html).
    -   [_**Pick-3 Lottery Strategy Software, System, Method, Play Pairs Last 100 Draws**_](https://saliu.com/STR30.htm).
    -   _"The Start Is the Hardest Part"_: [_**Play a Lotto Strategy, Lotto Strategies**_](https://forums.saliu.com/lottery-strategies-start.html).
    -   [_**Lotto wheels for lotto games drawing 5, 6, or 7 numbers**_](https://saliu.com/lotto_wheels.html).  
        The most advanced theory of _lotto wheels_ or _reduced lottery systems_. Get also original lotto wheels for lotto games drawing 5, 6, 7 numbers, Powerball, Mega Millions, Euromillions: Balanced, randomized, totally _free_.
    -   Download [**Lotto Software Lottery Software**](https://saliu.com/infodown.html).
    
    ![Visitors and software users judge positively Ion Saliu and his creations in lottery, gambling.](https://saliu.com/bbs/messages/HLINE.gif)
    
    **[Home](https://saliu.com/index.htm) | [Search](https://saliu.com/Search.htm) | [New Writings](https://saliu.com/bbs/index.html) | [Software](https://saliu.com/infodown.html) | [Odds, Generator](https://saliu.com/calculator_generator.html) | [Contents](https://saliu.com/content/index.html) | [Forums](https://forums.saliu.com/) | [Sitemap](https://saliu.com/sitemap/index.html)**
    
    ![One visitor considers Ion Saliu light in a lot of darkness in the world.](https://saliu.com/bbs/messages/HLINE.gif)
