#!/usr/bin/env julia

"""
Demonstration script for Wonder Grid Concurrent Processing System
Shows parallel processing capabilities, thread safety, and performance scaling
"""

using Dates
using Base.Threads

# Include required modules
include("src/wonder_grid_engine.jl")
include("src/backtesting.jl")
include("src/performance_optimization.jl")
include("src/concurrent_processing.jl")

"""
Demonstrate concurrent processing features
"""
function demo_concurrent_processing()
    println("🚀 Wonder Grid Concurrent Processing Demo")
    println("=" ^ 70)
    println("System Information:")
    println("  Available Threads: $(nthreads())")
    println("  Julia Version: $(VERSION)")
    
    if nthreads() == 1
        println("\n⚠️  Note: Running with single thread. For full concurrent demo,")
        println("   start Julia with: julia -t auto or julia -t <number>")
    end
    
    # Demo 1: Thread-safe data structures
    println("\n🔒 Demo 1: Thread-Safe Data Structures")
    println("-" ^ 50)
    
    demo_thread_safe_structures()
    
    # Demo 2: Parallel combination generation
    println("\n⚡ Demo 2: Parallel Combination Generation")
    println("-" ^ 50)
    
    demo_parallel_combination_generation()
    
    # Demo 3: Concurrent backtesting
    println("\n🏃 Demo 3: Concurrent Backtesting Performance")
    println("-" ^ 50)
    
    demo_concurrent_backtesting()
    
    # Demo 4: Performance scaling analysis
    println("\n📈 Demo 4: Performance Scaling Analysis")
    println("-" ^ 50)
    
    demo_scaling_analysis()
    
    # Demo 5: Thread pool management
    println("\n🏊 Demo 5: Thread Pool Management")
    println("-" ^ 50)
    
    demo_thread_pool_management()
    
    # Demo 6: Concurrent vs sequential comparison
    println("\n⚖️  Demo 6: Concurrent vs Sequential Comparison")
    println("-" ^ 50)
    
    demo_concurrent_vs_sequential()
    
    println("\n✅ Concurrent Processing Demo Complete!")
    println("=" ^ 70)
end

"""
Demonstrate thread-safe data structures
"""
function demo_thread_safe_structures()
    println("Demonstrating thread-safe data structures...")
    
    # Thread-safe pairing calculator demo
    println("\nConcurrent Pairing Calculator:")
    
    calc = ConcurrentPairingCalculator()
    
    # Sample combinations to process
    sample_combinations = [
        [1, 7, 15, 23, 39],
        [3, 11, 19, 27, 35],
        [2, 9, 16, 24, 38],
        [5, 12, 20, 28, 33],
        [4, 8, 17, 25, 36],
        [6, 13, 21, 29, 37]
    ]
    
    println("  Processing $(length(sample_combinations)) combinations...")
    
    start_time = time()
    
    if nthreads() > 1
        @threads for combo in sample_combinations
            add_combination_concurrent!(calc, combo)
        end
    else
        for combo in sample_combinations
            add_combination_concurrent!(calc, combo)
        end
    end
    
    processing_time = time() - start_time
    
    println("  ✅ Processed in $(round(processing_time * 1000, digits=2)) ms")
    println("  📊 Total pairs recorded: $(calc.total_pairs[])")
    
    # Test frequency lookups
    println("\nPairing Frequency Lookups:")
    test_pairs = [(1, 7), (3, 11), (5, 12), (1, 7), (3, 11)]  # Some repeated
    
    for (num1, num2) in test_pairs
        freq = get_pairing_frequency_concurrent(calc, num1, num2)
        println("  Pair ($num1, $num2): frequency = $freq")
    end
    
    println("  Cache Performance:")
    println("    Cache Hits: $(calc.cache_hits[])")
    println("    Cache Misses: $(calc.cache_misses[])")
    
    if calc.cache_hits[] + calc.cache_misses[] > 0
        hit_rate = calc.cache_hits[] / (calc.cache_hits[] + calc.cache_misses[]) * 100
        println("    Hit Rate: $(round(hit_rate, digits=1))%")
    end
    
    # Thread-safe FFG calculation demo
    println("\nConcurrent FFG Calculation:")
    
    key_numbers = [7, 13, 21, 29, 31]
    ffg_results = Dict{Int, Vector{Int}}()
    
    start_time = time()
    
    if nthreads() > 1
        @threads for key_number in key_numbers
            ffg_results[key_number] = calculate_ffg_concurrent(key_number)
        end
    else
        for key_number in key_numbers
            ffg_results[key_number] = calculate_ffg_concurrent(key_number)
        end
    end
    
    ffg_time = time() - start_time
    
    println("  ✅ Calculated FFG for $(length(key_numbers)) keys in $(round(ffg_time * 1000, digits=2)) ms")
    
    for (key, ffg) in sort(collect(ffg_results))
        println("  Key $key: $(length(ffg)) numbers [$(join(ffg[1:min(5, length(ffg))], ", "))$(length(ffg) > 5 ? "..." : "")]")
    end
    
    # Show cache statistics
    cache_stats = get_concurrent_cache_stats()
    println("\nCache Statistics:")
    println("  FFG Cache: $(cache_stats["ffg_cache_size"]) entries")
    println("  Pairing Cache: $(cache_stats["pairing_cache_size"]) entries")
    println("  Total Cached: $(cache_stats["total_cached_items"]) items")
end

"""
Demonstrate parallel combination generation
"""
function demo_parallel_combination_generation()
    println("Demonstrating parallel combination generation...")
    
    key_number = 13
    thread_count = min(4, nthreads())
    
    println("  Key Number: $key_number")
    println("  Using Threads: $thread_count")
    
    # Create parallel generator
    generator = ParallelCombinationGenerator(key_number, thread_count)
    
    println("  FFG Numbers: $(length(generator.ffg_numbers)) [$(join(generator.ffg_numbers[1:min(8, length(generator.ffg_numbers))], ", "))$(length(generator.ffg_numbers) > 8 ? "..." : "")]")
    
    # Generate combinations
    start_time = time()
    combinations = generate_combinations_parallel(generator)
    generation_time = time() - start_time
    
    println("  ✅ Generated $(length(combinations)) combinations in $(round(generation_time * 1000, digits=2)) ms")
    
    if !isempty(combinations)
        println("  📊 Generation Rate: $(round(length(combinations) / generation_time, digits=0)) combinations/second")
        
        # Show sample combinations
        println("\nSample Combinations:")
        sample_count = min(5, length(combinations))
        for i in 1:sample_count
            combo = combinations[i]
            println("    $(i): [$(join(combo, ", "))] (sum: $(sum(combo)))")
        end
        
        # Verify combination properties
        println("\nCombination Analysis:")
        sums = [sum(combo) for combo in combinations]
        println("  Sum Range: $(minimum(sums)) - $(maximum(sums))")
        println("  Average Sum: $(round(sum(sums) / length(sums), digits=1))")
        
        # Check for duplicates
        unique_combinations = unique(combinations)
        println("  Unique Combinations: $(length(unique_combinations))/$(length(combinations))")
        
        if length(unique_combinations) == length(combinations)
            println("  ✅ No duplicate combinations found")
        else
            println("  ⚠️  Found $(length(combinations) - length(unique_combinations)) duplicates")
        end
    else
        println("  ⚠️  No combinations generated (insufficient FFG numbers)")
    end
end

"""
Demonstrate concurrent backtesting
"""
function demo_concurrent_backtesting()
    println("Demonstrating concurrent backtesting performance...")
    
    # Generate test data
    key_number = 7
    generator = ParallelCombinationGenerator(key_number)
    combinations = generate_combinations_parallel(generator)
    
    if isempty(combinations)
        println("  ⚠️  No combinations available for backtesting demo")
        return
    end
    
    # Use subset for demo
    test_combinations = combinations[1:min(50, length(combinations))]
    
    # Create test draws
    test_draws = [
        LotteryDraw(Date("2023-01-01"), [1, 7, 14, 21, 28]),
        LotteryDraw(Date("2023-01-02"), [3, 9, 15, 22, 29]),
        LotteryDraw(Date("2023-01-03"), [5, 11, 17, 24, 31]),
        LotteryDraw(Date("2023-01-04"), [2, 8, 16, 23, 30]),
        LotteryDraw(Date("2023-01-05"), [4, 10, 18, 25, 32])
    ]
    
    println("  Test Setup:")
    println("    Combinations: $(length(test_combinations))")
    println("    Test Draws: $(length(test_draws))")
    println("    Total Tests: $(length(test_combinations) * length(test_draws))")
    
    # Sequential backtesting (for comparison)
    println("\nSequential Backtesting:")
    start_time = time()
    
    # Simple sequential implementation
    sequential_hits = Dict("3/5" => 0, "4/5" => 0, "5/5" => 0)
    
    for combination in test_combinations
        combo_set = Set(combination)
        for draw in test_draws
            draw_set = Set(draw.numbers)
            matches = length(intersect(combo_set, draw_set))
            if matches >= 3
                sequential_hits["$(matches)/5"] += 1
            end
        end
    end
    
    sequential_time = time() - start_time
    
    println("  ✅ Sequential completed in $(round(sequential_time * 1000, digits=2)) ms")
    println("  📊 Hits: 3/5=$(sequential_hits["3/5"]), 4/5=$(sequential_hits["4/5"]), 5/5=$(sequential_hits["5/5"])")
    
    # Concurrent backtesting
    println("\nConcurrent Backtesting:")
    
    parallel_engine = ParallelBacktestingEngine(min(4, nthreads()), 25)
    
    start_time = time()
    parallel_result = run_backtest_parallel(parallel_engine, test_combinations, test_draws)
    parallel_time = time() - start_time
    
    println("  ✅ Concurrent completed in $(round(parallel_time * 1000, digits=2)) ms")
    
    # Convert hit rates back to counts for comparison
    total_tests = length(test_combinations) * length(test_draws)
    parallel_hits = Dict(
        "3/5" => round(Int, parallel_result.hit_rates["3/5"] * total_tests),
        "4/5" => round(Int, parallel_result.hit_rates["4/5"] * total_tests),
        "5/5" => round(Int, parallel_result.hit_rates["5/5"] * total_tests)
    )
    
    println("  📊 Hits: 3/5=$(parallel_hits["3/5"]), 4/5=$(parallel_hits["4/5"]), 5/5=$(parallel_hits["5/5"])")
    
    # Performance comparison
    if sequential_time > 0
        speedup = sequential_time / parallel_time
        println("\nPerformance Comparison:")
        println("  Sequential Time: $(round(sequential_time * 1000, digits=2)) ms")
        println("  Concurrent Time: $(round(parallel_time * 1000, digits=2)) ms")
        println("  Speedup: $(round(speedup, digits=2))x")
        
        if speedup > 1.0
            println("  ✅ Concurrent processing is faster")
        else
            println("  ⚠️  Sequential processing is faster (overhead dominates for small dataset)")
        end
    end
    
    # Verify results match
    results_match = all(sequential_hits[tier] == parallel_hits[tier] for tier in ["3/5", "4/5", "5/5"])
    if results_match
        println("  ✅ Results match between sequential and concurrent")
    else
        println("  ⚠️  Results differ between sequential and concurrent")
    end
end

"""
Demonstrate scaling analysis
"""
function demo_scaling_analysis()
    println("Demonstrating performance scaling analysis...")
    
    if nthreads() == 1
        println("  ⚠️  Scaling analysis requires multiple threads")
        println("     Start Julia with: julia -t auto or julia -t <number>")
        return
    end
    
    key_number = 11
    max_threads = min(4, nthreads())  # Limit for demo
    
    println("  Key Number: $key_number")
    println("  Max Threads: $max_threads")
    
    # Create scaling analyzer
    analyzer = ScalingAnalyzer(max_threads)
    
    println("  Thread counts to test: $(join(analyzer.thread_counts, ", "))")
    
    # Run scaling analysis
    println("\nRunning scaling analysis...")
    
    iterations = 3  # Small number for demo
    performance_data = analyze_scaling_performance(analyzer, key_number, iterations)
    
    # Display results
    display_scaling_results(analyzer)
    
    # Additional analysis
    println("\nScaling Efficiency Analysis:")
    
    if length(performance_data) >= 2
        baseline_time = performance_data[1]["average_time"]
        
        for thread_count in sort(collect(keys(performance_data)))
            data = performance_data[thread_count]
            
            theoretical_speedup = thread_count
            actual_speedup = data["speedup"]
            efficiency = actual_speedup / theoretical_speedup * 100
            
            println("  $thread_count threads:")
            println("    Theoretical Speedup: $(theoretical_speedup)x")
            println("    Actual Speedup: $(round(actual_speedup, digits=2))x")
            println("    Parallel Efficiency: $(round(efficiency, digits=1))%")
        end
        
        # Find sweet spot
        best_efficiency = 0.0
        optimal_threads = 1
        
        for (threads, data) in performance_data
            if data["efficiency"] > best_efficiency
                best_efficiency = data["efficiency"]
                optimal_threads = threads
            end
        end
        
        println("\n💡 Optimal Configuration:")
        println("  Best Thread Count: $optimal_threads")
        println("  Peak Efficiency: $(round(best_efficiency * 100, digits=1))%")
    end
end

"""
Demonstrate thread pool management
"""
function demo_thread_pool_management()
    println("Demonstrating thread pool management...")
    
    # Create thread pool
    pool_size = min(3, nthreads())
    manager = ThreadPoolManager(pool_size)
    
    println("  Thread Pool Size: $pool_size")
    println("  Active Tasks: $(manager.active_tasks[])")
    
    # Submit various tasks
    println("\nSubmitting tasks to thread pool...")
    
    task_results = [Atomic{Int}(0) for _ in 1:8]
    task_times = Float64[]
    
    for i in 1:length(task_results)
        task_func = function()
            start_time = time()
            
            # Simulate different types of work
            if i % 3 == 0
                sleep(0.02)  # I/O simulation
            elseif i % 3 == 1
                # CPU work simulation
                sum(j^2 for j in 1:1000)
            else
                sleep(0.01)  # Mixed work
            end
            
            elapsed = time() - start_time
            push!(task_times, elapsed)
            atomic_add!(task_results[i], 1)
        end
        
        success = submit_task!(manager, task_func)
        println("  Task $i submitted: $(success ? "✅" : "❌")")
    end
    
    println("\nWaiting for task completion...")
    start_wait = time()
    
    completion_success = wait_for_completion(manager, 10.0)  # 10 second timeout
    wait_time = time() - start_wait
    
    println("  ✅ All tasks completed in $(round(wait_time * 1000, digits=2)) ms")
    println("  📊 Completion Success: $completion_success")
    
    # Verify all tasks completed
    completed_count = sum(result[] for result in task_results)
    println("  📋 Tasks Completed: $completed_count/$(length(task_results))")
    
    if !isempty(task_times)
        avg_task_time = sum(task_times) / length(task_times)
        println("  ⏱️  Average Task Time: $(round(avg_task_time * 1000, digits=2)) ms")
    end
    
    # Demonstrate pool shutdown
    println("\nShutting down thread pool...")
    shutdown!(manager)
    
    # Try to submit task after shutdown
    post_shutdown_success = submit_task!(manager, () -> nothing)
    println("  Post-shutdown task submission: $(post_shutdown_success ? "❌ Unexpected success" : "✅ Correctly rejected")")
end

"""
Demonstrate concurrent vs sequential comparison
"""
function demo_concurrent_vs_sequential()
    println("Demonstrating concurrent vs sequential performance comparison...")
    
    key_number = 9
    
    # Sequential processing
    println("\nSequential Processing:")
    
    start_time = time()
    
    # Standard engine
    standard_engine = WonderGridEngine()
    sequential_combinations = generate_combinations(standard_engine, key_number)
    
    sequential_time = time() - start_time
    
    println("  ✅ Generated $(length(sequential_combinations)) combinations")
    println("  ⏱️  Time: $(round(sequential_time * 1000, digits=2)) ms")
    
    if !isempty(sequential_combinations)
        println("  📊 Rate: $(round(length(sequential_combinations) / sequential_time, digits=0)) combinations/second")
    end
    
    # Concurrent processing
    println("\nConcurrent Processing:")
    
    start_time = time()
    
    # Concurrent engine
    concurrent_engine = ConcurrentWonderGridEngine(key_number, min(4, nthreads()))
    concurrent_combinations = generate_combinations_concurrent(concurrent_engine, key_number)
    
    concurrent_time = time() - start_time
    
    println("  ✅ Generated $(length(concurrent_combinations)) combinations")
    println("  ⏱️  Time: $(round(concurrent_time * 1000, digits=2)) ms")
    
    if !isempty(concurrent_combinations)
        println("  📊 Rate: $(round(length(concurrent_combinations) / concurrent_time, digits=0)) combinations/second")
    end
    
    # Performance comparison
    println("\nPerformance Comparison:")
    
    if sequential_time > 0 && concurrent_time > 0
        speedup = sequential_time / concurrent_time
        efficiency = speedup / nthreads() * 100
        
        println("  Sequential Time: $(round(sequential_time * 1000, digits=2)) ms")
        println("  Concurrent Time: $(round(concurrent_time * 1000, digits=2)) ms")
        println("  Speedup: $(round(speedup, digits=2))x")
        println("  Parallel Efficiency: $(round(efficiency, digits=1))%")
        
        if speedup > 1.1
            println("  ✅ Significant performance improvement with concurrency")
        elseif speedup > 0.9
            println("  ⚖️  Similar performance (overhead balanced by parallelism)")
        else
            println("  ⚠️  Sequential faster (overhead dominates for this workload)")
        end
    end
    
    # Verify results consistency
    if !isempty(sequential_combinations) && !isempty(concurrent_combinations)
        # Sort both for comparison
        sorted_sequential = sort(sequential_combinations)
        sorted_concurrent = sort(concurrent_combinations)
        
        results_match = sorted_sequential == sorted_concurrent
        println("  Results Match: $(results_match ? "✅ Yes" : "❌ No")")
        
        if !results_match
            println("    Sequential Count: $(length(sequential_combinations))")
            println("    Concurrent Count: $(length(concurrent_combinations))")
        end
    end
    
    # Show performance monitoring data
    perf_stats = get_performance_stats_concurrent(concurrent_engine.performance_monitor)
    
    if haskey(perf_stats, "operations") && !isempty(perf_stats["operations"])
        println("\nConcurrent Engine Performance Monitoring:")
        for (operation, stats) in perf_stats["operations"]
            println("  $operation:")
            println("    Count: $(stats["count"])")
            println("    Average Time: $(round(stats["average_time"] * 1000, digits=2)) ms")
        end
    end
end

"""
Interactive concurrent processing demo
"""
function interactive_concurrent_demo()
    println("\n🎮 Interactive Concurrent Processing Demo")
    println("=" ^ 60)
    
    println("This demo allows you to test concurrent processing with custom parameters.")
    
    print("Enter key number to test (1-39) [13]: ")
    key_input = strip(readline())
    key_number = isempty(key_input) ? 13 : parse(Int, key_input)
    
    if !(1 <= key_number <= 39)
        println("Invalid key number, using default: 13")
        key_number = 13
    end
    
    max_threads_available = nthreads()
    print("Enter number of threads to use (1-$max_threads_available) [$max_threads_available]: ")
    thread_input = strip(readline())
    thread_count = isempty(thread_input) ? max_threads_available : parse(Int, thread_input)
    
    if !(1 <= thread_count <= max_threads_available)
        println("Invalid thread count, using maximum: $max_threads_available")
        thread_count = max_threads_available
    end
    
    println("\n🚀 Running custom concurrent processing test...")
    println("  Key Number: $key_number")
    println("  Thread Count: $thread_count")
    
    # Create concurrent engine
    engine = ConcurrentWonderGridEngine(key_number, thread_count)
    
    # Generate combinations
    start_time = time()
    combinations = generate_combinations_concurrent(engine, key_number)
    generation_time = time() - start_time
    
    println("\n📊 Results:")
    println("  Combinations Generated: $(length(combinations))")
    println("  Generation Time: $(round(generation_time * 1000, digits=2)) ms")
    
    if !isempty(combinations)
        println("  Generation Rate: $(round(length(combinations) / generation_time, digits=0)) combinations/second")
        
        # Show sample combinations
        println("\n🎯 Sample Combinations:")
        sample_count = min(3, length(combinations))
        for i in 1:sample_count
            combo = combinations[i]
            println("    [$(join(combo, ", "))] (sum: $(sum(combo)))")
        end
    end
    
    # Performance monitoring
    perf_stats = get_performance_stats_concurrent(engine.performance_monitor)
    println("\n📈 Performance Statistics:")
    println("  Total Runtime: $(round(perf_stats["total_runtime"], digits=3)) seconds")
    println("  Memory Used: $(round(perf_stats["memory_used_mb"], digits=2)) MB")
    println("  Thread Count: $(perf_stats["thread_count"])")
    
    # Cache statistics
    cache_stats = get_concurrent_cache_stats()
    println("\n💾 Cache Statistics:")
    println("  Total Cached Items: $(cache_stats["total_cached_items"])")
    println("  FFG Cache: $(cache_stats["ffg_cache_size"]) entries")
    println("  Pairing Cache: $(cache_stats["pairing_cache_size"]) entries")
end

# Run the demo
if abspath(PROGRAM_FILE) == @__FILE__
    demo_concurrent_processing()
    interactive_concurrent_demo()
end