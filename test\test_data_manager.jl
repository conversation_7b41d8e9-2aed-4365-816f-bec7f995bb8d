# Test Data Manager
# 測試數據管理器 - 生成和管理各種測試數據

using Dates
using Random

# 引入必要的模組
include("../src/types.jl")
include("test_configuration.jl")

"""
測試數據管理器
負責生成、驗證和管理測試數據
"""
mutable struct TestDataManager
    configuration::TestDataConfiguration
    generated_datasets::Dict{String, Vector{LotteryDraw}}
    real_datasets::Dict{String, Vector{LotteryDraw}}
    edge_case_datasets::Dict{String, Vector{LotteryDraw}}
    validation_results::Dict{String, Bool}
    
    function TestDataManager(config::TestDataConfiguration)
        Random.seed!(config.random_seed)
        new(
            config,
            Dict{String, Vector{LotteryDraw}}(),
            Dict{String, Vector{LotteryDraw}}(),
            Dict{String, Vector{LotteryDraw}}(),
            Dict{String, Bool}()
        )
    end
end

"""
生成標準測試數據
創建指定大小的標準彩票數據
"""
function generate_standard_test_data(manager::TestDataManager, size::Int, dataset_name::String)::Vector{LotteryDraw}
    println("📊 生成標準測試數據: $dataset_name (大小: $size)")
    
    test_draws = LotteryDraw[]
    base_date = Date(2020, 1, 1)
    
    for i in 1:size
        # 生成隨機但合理的彩票號碼
        numbers = sort(sample(1:39, 5, replace=false))
        # 日期應該是最新的在前，所以從最大日期開始遞減
        draw_date = base_date + Day(size - i)
        # draw_id 應該是最新的最大，最舊的最小
        draw_id = size - i + 1

        push!(test_draws, LotteryDraw(numbers, draw_date, draw_id))
    end
    
    # 儲存生成的數據
    manager.generated_datasets[dataset_name] = test_draws
    
    # 驗證數據
    is_valid = validate_lottery_data(test_draws)
    manager.validation_results[dataset_name] = is_valid
    
    if is_valid
        println("  ✅ 數據生成成功並通過驗證")
    else
        println("  ⚠️ 數據生成成功但驗證失敗")
    end
    
    return test_draws
end

"""
生成邊界案例測試數據
創建各種邊界條件的測試數據
"""
function generate_edge_case_data(manager::TestDataManager)::Dict{String, Vector{LotteryDraw}}
    println("🔍 生成邊界案例測試數據...")
    
    edge_cases = Dict{String, Vector{LotteryDraw}}()
    
    # 1. 空數據集
    edge_cases["empty"] = LotteryDraw[]
    
    # 2. 單筆數據
    edge_cases["single"] = [LotteryDraw([1, 2, 3, 4, 5], Date(2022, 1, 1), 1)]
    
    # 3. 重複號碼組合
    duplicate_combo = [1, 2, 3, 4, 5]
    edge_cases["duplicates"] = [
        LotteryDraw(duplicate_combo, Date(2022, 1, 3), 3),
        LotteryDraw([6, 7, 8, 9, 10], Date(2022, 1, 2), 2),
        LotteryDraw(duplicate_combo, Date(2022, 1, 1), 1)
    ]
    
    # 4. 連續號碼
    edge_cases["consecutive"] = [
        LotteryDraw([1, 2, 3, 4, 5], Date(2022, 1, 5), 5),
        LotteryDraw([6, 7, 8, 9, 10], Date(2022, 1, 4), 4),
        LotteryDraw([11, 12, 13, 14, 15], Date(2022, 1, 3), 3),
        LotteryDraw([16, 17, 18, 19, 20], Date(2022, 1, 2), 2),
        LotteryDraw([21, 22, 23, 24, 25], Date(2022, 1, 1), 1)
    ]
    
    # 5. 極值號碼
    edge_cases["extremes"] = [
        LotteryDraw([1, 2, 3, 4, 39], Date(2022, 1, 5), 5),  # 最小和最大
        LotteryDraw([1, 10, 20, 30, 39], Date(2022, 1, 4), 4),  # 分散
        LotteryDraw([35, 36, 37, 38, 39], Date(2022, 1, 3), 3),  # 全部大號
        LotteryDraw([1, 2, 3, 4, 5], Date(2022, 1, 2), 2),    # 全部小號
        LotteryDraw([19, 20, 21, 22, 23], Date(2022, 1, 1), 1)   # 中間號碼
    ]
    
    # 6. 特殊模式
    edge_cases["patterns"] = [
        LotteryDraw([5, 10, 15, 20, 25], Date(2022, 1, 5), 5),  # 等差數列
        LotteryDraw([2, 4, 8, 16, 32], Date(2022, 1, 4), 4),   # 等比數列（部分）
        LotteryDraw([1, 4, 9, 16, 25], Date(2022, 1, 3), 3),   # 平方數
        LotteryDraw([3, 7, 11, 19, 23], Date(2022, 1, 2), 2),  # 質數
        LotteryDraw([6, 12, 18, 24, 30], Date(2022, 1, 1), 1)  # 6的倍數
    ]
    
    # 7. 日期邊界
    edge_cases["date_boundaries"] = [
        LotteryDraw([1, 2, 3, 4, 5], Date(2000, 1, 1), 3),    # 世紀初
        LotteryDraw([6, 7, 8, 9, 10], Date(2099, 12, 31), 2), # 世紀末
        LotteryDraw([11, 12, 13, 14, 15], Date(2024, 2, 29), 1) # 閏年
    ]
    
    # 儲存所有邊界案例
    for (case_name, case_data) in edge_cases
        manager.edge_case_datasets[case_name] = case_data
        manager.validation_results["edge_$case_name"] = validate_lottery_data(case_data)
    end
    
    println("  ✅ 已生成 $(length(edge_cases)) 種邊界案例")
    return edge_cases
end

"""
載入真實數據
從文件載入真實的彩票數據
"""
function load_real_data(manager::TestDataManager, file_path::String, dataset_name::String)::Vector{LotteryDraw}
    if !isfile(file_path)
        @warn "真實數據文件不存在: $file_path"
        return LotteryDraw[]
    end
    
    try
        println("📁 載入真實數據: $dataset_name")
        
        # 這裡實現從文件載入數據的邏輯
        # 目前返回空數據，實際實現時需要根據文件格式解析
        real_data = LotteryDraw[]
        
        # 儲存載入的數據
        manager.real_datasets[dataset_name] = real_data
        manager.validation_results["real_$dataset_name"] = validate_lottery_data(real_data)
        
        println("  ✅ 已載入 $(length(real_data)) 筆真實數據")
        return real_data
        
    catch e
        @error "載入真實數據失敗: $e"
        return LotteryDraw[]
    end
end

"""
驗證彩票數據
檢查數據的有效性和一致性
"""
function validate_lottery_data(data::Vector{LotteryDraw})::Bool
    if isempty(data)
        return true  # 空數據被認為是有效的
    end
    
    for (i, draw) in enumerate(data)
        # 檢查號碼數量
        if length(draw.numbers) != 5
            @warn "第 $i 筆數據號碼數量不正確: $(length(draw.numbers))"
            return false
        end
        
        # 檢查號碼範圍
        if !all(1 <= n <= 39 for n in draw.numbers)
            @warn "第 $i 筆數據號碼超出範圍: $(draw.numbers)"
            return false
        end
        
        # 檢查號碼唯一性
        if length(unique(draw.numbers)) != 5
            @warn "第 $i 筆數據包含重複號碼: $(draw.numbers)"
            return false
        end
        
        # 檢查號碼排序
        if draw.numbers != sort(draw.numbers)
            @warn "第 $i 筆數據號碼未排序: $(draw.numbers)"
            return false
        end
        
        # 檢查開獎 ID
        if draw.draw_id <= 0
            @warn "第 $i 筆數據開獎 ID 無效: $(draw.draw_id)"
            return false
        end
    end
    
    # 檢查日期順序（應該是最新的在前）
    for i in 2:length(data)
        if data[i-1].draw_date < data[i].draw_date
            @warn "數據日期順序不正確: 第 $(i-1) 筆 $(data[i-1].draw_date) 早於第 $i 筆 $(data[i].draw_date)"
            return false
        end
    end
    
    return true
end

"""
獲取指定大小的測試數據
根據配置返回適當的測試數據
"""
function get_test_data(manager::TestDataManager, size_category::String)::Vector{LotteryDraw}
    size = if size_category == "small"
        manager.configuration.small_dataset_size
    elseif size_category == "medium"
        manager.configuration.medium_dataset_size
    elseif size_category == "large"
        manager.configuration.large_dataset_size
    elseif size_category == "stress"
        manager.configuration.stress_dataset_size
    else
        throw(ArgumentError("未知的數據大小類別: $(size_category)"))
    end
    
    dataset_name = "standard_$size_category"
    
    # 如果已經生成過，直接返回
    if haskey(manager.generated_datasets, dataset_name)
        return manager.generated_datasets[dataset_name]
    end
    
    # 否則生成新數據
    return generate_standard_test_data(manager, size, dataset_name)
end

"""
獲取邊界案例數據
"""
function get_edge_case_data(manager::TestDataManager, case_name::String)::Vector{LotteryDraw}
    if isempty(manager.edge_case_datasets)
        generate_edge_case_data(manager)
    end
    
    if haskey(manager.edge_case_datasets, case_name)
        return manager.edge_case_datasets[case_name]
    else
        throw(ArgumentError("未知的邊界案例: $case_name"))
    end
end

"""
獲取所有可用的數據集
"""
function get_available_datasets(manager::TestDataManager)::Dict{String, Int}
    datasets = Dict{String, Int}()
    
    # 標準數據集
    for (name, data) in manager.generated_datasets
        datasets[name] = length(data)
    end
    
    # 真實數據集
    for (name, data) in manager.real_datasets
        datasets["real_$name"] = length(data)
    end
    
    # 邊界案例
    for (name, data) in manager.edge_case_datasets
        datasets["edge_$name"] = length(data)
    end
    
    return datasets
end

"""
清理測試數據
釋放記憶體中的測試數據
"""
function cleanup_test_data!(manager::TestDataManager)
    empty!(manager.generated_datasets)
    empty!(manager.real_datasets)
    empty!(manager.edge_case_datasets)
    empty!(manager.validation_results)
    
    # 強制垃圾回收
    GC.gc()
    
    println("🧹 測試數據已清理")
end

"""
獲取數據統計摘要
"""
function get_data_summary(manager::TestDataManager)::Dict{String, Any}
    total_datasets = length(manager.generated_datasets) + 
                    length(manager.real_datasets) + 
                    length(manager.edge_case_datasets)
    
    total_records = sum(length(data) for data in values(manager.generated_datasets)) +
                   sum(length(data) for data in values(manager.real_datasets)) +
                   sum(length(data) for data in values(manager.edge_case_datasets))
    
    valid_datasets = count(values(manager.validation_results))
    
    return Dict(
        "total_datasets" => total_datasets,
        "total_records" => total_records,
        "valid_datasets" => valid_datasets,
        "validation_rate" => total_datasets > 0 ? valid_datasets / total_datasets : 0.0,
        "generated_datasets" => length(manager.generated_datasets),
        "real_datasets" => length(manager.real_datasets),
        "edge_case_datasets" => length(manager.edge_case_datasets)
    )
end

"""
輔助函數：隨機抽樣
"""
function sample(collection, n::Int; replace::Bool=false)
    if replace
        return [rand(collection) for _ in 1:n]
    else
        shuffled = shuffle(collect(collection))
        return shuffled[1:min(n, length(shuffled))]
    end
end

"""
輔助函數：洗牌
"""
function shuffle(arr)
    result = copy(arr)
    for i in length(result):-1:2
        j = rand(1:i)
        result[i], result[j] = result[j], result[i]
    end
    return result
end

# 導出主要結構和函數
export TestDataManager, generate_standard_test_data, generate_edge_case_data
export load_real_data, validate_lottery_data, get_test_data
export get_edge_case_data, get_available_datasets, cleanup_test_data!
export get_data_summary
