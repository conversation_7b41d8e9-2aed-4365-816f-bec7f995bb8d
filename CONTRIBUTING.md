# 貢獻指南 - Wonder Grid Lottery System

感謝您對 Wonder Grid Lottery System 的興趣！我們歡迎所有形式的貢獻，包括代碼、文檔、測試、問題報告和功能建議。

## 📋 目錄

1. [貢獻方式](#貢獻方式)
2. [開發環境設置](#開發環境設置)
3. [代碼規範](#代碼規範)
4. [提交流程](#提交流程)
5. [測試要求](#測試要求)
6. [文檔貢獻](#文檔貢獻)
7. [問題報告](#問題報告)
8. [功能請求](#功能請求)

---

## 貢獻方式

### 🎯 我們歡迎的貢獻類型

- **🐛 錯誤修復**: 修復系統中的 bug
- **✨ 新功能**: 添加新的分析功能或優化
- **📚 文檔改進**: 改善文檔質量和完整性
- **🧪 測試增強**: 增加測試覆蓋率和測試質量
- **⚡ 性能優化**: 提升系統性能和效率
- **🔧 工具改進**: 改善開發工具和流程
- **🌐 國際化**: 添加多語言支援

### 🏆 貢獻者等級

| 等級 | 要求 | 權限 |
|------|------|------|
| **貢獻者** | 1+ 個被接受的 PR | 提交 PR，參與討論 |
| **活躍貢獻者** | 5+ 個被接受的 PR | 審查其他 PR |
| **核心貢獻者** | 20+ 個被接受的 PR | 直接推送權限 |
| **維護者** | 長期貢獻 + 邀請 | 完整管理權限 |

---

## 開發環境設置

### 前置需求

- **Julia 1.8+** (推薦 1.11+)
- **Git 2.0+**
- **VS Code** (推薦，配合 Julia 擴展)
- **4GB+ RAM** (開發需要)

### 設置步驟

#### 1. Fork 和 Clone

```bash
# 1. Fork 倉庫到您的 GitHub 帳戶
# 2. Clone 您的 fork
git clone https://github.com/YOUR_USERNAME/wonder-grid-lottery-system.git
cd wonder-grid-lottery-system

# 3. 添加上游倉庫
git remote add upstream https://github.com/original-repo/wonder-grid-lottery-system.git
```

#### 2. 開發環境配置

```bash
# 設置開發環境變量
export WONDER_GRID_ENV=development
export JULIA_NUM_THREADS=auto
export WONDER_GRID_LOG_LEVEL=debug

# 創建開發分支
git checkout -b feature/your-feature-name
```

#### 3. Julia 環境設置

```julia
# 啟動 Julia
julia -t auto --project=.

# 安裝開發依賴
julia> ]
pkg> add Test
pkg> add BenchmarkTools
pkg> add Profile
pkg> <Backspace>

# 載入系統
julia> include("src/wonder_grid_system.jl")

# 運行開發測試
julia> include("test/run_dev_tests.jl")
```

#### 4. VS Code 配置

創建 `.vscode/settings.json`：

```json
{
    "julia.executablePath": "/path/to/julia",
    "julia.enableTelemetry": false,
    "julia.symbolCacheDownload": true,
    "julia.enableCrashReporter": false,
    "files.associations": {
        "*.jl": "julia"
    },
    "editor.tabSize": 4,
    "editor.insertSpaces": true,
    "editor.rulers": [92],
    "files.trimTrailingWhitespace": true
}
```

---

## 代碼規範

### 🎨 代碼風格

#### Julia 代碼風格

```julia
# ✅ 好的風格
function calculate_skip_optimized(engine::OptimizedFilterEngine, number::Int)::Int
    # 使用描述性的變量名
    historical_data = engine.historical_data
    
    # 適當的註釋
    for (index, draw) in enumerate(historical_data)
        if number in draw.numbers
            return index - 1  # Skip 值是從 0 開始計算
        end
    end
    
    return length(historical_data)
end

# ❌ 不好的風格
function calc_skip(e,n)
    for (i,d) in enumerate(e.historical_data)
        if n in d.numbers;return i-1;end
    end;return length(e.historical_data)
end
```

#### 命名規範

- **函數名**: 使用 `snake_case`
- **類型名**: 使用 `PascalCase`
- **常數**: 使用 `UPPER_CASE`
- **變量名**: 使用 `snake_case`，要有描述性

```julia
# ✅ 好的命名
struct OptimizedFilterEngine
    historical_data::Vector{LotteryDraw}
    skip_cache::SkipCalculationCache
end

const GLOBAL_MEMORY_POOL = MemoryPool()

function calculate_pairing_frequency_optimized(engine, num1, num2)
    # ...
end

# ❌ 不好的命名
struct OFE
    hd::Vector{LotteryDraw}
    sc::SkipCalculationCache
end

const GMP = MemoryPool()

function calc_pf_opt(e, n1, n2)
    # ...
end
```

### 📝 文檔規範

#### 函數文檔

```julia
"""
    calculate_skip_optimized(engine::OptimizedFilterEngine, number::Int) -> Int

計算指定號碼的 Skip 值（優化版本）。

Skip 值定義為該號碼距離上次出現的期數。如果號碼從未出現過，
則返回歷史數據的總長度。

# 參數
- `engine::OptimizedFilterEngine`: 優化的過濾器引擎實例
- `number::Int`: 要計算 Skip 值的號碼 (1-39)

# 返回值
- `Int`: 該號碼的 Skip 值

# 範例
```julia
engine = OptimizedFilterEngine(historical_data)
skip = calculate_skip_optimized(engine, 1)
println("號碼 1 的 Skip 值: \$skip")
```

# 性能特色
- 使用緊湊數據結構提升性能
- 支援快取以避免重複計算
- 記憶體使用優化

# 另見
- [`calculate_one_filter`](@ref): 標準版本的 Skip 計算
- [`OptimizedFilterEngine`](@ref): 優化引擎的詳細說明
"""
function calculate_skip_optimized(engine::OptimizedFilterEngine, number::Int)::Int
    # 實現...
end
```

#### 類型文檔

```julia
"""
    OptimizedFilterEngine

高性能優化版本的過濾器引擎。

這個引擎整合了多種性能優化技術：
- 緊湊數據結構（節省 92.9% 記憶體）
- 多層快取系統
- 記憶體池管理
- 自動調優

# 字段
- `historical_data`: 歷史開獎數據
- `compact_data`: 緊湊格式的數據（可選）
- `skip_cache`: Skip 計算快取
- `ffg_cache`: FFG 計算快取
- `pairing_cache`: 配對分析快取

# 構造函數
```julia
OptimizedFilterEngine(
    historical_data::Vector{LotteryDraw};
    use_compact_data::Bool = true,
    enable_caching::Bool = true,
    auto_cleanup::Bool = true
)
```

# 範例
```julia
# 創建優化引擎
engine = OptimizedFilterEngine(draws)

# 計算 Skip 值
skip = calculate_skip_optimized(engine, 1)

# 獲取性能統計
stats = get_engine_statistics(engine)
```
"""
mutable struct OptimizedFilterEngine
    # 字段定義...
end
```

### 🧪 測試規範

#### 測試結構

```julia
@testset "OptimizedFilterEngine Tests" begin
    # 設置測試數據
    test_draws = [
        LotteryDraw([1, 5, 10, 15, 20], Date(2023, 1, 1), 1),
        LotteryDraw([2, 6, 11, 16, 21], Date(2023, 1, 2), 2)
    ]
    
    @testset "Construction" begin
        # 測試構造函數
        engine = OptimizedFilterEngine(test_draws)
        @test isa(engine, OptimizedFilterEngine)
        @test length(engine.historical_data) == 2
    end
    
    @testset "Skip Calculation" begin
        engine = OptimizedFilterEngine(test_draws)
        
        # 測試已知結果
        @test calculate_skip_optimized(engine, 1) == 1
        @test calculate_skip_optimized(engine, 2) == 0
        
        # 測試邊界條件
        @test calculate_skip_optimized(engine, 39) == 2  # 從未出現
    end
    
    @testset "Performance" begin
        # 性能測試
        engine = OptimizedFilterEngine(test_draws)
        
        # 測試快取效果
        @time result1 = calculate_skip_optimized(engine, 1)
        @time result2 = calculate_skip_optimized(engine, 1)  # 應該更快
        
        @test result1 == result2
    end
end
```

---

## 提交流程

### 🔄 Git 工作流程

#### 1. 保持同步

```bash
# 獲取最新變更
git fetch upstream
git checkout main
git merge upstream/main

# 更新您的 fork
git push origin main
```

#### 2. 創建功能分支

```bash
# 創建描述性的分支名
git checkout -b feature/add-new-filter
git checkout -b fix/memory-leak-in-cache
git checkout -b docs/improve-api-reference
git checkout -b perf/optimize-skip-calculation
```

#### 3. 提交變更

```bash
# 添加變更
git add .

# 寫好的提交訊息
git commit -m "feat: 添加新的 SIX 過濾器實現

- 實現 SIX 過濾器的核心算法
- 添加相應的測試案例
- 更新 API 文檔
- 性能測試顯示 15% 的改進

Closes #123"
```

#### 4. 推送和創建 PR

```bash
# 推送到您的 fork
git push origin feature/add-new-filter

# 在 GitHub 上創建 Pull Request
```

### 📝 提交訊息規範

使用 [Conventional Commits](https://www.conventionalcommits.org/) 格式：

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

#### 提交類型

- `feat`: 新功能
- `fix`: 錯誤修復
- `docs`: 文檔變更
- `style`: 代碼格式變更
- `refactor`: 重構
- `perf`: 性能改進
- `test`: 測試相關
- `chore`: 構建過程或輔助工具變更

#### 範例

```bash
# 新功能
git commit -m "feat(filter): 添加 SIX 過濾器支援"

# 錯誤修復
git commit -m "fix(cache): 修復記憶體洩漏問題"

# 文檔更新
git commit -m "docs(api): 更新 API 參考文檔"

# 性能改進
git commit -m "perf(skip): 優化 Skip 計算算法

- 使用位操作提升 30% 性能
- 減少記憶體分配
- 添加性能基準測試"
```

---

## 測試要求

### 🧪 測試覆蓋率

- **最低要求**: 80% 代碼覆蓋率
- **推薦目標**: 90%+ 代碼覆蓋率
- **核心功能**: 100% 覆蓋率

### 測試類型

#### 1. 單元測試

```julia
@testset "Skip Calculation Unit Tests" begin
    @test calculate_skip_sequential([...], 1) == expected_value
    @test_throws ArgumentError calculate_skip_sequential([], 1)
end
```

#### 2. 整合測試

```julia
@testset "Engine Integration Tests" begin
    engine = OptimizedFilterEngine(large_dataset)
    result = calculate_skip_optimized(engine, 1)
    @test isa(result, Int)
    @test result >= 0
end
```

#### 3. 性能測試

```julia
@testset "Performance Tests" begin
    engine = OptimizedFilterEngine(large_dataset)
    
    # 基準測試
    @benchmark calculate_skip_optimized($engine, 1)
    
    # 記憶體測試
    @test (@allocated calculate_skip_optimized(engine, 1)) < 1000
end
```

#### 4. 回歸測試

```julia
@testset "Regression Tests" begin
    # 確保修復的 bug 不會再次出現
    @test !has_memory_leak(create_large_engine())
end
```

### 運行測試

```bash
# 運行所有測試
julia test/run_all_tests.jl

# 運行特定測試
julia test/test_filter_engine.jl

# 運行性能測試
julia -t auto test/run_performance_tests.jl

# 生成覆蓋率報告
julia --code-coverage=user test/run_all_tests.jl
```

---

## 文檔貢獻

### 📚 文檔類型

1. **API 文檔**: 函數和類型的詳細說明
2. **用戶指南**: 使用教程和範例
3. **開發者文檔**: 架構和貢獻指南
4. **範例代碼**: 實際使用案例

### 文檔標準

- **清晰性**: 使用簡潔明瞭的語言
- **完整性**: 包含所有必要信息
- **準確性**: 確保代碼範例可以運行
- **一致性**: 遵循統一的格式和風格

---

## 問題報告

### 🐛 報告 Bug

使用 [Bug 報告模板](https://github.com/your-repo/wonder-grid-lottery-system/issues/new?template=bug_report.md)：

```markdown
**Bug 描述**
簡潔清楚地描述 bug。

**重現步驟**
1. 執行 '...'
2. 點擊 '....'
3. 滾動到 '....'
4. 看到錯誤

**預期行為**
描述您預期會發生什麼。

**實際行為**
描述實際發生了什麼。

**環境信息**
- OS: [e.g. Windows 11]
- Julia 版本: [e.g. 1.11.0]
- 系統版本: [e.g. v1.2.3]

**額外信息**
添加任何其他相關信息。
```

---

## 功能請求

### ✨ 請求新功能

使用 [功能請求模板](https://github.com/your-repo/wonder-grid-lottery-system/issues/new?template=feature_request.md)：

```markdown
**功能描述**
清楚簡潔地描述您想要的功能。

**問題描述**
描述這個功能要解決什麼問題。

**建議解決方案**
描述您希望如何實現這個功能。

**替代方案**
描述您考慮過的其他解決方案。

**額外信息**
添加任何其他相關信息或截圖。
```

---

## 🎉 感謝貢獻！

每一個貢獻都讓 Wonder Grid Lottery System 變得更好。無論是代碼、文檔、測試還是問題報告，我們都非常感謝您的參與！

### 🏆 貢獻者認可

- 所有貢獻者都會在 README 中被提及
- 重大貢獻者會獲得特殊徽章
- 核心貢獻者會被邀請加入維護團隊

### 📞 需要幫助？

- 💬 [GitHub Discussions](https://github.com/your-repo/wonder-grid-lottery-system/discussions)
- 📧 Email: <EMAIL>
- 📖 [開發者文檔](doc/developer_guide.md)

---

**再次感謝您對 Wonder Grid Lottery System 的貢獻！** 🎯
