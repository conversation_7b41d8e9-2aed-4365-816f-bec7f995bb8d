---
created: 2025-07-24T21:26:40 (UTC +08:00)
tags: [Markov Chains,lottery,lotto,mathematics,software,formula,probability,random,algorithm,number,followers,pairs,]
source: https://saliu.com/markov-chains-lottery.html
author: 
---

# Markov Chains, Followers, Pairs, Lottery, Lotto, Software

> ## Excerpt
> Markov Chains in lottery, lotto software are based on number followers and also lottery pairs discovered by founder of lotto mathematics, <PERSON>.

---
![Read an introduction to Markov Chains mathematics, randomness, random events, prediction.](https://saliu.com/HLINE.gif)

### I. [Introduction to _Markov Chains_, Applications to Random Events, Lottery](https://saliu.com/markov-chains-lottery.html#MarkovLottery)  
II. [<PERSON>'s Software for _Markov Chains_ in Lottery, Lotto, Horse Racing](https://saliu.com/markov-chains-lottery.html#Software)  
III. [Reports in _Markov Chains_ Software](https://saliu.com/markov-chains-lottery.html#Reports)  
IV. [Generate Combinations in _Markov Chains_ Software](https://saliu.com/markov-chains-lottery.html#Combinations)  
V. [Strategies for _Markov Chains_ in Lottery, Lotto, Horse Racing](https://saliu.com/markov-chains-lottery.html#Strategies)  
VI. [Resources in Lottery Lotto Software, Strategies, Systems](https://saliu.com/markov-chains-lottery.html#Resources)

![Access the best resources in Markov chain applications to lottery, lotto, random events.](https://saliu.com/HLINE.gif)

## <u>1. Introduction to <i>Markov Chains</i>, Applications to Random Events, Lottery</u>

First captured by the _WayBack Machine_ (_web.archive.org_) on July 26, 2015.

Axiomatic one, the _Markov Chains theory_ is very simple and also very hard to comprehend. Why? Because it is many hats to many people. The common ground of this simple example of interaction is **randomness**. Just about everybody states the Markov Chains only apply to random events. Nice and dandy — but what is **not** random? [_**The Universe itself is random beyond mathematical doubt.**_](https://saliu.com/bbs/messages/683.html) Still a majority of people will say right away that the lottery is random, while the Newtonian system is not. Yet, the _Markov Chains_ do not apply to lottery, because the next drawing is not at all influenced by the previous drawing. Meanwhile, the Newtonian system is closer to being a _Markov Chain_ application because it is far easier to predict the next state!

One much given example of a _Markov Chain_ application is weather forecasting. The weather is "less random" than the lottery. The tomorrow forecast is dependent on today's weather, _without_ the need to go back further in time (the day before yesterday, three days ago, etc.) If the weather state today is, randomly: 40% sunny, 30% cloudy, 30% rainy — tomorrow will be just about the same, but **not exactly the same**.

I won't go any further and confuse you deeper because the interpretation of the _Markov Chains_ thingy is a huge mess! Fuzzy mathematics is the queen of confusion. As you can discover at this website, my theory of everything is founded on the **ubiquitous randomness**. Everything, the entire Universe, is random and randomness has **rules**. The phenomena are differentiated by different probabilities, which in turn determine different **degrees of randomness**. A lower probability leads to a higher degree of randomness. The Sun appears as a perfect circle from the Earth. The next state (point) in a circle is predictable with a very high degree of certainty (some say absolute certainty). In reality, the Sun is far from being a perfect circle — there are random variations in Sun's shape that are much larger than the Earth! No [_**perfect shape**_](https://saliu.com/bbs/messages/636.html) has ever been discovered on Earth or in the Universe.

Furthermore, my theory states that everything is correlated — all events are **randomly correlated**, if you will. I discovered the third fundamental element of the [_**Fundamental Formula of Gambling: Degree of Certainty**_](https://saliu.com/Saliu2.htm). Various degrees of certainty are correlated with various degrees of randomness. Yes, it is more difficult to predict the next lotto draw than to predict tomorrow's weather. If it is hot and sunny and dry today, chances are it won't snow tomorrow. But I can be wrong weather-wise and right lottery-wise! There is no degree of certainty equal to 100% AND no degree of certainty equal to 0 — that's **undeniable mathematics**, it is **axiomaticism**.

With the help of computers, I have discovered possibly hundreds of parameters that prove all events are correlated, lottery included. In fact, the first application of my probability theory was lottery and gambling. Hence, I labeled that universal mathematical relation _**Fundamental Formula of Gambling (FFG)**_. The most descriptive name is _**Fundamental Formula of Everything (FFE)**_. Everything happens with a degree of certainty determined by a probability and a number of trials.

To make it brief, I noticed that the lotto numbers repeat more often after a number of trials (lottery drawings) equal to or less than the _FFG median_ (one of my fundamental concepts). I discovered also that the lottery numbers have tendencies to pair differently with other numbers. The most frequent lotto pairings will hit more often within certain ranges of drawings. I called the pairing discovery the _lotto wonder grid_ and it is applied by thousands of lottery players worldwide. If applied to lottery, the Markov Chains theory represents a subset of my _lotto wonder grid_.

The first software implementation of Markov Chains in lottery was first presented at this website in 2003: [_**Markov Chains, Lottery, Lotto, Software, Algorithms, Program**_](https://saliu.com/Markov_Chains.html). The first _Markov Chains Lotto_ program was the creation of **Cristiano Lopes**, a system engineer from Portugal/Italy. He was inspired by a college programming book that "predicted" a set of sentences from a previous set of sentences. A word was followed by other words. The algorithm would randomly select from a list of **followers**. Cristiano Lopes made the algorithm select lotto numbers from a list of numbers that followed the respective number. For example, in the lotto drawings _3 **22** 24 26 28 49_ and _5 **22** 23 24 36 37_, number **22** was followed by the numbers _24_ and _23_. _24_ and _23_ represent **followers** of lotto number **22**.

My _lotto wonder grid_ is more encompassing as it pairs the numbers that appeared in the same draw. In this example, it appears that numbers _22_ and _24_ are _well paired_. The _lotto wonder grid_ will make a list of all pairs for each lotto number; the pairs are displayed in descending order, from the most frequent to the least frequent. Then, the software will generate combinations only from the _top N_ pairings. The _Markov Chains Lotto_ program generates random combinations from the followers of the respective numbers.

![Ion Saliu's improved Markov-chains software is based on pairs of lotto numbers picked randomly.](https://saliu.com/HLINE.gif)

## <u>2. Ion Saliu's Software for <i>Markov Chains</i> in Lottery, Lotto, Horse Racing</u>

-   The ultimate lotto,lottery, horse racing software for applying _Markov Chains_, _pairs_, _number frequency_:
    
    -   **MarkovPick3** ~ for pick-3 daily games;
    -   **MarkovPick4** ~ for pick-4 lotteries;
    -   **MarkovLotto5** ~ for 5-number lotto games;
    -   **MarkovLotto6** ~ for 6-number lottos;
    -   **MarkovHorses3** ~ for horse racing trifectas.
    -   Please be advised that these programs, released in 2015, require a special form of _membership to download software_: _**Ultimate Lottery Software**_. Click on the top banner to learn the terms, conditions, and availability. All my software is always announced in the **Forums**, **New Writings**, **Software Downloads**. The presentation pages are always published first. Such Web resources are free to read and help the reader make a decision regarding the licensing of software.
    -   The _Markov Chains_ program runs from the main menu of the respective _**Ultimate Software**_ package, function _M = Markov Chains, Pairs, Followers_.
    
    ![The ultimate lottery software has Markov Chains specialized programs.](https://saliu.com/images/ultimate-lotto-software-60.gif)
    
-   The following screenshot is the main menu of all programs —
    
    ![The Markov chains lotto software is based on followers and lottery pairings as well.](https://saliu.com/images/markov-pick-lottery.gif)
    
    ## <u>3. Reports in <i>Markov Chains</i> Software</u>
    
-   The function _R = Report Pairs, Frequency (Hot)_ is mandatory to run first. If not, the software triggers an error.
    
    Here is a screenshot of _R = Report Pairs, Frequency (Hot)_.
    
    ![Markov chains, followers, pairings apply also to pick-3, pick 4 daily lottery games.](https://saliu.com/images/markov-report.gif)
    
    Here is a fragment of the main report, **MarkovLotto6.REP**:
    
    ![All lotto numbers are listed in Markov chains as followers and pairings.](https://saliu.com/images/markov-chains-lotto.gif)
    
    In addition, _Reporting_ creates _automatically_ 4 files that are needed by the rest of the functions (combinations generators). These text files are as exemplified for the 6-number lotto.
    
    -   **MarkovNumbersL6** = all N numbers sorted from _Hot_ to _Cold_;
        
        \* 22 44 31 37 38 5 46 24 39 42 43 19 23 47 29 14 33 36 2 20 21 4 1 6 25 28 49 18 32 8 34 35 9 10 11 13 41 3 15 26 16 17 30 40 45 7 12 48 27
        
    -   **MarkovPairsPivL6** = all Pairs sorted from _Hot_ to _Cold_ on N rows - with _PIVOT_;
        
        \* 1 43 38 3 27 6 42 8 14 19 22 24 5 30 34 35 2 4 10 44 47 29 41 20 31 26 46 15 49 36 37 13 39 40 11 21 12 32 45 33 16 17 25 18 7 28 23 48 9  
        \* 2 4 39 7 12 18 38 6 14 11 24 28 32 35 37 3 13 27 1 30 31 15 33 9 36 19 10 26 40 45 47 49 22 29 42 8 20 21 5 41 23 44 16 46 25 34 43 17 48  
        \* 3 4 8 1 14 15 38 41 28 30 2 39 12 45 19 34 22 24 26 7 47 23 6 40 32 42 43 33 16 20 31 25 17 11 44 36 29 48 13 18 10 5 27 35 21 46 37 9 49  
        \* 4 10 21 2 14 16 3 29 38 43 39 20 49 36 18 27 41 15 33 32 40 1 7 45 47 8 35 24 37 26 11 28 5 42 30 31 17 48 22 25 34 6 19 9 23 46 13 12 44  
        ...  
        \* 46 44 33 39 9 6 34 17 20 14 38 23 43 5 47 26 29 31 13 7 36 8 1 40 42 11 24 25 35 19 15 27 21 41 22 32 16 45 2 48 28 4 30 37 12 3 10 49 18  
        \* 47 33 39 38 24 25 14 15 20 40 43 6 36 27 44 45 7 13 2 37 3 16 17 42 4 5 1 46 22 41 34 9 18 11 30 19 10 28 21 31 32 8 23 35 12 48 49 29 26  
        \* 48 16 20 38 12 13 5 7 29 33 10 45 23 24 43 21 8 34 26 39 42 28 44 4 32 22 14 35 37 3 19 41 6 9 30 31 46 47 27 18 1 36 15 25 11 49 40 2 17  
        \* 49 4 8 10 11 13 24 28 2 22 35 38 45 21 5 1 25 26 27 9 30 31 32 33 16 18 40 42 20 36 12 39 7 19 14 47 37 15 23 3 41 34 43 44 29 46 6 48 17  
        
    -   **MarkovPairsNoPL6** = all Pairs sorted from _Hot_ to _Cold_ on N rows - NO _PIVOT_
        
        \* 43 38 3 27 6 42 8 14 19 22 24 5 30 34 35 2 4 10 44 47 29 41 20 31 26 46 15 49 36 37 13 39 40 11 21 12 32 45 33 16 17 25 18 7 28 23 48 9  
        \* 4 39 7 12 18 38 6 14 11 24 28 32 35 37 3 13 27 1 30 31 15 33 9 36 19 10 26 40 45 47 49 22 29 42 8 20 21 5 41 23 44 16 46 25 34 43 17 48  
        \* 4 8 1 14 15 38 41 28 30 2 39 12 45 19 34 22 24 26 7 47 23 6 40 32 42 43 33 16 20 31 25 17 11 44 36 29 48 13 18 10 5 27 35 21 46 37 9 49  
        \* 10 21 2 14 16 3 29 38 43 39 20 49 36 18 27 41 15 33 32 40 1 7 45 47 8 35 24 37 26 11 28 5 42 30 31 17 48 22 25 34 6 19 9 23 46 13 12 44  
        ...  
        \* 44 33 39 9 6 34 17 20 14 38 23 43 5 47 26 29 31 13 7 36 8 1 40 42 11 24 25 35 19 15 27 21 41 22 32 16 45 2 48 28 4 30 37 12 3 10 49 18  
        \* 33 39 38 24 25 14 15 20 40 43 6 36 27 44 45 7 13 2 37 3 16 17 42 4 5 1 46 22 41 34 9 18 11 30 19 10 28 21 31 32 8 23 35 12 48 49 29 26  
        \* 16 20 38 12 13 5 7 29 33 10 45 23 24 43 21 8 34 26 39 42 28 44 4 32 22 14 35 37 3 19 41 6 9 30 31 46 47 27 18 1 36 15 25 11 49 40 2 17  
        \* 4 8 10 11 13 24 28 2 22 35 38 45 21 5 1 25 26 27 9 30 31 32 33 16 18 40 42 20 36 12 39 7 19 14 47 37 15 23 3 41 34 43 44 29 46 6 48 17  
        
    -   **MarkovFollowersL6** = Markov Chains _Followers_ for all N lotto numbers - NO _PIVOT_.
        
        ```
        <span size="5" face="Courier New" color="#c5b358">  2  3  4  5  6 10 17 22
          7  4  5  3 16 21 22
         11 15 18 22
          8 11 10 12 15 18
        -1
        ...
         47  4  5 13 23  1
          4  6 11 15
        -1
          1  3  8  9
        </span>
        ```
        
    -   **MarkovLikePairsL6** = _Followers-like Pairs_ for all N numbers - NO _PIVOT_.
        
        ```
        <span size="5" face="Courier New" color="#c5b358"> 15 30 11  1 16  2 32 47
          1 11 18 42 44
         12 13 28 31 34
         11 21 26 32 46
        -1
        ...
         26 11 20 21  7 32 33 35 40
          2  3 15 30 32
        -1
          4 11 17 33 41
         21 23 18  2  4 28 32 42
        </span>
        ```
        
        -   The first version of this software triggered an error if the user chose a very low range of analysis. It happened that a few lotto numbers might not have had _followers_. The user would see _empty lines_: Numbers did not come out, therefore had no _followers_. You would have to run the _Report_ function again with higher (longer) _parpalucks_.
        -   _**Important changes to the**_ **Markov Chain** _**programs**_, November 2015: Both _Followers_ and _Follower-like Pairings_ can have **null** lines; i.e. no _Followers_ or no _Pairings_.
        -   A null line is represented by _\-1_ (_minus 1_).
        -   Also, _Followers_ and _Follower-like Pairings_ are generated by the same methodology.
        -   The _Followers_ are still calculated by the longer analysis range for pairings (_ParpaluckP_)
        -   The _Follower-like Pairings_ are generated by the shorter analysis range for number frequency (_ParpaluckF_).
        -   This _Markov Chains_ methodology shows an **improvement** in performance based on analyses of past drawings in several lottery games. Plus, there are no more errors caused by empty lines (null values).
    
    The program offers defaults for the _ranges of analysis_ or _parpalucks_. You can also choose your own parameters, of course. You might want to try **lower** parpaluck values than the defaults. Go back in your data file several draws and check how your strategies would fare.
    
    ![The special Markov software offers defaults for lotto draws analyses.](https://saliu.com/images/markov-draws-analysis.gif)
    
    ## <u>4. Generate Combinations in <i>Markov Chains</i> Software</u>
    
    There are 5 combination generating methods in this type of software: Functions _H, P, N, M, C_ in the main menu.
    
-   The function _H = Combinations of Hot Numbers_ generates combinations from _Hot-to-Cold_ numbers from the file created automatically by _Reporting_. The user chooses the number ranks to generate combinations from. You can see in the main report that a lotto drawing consists of the top half of hot numbers or the top half of cold numbers. Therefore, generating combinations from _top hot only_ or _top cold only_ makes a good candidate for the _**LIE elimination**_ feature of **LotWon** lottery software.
    
    Since the combinations will miss 1 or 2 or even 3 of winning lotto numbers in a real drawing, we can apply the _LieID5_, or _LieID4_, or even _LieID3_ filters. This statement is especially true for the very next lottery drawing, and most likely the next 5-6 draws.
    
    ![Markov chains generates lottery combinations from hot and cold numbers.](https://saliu.com/images/markov-hot-numbers.gif)
    
-   The function _P = Generate Combinations from Pairings with PIVOT_. The report file _MarkovPairsPivL6_ shows all lotto numbers and their pairings from HOT to COLD. You can choose a RANGE of ranks in the frequency matrix to generate combinations from; e.g. for the hottest 10 numbers AND the hottest 10 pairs. Each lotto number is _pivot_: it appears in _all_ combinations generated. The lotto combinations generated by this function also make good candidates for the _**LIE elimination**_ feature of **LotWon** lottery software.
    
    ![Generate Markov chains combinations from lotto pairs with a pivot number.](https://saliu.com/images/markov-pairs-pivot.gif)
    
-   The function _N = Generate Combinations from HOT-to-COLD Pairings_. The file _MarkovPairsNoPL6_ shows all lotto numbers and their pairings from _hot_ to _cold_. You can choose a range of ranks in the frequency matrix to generate combinations from; e.g. for the hottest 10 numbers AND the hottest 10 pairs. The lottery combinations generated by this function also make good candidates for the _**LIE elimination**_ feature of **LotWon** lotto software.
    
    ![Generate Markov chains combinations from lotto pairs without pivot numbers.](https://saliu.com/images/markov-pairs-pivotless.gif)
    
-   The function _M = Generate Combinations from Pairings as Markov Chains_. The combinations generated are Markov-style, but _not_ entirely. Instead of randomly picking numbers from a list of _Followers_, the numbers are randomly picked from the _Followers-like Pairs_ file, _MarkovLikePairsL6_. The lotto combinations generated by this function also make good candidates for the _**LIE elimination**_ feature of **LotWon** lottery software.
    
    ![Instead of Markov chains number follower, use lottery pairs to generate combinations.](https://saliu.com/images/markov-chains-pairs.gif)
    
-   The function _C = Generate Combinations as Traditional Markov Chains_. The combinations follow the _traditional_ Markov-Chains-generating. The function randomly picks numbers from the followers list (_MarkovFollowersL6_). The lottery combinations generated by this function also make good candidates for the _**LIE elimination**_ feature of **LotWon** lotto software. This statement is especially true for the very next drawing, and most likely the next 5-6 lotto draws, as no significant lottery prizes are expected.
-   Both Markov-Chains-style generators strip the duplicates: The users gets only unique lottery combinations. For example, I generate _traditional_ Markov Chains combinations for my pick 3 lottery game. I choose to generate 1000 random pick-3 sets based on the last (most recent) lottery drawing. The final output is around 90 unique combinations.
    
    ![The traditional Markov chains software generates random combinations from number followers.](https://saliu.com/images/markov-chains-followers.gif)
    
    ## <u>5. Strategies for <i>Markov Chains</i> in Lottery, Lotto, Horse Racing</u>
    
    **A**. You've read in the previous section about one type of lottery strategies created by this type of software: _**LIE elimination**_.
    
    **B**. The other type is _**straight lottery strategy**_. That is, generate lottery combinations to win **directly**, instead of generating and then lie-eliminating.
    
    -   The player generates combinations and plays them for a range of future drawings known as the _cycle of fruition_. The software user tests first how various functions of combination generating fared in past lottery drawings. You open your real data file (actual lottery drawings) and delete, say, the top 100 draws. _Save As_ the file (e.g. _Data-6.2_). Be careful NOT to _Save_ the file (you'd lose 100 real drawings!) Create a _D6.2_ file with _Data-6.2_ at the top. Generate combosnations and save them to an output file.
    -   In _**Super Utilities**_, check for winners in the output file against the original data file (e.g. _Data-6_). Check (the top) 100 lottery draws in the data file and all the combinations in the output file. See how many drawings elapsed from draw #100 to the first major win (e.g. _4 of 6_ or _5 of 6_, mare rarely a _6 of 6_). Those values represent _cycles of fruition_.
    -   The _cycles of fruition_ can be a valuable indicator of establishing a **pivot**: the draw in the data file when to start the strategy (generating combinations). For example, if you see draw #80 as the first major hit, the fruition cycle would be 20 (100 – 80). We apply a safety margin and establish the pivot 15 draws back. Thus, we delete the top 15 drawings in the lottery data file. _Save As_ and recreate the _D6.2_ file, then generate combinations to play. Read more on where to establish a pivot for a particular strategy: [_**First Lottery Systems, Gambling Systems on Skips, Software**_](https://forums.saliu.com/lottery-gambling-skips-systems.html).
    
    ![Generate thousands of random lotto combinations from Markov chains follower list.](https://saliu.com/HLINE.gif)
    
    ## [<u>6. Resources in Lotto Software, Strategies, Lottery Systems</u>](https://saliu.com/content/lottery.html)
    
    See a comprehensive directory of the pages and materials on the subject of lottery, software, systems, lotto wheels.
    
    -   The Main [_**Lotto, Lottery, Software, Strategy**_](https://saliu.com/LottoWin.htm) Page.  
        Presenting software to create free winning lotto, lottery strategies, systems based on mathematics. Get your lotto systems or wheels, the best lottery, lotto software, combinations, winning numbers.
    -   [_**Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies**_](https://saliu.com/Newsgroups.htm).  
        Read a genuine analysis of Excel spreadsheets applied to lottery and lotto developing of software, systems, and strategies. Combining Excel analysis with powerful lottery and lotto software programmed by this author, _Parpaluck_.
    -   [_**<u>MDIEditor Lotto WE</u>: Lottery Software Manual, Book, ebook, Help**_](https://saliu.com/MDI-lotto-guide.html).  
        ~ Also applicable to my entire lottery software; plus Powerball, Mega Millions, Euromillions.
    -   [_**Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps, Programs**_](https://saliu.com/forum/lotto-book.html).
    -   [**Lottery Mathematics, Lotto Mathematics, Math, Maths**](https://saliu.com/gambling-lottery-lotto/lottery-math.htm): Probability, Appearance, Repeat, Affinity, Number Affiliation, Lotto Wheels, Lottery Systems, Strategies.
    -   Practical [_**Lottery and Lotto Filtering in Software**_](https://saliu.com/filters.html).
    -   [_**Lottery <u>Skip</u> Systems, Software**_](https://saliu.com/skip-strategy.html): _Lotto, Powerball, Mega Millions, Euromillions._
    -   [_**Lotto, Lottery <u>Strategy in Reverse</u>: Not-to-Win Leads to Not-to-Lose or WIN**_](https://saliu.com/reverse-strategy.html).
    -   [_**Lotto, Lottery Strategy on Sums, Root Sums, Skips, Odd Even, Low High, Deltas**_](https://saliu.com/strategy.html).
    -   [_**Lottery Utility Software**_](https://saliu.com/lottery-utility.html)_**: Pick-3, 4 Lottery, Lotto-5, 6, Powerball, Mega Millions, Euromillions**_.
    -   [**Theory, Analysis of _Deltas_ in Lotto, Lottery Software, Strategy, Systems**](https://saliu.com/delta-lotto-software.html).
    -   [_**Lotto Decades, Last Digits, Systems, Strategies, Software**_](https://saliu.com/decades.html).
    -   [_**LIE Elimination: Lottery, Lotto Strategy in Reverse for Pairs, Number Frequency**_](https://saliu.com/lie-lottery-strategies-pairs.html).
    -   [_**Reversed Lottery Strategy for Lotto Decades, Last Digits, Odd Even, Low High, Skips**_](https://saliu.com/lie-lotto-strategies-decades.html).
    -   [_**Lotto Software for Groups of Numbers: Odd, Even, Low, High, Sums, Frequencies, User's Groups**_](https://saliu.com/lotto-groups.html).
    -   [_**The Best Strategy for Lottery, Gambling, Sports Betting, Horse Racing, Blackjack, Roulette**_](https://saliu.com/strategy-gambling-lottery.html).
    -   _"The Start Is the Hardest Part"_ in [_**Lottery Strategies**_](https://forums.saliu.com/lottery-strategies-start.html).
    -   [_**Strategy Lotto Software on Positional Frequency**_](https://forums.saliu.com/lotto-software-position-frequency.html).
    -   Download [**Lottery Software, Lotto Software**](https://saliu.com/infodown.html).
    
    ![See here the first application of Markov Chains mathematics to lottery software, lotto games.](https://saliu.com/HLINE.gif)
    
    **[Home](https://saliu.com/index.htm) | [Search](https://saliu.com/Search.htm) | [New Writings](https://saliu.com/bbs/index.html) | [Fundamental Formula](https://saliu.com/formula.htm) | [Odds, Generator](https://saliu.com/calculator_generator.html) | [Contents](https://saliu.com/content/index.html) | [Forums](https://forums.saliu.com/) | [Sitemap](https://saliu.com/sitemap/index.html)**
    
    ![Read the most thorough article on mathematics of Markov chains in lottery, algorithms, programs.](https://saliu.com/HLINE.gif)
