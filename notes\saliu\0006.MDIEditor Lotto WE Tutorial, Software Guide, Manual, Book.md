---
created: 2024-12-29T21:20:03 (UTC +08:00)
tags: [lotto,lottery,software,tutorial,user,guide,program,statistics,reports,strategy,apps,winning,numbers,drawings,results,combinations,manual,book,eBook,]
source: https://saliu.com/MDI-lotto-guide.html
author: 
---

# MDIEditor Lotto WE: Tutorial, Software Guide, Manual, Book

> ## Excerpt
> MDIEditor Lotto WE is the most comprehensive software for lottery and horse racing. Read, study tutorial, manual, user guide, instructions, program eBook.

---
![Study the mathematical foundation of lotto, lottery theory and software programming.](https://saliu.com/HLINE.gif)

### I. [Notes on Current Version of MDIEditor Lotto WE](https://saliu.com/MDI-lotto-guide.html#Version)  
II. [Introduction to MDIEditor Lotto Software](https://saliu.com/MDI-lotto-guide.html#Guide)  
III. [The Mathematical Foundation of Lotto, Lottery Theory and Software](https://saliu.com/MDI-lotto-guide.html#Software)  
IV. [The Installation of MDIEditor And Lotto WE](https://saliu.com/MDI-lotto-guide.html#Lotto)  
V. [Basic Overview of MDIEditor Lotto WE](https://saliu.com/MDI-lotto-guide.html#MDIEditor)  
5.1. Random Combination Generating  
5.2. Statistical Analyzing and Reporting  
5.3. Optimized Combination Generating  
5.4. Checking Winning Combinations  
5.5. Checking Strategies  
VI. [Create and Update Data Files: Drawings, Past Results, Past Winning Numbers](https://saliu.com/MDI-lotto-guide.html#Results)  
VII. [Generate Statistical Reports: Frequency, Skips, Filters](https://saliu.com/MDI-lotto-guide.html#Statistics)  
VIII. [Lotto, Lottery Strategies: Setting the Filters](https://saliu.com/MDI-lotto-guide.html#Strategy)  
IX. [Play a Lotto, Lottery Strategy: Generating Optimized Combinations](https://saliu.com/MDI-lotto-guide.html#Combinations)  
X. [Expandability and Interoperability: _Purge_ Previous Output Files](https://saliu.com/MDI-lotto-guide.html#Purge)  
XI. [_How About Them Lottery Filters?_](https://saliu.com/MDI-lotto-guide.html#Filters)  
XII. [Resources in Lotto, Lottery, Programming, Software](https://saliu.com/MDI-lotto-guide.html#Lottery)

![Start with the introduction to MDIEditor and Lotto WE.](https://saliu.com/HLINE.gif)

## <u>1. Notes on the Current Version of <span color="#ff0000"><i>MDIEditor Lotto</i></span></u>

### Current version: _4.0\_WE_ ~ Final ~ October 2006.

[![Shop best software online: Lottery, Powerball, Mega Millions, gambling blackjack, roulette, sports.](https://saliu.com/ScreenImgs/mdi-lotto-software.gif)](https://saliu.com/membership.html)

Many thanks to every axiomatic user of this application for their major contribution to discovering all sorts of bugs and quirks. This must be now one of those rare pieces of software that is bug-free. NO more updates or upgrades will be made to **MDIEditor And Lotto WE** — ever!

This application certainly deserves to be considered _**the best, the most powerful, the most comprehensive piece of software for lottery, lotto, horse racing — ever**_. It handles pick (digit) lotteries, 5 6 7-number lotto games, Powerball, Mega Millions, _SuperLotto_, Euromillions, Keno, horse racing (both _trifectas_ and _superfectas_). Nothing in the lottery world comes even remotely close.

Also, as a user put it so eloquently: _"**<u>MDIEditor Lotto WE</u>** is a work of great visual art"_.

-   Starting with Windows XP, Microsoft has made harder to use the help facilities in older 32-bit software applications. Microsoft would create an install file, specific for the version of Windows you needed **winhlp32** for.
-   Worse, Microsoft abandoned the creation of such an install file for Windows 10. Many older programs would have no help anymore, including my own creation **MDI Editor Lotto**!
-   You can find at this website and apply an elegant solution:
    -   [_**WINHLP32 in Windows 10 Help Files for Older Software Programs, Applications**_](https://saliu.com/gambling-lottery-lotto/windows-10-help.htm).

![Learn how to use lottery filters in MDIEditor And Lotto WE.](https://saliu.com/HLINE.gif)

## <u>2. Introduction to <span color="#ff0000">MDIEditor And Lotto WE Software</span></u>

This software is written on the basis of both years of studying lottery and other similar games, and on sound and tested mathematical and statistical formulae.

If you spend the time to get to know and understand the principals within which this software operates, it will be time well spent. In fact it will be time better spent than going down to your lottery outlet and asking for a 'quick pick' combination. It will also be time better spent than 'relying' on your combinations that you insist on playing each week.

To exemplify this point, in a 6/49 lottery (the most common worldwide) there are 13,983,816 possible combinations. For argument's sake, let's round that up to 14 million. If, say, every combination were to come out just once, in random order, in 14 million draws, then at 2 draws per week (104 per year), it would take just under 135,000 years for ALL the combinations to come out.

Unless they make a sudden breakthrough in the science of longevity, you are not going to be around to see that happen, and the chances are that your combination that you play each week will not happen in your lifetime.

Indeed, to be assured of a 99.9% degree of certainty that your combination will hit, you would in fact have to play every draw for around 664,000 years!

Now, there are those that say _"Hold on, in my 6/49 lotto game the probability of me hitting the jackpot in a certain draw is 1/13,983,816 – so don't tell me you can improve on that!"_ On its own, that statement IS true. At the beginning of every lottery draw, before the first number is drawn, the PROBABILITY of ANY combination to come out IS 1/13,983,816 – BUT STATISTICALLY it is not.

Say, for example, the last lottery draw's numbers were _1,2,3,4,5,6_. At the beginning of the next draw, as a single event on its own, there is still a 1/13,983,816 probability for the numbers _1 2 3 4 5 6_ to come out, BUT STATISTICALLY the chances are as far as it is possible to get from 'likely'. There is an important difference between the probability of a single event and the statistics of what has happened in many identical previous events. Contrary to popular belief, past drawings DO count in any game of chance. Pascal demonstrated this fact hundreds of years ago.

This piece of software, among other things, will enable you to study the statistics of your particular lottery game, to set filter values based on those statistics and generate optimized combinations that have a far greater chance to hit the next draw (or within a certain number of draws) than playing just random combinations.

![The software tutorial, manual presents the mathematics of lotto software programming.](https://saliu.com/HLINE.gif)

## <u>3. The Mathematical Foundation of Lotto, Lottery Theory and Software</u>

Perhaps, at this moment, you are asking yourself, _"So what's the point of playing the lottery then – how is this software going to help me?"_ Well, look at it this way. In the coin tossing game, there are two possible outcomes. Just suppose, based on statistical analysis, you could eliminate one of those outcomes for the next draw. Studying the statistics might give you that edge. Statistics can't predict the future, but they can give a good indication of what is more likely, or less likely, to happen.

Stockbrokers, for example, use statistical analysis all the time to decide where to invest YOUR money. If you've invested money in a private pension or whatever, then you are placing your trust in them to make important financial decisions on your behalf (with your hard-earned cash) based on _nothing more_ than statistics!

So is there any reason why you can't use the same principles to improve your chances of winning the lottery? No reason whatsoever. Let's go back to the point I was making about the [_**degree of certainty in coin tossing games**_](https://saliu.com/Saliu2.htm). Supposing your statistical analysis led you to believe that you could discard one of the two outcomes. You would be almost 100% sure to win. Remember, nothing can be 100% sure in a game of chance. Your historical data for a coin tossing game might go back thousands of tosses. You might find statistical trends, or 'trigger' points in that data that show you 'it's worth betting now', and that it's only worth betting on heads, or only worth betting on tails.

Obviously the lottery is far bigger in odds than a coin tossing game, but the principle is none-the-less the same. Suppose your statistical analysis of a 6/49 lottery suggests that you only create combinations from a field of 30 of those 49 numbers. In other words your statistical analysis tells you that there is a strong indication that 19 of the numbers are not likely to come out. There are 13,983,816 possible 6-number combinations from a field of 49 numbers. There are "only" 593,775 possible 6-number combinations from a field of 30 numbers. So by 'getting rid' of 19 of the numbers you would have already eliminated 13,390,041 combinations to play. Combine this with setting meaningful filter values (which you will see later on) and you further reduce the number of possible combinations that the software can find to play. Sometimes you will find that it generates no combinations at all! So don't play. Save the money, and when it does generate a few combinations, then play them. You have greatly increased your chance to win.

This software does so much more than has been mentioned here. This is just meant as an introduction and taster, but don't worry, all will be gone into in detail, so you can learn what to do and start increasing your chances of winning.

![How to install MDIEditor Lotto WE lottery software, horse racing application.](https://saliu.com/HLINE.gif)

## <u>4. The Installation of <span color="#ff0000">MDIEditor And Lotto WE</span></u>

Note: If you are currently using an older version of **MDIEditor and Lotto**, it is recommended that you copy your DATA\* files and any other files of importance to you, from their current directory/folder to a temporary one. After you have done this, you should then uninstall your current version of **MDIEditor and Lotto**. You are then ready to install the new version of the application.

When you download the various files needed for the program, make sure you put them ALL in the same folder. It is recommended that you create a new, empty folder for this purpose. When all the files have been downloaded successfully continue with the installation instructions.

To install the application, double-click **Setup**

The application installs by default under the following path:

**C:\\Program Files\\MDIEditorLotto WE\\**

Note: It is suggested at this point if you moved files to a temporary folder earlier, that you now move them back from their temporary position to **C:\\Program Files\\MDIEditorLotto WE\\**

If you are familiar with **MDIEditor and Lotto** then you are now ready to run the application, though it is advised in the first instance that when you do so, you click on _Help_ and read the _What's New_ and _General Help_ files.

It is strongly recommended, particularly if you are new to **MDIEditor Lotto WE**, that you read this User's Guide in its entirety before you attempt to work with the super application. This will save you time in the long run.

![This is a basic overview of MDIEditor Lotto Lottery great program.](https://saliu.com/HLINE.gif)

## <u>5. Basic Overview of <span color="#ff0000">MDIEditor And Lotto WE</span></u>

**MDIEditor and Lotto WE** covers all of the following games: Pick-3 and Pick-4; 5, 6, 7-number Lotto (jackpot games); Keno, Horse Racing, Powerball, Mega Millions (_5&1_), Euromillions (_5&2_).

**MDIEditor and Lotto WE** consists of six main utilities:

4.1. Random Combination Generating  
4.2. Statistical Analyzing and Reporting  
4.3. Optimized Combination Generating  
4.4. Combination Generating in Lexicographic Order (Sequentially)  
4.5. Checking Winning Combinations  
4.6. Checking Strategies

There is also a "hidden" feature in the statistical utility. The program creates the _wonder-grid_ files. They show every number in a game with its most frequent parings. There is plenty of information at **SALIU.COM**, especially the message board (_New Writings_). Use the _Search_ engine to find specific information on using the _wonder grids_. Or, you can start here: [_**Lottery Pairs System, Lotto Pair Strategy**_](https://saliu.com/bbs/messages/645.html).

The wonder-grids should be played as _boxed_ in the digit lotteries (pick-3, pick-4, horse racing). The filenames contain the string _GRID_. It includes Keno (_GRID.K_).

<u>Random Combination Generating</u>

This is the easiest of the functions to use in **MDIEditor Lotto WE** as it does not require the creation and use of data files. The combinations generated are totally random, and simulate the _Quick Pick_ play offered by lottery outlets. It also calculates the odds for the digit games and any lotto game format.

\*Note: There are internal built-in filters that operate when running the lotto combination generating features, only in the optimized functions. The _Odds and Random_ functions do not apply any filtering. The combinations are exactly as those generated by the lottery computers (_quick picks_, _computer picks_, _lucky dips_, etc.) A word of caution: these functions are extremely fast: at least 100,000 combinations per minute for most games.

<u>DIGIT 3+4 &amp; Horse-racing</u>: To generate random combinations for the digit games (Pick-3 and Pick-4) click _Digit_ on the menu bar. Then select _Random 3+4_. This will open two windows. The larger one is where the random combinations will appear. The smaller one tells you the odds for each game and is the active window.

To start generating the random combinations, simply click OK in the small window. Next, you will select the amount of combinations to generate. The default is 1000 for the digit lotteries and horseracing; 10,000 for lotto games. You can generate as many combinations as you want; it all depends on your computer.

You will be at the end of the file. The status bar will show the number of combinations generated.

You can then edit this file, print selectively, and save it to disk. Or you can discard the combinations just generated and perform the procedure again as many times as you like. Simply close the window. The function will open a new window every time it generates purely random combinations.

This is also the recommended method of creating the _simulated_ data files (_SIM_\*).

<u>Hints and Tips</u>

Always keep in mind the law of big numbers. It is recommended that you let the program run for longer periods of time and repeat that process several times. Then select combinations from the bottom of the list displayed. You have no doubt heard of people who won with a 'quick pick' combination. That happens because the lottery computers generate millions of random combinations ('quick picks') for a particular drawing. If you generate just one random combination, you have very little chance that it will win! If you generate thousands of combinations, several times, you are then in the realm of the law of big numbers.

<u>Lotto</u>

Click Lotto on the menu bar. Then choose _Odds + Random Lotto_ from the drop down menu. A _Biggest number_ window opens with '49' as the default. If this is correct then click OK. Or if your lottery's highest ball value is different simply type in that value and then click OK.  
Now, a 'Numbers/Line' window opens with '6' as the default. If your lottery requires six numbers per combination then click OK, or if not, enter the correct number instead of '6' and then click OK.

The _Lotto Odds_ window then opens and displays the odds for your particular lotto game. To start generating random combinations just click OK in the _Lotto Odds_ window.  
Next, you will select the amount of combinations to generate. The default is 10000 for the lotto games. You can generate as many combinations as you want; it all depends on your computer. The larger window now tells you that it is 'generating hundreds of combinations per minute'. You will be at the end of the file. The status bar will show the number of combinations generated.

You can then edit this file, print selectively, and save it off to disk. Or you can discard the combinations just generated and perform the procedure again as many times as you like.

<u>Hints and Tips</u>

Always keep in mind the _**law of big numbers**_. It is recommended that you let the program run for longer periods of time and repeat that process several times. Then select combinations from the bottom of the list displayed. You have no doubt heard of people who won with a _quick pick_ combination. That happens because the lottery computers generate millions of random combinations (_quick pick_) for a particular drawing. If you generate just one random combination, you have very little chance that it will win! If you generate thousands of combinations, several times, you are then in the land of the law of big numbers.

<u>Other Games</u>

Just follow the principal of the above instructions for generating random combinations for the other games offered by **MDIEditor Lotto WE**.

Do not run multiple random generation processes simultaneously. That feature was available in the freeware version. It was eliminated in this version in order to assure the best performance. The new filters are very demanding on the system!

![The files of past lottery drawings, draws, winning numbers are essential to MDIEditor Lotto WE.](https://saliu.com/HLINE.gif)

## <u>6. Creating and Updating the Data Files: Drawings, Past Results, Past Winning Numbers</u>

Whichever game you want to analyze and produce reports for, you will first have to set up the relevant data file. The following files are already provided by this lottery software package and contain sample data:

\- _**DATA3**_ for the pick-3 game  
\- _**DATA4**_ for the pick-4 game  
\- _**DATAH**_ for horse racing 3 (_trifectas_)  
\- _**DATAH4**_ for horse-racing 4 (_superfectas_)  
\- _**DATAL4**_ for the lotto-4 game  
\- _**DATA5**_ for the lotto-5 game  
\- _**DATA6**_ for the lotto-6 game  
\- _**DATA7**_ for the lotto-7 game  
\- _**DATAK**_ for the Keno game  
\- _**DataPB5**_ for the Powerball _5+1_ or Megamillions _5+1_ games  
\- _**DATAEu**_ for the Euromillions game.

Note: The above _**DATA6**_ file is used to take you through the principals involved in the next section _Producing a statistical report_. It is suggested, therefore, that if you are new to **MDIEditor Lotto WE**, you read and follow the instructions in the next section to get yourself acquainted with the statistical reports BEFORE you overwrite the existing sample data in the _DATA6_ file with your own.

The creation of the data files really is a very basic task. Just make sure you adhere to the following conditions:

The lotto games data files must have EXACTLY the relevant quantity of numbers per line:

\- _**DATA3**_ must have exactly 3 digits per line  
\- _**DATA4**_ must have exactly 4 digits per line  
\- _**DATAL4**_ must have exactly 4 lotto numbers per line  
\- _**DATA5**_ must have exactly 5 lotto numbers per line  
\- _**DATA6**_ must have exactly 6 lotto numbers per line  
\- _**DATA7**_ must have exactly 7 lotto numbers per line  
\- _**DATAH**_ must have exactly 3 horse numbers per line  
\- _**DATAH4**_ must have exactly 4 horse numbers per line  
\- _**DATAPB5**_ must have exactly 5 regular lotto numbers per line, plus the _Power Ball_ at the end  
\- _**DATAK**_ must have exactly 22 keno numbers per line (to handle games that draw 20 or 22 numbers; if your game draws 20 numbers, add 0, 0 at the end of every line; see the sample _DataK_)  
\- _**DATAEu**_ must have exactly 5 regular lotto numbers per line, plus 2 Star numbers at the end.

\* The only field separators allowed to separate numbers on the same line are commas (,) or spaces ( ) or a combination of the two. It is suggested that you use only commas, or only spaces to separate the numbers on each line. The blank space is the most universal field separator.

Included are sample data files for all lotto and lottery games covered by MDI Editor And Lotto. Their file names start with DATA.

\* You must have the most recent draw of your data at the TOP of your DATA\* file. Each subsequent line must be the next OLDEST draw, and so on until eventually the bottom line of your DATA\* file is the oldest draw for the data you have.

Note: You will also see provided (when you click _File open_) that there is a _DataPB5_. These are for the Powerball games. If you are playing the Powerball _5+1_ game (or Mega Millions), then each line of your data file must have 6 numbers per line. That is, the 5 main numbers followed by the Powerball number. There is no need to put any extra spaces between the last main ball and the Powerball, but the Powerball must be the last number of the 6 on each line.

Examples:

```
<span size="5" face="Courier New"><b>DATA6
01 04 13 17 33 46
12 22 23 33 37 40
04 29 30 31 41 44

OR;

1,04,13,17,33,46
12,22,23,33,37,40
4,29,30,31,41,44

DATA4
1,4,5,2
3,1,9,0
6,6,3,1

OR;
1  4  5  2
3  1  9  0
6  6  3  1
</b></span>
```

Note: It is suggested that the digit DATA\* files should have at least 200 lines of data, and the lotto DATA\* files should have at least 100 lines of data.

The data for your lottery game can normally be found on the web. Different lottery commissions use different layouts on their websites for listing their lotteries' histories. The setting up of your data files will most normally involve you finding the relevant history and then typing the numbers manually into your DATA\* file (though some sites may facilitate more easily cutting-and-pasting the data). Remember to start with the most recent drawing at the TOP of your DATA\* file.

This task can be a little laborious, but once you have done it, you will only ever need to update your DATA\* file with the new drawing data each week/draw. It is important to keep you DATA\* file up to date. To do this simply note the numbers for the draw that just occurred in your lottery and enter this new data at the TOP of your DATA\* file on line one. It's as simple as that.

Note: It is strongly recommended that after you have finished creating your DATA\* file, you re-check that each line is correct. This might not seem like a very appealing task, but if you have made a mistake (which is quite possible if typing the data manually), it will render the statistical analysis and reporting of your DATA\* file useless. Take the time now, and get it right now. Then it's done, and you can relax in the knowledge that the data you are using is correct - BECAUSE YOU CHECKED IT. On a personal note, I re-checked the data in my DATA6 file several weeks after I had been using it - I FOUND TWO MISTAKES! CHECK YOURS NOW! GET IT RIGHT NOW!

Note: Under the _Check_ menu, you'll find a great command prompt utility to check your data files: **PARSEL**. Run it from time to time - it can spot some errors in the files.

All data files for the lotto games, including the SIMulated files must be in ASCENDING order; e.g. 1, 2, 3, 4, 5, 6  
but not  
3, 2, 5, 1, 6, 4 etc., etc.

\* There is a nice utility under the Lotto menu: Sort File. The function sorts in ascending order the combinations in a lotto data file. Moreover, a DOS (command prompt) utility is a nice tool for sorting and formatting lottery data files: **SORTING**.

Nota bene: Version **4\_WE** of _**MDIEditor and Lotto**_ recommends very large data files. The minimum requirements are: - 10, 000 for pick-3 and horseracing 3; 100,000 for pick-4 and horseracing 4; 200,000 for the lotto games. You can use the _Random and odds_ routines in the _Lotto_ and _Digit_ menus. Generate the minimum number of combinations and stop the generation. Delete all empty lines, if you see any! Go to the end of the file and select the last empty line. Press _Delete_. You will do this only once - deleting the last empty line.

Save the new combined file as the SIMULATED data file (_SIM-6_, etc.). You SHOULD NOT NEED TO WORK WITH THIS FILE AGAIN. It will be automatically added to the concatenated big file (D6, or D5, etc.) by the functions under the Data menu. The functions are named _Make D_\*; for example: _Make D6_ for the lotto-6 game. The function copies Data-6+Sim-6 and the result file is named D6. The function runs in the background a command line program named **MakeD**. Do not delete or move that program. The function resembles the make data utilities in the _**command prompt**_ LotWon lottery software. The D\* huge files are absolutely needed by the filter reporting and the optimized combination generators.

The statistical reports work just fine with real data files alone (Data-6, etc.). One requirement refers to the length (span) of the report. The frequency reporting will always be accurate regardless of the span. The skips, however, will not be entirely accurate. The last skips for a particular number do not show the real story. For example, a number has as last skip the value zero. That means that the particular number hit in two consecutive draws? Most likely not. The number was at the end of the reporting range, and the program did not know what happened beyond that point. To avoid such inaccuracies, it is recommended to choose as span (length) of reporting for a maximum equal to _Total draws in the data file_ divided by 2. If a data file has a total of 200 drawings, do the stats reporting for 100 or fewer draws.

-   • Do NOT mix different game formats in the same results file! Start a new data file if the game format changes (e.g. from '5 of 53' + '1 of 40', or from 5/55/1/42, etc.) If the lottery commission _changes the game format_, you must create a _new_ lotto/lottery data file! Do not mix game formats in one data file! For example, Pennsylvania changed from lotto 6/69 to lotto 6/49. I discarded of all previous lotto 6/69 drawings. I renamed the old 6/69 file and preserved it for archiving and researching purposes. I started from scratch a Pennsylvania lotto 6/49 history file. I recreated also the SIMulated data file, specifically for a 6/49 lotto game. The Powerball game changed its format (January 7, 2009) from 55 regular numbers to 59 regular numbers; from 42 power balls to 39. I proceeded as above. I changed the Powerball data files to contain only regular lotto numbers from 1 to 59 and power balls from 1 to 39. Please pay special attention to this requirement of **SuperPower**, **LotWon**, **MDIEditor And Lotto WE** software. More than a tip — it is a **requirement**: Do not mix various game formats in the same data file — ever!

![MDIEditor Lotto WE generates powerful, meaningful statistical reports to help users with strategies.](https://saliu.com/HLINE.gif)

## <u>7. Generating the Statistical Reports: Frequency, Skips, Filters</u>

When you have created your DATA\* file you are ready to produce a statistical report.

First a word of encouragement: Don't be put off by the above heading. You may see the word _statistical_ and immediately feel this is not for you. You might think _"Oh, I was never any good at math when I was at school"_. Don't be put off by that. **MDIEditor and Lotto** takes care of the mathematics itself. The reports produced might at first seem daunting to you with their array of numbers and values, but you will soon feel comfortable with them - and who knows, you might even discover a strategy for your own lottery that no-one has discovered before. If you really want to have a good chance of beating the odds, then you will need to use the statistical analysis and reporting feature offered by grandiose lottery application. So go on, take the plunge.

There are two types of statistical reports:

~ Frequency and skip reports: menu item _Stats_;  
~ Filters reports: menu item _Filters_.

The following shows how to produce a statistical report for a 6/48 lotto game using the existing DATA6 file provided by **MDIEditor Lotto**.

<u>A. The Statistical reporting (<i>frequency</i> and <i>skips</i>)</u>

Run **MDIEditor and Lotto WE**. The application starts with a blank window in view. Click Stats on the menu bar and select _Lotto 6_.

An _Open file_ window appears with _DATA6_ as the default file to open. Click _Open_.

A _Scope of Report_ window opens. Whatever number you enter here dictates the number of lines of analysis in the produced report. If this is your first time producing a report, it is suggested that you accept the defaults.

Click OK

The following window asks you for the biggest number in your game, which in this instance is 48. So type '48' in the box provided.

Click OK

The window now tells you which file it is analyzing. The size of your data file, the number of lines in the finished report, and the speed of your computer, will determine how long this process takes. In any event it shouldn't take more than a few minutes. When the report is ready, it is automatically displayed on screen.

What the report shows you -

The first part of the report lists the ball numbers down the left (in this case 1 - 48). Then as you read across it shows you how many times each ball came up in the number of draws you just analyzed (in this case 100).

It also shows you for each ball, which was its respective _Most drawn with_ and _Least drawn with_ ball - and the number of times these occurred in the 100 draws. If you followed the instructions above then you should now have a report on your screen, the top of which should look something like this:

```
<span size="5" face="Courier New"><b>                Lotto-6 Statistics
                Data file analyzed: C:\Lottery\DATA-6
                Total lotto drawings analyzed:  100 
  
   Lotto     Total    MOST drawn  Hits most    LEAST drawn  Hits least
   Number    Hits     with #      drawn with:  with #       drawn with:
  
     1         14         14           4            26           0
     2         12         25           3             6           0
     3         11          1           2            34           0
     4         11         16           4             2           0
     5         13         16           5            48           0
     6         12         43           4            32           0
     7          8         15           3            27           0
     8          9          5           4            18           0
     9         13         24           4             6           0
    10         13         15           5            17           0
    11          8         22           3            20           0
    12          8         48           4             4           0
</b></span>
```

Look at the line corresponding to Lotto Number 1. You can see that in the 100 draws that you analyzed, ball number 1 was present in 14 of those 100 draws. You can also see that it was MOST drawn with number 14 and it tells you they paired together 4 times (Hits most drawn with). Again going further right, you can see that in the 100 draws, number 1 was least drawn with number 26, which happened no times. In other words in the most recent 100 draws number 1 and number 26 never appeared in the same draw.

OK, lets go further down the report. The next part of the report gives you a 'skip' chart. 'Skip', means the number of draws in between each occurrence of a hit, or the number of draws for which a lotto number was 'idle' and did not come up. The skip chart should look like this:

```
<span size="5" face="Courier New"><b>                               Lotto-6 SKIP Chart
                               
                               <u>Lotto number 1</u>
Hits: 14 
Skips:   7  1  1  10  16  3  6  5  7  7  0  7  7  1 
Sorted Skips:  0  1  1  1  3  5  6  7  7  7  7  7  10  16 
Skip Median:  6 

                               <u>Lotto number 2</u>
Hits: 12 
Skips:   6  5  6  10  19  1  7  0  9  12  6  7 
Sorted Skips:  0  1  5  6  6  6  7  7  9  10  12  19 
Skip Median:  6 

                               <u>Lotto number 3</u>
Hits: 11 
Skips:   13  1  15  18  3  18  2  2  7  4  3 
Sorted Skips:  1  2  2  3  3  4  7  13  15  18  18 
Skip Median:  4 

                               <u>Lotto number 4</u>
Hits: 11 
Skips:   1  11  10  0  1  6  0  32  1  1  25 
Sorted Skips:  0  0  1  1  1  1  6  10  11  25  32 
Skip Median:  1

... etc. 
</b></span>
```

You can see the first entry of the skip chart is for 'Lotto # 1'. It tells you that it hit 14 times in the 100 draws you analyzed. Then there are three headings: _\* Skips \*\* Sorted Skips \*\*\* Skip Median_.

Let's take them one at a time. You will see that the first number to the right of the 'Skips' heading is '7'. That means that ball number 1 has not appeared in any of the previous 8 draws. (The minimum value for a skip is ZERO. If the skip value is ZERO this tells you that it hit the previous draw. So in other words it didn't 'skip' any drawings. Therefore a skip value of 7 means the ball has not hit for 8 draws, because zero to 7 is a total of eight).

Let's clarify that by looking at the top part of _Data6_:

```
<span size="5" face="Courier New"><b>  7  16  22  26  29  31
  4  11  22  23  34  46
  8  12  26  28  31  48
 12  13  16  21  31  48
  6  10  14  34  36  48
 30  33  37  39  43  44
  2  16  18  19  43  45
...
</b></span>
```

Starting with the top line (line 1) of the data6 file, count down ... zero (line 1), one (line 2), two, three, four, five, six, seven. You should have arrived at line 8, which shows the number 1. So it is eight draws, or a skip of 7, since number 1 last appeared, or 'hit'.

Look back at the skip chart for lotto number 1. The number to the right of the 7 is a 1. We've established that the last time the number 1 hit was 8 draws ago. Now we can see that before that, its previous hit was 2 draws before that, or a skip of 1. And again the next number in the skip chart for lotto number 1, is also a 1. So that means that lotto number 1 hit again 2 draws previously.

You might want to take some time and look at several of the ball's skip charts and check them against data6, to familiarize yourself with what the skip chart shows you. The 'Sorted skips' heading then shows you all the skips for a particular number in ascending order.

The 'Skip median' heading shows you the median value of the sorted skips.

Note: The median is the middle value of a string of numbers. It should not be confused with the average. If you were to work out the average skip for the above example of lotto number 1, you would find it happens to be 5.07. All the average tells you is just that - that on average, number 1 has hit every 5 (or just over) draws in the last 100 draws.

The median, however, is different. The median is the middle value in a string of values. Therefore 50% of the values are within the median or less, and 50% are within the median or more. In our example of lotto ball number 1, the skip median is 6. That tells us that 50% of the time lotto ball number 1 hits within a skip of 6 or less. The skip chart can be a very important tool in the selection and non-selection of lotto balls.

There is a great program that analyzes the skips in most lottery games, and then generates lottery systems based on skips. It is _**DOS (command prompt)**_ software: **SkipSystem**. It is available as free software to registered members. Read: [**<u>Strategy, Systems on Skips</u>**](https://saliu.com/skip-strategy.html): _**Lotto, Lottery, Powerball, Mega Millions, Euromillions, Horse Racing**_.

<u>B. Filter Reporting</u> A _Range of Draws for Filter Analysis_ window opens. Again, if this is your first time producing a report then accept the default (which will be the total number of lines in the DATA6 file).

Click OK.

Going further down the statistical report brings you to the _Filter Table_. This probably causes more 'worry' than any other aspect of the statistical report. All those numbers! All those Filters! What does it all mean? Well let's go through it.

At the top you have column headings, with their respective filter names. Under each is given the filter's respective median, average and standard deviation values.

At the very left of the filter table you will see the heading 'Draw'. And under this heading are the numbers 1 to 100. They correspond to the data6 file. That is, draw number 1 corresponds to line 1 in the data6 file. Reading across the table from draw 1 you see a set of values for each filter. These filter values are produced as a result of the numbers that came out in line 1 of the data6 file and how they relate to previous draws. So let's look at draw 1 as an example.

We'll come to the _Ion_\* filters in a moment. We'll begin by looking at the _Any_ and _Ver_ filters.

![This special application plots accurate lotto stats, statistics, number hits.](https://saliu.com/ScreenImgs/lotto-stats-mdi.gif)

First of all, the 'Any' filters refer to drawn numbers in any position, and the 'Ver' filters refer to drawn numbers in 'placed' position. So you will see that the Any 1 filter has a value of ZERO for draw 1. Look at the data6 file.

You will see that number 22 was a repeat from the previous draw. Therefore the Any 1 filter has a value of ZERO because you don't have to 'skip' any draws to find one number that repeated. Look at the Any 2 filter. It has a value of 1. That means if you go back two draws in the data6 file, you will have found two numbers (in any position) that are repeated in line one… and so on up to the Any 6 filter. The Any 6 filter has a value of 35. This means that you will find all the six numbers from line 1 of the data-6 file, within the previous 36 draws in the data6 file - in any position.

```
<span size="5" face="Courier New"><b>Data-6

Line 1:  7  16  22  26  29  31
Line 2:  4  11  22  23  34  46
Line 3:  8  12  26  28  31  48
Line 4: 12  13  16  21  31  48
Line 5:  6  10  14  34  36  48
Line 6: 30  33  37  39  43  44
Line 7:  2  16  18  19  43  45
...
</b></span>
```

So then, you can do the same for the 'Ver' filters. E.g. Ver 1 has a filter value of ZERO. And you can see in the above example that, indeed, the number 22 is not only a repeat from the previous draw, but it is a repeat in the same position. The Ver 6 filter shows a value of 39. So you would have to go back 40 draws to find all the six numbers from line 1 of data6, in their corresponding 'positions'. You will also have noticed the presence of '+' and '-' signs next to some of the filter values in the filter section of the statistical report. All these tell you is whether that particular filter was higher or lower in value than its previous occurrence.

Note: A cursory glance at the '+' and '-' signs will show you that in a vast majority of cases three '+'s are always followed by a '-' and vice versa. This can be a useful tool in selecting filter values.

Now, as far as the 'Ion\*' filters go, you do not need to know what facet of the data file they are looking at. With these, as with the 'Any\*' and 'Ver\*' filters, the median is the key.

• Example for Pick-3 - The pick-3 and pick-4 games' statistical reports are slightly different. Here is an example of a pick-3 statistical report:

![MDIEditor Lotto creates also meaningful reports of stats, statistics for pick lotteries.](https://saliu.com/ScreenImgs/pick-stats-mdi.gif)

You can see from the above table that digit '0' was drawn a total of 141 times and was paired most with digit '2'. You can see that they came out together in 32 drawings. You can also see that digit '0' was least paired with digit '9' - they came out together in 24 drawings. Therefore it might be preferable to play digit '0' with digit '2' and avoid playing digit '0' with digit '9'.

You will see the skip chart is slightly different to the lotto one. It not only shows the skip of each number in ANY position, but also shows the skips for each number in positions 1, 2, and 3. Obviously in the pick-3 game, you not only have to get the numbers right, but you also have to get them in the right order.

The skip chart for the digit '0' indicates that it "prefers" to come out in consecutive drawings (the value '0' in the string) or to wait just 1 drawing between hits. This is real data in the Pennsylvania lottery and I always consider such values. If I want even more detailed skip stats, I look also at the positional skip. In this real-life case, the digit '0' shows a 'preferred' wait period of 4 drawings when it is drawn in the first (lead) position; also 4 when drawn in the second (middle) position; and for the third (last) position, the digit '0' prefers to wait 0 or 1 drawing between hits. Overall ("Any position") the digit '0' does not like to wait more than 10 drawings between hits.

The Powerball Games The principals of interpreting the statistical reports for the Powerball games are the same as for all the other games. But let's have a look at part of the statistical report for a Powerball 5+1 game.

```
<span size="5" face="Courier New"><b>                   'Regular-5' Numbers Statistics
                   Data file analyzed: C:\Lottery\PowerBall
                   Total drawings analyzed:  100 

 Lotto    Total    MOST drawn  Hits most    LEAST drawn  Hits least
 Number   Hits     with #      drawn with:  with #       drawn with:

   1        10          6           3             3           0
   2        16         25           5            34           0
   3         9         11           3            34           0
</b></span>
```

The first part you will see shows the statistics for the 'Regular' 5 balls, and shows details for each of them much just like the other statistical reports we have looked at. The next part is the skip chart for the _regular_ or main 5 balls drawn:

```
<span size="5" face="Courier New"><b>                               <i>Regular-5</i> SKIP Chart
                               
                               <u><i>Regular-5</i> # 1</u>
Hits: 10 
Skips:   3  3  1  8  30  4  10  10  5  8 
Sorted Skips:  1  3  3  4  5  8  8  10  10  30 
Skip Median:  5 

                               <u><i>Regular-5</i> # 2</u>
Hits: 16 
Skips:   12  1  1  4  27  1  16  0  0  0  0  12  0  1  5  4 
Sorted Skips:  0  0  0  0  0  1  1  1  1  4  4  5  12  12  16  27 
Skip Median:  1 

                               <u><i>Regular-5</i> # 3</u>
Hits: 9 
Skips:   25  0  12  21  15  2  2  5  5 
Sorted Skips:  0  2  2  5  5  12  15  21  25 
Skip Median:  5 

                               <u><i>Regular-5</i> # 4</u>
Hits: 17 
Skips:   7  2  0  1  13  0  15  3  8  3  2  1  2  5  0  4  2 
Sorted Skips:  0  0  0  1  1  2  2  2  2  3  3  4  5  7  8  13  15 
Skip Median:  2 

... etc.

<u>Next comes the <i>Power Ball</i> skip chart:</u>

                              PowerBall SKIP Chart
                               
                               <u><i>Powerball</i> # 1</u>
Hits: 10 
PB Skips:   35  6  1  2  6  0  16  13  2  4 
Sorted PB Skips:  0  1  2  2  4  6  6  13  16  35 
PB Skip Median:  4 

                               <u><i>Powerball</i> # 2</u>
Hits: 7 
PB Skips:   30  2  27  2  9  17  3 
Sorted PB Skips:  2  2  3  9  17  27  30 
PB Skip Median:  9 

                               <u><i>Power Ball</i> # 3</u>
Hits: 5 
PB Skips:   4  10  29  33  1 
Sorted PB Skips:  1  4  10  29  33 
PB Skip Median:  10 

... etc.
</b></span>
```

<u>And finally the <i>filter</i> analysis table:</u>

![Analyze the Powerball filters in ranges of past lottery drawings.](https://saliu.com/ScreenImgs/powerball-filters-mdi.gif)

The new column here is for the _Power Ball_. The filter value for the _Powerball_ simply indicates how many draws it is since the Powerball for that draw last came out.

So now you have an idea of the 'what', we can move on to the 'what to do with'.

![The tutorial teaches how to create lottery strategy by setting filters or restrictions.](https://saliu.com/HLINE.gif)

## <u>8. Lotto, Lottery Strategies: Setting the Filters</u>

Simply put it, a strategy is a collection of settings for one or more filters. The filters are parameters that eliminate combinations in the generating processes.

There are probably as many strategies for playing the lotto and other games, as there are combinations in them, but one thing is sure, if you find a decent strategy for the game you are playing, you increase your chance to hit the jackpot massively!

One thing to consider, though it may sound obvious, is that you are not going to win every draw. I suggest you tell that to yourself now. What you need to do is find a strategy that suggests statistically, that it will hit, say, one, two, or maybe three times in a year. This will make the strategy tight, therefore generating fewer optimized combinations to play, therefore being more affordable, and at the same time giving you an increased likelihood to hit the jackpot with fewer combinations. If you find such a strategy AND it works, i.e. you do win the jackpot within a year, then there is no reason why that strategy will not continue to be valid for each year after that. Winning a lottery jackpot once a year for the rest of your life doesn't sound too bad does it!

Obviously the looser your strategy, the more combinations will be generated, and therefore, a greater cost will be involved in playing it.

What follows is an example of how to look for a possible start point for a strategy, then how to refine it and, finally how to test it. It assumes that you have produced the above example statistical report using the data6 file provided.

Pool of Numbers Using the filter table in the statistical report, have a look at the Any 6 filter median. You will see it has a value of 16. That means that 50% of the time the 6 winning numbers drawn came from the pool of numbers from the last 17 draws. That is not 'on average' 50% of the time…, that is mathematically and exactly 50% of the time the pool of numbers taken from the previous 17 draws WILL HAVE the six winning numbers (in this example).

That means, that if you discarded any number that didn't appear in the top 17 lines of your DATA\* file and played with the only the pool of numbers that was left, then 50% of the time you would have the six winning numbers of the next draw in your pool of numbers. Now in reality the pool of numbers from 17 draws will often contain nearly all the numbers anyway. In fact, using data6 as an example, the top 17 lines contain all the lotto numbers EXCEPT the numbers 24, 27, and 37. That would leave you a pool of 45 numbers to play. Total combinations of 45 numbers taken 6 at a time = 8,145,060. So that's obviously not viable on its own as a strategy.

Look again at the Any 6 filter. If you look down the 100 draws of analysis you will notice that on 4 occasions the Any 6 filter had a value of 6 or less. That has happened 4 times in 100 draws. At two draws per week, that equates to happening roughly 4 times within a year. The first occurrence of the Any6 filter being 6 or less is in line 28. If you go to the data-6 file and go to line 28, you will see that all six numbers from line 28 are present in the pool of numbers from the previous 7 draws only. The pool of numbers created from those 7 draws, is 33 numbers. Total combinations of 33 numbers taken 6 at a time = 1,107,568. Obviously playing over a million combinations each draw isn't practical either, but you see that you're already slashing the total combinations with which to play and you're learning to find and look for strategies.

Let's continue with this example. If you look only at the lines in the filter table that correspond to the above criteria for the Any 6 filter you could produce this edited version of the table.

![Analyze the 6-49 lotto filters in ranges of past lottery drawings.](https://saliu.com/ScreenImgs/lotto-649-filters-mdi.gif)

You can do this by using the _Check_ feature on the menu bar. Click _Check_, then select Strategies and Lotto 6 from the drop down menus. It will ask you for a file to open. Select _check.6_ and click _Open_. You will see a table for entering filter values. It is identical to the one you will see when you run the optimized combination generating feature. Enter the number 7 in the Max field for the Any 6 filter, then click OK.

You will see the above table presented before you. This makes it much easier to look at your possible strategy. Now you can clearly compare the 4 instances where this strategy hit, and look at how all the other filter values compare with each other. It is a good idea to create a blank form on a piece of paper, which resembles the strategy checking filter input screen, so you can enter various Min and Max values for the filters on paper first as you refine your strategy - this is also useful when you find a strategy that you want to play, as you will then have a hard copy of the filters' values that you use. What you want to look for now is whether there are any other 'anomalies' that fit with this possible strategy. Are there any here? Yes! Look at the filter Ion 4. In all four occurrences this filter value is ZERO. So you could also set this filter to a value of MAX = 1.

Now, if there aren't any other obvious 'gems' like this (which in this case there aren't) then fill in the rest of the form so that each filter's Min and Max setting incorporates all four values shown in the check table. In other words for all the other filters write the lowest value in the MIN box and the highest value + 1 in the MAX box.

Note: A filter's Max setting must be at least the Min setting + 1.

Also you should consider setting only a couple of the filters to very specific/'tight' values. The rest of the filters MIN/MAX levels should then be set conservatively, so that they at least incorporate all the instances where the tight values hit.

You should end up with the following:

![Play a good lotto strategy enabling tight restrictions, filter settings.](https://saliu.com/ScreenImgs/lotto-strategy-mdi.gif)

• Many users view the Ion\_5 filter as the key to a goldmine. They see values such as 417. They think immediately of setting Ion5 as follows: ~ minimum level = 417 ~ maximum level = 418.

They run the program and get no combination at all after days and nights of continuous running! There is no bug in _**MDIEditor and Lotto WE**_. A repeating value of 417 for _Ion\_5_ indicates an insufficient size of the data file. The 417 value is not to be relied on as far a maximum level is concerned. Most likely, Ion5 goes a lot higher. You should not use _Max\_Ion5=418_ under these circumstances! That's why your computer doesn't generate any combination. It is very, vary rare for a value of _Ion5_ to reach EXACTLY 417. The maximum values should be used only with very large data files. The 6-number lotto software in the **Bright/Ultimate** packages require over 12 million draws (_real_, plus _simulated drawings_).

Any time you see a value higher than 100 (e.g. 417 or 1000) repeating more than a dozen times, it should raise a red flag. The data file is too small (real draws + SIMulated combinations). I have seen _Ion5_ (in my in-house software) over 5000 many times (in a lotto-5 with a simulated data file of over 400,000 lines)! If your data file is large enough (100,000 or 200,000 for lotto-6), you will see _Ion5_ in 4 digits!

When you run the application only for the frequency report, you can choose to use fewer drawings, such as 100 or 200.

Create a very large SIMulated data file-I recommend at least 100,000 (one hundred thousand) combinations (lines). You can use the random modules in **MDIEditor and Lotto WE** or/and the editions of **LotWon** (_**command prompt**_ software). I created _SIM_ files of over 200,000 lines, and then I purged them. It's best to work with clean data files (that is, files without duplicate combinations).

There is another _**command prompt**_ utility software: **FileLines**. You'll find it in the _Check_ menu, _Strategies_, _Cross-check with FileLines_. **File Lines** help you put together strategies from MDIEditor And Lotto WE, and strategies from LotWon lottery software and utilities. Read: [_**Cross-reference strategy files created by LotWon, SuperPower, MDIEditor Lotto WE**_](https://saliu.com/cross-lines.html).

![Study how the best lottery software generates optimized lotto combinations.](https://saliu.com/HLINE.gif)

## <u>9. Playing a Lotto, Lottery Strategy</u>

When you have the information above, it's always a good idea to test the strategy, though it isn't necessary. One of the main reasons for testing a strategy is to see how many combinations it is likely to generate. You may find that when you run the optimized combination-generating feature using the filter values above, it still produces too many combinations for your budget.

So you might then want to try and 'tighten' the strategy further. Or, if you're happy with it, you could simply run the optimized combination-generating feature several times, and leave it running for longer periods of time. Then take the last few combinations from the last run and play these – remember what was said earlier about the law of big numbers.

<u>Generating Optimized Combinations</u>

To run the optimized combination-generating feature, first make sure you have run the statistical report feature. Then click Lotto on the menu bar, and then select Lotto 6 and Optimized Lotto 6 from the drop down menus. Then follow the instructions and when presented with the opportunity, enter the filter values for your strategy.

<u>Generating Sequential (Lexicographic) Combinations</u>

To run the sequential combination-generating feature, first make sure you have run the statistical report feature. Then click Lotto on the menu bar, and then select Lotto 6 and Sequential Lotto 6 from the drop down menus. Then follow the instructions and when presented with the opportunity, enter the filter values for your strategy.

This function is very different from the Optimized feature. The Sequential generates all the combinations in the set - in lexicographical order. If no filters are enabled, all possible combinations in the set are generated. For example, a lotto 6/49 game: C(49, 6) = 13983816 combinations total.

The function uses a temporary file to hold the sequential combinations. The file will be deleted at the end of the run. There is no way around it. Be aware of the gigantic size of the file. You can calculate (guestimate!) it this way. Every lotto number will be up to 2 bytes in size. Six lotto numbers will require 6 x 2 = 12 bytes. There is also a blank space between numbers plus one byte for {Enter}. So, a lotto-6 combination requires 20 bytes (per line). Thus, 13983816 will take up some 250 MB. The temporary file for a Powerball game will take up far more than that figure!

Beware! If the program ends the sequential generating process UNNATURALLY, the temporary file will not be deleted. You need to delete the sequential temp files manually. The file names start with ION. Click File, Open; right click on a file named ION\*; select Delete.

Also, the speed of execution in 'Lotto - Sequential' slows down tremendously when enabling large values for the filter named 'Past draws' in Input form. That's what Windows is all about. The DOS LotWon is hundreds of times faster for the same levels of the 'Past draws' filter. The 'Lotto - Optimized' features are adequately fast even for 'Past draws' in the hundreds of thousands ranges.

<u>Other strategies</u>

You might want to look for other strategies. If you look back at the statistical report, you will see there are two occurrences of the Ver 6 filter being 11. So twice in the last 100 draws, all the numbers came out IN POSITION from a pool of numbers taken from the previous 11 draws. It's only happened twice, but that's twice in the last year, and using this as a start point for a strategy would probably result in one that produces very, very few, if any, combinations to play each draw. But when the strategy hits again, you have a really good chance to hit the jackpot!

You don't just have to look at the statistical report. If you look at your DATA\* file, you may find 'anomalies' in your real data. There are many posts on the massage board at **SALIU.COM** that refer to just such possible strategies. More often these involve looking for a 'trigger' draw. This would mean that you don't play every draw, but that you wait for your 'trigger' draw to occur – and then you play 'N' number of draws after that. One example that has come up, and seems to be true for several lotteries around the world, is that when the sum total of all the numbers in a given draw adds up to less than 100, then 50+% of the time, you will find that within the next four draws, you have a draw where the highest ball drawn is <=33. Therefore, a possible strategy here would be: wait for the trigger draw (sum total < 100), then play the next 4 draws only, using only balls 1 to 33.

You can find various useful tips and strategy discussions at the following links:

-   About the new **MDIEditor Lotto WE** and a really good start point for a [_**Powerball lottery strategy**_](https://saliu.com/bbs/messages/623.html) – I have done some testing of this myself. It only produces combinations sometimes, but when it does, the amount won would have been far more than the amount bet – <u>needs <i><b>Purging</b></i> with other lotto strategies</u>.
-   [_**DEZ's <u>Pick-3</u> lottery strategy**_](https://saliu.com/bbs/messages/928.html).
-   A big discussion about possible [**_trigger_ draws for lotto 6**](https://saliu.com/bbs/messages/898.html).
-   Useful discussion on the [_**<u>Wonder Grid</u> Lotto Strategy**_](https://saliu.com/bbs/messages/638.html) (though this deals somewhat more with the LotWon software, it is nonetheless worth a read).
-   [_**Horse Racing Information**_](https://saliu.com/bbs/messages/813.html).
-   [_**<u>Powerball</u> Strategy, Systems, Software**_](https://saliu.com/powerball.html)
-   [**_<u>Mega Millions</u> Strategy, Systems, Software_**](https://saliu.com/mega-millions.html)
-   [**_<u>Euromillions</u> Strategy, Systems, Software_**](https://saliu.com/euro_millions.html).

<u>Final Comments</u>  
You should also read the help files that accompany **MDIEditor And Lotto**. And you should check out the website **SALIU.COM**, and read as much of the <u>1990s message board</u> (_bbs/messages/_) as you can. When you find something relevant for you, then I suggest typing out that information in your own words into a word document or similar. By doing this you will improve your understanding of what you have just read and you will also build an invaluable reference aid. To look up posts on the massage board for a particular subject simply use the search facility:

-   the [_**Help**_](https://saliu.com/Help.htm) page
-   the [_**Search**_](https://saliu.com/Search.htm) page.

~ To use the lotto wheels (systems) read the _README5,6,7.TXT_ files. You can find the wheels at the software download site (see the _<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>_ section). Run also the lotto wheeler program **LottoWheeler**. It wheels your lotto numbers: Replaces the theoretical numbers in a wheel by your picks (the lotto numbers you want to play). Read: _Software for lottery wheel players: Fill out_ [_**lotto wheels with player's lotto picks (numbers)**_](https://saliu.com/bbs/messages/857.html).

Ion Saliu,  
With much appreciated English-language help from:  
Nik _Kulai_ Barker (_Many thanks, Kulai, axiomatic one!_)

![ION filters in MDIEditor and Lotto, filters in lottery software eliminate millions of combinations.](https://saliu.com/HLINE.gif)

## <u>10. Expandability and Interoperability: <i>Purging</i> Previous Output Files</u>

The menu entries Lotto and Digit have a special feature for every lotto or lottery game: **Purge** File.

The function takes a text file of previously generated combinations and purges it. That is, it applies the filtering in **MDIEditor Lotto** to reduce further the output file.

The output file can be:  
~ a file of combinations previously generated by **MDIEditor Lotto**;  
~ a file of combinations previously generated by the _**command prompt LotWon**_ lotto/lottery programs;  
~ a lotto wheel (that needs to be reduced probably because of its large size);  
~ a file of combinations previously generated by another application (other than **MDIEditor Lotto** or **LotWon**/**SuperPower** _**command prompt**_ software).

The output file must be compliant with the LotWon or **MDIEditor Lotto** format:

~ the file must be in **text format** (no special formatting, no blank lines);  
~ the file must be compliant with the format of the data file for the respective lotto/lottery game; e.g. an output file to purge in the lotto-6 game must have exactly 6 numbers per line (combination); exception Keno: DataK must have exactly 22 numbers per line (the drawings), but an output file has exactly 10 numbers in a combination;  
~ the _name_ of the new output file must be **different** from the _purge_ filename.

The **Purge** function is also a valuable tool to test the effectiveness of the filters. Instead of generating millions of combinations, generate a 'purge' file of, say, 10,000 combinations in a lotto game. Then, you can test how many combinations a particular filter eliminates. You can test the internal filters as well. Check the corresponding checkbox to enable the inner filters. Make sure the rest of the filters are disabled; i.e. the text boxes in the input form are blank (set to zero). Click OK to start the purge process. The program will eliminate the combinations restricted by the inner filters. You can divide the amount of the combinations generated to the total number of combinations in the purge (output) file and thus calculate the percentage of the elimination.

You can also test the efficacy (eliminating power) of each filter for a value equal to 1, for both the minimum and the maximum levels. Let's exemplify by the famous now _Ion5_ filter.

First, here is how to figure out the MAXIMUM level to set Ion5 to. Say, the game is lotto 6/49. The integer part of 49/2 is 24. Divide the total number of combinations to use (what you entered at the respective prompt) by 24. That is the maximum level for ION5 you can set to. Keep in mind that Ion-5 can be bigger than that, sometimes much larger. It all depends on the total number of drawings to use. For a lotto 6/49 game: it is likely that 200,000 past drawings to use will report the correct maximum value for Ion5.

Test the efficiency of the minimum level of Ion-5: Purge the 10,000 output file against the D6 file. Set the min\_Ion5 textbox to 1 in the input form. Make sure all the other textboxes are blank and 'Enable inner filters' is not checked. Click OK to start generating combinations. Compare the amount generated to the initial 10,000 combos.

Test the efficiency of the MAXIMUM level of Ion5: Purge the 10,000 output file against the D6 file. Set the MAX\_Ion5 textbox to 1 in the input form. Make sure all the other textboxes are blank and 'Enable inner filters' is not checked. Click OK to start generating combinations. Compare the amount generated to the initial 10,000 combos.

The eliminating efficiency percentages tend to a constant for larger and larger output (purge) files. I believe 10000 is an adequate benchmark.

The most accurate testing method, however, is the Sequential option. It generates all the combinations in the set in lexicographic order. Here is how you can also check if a strategy generates, correctly, the winning combination.

This is an example for the lotto-4 game. Let's say that the Filters report shows the following string of filter values for line (draw) #20, data file _DataL4_, from _Ion1_ to _Ver4_:

_112-2-3-6-11-1-9-13-15-1-9-15-43_

First, open the DataL4 file and delete the first 20 lines (drawings). Draw #20 will act now like the unknown: The very first winning lottery combination to hit next; we "do not know" what combination it will hit next! Save As the new file under a different name; e.g. DataL4.2. Run Lotto-4, Sequential. Data file to run: DataL4.2. In the input form, type all the filters in the minimum side of the input boxes (from 112 to 43). Be absolutely accurate!  
Next, type in the maximum side of the input boxes the filter values increased by 1:

_113-3-4-7-12-2-10-14-16-2-10-16-44_

This is the tightest filter setting. If the strategy was correctly entered - and if the software is error-free! - the program will generate exactly 1 combination. It is the winning combination - the one showed in line #20 in the filters report!

The _Inner Filters_ must be disabled; the Past Draws filter must be set to 0 (empty input box). Also, be aware that some filters can reach very high values. If your data file is too small, some filters (like _Ion1_, _Ion4_, _Ion5_) could have reached far higher levels. Do not enable their maximum levels! You will notice that a largest number in a column repeats very often.

This method (running _Sequential_ with the tightest settings of the filters) discovered the largest number of bugs in **MDIEditor And Lotto WE**! Hopefully, there are no more errors in this great piece of software!

**FileLines** (_Check_, _Strategies_, _Cross-check_) is an additional tool in the process of expandability and interoperability of **MDIEditor Lotto**.

The inner filters should be always disabled when checking the accuracy of a strategy. The inner filters should be viewed as a bonus in the _Optimized_ (randomized) modules of the combination generators.

The _Input_ form has two functionality options: _Open_ strategy and _Save_ strategy. First, you type the filters of your strategy in the input fields. Click Save to save your data. The strategy name starts with _Strategy_, but you can select any name you want. Next time you want to use the strategy, click on _Open_. The input fields will be filled out automatically. You can click OK to run the strategy unedited, or you can edit data in the text boxes.

![ION filters in MDIEditor Lotto, filters in lottery software are sought-after by programmers.](https://saliu.com/HLINE.gif)

## <u>11. How About Them Filters?!</u>

The new inner filters are founded on the _**FFG deviation**_. They are more powerful and meaningful than before. The inner filters are disabled by default. The user can enable or disable them by clicking the corresponding checkbox on the input form.

There is a new textbox regarding total past drawings to eliminate: _Past Draws_. It acts like an inner filter, or like an external filter. _Past Draws_ is disabled by default.

Total past drawings to eliminate functions in the _N of N_ format. For example, in the lotto 6 game it eliminates all the combinations with 6 numbers in common. That is, it eliminates lotto-6 combinations that match exactly past drawings in the lotto-6 data file. Let's say one of the past draws in a lotto 6 game was 7, 10, 13, 22, 34, 44. The new feature eliminates the combination 7, 10, 13, 22, 34, 44 in the generating processes. If you leave the filter blank, no past drawings will be eliminated. In the Powerball-type games (Mega Millions, Euromillions), this feature applies the elimination to the 5 regular numbers only. Also, this feature acts partially in the Keno game, given the enormity of a 10/20/80 Keno game.

Run **Collisions** to have an idea on how the lotto numbers repeat. Refer to the [_**Birthday Paradox**_](https://saliu.com/birthday.html) page. It is safe, however, to set this filter to very high values. There is one nice command line utility in this package: PastDraws (under the _Filters_ menu). Run it from time to time for your lotto games (not pick-3,4 or horse racing). I haven't seen the _**Past Draws**_ filter under 400,000 for a lotto-6 _D6_ file with 400,000 drawings! The lotto-6 programs in the **Bright / Ultimate Software** applications show no-repeats of over 10 million sometimes! You can copy _**PastDraws**_ to your _**Command Prompt LotWon**_ software folder.

• An obsessive type of request pertains to the _ION_ filters in **MDIEditor and Lotto**, as well as some filters in **LotWon** _**command prompt**_ lottery software. How are the filters built? What do the filters mean? What are the formulas and algorithms that create the lotto filters? What's all the buzz about, axiomatic ones?

The filters in my lotto and lottery software represent complex mathematics. Also, I change the filters quite often. Not to mention that I even replace some filters from time to time. Could I ever be able to keep every user up to date with the meaning of every filter in my lotto, lottery programs? I, the author, forget sometimes what that particular lotto filter, or that particular filter represents. Yet, I do not need to know in order to create a lottery strategy.

Trying to understand what each filter does would complicate my lottery software unnecessarily. The lottery filters are better understood if the source code is revealed and the user is a programmer as well. Now, we got to the heart of the matter! Only lottery/lotto/software developers want to know all about the filters. They want to incorporate similar features in their lotto software.

That is, they want to steal my ideas; and, then, they want to thumb their ugly noses at me! They are the ones who ask so insistently that I explain to them how the lottery filters are built. They still won't be fully satisfied: They would want also the source code, with very clear comments! Regular (normal!) users of **LotWon** lotto lottery software and **MDIEditor and Lotto** DO NOT need to know how the filters are built. End of story.

![It is very important to study carefully the instructions, tutorial, manual of lotto software.](https://saliu.com/HLINE.gif)

## [<u>Resources in Lottery Software, Strategy, Systems, Lotto Wheeling</u>](https://saliu.com/content/lottery.html)

See a comprehensive directory of the pages and materials on the subject of lottery, software, strategies, systems, lotto wheels.

## Please study also the complement of this publication: [_**The Best Ever Lottery Strategy, Lotto Strategies**_](https://software.saliu.com/lottery-strategy-lotto-strategies.html).

-   _**The Main**_ [_**Lotto, Lottery, Software, Strategies**_](https://saliu.com/LottoWin.htm) _**page**_.  
    Presenting software to create free winning lotto, lottery strategies, systems based on mathematics. Get your lotto systems or wheels, the best lottery, lotto software, combinations, winning numbers.
-   [_**Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies**_](https://saliu.com/Newsgroups.htm).  
    Read a genuine analysis of Excel spreadsheets applied to lottery and lotto developing of software, systems, and strategies. Combining Excel analysis with powerful lottery and lotto software programmed by this author, _Parpaluck_.
-   [_**Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps, Programs**_](https://saliu.com/forum/lotto-book.html).
-   [_**Documentation, Help: MDIEditor Lotto WE, Lottery Software, Strategy Tutorial**_](https://saliu.com/mdi_lotto.html).
-   [_**Filters, Restrictions, Elimination, Reduction in Lotto, Lottery Software**_](https://saliu.com/bbs/messages/42.html).
-   [_**Step-By-Step Guide to Lotto, Lottery Filters in Software**_](https://saliu.com/bbs/messages/569.html).
-   [_**Basic Manual for Lotto Software, Lottery Software**_](https://saliu.com/bbs/messages/818.html).
-   [**Vertical or Positional Filters** _**in Lottery Software**_](https://saliu.com/bbs/messages/838.html).
-   [_**Beginner's Basic Steps to**_ **LotWon** _**Lottery Software, Lotto Software**_](https://saliu.com/bbs/messages/896.html).
-   [**Dynamic** or **Static** _**Filters: Lottery Software, Lotto Analysis, Mathematics**_](https://saliu.com/bbs/messages/919.html).
-   [**<u>Lottery Skip Systems</u>**](https://saliu.com/skip-strategy.html): _**Lotto, Powerball, Mega Millions, Euromillions**_.
-   [_**Lottery Utility Software**_](https://saliu.com/lottery-utility.html): _**Pick-3, 4 Lottery, Lotto-5, 6, Powerball, Mega Millions, Thunderball, Euromillions**_.
-   [_**Filtering, Filters in Lottery Software, Lotto Software**_](https://saliu.com/filters.html).
-   [_**Lottery Strategy, Systems Based on Number Frequency**_](https://saliu.com/frequency-lottery.html)
-   [_**Lotto Decades: Software, Reports, Analysis, Strategies**_](https://saliu.com/decades.html)
-   [_**Analysis of Best Ranges for Lotto Number Frequency, Lottery Pairs, Pairings**_](https://saliu.com/lottery-lotto-pairs.html).
-   [_**Lotto Software for Groups of Numbers: Odd, Even, Low, High, Sums, Frequencies**_](https://saliu.com/lotto-groups.html).
-   [_**Pick-3 Lottery Strategy Software, System, Method, Play Pairs Last 100 Draws**_](https://saliu.com/STR30.htm).
-   _"The Start Is the Hardest Part"_: [_**Play a Lottery Strategy, Lotto Strategies**_](https://forums.saliu.com/lottery-strategies-start.html).
-   [**<u>Lotto wheel</u>**s](https://saliu.com/lotto_wheels.html) _**for lotto games drawing 5, 6, or 7 numbers**_.  
    The most advanced theory of lotto wheels or reduced lottery systems. Get also original lotto wheels for lotto games drawing 5, 6, 7 numbers, Powerball, Mega Millions, Euromillions: Balanced, randomized, totally free.
-   _**Download**_ [**<u>Lottery Software, Programs</u>**](https://saliu.com/infodown.html), _**Systems, Lotto Wheels**_.

![Study the comprehensive lotto eBook, lottery manual of MDIEditor Lottery software.](https://saliu.com/HLINE.gif)

**[Home](https://saliu.com/index.htm) | [Search](https://saliu.com/Search.htm) | [New Writings](https://saliu.com/bbs/index.html) | [Odds, Generator](https://saliu.com/calculator_generator.html) | [Contents](https://saliu.com/content/index.html) | [Forums](https://forums.saliu.com/) | [Sitemap](https://saliu.com/sitemap/index.html)**

![You visited the site of MDI Editor Lotto Software tutorials, user guide, lottery manual.](https://saliu.com/HLINE.gif)
