#!/usr/bin/env julia

"""
Simple validation script for enhanced performance reporting system
"""

println("🔍 Validating Enhanced Performance Reporting System")
println("=" ^ 50)

# Test 1: Check if files exist and can be loaded
println("\n📁 Test 1: File Structure Validation")

required_files = [
    "src/wonder_grid_engine.jl",
    "src/backtesting.jl", 
    "src/performance_reporting.jl"
]

for file in required_files
    if isfile(file)
        println("  ✅ $file exists")
    else
        println("  ❌ $file missing")
    end
end

# Test 2: Check key function definitions
println("\n🔧 Test 2: Function Definition Validation")

try
    include("src/wonder_grid_engine.jl")
    println("  ✅ Wonder Grid Engine loaded")
catch e
    println("  ❌ Wonder Grid Engine error: $e")
end

try
    include("src/backtesting.jl")
    println("  ✅ Backtesting Engine loaded")
catch e
    println("  ❌ Backtesting Engine error: $e")
end

try
    include("src/performance_reporting.jl")
    println("  ✅ Performance Reporting loaded")
catch e
    println("  ❌ Performance Reporting error: $e")
end

# Test 3: Basic functionality test
println("\n⚙️  Test 3: Basic Functionality Test")

try
    # Test theoretical probability calculation
    theoretical_probs = calculate_theoretical_probabilities()
    
    if haskey(theoretical_probs, "3/5") && haskey(theoretical_probs, "4/5") && haskey(theoretical_probs, "5/5")
        println("  ✅ Theoretical probabilities calculated")
        println("    3/5: $(round(theoretical_probs["3/5"] * 100, digits=4))%")
        println("    4/5: $(round(theoretical_probs["4/5"] * 100, digits=6))%") 
        println("    5/5: $(round(theoretical_probs["5/5"] * 100, digits=8))%")
    else
        println("  ❌ Theoretical probabilities incomplete")
    end
    
    # Test confidence interval calculation
    test_probs = Dict("3/5" => 0.05, "4/5" => 0.01, "5/5" => 0.001)
    confidence_intervals = calculate_confidence_intervals(test_probs, 100)
    
    if length(confidence_intervals) == 3
        println("  ✅ Confidence intervals calculated")
    else
        println("  ❌ Confidence intervals incomplete")
    end
    
    # Test statistical summary generation
    mock_report = PerformanceReport(
        "Test", 7, (Date("2023-01-01"), Date("2023-12-31")), 100, 100,
        HitRates(0.05, 0.01, 0.001), Dict("3/5" => 5, "4/5" => 1, "5/5" => 0),
        Dict("3/5" => 2.0), EfficiencyComparison(Dict("3/5" => 2.0), Dict("3/5" => 0.05), Dict("3/5" => 0.025)),
        CostAnalysis(100, 1.0, 100.0), 1.0, 2.0, 3.0, 1.0, 0.95, Dict("best_efficiency_ratio" => 2.0)
    )
    
    empirical_probs = Dict("3/5" => 0.05, "4/5" => 0.01, "5/5" => 0.001)
    significance_tests = Dict("3/5" => Dict("is_significant_05" => false))
    
    summary = generate_statistical_summary(mock_report, empirical_probs, theoretical_probs, significance_tests)
    
    if haskey(summary, "overall_improvement_ratio")
        println("  ✅ Statistical summary generated")
        println("    Overall improvement: $(round(summary["overall_improvement_ratio"], digits=2))x")
    else
        println("  ❌ Statistical summary incomplete")
    end
    
catch e
    println("  ❌ Basic functionality error: $e")
end

# Test 4: Enhanced features validation
println("\n🚀 Test 4: Enhanced Features Validation")

enhanced_functions = [
    "generate_statistical_report",
    "display_statistical_report", 
    "calculate_confidence_intervals",
    "perform_significance_tests",
    "calculate_expected_value_analysis",
    "calculate_risk_analysis",
    "export_statistical_report_csv"
]

for func_name in enhanced_functions
    try
        func = eval(Symbol(func_name))
        if isa(func, Function)
            println("  ✅ $func_name defined")
        else
            println("  ❌ $func_name not a function")
        end
    catch e
        println("  ❌ $func_name missing or error")
    end
end

println("\n✅ Validation Complete!")
println("=" ^ 50)