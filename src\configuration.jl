# Configuration and parameter management for Wonder Grid Lottery System

using Dates

"""
System configuration structure
"""
mutable struct WonderGridConfig
    # Strategy parameters
    key_number::Union{Int, Nothing}
    auto_key_selection::Bool
    key_selection_method::String  # "random", "optimal", "sequential", "manual"
    
    # Analysis parameters
    analysis_depth::String  # "basic", "standard", "comprehensive"
    include_lie_elimination::Bool
    lie_threshold::Float64
    
    # Data parameters
    data_source::String
    historical_data_range::Tuple{Date, Date}
    minimum_data_points::Int
    
    # Performance parameters
    enable_parallel_processing::Bool
    max_threads::Int
    cache_enabled::Bool
    cache_size_mb::Int
    
    # Output parameters
    output_format::Vector{String}  # ["csv", "txt", "json"]
    output_directory::String
    include_metadata::Bool
    show_progress::Bool
    
    # Validation parameters
    validate_inputs::Bool
    strict_validation::Bool
    error_handling_mode::String  # "strict", "lenient", "interactive"
    
    # Display parameters
    display_mode::String  # "compact", "detailed", "interactive"
    max_combinations_display::Int
    color_output::Bool
    
    function WonderGridConfig()
        new(
            nothing,           # key_number
            true,             # auto_key_selection
            "optimal",        # key_selection_method
            "standard",       # analysis_depth
            true,             # include_lie_elimination
            0.1,              # lie_threshold
            "historical",     # data_source
            (Date("2020-01-01"), Date("2023-12-31")), # historical_data_range
            50,               # minimum_data_points
            false,            # enable_parallel_processing
            4,                # max_threads
            true,             # cache_enabled
            100,              # cache_size_mb
            ["csv", "txt"],   # output_format
            "results",        # output_directory
            true,             # include_metadata
            true,             # show_progress
            true,             # validate_inputs
            false,            # strict_validation
            "interactive",    # error_handling_mode
            "detailed",       # display_mode
            100,              # max_combinations_display
            true              # color_output
        )
    end
end

"""
Configuration manager for handling system settings
"""
struct ConfigurationManager
    config::WonderGridConfig
    config_file::String
    
    function ConfigurationManager(config_file::String="wonder_grid_config.txt")
        config = WonderGridConfig()
        manager = new(config, config_file)
        
        # Try to load existing configuration
        if isfile(config_file)
            load_configuration!(manager)
        end
        
        return manager
    end
end

"""
Interactive configuration setup
"""
function interactive_configuration_setup()::WonderGridConfig
    println("🔧 Wonder Grid Lottery System - Configuration Setup")
    println("=" ^ 60)
    
    config = WonderGridConfig()
    
    # Key number configuration
    println("\n📊 STRATEGY CONFIGURATION")
    println("-" ^ 30)
    
    print("Use automatic key number selection? (y/n) [y]: ")
    auto_response = strip(readline())
    config.auto_key_selection = isempty(auto_response) || lowercase(auto_response) == "y"
    
    if !config.auto_key_selection
        while true
            print("Enter key number (1-39): ")
            try
                key_input = strip(readline())
                if !isempty(key_input)
                    key_num = parse(Int, key_input)
                    if 1 <= key_num <= 39
                        config.key_number = key_num
                        break
                    else
                        println("❌ Key number must be between 1 and 39")
                    end
                end
            catch
                println("❌ Please enter a valid number")
            end
        end
    else
        println("Select key selection method:")
        println("  1. Optimal (recommended)")
        println("  2. Random")
        println("  3. Sequential")
        
        while true
            print("Choice (1-3) [1]: ")
            method_choice = strip(readline())
            
            if isempty(method_choice) || method_choice == "1"
                config.key_selection_method = "optimal"
                break
            elseif method_choice == "2"
                config.key_selection_method = "random"
                break
            elseif method_choice == "3"
                config.key_selection_method = "sequential"
                break
            else
                println("❌ Please enter 1, 2, or 3")
            end
        end
    end
    
    # Analysis configuration
    println("\n🔬 ANALYSIS CONFIGURATION")
    println("-" ^ 30)
    
    println("Select analysis depth:")
    println("  1. Basic (fast, essential metrics)")
    println("  2. Standard (recommended)")
    println("  3. Comprehensive (detailed, slower)")
    
    while true
        print("Choice (1-3) [2]: ")
        depth_choice = strip(readline())
        
        if isempty(depth_choice) || depth_choice == "2"
            config.analysis_depth = "standard"
            break
        elseif depth_choice == "1"
            config.analysis_depth = "basic"
            break
        elseif depth_choice == "3"
            config.analysis_depth = "comprehensive"
            break
        else
            println("❌ Please enter 1, 2, or 3")
        end
    end
    
    print("Enable LIE (Lottery Information Elimination)? (y/n) [y]: ")
    lie_response = strip(readline())
    config.include_lie_elimination = isempty(lie_response) || lowercase(lie_response) == "y"
    
    if config.include_lie_elimination
        while true
            print("LIE threshold (0.0-1.0) [0.1]: ")
            threshold_input = strip(readline())
            
            if isempty(threshold_input)
                config.lie_threshold = 0.1
                break
            else
                try
                    threshold = parse(Float64, threshold_input)
                    if 0.0 <= threshold <= 1.0
                        config.lie_threshold = threshold
                        break
                    else
                        println("❌ Threshold must be between 0.0 and 1.0")
                    end
                catch
                    println("❌ Please enter a valid number")
                end
            end
        end
    end
    
    # Output configuration
    println("\n📤 OUTPUT CONFIGURATION")
    println("-" ^ 30)
    
    println("Select output formats (comma-separated):")
    println("  Available: csv, txt, json")
    print("Formats [csv,txt]: ")
    format_input = strip(readline())
    
    if isempty(format_input)
        config.output_format = ["csv", "txt"]
    else
        formats = [strip(f) for f in split(format_input, ",")]
        valid_formats = ["csv", "txt", "json"]
        config.output_format = [f for f in formats if f in valid_formats]
        
        if isempty(config.output_format)
            println("⚠️  No valid formats specified, using default: csv, txt")
            config.output_format = ["csv", "txt"]
        end
    end
    
    print("Output directory [results]: ")
    dir_input = strip(readline())
    if !isempty(dir_input)
        config.output_directory = dir_input
    end
    
    # Performance configuration
    println("\n⚡ PERFORMANCE CONFIGURATION")
    println("-" ^ 30)
    
    print("Enable parallel processing? (y/n) [n]: ")
    parallel_response = strip(readline())
    config.enable_parallel_processing = lowercase(parallel_response) == "y"
    
    if config.enable_parallel_processing
        while true
            print("Maximum threads [4]: ")
            thread_input = strip(readline())
            
            if isempty(thread_input)
                config.max_threads = 4
                break
            else
                try
                    threads = parse(Int, thread_input)
                    if threads > 0
                        config.max_threads = threads
                        break
                    else
                        println("❌ Thread count must be positive")
                    end
                catch
                    println("❌ Please enter a valid number")
                end
            end
        end
    end
    
    print("Show progress indicators? (y/n) [y]: ")
    progress_response = strip(readline())
    config.show_progress = isempty(progress_response) || lowercase(progress_response) == "y"
    
    println("\n✅ Configuration setup complete!")
    return config
end

"""
Validate configuration parameters
"""
function validate_configuration(config::WonderGridConfig)::Tuple{Bool, Vector{String}}
    errors = String[]
    
    # Validate key number
    if !config.auto_key_selection && (config.key_number === nothing || config.key_number < 1 || config.key_number > 39)
        push!(errors, "Key number must be between 1 and 39 when manual selection is enabled")
    end
    
    # Validate key selection method
    valid_methods = ["random", "optimal", "sequential", "manual"]
    if !(config.key_selection_method in valid_methods)
        push!(errors, "Invalid key selection method: $(config.key_selection_method)")
    end
    
    # Validate analysis depth
    valid_depths = ["basic", "standard", "comprehensive"]
    if !(config.analysis_depth in valid_depths)
        push!(errors, "Invalid analysis depth: $(config.analysis_depth)")
    end
    
    # Validate LIE threshold
    if config.lie_threshold < 0.0 || config.lie_threshold > 1.0
        push!(errors, "LIE threshold must be between 0.0 and 1.0")
    end
    
    # Validate date range
    if config.historical_data_range[1] >= config.historical_data_range[2]
        push!(errors, "Invalid historical data range: start date must be before end date")
    end
    
    # Validate minimum data points
    if config.minimum_data_points < 1
        push!(errors, "Minimum data points must be at least 1")
    end
    
    # Validate thread count
    if config.max_threads < 1
        push!(errors, "Maximum threads must be at least 1")
    end
    
    # Validate cache size
    if config.cache_size_mb < 1
        push!(errors, "Cache size must be at least 1 MB")
    end
    
    # Validate output formats
    valid_formats = ["csv", "txt", "json"]
    invalid_formats = [f for f in config.output_format if !(f in valid_formats)]
    if !isempty(invalid_formats)
        push!(errors, "Invalid output formats: $(join(invalid_formats, ", "))")
    end
    
    # Validate error handling mode
    valid_modes = ["strict", "lenient", "interactive"]
    if !(config.error_handling_mode in valid_modes)
        push!(errors, "Invalid error handling mode: $(config.error_handling_mode)")
    end
    
    # Validate display mode
    valid_display_modes = ["compact", "detailed", "interactive"]
    if !(config.display_mode in valid_display_modes)
        push!(errors, "Invalid display mode: $(config.display_mode)")
    end
    
    # Validate max combinations display
    if config.max_combinations_display < 1
        push!(errors, "Maximum combinations display must be at least 1")
    end
    
    return isempty(errors), errors
end

"""
Display helpful error messages
"""
function display_validation_errors(errors::Vector{String})
    println("❌ Configuration validation failed:")
    println("-" ^ 40)
    
    for (i, error) in enumerate(errors)
        println("  $i. $error")
    end
    
    println("\n💡 Please correct these issues and try again.")
    println("   Use interactive_configuration_setup() for guided setup.")
end

"""
Save configuration to file
"""
function save_configuration!(manager::ConfigurationManager)
    config = manager.config
    
    open(manager.config_file, "w") do file
        println(file, "# Wonder Grid Lottery System Configuration")
        println(file, "# Generated: $(Dates.now())")
        println(file, "")
        
        println(file, "[Strategy]")
        println(file, "key_number = $(config.key_number)")
        println(file, "auto_key_selection = $(config.auto_key_selection)")
        println(file, "key_selection_method = $(config.key_selection_method)")
        println(file, "")
        
        println(file, "[Analysis]")
        println(file, "analysis_depth = $(config.analysis_depth)")
        println(file, "include_lie_elimination = $(config.include_lie_elimination)")
        println(file, "lie_threshold = $(config.lie_threshold)")
        println(file, "")
        
        println(file, "[Data]")
        println(file, "data_source = $(config.data_source)")
        println(file, "historical_data_start = $(config.historical_data_range[1])")
        println(file, "historical_data_end = $(config.historical_data_range[2])")
        println(file, "minimum_data_points = $(config.minimum_data_points)")
        println(file, "")
        
        println(file, "[Performance]")
        println(file, "enable_parallel_processing = $(config.enable_parallel_processing)")
        println(file, "max_threads = $(config.max_threads)")
        println(file, "cache_enabled = $(config.cache_enabled)")
        println(file, "cache_size_mb = $(config.cache_size_mb)")
        println(file, "")
        
        println(file, "[Output]")
        println(file, "output_format = $(join(config.output_format, ","))")
        println(file, "output_directory = $(config.output_directory)")
        println(file, "include_metadata = $(config.include_metadata)")
        println(file, "show_progress = $(config.show_progress)")
        println(file, "")
        
        println(file, "[Validation]")
        println(file, "validate_inputs = $(config.validate_inputs)")
        println(file, "strict_validation = $(config.strict_validation)")
        println(file, "error_handling_mode = $(config.error_handling_mode)")
        println(file, "")
        
        println(file, "[Display]")
        println(file, "display_mode = $(config.display_mode)")
        println(file, "max_combinations_display = $(config.max_combinations_display)")
        println(file, "color_output = $(config.color_output)")
    end
    
    println("✅ Configuration saved to: $(manager.config_file)")
end

"""
Load configuration from file
"""
function load_configuration!(manager::ConfigurationManager)
    if !isfile(manager.config_file)
        println("⚠️  Configuration file not found: $(manager.config_file)")
        return false
    end
    
    try
        current_section = ""
        
        open(manager.config_file, "r") do file
            for line in eachline(file)
                line = strip(line)
                
                # Skip comments and empty lines
                if isempty(line) || startswith(line, "#")
                    continue
                end
                
                # Section headers
                if startswith(line, "[") && endswith(line, "]")
                    current_section = line[2:end-1]
                    continue
                end
                
                # Key-value pairs
                if contains(line, "=")
                    key, value = split(line, "=", limit=2)
                    key = strip(key)
                    value = strip(value)
                    
                    # Parse values based on section and key
                    parse_config_value!(manager.config, current_section, key, value)
                end
            end
        end
        
        println("✅ Configuration loaded from: $(manager.config_file)")
        return true
        
    catch e
        println("❌ Error loading configuration: $e")
        return false
    end
end

"""
Parse configuration value based on type
"""
function parse_config_value!(config::WonderGridConfig, section::String, key::String, value::String)
    try
        if section == "Strategy"
            if key == "key_number"
                config.key_number = value == "nothing" ? nothing : parse(Int, value)
            elseif key == "auto_key_selection"
                config.auto_key_selection = parse(Bool, value)
            elseif key == "key_selection_method"
                config.key_selection_method = value
            end
            
        elseif section == "Analysis"
            if key == "analysis_depth"
                config.analysis_depth = value
            elseif key == "include_lie_elimination"
                config.include_lie_elimination = parse(Bool, value)
            elseif key == "lie_threshold"
                config.lie_threshold = parse(Float64, value)
            end
            
        elseif section == "Data"
            if key == "data_source"
                config.data_source = value
            elseif key == "historical_data_start"
                start_date = Date(value)
                config.historical_data_range = (start_date, config.historical_data_range[2])
            elseif key == "historical_data_end"
                end_date = Date(value)
                config.historical_data_range = (config.historical_data_range[1], end_date)
            elseif key == "minimum_data_points"
                config.minimum_data_points = parse(Int, value)
            end
            
        elseif section == "Performance"
            if key == "enable_parallel_processing"
                config.enable_parallel_processing = parse(Bool, value)
            elseif key == "max_threads"
                config.max_threads = parse(Int, value)
            elseif key == "cache_enabled"
                config.cache_enabled = parse(Bool, value)
            elseif key == "cache_size_mb"
                config.cache_size_mb = parse(Int, value)
            end
            
        elseif section == "Output"
            if key == "output_format"
                config.output_format = split(value, ",")
            elseif key == "output_directory"
                config.output_directory = value
            elseif key == "include_metadata"
                config.include_metadata = parse(Bool, value)
            elseif key == "show_progress"
                config.show_progress = parse(Bool, value)
            end
            
        elseif section == "Validation"
            if key == "validate_inputs"
                config.validate_inputs = parse(Bool, value)
            elseif key == "strict_validation"
                config.strict_validation = parse(Bool, value)
            elseif key == "error_handling_mode"
                config.error_handling_mode = value
            end
            
        elseif section == "Display"
            if key == "display_mode"
                config.display_mode = value
            elseif key == "max_combinations_display"
                config.max_combinations_display = parse(Int, value)
            elseif key == "color_output"
                config.color_output = parse(Bool, value)
            end
        end
        
    catch e
        println("⚠️  Warning: Could not parse $section.$key = $value ($e)")
    end
end

"""
Display current configuration
"""
function display_configuration(config::WonderGridConfig)
    println("🔧 Current Wonder Grid Configuration")
    println("=" ^ 50)
    
    println("\n📊 Strategy Settings:")
    println("  Key Number: $(config.key_number === nothing ? "Auto-select" : config.key_number)")
    println("  Auto Selection: $(config.auto_key_selection)")
    println("  Selection Method: $(config.key_selection_method)")
    
    println("\n🔬 Analysis Settings:")
    println("  Analysis Depth: $(config.analysis_depth)")
    println("  LIE Elimination: $(config.include_lie_elimination)")
    if config.include_lie_elimination
        println("  LIE Threshold: $(config.lie_threshold)")
    end
    
    println("\n📊 Data Settings:")
    println("  Data Source: $(config.data_source)")
    println("  Date Range: $(config.historical_data_range[1]) to $(config.historical_data_range[2])")
    println("  Min Data Points: $(config.minimum_data_points)")
    
    println("\n⚡ Performance Settings:")
    println("  Parallel Processing: $(config.enable_parallel_processing)")
    if config.enable_parallel_processing
        println("  Max Threads: $(config.max_threads)")
    end
    println("  Cache Enabled: $(config.cache_enabled)")
    println("  Cache Size: $(config.cache_size_mb) MB")
    
    println("\n📤 Output Settings:")
    println("  Formats: $(join(config.output_format, ", "))")
    println("  Directory: $(config.output_directory)")
    println("  Include Metadata: $(config.include_metadata)")
    println("  Show Progress: $(config.show_progress)")
    
    println("\n🔍 Validation Settings:")
    println("  Validate Inputs: $(config.validate_inputs)")
    println("  Strict Validation: $(config.strict_validation)")
    println("  Error Handling: $(config.error_handling_mode)")
    
    println("\n🖥️  Display Settings:")
    println("  Display Mode: $(config.display_mode)")
    println("  Max Combinations: $(config.max_combinations_display)")
    println("  Color Output: $(config.color_output)")
    
    println("\n" * "=" ^ 50)
end

"""
Quick configuration presets
"""
function get_configuration_preset(preset_name::String)::WonderGridConfig
    config = WonderGridConfig()
    
    if preset_name == "beginner"
        config.analysis_depth = "basic"
        config.auto_key_selection = true
        config.key_selection_method = "optimal"
        config.include_lie_elimination = false
        config.output_format = ["txt"]
        config.display_mode = "compact"
        config.show_progress = true
        
    elseif preset_name == "standard"
        # Default configuration is already standard
        
    elseif preset_name == "advanced"
        config.analysis_depth = "comprehensive"
        config.include_lie_elimination = true
        config.lie_threshold = 0.05
        config.enable_parallel_processing = true
        config.output_format = ["csv", "txt", "json"]
        config.display_mode = "detailed"
        config.strict_validation = true
        
    elseif preset_name == "performance"
        config.analysis_depth = "basic"
        config.enable_parallel_processing = true
        config.max_threads = 8
        config.cache_enabled = true
        config.cache_size_mb = 500
        config.show_progress = false
        config.display_mode = "compact"
        
    else
        println("⚠️  Unknown preset: $preset_name")
        println("Available presets: beginner, standard, advanced, performance")
    end
    
    return config
end

"""
Interactive key number selection
"""
function interactive_key_selection(config::WonderGridConfig)::Int
    if !config.auto_key_selection && config.key_number !== nothing
        return config.key_number
    end
    
    if config.key_selection_method == "manual"
        while true
            print("Enter key number (1-39): ")
            try
                key_input = strip(readline())
                key_num = parse(Int, key_input)
                if 1 <= key_num <= 39
                    return key_num
                else
                    println("❌ Key number must be between 1 and 39")
                end
            catch
                println("❌ Please enter a valid number")
            end
        end
        
    elseif config.key_selection_method == "random"
        key_num = rand(1:39)
        println("🎲 Randomly selected key number: $key_num")
        return key_num
        
    elseif config.key_selection_method == "sequential"
        # This would need to track state between runs
        # For now, start with 1
        println("📈 Using sequential key number: 1")
        return 1
        
    else  # optimal
        # This would need historical analysis to determine optimal key
        # For now, use a reasonable default
        key_num = 13  # Often considered lucky
        println("🎯 Using optimal key number: $key_num")
        return key_num
    end
end