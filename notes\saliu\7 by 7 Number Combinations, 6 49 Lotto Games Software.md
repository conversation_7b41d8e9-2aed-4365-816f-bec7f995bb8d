---
created: 2025-07-24T22:10:02 (UTC +08:00)
tags: [6 number lotto,7-number lottery combinations,software,clusters,matrix,combination,lottery filters,lotto Sudoku,]
source: https://saliu.com/7by7-lotto-6-49.html
author: 
---

# 7 by 7 Number Combinations, 6 49 Lotto Games Software

> ## Excerpt
> Lotto software for 6 of 49 lotto games, generate 7-number combinations of all numbers, a matrix of 7 by 7 lotto numbers, perfect lottery Sudoku.

---
First captured by the _WayBack Machine_ (_web.archive.org_) on November 27, 2010.

<big>• Bright49</big>: Lotto software that generates random combinations for 6 / 49 lotto, but 7-by-7-number clusters, like **_Shuffle_** in **Combine6**. This represents a _perfect square_ lotto matrix, very much like lottery Sudoku.

-   This lotto software package is no longer updated. Instead, all programs specific to this package were moved to the most comprehensive 6-number lotto software suite: **Bright6**. You should always run **Bright 6** as it is always up-to-the-date, with all known errors ironed out.
-   The powerful lotto-6 software package runs under 32-bit/64-bit Windows OS and [_**32 / 64-bit Windows Vista / Windows 7, 8, 10**_](https://saliu.com/gambling-lottery-lotto/command-prompt.htm) via a great-looking and highly functional interface.
-   The new lotto software requires paid [**<u>Permanent Membership</u>**](https://saliu.com/membership.html) in order to download it; the usage is totally free thereafter.

The options specific to this **_7 by 7 from 49_** lotto software package and different from **Bright6** are _**F (filters)**_ and _**C (7-number lotto cluster generating)**_. The filter report is generated by a lotto program named <big>LottoGroupSkips6exe</big>. The 7-number lotto combinations are generated by the <big>Cluster49-7</big> application. I left in the bundle all the programs incorporated in **Bright-6**. I thought it convenient to do some lotto calculations or combination generating in the same interface, instead of hunting in different software packages.

First, let's take an artistically educated peek at the main menus.

![Powerful lotto software applied to 6 from 49 lotto game: generate 7-number combinations in groups of all numbers.](https://saliu.com/ScreenImgs/lotto-7x7-49.gif)

Axiomatic one, I've written lotto software of a different nature this year of grace 2010. There was a mix of curiosity and skepticism related to an old lotto 6/40 strategy that hit the jackpot but it wasn't played (1986)! I started the talk back in January 17, 2001. I wrote this message in my oldest forum: [_**History of my Lottery, Lotto Experience: Software, Systems**_](https://saliu.com/bbs/messages/532.html).

That post triggered questions in my previous forums and also from readers of my book. Why don't I present clearly and in detail that lotto strategy that hit a jackpot in 1986 (although it was not played)? I would start two threads in the forum, for open discussions on that lotto strategy. Then, I put together an article at SALIU.COM with human ad technical details (computer programming, software, probability theory).

-   _The big lotto strategy did hit the jackpot and will hit again_
-   It is guaranteed to win to the benefit of several lottery players
-   The guarantee is definitely above random play.

![The lotto software creates a matrix of 7 by 7 lotto numbers in perfect lottery Sudoku.](https://saliu.com/HLINE.gif)

The request to write specific software for this type of lotto strategy was imminent. Writing started with 6-number lotto, specifically for bookie lotteries. Then I wrote the software for the lotto-6 games generating 12-number combinations. It is all presented here:

-   [_**Lotto Strategy, Software: 12-numbers Combinations in 6-number Lotto Games**_](https://saliu.com/12-number-lotto-combinations.html).

I expanded the concept and I wrote <big>LottoGroupSkips6</big>. The lotto program generates a special report for lotto-6 regarding the SKIPS of the subgroups of numbers: _**Singles, Pairs, Triples, Quads, Quintets, Sextets**_. The report shows, draw by draw, when each subgroup last hit; that is, how many drawings each subgroup skipped in the most recent past. These 6 parameters will serve as filters in the lotto program that generates 12-number combinations while working with a 6-number lotto results file.

Here is the beginning of a report:

![Lotto strategy for 6 numbers in 12-number combinations of singles to sextets.](https://saliu.com/ScreenImgs/lotto-6of12.gif)

![Lotto Strategy, Software: 12-Numbers Combinations in 6-Number Lotto Games.](https://saliu.com/ScreenImgs/lotto-7x7-49-filters.gif)

<big>•</big> I left the filter generator unchanged from **Bright 12**. You simply disregard the _ONES_ filter. Evidently, we can't afford to eliminate any single lotto number. The identity of the combination generator is creating clusters of all 49 numbers in the lotto-6 game. <big>Cluster49-7</big> doesn't even show the _ONES_ filter in the interface.

Back in time again. In 2006, 17 professors and staff members at Bradford University and College in Britain won the lotto jackpot (a _6 from 49_ game). The syndicate played 17 tickets in The United Kingdom National Lottery. They applied a strategy that resembles the _**Shuffle**_ option in my lotto software (e.g. **Combine6**). Read:

-   [_**Professors Play All Lotto Numbers and Win the Jackpot**_](https://saliu.com/all-lotto-numbers.html).

The shuffle option in my lotto software generates all the numbers in the game, in 8 lines (combinations) of 6 numbers each. So, in a game with 49 numbered balls, one game would be left out. My software generates a 9th line with the missing number, and adds 5 more numbers that were already generated in previous lines. All 9 lotto combinations consist of unique numbers only. But why not all 49 numbers with no repeats, as in 7 numbers per line, for a total of 7 combinations?

There is a loyal member of my communities: Attila David. He wrote early in my forums (2001) regarding lotto pairing. He was also thinking of generating the 6/49 lotto numbers in a _matrix of 7 by 7_. That works only with lotto games where the largest number is a perfect square (25, 36, 49, 64). Of course, there are only two viable options: 6-by-6 = 36 (extremely rare now) and, especially, 7-by-7 = 49.

October of this year of grace 2010 had some accented changes in weather: hot, cold, hot again, cold again. Very hard to figure out how to dress — for me, at least! When I get into such states, I comfort myself by… writing new software! It abates my misery and thoughts away from the discomfort caused by a cold. And thus I started writing a new lotto program to generate _perfect clusters_ (_7x7 matrix_) for the 49-ball lotto game. Good thing to suffer from an out-of-season cold! For I discovered a serious error in my previous releases **Combine 6-12** and **Combine 5-10**. The error occurred when the user wanted to generate the lotto combinations to disk files. I overlooked something of great importance: The output file was opened, then closed before the generating even started! No more errors… for now…

The filters reported in the first program will feed the new lotto software. It is a _**7x7-number lotto combination generator**_: **Cluster-49-7**. The lotto software generates random combinations for lotto-6 data files, but 7 numbers per combination; the data file must still be in the 6-numbers-per-line format as in the **Bright-6** programs. The numbers are not sorted in ascending order; the numbers remain in random order.

```
<span size="5" face="Courier New" color="#c5b358"> 5  35  20  28  7  38  12 
 11  40  13  33  30  24  44 
 19  3  37  32  34  26  8 
 41  48  22  15  42  49  21 
 4  36  39  17  29  47  45 
 31  9  16  6  43  25  1 
 27  23  10  46  14  18  2 

 32  13  49  30  29  36  48 
 39  40  34  20  15  14  44 
 19  17  35  1  9  16  6 
 3  46  5  37  2  18  26 
 4  27  33  23  8  12  47 
 31  24  41  7  43  42  38 
 25  22  21  11  10  45  28 
</span>
```

The user can run **Sorting** to sort the numbers in ascending order, line by line. The clusters are not separated by a blank line or another indicator. My lotto software does not accept data files with blank lines. There is a lot of use in that requirement.

```
<span size="5" face="Courier New" color="#c5b358">  5   7  12  20  28  35  38
 11  13  24  30  33  40  44
  3   8  19  26  32  34  37
 15  21  22  41  42  48  49
  4  17  29  36  39  45  47
  1   6   9  16  25  31  43
  2  10  14  18  23  27  46

 13  29  30  32  36  48  49
 14  15  20  34  39  40  44
  1   6   9  16  17  19  35
  2   3   5  18  26  37  46
  4   8  12  23  27  33  47
  7  24  31  38  41  42  43
 10  11  21  22  25  28  45
</span>
```

![How the professors won the UK lotto jackpot by playing all numbers.](https://saliu.com/HLINE.gif)

Looking at the filters above, you might be inclined to apply the highest values to the 7-by-7-number lotto generator. But let's be mindful of certain limitations. Obviously, the odds are higher as the number of total possible combinations is larger in 7/49 than in 6/49. Furthermore, there is a limit imposed by the performance of the computers. Generating all the numbers in the 6/49 lotto game, 7 per line and in 7 lines, with high filter levels, can quickly come to a halt. The computers are not fast enough to quickly find qualifying matrices of 7 numbers per row and in groups of 7 columns.

I did several tests on my PC. I could not get to generate any 7-by-7 clusters (matrixes) for a 49-ball lotto number. I came across maximum values for the filters that my computer couldn't climb.

First of all, I used a real data file with drawings from the 6-49 lotto game played in Romania. I visited last summer and created a data file from the beginning of that lotto game. A file of simulated lotto drawings is mandatory. I used the best SIMulated data file for 6 / 49 lotto there is. I generated all 13983816 possible combinations in lexicographical order. My special software, **Permute Combine**, is best suited for the task. Then, I shuffled (randomized) that file in **Shuffle** (part of the **Bright-49** package). The result was _SIM-6_. I combined the real data file with _SIM-6_ and got _D6_, the big data file used by **Combine6-12**. Both files (_SIM-6_ and lexicographical 6/49) are available as downloads to registered members (huge sizes: over 300 MB each!)

My personal computer has these performance specs:

Processor Intel(R) Core(TM)2 Quad CPU Q6600 @ 2.40GHz, 2403 Mhz, 4 Core(s), 4 Logical Processor(s)  
Installed Physical Memory (RAM) 4.00 GB  
OS Name Microsoft Windows 7 Professional  
System Type x64-based PC

These are non-rocket-science maximum values of filters I encountered (that slowly generate some lotto clusters):

Triples: 45 - 50  
Quads: 500  
Quintets: 13000  
Sextet: 250000

A lot depends on your computer. You will need a few trial-and-error runs. Run this software at Command Prompt only. Close down all applications, including Internet browsers.

You might want to start with lower settings for the _Triple, Quad, Quintets_, and _Sextet_ filters. Then, you set higher and higher filter values that still generate 7-by-7 loto matrices. The generation process should be slow, however. A slow generation is a favorite of randomness. The chance is better you'll get the 6-number winner within a small amount of 7-number combinations. Let's notice that a 7-number combination expands to 7 lotto-6 combinations that did not repeat from the past drawings. Such combinations have a better chance to hit the lotto jackpot in the near future.

You can see how a lotto strategy fared in the past by going back 100 drawings in your game. Delete the top 100 draws in your _Data-6_ file. Save it as _Data-6.2_. Recreate the new _D6_ (_Data-6.2+SIM-6 D6_). Run **Cluster49-7** with your filter settings (the slow generating process). Select your combinations from the bottom of the output file. Use **Winners** (also included in **Bright 49**) to check for winners against your original _Data-6_. Repeat the process by deleting the top 99 draws from your _Data-6_. Save again as _Data-6.2_. Recreate _D6_, etc.

<u>How to check for winners against your file with real lotto drawings.</u>  
You run **Break Down Numbers** (option _**B: Break Down Lines of Numbers**_). The program will break the 7-number lines (combinations) into groups of 6 lotto numbers each. Feed that group to **Winners** (as input file).

<u>Caveat:</u>  
**BreakDownNumbers** will eliminate all duplicates and allow only unique 6-number combinations. You might notice, however, that 7-number lines generated by **Cluster-49-7** have in common 6, or 7 groups of numbers. Therefore, multiple jackpot lotto combinations are possible, but **Winners** will report only one.

![The old lotto strategy is also new: It applies to bookie lotteries lotto as well.](https://saliu.com/HLINE.gif)

**Bright49** is immediately available to download to registered members:

-   [_**Download <u>Bright-49</u> from this location**_.](https://saliu.com/code/BRIGHT49.exe)

Before downloading, create a folder (directory) for this software package; e.g. **BRIGHT49**. You can create a folder more easily in the GUI mode, in _Windows Explorer_ (_File Explorer_ in Windows 8, 10). Open Explorer and find your drive **C:**. You will probably find it under _My Computer_ (or simply _Computer_ in Vista / Windows 7; _This PC_ in Windows 10). Double click **C:** in the left pane. Move to the right pane, all the way down. Right click on the empty space. From the ensuing menu select _New_, then _Folder_. In the highlighted _New Folder_ type **BRIGHT49**. You download **Bright 49** from **saliu.com** to your **C:\\BRIGHT49** folder.

You can decompress **BRIGHT49** in Windows Explorer. Navigate to your **C:** drive, then to the **BRIGHT49** folder. Double click on the folder. Move to the right pane and now double click **BRIGHT49**. The self-decompression starts automatically. Select the same folder for the location of the decompressed files. You can do the same thing at the _**command prompt**_. At the **C:\\BRIGHT49** prompt, you type: **BRIGHT49** and press _Enter_. The decompression starts automatically and you can select the same folder as the destination.

After decompression, read either _README.TXT_ or _README49.TXT_. Type **B49** (or **b49**) to start the application.

![Read the best in lottery mathematics: Software, systems, strategies, lotto squares.](https://saliu.com/HLINE.gif)

[

## Resources in Lottery Software, Strategies, Systems, Lotto Wheels

](https://saliu.com/content/lottery.html)See a comprehensive directory of the pages and materials on the subject of lottery, lotto, software, systems, lotto wheeling.

-   [_**Play-All-Lotto-Numbers Analysis: Shuffle Software, Systems**_](https://saliu.com/all-lotto-numbers.html).  
    British professors won the UK National Lottery jackpot. They played all the numbers in the lotto game by shuffling or randomizing the lotto 6/49 numbers.
-   [_**Lotto Strategy, Software: 12-numbers Combinations in 6-number Lotto Games**_](https://saliu.com/12-number-lotto-combinations.html).
-   [_**Lotto Software, Wheels: 10-Number Combinations in Lotto 5 Games**_](https://saliu.com/lotto-10-5-combinations.html).
-   [_**Lotto Strategy: 12-Number Combinations Wheeled, Played in Lotto-6 Games**_](https://saliu.com/lotto-jackpot-lost.html).
-   [_**History of Lottery Experiences: Lotto Systems, Software, Strategies**_](https://saliu.com/bbs/messages/532.html).
-   [_**Randomness, Degree of Randomness, Absolute Certainty, True Random Numbers**_](https://saliu.com/bbs/messages/683.html).
-   [**<u>Play-All-Lotto-Numbers Analysis</u>**: _**Shuffle Software, Systems**_](https://saliu.com/all-lotto-numbers.html).  
    British professors won the UK National Lottery jackpot. They played all the numbers in the lotto game by shuffling or randomizing the lotto 6/49 numbers.
-   Are [_**lottery, gambling winnable**_](https://saliu.com/TRUTH.html) consistently — or is Ion Saliu just another crook or lunatic?
-   [**<u>Skip Systems</u>** _**for Lottery, Powerball, Mega Millions, Euromillions**_](https://saliu.com/skip-strategy.html).
-   [_**Lottery Utility Software**_](https://saliu.com/lottery-utility.html): Pick-3, 4 Lottery, Lotto-5, 6, Powerball, Mega Millions, Thunderball, Euromillions.
-   [_**Basics of Lottery, Lotto Strategy Based on: Sums (Sum-Totals); Odd / Even; Low / High Numbers**_](https://saliu.com/strategy.html).
-   [_**Lottery Strategy, Systems Based on Number Frequency**_](https://saliu.com/frequency-lottery.html).
-   [**<u>Lotto Decades</u>**: _**Analysis, Software, Systems**_](https://saliu.com/decades.html).
-   The Best Analysis Ranges for [_**Lotto Number Frequency, Lottery Pairs**_](https://saliu.com/lottery-lotto-pairs.html).
-   [_**<u>MDIEditor Lotto WE</u>: Tutorial, Software User Guide, Manual, Book, ebook**_](https://saliu.com/MDI-lotto-guide.html).  
    ~ Also applicable to LotWon lottery, lotto software; plus Powerball, Mega Millions, Euromillions.
-   [_**Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps, Programs**_](https://saliu.com/forum/lotto-book.html).
-   Practical [_**Lottery Filtering, Lotto Filters in Software**_](https://saliu.com/filters.html).
-   [_**Create Lotto Wheels: Manually, in Lotto Wheeling Software**_](https://saliu.com/lottowheel.html).
-   [_**Free lottery wheeling software for players of lotto wheels**_](https://saliu.com/bbs/messages/857.html).  
    ~ Fill out lotto wheels with player's picks (numbers to play); presenting **FillWheel, LottoWheeler** lottery wheeling software.
-   _**Download**_ [**<u>lottery software, lotto programs</u>**](https://saliu.com/infodown.html).

![Lotto 49 is a perfect square with a matrix of 7 numbers per line, 7 total lines in a matrix.](https://saliu.com/HLINE.gif)

**[Home](https://saliu.com/index.htm) | [Search](https://saliu.com/Search.htm) | [New Writings](https://saliu.com/bbs/index.html) | [Fundamental Formula](https://saliu.com/formula.htm) | [Odds, Generator](https://saliu.com/calculator_generator.html) | [Contents](https://saliu.com/content/index.html) | [Forums](https://forums.saliu.com/) | [Sitemap](https://saliu.com/sitemap/index.html)**

![You exit the site of the new best lotto software and strategies to win the jackpot.](https://saliu.com/HLINE.gif)
