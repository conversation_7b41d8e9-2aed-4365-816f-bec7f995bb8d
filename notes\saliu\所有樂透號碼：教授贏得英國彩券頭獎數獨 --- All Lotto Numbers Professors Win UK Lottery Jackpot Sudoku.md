---
created: 2025-07-24T22:02:03 (UTC +08:00)
tags: [lotto,jackpot,all numbers,software,lottery,system,professors,UK,National Lottery,6 49,win,play,syndicate,lotto group,Sudoku,strategy,]
source: https://saliu.com/all-lotto-numbers.html
author: 
---

# 所有樂透號碼：教授贏得英國彩券頭獎數獨 --- All Lotto Numbers: Professors Win UK Lottery Jackpot Sudoku

> ## Excerpt
> 17 British Professors won the UK National Lottery 6/49 lotto jackpot. Syndicate of professors played sixline lotto: All numbers in 649 lottery game.

---
<big>•</big> 2006 年 10 月 21 日，英國國家彩券發生了一起意外事件。據報道，這起事件在世界各地引起了轟動，因為這些彩券玩家竟然也是教授！玩家們自己也聲稱，他們在設計策略時運用了數學。實際上，彩票俱樂部的成員中只有一兩位是真正的教授…

他們組織了一個彩票集團。該集團由英國布拉德福德大學及學院的 17 名員工組成。他們從 1994 年就開始一起玩樂透了。直到今年，他們才取得了驚人的成功。他們在過去四年裡一直運用他們的獲勝策略（ _買下所有彩票號碼_ ）。

_「我們只是認為，如果所有號碼都被使用，我們一定有很大的獲勝機會，事實證明確實如此，儘管你從未真正想過這會發生在你身上，」_ 該團體領導人說。

顯然，在英國 _49 場_彩券遊戲中，他們一共買了 17 張彩券（17 個樂透-6 組合）。 17 張彩券的數量看起來有點奇怪（雙關語）！

<big>•</big> 我要在這裡告訴你真正的數學故事。我從 20 世紀 90 年代初就開始嘗試類似的彩券策略。我把這種彩票玩法稱為 **“SHUFFLE”（洗牌）** 。毋庸置疑，我是第一個提出這個想法和軟體的彩票分析師。事實上，我的彩券洗牌軟體至今仍是唯一擁有此功能的軟體。

![Lotto software of random combinations has multiple options, including shuffling.](https://saliu.com/ScreenImgs/random-lotto-options.gif)

![Lotto shuffling software uses most filters, but filtering must not be too tight.](https://saliu.com/ScreenImgs/lotto-clusters-filters.gif)

![Lotto software generates group combinations that consist of all numbers in a lotto game.](https://saliu.com/ScreenImgs/all-lotto-numbers.gif)

它類似於洗牌。所有牌都留在牌堆裡。在我的洗牌方法中，所有樂透號碼都保留在「牌堆」裡。我們將號碼分成 9 組，每組 6 個號碼（例如 _49 選 6 的_樂透遊戲）。如果我們只玩 8 張彩票，就會漏掉一個號碼：49。因此，我們將樂透號碼 49 加到第 9 行，然後添加先前選擇的 5 個號碼來完成彩票。最簡單的方法如下——但強烈建議**不要玩**這樣的彩票！

1,2,3,4,5,6  
7,8,9,10,11,12  
37,38,39,40,41,42  
...  
49, 1, 8, 15, 22, 30  

我還發布了一些專門處理**樂透洗牌的**軟體。這些程式自 1994 年起就一直是免費軟體：

SHUF-5  
SHUF-6

這兩款彩票程式已不再獨立運行，因為它們已被更強大的功能所取代。 **洗牌**功能被加入到功能更強大的軟體中。改進甚至更上一層樓： **Bright** 彩券軟體包。 2015 年初， **Ultimate Lottery Software** 應用程式正式問世。

在 **Combine** 程式中，可以_將所有樂透號碼進行混洗_ 。以下是一個使用 **Combine6** 產生的所有樂透 6/49 號碼的良好範例（ **S = 混洗**選項）：

集群 #1：  
25 43 41 47 32 3  
2 20 12 8 33 14  
27 31 24 16 36 22  
6 48 15 9 4 29  
13 11 1 46 17 23  
7 18 34 44 42 19  
10 26 35 38 5 49  
39 21 37 45 28 40  
30 25 20 24 4 23  

該**組**代表包含所有 _49 個樂透號碼的 6 號_組合，除最後一張彩券外，這些號碼均為唯一。最後一個組合與先前產生的 5 個樂透號碼重複：1 個來自第 1 行，1 個來自第 2 行，1 個來自第 3 行，1 個來自第 4 行，1 個來自第 5 行。順便說一句，上述組中的任何行均不重複過去樂透開獎中的任何 6 號組合。過去開獎的數十萬個，有時甚至數百萬個組合不會重複——包括 _Delta_ 格式。

-   在絕大多數情況下，數十萬次抽獎，包括_模擬_抽獎（ _SIM_ 檔案中的組合），過去的樂透組合都不會重複出現。只需查看 _W6.1_ 報告中的 _Del6_ 欄位即可。 _中位數_超過 _78 萬。 Del6_ _過濾器_會以_增量_格式剔除 _D6_ 資料檔中的過往組合。也就是說，程式不僅會剔除_直選_組合（精確的 6 個數字組），還會剔除相鄰數字之間具有相同_差異_ （增量）的組合。
-   我們必須至少使用 _Del6_ 過濾器，因為我們可能要處理_數萬億個_不同的簇。在使用 **Shuffle** 函數產生彩票組合時，數量驚人的獨特簇（矩陣）也需要耐心。

<big>•</big> 我並非說英國集團利用了我的彩券策略。我並未明確告知任何玩家或玩家群如何應用彩票洗牌的具體策略。但他們可能知道我的彩票軟體中的**洗牌**功能。這種情況很有可能發生。我的彩票軟體在全球擁有相當多的追隨者。許多訪客是透過搜尋 _「六線彩券」_ 、 _「ion saliu 六線彩券組」_ 、 _「彩券洗牌」_ 或 _「玩所有彩券號碼」_ 等關鍵字來造訪我的網站的。

當然，IP 位址在 Web 伺服器日誌檔案中匹配已經很久了。我想強調一下，我的軟體完全免費——沒有任何附加條件——基本上直到 2007 年。它是免費的——你贏了，所有獎金都歸你所有。此外，教授們採用的「洗牌」策略非常不合邏輯。為什麼要 17 張彩券？只需要 **9 個** 6 位數的組合。如果他們玩了兩組，那麼應該玩 18 張彩券。看來他們取消了第二組的最後一行，這樣每個玩家就只能玩一張樂透了！幸運的是，他們沒有取消中獎彩券！

或者，也許他們非常有效率！樂透 6/49 的彩券組包含 **8 個** 6 位數組合。因此，他們添加了兩個組，這樣就有了 16 個完整的 6 位數組合。每個組別的 9 號號碼都包含一個號碼。他們把最後幾組號碼組合起來，再加上 4 個不同的號碼。就這樣，他們得到了 17 張彩票，也就是每位玩家一張。從支付角度來看，他們把這些彩券一起玩了。

<big>•</big> 這個故事重新激起了我對洗牌策略的興趣。誠然，早期的電腦無法處理這種組合生成策略。計算機必須足夠快才能完成如此艱鉅的任務。按字典順序產生所有樂透組合涉及非常複雜的數學和非常艱鉅的計算機科學。即使是當今速度最快的計算機也只能進行隨機生成；因此**有了“洗牌”** 一詞。

我現在用的是更快的電腦。我仍然可以繼續用更快的 PC！彩券組合產生器的速度很慢，即使是採用多次中獎策略的情況也是如此。好消息是，電腦的速度一年比一年快。

我有一個彩票資料檔 ( _D6_ )，裡面有超過 12,000,000 個組合（6/49 彩票遊戲有 1200 萬個組合）。你們大多數人都知道，D6 檔案包含 Lotto-6 的真實開獎結果（過去的中獎號碼）以及數千甚至數百萬個_模擬_的彩票開獎結果。我記得曾經有一段時間，個人電腦幾乎無法處理只有 100 個開獎結果的資料檔案！

我的樂透軟體有一個缺陷，我在 **MDIEditor And Lotto WE** 4.0 版本中修復了它： _**內部過濾器**_ 。使用者無法停用_**內部過濾器**_ 。內部過濾器允許的頻率在統計上足夠高，但並非 100%。組合產生器可以排除 D6 檔案中的所有組合 _，_ 甚至更多。 Delta（相鄰樂透號碼之間的差異）也會應用於過去的開獎結果。例如，這是 _D6_ 彩票檔案中的一個組合（ _真實_結果加上_模擬_組合）：

6, 13, 26, 33, 38, 48

以下組合將被淘汰（以及更多）：

6, 13, 26, 33, 38, 48  
5, 12, 25, 32, 37, 47  
7, 14, 27, 34, 39, 49

-   D6 檔案中用於排除**所有**過去組合的過濾器也是基於 _delta_ 的，甚至比我之前的免費彩票軟體更難。該過濾器在 D6 檔案中超過 70 萬行的彩票組合中（指 _6/49 的_情況）的成功率超過 50%。該過濾器名為 _Del6_ 。我將過濾器 _Del6 的最小值設定為 700000——_ 現在我確實看到了樂透 _「數獨」_ 了。在 2015 年 11 月之前，我從未見過這種過濾器設定的聚類結果！太棒了！太棒了！
-   以賓州彩券 _5/43_ 樂透遊戲為例，我的 _D5_ 資料檔包含超過 400 萬種組合。遊戲的過濾器名為 _Del5_ ，它可以消除 _delta_ 格式的 5 個數字組。該過濾器的成功率超過 50%（每兩次抽獎中會有一次），D5 的彩票組合數超過 _50,000_ 條。 50,000 代表過濾器的中位數。當 _Del5 的最小值為 50,000_ 時，我可以在相當短的時間內看到樂透組合。
-   如果生成過程太慢（例如長時間沒有樂透組合），請停止程式並降低 _Del5_ 或 _Del6_ 的級別，直到您看到樂透組合為止。從輸出檔案末端（儲存洗牌組合的位置）選擇要播放的樂透組合。
    -   一些過濾器的問題是由簇（矩陣）中行的排序引起的。 6 個數字的行並非像我的軟體中其他組合產生方法那樣按升序排列。我決定不對簇行進行排序，因為應用_隨機播放_功能在彩票遊戲中根本行不通。隨機播放功能在選號（數字）遊戲中一直有效，因為這類遊戲從不排序（因為順序在這類彩票遊戲中至關重要）。
    -   2015 年 11 月，我決定再次做出改變。我注意到電腦速度真的快了很多——而且速度每個月都在提升。如上所述，我的樂透 5/6 軟體中的_**洗牌**_功能在 _Del5_ 和 _Del6_ 過濾器的等級較高（嚴格）時確實運作得更好。
    -   唯一不適用於_**洗牌**_功能的過濾器是那些會剔除_個別_號碼的過濾器。此彩票功能必須適用於**遊戲中的所有彩票號碼** 。以下彩票過濾器不適用於**洗牌功能** ： _One、Any1、Ver1_ 。程式中會顯示一個螢幕，警告使用者某些過濾器不得啟用。您也可以透過啟用設定非常寬鬆的過濾器來解決這個問題，這些過濾器通常會快速產生大量組合。如果您長時間沒有看到任何組合，則表示這些過濾器不適用於**洗牌功能** 。
-   我發現更多證據表明運行時間非常重要。也就是說，在前幾個組合中產生獲勝組合的機率非常低。我注意到，在這些類型的運行中，出現了 1827 個組合和 914 個組合（實際上，它們是叢集的組成部分）。沒有遇到重複的組合。
-   長期運行中獲勝組合的數量比隨機遊戲好兩倍。長期運行中獲勝組合的數量比低期運行結果好四倍（與獲勝機率相比）。
-   這樣一來，我就會說，耐心是有回報的，尤其是像**樂透洗牌**這樣的策略。大學集團絕對獲得了回報。
-   我想到了另一種克服困難的方法。例如，在你的資料檔中，找回 100 幅真實的圖畫。這表示你刪除檔案中最上面的 100 行，並用新名稱儲存（例如 _Data-6.2_ ）。同時建立一個新的 D6 檔案（例如 _D6.2_ ）。運行 **shuffle** 函數生成聚類。
-   像處理資料檔案一樣，按升序對輸出檔案進行排序。聚類結果將無序保存到磁碟。現在，將輸出檔案與原始結果檔案（特別是與運行隨機播放前刪除的 100 張彩票）進行比較。如果您沒有看到任何大獎（例如中頭獎或二等獎），那麼現在就可以從該輸出檔中抽取聚類結果了。
-   這就像存錢買100次彩票，或者說存了大約兩年的錢。記住，英國大學彩票集團的彩票購買時間更長…
    
    中獎號碼為： 15、18、23、31、37、49。  
    此組合透過 _**LotWon**_ 樂透軟體中的_內部過濾器_或 **MDIEditor Lotto** 中的_內部過濾器_ 。  
    是
    
    ![Lotto Sudoku or Sudoku Lotto?](https://saliu.com/HLINE.gif)
    
    <big>•</big> 那個我並不熱衷的謎題或遊戲—— 數獨 ——確實和我的樂透組合很像。 （毋庸置疑， _數獨_這玩意兒在我於 1990-1991 年將樂透組合或洗牌軟體作為共享軟體發布幾年後才開始流行起來。 _數獨_在 2005 年才開始流行起來。組合（或 _9x9 數獨網格_ ）中的所有數字都是唯一的。我的老樂透朋友們。然不會錯過_這種_相似之處。
    
    獨特的樂透組合（包含所有樂透號碼且所有號碼都不同的組合）的數量遠遠超過可能的數_獨_網格總數。如今的電腦簡直就像不會走路的嬰兒，卻被要求與成年人進行奧運短跑比賽！我提供了一些關於樂透轉盤可能_轉位_ （旋轉等）次數的精確計算。請閱讀：
    
    -   [_**版權所有 © 樂透、樂透號碼、組合、輪盤、數學關係、公式、方程式**_](https://saliu.com/copyright.html) 。
    
    樂透彩票總號碼簇的計算涉及階乘（例如 49！）。如今的電腦只能產生隨機樂透彩票號碼簇。產生字典順序排列的號碼簇需要很多年的時間。我的機率程式 **Shuffle** 也可以產生樂透彩票號碼簇。這個機率軟體也會將所有樂透彩票號碼_打亂_ ，並將它們隨機排列在一個向量（行）中。例如，49 個號碼的向量可以分成 7 行，每行 7 個號碼。因此，一個 49 個號碼的向量被轉換為 7×7 的矩陣。
    
    我的組合軟體 **PermuteCombine** 可能是目前唯一能夠產生所有可能的數獨網格的程式。實際上， **PermuteCombine** 中的 _「排列」_ 功能完全可以完成這項艱鉅的任務（前提是擁有一台性能強大的電腦）。
    
    如果你想玩_數獨_ ——為什麼要玩這麼頻繁？玩_全樂透號碼_組合不是更有回報嗎？你甚至可以排除過去出現過的 6 個數字組合：它們重複出現的機率在一生中幾乎可以忽略不計。同時，玩全樂透號碼組合則提供了遠超想像的贏大錢的機會。
    
    ![There is a connection between lotto and Sudoku.](https://saliu.com/HLINE.gif)[](https://saliu.com/content/lottery.html)
    
    ## [<u><span lang="zh-TW"><span data-immersive-translate-translation-element-mark="1"><span data-immersive-translate-translation-element-mark="1">彩票軟體、樂透轉盤資源</span></span></span></u>](https://saliu.com/content/lottery.html)
    
    -   [_**樂透軟體**_ ： _**樂透 6/49 遊戲中的**_ **7×7 數字組合** （矩陣、群集）](https://saliu.com/7by7-lotto-6-49.html) 。  
        此處介紹的彩票策略有所改善：對樂透 6/49 號碼進行打亂或隨機化。產生每個組合包含 7 個樂透號碼的完美平方或矩陣，總共 7 行。
    -   高效能 [_**Lotto-6 軟體**_](https://saliu.com/lotto6-software.html) 。
    -   主要的[_**樂透、樂透、軟體、策略**_](https://saliu.com/LottoWin.htm)頁面。  
        提供創建免費中獎彩券、彩券策略和基於數學系統的軟體。取得您的彩券系統或輪盤、最佳彩券、彩券軟體、組合和中獎號碼。
    -   [_**樂透、彩票軟體、Excel 電子表格：程式設計、策略**_](https://saliu.com/Newsgroups.htm) 。  
        閱讀一篇關於 Excel 電子表格在彩票和樂透軟體、系統和策略開發中應用的真實分析。本文將 Excel 分析與作者 _Parpaluck_ 編寫的強大彩票和樂透軟體結合。
    -   [_**<u>MDIEditor Lotto WE</u> ：教學、軟體使用者指南、手冊、書籍、電子書**_](https://saliu.com/MDI-lotto-guide.html) 。  
        ~ 也適用於 LotWon 彩票、樂透軟體；以及 Powerball、Mega Millions、Euromillions。
    -   [_**視覺化教學、書籍、手冊：彩票軟體、樂透應用程式、程式**_](https://saliu.com/forum/lotto-book.html) 。
    -   _「我的王國有一個好的樂透教學！」_ [_**樂透，彩票策略教學**_](https://saliu.com/bbs/messages/818.html) 。
    -   [_**文件、幫助：MDIEditor Lotto WE、彩票軟體、策略教學**_](https://saliu.com/mdi_lotto.html) 。
    -   [**<u>彩票跳過系統 </u>**](https://saliu.com/skip-strategy.html) ： _**樂透、強力球、超級百萬、歐洲百萬彩票**_ 。
    -   [_**彩券實用軟體**_](https://saliu.com/lottery-utility.html) ：Pick-3、4 彩券、Lotto-5、6、強力球、超級百萬、歐洲百萬。
    -   [_**彩票軟體中的實用彩票過濾**_](https://saliu.com/filters.html) 。
    -   [_**大獎彩券策略：12 個數字組合、樂透 6 輪、先鋒軟體**_](https://saliu.com/lotto-jackpot-lost.html) 。
    -   下載[**彩券軟體，樂透軟體**](https://saliu.com/infodown.html) 。
    
    ![University Professors played sixline lotto: All the numbers in the 6-49 game.](https://saliu.com/HLINE.gif)
    
    **[首頁](https://saliu.com/index.htm) | [搜尋](https://saliu.com/Search.htm) | [新文章](https://saliu.com/bbs/index.html) | [基本公式](https://saliu.com/formula.htm) | [賠率產生器](https://saliu.com/calculator_generator.html) | [目錄](https://saliu.com/content/index.html) | [論壇](https://forums.saliu.com/) | [網站地圖](https://saliu.com/sitemap/index.html)**
    
    ![Lottery software generates all numbers in lotto games in Sudoku grids like.](https://saliu.com/images/HLINE.gif)
