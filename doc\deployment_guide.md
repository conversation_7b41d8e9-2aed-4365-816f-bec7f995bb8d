# Wonder Grid Lottery System - 部署指南

## 📋 目錄

1. [部署概述](#部署概述)
2. [開發環境部署](#開發環境部署)
3. [測試環境部署](#測試環境部署)
4. [生產環境部署](#生產環境部署)
5. [容器化部署](#容器化部署)
6. [雲端部署](#雲端部署)
7. [監控和維護](#監控和維護)

---

## 部署概述

Wonder Grid Lottery System 支援多種部署方式，從單機開發環境到大規模生產環境。

### 🎯 部署架構

```
┌─────────────────────────────────────────────────────────────┐
│                    Wonder Grid System                        │
├─────────────────────────────────────────────────────────────┤
│  應用層    │ Julia Runtime + Wonder Grid Core               │
├─────────────────────────────────────────────────────────────┤
│  快取層    │ Multi-Level Cache + Memory Pool                │
├─────────────────────────────────────────────────────────────┤
│  數據層    │ Historical Data + Compact Storage              │
├─────────────────────────────────────────────────────────────┤
│  監控層    │ Performance Monitor + Auto Tuner              │
└─────────────────────────────────────────────────────────────┘
```

### 部署選項

| 部署類型 | 適用場景 | 複雜度 | 性能 | 可擴展性 |
|----------|----------|--------|------|----------|
| **單機部署** | 開發、小規模使用 | 低 | 中 | 低 |
| **叢集部署** | 中大規模使用 | 中 | 高 | 中 |
| **容器部署** | 微服務、CI/CD | 中 | 高 | 高 |
| **雲端部署** | 大規模、高可用 | 高 | 很高 | 很高 |

---

## 開發環境部署

### 快速開發設置

#### 1. 基本開發環境

```bash
# 克隆倉庫
git clone https://github.com/your-repo/wonder-grid-lottery-system.git
cd wonder-grid-lottery-system

# 設置開發環境
export JULIA_NUM_THREADS=auto
export WONDER_GRID_ENV=development
export WONDER_GRID_LOG_LEVEL=debug

# 啟動開發模式
julia -t auto --project=.
```

#### 2. 開發配置文件

創建 `config/development.jl`：

```julia
# 開發環境配置
const DEV_CONFIG = Dict(
    # 性能配置（開發優化）
    "performance" => Dict(
        "enable_caching" => true,
        "cache_size_multiplier" => 0.5,  # 較小的快取以節省記憶體
        "enable_compact_data" => false,  # 開發時禁用以便調試
        "auto_cleanup" => true,
        "cleanup_interval_minutes" => 5  # 更頻繁的清理
    ),
    
    # 監控配置
    "monitoring" => Dict(
        "enable_performance_monitoring" => true,
        "enable_detailed_logging" => true,
        "log_level" => "debug",
        "enable_auto_tuning" => false  # 開發時禁用自動調優
    ),
    
    # 數據配置
    "data" => Dict(
        "max_historical_records" => 1000,  # 限制數據量以加快載入
        "enable_data_validation" => true,
        "backup_enabled" => false
    )
)
```

#### 3. 開發工具設置

```julia
# 開發輔助函數
function dev_quick_test()
    println("🧪 快速開發測試...")
    
    # 創建小型測試數據
    test_data = [
        LotteryDraw([1, 5, 10, 15, 20], Date(2023, 1, 1), 1),
        LotteryDraw([2, 6, 11, 16, 21], Date(2023, 1, 2), 2)
    ]
    
    # 測試基本功能
    engine = OptimizedFilterEngine(test_data, use_compact_data=false)
    result = calculate_skip_optimized(engine, 1)
    
    println("✅ 基本功能測試通過: Skip = $result")
    return true
end

function dev_performance_check()
    println("📊 開發性能檢查...")
    
    summary = get_global_performance_summary()
    println("性能等級: $(summary["performance_grade"])")
    
    return summary
end
```

---

## 測試環境部署

### 測試環境設置

#### 1. 測試環境配置

創建 `config/testing.jl`：

```julia
# 測試環境配置
const TEST_CONFIG = Dict(
    # 性能配置（接近生產環境）
    "performance" => Dict(
        "enable_caching" => true,
        "cache_size_multiplier" => 0.8,
        "enable_compact_data" => true,
        "auto_cleanup" => true,
        "cleanup_interval_minutes" => 10
    ),
    
    # 監控配置
    "monitoring" => Dict(
        "enable_performance_monitoring" => true,
        "enable_detailed_logging" => true,
        "log_level" => "info",
        "enable_auto_tuning" => true,
        "tuning_interval_minutes" => 30
    ),
    
    # 測試配置
    "testing" => Dict(
        "enable_comprehensive_tests" => true,
        "enable_performance_tests" => true,
        "enable_stress_tests" => true,
        "test_data_size" => "large"
    )
)
```

#### 2. 自動化測試部署

創建 `scripts/deploy_testing.sh`：

```bash
#!/bin/bash

echo "🧪 部署測試環境..."

# 設置環境變量
export WONDER_GRID_ENV=testing
export JULIA_NUM_THREADS=auto
export WONDER_GRID_LOG_LEVEL=info

# 創建測試目錄
mkdir -p logs/testing
mkdir -p data/testing
mkdir -p reports/testing

# 載入測試配置
julia -t auto -e "
include(\"config/testing.jl\")
include(\"src/wonder_grid_system.jl\")

# 運行完整測試套件
println(\"🚀 開始測試環境驗證...\")
include(\"test/run_all_tests.jl\")

# 生成測試報告
println(\"📊 生成測試報告...\")
include(\"test/generate_test_report.jl\")

println(\"✅ 測試環境部署完成\")
"
```

#### 3. 持續整合設置

創建 `.github/workflows/testing.yml`：

```yaml
name: Testing Environment Deployment

on:
  push:
    branches: [ develop, testing ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        julia-version: ['1.8', '1.11']
        
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Julia
      uses: julia-actions/setup-julia@v1
      with:
        version: ${{ matrix.julia-version }}
        
    - name: Configure Environment
      run: |
        export JULIA_NUM_THREADS=auto
        export WONDER_GRID_ENV=testing
        
    - name: Run Tests
      run: |
        julia -t auto test/run_all_tests.jl
        
    - name: Generate Reports
      run: |
        julia -t auto test/generate_test_report.jl
        
    - name: Upload Test Results
      uses: actions/upload-artifact@v3
      with:
        name: test-results-${{ matrix.julia-version }}
        path: reports/
```

---

## 生產環境部署

### 生產環境設置

#### 1. 生產配置

創建 `config/production.jl`：

```julia
# 生產環境配置
const PROD_CONFIG = Dict(
    # 高性能配置
    "performance" => Dict(
        "enable_caching" => true,
        "cache_size_multiplier" => 1.5,  # 更大的快取
        "enable_compact_data" => true,
        "auto_cleanup" => true,
        "cleanup_interval_minutes" => 30,
        "enable_parallel_computing" => true,
        "max_parallel_tasks" => Sys.CPU_THREADS * 2
    ),
    
    # 監控配置
    "monitoring" => Dict(
        "enable_performance_monitoring" => true,
        "enable_detailed_logging" => false,  # 減少日誌開銷
        "log_level" => "warn",
        "enable_auto_tuning" => true,
        "tuning_interval_minutes" => 60,
        "enable_alerts" => true
    ),
    
    # 安全配置
    "security" => Dict(
        "enable_input_validation" => true,
        "enable_rate_limiting" => true,
        "max_requests_per_minute" => 1000,
        "enable_audit_logging" => true
    ),
    
    # 可靠性配置
    "reliability" => Dict(
        "enable_health_checks" => true,
        "health_check_interval_seconds" => 30,
        "enable_graceful_shutdown" => true,
        "max_memory_usage_gb" => 16,
        "enable_automatic_restart" => true
    )
)
```

#### 2. 生產部署腳本

創建 `scripts/deploy_production.sh`：

```bash
#!/bin/bash

echo "🚀 部署生產環境..."

# 檢查權限
if [[ $EUID -eq 0 ]]; then
   echo "❌ 不要以 root 身份運行生產部署"
   exit 1
fi

# 設置生產環境變量
export WONDER_GRID_ENV=production
export JULIA_NUM_THREADS=auto
export WONDER_GRID_LOG_LEVEL=warn
export JULIA_MAX_MEMORY=16G

# 創建生產目錄結構
sudo mkdir -p /opt/wonder-grid
sudo mkdir -p /var/log/wonder-grid
sudo mkdir -p /var/lib/wonder-grid/data
sudo mkdir -p /var/lib/wonder-grid/cache
sudo mkdir -p /etc/wonder-grid

# 設置權限
sudo chown -R wonder-grid:wonder-grid /opt/wonder-grid
sudo chown -R wonder-grid:wonder-grid /var/log/wonder-grid
sudo chown -R wonder-grid:wonder-grid /var/lib/wonder-grid

# 複製系統文件
sudo cp -r src/ /opt/wonder-grid/
sudo cp -r config/ /opt/wonder-grid/
sudo cp config/production.jl /etc/wonder-grid/config.jl

# 創建系統服務
sudo tee /etc/systemd/system/wonder-grid.service > /dev/null <<EOF
[Unit]
Description=Wonder Grid Lottery System
After=network.target

[Service]
Type=simple
User=wonder-grid
Group=wonder-grid
WorkingDirectory=/opt/wonder-grid
Environment=JULIA_NUM_THREADS=auto
Environment=WONDER_GRID_ENV=production
ExecStart=/usr/local/bin/julia -t auto src/wonder_grid_system.jl
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
EOF

# 啟用並啟動服務
sudo systemctl daemon-reload
sudo systemctl enable wonder-grid
sudo systemctl start wonder-grid

echo "✅ 生產環境部署完成"
echo "📊 檢查狀態: sudo systemctl status wonder-grid"
echo "📋 查看日誌: sudo journalctl -u wonder-grid -f"
```

#### 3. 健康檢查

創建 `scripts/health_check.jl`：

```julia
#!/usr/bin/env julia

# 生產環境健康檢查
function health_check()
    println("🏥 Wonder Grid 系統健康檢查")
    println("=" ^ 40)
    
    health_status = Dict{String, Bool}()
    
    # 檢查系統基本功能
    try
        # 測試基本計算
        test_data = [LotteryDraw([1, 5, 10, 15, 20], Date(2023, 1, 1), 1)]
        engine = OptimizedFilterEngine(test_data)
        result = calculate_skip_optimized(engine, 1)
        health_status["basic_computation"] = true
        println("✅ 基本計算功能正常")
    catch e
        health_status["basic_computation"] = false
        println("❌ 基本計算功能異常: $e")
    end
    
    # 檢查性能監控
    try
        summary = get_global_performance_summary()
        health_status["performance_monitoring"] = true
        println("✅ 性能監控正常")
    catch e
        health_status["performance_monitoring"] = false
        println("❌ 性能監控異常: $e")
    end
    
    # 檢查記憶體使用
    try
        memory_gb = Sys.total_memory() / (1024^3)
        available_gb = Sys.free_memory() / (1024^3)
        memory_usage_percent = (1 - available_gb / memory_gb) * 100
        
        if memory_usage_percent < 90
            health_status["memory_usage"] = true
            println("✅ 記憶體使用正常: $(round(memory_usage_percent, digits=1))%")
        else
            health_status["memory_usage"] = false
            println("⚠️ 記憶體使用過高: $(round(memory_usage_percent, digits=1))%")
        end
    catch e
        health_status["memory_usage"] = false
        println("❌ 記憶體檢查異常: $e")
    end
    
    # 檢查執行緒
    thread_count = Threads.nthreads()
    if thread_count > 1
        health_status["threading"] = true
        println("✅ 多執行緒正常: $thread_count 個執行緒")
    else
        health_status["threading"] = false
        println("⚠️ 多執行緒未啟用")
    end
    
    # 整體健康狀態
    healthy_components = count(values(health_status))
    total_components = length(health_status)
    health_percentage = (healthy_components / total_components) * 100
    
    println("\n📊 整體健康狀態: $(round(health_percentage, digits=1))%")
    
    if health_percentage >= 90
        println("🎉 系統狀態：優秀")
        exit(0)
    elseif health_percentage >= 70
        println("✅ 系統狀態：良好")
        exit(0)
    else
        println("❌ 系統狀態：需要關注")
        exit(1)
    end
end

# 執行健康檢查
health_check()
```

---

## 容器化部署

### Docker 部署

#### 1. Dockerfile

創建 `Dockerfile`：

```dockerfile
# 使用官方 Julia 映像
FROM julia:1.11

# 設置工作目錄
WORKDIR /app

# 設置環境變量
ENV JULIA_NUM_THREADS=auto
ENV WONDER_GRID_ENV=production
ENV JULIA_DEPOT_PATH=/app/.julia

# 安裝系統依賴
RUN apt-get update && apt-get install -y \
    git \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 複製源碼
COPY src/ ./src/
COPY config/ ./config/
COPY scripts/ ./scripts/

# 預編譯 Julia 包
RUN julia -e "using Pkg; Pkg.precompile()"

# 創建非 root 用戶
RUN useradd -m -u 1000 wondergrid
RUN chown -R wondergrid:wondergrid /app
USER wondergrid

# 健康檢查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD julia scripts/health_check.jl

# 暴露端口（如果有 Web 介面）
EXPOSE 8080

# 啟動命令
CMD ["julia", "-t", "auto", "src/wonder_grid_system.jl"]
```

#### 2. Docker Compose

創建 `docker-compose.yml`：

```yaml
version: '3.8'

services:
  wonder-grid:
    build: .
    container_name: wonder-grid-system
    restart: unless-stopped
    environment:
      - JULIA_NUM_THREADS=auto
      - WONDER_GRID_ENV=production
      - JULIA_MAX_MEMORY=8G
    volumes:
      - ./data:/app/data:ro
      - ./logs:/app/logs
      - wonder-grid-cache:/app/cache
    ports:
      - "8080:8080"
    healthcheck:
      test: ["CMD", "julia", "scripts/health_check.jl"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        limits:
          memory: 8G
          cpus: '4'
        reservations:
          memory: 4G
          cpus: '2'

  # 監控服務（可選）
  monitoring:
    image: prom/prometheus:latest
    container_name: wonder-grid-monitoring
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
    depends_on:
      - wonder-grid

volumes:
  wonder-grid-cache:
    driver: local
```

#### 3. 容器部署腳本

創建 `scripts/deploy_docker.sh`：

```bash
#!/bin/bash

echo "🐳 Docker 容器化部署..."

# 檢查 Docker
if ! command -v docker &> /dev/null; then
    echo "❌ Docker 未安裝"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose 未安裝"
    exit 1
fi

# 創建必要目錄
mkdir -p data logs monitoring

# 構建映像
echo "🔨 構建 Docker 映像..."
docker build -t wonder-grid-system:latest .

# 啟動服務
echo "🚀 啟動容器服務..."
docker-compose up -d

# 等待服務啟動
echo "⏳ 等待服務啟動..."
sleep 30

# 檢查服務狀態
echo "🔍 檢查服務狀態..."
docker-compose ps

# 檢查健康狀態
echo "🏥 檢查健康狀態..."
docker exec wonder-grid-system julia scripts/health_check.jl

echo "✅ Docker 部署完成"
echo "📊 查看日誌: docker-compose logs -f wonder-grid"
echo "🔧 管理命令:"
echo "  - 停止: docker-compose down"
echo "  - 重啟: docker-compose restart"
echo "  - 更新: docker-compose pull && docker-compose up -d"
```

---

## 雲端部署

### AWS 部署

#### 1. EC2 部署

```bash
#!/bin/bash
# AWS EC2 部署腳本

# 創建 EC2 實例
aws ec2 run-instances \
    --image-id ami-0c02fb55956c7d316 \
    --instance-type c5.2xlarge \
    --key-name wonder-grid-key \
    --security-group-ids sg-******** \
    --subnet-id subnet-******** \
    --user-data file://scripts/ec2-user-data.sh \
    --tag-specifications 'ResourceType=instance,Tags=[{Key=Name,Value=wonder-grid-production}]'
```

#### 2. ECS 部署

創建 `aws/ecs-task-definition.json`：

```json
{
  "family": "wonder-grid-system",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "2048",
  "memory": "8192",
  "executionRoleArn": "arn:aws:iam::account:role/ecsTaskExecutionRole",
  "taskRoleArn": "arn:aws:iam::account:role/ecsTaskRole",
  "containerDefinitions": [
    {
      "name": "wonder-grid",
      "image": "your-account.dkr.ecr.region.amazonaws.com/wonder-grid:latest",
      "essential": true,
      "portMappings": [
        {
          "containerPort": 8080,
          "protocol": "tcp"
        }
      ],
      "environment": [
        {
          "name": "JULIA_NUM_THREADS",
          "value": "auto"
        },
        {
          "name": "WONDER_GRID_ENV",
          "value": "production"
        }
      ],
      "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "/ecs/wonder-grid",
          "awslogs-region": "us-west-2",
          "awslogs-stream-prefix": "ecs"
        }
      },
      "healthCheck": {
        "command": ["CMD-SHELL", "julia scripts/health_check.jl"],
        "interval": 30,
        "timeout": 10,
        "retries": 3,
        "startPeriod": 60
      }
    }
  ]
}
```

### Kubernetes 部署

#### 1. Kubernetes 配置

創建 `k8s/deployment.yaml`：

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: wonder-grid-system
  labels:
    app: wonder-grid
spec:
  replicas: 3
  selector:
    matchLabels:
      app: wonder-grid
  template:
    metadata:
      labels:
        app: wonder-grid
    spec:
      containers:
      - name: wonder-grid
        image: wonder-grid-system:latest
        ports:
        - containerPort: 8080
        env:
        - name: JULIA_NUM_THREADS
          value: "auto"
        - name: WONDER_GRID_ENV
          value: "production"
        resources:
          requests:
            memory: "4Gi"
            cpu: "2"
          limits:
            memory: "8Gi"
            cpu: "4"
        livenessProbe:
          exec:
            command:
            - julia
            - scripts/health_check.jl
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          exec:
            command:
            - julia
            - scripts/health_check.jl
          initialDelaySeconds: 30
          periodSeconds: 10
        volumeMounts:
        - name: data-volume
          mountPath: /app/data
          readOnly: true
        - name: cache-volume
          mountPath: /app/cache
      volumes:
      - name: data-volume
        configMap:
          name: wonder-grid-data
      - name: cache-volume
        emptyDir: {}
---
apiVersion: v1
kind: Service
metadata:
  name: wonder-grid-service
spec:
  selector:
    app: wonder-grid
  ports:
  - protocol: TCP
    port: 80
    targetPort: 8080
  type: LoadBalancer
```

---

## 監控和維護

### 監控設置

#### 1. 系統監控

創建 `scripts/monitoring_setup.sh`：

```bash
#!/bin/bash

echo "📊 設置系統監控..."

# 安裝監控工具
sudo apt update
sudo apt install -y htop iotop nethogs

# 設置日誌輪轉
sudo tee /etc/logrotate.d/wonder-grid > /dev/null <<EOF
/var/log/wonder-grid/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 wonder-grid wonder-grid
    postrotate
        systemctl reload wonder-grid
    endscript
}
EOF

# 設置監控腳本
sudo tee /usr/local/bin/wonder-grid-monitor > /dev/null <<'EOF'
#!/bin/bash

# 檢查服務狀態
if ! systemctl is-active --quiet wonder-grid; then
    echo "❌ Wonder Grid 服務未運行"
    systemctl restart wonder-grid
fi

# 檢查記憶體使用
MEMORY_USAGE=$(free | grep Mem | awk '{printf "%.1f", $3/$2 * 100.0}')
if (( $(echo "$MEMORY_USAGE > 90" | bc -l) )); then
    echo "⚠️ 記憶體使用過高: ${MEMORY_USAGE}%"
fi

# 檢查磁碟空間
DISK_USAGE=$(df /var/lib/wonder-grid | tail -1 | awk '{print $5}' | sed 's/%//')
if [ "$DISK_USAGE" -gt 85 ]; then
    echo "⚠️ 磁碟空間不足: ${DISK_USAGE}%"
fi
EOF

chmod +x /usr/local/bin/wonder-grid-monitor

# 設置定時任務
(crontab -l 2>/dev/null; echo "*/5 * * * * /usr/local/bin/wonder-grid-monitor") | crontab -

echo "✅ 監控設置完成"
```

#### 2. 性能監控

創建 `scripts/performance_monitor.jl`：

```julia
#!/usr/bin/env julia

# 性能監控腳本
function performance_monitor()
    println("📈 Wonder Grid 性能監控")
    println("時間: $(now())")
    println("=" ^ 40)
    
    # 獲取系統性能摘要
    summary = get_global_performance_summary()
    
    # 輸出關鍵指標
    println("🎯 關鍵性能指標:")
    println("  性能等級: $(summary["performance_grade"])")
    println("  快取命中率: $(round(summary["cache_hit_rate"] * 100, digits=1))%")
    println("  平均延遲: $(round(summary["avg_skip_time_ms"], digits=2))ms")
    
    # 檢查警告條件
    warnings = []
    
    if summary["cache_hit_rate"] < 0.7
        push!(warnings, "快取命中率過低")
    end
    
    if summary["avg_skip_time_ms"] > 100
        push!(warnings, "平均延遲過高")
    end
    
    if !isempty(warnings)
        println("\n⚠️ 警告:")
        for warning in warnings
            println("  - $warning")
        end
    else
        println("\n✅ 所有指標正常")
    end
    
    # 記錄到日誌文件
    log_file = "/var/log/wonder-grid/performance.log"
    if isfile(dirname(log_file))
        open(log_file, "a") do f
            write(f, "$(now()): 性能等級=$(summary["performance_grade"]), 快取命中率=$(summary["cache_hit_rate"])\n")
        end
    end
end

# 執行監控
performance_monitor()
```

### 維護腳本

#### 1. 自動維護

創建 `scripts/maintenance.sh`：

```bash
#!/bin/bash

echo "🔧 Wonder Grid 系統維護..."

# 清理日誌
find /var/log/wonder-grid -name "*.log" -mtime +30 -delete
echo "✅ 清理舊日誌"

# 清理快取
julia -e "
include(\"/opt/wonder-grid/src/wonder_grid_system.jl\")
cleaned = auto_cleanup!(GLOBAL_MEMORY_POOL)
println(\"✅ 清理記憶體池: \$cleaned 個項目\")
"

# 檢查磁碟空間
df -h /var/lib/wonder-grid

# 更新系統（可選）
# sudo apt update && sudo apt upgrade -y

echo "✅ 系統維護完成"
```

#### 2. 備份腳本

創建 `scripts/backup.sh`：

```bash
#!/bin/bash

BACKUP_DIR="/backup/wonder-grid"
DATE=$(date +%Y%m%d_%H%M%S)

echo "💾 Wonder Grid 系統備份..."

# 創建備份目錄
mkdir -p "$BACKUP_DIR"

# 備份配置文件
tar -czf "$BACKUP_DIR/config_$DATE.tar.gz" /etc/wonder-grid/

# 備份數據文件
tar -czf "$BACKUP_DIR/data_$DATE.tar.gz" /var/lib/wonder-grid/data/

# 備份日誌（最近7天）
find /var/log/wonder-grid -name "*.log" -mtime -7 | tar -czf "$BACKUP_DIR/logs_$DATE.tar.gz" -T -

# 清理舊備份（保留30天）
find "$BACKUP_DIR" -name "*.tar.gz" -mtime +30 -delete

echo "✅ 備份完成: $BACKUP_DIR"
```

---

**🎉 部署完成！**

現在您已經掌握了 Wonder Grid Lottery System 的完整部署流程。根據您的需求選擇合適的部署方式：

- **開發環境**：快速設置，便於調試
- **測試環境**：接近生產，自動化測試
- **生產環境**：高性能，高可靠性
- **容器化**：可移植，易於管理
- **雲端部署**：可擴展，高可用

記得定期執行監控和維護腳本，確保系統穩定運行！
