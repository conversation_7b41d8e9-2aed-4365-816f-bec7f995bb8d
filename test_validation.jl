include("src/WonderGridLotterySystem.jl")
using .WonderGridLotterySystem

# Test data validation
println("Testing Data Validation System")
println("=" ^ 40)

# Test valid lottery numbers
valid_numbers = [1, 15, 23, 31, 39]
result = validate_lottery_numbers(valid_numbers)
println("Valid numbers $valid_numbers: $(result.is_valid) - $(result.message)")

# Test invalid numbers (out of range)
invalid_numbers1 = [1, 15, 23, 31, 40]
result = validate_lottery_numbers(invalid_numbers1)
println("Invalid numbers $invalid_numbers1: $(result.is_valid) - $(result.message)")

# Test invalid numbers (wrong count)
invalid_numbers2 = [1, 15, 23, 31]
result = validate_lottery_numbers(invalid_numbers2)
println("Invalid numbers $invalid_numbers2: $(result.is_valid) - $(result.message)")

# Test invalid numbers (duplicates)
invalid_numbers3 = [1, 15, 23, 23, 39]
result = validate_lottery_numbers(invalid_numbers3)
println("Invalid numbers $invalid_numbers3: $(result.is_valid) - $(result.message)")

# Test file validation
println("\nTesting File Validation:")
validator = DataValidator()
result = validate_data5_file(validator, "data/fan5.csv")
println("File validation result: $(result.is_valid) - $(result.message)")

# Test flexible validation (non-strict chronology)
println("\nTesting Flexible File Validation:")
result_flexible = validate_data5_file_flexible(validator, "data/fan5.csv", strict_chronology=false)
println("Flexible validation result: $(result_flexible.is_valid) - $(result_flexible.message)")

# Test data integrity
println("\nTesting Data Integrity:")
fm = FileManager()
data = read_data5_file(fm, "data/fan5.csv")
integrity_report = verify_data_integrity(data)

println("Data Integrity Report:")
println("  Total draws: $(integrity_report.total_draws)")
println("  Duplicates found: $(integrity_report.duplicates_found)")
println("  Invalid numbers: $(integrity_report.invalid_numbers)")
println("  Chronology issues: $(integrity_report.chronology_issues)")
println("  Overall valid: $(integrity_report.is_valid)")

if !isempty(integrity_report.issues)
    println("  Issues found:")
    for issue in integrity_report.issues[1:min(5, length(integrity_report.issues))]
        println("    - $issue")
    end
    if length(integrity_report.issues) > 5
        println("    ... and $(length(integrity_report.issues) - 5) more issues")
    end
end