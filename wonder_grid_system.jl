#!/usr/bin/env julia

"""
Wonder Grid Lottery System - Complete Integrated System
Main application entry point with full workflow orchestration
"""

using Dates
using Base.Threads

# Include all system components
include("src/wonder_grid_engine.jl")
include("src/lie_elimination.jl")
include("src/backtesting.jl")
include("src/performance_reporting.jl")
include("src/configuration.jl")
include("src/result_display.jl")
include("src/performance_optimization.jl")
include("src/concurrent_processing.jl")

"""
Complete Wonder Grid System integrating all components
"""
struct WonderGridSystem
    # Core engines
    wonder_grid_engine::WonderGridEngine
    lie_engine::Union{LIEEliminationEngine, Nothing}
    backtesting_engine::BacktestingEngine
    
    # Optimization engines
    optimized_engine::OptimizedWonderGridEngine
    concurrent_engine::Union{ConcurrentWonderGridEngine, Nothing}
    
    # Reporting and display
    performance_reporter::PerformanceReporter
    result_display_manager::ResultDisplayManager
    
    # System configuration
    config::WonderGridConfig
    config_manager::ConfigurationManager
    
    # System state
    historical_data::Vector{LotteryDraw}
    system_initialized::Bool
    initialization_time::Float64
    
    function WonderGridSystem(config_file::String = "wonder_grid_config.txt")
        println("🚀 Initializing Wonder Grid Lottery System...")
        start_time = time()
        
        # Load configuration
        config_manager = ConfigurationManager(config_file)
        config = config_manager.config
        
        # Validate configuration
        is_valid, errors = validate_configuration(config)
        if !is_valid
            println("❌ Configuration validation failed:")
            display_validation_errors(errors)
            error("Invalid configuration. Please fix errors and restart.")
        end
        
        println("✅ Configuration loaded and validated")
        
        # Initialize core engines
        wonder_grid_engine = WonderGridEngine()
        backtesting_engine = BacktestingEngine()
        
        # Initialize optimization engines
        optimized_engine = OptimizedWonderGridEngine()
        
        # Initialize concurrent engine if parallel processing is enabled
        concurrent_engine = nothing
        if config.enable_parallel_processing && nthreads() > 1
            concurrent_engine = ConcurrentWonderGridEngine(
                config.key_number !== nothing ? config.key_number : 13,
                min(config.max_threads, nthreads())
            )
            println("✅ Concurrent processing enabled ($(nthreads()) threads)")
        else
            println("ℹ️  Concurrent processing disabled")
        end
        
        # Initialize LIE engine if enabled
        lie_engine = nothing
        if config.include_lie_elimination
            # Will be initialized when historical data is loaded
            println("ℹ️  LIE elimination will be initialized with historical data")
        end
        
        # Initialize reporting and display
        performance_reporter = PerformanceReporter(wonder_grid_engine, backtesting_engine, lie_engine)
        result_display_manager = ResultDisplayManager(
            config.show_progress,
            config.output_format[1],  # Primary format
            config.output_directory
        )
        
        # Initialize system state
        historical_data = LotteryDraw[]
        initialization_time = time() - start_time
        
        println("✅ Wonder Grid System initialized in $(round(initialization_time, digits=3)) seconds")
        
        new(
            wonder_grid_engine,
            lie_engine,
            backtesting_engine,
            optimized_engine,
            concurrent_engine,
            performance_reporter,
            result_display_manager,
            config,
            config_manager,
            historical_data,
            true,
            initialization_time
        )
    end
end

"""
Load historical lottery data into the system
"""
function load_historical_data!(system::WonderGridSystem, data_source::Union{String, Vector{LotteryDraw}})
    println("📊 Loading historical lottery data...")
    
    if isa(data_source, String)
        # Load from file (implementation would depend on file format)
        println("  Loading from file: $data_source")
        # For now, generate sample data
        system.historical_data = generate_sample_historical_data(200)
    elseif isa(data_source, Vector{LotteryDraw})
        # Use provided data
        system.historical_data = data_source
    else
        error("Invalid data source type")
    end
    
    # Validate historical data
    if length(system.historical_data) < system.config.minimum_data_points
        @warn "Historical data has only $(length(system.historical_data)) points, minimum is $(system.config.minimum_data_points)"
    end
    
    # Initialize LIE engine if enabled
    if system.config.include_lie_elimination && !isempty(system.historical_data)
        system.lie_engine = LIEEliminationEngine(system.historical_data, system.config.lie_threshold)
        println("✅ LIE elimination engine initialized")
        
        # Update performance reporter
        system.performance_reporter = PerformanceReporter(
            system.wonder_grid_engine,
            system.backtesting_engine,
            system.lie_engine
        )
    end
    
    println("✅ Historical data loaded: $(length(system.historical_data)) draws")
    
    return system
end

"""
Generate sample historical data for testing
"""
function generate_sample_historical_data(count::Int)::Vector{LotteryDraw}
    draws = LotteryDraw[]
    base_date = Date("2020-01-01")
    
    for i in 1:count
        # Generate realistic lottery numbers with some patterns
        if i % 15 == 0
            # Every 15th draw has an arithmetic pattern
            start_num = rand(1:8)
            numbers = [start_num + j*4 for j in 0:4]
            numbers = [min(n, 39) for n in numbers]
        elseif i % 7 == 0
            # Every 7th draw has consecutive numbers
            start_num = rand(1:35)
            numbers = collect(start_num:(start_num+4))
        else
            # Regular random draw
            numbers = sort(rand(1:39, 5))
            while length(unique(numbers)) != 5
                numbers = sort(rand(1:39, 5))
            end
        end
        
        draw_date = base_date + Day(i * 3)  # Every 3 days
        push!(draws, LotteryDraw(draw_date, numbers))
    end
    
    return draws
end

"""
Execute complete Wonder Grid workflow
"""
function execute_wonder_grid_workflow(system::WonderGridSystem, key_number::Union{Int, Nothing} = nothing)
    println("🎯 Executing Wonder Grid Workflow")
    println("=" ^ 60)
    
    workflow_start_time = time()
    
    # Step 1: Determine key number
    if key_number === nothing
        if system.config.auto_key_selection
            key_number = interactive_key_selection(system.config)
        else
            key_number = system.config.key_number !== nothing ? system.config.key_number : 13
        end
    end
    
    println("🔑 Using key number: $key_number")
    
    # Step 2: Generate combinations
    println("\n📊 Step 2: Generating combinations...")
    
    combinations = if system.config.enable_parallel_processing && system.concurrent_engine !== nothing
        println("  Using concurrent processing...")
        generate_combinations_concurrent(system.concurrent_engine, key_number)
    else
        println("  Using optimized processing...")
        generate_combinations_optimized(system.optimized_engine, key_number)
    end
    
    if isempty(combinations)
        println("❌ No combinations generated for key number $key_number")
        return nothing
    end
    
    println("✅ Generated $(length(combinations)) combinations")
    
    # Step 3: Apply LIE elimination if enabled
    if system.config.include_lie_elimination && system.lie_engine !== nothing
        println("\n🚫 Step 3: Applying LIE elimination...")
        
        original_count = length(combinations)
        combinations = eliminate_combinations(system.lie_engine, combinations)
        filtered_count = length(combinations)
        
        elimination_rate = (original_count - filtered_count) / original_count * 100
        println("✅ LIE elimination complete: $original_count → $filtered_count ($(round(elimination_rate, digits=1))% eliminated)")
    else
        println("\n⏭️  Step 3: LIE elimination skipped")
    end
    
    # Step 4: Backtesting (if historical data available)
    backtest_result = nothing
    if !isempty(system.historical_data)
        println("\n📈 Step 4: Running backtesting...")
        
        # Use recent historical data for testing
        test_draws = system.historical_data[max(1, length(system.historical_data)-50):end]
        
        backtest_result = if system.config.enable_parallel_processing && system.concurrent_engine !== nothing
            println("  Using concurrent backtesting...")
            parallel_bt_engine = ParallelBacktestingEngine(min(4, nthreads()), 50)
            run_backtest_parallel(parallel_bt_engine, combinations, test_draws)
        else
            println("  Using standard backtesting...")
            run_backtest(system.backtesting_engine, combinations, test_draws)
        end
        
        println("✅ Backtesting complete")
        println("  Hit rates: 3/5=$(round(backtest_result.hit_rates["3/5"]*100, digits=3))%, 4/5=$(round(backtest_result.hit_rates["4/5"]*100, digits=3))%, 5/5=$(round(backtest_result.hit_rates["5/5"]*100, digits=3))%")
    else
        println("\n⏭️  Step 4: Backtesting skipped (no historical data)")
    end
    
    # Step 5: Generate performance report
    performance_report = nothing
    if backtest_result !== nothing
        println("\n📊 Step 5: Generating performance report...")
        
        if system.config.analysis_depth == "comprehensive"
            performance_report = generate_statistical_report(
                system.performance_reporter,
                key_number,
                system.historical_data[max(1, length(system.historical_data)-30):end]
            )
            println("✅ Comprehensive statistical report generated")
        else
            performance_report = generate_performance_report(
                system.performance_reporter,
                key_number,
                system.historical_data[max(1, length(system.historical_data)-30):end]
            )
            println("✅ Standard performance report generated")
        end
    else
        println("\n⏭️  Step 5: Performance reporting skipped")
    end
    
    # Step 6: Display and export results
    println("\n💾 Step 6: Displaying and exporting results...")
    
    # Display combinations
    if system.config.display_mode == "detailed"
        display_combinations(combinations[1:min(system.config.max_combinations_display, length(combinations))], 
                           "Wonder Grid Combinations (Key: $key_number)")
    elseif system.config.display_mode == "compact"
        println("  Generated $(length(combinations)) combinations for key $key_number")
    end
    
    # Export results
    if !isempty(combinations)
        export_metadata = Dict{String, Any}(
            "key_number" => key_number,
            "generation_method" => "Wonder Grid",
            "lie_elimination" => system.config.include_lie_elimination,
            "analysis_date" => string(Dates.now()),
            "total_combinations" => length(combinations),
            "system_version" => "1.0"
        )
        
        # Export in configured formats
        base_filename = "wonder_grid_key_$(key_number)_$(Dates.format(now(), "yyyymmdd_HHMMSS"))"
        
        exported_files = batch_export_combinations(
            combinations,
            base_filename,
            system.config.output_format,
            export_metadata
        )
        
        display_export_summary(exported_files, length(combinations))
    end
    
    # Step 7: Display performance report
    if performance_report !== nothing
        println("\n📈 Step 7: Performance Analysis")
        
        if system.config.analysis_depth == "comprehensive" && isa(performance_report, Dict)
            display_statistical_report(performance_report)
        elseif isa(performance_report, PerformanceReport)
            display_performance_report(performance_report)
        end
    end
    
    workflow_time = time() - workflow_start_time
    
    println("\n✅ Wonder Grid Workflow Complete!")
    println("⏱️  Total execution time: $(round(workflow_time, digits=2)) seconds")
    println("=" ^ 60)
    
    # Return workflow results
    return Dict{String, Any}(
        "key_number" => key_number,
        "combinations" => combinations,
        "backtest_result" => backtest_result,
        "performance_report" => performance_report,
        "execution_time" => workflow_time,
        "exported_files" => exported_files
    )
end

"""
Interactive system menu
"""
function interactive_system_menu(system::WonderGridSystem)
    while true
        println("\n🎯 Wonder Grid Lottery System - Main Menu")
        println("=" ^ 50)
        println("1. Execute Wonder Grid Workflow")
        println("2. Load Historical Data")
        println("3. System Configuration")
        println("4. Performance Analysis")
        println("5. System Status")
        println("6. Run System Tests")
        println("7. Exit")
        
        print("\nSelect option (1-7): ")
        choice = strip(readline())
        
        try
            if choice == "1"
                execute_workflow_menu(system)
            elseif choice == "2"
                load_data_menu(system)
            elseif choice == "3"
                configuration_menu(system)
            elseif choice == "4"
                performance_analysis_menu(system)
            elseif choice == "5"
                display_system_status(system)
            elseif choice == "6"
                run_system_tests_menu(system)
            elseif choice == "7"
                println("👋 Goodbye!")
                break
            else
                println("❌ Invalid option. Please select 1-7.")
            end
        catch e
            println("❌ Error: $e")
            println("Please try again.")
        end
    end
end

"""
Execute workflow menu
"""
function execute_workflow_menu(system::WonderGridSystem)
    println("\n🎯 Execute Wonder Grid Workflow")
    println("-" ^ 40)
    
    print("Enter key number (1-39) or press Enter for auto-selection: ")
    key_input = strip(readline())
    
    key_number = nothing
    if !isempty(key_input)
        try
            key_number = parse(Int, key_input)
            if !(1 <= key_number <= 39)
                println("❌ Key number must be between 1 and 39")
                return
            end
        catch
            println("❌ Invalid key number")
            return
        end
    end
    
    # Execute workflow
    result = execute_wonder_grid_workflow(system, key_number)
    
    if result !== nothing
        println("\n📊 Workflow Summary:")
        println("  Key Number: $(result["key_number"])")
        println("  Combinations: $(length(result["combinations"]))")
        println("  Execution Time: $(round(result["execution_time"], digits=2))s")
        
        if haskey(result, "exported_files")
            println("  Exported Files: $(length(result["exported_files"]))")
        end
    end
end

"""
Load data menu
"""
function load_data_menu(system::WonderGridSystem)
    println("\n📊 Load Historical Data")
    println("-" ^ 30)
    
    println("1. Generate sample data")
    println("2. Load from file (not implemented)")
    println("3. Use current data ($(length(system.historical_data)) draws)")
    
    print("Select option (1-3): ")
    choice = strip(readline())
    
    if choice == "1"
        print("Enter number of sample draws to generate [200]: ")
        count_input = strip(readline())
        count = isempty(count_input) ? 200 : parse(Int, count_input)
        
        sample_data = generate_sample_historical_data(count)
        load_historical_data!(system, sample_data)
        
    elseif choice == "2"
        println("⚠️  File loading not implemented yet")
        
    elseif choice == "3"
        if isempty(system.historical_data)
            println("❌ No historical data loaded")
        else
            println("✅ Using current data: $(length(system.historical_data)) draws")
        end
    else
        println("❌ Invalid option")
    end
end

"""
Configuration menu
"""
function configuration_menu(system::WonderGridSystem)
    println("\n⚙️  System Configuration")
    println("-" ^ 30)
    
    println("1. Display current configuration")
    println("2. Load configuration preset")
    println("3. Interactive configuration setup")
    println("4. Save current configuration")
    
    print("Select option (1-4): ")
    choice = strip(readline())
    
    if choice == "1"
        display_configuration(system.config)
        
    elseif choice == "2"
        println("Available presets: beginner, standard, advanced, performance")
        print("Enter preset name: ")
        preset_name = strip(readline())
        
        if preset_name in ["beginner", "standard", "advanced", "performance"]
            system.config = get_configuration_preset(preset_name)
            println("✅ Configuration preset '$preset_name' loaded")
        else
            println("❌ Invalid preset name")
        end
        
    elseif choice == "3"
        system.config = interactive_configuration_setup()
        println("✅ Interactive configuration complete")
        
    elseif choice == "4"
        save_configuration!(system.config_manager)
        
    else
        println("❌ Invalid option")
    end
end

"""
Performance analysis menu
"""
function performance_analysis_menu(system::WonderGridSystem)
    println("\n📈 Performance Analysis")
    println("-" ^ 30)
    
    if isempty(system.historical_data)
        println("❌ No historical data loaded. Please load data first.")
        return
    end
    
    println("1. Single key analysis")
    println("2. Comparative analysis")
    println("3. System benchmark")
    
    print("Select option (1-3): ")
    choice = strip(readline())
    
    if choice == "1"
        print("Enter key number (1-39): ")
        key_input = strip(readline())
        
        try
            key_number = parse(Int, key_input)
            if 1 <= key_number <= 39
                println("Running analysis for key $key_number...")
                
                statistical_report = generate_statistical_report(
                    system.performance_reporter,
                    key_number,
                    system.historical_data[max(1, length(system.historical_data)-50):end]
                )
                
                display_statistical_report(statistical_report)
            else
                println("❌ Key number must be between 1 and 39")
            end
        catch
            println("❌ Invalid key number")
        end
        
    elseif choice == "2"
        println("Running comparative analysis...")
        
        test_keys = [7, 13, 21, 29]
        test_draws = system.historical_data[max(1, length(system.historical_data)-30):end]
        
        reports = generate_comparative_report(system.performance_reporter, test_keys, test_draws)
        display_comparative_analysis(reports)
        
    elseif choice == "3"
        println("Running system benchmark...")
        
        # Benchmark different approaches
        key_number = 13
        iterations = 5
        
        benchmark_results = benchmark_performance(key_number, iterations)
        
        println("Benchmark Results:")
        println("  Speedup Factor: $(round(benchmark_results["speedup_factor"], digits=2))x")
        println("  Improvement: $(round(benchmark_results["improvement_percentage"], digits=1))%")
        
    else
        println("❌ Invalid option")
    end
end

"""
Display system status
"""
function display_system_status(system::WonderGridSystem)
    println("\n📊 Wonder Grid System Status")
    println("=" ^ 40)
    
    println("System Information:")
    println("  Initialized: $(system.system_initialized)")
    println("  Initialization Time: $(round(system.initialization_time, digits=3))s")
    println("  Julia Version: $(VERSION)")
    println("  Available Threads: $(nthreads())")
    
    println("\nConfiguration:")
    println("  Analysis Depth: $(system.config.analysis_depth)")
    println("  LIE Elimination: $(system.config.include_lie_elimination)")
    println("  Parallel Processing: $(system.config.enable_parallel_processing)")
    println("  Output Formats: $(join(system.config.output_format, ", "))")
    
    println("\nData Status:")
    println("  Historical Draws: $(length(system.historical_data))")
    if !isempty(system.historical_data)
        date_range = (minimum([d.draw_date for d in system.historical_data]), 
                     maximum([d.draw_date for d in system.historical_data]))
        println("  Date Range: $(date_range[1]) to $(date_range[2])")
    end
    
    println("\nEngine Status:")
    println("  Wonder Grid Engine: ✅ Ready")
    println("  LIE Engine: $(system.lie_engine !== nothing ? "✅ Ready" : "⚠️  Not initialized")")
    println("  Backtesting Engine: ✅ Ready")
    println("  Optimized Engine: ✅ Ready")
    println("  Concurrent Engine: $(system.concurrent_engine !== nothing ? "✅ Ready" : "⚠️  Disabled")")
    
    # Cache statistics
    cache_stats = get_cache_stats()
    println("\nCache Status:")
    println("  Total Cached Items: $(cache_stats["total_cached_items"])")
    println("  FFG Cache: $(cache_stats["ffg_cache_size"]) entries")
    
    if system.concurrent_engine !== nothing
        concurrent_cache_stats = get_concurrent_cache_stats()
        println("  Concurrent Cache: $(concurrent_cache_stats["total_cached_items"]) entries")
    end
end

"""
Run system tests menu
"""
function run_system_tests_menu(system::WonderGridSystem)
    println("\n🧪 System Tests")
    println("-" ^ 20)
    
    println("1. Quick system test")
    println("2. Comprehensive unit tests")
    println("3. Integration tests")
    println("4. Performance tests")
    
    print("Select option (1-4): ")
    choice = strip(readline())
    
    if choice == "1"
        run_quick_system_test(system)
    elseif choice == "2"
        println("Running comprehensive unit tests...")
        include("test_comprehensive_unit_tests.jl")
    elseif choice == "3"
        println("Running integration tests...")
        include("test_integration_end_to_end.jl")
    elseif choice == "4"
        println("Running performance tests...")
        run_performance_tests(system)
    else
        println("❌ Invalid option")
    end
end

"""
Run quick system test
"""
function run_quick_system_test(system::WonderGridSystem)
    println("🚀 Running Quick System Test...")
    
    test_start_time = time()
    
    try
        # Test 1: Configuration validation
        is_valid, errors = validate_configuration(system.config)
        @assert is_valid == true
        println("✅ Configuration validation")
        
        # Test 2: Combination generation
        combinations = generate_combinations_optimized(system.optimized_engine, 13)
        @assert isa(combinations, Vector{Vector{Int}})
        println("✅ Combination generation ($(length(combinations)) combinations)")
        
        # Test 3: LIE elimination (if available)
        if system.lie_engine !== nothing && !isempty(combinations)
            filtered = eliminate_combinations(system.lie_engine, combinations[1:min(10, length(combinations))])
            @assert isa(filtered, Vector{Vector{Int}})
            println("✅ LIE elimination")
        else
            println("⏭️  LIE elimination skipped")
        end
        
        # Test 4: Backtesting (if data available)
        if !isempty(system.historical_data) && !isempty(combinations)
            test_combos = combinations[1:min(5, length(combinations))]
            test_draws = system.historical_data[1:min(10, length(system.historical_data))]
            
            result = run_backtest(system.backtesting_engine, test_combos, test_draws)
            @assert isa(result, BacktestResult)
            println("✅ Backtesting")
        else
            println("⏭️  Backtesting skipped")
        end
        
        # Test 5: Export functionality
        if !isempty(combinations)
            test_combos = combinations[1:min(3, length(combinations))]
            export_combinations_csv(test_combos, "quick_test.csv", Dict("test" => true))
            @assert isfile("quick_test.csv")
            rm("quick_test.csv")
            println("✅ Export functionality")
        else
            println("⏭️  Export test skipped")
        end
        
        test_time = time() - test_start_time
        
        println("🎉 Quick System Test PASSED!")
        println("⏱️  Test time: $(round(test_time, digits=2)) seconds")
        
    catch e
        test_time = time() - test_start_time
        println("❌ Quick System Test FAILED: $e")
        println("⏱️  Test time: $(round(test_time, digits=2)) seconds")
    end
end

"""
Run performance tests
"""
function run_performance_tests(system::WonderGridSystem)
    println("⚡ Running Performance Tests...")
    
    # Test combination generation performance
    key_number = 13
    iterations = 3
    
    println("Testing combination generation performance...")
    
    # Standard engine
    start_time = time()
    for i in 1:iterations
        combinations = generate_combinations(system.wonder_grid_engine, key_number)
    end
    standard_time = (time() - start_time) / iterations
    
    # Optimized engine
    start_time = time()
    for i in 1:iterations
        combinations = generate_combinations_optimized(system.optimized_engine, key_number)
    end
    optimized_time = (time() - start_time) / iterations
    
    # Concurrent engine (if available)
    concurrent_time = 0.0
    if system.concurrent_engine !== nothing
        start_time = time()
        for i in 1:iterations
            combinations = generate_combinations_concurrent(system.concurrent_engine, key_number)
        end
        concurrent_time = (time() - start_time) / iterations
    end
    
    println("Performance Results:")
    println("  Standard Engine: $(round(standard_time * 1000, digits=2)) ms")
    println("  Optimized Engine: $(round(optimized_time * 1000, digits=2)) ms")
    if concurrent_time > 0
        println("  Concurrent Engine: $(round(concurrent_time * 1000, digits=2)) ms")
    end
    
    if optimized_time > 0
        speedup = standard_time / optimized_time
        println("  Optimization Speedup: $(round(speedup, digits=2))x")
    end
    
    if concurrent_time > 0 && standard_time > 0
        concurrent_speedup = standard_time / concurrent_time
        println("  Concurrent Speedup: $(round(concurrent_speedup, digits=2))x")
    end
end

"""
Main application entry point
"""
function main()
    println("🎯 Wonder Grid Lottery System v1.0")
    println("=" ^ 50)
    
    try
        # Initialize system
        system = WonderGridSystem()
        
        # Load sample data if no historical data exists
        if isempty(system.historical_data)
            println("📊 Loading sample historical data...")
            load_historical_data!(system, generate_sample_historical_data(100))
        end
        
        # Start interactive menu
        interactive_system_menu(system)
        
    catch e
        println("❌ System initialization failed: $e")
        println("Please check your configuration and try again.")
    end
end

# Run main application if this file is executed directly
if abspath(PROGRAM_FILE) == @__FILE__
    main()
end