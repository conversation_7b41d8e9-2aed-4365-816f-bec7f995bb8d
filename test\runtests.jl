using Test
using WonderGridLotterySystem

@testset "WonderGridLotterySystem.jl" begin
    @testset "Core Types" begin
        # Test basic type construction
        draw = LotteryDraw([1, 15, 23, 31, 39], Date("2022-05-01"), 1)
        @test draw.numbers == [1, 15, 23, 31, 39]
        @test draw.draw_date == Date("2022-05-01")
        @test draw.draw_id == 1
        
        # Test game type
        game = Lotto5_39()
        @test game.total_numbers == 39
        @test game.numbers_per_draw == 5
        
        # Test validation result
        result = ValidationResult(true, "Valid")
        @test result.is_valid == true
        @test result.message == "Valid"
    end
end