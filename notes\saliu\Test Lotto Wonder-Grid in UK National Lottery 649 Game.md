---
created: 2025-01-03T19:54:38 (UTC +08:00)
tags: [wonder grid,lotto,software,lottery,drawings,draws,data file,test,pairings,pairs,wins,test,]
source: https://saliu.com/bbs/messages/840.html
author: 
---

# Test Lotto Wonder-Grid in UK National Lottery 649 Game

> ## Excerpt
> A test of the famous lotto wonder-grid in UK National Lottery 649 lotto game done by <PERSON>, famous lottery player, system, software developer.

---
[![Pay Membership, Download Software: Lottery, Gambling, Roulette, Sports, Powerball, Mega Millions.](https://saliu.com/images/software-lottery-gambling.gif)](https://saliu.com/membership.html)  
![A test of the famous 'lotto wonder-grid' in UK National Lottery 649 lotto game. The tester is (in)famous lottery player <PERSON><PERSON>, a lottery system software developer.](https://saliu.com/bbs/messages/HLINE.gif)

Posted by <PERSON> _"Don't-Call-Me-<PERSON><PERSON>"_ <PERSON> on August 17, 2001.

<u>Task</u>

To test the new UTIL-6 to ascertain how well it hits at least 4/6 in the 'next' draw.

```
<span size="5" face="Courier New"><b><u>Method</u>

First of the all the filenames I am using are:
(they are explained in more detailed as I go through the methodology)

UKALL - My file of ALL previous 6/49 draws from the UK National Lottery
UKATEST – A file with the most recent 100 draws from UKALL
LPTESTxxx – A Least Pairing file from the previous 147 draws of UKALL*
ADTESTxxx – An All Doubles file from the previous 147 draws of UKALL
BPTESTxxx – A Best Pairing file from the previous 147 draws of UKALL
CHEKxxx – A file containing the 'check for winners' information
</b></span>
```

\*LPTEST is created each time for the purposes of potential future analyses, but is not used directly in the following test.

Initially I set up UKATEST by removing (cutting) the most recent 100 draws from my UKALL file and pasted them in my UKATEST file. To further iterate the point, my UKALL file HAD lotto 585 drawings in it. For the purposes of this task it now had 485 drawings, and UKATEST had 100 drawings.  
Then the following method was used to see if the new UTIL-6 'worked':

1) Run TOOLS choose option 6, then option 2

How many lotto balls: 49  
Enter which filename: UKALL  
How many past Drawings to Check: 147  
Name the All Doubles file: ADTESTxxx  
Name the Least Pairings file: LPTESTxxx  
Set upper limit for LP: 0  

Then exit TOOLS

2) Run (the new) UTIL-6 and choose option 'F'

How many balls: \[default\]  
Data file to use: UKALL  
How many past drawings: \[default\] (147)  

Name the main report: \[default\]  
Name the 'pairs' report: \[default\]  

Name the Best Pairs file: BPTESTxxx  
How many 'Best Pairs' to save: 5  
Enter the position to start: 1  
Name the Worst Pairs file: \[default\]  
How many Worst Pairs to save: \[default\]  

Then scroll down through the FREQ6 file that appears on the screen until you get the 'Run it again Y/N' prompt and hit 'N'

Then exit UTIL-6

3) At the DOS prompt type: edit UKATEST

Scroll down to the bottom and highlight the last line

Use CTRL X to cut the line

WITHOUT closing UKATEST choose File, Open, and type UKALL

At the top of UKALL hit RETURN (to give you a blank line at the top), then move the cursor up one line so that it is at the start of the blank line and use CTRL V to paste in the line that you cut from UKATEST.

Choose File, Exit and you will be asked if you want to 'save changes' to UKATEST and UKALL, so choose YES both times.

UKALL now has 'next weeks' draw at the top and ADTESTxxx, LPTESTxxx and BPTESTxxx were all created from what was known BEFORE that 'next' draw took place.

4) Now to check the results:

At the DOS prompt again type: UTIL-6

This time choose option 'W'

Output file to check: BPTESTxxx  
Real Data file to check: UKALL  

How many past drawings to check in Output file: 49

\------ // ----------// ----------//------------ in Real file: xxx

\[Note: it's up to you what you put here: you can check the whole of UKALL, or you can just check one draw, the top line, as that is the 'next' draw that you are hoping BPTESTxxx hits with at least 4 numbers – In my test I always had a look at the whole of UKALL anyway\]

Choose 'D' for the Output Device  
Name the Output File: CHEKxxx  

Then exit UTIL-6

5) At the DOS prompt type: edit CHEKxxx and look through the file to see if you had any winners in LINE 1.

Then exit back to the DOS prompt

6) Type: edit ADTESTxxx

This file, remember, was created earlier BEFORE you put the 'next' draw into UKALL, and lists all the numbers from 1-49 and how many times they had hit with each other in the previous 147 draws.

Now, on a piece of paper draw the following table: (let's presume that the 'next' draws numbers, i.e. the new first line at the top of UKALL), were: 5, 22, 34, 38, 43, 46)

```
<span size="5" face="Courier New"><b>
2234384346
5
22
34
38
43
</b></span>
```

So you have the last five numbers across the top, and the first five number down the side.

Now look at your ADTESTxxx file – go to the number 5 section and check how many times it was paired with 22, 34, 38, 43 and 46. And enter each of the values in the appropriate box:

```
<span size="5" face="Courier New"><b>
2234384346
5 2 0 3 2 2
22
34
38
43
</b></span>
```

Then check all the other numbers – OBVIOUSLY you don't check how many times, for example, 22 came out with 22!!!!!!! (Every time or never, depending on how you view life!)

And you will end up with a grid that looks something like this:

```
<span size="5" face="Courier New"><b>
2234384346
5 2 0 3 2 2
22 1 2 1 5
34 1 1 2
38 0 7
43 4

TOTAL HITS: 33WINNERS: 0
</b></span>
```

You can see above that I have made a note under the table that shows what was the total number of hits (each number with each number), and whether the CHEKxxx file showed any winners in line 1. You might as well do this, as it makes referencing later on, if you want to look back over everything a lot easier.

7) Now go back and repeat steps 1-6, incrementing each time the number of your ADTESTxxx, LPTESTxxx, BPTESTxxx and CHEKxxx files, until you have completed the 100 tests.

```
<span size="5" face="Courier New"><b><i>Results</i>

The results were very interesting, though a little disappointing:

In the 100 test draws there were:

Four x 4/6 winners
One x 5/6 winners
None x 6/6 winners
</b></span>
```

That is on average a 1 in 20 draw hit rate. Now if you consider that an average 4/6 wins you around £75 and a 5/6 wins you around £1200 pounds, then you would have won around: £1500, BUT if you'd played every draw (at £49 a time) you would have spent £4900! A net loss of £3400!!! (Obviously there would have been quite a few 3/6 winners as well, so the deficit wouldn't have been quite that great, but a quick check through shows that there still aren't nearly enough 3/6 winners to even come close to breaking even if you played 49 lines each draw)

Looking back through the 'grids' of how many times each number had hit with each other number in the previous 147 draws, 88/100 had at least one zero pairing! That means that 88% of the time at least 1 number in any given draw has NEVER come out with any of one of the five other numbers in that draw in the last 147 draws! Interestingly, a break down of my 100 test draws (using real draws, remember!) shows the following:

88/100 draws have at least 1 number that has never paired with at least one other number in that draw:

```
<span size="5" face="Courier New"><b>Draws with Zero Pairs: (from previous 147 draws)

1 zero pair  :28
2 zero pairs:29
3 zero pairs:17
4 zero pairs:7
5 zero pairs:3
6 zero pairs:1
7 zero pairs:3

total draws:88
</b></span>
```

29 out of the total 100 draws (29% - nearly 1/3 draws) has 2 Zero pairings from the last 147 draws!

There are many other bits of information you can glean from doing this test, but I'll leave it there for now. It is a laborious test. It takes time. But it was worth doing – I have saved a load of money by doing this test first! The bags under my eyes are now big enough for the weekly shopping(!), but my wife hasn't left me yet!

_Conclusion_

When I first ran the new UTIL-6 on my 585 draw UKALL file and, hence, looked BACK at how many times the BEST6 file WOULD HAVE hit, it looked really impressive. But I thought: “Hold on, I am looking back here, IF I had been able to create that particular BEST6 file at the beginning of the lottery then I WOULD HAVE won the jackpot. But that is hindsight. And I need to develop (or try to) foresight”.

It came to me that, though the BEST6 file, takes each number and gives it its best five pairings from the previous 147 draws, IT DOES NOT check whether those 5 best pairs have ever come out with each other in the previous 147 draws. That is why I did this test. Predicting what comes next is a lot harder than looking back at what has already been.

I would have loved to have seen a 4/6 winner every draw in my test, with the occasional 5/6 and, who knows, a rare 6/6, but the results of my test prove that 88% of the time in any given draw there will be at least 1 zero pairing from the previous 147 draws (and amazingly 1 in approx 33 draws will have SEVEN zero pairings!!!!!).

So, it is no surprise that I only had 4 x 4/6 and 1 x 5/6 winners in a hundred draws because I was choosing the 5 best pairs for each number, but with no regard to how they relate TO EACH OTHER in as far as the previous 147 draws were concerned.

<u>Further Development</u>

Using the Pairs file from each draw, I have a theory that the five pairings around the MEDIAN will actually hit better than the five best pairings, in that they will not only relate to the primary number, but will also relate to each other better.

As yet though, I have no proof of this.

I hope this helps.

Nik Barker (royalty-name _Kulai_)

![Test lotto wonder-grid in UK National Lottery 649 lotto game.](https://saliu.com/bbs/messages/HLINE.gif)

## [Resources in Lottery Software, Systems, Lotto Wheeling](https://saliu.com/content/lottery.html)

-   The Main [_**Lotto, Lottery, Software, Strategy Systems**_](https://saliu.com/LottoWin.htm) Page.  
    Presenting software to create free winning lotto, lottery strategies, systems based on mathematics. Get your lotto systems or wheels, the best lottery, lotto software, combinations, winning numbers.
-   [_**Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies**_](https://saliu.com/Newsgroups.htm).  
    Read a genuine analysis of Excel spreadsheets applied to lottery and lotto software development, systems, and strategies. Combining Excel analysis with powerful lottery and lotto software programmed by this author, _Parpaluck_.
-   [_**<u>MDIEditor Lotto WE</u>: Tutorial, Software User Guide, Manual, Book, ebook**_](https://saliu.com/MDI-lotto-guide.html).  
    ~ Also applicable to LotWon lottery, lotto software; plus Powerball/Mega Millions, Euromillions.
-   [_**Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps, Programs**_](https://saliu.com/forum/lotto-book.html).
-   [_**<u>Skip Systems</u> Software**_](https://saliu.com/skip-strategy.html): _**Lotto, Lottery, Powerball, Mega Millions, Euromillions**_.
-   [**<u>Lottery Utility Software</u>**](https://saliu.com/lottery-utility.html): _**Pick-3, 4 Lottery, Lotto-5, 6, Powerball, Mega Millions, Thunderball, Euromillions**_.
-   [**Lotto wheels**](https://saliu.com/lotto_wheels.html) for lottery games drawing 5, 6, or 7 numbers.  
    The most advanced theory of lotto wheels or reduced lottery systems. Get also original lotto wheels for lotto games drawing 5, 6, or 7 numbers: Balanced, randomized, and free.
-   Download [**lottery software, lotto software**](https://saliu.com/infodown.html).

Comments:  

![Loto software for UK national lottery.](https://saliu.com/bbs/messages/HLINE.gif)

**[Home](https://saliu.com/index.htm) | [Search](https://saliu.com/Search.htm) | [New Writings](https://saliu.com/bbs/index.html) | [Software](https://saliu.com/infodown.html) | [Odds, Generator](https://saliu.com/calculator_generator.html) | [Contents](https://saliu.com/content/index.html) | [Forums](https://forums.saliu.com/) | [Sitemap](https://saliu.com/sitemap/index.html)**

![Ion Saliu: Software, Programs, Apps, Systems, Strategies.](https://saliu.com/bbs/messages/HLINE.gif)
