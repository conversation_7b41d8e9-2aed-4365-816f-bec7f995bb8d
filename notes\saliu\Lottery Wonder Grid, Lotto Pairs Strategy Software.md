---
created: 2025-07-24T21:26:40 (UTC +08:00)
tags: [strategy,strategies,winning,wonder,grid,wonder-grid,lotto,pairs,pairings,software,lottery,loto,loterie,loteria,systems,frequency,numbers,jackpot,combinations,analysis,]
source: https://saliu.com/bbs/messages/grid.html
author: 
---

# Lottery Wonder Grid, Lotto Pairs Strategy Software

> ## Excerpt
> Wonder grid is a powerful lottery strategy based on lotto numbers and their top pairings (pairs) generated by LotWon lottery software.

---
[Winning reports for the wonder grid: GridCheck632](https://saliu.com/bbs/messages/9.html).

I have done extensively more research in the field. In the lotto games, the top 4 (lotto-5) or top 5 (lotto-6) do not hit in the next draw in many, many thousands of draws or simulated combinations. The top 1-2-3-4-5 pairs may hit within a range of future draws, if the same wonder grid is played. My freeware GridCheck632 shows the winning strings and their skips for a tough lotto 6/69 game. The wonder grid does not hit in the next very few draws. I discovered that consecutive pairings such as 1-2-3-4 or 1-2-3-4-5 are no shows! Also, the pairings from draw to draw show real randomness, in the line of the real lottery draws themselves. Here is a sample report for PA lotto 5/39 game. The report shows each number in the draw and its pairings. For example, the first number in the draw came out with its pairs of indexes 4, 18, 21, 23. No 1-2-3-4!

Given a range of drawings, each lotto number shows a clear bias towards being drawn with the rest of the lotto numbers. The newest version of my lotto software Bright\* has a component that plots a special lotto pairing report: SkipPair\*. The following pairing report was done by function _P = Pairing Works_, then _R = Pairing Report_.

[![Lottery Utilities Software, Tools, Lotto Wheels, Systems.](https://saliu.com/ScreenImgs/lotto-pair-ranks.gif)](https://saliu.com/free-lotto-tools.html)

The analysis shows, however, that top pairings come out reasonably frequently. Instead of pairs 1-2-3-4, the range of pairs from 2 to 9 should be selected. They had lotto jackpot hits in draws #6 and #63 (the pairings marked with an \*). Three of the pair indexes are consecutive, but not all four. Also, the second report shows that none of the pairings was a repeat. The filter 'FOUR' covers the entire range of analysis (900+). The top 8 pairings had also several 4 winning numbers, when only three of the pair indexes were correct.

```
<span size="5" face="Courier New" color="#c5b358">                   LOTTO-5 Pairing Report by Draw
                   File: LOTTERY\LOTTO-5\PA-5
                   Draws Analyzed:  1000
                   Pairings Draw Range:  78

 Line      Num           Num           Num           Num           Num
 no.        1             2             3             4             5

    1   4 18 21 33    7 13 28 36   14 18 21 22    2 18 24 26   15 19 20 36
    2  12 17 26 35    9 14 23 36   23 25 35 36    3 10 11 29   17 19 25 27
    3  14 15 21 37   14 21 24 25   20 25 26 37   19 20 36 37   17 18 19 28
    4   8 16 17 30   13 22 30 33   18 19 25 27   15 22 23 33   21 25 32 37
    5  15 16 28 32    3 16 28 38    1  8 22 32    4  8 27 32    1  5  6 36
    6 * 2  6  7  8    7 20 22 32   12 22 30 36   20 29 30 34    5 12 26 27
...
   53   5  7  8 19    2 10 21 23    1  6 28 30   11 12 16 21   18 23 24 28
   61   4  9 23 33    6  7  8 10    3  6  9 22    5 20 23 37    5 12 21 25
   62   1  3  7 19    5  7 17 34    4 11 15 29    4  7 16 17    3 15 27 32
   63   5  8 22 26    4 10 35 36   14 27 33 37  * 2  7  8  9    9 11 25 38
   64  23 26 33 37    2 17 24 26   15 29 32 34   11 17 22 25    1 19 26 34
</span>
```

We can cut the COW (cost of winning) if following the skips. The top 8 pairings have waiting periods of around 50 lottery draws. The cost can be diminished further by using the 'SKIPS' as filters. See also my lotto software SKIPS5, SKIPS6. Better still: SKIPS and especially SkipSystem. The third report shows that draw #6 had a very nice skip string. It was possible to win the jackpot with just one combination! The draw #63 had also an easy to predict skip string. The wonder grid must be sorted before using the skips as eliminating conditions. Use my probability software Sorting for the task.

•• How to apply the wonder grid theory -

-   1) Follow the tactic of GridCheck632  
    
-   2) Create a wonder grid for pairs 2-9 (lotto-5) or 3-12 (lotto-6). Do not select all-consecutive indexes for a wonder grid. Do not select a repeat pairing string. Eliminate some combinations using the skips as filters. Allow a wait period between hits.  
    -   2.a) if you select pairings 3-6-7-9-10, create the wonder grid every time you play; the combinations may change from draw to draw;  
        
    -   2.b) you can play the same grid for the next draws, without changes;
-   3) You can select more than one pairing strings; for example 3-6-7-9-10 and 3-5-7-8-11; or more, and eliminate combinations using the skips or standard LotWon software filters.

••• I wrote in 2001 in [_**Wonder Grid: Worst-case scenarios**_](https://saliu.com/bbs/messages/720.html):  
_"By the way, applying the concept to the pick-3 game: the behavior is different. Getting a 'wonder-grid' of the best pick-3 lottery pairings results in 10 combinations. They offer acceptable results only as 'boxed' lottery combinations. Playing the 'wonder-grid' to win 'straight' is inconsistent. The best I have gotten so far for pick-3 is ELIMINATING the 'worst 6' pairings. The result is some 50-70 combinations with a lot of 'straight' hits. There is also a larger number of 'boxed' hits. The best result, however, is playing 'straight only' (disregard all the 'boxed' hits!)  
I eliminated also the 'worst 7 pairings'. For the most part, running POWER-3 results in 0 combinations. Sometimes, I get 1 or 2 straight combinations. I haven't hit yet, eliminating the 'worst 7' pairings. But, who knows! I'll check more draws."_

• That was then. Things have become much clearer now. In fact, playing the best 3 pairs while eliminating the worst 7 pairs is one proven strategy at beating not only random play handily, but also beating the monstrous house edge of 50% (for pick lotteries)! I create the final grid and play it for the next 100 draws. This appears to be the optimal range for the fruition of a pick-3 lottery wonder grid. There are two interesting triggers.

1) If the final grid consists of 18 or 19 combinations, it is best to play the same grid for the next 100 draws;  
2) If the grid consists of 10 or 11 combinations, the probability is higher the grid will hit the very next draw; if the grid does not hit, discard of it.

Here is an exemplification of playing case scenario 1.

```
<span size="5" face="Courier New" color="#c5b358">                   PICK-3 Lottery Winning Number Checking 
                   Files: OUT3 ( 19 ) against PA-3 ( 100 )

    Line   Combination                        Straight   Boxed 
     no.     Checked                           Winner    Winner

      1       0 4 9             in draw #                  22 
      1       0 4 9             in draw #                  77 
      2       0 9 4             in draw #                  22 
      2       0 9 4             in draw #                  77 
      4       1 1 6             in draw #                  2 
      4       1 1 6             in draw #        67 *
      6       1 6 1             in draw #        2 *
...
     16       8 1 6             in draw #                  93 
     17       8 6 1             in draw #                  93 
     18       9 0 4             in draw #                  22 
     18       9 0 4             in draw #                  77 
     19       9 4 0             in draw #                  22 
     19       9 4 0             in draw #                  77 

         Total Hits:                             5         19 
</span>
```

I get the same number of straight hits consistently, for either 18 or 19 combinations. No other strategy is involved. Even if playing the same grid every draw for the next 100 draws, the COW (cost of winning) is 1800 (1900). The five wins amount to 2500. The result is profit. Not only does this strategy beat random play, it also beats the monstrous house edge or house advantage imposed by the lottery (50%)! Random play will yield 1.8 straight wins (1800 / 1000).

Of course, the skips are omnipresent. Nothing can avoid skipping — it's mathematical! The strategy above does not hit immediately in most situations. The player can safely sit out 5 drawings between hits. But this pick-3 pair strategy should hit within the next 12-13 draws, based on my FFG calculations. The hit in draw #93 \* in the report above indicates a hit after 7 drawings (100 – 93).

I prefer changing the pairing sets after the first hit. I redo the wonder grid at the winning drawing. This is a very favorable case. I skipped 5 draws; I paid for 2 draws: $38. I won straight. I discard of the strategy. If I continued to play, I would skip 5 drawings again. 93 – 77 – 5 = 11. 18 \* 11 = $198: Still a good outcome.

Forget about beating the random expectation by better than 3 standard deviations! I'm happy scientifically, too. In the process, I discovered a better instrument to measure fluctuation in stochastic events. The new measurement is more precise and more useful than the standard deviation. Suffice for now to say that I call it _FFG deviation_. Vivat FFG! Vivat Numerus Omnipotentus!

If reversing and playing the worst three pairs while eliminating the top three pairs does not yield any straight wins for most cases. Also, there are significantly fewer boxed hits. Unquestionably, the top pairings show a significantly higher frequency in regards to future draws.

These discoveries are consistent with the Fundamental Formula of Gambling (FFG). Each number has a strong tendency to repeat after a number of trials less than or equal to the FFG median. Pairs of lottery numbers have necessarily the same tendency. Pairs of numbers that came out together have then the tendency to repeat more often for ranges shorter than the FFG median. The clear trend continues for a number of future trials. I noticed, in fact, that the trend goes both directions: future and past. I am not talking about the past as in curve fitting cases. I usually do a pairing for 20 draws (pick 3). I check the past BEYOND the 20 draws analyzed. The past 100 lottery draws show a trend similar to the one for the future 100 draws.

Almighty Number is the only reality.

I hope this new material responds to most questions I have received with regards the lottery wonder grid. I hope it is of real help.

Copyright © MMIII, Ion Saliu. All rights reserved worldwide.

![Lottery wonder-grid has new lotto pairing research, software.](https://saliu.com/bbs/messages/HLINE.gif)

## [Resources in Lottery, Software, Systems, Lotto Wheels, Strategies](https://saliu.com/content/lottery.html)

-   The Main [_**Lotto, Lottery, Software, Strategy, Systems**_](https://saliu.com/LottoWin.htm) Page.  
    Presenting software to create free winning lotto, lottery strategies, systems based on mathematics. Get your lotto systems or wheels, the best lottery, lotto software, combinations, winning numbers.
-   [Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies](https://saliu.com/Newsgroups.htm).  
    Read a genuine analysis of Excel spreadsheets applied to lottery and lotto software development, systems, and strategies. Combining Excel analysis with powerful lottery and lotto software programmed by this author, _Parpaluck_.
-   [_**<u>MDIEditor Lotto WE</u>: Lottery Software Manual, Book, ebook, Help**_](https://saliu.com/MDI-lotto-guide.html).  
    ~ Also applicable to LotWon lottery, lotto software; plus Powerball/Mega Millions, Euromillions.
-   [_**Manual, Tutorial of <u>Lottery Software</u>, Lotto Apps**_](https://saliu.com/forum/lotto-book.html).
-   [**Skips Lottery, Lotto, Gambling, Systems, Strategy**](https://saliu.com/skip-strategy.html)
-   [Lottery Utility Software](https://saliu.com/lottery-utility.html): Pick-3, 4 Lottery, Lotto-5, 6, Powerball, Mega Millions, Thunderball, Euromillions.
-   [**Lotto wheels**](https://saliu.com/lotto_wheels.html) _**for lottery games drawing 5 6 7 numbers**_.  
    The most advanced theory of lotto wheels or reduced lottery systems. Get also original lotto wheels for lotto games drawing 5, 6, or 7 numbers: Balanced, randomized, and free.
-   Download [**lottery software, lotto software**](https://saliu.com/infodown.html).

!['Wonder grid' means special lottery, lotto systems to win big money.](https://saliu.com/bbs/messages/HLINE.gif)

**[Home](https://saliu.com/index.htm) | [Search](https://saliu.com/Search.htm) | [New Writings](https://saliu.com/bbs/index.html) | [Software](https://saliu.com/infodown.html) | [Odds, Generator](https://saliu.com/calculator_generator.html) | [Contents](https://saliu.com/content/index.html) | [Forums](https://forums.saliu.com/) | [Sitemap](https://saliu.com/sitemap/index.html)**

![SALIU COM is the site of lottery, lotto, software, wonder-grid strategy.](https://saliu.com/bbs/messages/HLINE.gif)
