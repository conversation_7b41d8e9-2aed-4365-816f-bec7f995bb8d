# Wonder Grid strategy engine

"""
WonderGridEngine - Core implementation of Wonder Grid strategy with integrated components
"""
mutable struct WonderGridEngine
    ffg_calculator::FFGCalculator
    skip_analyzer::SkipAnalyzer
    pairing_engine::PairingEngine
    historical_data::Vector{LotteryDraw}
    strategy_cache::Dict{String, Any}
    performance_metrics::Dict{String, Float64}
    
    function WonderGridEngine(historical_data::Vector{LotteryDraw})
        if isempty(historical_data)
            throw(ArgumentError("Historical data cannot be empty"))
        end
        
        # Initialize components with shared data
        ffg_calc = FFGCalculator()
        skip_analyzer = SkipAnalyzer(historical_data)
        pairing_engine = PairingEngine(historical_data)
        
        # Initialize caches and metrics
        strategy_cache = Dict{String, Any}()
        performance_metrics = Dict{String, Float64}(
            "initialization_time" => 0.0,
            "last_key_selection_time" => 0.0,
            "last_combination_generation_time" => 0.0,
            "total_operations" => 0.0
        )
        
        new(ffg_calc, skip_analyzer, pairing_engine, historical_data, 
            strategy_cache, performance_metrics)
    end
end

"""
Create WonderGridEngine from raw data (Vector{Vector{Int}})
"""
function WonderGridEngine(raw_data::Vector{Vector{Int}})
    # Convert raw data to LotteryDraw format
    draws = LotteryDraw[]
    for (i, numbers) in enumerate(raw_data)
        # Create sequential dates for raw data
        date = Date(2022, 1, 1) + Day(length(raw_data) - i)
        draw = LotteryDraw(numbers, date, i)
        push!(draws, draw)
    end
    
    return WonderGridEngine(draws)
end

"""
Update engine with new historical data
"""
function update_engine!(engine::WonderGridEngine, new_data::Vector{LotteryDraw})
    # Update historical data
    append!(engine.historical_data, new_data)
    
    # Update all components
    for draw in new_data
        update_skips(engine.skip_analyzer, draw)
        update_pairings(engine.pairing_engine, draw)
        update_ffg_calculations!(engine.ffg_calculator, engine.historical_data, draw)
    end
    
    # Clear strategy cache as it's now invalid
    empty!(engine.strategy_cache)
    
    # Update performance metrics
    engine.performance_metrics["total_operations"] += 1
end

"""
Update engine with single new draw
"""
function update_engine!(engine::WonderGridEngine, new_draw::LotteryDraw)
    update_engine!(engine, [new_draw])
end

"""
Get engine status and health metrics
"""
function get_engine_status(engine::WonderGridEngine)::Dict{String, Any}
    return Dict{String, Any}(
        "historical_data_count" => length(engine.historical_data),
        "cache_entries" => length(engine.strategy_cache),
        "performance_metrics" => copy(engine.performance_metrics),
        "components_status" => Dict{String, Any}(
            "ffg_calculator_cache_size" => length(engine.ffg_calculator.ffg_cache),
            "skip_analyzer_cache_size" => length(engine.skip_analyzer.skip_cache),
            "pairing_engine_cache_size" => length(engine.pairing_engine.pairing_cache)
        ),
        "last_data_date" => isempty(engine.historical_data) ? nothing : engine.historical_data[1].draw_date,
        "data_date_range" => isempty(engine.historical_data) ? nothing : 
            (engine.historical_data[end].draw_date, engine.historical_data[1].draw_date)
    )
end

"""
Clear all caches to free memory
"""
function clear_engine_caches!(engine::WonderGridEngine)
    empty!(engine.strategy_cache)
    empty!(engine.ffg_calculator.ffg_cache)
    empty!(engine.skip_analyzer.skip_cache)
    clear_caches!(engine.pairing_engine)
    
    engine.performance_metrics["total_operations"] += 1
end

"""
Validate engine integrity and component synchronization
"""
function validate_engine_integrity(engine::WonderGridEngine)::Dict{String, Any}
    validation_results = Dict{String, Any}()
    
    # Check data consistency across components
    ffg_data_size = length(engine.ffg_calculator.ffg_cache) > 0 ? 
        length(engine.historical_data) : 0
    skip_data_size = length(engine.skip_analyzer.historical_data)
    pairing_data_size = length(engine.pairing_engine.historical_data)
    main_data_size = length(engine.historical_data)
    
    data_consistency = (skip_data_size == main_data_size && 
                       pairing_data_size == main_data_size)
    
    validation_results["data_consistency"] = data_consistency
    validation_results["data_sizes"] = Dict{String, Int}(
        "main" => main_data_size,
        "skip_analyzer" => skip_data_size,
        "pairing_engine" => pairing_data_size
    )
    
    # Validate data integrity
    if main_data_size > 0
        # Check for valid lottery draws
        valid_draws = all(draw -> length(draw.numbers) == 5 && 
                         all(n -> 1 <= n <= 39, draw.numbers), 
                         engine.historical_data)
        validation_results["valid_draws"] = valid_draws
        
        # Check chronological order (newest first)
        chronological = main_data_size <= 1 || 
            all(i -> engine.historical_data[i].draw_date >= engine.historical_data[i+1].draw_date,
                1:(main_data_size-1))
        validation_results["chronological_order"] = chronological
    else
        validation_results["valid_draws"] = false
        validation_results["chronological_order"] = false
    end
    
    # Overall integrity
    validation_results["overall_integrity"] = (data_consistency && 
                                             validation_results["valid_draws"] && 
                                             validation_results["chronological_order"])
    
    return validation_results
end

"""
Select optimal key numbers based on FFG median analysis with performance tracking
"""
function select_key_numbers(engine::WonderGridEngine)::Vector{Int}
    start_time = time()
    
    # Check cache first
    cache_key = "favorable_numbers"
    if haskey(engine.strategy_cache, cache_key)
        engine.performance_metrics["last_key_selection_time"] = time() - start_time
        return engine.strategy_cache[cache_key]
    end
    
    favorable_numbers = Int[]
    
    # Use integrated FFG analysis for key number selection
    for number in 1:39
        current_skip = get_current_skip(engine.skip_analyzer, number)
        ffg_median = calculate_ffg_median(engine.ffg_calculator, number, engine.historical_data)
        
        # Core Wonder Grid criterion: current skip ≤ FFG median
        if current_skip <= ffg_median
            push!(favorable_numbers, number)
        end
    end
    
    # Cache the result
    engine.strategy_cache[cache_key] = favorable_numbers
    
    # Update performance metrics
    selection_time = time() - start_time
    engine.performance_metrics["last_key_selection_time"] = selection_time
    engine.performance_metrics["total_operations"] += 1
    
    return favorable_numbers
end

"""
Select key numbers with integrated FFG median analysis and advanced filtering
"""
function select_key_numbers_with_ffg_analysis(engine::WonderGridEngine; 
                                             max_numbers::Int = 10,
                                             min_pairing_quality::Float64 = 0.3,
                                             max_skip_ratio::Float64 = 0.8)::Vector{Int}
    start_time = time()
    
    candidates = Tuple{Int, Float64, Dict{String, Float64}}[]
    
    for number in 1:39
        # Get FFG analysis
        current_skip = get_current_skip(engine.skip_analyzer, number)
        ffg_median = calculate_ffg_median(engine.ffg_calculator, number, engine.historical_data)
        
        # Only consider numbers that meet FFG criterion
        if current_skip <= ffg_median
            skip_ratio = current_skip / max(ffg_median, 1.0)
            
            # Get pairing quality using enhanced pairing engine
            pairing_analysis = identify_top_25_percent_pairings(engine.pairing_engine, number)
            pairing_quality = pairing_analysis["frequency_coverage"] / 100.0  # Convert to 0-1 scale
            
            # Calculate skip probability
            skip_probability = compute_skip_probability(engine.ffg_calculator, current_skip, ffg_median)
            
            # Apply filters
            if (pairing_quality >= min_pairing_quality && 
                skip_ratio <= max_skip_ratio)
                
                # Calculate composite score (lower is better)
                composite_score = (skip_ratio * 0.5) + 
                                ((1.0 - pairing_quality) * 0.3) + 
                                ((1.0 - skip_probability) * 0.2)
                
                metrics = Dict{String, Float64}(
                    "skip_ratio" => skip_ratio,
                    "pairing_quality" => pairing_quality,
                    "skip_probability" => skip_probability,
                    "current_skip" => Float64(current_skip),
                    "ffg_median" => ffg_median
                )
                
                push!(candidates, (number, composite_score, metrics))
            end
        end
    end
    
    # Sort by composite score and return top numbers
    sort!(candidates, by = x -> x[2])
    selected_numbers = [num for (num, _, _) in candidates[1:min(max_numbers, length(candidates))]]
    
    # Update performance metrics
    selection_time = time() - start_time
    engine.performance_metrics["last_key_selection_time"] = selection_time
    engine.performance_metrics["total_operations"] += 1
    
    return selected_numbers
end

"""
Get comprehensive key number analysis with FFG integration
"""
function get_comprehensive_key_analysis(engine::WonderGridEngine)::Dict{String, Any}
    start_time = time()
    
    analysis = Dict{String, Any}()
    favorable_numbers = Int[]
    all_numbers_analysis = Dict{Int, Dict{String, Any}}()
    
    for number in 1:39
        # FFG analysis
        current_skip = get_current_skip(engine.skip_analyzer, number)
        ffg_median = calculate_ffg_median(engine.ffg_calculator, number, engine.historical_data)
        skip_probability = compute_skip_probability(engine.ffg_calculator, current_skip, ffg_median)
        
        # Pairing analysis
        pairing_analysis = identify_top_25_percent_pairings(engine.pairing_engine, number)
        
        # Determine if favorable
        is_favorable = current_skip <= ffg_median
        if is_favorable
            push!(favorable_numbers, number)
        end
        
        # Store comprehensive analysis
        number_analysis = Dict{String, Any}(
            "current_skip" => current_skip,
            "ffg_median" => ffg_median,
            "skip_ratio" => current_skip / max(ffg_median, 1.0),
            "skip_probability" => skip_probability,
            "is_favorable" => is_favorable,
            "pairing_coverage" => pairing_analysis["frequency_coverage"],
            "top_pairings_count" => pairing_analysis["actual_count"],
            "total_frequency" => pairing_analysis["total_frequency"],
            "recommendation" => is_favorable ? 
                (current_skip / ffg_median < 0.5 ? "HIGHLY RECOMMENDED" : "RECOMMENDED") : 
                "NOT RECOMMENDED"
        )
        
        all_numbers_analysis[number] = number_analysis
    end
    
    # Calculate summary statistics
    if !isempty(favorable_numbers)
        favorable_skip_ratios = [all_numbers_analysis[n]["skip_ratio"] for n in favorable_numbers]
        favorable_coverages = [all_numbers_analysis[n]["pairing_coverage"] for n in favorable_numbers]
        
        analysis["summary"] = Dict{String, Any}(
            "total_favorable" => length(favorable_numbers),
            "favorable_percentage" => (length(favorable_numbers) / 39) * 100,
            "mean_skip_ratio" => mean(favorable_skip_ratios),
            "mean_pairing_coverage" => mean(favorable_coverages),
            "best_skip_ratio" => minimum(favorable_skip_ratios),
            "best_pairing_coverage" => maximum(favorable_coverages)
        )
    else
        analysis["summary"] = Dict{String, Any}(
            "total_favorable" => 0,
            "favorable_percentage" => 0.0,
            "mean_skip_ratio" => 0.0,
            "mean_pairing_coverage" => 0.0,
            "best_skip_ratio" => 0.0,
            "best_pairing_coverage" => 0.0
        )
    end
    
    analysis["favorable_numbers"] = favorable_numbers
    analysis["all_numbers_analysis"] = all_numbers_analysis
    analysis["analysis_time"] = time() - start_time
    
    return analysis
end

"""
Evaluate and rank potential key numbers based on multiple criteria
"""
function evaluate_key_numbers(engine::WonderGridEngine)::Vector{Tuple{Int, Float64, Dict{String, Float64}}}
    candidates = Tuple{Int, Float64, Dict{String, Float64}}[]
    
    for number in 1:39
        # Get skip analysis
        skip_chart = generate_skip_chart(engine.skip_analyzer, number)
        
        # Only consider favorable numbers (current skip ≤ FFG median)
        if skip_chart.is_favorable
            # Calculate multiple scoring factors
            skip_ratio = skip_chart.current_skip / skip_chart.ffg_median
            
            # Get pairing strength (number of strong pairings available)
            top_pairings = get_top_pairings(engine.pairing_engine, number, 0.25)
            pairing_strength = length(top_pairings) / 10.0  # Normalize to 0-1 scale
            
            # Calculate skip probability
            skip_probability = compute_skip_probability(engine.ffg_calculator, 
                                                     skip_chart.current_skip, 
                                                     skip_chart.ffg_median)
            
            # Calculate frequency score (how often this number appears)
            frequency_score = calculate_frequency_score(engine, number)
            
            # Calculate stability score (consistency of skip patterns)
            stability_score = calculate_stability_score(engine, number)
            
            # Composite score calculation (lower is better)
            # Weight factors: skip_ratio (40%), pairing_strength (25%), 
            # skip_probability (20%), frequency (10%), stability (5%)
            composite_score = (skip_ratio * 0.4) + 
                            ((1.0 - pairing_strength) * 0.25) +  # Invert pairing strength
                            ((1.0 - skip_probability) * 0.2) +   # Invert probability
                            ((1.0 - frequency_score) * 0.1) +    # Invert frequency
                            ((1.0 - stability_score) * 0.05)     # Invert stability
            
            # Store detailed metrics
            metrics = Dict{String, Float64}(
                "skip_ratio" => skip_ratio,
                "pairing_strength" => pairing_strength,
                "skip_probability" => skip_probability,
                "frequency_score" => frequency_score,
                "stability_score" => stability_score,
                "current_skip" => Float64(skip_chart.current_skip),
                "ffg_median" => skip_chart.ffg_median
            )
            
            push!(candidates, (number, composite_score, metrics))
        end
    end
    
    # Sort by composite score (ascending - lower is better)
    sort!(candidates, by = x -> x[2])
    
    return candidates
end

"""
Rank key numbers by favorability score
"""
function rank_key_numbers(engine::WonderGridEngine, max_numbers::Int = 10)::Vector{Tuple{Int, Float64}}
    evaluated_numbers = evaluate_key_numbers(engine)
    
    # Extract just the number and score for ranking
    ranked_numbers = [(num, score) for (num, score, _) in evaluated_numbers]
    
    return ranked_numbers[1:min(max_numbers, length(ranked_numbers))]
end

"""
Select key numbers automatically based on optimal criteria
"""
function select_key_numbers_auto(engine::WonderGridEngine, max_numbers::Int = 5)::Vector{Int}
    ranked_numbers = rank_key_numbers(engine, max_numbers)
    return [num for (num, _) in ranked_numbers]
end

"""
Select key numbers with custom filtering criteria
"""
function select_key_numbers_filtered(engine::WonderGridEngine; 
                                   max_skip_ratio::Float64 = 0.8,
                                   min_pairing_strength::Float64 = 0.6,
                                   min_probability::Float64 = 0.4,
                                   max_numbers::Int = 10)::Vector{Int}
    evaluated_numbers = evaluate_key_numbers(engine)
    
    filtered_numbers = Int[]
    
    for (number, score, metrics) in evaluated_numbers
        if (metrics["skip_ratio"] <= max_skip_ratio &&
            metrics["pairing_strength"] >= min_pairing_strength &&
            metrics["skip_probability"] >= min_probability)
            push!(filtered_numbers, number)
        end
        
        # Stop when we have enough numbers
        if length(filtered_numbers) >= max_numbers
            break
        end
    end
    
    return filtered_numbers
end

"""
Get detailed analysis for a specific key number candidate
"""
function analyze_key_number(engine::WonderGridEngine, number::Int)::Dict{String, Any}
    if !(1 <= number <= 39)
        throw(ArgumentError("Number must be between 1 and 39"))
    end
    
    # Get skip analysis
    skip_chart = generate_skip_chart(engine.skip_analyzer, number)
    
    # Get pairing analysis
    top_pairings = get_top_pairings(engine.pairing_engine, number, 0.25)
    all_pairings = get_all_pairings_for_number(engine.pairing_engine, number)
    
    # Calculate potential combinations
    potential_combinations = length(top_pairings) >= 4 ? binomial(min(10, length(top_pairings)), 4) : 0
    
    # Get frequency information
    frequency_score = calculate_frequency_score(engine, number)
    stability_score = calculate_stability_score(engine, number)
    
    # Calculate skip probability
    skip_probability = compute_skip_probability(engine.ffg_calculator, 
                                             skip_chart.current_skip, 
                                             skip_chart.ffg_median)
    
    return Dict{String, Any}(
        "number" => number,
        "is_favorable" => skip_chart.is_favorable,
        "current_skip" => skip_chart.current_skip,
        "ffg_median" => skip_chart.ffg_median,
        "skip_ratio" => skip_chart.current_skip / skip_chart.ffg_median,
        "skip_probability" => skip_probability,
        "top_pairings_count" => length(top_pairings),
        "top_pairings" => top_pairings[1:min(10, length(top_pairings))],
        "potential_combinations" => potential_combinations,
        "frequency_score" => frequency_score,
        "stability_score" => stability_score,
        "recommendation" => skip_chart.is_favorable ? 
            (skip_chart.current_skip / skip_chart.ffg_median < 0.5 ? "HIGHLY RECOMMENDED" : "RECOMMENDED") : 
            "NOT RECOMMENDED"
    )
end

"""
Calculate frequency score for a number (0-1 scale, higher is better)
"""
function calculate_frequency_score(engine::WonderGridEngine, number::Int)::Float64
    # Count occurrences of the number in historical data
    occurrences = 0
    total_draws = length(engine.skip_analyzer.historical_data)
    
    for draw in engine.skip_analyzer.historical_data
        if number in draw.numbers
            occurrences += 1
        end
    end
    
    # Calculate frequency relative to expected (5/39 for Lotto 5/39)
    expected_frequency = (5.0 / 39.0) * total_draws
    actual_frequency = Float64(occurrences)
    
    # Normalize to 0-1 scale (1.0 = exactly expected frequency)
    if expected_frequency > 0
        frequency_ratio = actual_frequency / expected_frequency
        # Cap at reasonable bounds and normalize
        return min(1.0, max(0.0, frequency_ratio / 2.0))  # Divide by 2 to keep in 0-1 range
    else
        return 0.0
    end
end

"""
Calculate stability score for a number's skip patterns (0-1 scale, higher is better)
"""
function calculate_stability_score(engine::WonderGridEngine, number::Int)::Float64
    skips = calculate_skips(engine.skip_analyzer, number)
    
    if length(skips) < 3
        return 0.5  # Default score for insufficient data
    end
    
    # Calculate coefficient of variation (std dev / mean)
    skip_mean = mean(skips)
    skip_std = std(skips)
    
    if skip_mean > 0
        cv = skip_std / skip_mean
        # Convert to stability score (lower CV = higher stability)
        # Normalize CV to 0-1 scale (assuming CV rarely exceeds 2.0)
        stability = max(0.0, 1.0 - min(1.0, cv / 2.0))
        return stability
    else
        return 0.5
    end
end

"""
Compare multiple key number candidates
"""
function compare_key_numbers(engine::WonderGridEngine, numbers::Vector{Int})::Dict{String, Any}
    if any(n -> !(1 <= n <= 39), numbers)
        throw(ArgumentError("All numbers must be between 1 and 39"))
    end
    
    comparison = Dict{String, Any}()
    analyses = Dict{Int, Dict{String, Any}}()
    
    # Analyze each number
    for number in numbers
        analyses[number] = analyze_key_number(engine, number)
    end
    
    # Find best candidate based on multiple criteria
    best_overall = 0
    best_score = Inf
    best_favorable = 0
    best_favorable_score = Inf
    
    for number in numbers
        analysis = analyses[number]
        
        # Calculate composite score for comparison
        skip_ratio = analysis["skip_ratio"]
        pairing_strength = analysis["top_pairings_count"] / 10.0
        frequency_score = analysis["frequency_score"]
        
        composite_score = skip_ratio * 0.5 + (1.0 - pairing_strength) * 0.3 + (1.0 - frequency_score) * 0.2
        
        # Track best overall
        if composite_score < best_score
            best_score = composite_score
            best_overall = number
        end
        
        # Track best among favorable numbers
        if analysis["is_favorable"] && composite_score < best_favorable_score
            best_favorable_score = composite_score
            best_favorable = number
        end
    end
    
    comparison["analyses"] = analyses
    comparison["best_overall"] = best_overall
    comparison["best_overall_score"] = best_score
    comparison["best_favorable"] = best_favorable
    comparison["best_favorable_score"] = best_favorable_score
    comparison["favorable_count"] = count(n -> analyses[n]["is_favorable"], numbers)
    
    return comparison
end

"""
Generate combinations for a specific key number using advanced combination generator
"""
function generate_combinations(engine::WonderGridEngine, key_number::Int)::Vector{Vector{Int}}
    # Use the advanced combination generator for standard C(10,4) = 210 combinations
    generator = CombinationGenerator(engine.pairing_engine)
    return generate_standard_combinations(generator, key_number)
end

"""
Basic combination generation (fallback method)
"""
function generate_combinations_basic(engine::WonderGridEngine, key_number::Int)::Vector{Vector{Int}}
    start_time = time()
    
    # Validate key number
    if !(1 <= key_number <= 39)
        throw(ArgumentError("Key number must be between 1 and 39"))
    end
    
    # Check if key number is favorable
    current_skip = get_current_skip(engine.skip_analyzer, key_number)
    ffg_median = calculate_ffg_median(engine.ffg_calculator, key_number, engine.historical_data)
    
    if current_skip > ffg_median
        @warn "Key number $key_number is not favorable (skip: $current_skip > median: $(round(ffg_median, digits=1)))"
    end
    
    # Get top 25% pairings using enhanced pairing engine
    pairing_analysis = identify_top_25_percent_pairings(engine.pairing_engine, key_number)
    top_pairings = pairing_analysis["top_numbers"]
    
    # Ensure we have sufficient pairings for combination generation
    if length(top_pairings) < 4
        # Fallback: get more pairings if needed
        extended_analysis = identify_top_pairings(engine.pairing_engine, key_number, 0.5)
        top_pairings = extended_analysis["top_numbers"]
        
        if length(top_pairings) < 4
            # Last resort: add random numbers
            all_numbers = collect(1:39)
            filter!(n -> n != key_number, all_numbers)
            
            for num in all_numbers
                if !(num in top_pairings) && length(top_pairings) < 10
                    push!(top_pairings, num)
                end
                if length(top_pairings) >= 4
                    break
                end
            end
        end
    end
    
    # Optimize pairing count for C(n,4) combinations
    # Target: C(10,4) = 210 combinations for optimal balance
    target_pairings = 10
    if length(top_pairings) > target_pairings
        top_pairings = top_pairings[1:target_pairings]
    end
    
    # Generate all C(n,4) combinations where n = length(top_pairings)
    combinations = Vector{Vector{Int}}()
    
    if length(top_pairings) >= 4
        for combo in Combinatorics.combinations(top_pairings, 4)
            # Create combination with key number included
            full_combo = sort([key_number; combo])
            push!(combinations, full_combo)
        end
    else
        throw(ArgumentError("Insufficient pairings available for key number $key_number"))
    end
    
    # Validate all combinations
    for combo in combinations
        if !(key_number in combo)
            throw(AssertionError("Generated combination does not contain key number: $combo"))
        end
        if length(combo) != 5
            throw(AssertionError("Generated combination has wrong length: $combo"))
        end
        if !all(n -> 1 <= n <= 39, combo)
            throw(AssertionError("Generated combination contains invalid numbers: $combo"))
        end
    end
    
    # Update performance metrics
    generation_time = time() - start_time
    engine.performance_metrics["last_combination_generation_time"] = generation_time
    engine.performance_metrics["total_operations"] += 1
    
    return combinations
end

"""
Generate combinations with custom pairing percentage
"""
function generate_combinations_with_percentage(engine::WonderGridEngine, key_number::Int, 
                                             pairing_percentage::Float64)::Vector{Vector{Int}}
    if !(1 <= key_number <= 39)
        throw(ArgumentError("Key number must be between 1 and 39"))
    end
    
    if !(0.0 < pairing_percentage <= 1.0)
        throw(ArgumentError("Pairing percentage must be between 0 and 1"))
    end
    
    # Get pairings with custom percentage
    pairing_analysis = identify_top_pairings(engine.pairing_engine, key_number, pairing_percentage)
    top_pairings = pairing_analysis["top_numbers"]
    
    # Ensure minimum pairings
    if length(top_pairings) < 4
        extended_analysis = identify_top_pairings(engine.pairing_engine, key_number, 0.5)
        top_pairings = extended_analysis["top_numbers"][1:min(10, length(extended_analysis["top_numbers"]))]
    end
    
    # Generate combinations
    combinations = Vector{Vector{Int}}()
    
    if length(top_pairings) >= 4
        # Limit to reasonable number for performance
        max_pairings = min(15, length(top_pairings))  # Max C(15,4) = 1365 combinations
        working_pairings = top_pairings[1:max_pairings]
        
        for combo in Combinatorics.combinations(working_pairings, 4)
            full_combo = sort([key_number; combo])
            push!(combinations, full_combo)
        end
    end
    
    return combinations
end

"""
Generate multiple key number combinations efficiently using batch processing
"""
function generate_multiple_key_combinations(engine::WonderGridEngine, 
                                          key_numbers::Vector{Int})::Dict{Int, Vector{Vector{Int}}}
    generator = CombinationGenerator(engine.pairing_engine)
    return generate_batch_combinations(generator, key_numbers)
end

"""
Basic multiple key combination generation (fallback method)
"""
function generate_multiple_key_combinations_basic(engine::WonderGridEngine, 
                                                key_numbers::Vector{Int})::Dict{Int, Vector{Vector{Int}}}
    start_time = time()
    
    results = Dict{Int, Vector{Vector{Int}}}()
    
    for key_number in key_numbers
        if 1 <= key_number <= 39
            try
                combinations = generate_combinations_basic(engine, key_number)
                results[key_number] = combinations
            catch e
                @warn "Failed to generate combinations for key number $key_number: $e"
                results[key_number] = Vector{Vector{Int}}()
            end
        else
            @warn "Invalid key number: $key_number"
            results[key_number] = Vector{Vector{Int}}()
        end
    end
    
    # Update performance metrics
    total_time = time() - start_time
    engine.performance_metrics["total_operations"] += length(key_numbers)
    
    return results
end

"""
Generate combinations with LIE elimination applied
"""
function generate_combinations_with_lie_elimination(engine::WonderGridEngine, key_number::Int)::Vector{Vector{Int}}
    # Generate standard combinations first
    combinations = generate_combinations(engine, key_number)
    
    # Create LIE elimination engine
    lie_engine = LIEEliminationEngine(engine.pairing_engine)
    
    # Apply LIE elimination filter
    filtered_combinations = apply_elimination_filter(lie_engine, combinations)
    
    return filtered_combinations
end

"""
Generate combinations with LIE elimination and cost analysis
"""
function generate_combinations_with_lie_analysis(engine::WonderGridEngine, key_number::Int)::Dict{String, Any}
    start_time = time()
    
    # Generate standard combinations
    original_combinations = generate_combinations(engine, key_number)
    original_count = length(original_combinations)
    
    # Create LIE elimination engine
    lie_engine = LIEEliminationEngine(engine.pairing_engine)
    
    # Apply LIE elimination
    filtered_combinations = apply_elimination_filter(lie_engine, original_combinations)
    filtered_count = length(filtered_combinations)
    
    # Calculate cost savings
    cost_savings = calculate_cost_savings(lie_engine, original_count, filtered_count)
    
    # Generate LIE combinations for analysis
    lie_combinations = generate_lie_combinations(lie_engine, key_number)
    
    analysis_time = time() - start_time
    
    return Dict{String, Any}(
        "key_number" => key_number,
        "original_combinations" => original_combinations,
        "filtered_combinations" => filtered_combinations,
        "lie_combinations" => lie_combinations,
        "original_count" => original_count,
        "filtered_count" => filtered_count,
        "eliminated_count" => original_count - filtered_count,
        "cost_savings_percentage" => cost_savings,
        "analysis_time" => analysis_time
    )
end

"""
Generate combinations for multiple key numbers with LIE elimination
"""
function generate_multiple_combinations_with_lie(engine::WonderGridEngine, 
                                               key_numbers::Vector{Int})::Dict{Int, Dict{String, Any}}
    results = Dict{Int, Dict{String, Any}}()
    
    for key_number in key_numbers
        if 1 <= key_number <= 39
            try
                analysis = generate_combinations_with_lie_analysis(engine, key_number)
                results[key_number] = analysis
            catch e
                @warn "Failed to generate LIE analysis for key number $key_number: $e"
                results[key_number] = Dict{String, Any}(
                    "error" => string(e),
                    "original_count" => 0,
                    "filtered_count" => 0,
                    "cost_savings_percentage" => 0.0
                )
            end
        else
            @warn "Invalid key number: $key_number"
        end
    end
    
    return results
end

"""
Execute Wonder Grid strategy with integrated LIE elimination
"""
function execute_wonder_grid_with_lie(engine::WonderGridEngine, key_number::Int; 
                                    elimination_threshold::Float64 = 0.1)::Dict{String, Any}
    start_time = time()
    
    # Validate key number favorability
    current_skip = get_current_skip(engine.skip_analyzer, key_number)
    ffg_median = calculate_ffg_median(engine.ffg_calculator, key_number, engine.historical_data)
    
    if current_skip > ffg_median
        @warn "Key number $key_number is not favorable (skip: $current_skip > median: $(round(ffg_median, digits=1)))"
    end
    
    # Generate combinations with LIE elimination
    lie_engine = LIEEliminationEngine(engine.pairing_engine)
    lie_engine.elimination_threshold = elimination_threshold
    
    analysis = generate_combinations_with_lie_analysis(engine, key_number)
    
    # Get pairing analysis for context
    pairing_analysis = identify_top_25_percent_pairings(engine.pairing_engine, key_number)
    
    # Calculate efficiency metrics
    efficiency_metrics = Dict{String, Float64}(
        "skip_ratio" => current_skip / ffg_median,
        "pairing_coverage" => pairing_analysis["frequency_coverage"],
        "combination_reduction_ratio" => analysis["cost_savings_percentage"] / 100.0,
        "final_combination_density" => analysis["filtered_count"] / 210.0  # Relative to standard C(10,4)
    )
    
    execution_time = time() - start_time
    
    return Dict{String, Any}(
        "strategy_name" => "Wonder Grid with LIE Elimination",
        "key_number" => key_number,
        "key_number_analysis" => Dict{String, Any}(
            "current_skip" => current_skip,
            "ffg_median" => ffg_median,
            "is_favorable" => current_skip <= ffg_median,
            "skip_probability" => compute_skip_probability(engine.ffg_calculator, current_skip, ffg_median)
        ),
        "pairing_analysis" => pairing_analysis,
        "combination_analysis" => analysis,
        "efficiency_metrics" => efficiency_metrics,
        "elimination_threshold" => elimination_threshold,
        "execution_time" => execution_time,
        "recommended_play_set" => analysis["filtered_combinations"]
    )
end

"""
Compare Wonder Grid strategy with and without LIE elimination
"""
function compare_wonder_grid_strategies(engine::WonderGridEngine, key_number::Int)::Dict{String, Any}
    # Standard Wonder Grid
    standard_combinations = generate_combinations(engine, key_number)
    standard_count = length(standard_combinations)
    
    # Wonder Grid with LIE
    lie_analysis = generate_combinations_with_lie_analysis(engine, key_number)
    
    # Calculate comparison metrics
    cost_reduction = lie_analysis["cost_savings_percentage"]
    combination_efficiency = lie_analysis["filtered_count"] / standard_count
    
    return Dict{String, Any}(
        "key_number" => key_number,
        "standard_strategy" => Dict{String, Any}(
            "name" => "Standard Wonder Grid",
            "combination_count" => standard_count,
            "combinations" => standard_combinations
        ),
        "lie_strategy" => Dict{String, Any}(
            "name" => "Wonder Grid with LIE",
            "combination_count" => lie_analysis["filtered_count"],
            "combinations" => lie_analysis["filtered_combinations"],
            "eliminated_combinations" => lie_analysis["lie_combinations"]
        ),
        "comparison_metrics" => Dict{String, Any}(
            "cost_reduction_percentage" => cost_reduction,
            "combination_efficiency" => combination_efficiency,
            "eliminated_count" => lie_analysis["eliminated_count"],
            "strategy_recommendation" => cost_reduction > 10.0 ? 
                "LIE elimination recommended (>10% cost savings)" : 
                "Standard strategy may be sufficient"
        )
    )
end

"""
Optimize LIE elimination threshold for a key number
"""
function optimize_lie_threshold(engine::WonderGridEngine, key_number::Int; 
                              thresholds::Vector{Float64} = [0.05, 0.1, 0.15, 0.2, 0.25])::Dict{String, Any}
    optimization_results = Dict{String, Any}()
    threshold_analyses = Dict{Float64, Dict{String, Any}}()
    
    # Test different thresholds
    for threshold in thresholds
        lie_engine = LIEEliminationEngine(engine.pairing_engine)
        lie_engine.elimination_threshold = threshold
        
        # Generate combinations with this threshold
        original_combinations = generate_combinations(engine, key_number)
        filtered_combinations = apply_elimination_filter(lie_engine, original_combinations)
        
        cost_savings = calculate_cost_savings(lie_engine, length(original_combinations), length(filtered_combinations))
        
        threshold_analyses[threshold] = Dict{String, Any}(
            "threshold" => threshold,
            "original_count" => length(original_combinations),
            "filtered_count" => length(filtered_combinations),
            "cost_savings_percentage" => cost_savings,
            "elimination_ratio" => (length(original_combinations) - length(filtered_combinations)) / length(original_combinations)
        )
    end
    
    # Find optimal threshold (balance between cost savings and maintaining combinations)
    optimal_threshold = 0.1  # Default
    best_score = 0.0
    
    for (threshold, analysis) in threshold_analyses
        # Score based on cost savings but penalize excessive elimination
        savings = analysis["cost_savings_percentage"]
        elimination_ratio = analysis["elimination_ratio"]
        
        # Optimal score: good savings without eliminating too many combinations
        score = savings * (1.0 - min(0.5, elimination_ratio))  # Penalize if >50% eliminated
        
        if score > best_score
            best_score = score
            optimal_threshold = threshold
        end
    end
    
    optimization_results["threshold_analyses"] = threshold_analyses
    optimization_results["optimal_threshold"] = optimal_threshold
    optimization_results["optimal_analysis"] = threshold_analyses[optimal_threshold]
    optimization_results["recommendation"] = "Use threshold $(optimal_threshold) for $(round(threshold_analyses[optimal_threshold]["cost_savings_percentage"], digits=1))% cost savings"
    
    return optimization_results
end

"""
Generate combinations with advanced options and validation
"""
function generate_combinations_advanced(engine::WonderGridEngine, key_number::Int;
                                       generation_type::String = "standard",
                                       max_cost::Float64 = 210.0,
                                       quality_threshold::Float64 = 0.5,
                                       validate_results::Bool = true)::Dict{String, Any}
    
    generator = CombinationGenerator(engine.pairing_engine)
    
    # Generate combinations based on type
    combinations = if generation_type == "standard"
        generate_standard_combinations(generator, key_number)
    elseif generation_type == "cost_optimized"
        generate_cost_optimized_combinations(generator, key_number, target_cost=max_cost)
    elseif generation_type == "quality_based"
        generate_quality_combinations(generator, key_number, quality_threshold=quality_threshold)
    elseif generation_type == "efficient"
        generate_efficient_combinations(generator, key_number, max_combinations=round(Int, max_cost))
    else
        throw(ArgumentError("Unknown generation type: $generation_type"))
    end
    
    result = Dict{String, Any}(
        "key_number" => key_number,
        "generation_type" => generation_type,
        "combinations" => combinations,
        "combination_count" => length(combinations),
        "estimated_cost" => Float64(length(combinations))
    )
    
    # Add validation if requested
    if validate_results
        validation = generate_validated_combinations(generator, key_number)
        result["validation"] = validation
        result["is_valid"] = validation["overall_valid"]
    end
    
    return result
end

"""
Get comprehensive combination generation analysis
"""
function get_combination_generation_analysis(engine::WonderGridEngine, key_number::Int)::Dict{String, Any}
    generator = CombinationGenerator(engine.pairing_engine)
    
    # Get optimization analysis
    optimization = optimize_combination_generation(generator, key_number)
    
    # Get validation results
    validation = generate_validated_combinations(generator, key_number)
    
    # Get pairing analysis
    pairing_analysis = identify_top_25_percent_pairings(engine.pairing_engine, key_number)
    
    # Get FFG analysis
    current_skip = get_current_skip(engine.skip_analyzer, key_number)
    ffg_median = calculate_ffg_median(engine.ffg_calculator, key_number, engine.historical_data)
    
    return Dict{String, Any}(
        "key_number" => key_number,
        "is_favorable" => current_skip <= ffg_median,
        "ffg_analysis" => Dict{String, Any}(
            "current_skip" => current_skip,
            "ffg_median" => ffg_median,
            "skip_ratio" => current_skip / max(ffg_median, 1.0)
        ),
        "pairing_analysis" => pairing_analysis,
        "optimization_analysis" => optimization,
        "validation_results" => validation,
        "generation_statistics" => get_generation_statistics(generator),
        "recommendation" => optimization["recommended_strategy"],
        "recommendation_reason" => optimization["recommendation_reason"]
    )
end

"""
Get combination generation statistics
"""
function get_combination_statistics(engine::WonderGridEngine, key_number::Int)::Dict{String, Any}
    if !(1 <= key_number <= 39)
        throw(ArgumentError("Key number must be between 1 and 39"))
    end
    
    # Get pairing analysis
    pairing_analysis = identify_top_25_percent_pairings(engine.pairing_engine, key_number)
    
    # Calculate potential combinations
    pairing_count = pairing_analysis["actual_count"]
    potential_combinations = pairing_count >= 4 ? binomial(pairing_count, 4) : 0
    
    # Get FFG analysis
    current_skip = get_current_skip(engine.skip_analyzer, key_number)
    ffg_median = calculate_ffg_median(engine.ffg_calculator, key_number, engine.historical_data)
    
    return Dict{String, Any}(
        "key_number" => key_number,
        "is_favorable" => current_skip <= ffg_median,
        "current_skip" => current_skip,
        "ffg_median" => ffg_median,
        "skip_ratio" => current_skip / max(ffg_median, 1.0),
        "pairing_count" => pairing_count,
        "potential_combinations" => potential_combinations,
        "pairing_coverage" => pairing_analysis["frequency_coverage"],
        "total_frequency" => pairing_analysis["total_frequency"],
        "estimated_cost" => Float64(potential_combinations),
        "efficiency_estimate" => pairing_analysis["frequency_coverage"] / 50.0  # Relative to 50% target
    )
end

"""
Execute Wonder Grid strategy for a key number with comprehensive analysis
"""
function execute_strategy(engine::WonderGridEngine, key_number::Int)::StrategyResult
    start_time = time()
    
    # Validate key number
    if !(1 <= key_number <= 39)
        throw(ArgumentError("Key number must be between 1 and 39"))
    end
    
    # Get comprehensive analysis before generation
    stats = get_combination_statistics(engine, key_number)
    
    if !stats["is_favorable"]
        @warn "Executing strategy for unfavorable key number $key_number (skip ratio: $(round(stats["skip_ratio"], digits=2)))"
    end
    
    # Generate combinations
    combinations = generate_combinations(engine, key_number)
    
    generation_time = time() - start_time
    
    # Calculate estimated cost (assuming $1 per combination)
    estimated_cost = Float64(length(combinations))
    
    # Calculate expected efficiency based on pairing analysis
    pairing_coverage = stats["pairing_coverage"]
    base_efficiency = pairing_coverage / 34.3  # Relative to average coverage (34.3%)
    
    expected_efficiency = Dict{String, Float64}(
        "3/5" => max(1.0, base_efficiency * 1.2),  # Conservative estimate
        "4/5" => max(1.0, base_efficiency * 1.8),  # Moderate improvement
        "5/5" => max(1.0, base_efficiency * 3.5)   # Optimistic for jackpot
    )
    
    # Update performance metrics
    engine.performance_metrics["total_operations"] += 1
    
    return StrategyResult(
        key_number,
        combinations,
        generation_time,
        estimated_cost,
        expected_efficiency
    )
end

"""
Execute Wonder Grid strategy for multiple key numbers
"""
function execute_multiple_strategies(engine::WonderGridEngine, 
                                   key_numbers::Vector{Int})::Dict{Int, StrategyResult}
    results = Dict{Int, StrategyResult}()
    
    for key_number in key_numbers
        if 1 <= key_number <= 39
            try
                result = execute_strategy(engine, key_number)
                results[key_number] = result
            catch e
                @warn "Failed to execute strategy for key number $key_number: $e"
            end
        else
            @warn "Invalid key number: $key_number"
        end
    end
    
    return results
end

"""
Execute optimal Wonder Grid strategy (automatically select best key numbers)
"""
function execute_optimal_strategy(engine::WonderGridEngine; 
                                max_key_numbers::Int = 3,
                                min_pairing_quality::Float64 = 0.35)::Dict{String, Any}
    start_time = time()
    
    # Get comprehensive analysis
    analysis = get_comprehensive_key_analysis(engine)
    
    if analysis["summary"]["total_favorable"] == 0
        return Dict{String, Any}(
            "success" => false,
            "message" => "No favorable key numbers found",
            "execution_time" => time() - start_time,
            "analysis" => analysis
        )
    end
    
    # Select best key numbers based on multiple criteria
    best_keys = select_key_numbers_with_ffg_analysis(engine, 
                                                   max_numbers=max_key_numbers,
                                                   min_pairing_quality=min_pairing_quality)
    
    if isempty(best_keys)
        return Dict{String, Any}(
            "success" => false,
            "message" => "No key numbers meet quality criteria",
            "execution_time" => time() - start_time,
            "analysis" => analysis
        )
    end
    
    # Execute strategies for selected keys
    strategy_results = execute_multiple_strategies(engine, best_keys)
    
    # Calculate combined statistics
    total_combinations = sum(length(result.combinations) for result in values(strategy_results))
    total_cost = sum(result.estimated_cost for result in values(strategy_results))
    
    # Calculate weighted efficiency
    weighted_efficiency = Dict{String, Float64}()
    for tier in ["3/5", "4/5", "5/5"]
        total_weight = 0.0
        weighted_sum = 0.0
        
        for result in values(strategy_results)
            weight = length(result.combinations)
            weighted_sum += result.expected_efficiency[tier] * weight
            total_weight += weight
        end
        
        weighted_efficiency[tier] = total_weight > 0 ? weighted_sum / total_weight : 1.0
    end
    
    execution_time = time() - start_time
    
    return Dict{String, Any}(
        "success" => true,
        "selected_key_numbers" => best_keys,
        "strategy_results" => strategy_results,
        "summary" => Dict{String, Any}(
            "total_key_numbers" => length(best_keys),
            "total_combinations" => total_combinations,
            "total_cost" => total_cost,
            "average_combinations_per_key" => total_combinations / length(best_keys),
            "weighted_efficiency" => weighted_efficiency
        ),
        "execution_time" => execution_time,
        "analysis" => analysis
    )
end

"""
Get strategy recommendations based on current data
"""
function get_strategy_recommendations(engine::WonderGridEngine)::Dict{String, Any}
    # Get comprehensive analysis
    analysis = get_comprehensive_key_analysis(engine)
    
    recommendations = Dict{String, Any}()
    
    if analysis["summary"]["total_favorable"] == 0
        recommendations["primary"] = "No favorable numbers available. Wait for better conditions."
        recommendations["alternative"] = "Consider using LIE elimination strategy or wait for new draws."
        recommendations["risk_level"] = "HIGH"
    elseif analysis["summary"]["total_favorable"] <= 5
        recommendations["primary"] = "Limited favorable numbers. Use conservative approach with top 2-3 numbers."
        recommendations["alternative"] = "Consider smaller combination sets or wait for more favorable conditions."
        recommendations["risk_level"] = "MEDIUM"
    else
        recommendations["primary"] = "Good conditions for Wonder Grid strategy. Multiple favorable numbers available."
        recommendations["alternative"] = "Can use multiple key numbers or focus on highest quality numbers."
        recommendations["risk_level"] = "LOW"
    end
    
    # Add specific recommendations
    if analysis["summary"]["total_favorable"] > 0
        favorable_numbers = analysis["favorable_numbers"]
        
        # Sort by quality (pairing coverage)
        sorted_by_quality = sort(favorable_numbers, 
                               by = n -> analysis["all_numbers_analysis"][n]["pairing_coverage"], 
                               rev = true)
        
        recommendations["top_3_numbers"] = sorted_by_quality[1:min(3, length(sorted_by_quality))]
        recommendations["best_single_number"] = sorted_by_quality[1]
        
        # Calculate expected performance
        best_coverage = analysis["all_numbers_analysis"][sorted_by_quality[1]]["pairing_coverage"]
        recommendations["expected_performance"] = best_coverage > 36.0 ? "EXCELLENT" : 
                                                best_coverage > 34.0 ? "GOOD" : "FAIR"
    end
    
    recommendations["analysis_summary"] = analysis["summary"]
    recommendations["timestamp"] = Dates.now()
    
    return recommendations
end