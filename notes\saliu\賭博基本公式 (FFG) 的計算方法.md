賭博基本公式 (Fundamental Formula of Gambling, 簡稱 FFG) 是由 Ion Saliu 提出的核心數學理論，旨在揭示機率、試驗次數與確定性程度之間的關係，並應用於彩票分析與策略制定中。

### 賭博基本公式 (FFG) 的計算方法

FFG 是一個描述事件在給定機率下，達到特定確定性所需的試驗次數的公式。

1. **FFG 的核心要素**：
    
    - **個體機率 (p)**：單一事件發生的機率。
    - **試驗次數 (N)**：進行試驗的總次數。
    - **確定性程度 (Degree of Certainty, DC)**：在 N 次試驗中，事件至少發生一次的機率。
2. **FFG 的數學公式**：
    
    - FFG 可以計算在給定事件機率 (p) 和所需確定性程度 (DC) 的情況下，該事件至少發生一次所需的試驗次數 (N)。
    - FFG 的計算基於對數公式：**N = log(1-pi) / log(1-p)**。其中 `pi` 代表確定性程度 (DC)，`p` 代表個體機率。
    - `SuperFormula` 是專門用於自動執行這些 FFG 計算的軟體。
    - **範例**：若想以 99% 的確定性程度 (DC=99%) 至少獲得一次「正面」朝上 (p=1/2)，需要擲硬幣 7 次。
3. **FFG 中位數的計算**：
    
    - **FFG 中位數**：特指當確定性程度 (DC) 設定為 **50%** 時，事件至少發生一次所需的試驗次數 (N)。
    - 這個數值代表了某一事件在 50% 的情況下會發生的時間點，例如彩票號碼兩次中獎之間的間隔次數（跳躍）。
    - 彩票軟體（例如 `LotWon`）會生成報告，其中會顯示每個過濾器的中位數。使用者可以透過排序報告中的列來手動確定中位數，中位數即為排序後的中間值。
    - FFG 中位數是一個常數，具有堅實的數學基礎。

### 賭博基本公式 (FFG) 的利用方式

FFG 及其衍生的概念（特別是 FFG 中位數）是制定彩票策略和過濾組合的基石，旨在提高中獎機率並優化投注成本。

1. **彩票過濾器設定**：
    
    - FFG 中位數是設定彩票過濾器**正常範圍**的重要依據。
    - 透過觀察過濾器中位數，用戶可以判斷哪些數值是「正常範圍」內的，進而設定過濾器的**最小值**和**最大值**，以減少投注組合。
    - 嚴格的過濾器設定可以基於中位數的倍數，例如**中位數乘以 3、4 或 5**，或**中位數除以 3、4 或 5**。這能夠**大量消除**低機率的組合，大幅減少投注量。
    - 排序後的報告有助於**識別「古怪」值**（out-of-range values），即那些超出 FFG 中位數統計參數範圍的數值。
2. **跳躍系統 (Skips Systems)**：
    
    - **FFG 中位數是分析號碼跳躍的核心**。Ion Saliu 的理論指出，每個彩票號碼在超過 50% 的情況下，會在其 FFG 中位數或更少的抽獎次數後重複出現。
    - 基於 FFG 的跳躍系統策略建議，僅在號碼的**當前跳躍次數小於或等於其 FFG 中位數**時進行投注。
    - 研究顯示，選擇當前跳躍值落在 FFG 中位數範圍內的樂透號碼，中獎機率比純隨機選擇高出七倍。
    - `SkipSystem` 軟體利用 FFG 原理自動生成或允許用戶創建基於跳躍值的策略系統。
3. **謊言消除 (LIE Elimination) — 反向策略**：
    
    - FFG 的概念也應用於「謊言消除」策略，這是一種**反向彩票策略**。
    - 該策略旨在**有意設定預計不會中獎的過濾器**，從而消除極不可能中獎的組合。
    - FFG 原理幫助識別出那些在下一次開獎中**極少出現**特定模式的組合（例如，某些配對、十年組、頻率組或跳躍組）。將這些組合歸入「謊言檔案」（LIE file），並透過「清除」（Purge）功能從總組合中剔除，以減少投注量。
4. **鐘形曲線 (Bell Curve) 與最佳組合生成**：
    
    - FFG 理論指出，中獎組合主要來自**FFG 中位數附近的「鐘形曲線」區域**。
    - 彩票軟體會生成落在 FFG 中位數範圍內的組合，因為這些組合的出現頻率明顯更高，從而提高中獎率。
5. **動態過濾與趨勢分析**：
    
    - FFG 強調彩票是**動態過程**，而非靜態現象。過濾器應動態調整，而非靜態使用。
    - 透過分析過濾器數值的趨勢（例如，連續三次增加後常會出現反轉），並結合 FFG 的確定性程度概念（例如，90% 確定性會發生趨勢反轉），可以更精準地設定過濾器等級。
6. **跨策略交叉引用**：
    
    - FFG 的應用也體現在跨策略交叉引用中，例如使用 `FileLines` 軟體整合來自不同過濾器報告（如中獎報告、跳躍報告、頻率報告、德爾塔報告等）的數據。
    - 這種方法有助於發現**多個策略共同支持**的強大模式，進一步優化投注組合。
7. **其他賭博遊戲的應用**：
    
    - FFG 不僅限於彩票，它是一套**通用的賭博數學理論**。
    - 它被應用於**輪盤賭**（例如，預測數字在 FFG 中位數旋轉次數內的重複出現）。
    - 也適用於**賽馬**（分析位置偏差和號碼重複）、**百家樂**、**骰寶** 和**賭場戰爭** 等多種賭博遊戲。

總而言之，FFG 提供了一套嚴謹的數學框架，將傳統的機率論擴展到考慮事件在時間序列中的「確定性程度」。透過 FFG 中位數等參數，用戶能夠科學地分析彩票數據，制定出更具效益的策略，從而顯著提高中獎機率和降低投注成本。