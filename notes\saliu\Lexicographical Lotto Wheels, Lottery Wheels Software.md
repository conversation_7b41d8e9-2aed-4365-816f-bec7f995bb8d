---
created: 2025-07-24T22:10:03 (UTC +08:00)
tags: [lotto wheels,combinations,numbers,lotto,systems,wheel,lottery,lexicographic,lexicographical,order,index,balanced,randomized,loto,symmetrical]
source: https://saliu.com/bbs/messages/772.html
author: <PERSON>
---

# Lexicographical Lotto Wheels, Lottery Wheels Software

> ## Excerpt
> The lotto software creates lottery wheels based on lexicographical order. The lotto wheels consist of balanced lexicographic indexes or ranks.

---
[![Lottery Software: Jackpot Lotto, Pick Lotteries, Powerball, Mega Millions, Euromillions, Keno.](https://saliu.com/images/lotto-wheels.gif)](https://saliu.com/membership.html)  

## Software for Balanced Lotto Wheels Based on Lexicographic Order, Indexes

## By <PERSON>, ★ _Founder of Lottery Wheeling Mathematics_

![Generate loto wheels inside the median Gauss bell.](https://saliu.com/bbs/messages/HLINE.gif)

Written on July 15, 2002; last update September 2011.

• Combinations ~ lotto software to generate universal combinations.  
• LexicoWheels ~ lotto software to generate balanced lotto wheels based on lexicographical order (indexes).  
• DrawIndex ~ lotto software to calculate the lexicographical ranks (indices) of every real lottery drawing.

![The lotto software creates lottery wheels based on lexicographical order. The lotto wheels consist of balanced lexicographic indexes or ranks.](https://saliu.com/ScreenImgs/lexico-lottowheels.gif)

• These reports were created by my lexicographical software DrawIndex. The program calculates the lexicographic order or index of lotto combinations in sets of numbers. You can see how disproportionate the indexes of the 163-line lotto wheel are. The 49-6 game has a total of 13,983,816 combinations. The 163-line wheel contains 86 of 163 combinations between the indexes 13,687,807 and 13,978,985!!! The 163-line lotto wheel contains 21 of 163 lines from the 1st million indexes!!!

I wrote in another article that the lottery drawings quite strongly tend to cluster in the FFG median area. That said, wouldn't it be far better to randomly generate combinations with indexes in the FFG median area? Even Kokostirk nods approvingly...  
[Dynamic versus Static in Lotto Analysis](https://saliu.com/bbs/messages/919.html) (combinations around the median and FFG median).

The 57-line group of 49 numbers consists of combinations symmetrically selected from the complete 13,983,816-combination set. I used a program similar to my Combinations lotto wheeling software. It generates combinations equally distanced (the step) and starting at an index that offers symmetry. Program name: LexicoWheels (menu #4 in the Bright 5, 6 software packages).

```
<span size="5" face="Courier New" color="#ff8040">                   Lotto-6 Draw Lexicographic Order
                   File: WHEEL-49-163.IND

 Line      D R A W         Lexicographical
  no.                           Order

    1:   1  2  3  7 11 15         2,963
    2:   1  2  4  8 12 19        18,014
    3:   1  2  5  9 16 20        32,177
    4:   1  2  6 13 17 21        47,291
    5:   1  2 10 14 18 22        89,077
    6:   1  3  4  5  6 10       178,369
    7:   1  3  8  9 13 14       229,735
  ...
  157:  30 35 37 41 43 45     13,954,112
  158:  30 36 41 44 46 47     13,955,312
  159:  30 37 39 42 46 47     13,955,642
  160:  31 34 39 40 41 49     13,961,927
  161:  31 34 42 43 44 49     13,962,184
  162:  33 36 39 44 45 46     13,974,292
  163:  35 36 37 42 44 48     13,978,985

  Total Hits:          0       4       117     2260


                   Lotto-6 Draw Lexicographical Order
                   File: SET-49-57.IND

 Line      D R A W         Lexicographical
  no.                           Order

    1:   1  2  9 15 35 49        80,900
    2:   1  3 24 29 40 45       327,720
    3:   1  5 15 17 20 35       574,540
    4:   1  7 17 19 26 46       821,360
    5:   1 10 12 24 42 48      1,068,180
...
   54:  18 22 30 33 45 46     13,162,360
   55:  20 21 35 36 39 45     13,409,180
   56:  22 27 29 33 37 48     13,656,000
   57:  27 32 40 41 44 46     13,902,820

   Total Hits:        0      2        33      771

~ Comparatively (line for line), the 163-line wheel should have:
   Total Hits:        0      6        94       2205
                           worse     better   better
</span>
```

![Create lottery wheels as symmetrical lotto wheels in software.](https://saliu.com/bbs/messages/HLINE.gif)

~ It appears I encounter this situation more often than not: the lotto wheels perform worse as far as the highest prizes are concerned! All that trouble to design the wheels, and to wheel the player's picks, only to expect a worse chance to hit the jackpot or the second prize! Given the extra effort, shouldn't the wheels ALWAYS show an advantage at all prize levels? Or, at least, show a clear advantage more often than not...

I do not recommend wheeling lotto numbers anymore. You still want to play lotto wheels from time to time? Or, you want to test if lotto wheeling works better than random play? Well, then, you can use my free lotto 5, 6, 7-wheels packages: _SYSTEM6, WHEEL5, WHEEL7_. The wheels are balanced. Keep in mind that they are also randomized — a keyword here. The randomized lotto wheels perform better than ordinary wheels.

The lexicographic indexes are very important. You may test this statement, or any other. The testing software is also free, plus other combinatorial software at this site. Testing will come at no cost to you; playing the wheels will cost you an arm and a leg, however. Read the article presenting the free lotto system packages:  
"The best free [lotto wheels](https://saliu.com/lotto_wheels.html), reduced lottery systems: balanced and randomized".

• LexicoWheels generates lotto wheels based on lexicographical indexes. The program does all the calculations automatically. Each lotto wheel guarantee (e.g. _4 of 6_) has a specific step and a specific start index.

A lexicographic lotto wheel for 10 numbers with the _4 of 6_ guarantee is equidistant from the start (index #1) and the end (index #210): combinations #15, 105, 195. Before you convert the wheel to your picks, you make sure your lotto picks are sorted in ascending order; e.g. 3, 7, 12, 19, 22, 23, 35, 38, 42, 49.

![Register for the best software to generate well balanced lotto wheels, lottery wheels.](https://saliu.com/bbs/messages/HLINE.gif)

[

## Resources in Lottery, Software, Systems, Lotto Wheels, Strategies

](https://saliu.com/content/lottery.html)Lists the main pages on the subject of lottery, lotto, software, wheels and systems.

-   [_**Lottery Software Tools, Lotto Wheeling Software Wheels**_](https://saliu.com/free-lotto-tools.html) from the download site:
-   Wheel-632, Wheel-532, the best on-the-fly wheeling software; applies real lottery filtering.  
    ~ Superseded by the most powerful integrated packages
-   Pick532, Pick532 and, especially, the Bright software packages.  
    
-   Combinations, the universal combinations generator for any lotto, Keno, Powerball, Mega Millions, Euromillions;  
    
-   LexicoWheels, generator of lotto wheels based on lexicographical indexing; applies to any lotto, Keno, Powerball, Mega Millions, Euromillions or two-in-one lotto games.  
    
-   WheelCheck5, WheelCheck6, software to verify lotto wheels for missing combinations and generate lotto-5 and lotto6 systems.  
    
-   LottoWheeler, free wheeling software: fill out lotto wheels with player lotto numbers. Supersedes the old-time favorite FillWheel (still offered). The two pieces of software replace the theoretical lotto numbers in the SYS/WHEEL files by your picks (the lotto numbers you want to play).
-   Shuffle, SuperFormula to shuffle (randomize, randomly arrange) your lotto picks. Repeat the randomizing procedure several times. The randomized wheels can be used directly, without shuffling your lottery picks first.
-   The Main [_**Lotto, Lottery, Software, Strategy, Systems**_](https://saliu.com/LottoWin.htm), Wheeling Page.  
    Presenting software to create free winning lotto, lottery strategies, systems based on mathematics. Get your lotto systems or wheels, the best lottery, lotto software, combinations, winning numbers.
-   [**Lotto wheels**](https://saliu.com/lotto_wheels.html) _**for lottery games drawing 5 6 7 numbers**_.  
    The most advanced theory of lotto wheels or reduced lottery systems. Get also original lotto wheels for lotto games drawing 5, 6, or 7 numbers: Balanced, randomized, and free.
-   [The myth of lotto wheels or abbreviated lotto systems](https://saliu.com/bbs/messages/11.html).  
    Statistical analysis of real lottery data proves the negative effect of playing static (unbalanced or split) lotto wheels.
-   [WHEEL-632 available as lotto wheeling software](https://saliu.com/bbs/messages/wheel.html) — the best on-the-fly wheeling software; applies real lottery filtering.
-   Free [lottery software for players of lotto wheels](https://saliu.com/bbs/messages/857.html): Fill out lotto wheels with player's picks (numbers to play).
-   [Software to verify lotto wheels](https://saliu.com/check-wheels.html) for missing combinations and generate lotto 5, 6 abbreviated (reduced) systems.
-   [Check WHEEL and lotto wheels for winners](https://saliu.com/bbs/messages/90.html).
-   [_**The Best Lotto Wheels for 18 Numbers, 4 in 6 Guarantee**_](https://saliu.com/bbs/best-18-number-lotto-wheels.html).
-   [Genuine Powerball wheels](https://saliu.com/powerball_wheels.html).
-   [Genuine Mega Millions wheels](https://saliu.com/megamillions_wheels.html).
-   [Genuine Euromillions wheels](https://saliu.com/euro_millions_wheels.html).

![Lotto wheels, lottery wheels are lexicographically balanced and randomized.](https://saliu.com/bbs/messages/HLINE.gif)

**[Home](https://saliu.com/index.htm) | [Search](https://saliu.com/Search.htm) | [New Writings](https://saliu.com/bbs/index.html) | [Software](https://saliu.com/infodown.html) | [Odds, Generator](https://saliu.com/calculator_generator.html) | [Contents](https://saliu.com/content/index.html) | [Forums](https://forums.saliu.com/) | [Sitemap](https://saliu.com/sitemap/index.html)**

![The lotto wheels consist of balanced lexicographic indexes or ranks; software to generate best lotto wheels.](https://saliu.com/bbs/messages/HLINE.gif)
