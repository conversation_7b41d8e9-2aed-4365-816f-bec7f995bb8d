using WonderGridLotterySystem
using Statistics
using Dates

println("Testing FFG Calculator System")
println("=" ^ 40)

# Load lottery data
fm = FileManager()
data = read_data5_file(fm, "data/fan5.csv")
println("Loaded $(length(data)) lottery draws for FFG analysis")

# Create FFG calculator
calc = FFGCalculator()
println("FFG Calculator initialized with $(calc.degree_of_certainty * 100)% degree of certainty")

# Test FFG median calculations for various numbers
test_numbers = [1, 10, 14, 27, 39]  # Mix of frequent and infrequent numbers

println("\nFFG Median Analysis:")
println("=" ^ 50)

ffg_results = []

for number in test_numbers
    println("\nNumber $number:")
    
    # Calculate FFG median
    ffg_median = calculate_ffg_median(calc, number, data)
    
    # Get current skip for comparison
    analyzer = SkipAnalyzer(data)
    current_skip = get_current_skip(analyzer, number)
    
    # Calculate skip probability
    skip_prob = compute_skip_probability(calc, current_skip, ffg_median)
    
    # Check favorable timing
    is_favorable = is_favorable_timing(calc, number, current_skip, data)
    
    println("  FFG Median: $(round(ffg_median, digits=2))")
    println("  Current Skip: $current_skip")
    println("  Skip Probability: $(round(skip_prob * 100, digits=1))%")
    println("  Favorable Timing: $(is_favorable ? "YES" : "NO")")
    
    # Calculate actual skip statistics for comparison
    skips = calculate_skips(analyzer, number)
    if length(skips) > 1
        actual_median = median(skips)
        actual_mean = mean(skips)
        println("  Actual Skip Median: $(round(actual_median, digits=2))")
        println("  Actual Skip Mean: $(round(actual_mean, digits=2))")
        println("  FFG vs Actual Median Ratio: $(round(ffg_median / actual_median, digits=2))")
    end
    
    push!(ffg_results, (number, ffg_median, current_skip, is_favorable))
end

# Analyze FFG medians for all numbers
println("\n" * "=" ^ 50)
println("Complete FFG Analysis (All Numbers)")
println("=" ^ 50)

all_ffg_medians = []
all_favorable = []
all_unfavorable = []

for number in 1:39
    ffg_median = calculate_ffg_median(calc, number, data)
    analyzer = SkipAnalyzer(data)
    current_skip = get_current_skip(analyzer, number)
    is_favorable = is_favorable_timing(calc, number, current_skip, data)
    
    push!(all_ffg_medians, ffg_median)
    
    if is_favorable
        push!(all_favorable, (number, current_skip, ffg_median))
    else
        push!(all_unfavorable, (number, current_skip, ffg_median))
    end
end

println("FFG Median Statistics:")
println("  Mean FFG Median: $(round(mean(all_ffg_medians), digits=2))")
println("  Median FFG Median: $(round(median(all_ffg_medians), digits=2))")
println("  Min FFG Median: $(round(minimum(all_ffg_medians), digits=2))")
println("  Max FFG Median: $(round(maximum(all_ffg_medians), digits=2))")
println("  Standard Deviation: $(round(std(all_ffg_medians), digits=2))")

println("\nFavorable Numbers ($(length(all_favorable)) total):")
sort!(all_favorable, by = x -> x[2] / x[3])  # Sort by skip/median ratio
for i in 1:min(15, length(all_favorable))
    number, skip, median = all_favorable[i]
    ratio = skip / median
    println("  $i. Number $number: skip $skip vs median $(round(median, digits=1)) (ratio: $(round(ratio, digits=2)))")
end

println("\nUnfavorable Numbers ($(length(all_unfavorable)) total):")
sort!(all_unfavorable, by = x -> x[2] / x[3], rev = true)  # Sort by skip/median ratio (highest first)
for i in 1:min(10, length(all_unfavorable))
    number, skip, median = all_unfavorable[i]
    ratio = skip / median
    println("  $i. Number $number: skip $skip vs median $(round(median, digits=1)) (ratio: $(round(ratio, digits=2)))")
end

# Test theoretical vs empirical FFG
println("\n" * "=" ^ 50)
println("Theoretical vs Empirical FFG Comparison")
println("=" ^ 50)

# Theoretical FFG median for Lotto 5/39
theoretical_median = calculate_theoretical_ffg_median(calc)
println("Theoretical FFG median for Lotto 5/39: $(round(theoretical_median, digits=2))")
println("Average empirical FFG median: $(round(mean(all_ffg_medians), digits=2))")
println("Difference: $(round(mean(all_ffg_medians) - theoretical_median, digits=2))")

# Performance test
println("\n" * "=" ^ 50)
println("Performance Test")
println("=" ^ 50)

start_time = time()
for number in 1:39
    ffg_median = calculate_ffg_median(calc, number, data)
    current_skip = get_current_skip(SkipAnalyzer(data), number)
    skip_prob = compute_skip_probability(calc, current_skip, ffg_median)
    is_favorable = is_favorable_timing(calc, number, current_skip, data)
end
elapsed_time = time() - start_time

println("Calculated FFG analysis for all 39 numbers in $(round(elapsed_time, digits=3)) seconds")
println("Average time per number: $(round(elapsed_time / 39 * 1000, digits=2)) ms")

# Test different degree of certainty values
println("\n" * "=" ^ 50)
println("Degree of Certainty Analysis")
println("=" ^ 50)

dc_values = [0.25, 0.5, 0.75, 0.9]
test_number = 10

println("Testing different DC values for number $test_number:")
current_skip = get_current_skip(SkipAnalyzer(data), test_number)

for dc in dc_values
    calc_dc = FFGCalculator(dc)
    ffg_median = calculate_ffg_median(calc_dc, test_number, data)
    skip_prob = compute_skip_probability(calc_dc, current_skip, ffg_median)
    
    println("  DC $(dc * 100)%: FFG median $(round(ffg_median, digits=2)), skip prob $(round(skip_prob * 100, digits=1))%")
end