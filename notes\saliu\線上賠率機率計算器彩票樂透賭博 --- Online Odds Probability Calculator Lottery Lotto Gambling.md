---
created: 2025-07-24T22:50:28 (UTC +08:00)
tags: [online,odds,probability,calculator,calculate,lotto,lottery,power ball,PowerBall,gambling,keno,Euro Millions,roulette,sports betting,horseracing,soccer pools,1X2,horse races,pick-3,pick-4,]
source: https://saliu.com/online_odds.html
author: 
---

# 線上賠率機率計算器彩票樂透賭博 --- Online Odds Probability Calculator Lottery Lotto Gambling

> ## Excerpt
> Online calculator of odds, probabilities for lottery, lotto, Powerball, Mega Millions, roulette, keno, sport betting, horse racing, Euromillions, soccer pools.

---
這就是：有史以來_**最好的線上賠率、機率計算器**_ ！  
2003 年 8 月 4 日由 _WayBack Machine_ ( _web.archive.org_ ) 首次捕獲。

### IonSaliuCalculator，版本 6.1，2004 年 2 月 16 日

_IonSaliuCalculator_ 是一個 _ActiveX 控制項_ 。它需要 Microsoft Internet Explorer 4 或更高版本。最近，Google Chrome 和 Firefox 提供免費擴充功能來模擬 Internet Explorer（例如 _IE Tab_ ），從而執行 ActiveX 控制項。此外，在安全性提示字元下，按一下 _「允許安裝」_ 和 _「允許運行」_ 。您可能需要將 _//saliu.com_ 新增至您的 _「受信任的網站」_ （透過 _「工具」、「Internet 選項」、「安全性」標籤_ ）。賠率、機率計算器保證是安全的。全球數百萬彩票玩家、賭徒和數學愛好者已經運行了數十億次該在線應用程序，從未出現過任何問題。唯一的問題是，很少出現_同時在線用戶過多的情況_ 。

最好的線上賠率計算器涵蓋了許多領域：選秀遊戲、樂透、基諾、強力球、超級百萬、歐洲百萬彩票、賽馬、體育博彩、足球彩票、輪盤賭。  
賠率的計算方式如下：  
\- _正是_如此 -  
\- _至少_ 。

例如，樂透遊戲需要 3 個參數：  
\- 遊戲中的最大數字（例如 49）  
\- 每個組合的數字（例如 6）  
\- 池大小（例如，玩 18 個數字的系統）。  

```
<span size="5" face="Courier New" color="#c5b358">The odds calculated as EXACTLY in a lotto game '6/49':

 -  0 of 6 in 18 from 49  = 1 in 18.99 
 -  1 of 6 in 18 from 49  = 1 in 4.57 
 -  2 of 6 in 18 from 49  = 1 in 2.9 
 -  3 of 6 in 18 from 49  = 1 in 3.81 
 -  4 of 6 in 18 from 49  = 1 in 9.83 
 -  5 of 6 in 18 from 49  = 1 in 52.65 
 -  6 of 6 in 18 from 49  = 1 in 753.28 

 The odds calculated as AT LEAST in a lotto game '6/49':

 -  0 of 6 in 18 from 49  = 1 in 1 
 -  1 of 6 in 18 from 49  = 1 in 1.06 
 -  2 of 6 in 18 from 49  = 1 in 1.37 
 -  3 of 6 in 18 from 49  = 1 in 2.6 
 -  4 of 6 in 18 from 49  = 1 in 8.19 
 -  5 of 6 in 18 from 49  = 1 in 49.21 
 -  6 of 6 in 18 from 49  = 1 in 753.28 

The odds calculated as EXACTLY in a lotto game '6/49':

 -  0 of 6 in 6 from 49  = 1 in 2.29 
 -  1 of 6 in 6 from 49  = 1 in 2.42 
 -  2 of 6 in 6 from 49  = 1 in 7.55 
 -  3 of 6 in 6 from 49  = 1 in 56.66 
 -  4 of 6 in 6 from 49  = 1 in 1032.4 
 -  5 of 6 in 6 from 49  = 1 in 54200.84 
 -  6 of 6 in 6 from 49  = 1 in 13983816 

 The probabilities calculated as AT LEAST in a lotto game '6/49':

 -  0 of 6 in 6 from 49  = 1 in 1 
 -  1 of 6 in 6 from 49  = 1 in 1.77 
 -  2 of 6 in 6 from 49  = 1 in 6.62 
 -  3 of 6 in 6 from 49  = 1 in 53.66 
 -  4 of 6 in 6 from 49  = 1 in 1013.03 
 -  5 of 6 in 6 from 49  = 1 in 53991.57 
 -  6 of 6 in 6 from 49  = 1 in 13983816 
</span>
```

如果將池大小設定為每個組合的數字（例如 6），則結果將反映常用的機率：

基諾遊戲的機率（賠率）採用與樂透相同的函數計算，並使用三個參數。例如：80、20、20。

為 pick-3 遊戲計算的機率。

```
<span size="5" face="Courier New" color="#c5b358">The pick-3 game draws 3 digits from 0 to 9; e.g. 090.
The odds of the pick-3 game are:
- straight pick: 1 in 1000;
 - boxed: 1 in 220;
 - front/back pair: 1 in 100.
</span>
```

為 pick-4 遊戲計算的機率。

```
<span size="5" face="Courier New" color="#c5b358">The pick-4 game draws 4 digits from 0 to 9; e.g. 0903.
The odds of the pick-4 game are:
- straight pick: 1 in 10,000;
 - boxed: 1 in 715;
 - front/back 3-digits: 1 in 1000.
</span>
```

計算強力球遊戲的機率（12 種可能的賠率元素）。

```
<span size="5" face="Courier New" color="#c5b358"> The odds calculated as EXACTLY in a Powerball game 
 '5/53 AND 1/42':

~ 0 of 5 in 5 from 53  AND  0 of 1 in 1 from 42  = 1 in 1.72 
~ 0 of 5 in 5 from 53  AND  1 of 1 in 1 from 42  = 1 in 70.39 
~ 1 of 5 in 5 from 53  AND  0 of 1 in 1 from 42  = 1 in 3.02 
~ 1 of 5 in 5 from 53  AND  1 of 1 in 1 from 42  = 1 in 123.88 
~ 2 of 5 in 5 from 53  AND  0 of 1 in 1 from 42  = 1 in 17 
~ 2 of 5 in 5 from 53  AND  1 of 1 in 1 from 42  = 1 in 696.85 
~ 3 of 5 in 5 from 53  AND  0 of 1 in 1 from 42  = 1 in 260.61 
~ 3 of 5 in 5 from 53  AND  1 of 1 in 1 from 42  = 1 in 10685 
~ 4 of 5 in 5 from 53  AND  0 of 1 in 1 from 42  = 1 in 12248.66 
~ 4 of 5 in 5 from 53  AND  1 of 1 in 1 from 42  = 1 in 502194.88 
~ 5 of 5 in 5 from 53  AND  0 of 1 in 1 from 42  = 1 in 2939677.32 
~ 5 of 5 in 5 from 53  AND  1 of 1 in 1 from 42  = 1 in 120526770 


 The probability calculated as AT LEAST in a Powerball game 
 '5/53 AND 1/42':

~ 0 of 5 in 5 from 53  AND  0 of 1 in 1 from 42  = 1 in 1 
~ 0 of 5 in 5 from 53  AND  1 of 1 in 1 from 42  = 1 in 42 
~ 1 of 5 in 5 from 53  AND  0 of 1 in 1 from 42  = 1 in 2.48 
~ 1 of 5 in 5 from 53  AND  1 of 1 in 1 from 42  = 1 in 104.14 
~ 2 of 5 in 5 from 53  AND  0 of 1 in 1 from 42  = 1 in 15.56 
~ 2 of 5 in 5 from 53  AND  1 of 1 in 1 from 42  = 1 in 653.33 
~ 3 of 5 in 5 from 53  AND  0 of 1 in 1 from 42  = 1 in 249.08 
~ 3 of 5 in 5 from 53  AND  1 of 1 in 1 from 42  = 1 in 10461.49 
~ 4 of 5 in 5 from 53  AND  0 of 1 in 1 from 42  = 1 in 11907.41 
~ 4 of 5 in 5 from 53  AND  1 of 1 in 1 from 42  = 1 in 500111.08 
~ 5 of 5 in 5 from 53  AND  0 of 1 in 1 from 42  = 1 in 2869685 
~ 5 of 5 in 5 from 53  AND  1 of 1 in 1 from 42  = 1 in 120526770 

</span>
```

針對 Euromillions 遊戲計算的機率（18 種可能的賠率元素）。

![Binomial Standard Deviation Formula and odds, probabilities online.](https://saliu.com/HLINE.gif)

```
<span size="5" face="Courier New" color="#c5b358">
 The odds calculated as EXACTLY in a Euromillions game
 '5/50 AND 2/9':

~ 0 of 5 in 5 from 50  AND  0 of 2 in 2 from 9  = 1 in 2.97 
~ 0 of 5 in 5 from 50  AND  1 of 2 in 2 from 9  = 1 in 4.46 
~ 0 of 5 in 5 from 50  AND  2 of 2 in 2 from 9  = 1 in 62.43 
~ 1 of 5 in 5 from 50  AND  0 of 2 in 2 from 9  = 1 in 4.88 
~ 1 of 5 in 5 from 50  AND  1 of 2 in 2 from 9  = 1 in 7.31 
~ 1 of 5 in 5 from 50  AND  2 of 2 in 2 from 9  = 1 in 102.39 
~ 2 of 5 in 5 from 50  AND  0 of 2 in 2 from 9  = 1 in 25.6 
~ 2 of 5 in 5 from 50  AND  1 of 2 in 2 from 9  = 1 in 38.39 
~ 2 of 5 in 5 from 50  AND  2 of 2 in 2 from 9  = 1 in 537.53 
~ 3 of 5 in 5 from 50  AND  0 of 2 in 2 from 9  = 1 in 366.88 
~ 3 of 5 in 5 from 50  AND  1 of 2 in 2 from 9  = 1 in 550.33 
~ 3 of 5 in 5 from 50  AND  2 of 2 in 2 from 9  = 1 in 7704.58 
~ 4 of 5 in 5 from 50  AND  0 of 2 in 2 from 9  = 1 in 16142.93 
~ 4 of 5 in 5 from 50  AND  1 of 2 in 2 from 9  = 1 in 24214.4 
~ 4 of 5 in 5 from 50  AND  2 of 2 in 2 from 9  = 1 in 339001.6 
~ 5 of 5 in 5 from 50  AND  0 of 2 in 2 from 9  = 1 in 3632160 
~ 5 of 5 in 5 from 50  AND  1 of 2 in 2 from 9  = 1 in 5448240 
~ 5 of 5 in 5 from 50  AND  2 of 2 in 2 from 9  = 1 in 76275360 


 The probabilities calculated as AT LEAST in a Euromillions game 
 '5/50 AND 2/9':

~ 0 of 5 in 5 from 50  AND  0 of 2 in 2 from 9  = 1 in 1 
~ 0 of 5 in 5 from 50  AND  1 of 2 in 2 from 9  = 1 in 2.4 
~ 0 of 5 in 5 from 50  AND  2 of 2 in 2 from 9  = 1 in 36 
~ 1 of 5 in 5 from 50  AND  0 of 2 in 2 from 9  = 1 in 2.36 
~ 1 of 5 in 5 from 50  AND  1 of 2 in 2 from 9  = 1 in 5.67 
~ 1 of 5 in 5 from 50  AND  2 of 2 in 2 from 9  = 1 in 85.03 
~ 2 of 5 in 5 from 50  AND  0 of 2 in 2 from 9  = 1 in 13.94 
~ 2 of 5 in 5 from 50  AND  1 of 2 in 2 from 9  = 1 in 33.45 
~ 2 of 5 in 5 from 50  AND  2 of 2 in 2 from 9  = 1 in 501.73 
~ 3 of 5 in 5 from 50  AND  0 of 2 in 2 from 9  = 1 in 209.24 
~ 3 of 5 in 5 from 50  AND  1 of 2 in 2 from 9  = 1 in 502.17 
~ 3 of 5 in 5 from 50  AND  2 of 2 in 2 from 9  = 1 in 7532.62 
~ 4 of 5 in 5 from 50  AND  0 of 2 in 2 from 9  = 1 in 9375.04 
~ 4 of 5 in 5 from 50  AND  1 of 2 in 2 from 9  = 1 in 22500.11 
~ 4 of 5 in 5 from 50  AND  2 of 2 in 2 from 9  = 1 in 337501.59 
~ 5 of 5 in 5 from 50  AND  0 of 2 in 2 from 9  = 1 in 2118760 
~ 5 of 5 in 5 from 50  AND  1 of 2 in 2 from 9  = 1 in 5085024 
~ 5 of 5 in 5 from 50  AND  2 of 2 in 2 from 9  = 1 in 76275360 

</span>
```

為賽馬計算的賠率。

```
<span size="5" face="Courier New" color="#c5b358">The odds in horseracing, 8 horses in the race:

 - to win probability (top 1) = 1 in 8 
 - exacta probability (top 2) = 1 in 56 
 - trifecta probability (top 3) = 1 in 336 
 - superfecta probability (top 4) = 1 in 1680 
 - quinfecta probability (top 5) = 1 in 6720 
</span>
```

針對單零和雙零輪盤賭桌計算的機率。

```
<span size="5" face="Courier New" color="#c5b358">       Various odds at 0 and 00 roulette

 Type of Bet                Single 0     Double 0

 - straight-up (1 number):  1 in 37      1 in 38    
 - split (2 numbers):       1 in 18.5    1 in 19    
 - street (3 numbers):      1 in 12.3    1 in 12.7  
 - corner (4 numbers):      1 in 9.25    1 in 9.5   
 - fiveline (5 numbers):    1 in 7.4     1 in 7.6   
 - sixline (6 numbers):     1 in 6.2     1 in 6.3   
 - column (12 numbers):     1 in 3.1     1 in 3.2   
 - dozen (12 numbers):      1 in 3.1     1 in 3.2   
 - black/red (18 numbers):  1 in 2.06    1 in 2.11  
 - even/odd (18 numbers):   1 in 2.06    1 in 2.11  
 - low/high (18 numbers):   1 in 2.06    1 in 2.11  

</span>
```

美國體育博彩計算的賠率；即在每場比賽中押註一支球隊。

足球彩券 1X2 的賠率計算方式；即每場比賽有三種可能結果之一；1 = 主隊獲勝；2 = 客隊獲勝；X = 平手（平手）。

```
<span size="5" face="Courier New" color="#c5b358">The AT LEAST odds in American sports betting, 5 games:

 - parlay probability 0 of 5  = 1 in 1 
 - parlay probability 1 of 5  = 1 in 2 
 - parlay probability 2 of 5  = 1 in 4 
 - parlay probability 3 of 5  = 1 in 8 
 - parlay probability 4 of 5  = 1 in 16 
 - parlay probability 5 of 5  = 1 in 32 


 The AT LEAST odds in soccer pools 1X2 for 13 games:

 -  0 of 13  = 1 in 1 
 -  1 of 13  = 1 in 3 
 -  2 of 13  = 1 in 9 
 -  3 of 13  = 1 in 27 
 -  4 of 13  = 1 in 81 
 -  5 of 13  = 1 in 243 
 -  6 of 13  = 1 in 729 
 -  7 of 13  = 1 in 2187 
 -  8 of 13  = 1 in 6561 
 -  9 of 13  = 1 in 19683 
 -  10 of 13  = 1 in 59049 
 -  11 of 13  = 1 in 177147 
 -  12 of 13  = 1 in 531441 
 -  13 of 13  = 1 in 1594323 
</span>
```

線上數位、組合產生器中還存在以下功能：IonSaliuGenerator。  
\- 計算二項式標準差；  
\- 計算賭博的基本公式：單一機率為 p 的事件以確定性程度 DC 出現所需的試驗次數；  
\- 在富文本框中開啟一個檔案；  
\- 編輯富文本框中的文件；或者，編輯由賠率計算函數創建的內容；  
\- 將文件保存在富文本框中；或者，保存由賠率計算函數創建的內容；  
\- 在富文本方塊中列印文件；或者，將賠率計算函數所建立的內容列印到預設印表機；  
\- 變更富文本框中文件的字型；選擇富文本框的內容，然後按一下「字型」按鈕。

-   **Ion-Odds-Calculator** 是一個 _ActiveX 控制項_ 。
-   您需要啟用瀏覽器以允許下載和執行 ActiveX 控制項。與我的所有軟體一樣， **IonOddsCalc** ActiveX 控制賠率計算器保證安全。目前尚未收到與我的軟體相關的安全問題報告。使用 _“儲存”_ 選項時，請謹慎命名檔案名稱。請勿覆蓋您不想覆蓋的文件！

也可以執行 **IonSaliuGen** 線上 ActiveX 控制項。它是[_**最好的隨機數字和組合生成器**_](https://saliu.com/generator.html) ：樂透、強力球、彩票、賽馬、輪盤賭、體育博彩、足球彩票 1X2、歐洲百萬彩票。

在一個地方運行兩個在線 ActiveX 控件，無需太多演示 - [_**在線賠率、概率計算器、隨機數組合生成器**_](https://saliu.com/calculator_generator.html) ：樂透、強力球、超級百萬、彩票、賽馬、輪盤賭、體育博彩、足球彩票 1X2、歐洲百萬彩票。

[_**計算、產生任何數字和單字的排列、排列、組合**_](https://saliu.com/permutations.html) （包括強力球）、指數。

[_**適用於任何樂透、基諾、強力球遊戲的通用組合產生器**_](https://saliu.com/combinations.html) ： _分 K 步，每次取出 M 個 N 個數字_ 。

## [機率論、數學、賠率、隨機數產生器、軟體方面的資源](https://saliu.com/content/probability.html)

查看有關機率論、數學、機率、組合學以及軟體主題的頁面和資料的綜合目錄。

-   _**下載**__**樂透、樂透、賭博數學**_[**<u>軟體 </u>**](https://saliu.com/infodown.html) 。

### <u><span lang="zh-TW"><span data-immersive-translate-translation-element-mark="1"><span data-immersive-translate-translation-element-mark="1">最佳<i>賠率，機率計算器 </i>，版本 6.1，2004 年 2 月</span></span></span></u>

-   Internet Explorer 的情況越來越糟，其瀏覽器市佔率急劇下降。
-   因此，我決定將這兩個 ActiveX 控制項作為 Windows 的**獨立軟體**提供。這樣，彩票玩家就可以隨時在自己的電腦上**離線**運行該軟體。該軟體可以**免費**下載並永久運行，沒有任何附加條件。[](https://saliu.com/gambling-lottery-lotto/odds-generator.html)
    
    ### [_離線賠率計算器，彩票、樂透、強力球、超級百萬、歐洲百萬、賽馬、體育、輪盤賭的數位組合產生器_](https://saliu.com/gambling-lottery-lotto/odds-generator.html)
    

![Ion Saliu's Theory of Probability Book founded on mathematics, also applied to odds calculating.](https://saliu.com/probability-book-Saliu.jpg) [閱讀 Ion Saliu 的第一本印刷版書籍： **_《機率論，現場版！ 》_**](https://saliu.com/probability-book.html)  
~ 建立在具有廣泛科學應用的寶貴數學發現之上，包括機率論與賠率計算、彩票、賭博、大多數遊戲中的機率之間的有機聯繫。

![Run online calculator for lotto, Powerball, Mega Millions, lottery, horse-racing, roulette, sports betting, soccer pools 1x2, Euromillions.](https://saliu.com/HLINE.gif)

**[首頁](https://saliu.com/index.htm) | [搜尋](https://saliu.com/Search.htm) | [新作](https://saliu.com/bbs/index.html) | [基本公式](https://saliu.com/formula.htm) | [伊翁薩利烏](https://saliu.com/Ion_Saliu.html) | [目錄](https://saliu.com/content/index.html) | [論壇](https://forums.saliu.com/) | [網站地圖](https://saliu.com/sitemap/index.html)**

![The web site for odds calculator in lottery, lotto, gambling.](https://saliu.com/HLINE.gif)
