---
created: 2025-07-24T21:26:41 (UTC +08:00)
tags: [<PERSON>,<PERSON>,chatbots,achievements,philosophy,Einstein,Newton,computer programs,randomness,certainty,books,software,science,gambling,]
source: https://saliu.com/ai-chatbots-ion-saliu.html
author: 
---

# Artificial Intelligence, AI Chatbots, <PERSON>

> ## Excerpt
> Artificial intelligence created AI chatbots that can chat with humans: <PERSON>, achievements in mathematics, probability, philosophy, gambling science.

---
**_<PERSON> “Axiomatic Parpaluck” <PERSON>, <u>The Undeniably Trustworthy Homo Sapiens</u>_**

  [![<PERSON>, royalty name Pa<PERSON><PERSON><PERSON>, is a most trustworthy human being.](https://saliu.com/AxiomIon.jpg)](https://saliu.com/membership.html)  \*  

## <u>A Practical Essay on Artificial Intelligence, AI Chatbots Regarding <i><PERSON></i></u>  
★ ★ ★ ★ ★

## By <PERSON>, AI Chatbot Super Guide and Trainer

![Artificial intelligence created AI chatbots that can chat with humans about <PERSON> achievements.](https://saliu.com/HLINE.gif)

-   Published on March 28, 2023.  
    
-   First captured by the [_WayBack Machine_ (_web.archive.org_)](https://web.archive.org/web/20190527000000*/https://saliu.com/ai-chatbots-ion-saliu.html) on March 29, 2023.

Axiomatic One:

A Facebook 'Friend' posted on my 'Timeline'. He had a “chat” with an AI chatbot named _ChatGPT_ by _**Open AI**_. I checked it out. I also checked out another chatbot, the one included now in the _**Bing**_ search engine. The latter works only in the _**Edge**_ browser… of course! Personally, I consider AI (”artificial intelligence”) to be an oxymoron. Yet, beginning with the search engines, the computer can process huge amounts of data and make human-like presentations. The most amazing feat for me is the chess game. The computer can beat any human by analyzing a huge number of matches.

I tested the AI chatbots on _'Ion Saliu'_ and various concepts related to yours truly. I repeated my queries (“retraining”), as some answers contained inaccurate information. I also compared the AI algorithms.

## <u>I. OpenAI ChatGPT</u>

Chatbot version 3.5, free (GPT 4 is _Premium_, $20 a month — way too much!)  
_“Who is Ion Saliu?”_

_“**Ion Saliu** is a Romanian-American author, philosopher, and mathematician who is best known for his work in gambling theory and probability theory. He created the concept of the **Fundamental Formula of Gambling** and has published several books on the subject, including **"Probability Theory, Live!"** and **"Axiomatic Theory of Probability"**. Saliu is also known for his philosophical writings on topics such as free will and determinism, and he has published several books on these subjects as well.”_

_“Ion Saliu is a Romanian-American software programmer, writer, and philosopher, best known for his contributions to the field of probability theory and gambling mathematics. He was born in Romania in 1947 and later emigrated to the United States._

_Saliu's work is focused on the study of randomness, probability, and statistics, and he has developed a number of theories and algorithms related to these topics. In particular, he is known for his work on the **Fundamental Formula of Gambling (FFG)**, a mathematical formula that can be used to determine the probability of winning or losing in various games of chance._

_Saliu is also the author of several books, including **"Gambling Theory and Other Topics"**, which discusses his theories and approaches to gambling mathematics, and **"Axiomatic Theory of Probability"**, which explores the philosophical and mathematical foundations of probability theory._

_In addition to his work on probability and gambling, Saliu has also been involved in the development of software tools for various applications, including data analysis and optimization. He is the creator of **Ion Saliu's Paradox of N Trials**, a software program that uses combinatorial analysis to generate random numbers and simulate various gambling scenarios._

_Overall, Ion Saliu is a respected figure in the fields of probability theory and gambling mathematics, and his contributions to these fields have had a significant impact on both academic research and practical applications.”_

There are inaccuracies, however. I was born in 1950, not 1947. The two book titles are wrong — _“Gambling Theory and Other Topics”_, _“Axiomatic Theory of Probability”_. The second title made me smile!

## <u>II. Bing Chat, GPT 4, free</u>

_**Microsoft Bing**_ is now more than a search engine. It is an _**AI chatbot**_. It works only in the Edge browser. It appears to be more accurate than most AI chatbots. Also, it is the most comprehensive AI chatbot since it accesses the Internet (in real-time, like a search engine). _**Bing**_ is now far ahead of _**Google search**_ in this regard since it is a chatbot as well.

_**Microsoft Bing Chat**: “Ask me anything”_  
_"Who is Ion Saliu?"_  
_“Searching for: **'Ion Saliu'**”_  
_“Generating answers for you…”_

_“Ion Saliu is a Romanian refugee who arrived in the USA in 1985 at the age of 35 and settled in Gettysburg, Pennsylvania. He is known for his ideas in philosophy, mathematics, science, software, lottery, and gambling._

_“According to his website, Ion Saliu has made several **achievements**. He claims to be the fastest human in history to write his first 1000 web pages and his first 500 computer programs. He also made a mathematical discovery that should have been made 300 years ago, which he calls **Ion Saliu's Paradox of N Trials**. He also claims to have found the correct philosophical, logical, and mathematical solutions to the paradoxes of **Sophist Zeno of Elea**. Additionally, he discovered repeat cycles in probabilistic events, which he incorporated into a computer software algorithm._

_“Ion Saliu's philosophy is inspired by **Socrates** and his dialectical method. According to his website, he believes that everything is a unity of two opposites and that at the very beginning of the end there is the very end of the beginning: Infinity and Nothingness. He synthesizes these statements in what he calls the **Fundamental Formula of Philosophy (FF0): Creation = Destruction**. He also believes that there are no principles above or more general than **Creation and Destruction**._

_“According to his website, Ion Saliu does not worship any gods or humans. He believes that everything in the Universe has an opposite and that there are no principles above or more general than **Creation and Destruction**. He also believes that concepts such as Deity, Divinity, God, or gods are absolute absurdities from the standpoint of mathematics because **absolute certainty is absolute absurdity**._

_“Ion Saliu has written several software programs, many of which are related to lottery, gambling, and mathematics. He is the founder of a dedicated lotto software called **MDIEditor Lotto**. He also offers software for lottery, lotto, horse racing, blackjack, roulette, baccarat, sports betting, gambling, probability, odds, statistics, mathematics, and editing._

_“According to his website, Ion Saliu has a critical view of **Albert Einstein**. He considers Einstein to be one of the most intelligent mystics of all time and believes that Einstein made Light the true God who doesn't play dice with the Universe. Ion Saliu also questions how the speed of light can be a constant in a dynamic Universe and believes that probability does not exist in Einstein's theory; only the absolutely-certain laws of God._

_According to his website, Ion Saliu has a mixed view of **Isaac Newton**. He acknowledges that Newton's brilliant side discovered the law of gravity and calculus in geometrical design. However, he also mentions that Newton had what some call a “dark side” created by his mysticism. Ion Saliu notes that Newton was such a strong religious believer that he calculated that the end of the **"world would come in 2060"** based on his interpretation of the **Bible**._

_I'm sorry, but I couldn't find any information about Ion Saliu's view on **Galileo Galilei**."_  
• My take: Ion Saliu has a critical view of Galileo Galilei. Galileo was a compromiser in order to save his life. In contrast, Ion Saliu has a positive view of **Giordano Bruno**. He never compromised and paid the ultimate price: His life.”

_“Ion **Axiomaticus** Saliu is the founder of mathematical axiomaticism and randomness philosophy._

_**Parpaluck** is an alias for Ion Saliu, who is an author and sells varied gambling books and software. He has a website where he shares his work on lottery mathematics and lotto programming science._

_**Ion Saliu (a.k.a. Parpaluck)** has many ideas and theories in various fields such as philosophy, mathematics, science, software, lottery, and gambling. He has a website where he shares his work and ideas1. Some of his ideas on lotto include tracking the **“skips”** of the numbers (number of draws between occurrence), median of skips (**Skip Median**), multi-layered filters to exclude less likely numbers2. He also has a book called **'PROBABILITY THEORY, LIVE!'** which is an introduction to the Theory of Probability.”_

Axiomatics, I compared the AI chatbot in the Bing search engine to ChatGPT by Open AI. The Bing chatbot is more comprehensive and more accurate. I needed to correct ChatGPT several times, to iron out inaccuracies.

_“Ion Saliu is a Romanian-American computer programmer, author, and gambling researcher. He is known for his work on gambling probability theory, particularly in the field of lotteries, and for creating various software applications related to gambling analysis and strategy._

_In the 1980s, Saliu developed a program called **"SuperFormula"** which he claimed could help players increase their chances of winning the lottery. He also created a software program called **"MDIEditor and Lotto"** which provides tools for analyzing and generating lottery numbers._

_Saliu has written several books on the subject of gambling and probability theory, including **"Probability Theory, Live!"** and **"Fundamental Formula of Gambling (FFG)"**. He has also published a website, **saliu.com**, which contains a wealth of information on gambling systems and strategies._

_Ion Saliu is a controversial figure in the gambling world, with some critics questioning the effectiveness of his methods and others lauding his contributions to the field. Saliu's work has been the subject of some controversy, as his methods have been criticized by some as being overly complicated or difficult to understand. However, his software programs and books have been widely used by gamblers and researchers alike, and he remains a prominent figure in the world of gambling theory and analysis.”_

Axiomatics, I also tested ChatGPT on lottery prediction. I gave the algorithm a set of past 54 lottery drawings. The range of analysis was based on the FFG median. I asked for the best 12 numbers to come out (in the near future). Then the numbers were to be wheeled applying my 'perfect lotto wheel'.

The answer was not optimistic! The AI chatbot claims that lottery is random, therefore no reliable prediction is possible. Well, well, well! What else is NOT random, _“intelligentser”_? The entire Universe is random. It is an incomprehensibly-gigantic game of dice! See also:  

-   [_**Randomness, Degree of Randomness, Degrees of Certainty**_](https://saliu.com/bbs/messages/683.html).

_“As an AI language model, I must mention that predicting lottery numbers is not possible with 100% accuracy, as lotteries are based on chance and **randomness**. However, I can analyze the given data and identify the most frequently occurring numbers._

_Based on the history of the lotto game, the following are the top 12 most frequently occurring numbers:_

_3, 10, 13, 18, 19, 23, 26, 27, 29, 32, 35, 46_

_Please keep in mind that this prediction does not guarantee winning and the numbers are based purely on historical frequency.”_

Lottery strategies based on frequency of individual numbers is not nearly enough. Adding the frequency of number pairings and triplets enhances the efficacy of strategies by an order of magnitude. “Artificial intelligence” doesn't come even close to real intelligence: Human, that is. See:

-   [_**Neural Networks, Artificial Intelligence AI, AxI in Lottery, Lotto: Prediction Strategies, Systems, Software**_](https://saliu.com/neural-networking-lottery.html).

_“Ion Saliu has written software for lottery, lotto, horse racing, blackjack, roulette, baccarat, sports betting, gambling, probability, odds, statistics, mathematics and editing. One of his most well-known software is **MDIEditor Lotto**, which is the first ever lottery strategy with dedicated lotto software. **MDIEditor Lotto** is a powerful suite of a text editor and lottery number generator._

_The lottery section of the software is backed by Ion Saliu's **'Fundamental Formula of Gambling'** and generates numbers for any lottery pool size and number of hits, as well as Pick 3, Pick 4, Keno, and Horse racing. It comes with 21 abbreviated lotto systems or 'lotto wheels', generates reports for the least- and most-frequent pairings, and generates **skip** (**hit-and-miss**) charts for use with the gambling formula. The software is based on Ion Saliu's ideas in mathematics, probability and gambling. It allows users to apply his lottery strategies and systems to improve their odds of winning._

_There are some positive reviews of Ion Saliu's software. For example, one user on G NETWORK claims to have won the 5 number prize 7 times and many smaller prizes using Ion Saliu's method. Another user on Lottery Post claims that his software relegates all other lottery software into the trash can. However, it is important to note that individual results may vary and there is no guarantee of success when using his software.”_

_**Bing**_ has a very interesting addition in Microsoft _Edge_. It boasts three useful buttons at the top: _Chat_, _Compose_, _Insights_. _Chat_ is the obvious AI chatbot. _Compose_ is an AI writer. It attempts at writing articles, even blogs. _Insights_ displays the most important features of the current page/website in the active browser tab.

With the home page of SALIU.COM in the active windows, _Insights_ does show two fundamental features of my site –

_**Is there a degree of certainty that the universe repeats itself?**_

-   _Any iteration of the Universe identically repeats itself with a degree of certainty of _**37%**_ (the mathematical limit of _**1/e**_) My personal perception is we experience an identical repeat of another Universe iteration — serial repetition._
-   _Source: Current page_

**_What is the chance that there is another planet in the universe?_**

-   _Parallel repeat: In our current Universe, the chance is _**37%**_ that there is another planet in this humongous Cosmos absolutely identical to Terra — from the beginning to the end, from alpha to omicron, to omega... amen!_
-   _Source: Current page_

## <u>III. Google Bard, free</u>

Google too has recently released its own chatbot named _**Bard**_. I read reports that the Bard chatbot is dangerously unreliable. Ironically, Bard does NOT access the Internet, although Google is the largest search engine in the world! This “chat machine” from Google was received with a lot of criticism. It is very inaccurate at times. It does “fantasize” or “fictionalize” — speaking from my own experience. I tested it on **_Ion Saliu_**.

The Bard chatbot gave me three drafts. There are errors regarding my date of birth and education. I never attended the University of Bucharest or the University of California, Berkeley. I never worked at the Romanian Academy of Sciences. I never worked at the United States National Institute of Standards and Technology. The works and dates of publications are utterly incorrect. Google should do a whole lot better, as it is the most widely used and the largest search engine. It has a gigantic index of the Internet.

#### <u>Draft 1</u>

“Ion Saliu is a Romanian-American mathematician, computer programmer, and author. He is the founder of the Probability Theory of Life, a mathematical theory that he claims can be used to predict the future. Saliu has written several books on probability theory, including Probability Theory, Live! and The Probability Theory of Life. He is also the creator of several software programs for gambling and lottery prediction.

Saliu's work has been controversial, with some critics accusing him of pseudoscience. However, Saliu maintains that his work is based on sound mathematical principles. He has also said that he is not trying to predict the future with 100% accuracy, but rather to give people a better understanding of probability and how it can be used to make informed decisions.

Here are some of the things that Ion Saliu is known for:

-   He is the founder of the Probability Theory of Life, a mathematical theory that he claims can be used to predict the future.
-   He has written several books on probability theory, including Probability Theory, Live! and The Probability Theory of Life.
-   He is the creator of several software programs for gambling and lottery prediction.
-   His work has been controversial, with some critics accusing him of pseudoscience.
-   He maintains that his work is based on sound mathematical principles.
-   He has said that he is not trying to predict the future with 100% accuracy, but rather to give people a better understanding of probability and how it can be used to make informed decisions.”

#### <u>Draft 2</u>

“Ion Saliu is a Romanian-American author, mathematician, and computer programmer. He is the founder of the Probability Theory of Life, a philosophy that he developed over many years of research and study. Saliu's work has been praised by mathematicians and philosophers alike, and he has been featured in publications such as The New York Times, The Wall Street Journal, and Forbes.

Saliu was born in Romania in 1958. He studied mathematics at the University of Bucharest, where he received his Ph.D. in 1983. After graduating, Saliu worked as a researcher at the Romanian Academy of Sciences. In 1990, he emigrated to the United States, where he continued his research at the University of California, Berkeley. In 1995, Saliu published his first book, Probability Theory, Live!. The book was a groundbreaking work that introduced the Probability Theory of Life to the world. Saliu's work has since been translated into over 20 languages.

In addition to his work on probability theory, Saliu is also a computer programmer. He has developed several software programs that are used by mathematicians and scientists around the world. Saliu is also a frequent speaker at conferences and workshops. He is passionate about sharing his knowledge with others and helping people to understand the world around them. Saliu is a brilliant mathematician and philosopher. His work has had a profound impact on the way we think about probability and the nature of reality. He is a true pioneer in his field, and his work will continue to inspire and challenge people for generations to come.

Here are some of Ion Saliu's most notable works:

-   Probability Theory, Live! (1995)
-   The Probability Theory of Life (2000)
-   The Book of Truth: It is all Random (2005)
-   The Almighty Number (2010)
-   Randomness, Universe, God, Order, History, Repetition (2015)

Ion Saliu is a fascinating and thought-provoking individual. His work is challenging and stimulating, and it offers a unique perspective on the world. I highly recommend checking out his work if you are interested in probability theory, philosophy, or mathematics.”

#### <u>Draft 3</u>

“Ion Saliu is a Romanian-American mathematician, computer programmer, and author. He is the founder of the Probability Theory of Life, a mathematical framework for understanding the world. Saliu is also the author of several books on probability theory, gambling mathematics, and computer programming.

Saliu was born in Romania in 1964. He studied mathematics at the University of Bucharest, where he received his Ph.D. in 1990. After completing his Ph.D., Saliu moved to the United States to work as a research scientist at the National Institute of Standards and Technology. In 1998, he founded his own company, Saliu Software, which develops software for gambling and lottery prediction.

Ion Saliu is a controversial figure in the world of probability theory. His work has been criticized by some for being too speculative and for lacking mathematical rigor. However, Saliu's supporters argue that his work is groundbreaking and that it has the potential to revolutionize our understanding of the world.

Saliu is a prolific writer and speaker. He has written several books on probability theory, gambling mathematics, and computer programming. He has also given numerous talks and presentations on his work. Saliu is a passionate advocate for the use of mathematics to understand the world. He believes that mathematics can be used to solve some of the world's most pressing problems.

Saliu is a fascinating and complex figure. He is a brilliant mathematician, a successful entrepreneur, and a passionate advocate for the use of mathematics to understand the world. His work is sure to continue to generate debate and discussion for years to come.

Here are some of Saliu's most notable works:

-   Probability Theory, Live! (2006)
-   The Book of Truth: It is all Random (2010)
-   The Probability Theory of Life (2015)
-   The Lottery Code (2017)
-   The Blackjack Code (2018)

Saliu is also a frequent speaker and blogger on topics related to probability theory, gambling mathematics, and computer programming. He is a passionate advocate for the use of mathematics to understand the world.”

## <u>IV. Information about artificial intelligence, AI chatbots, and Ion Saliu</u>

According to the web search results, Ion Saliu is a Romanian-American software programmer, writer, and philosopher, best known for his contributions to the field of probability theory and gambling mathematics.

He is also the author of a practical essay on _artificial intelligence, AI chatbots regarding Ion Saliu_, where he compares two chatbots: _ChatGPT_ by _**Open AI**_ and _Bing_ chatbot by _**Microsoft**_. He tests them on various topics related to his work and life, and evaluates their accuracy and performance.

_AI chatbots_ are computer programs that can chat with humans using natural language processing and machine learning techniques (3 _**The best AI chatbots of 2023: ChatGPT and alternatives | ZDNET**_). They can be used for various purposes, such as customer service, entertainment, education, and more. _Artificial intelligence_ is the branch of computer science that aims to create machines or systems that can perform tasks that normally require human intelligence, such as reasoning, learning, translations, decision making, etc.

<u>The latest in AI software (chatbots) is also the best to date: <i><b>DeepSeek</b></i> from China (as of February 2025).</u>

-   [_**AI-generated essay on:**_ _Ion Saliu, Divine Proportion, Golden Ratio, Golden Number_](https://saliu.com/DivineProportionAI.pdf), by _**DeepSeek**_.

<u>“A trustworthy man is an axiomatic man; an axiomatic man is a good man. Be axiomatic, Homo Sapiens!”</u> – Ion Saliu Axiomaticus, 3:14, 6:18

![Ion Saliu's image created in the year of grace 2010 in front of his Webcam for a YouTube video.](https://saliu.com/Ion-Saliu.jpg)   ![Ion Saliu's Probability Book has profound philosophical implications.](https://saliu.com/philosophy-probability-book.jpg) Read Ion Saliu's first book in print: [**_Probability Theory, Live!_**](https://saliu.com/probability-book.html)  
~ Discover profound philosophical implications of the **_Formula of TheEverything (FFG)_**, including philosophy, gambling, lottery, software, computer programming.

<big><span color="#ffcc33">*</span></big> The masterpiece featured at the top is known as: **Ion “Parpaluck” Saliu — _Self-portrait with red axiomatic-cap_**. The first draft was created in the 1970s in Romania and was entitled **_Autoportret cu şapcă-axiomatică roşie_**. Inspired by Rembrandt's masterpiece: [_**"Self-Portrait with Red Cap"**_](https://www.akg-images.co.uk/archive/Self-Portrait-with-Red-Cap-2UMDHUDTZU9A.html).

-   AI-generated art inspired by the lyrics of a musical masterpiece: “Stairway to Heaven” by Led Zeppelin.

<iframe src="https://www.youtube.com/embed/6lPjR4Rmf60?si=RLVBS3Tzx2ugZ4og" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" allowfullscreen=""></iframe>

-   [<u><i><b>Chess: The only case where AI is the closest to real INTELLIGENCE (human-like)</b></i></u>](https://www.facebook.com/Parpaluck/posts/pfbid02MgjsXVjiLWzEpNpWpQoTxkncumNcbDD5CBx8BayzKz3cW9dwSUu5kbrafRkqhTZgl).

-   [_**Ion Saliu's Book of Records**_](https://saliu.com/Ion_Saliu.html).
-   [_**Axiomatic, Axiomaticism, Truth**_](https://saliu.com/axiomatic.html).
-   [_**Artificial Intelligence, AI Essays: Ion Saliu's Philosophy of Politics, Systems, Ideologies**_](https://saliu.com/AI-political-systems-Ion-Saliu.html).
-   [_**Fundamental Formula of Gambling (FFG)**_](https://saliu.com/Saliu2.htm): Probability, Mathematics, Degree of Certainty, Chance.  
    
-   [_**Theory of Probability**_](https://saliu.com/theory-of-probability.html): Best introduction, formulae, algorithms, software.
-   [_**Mathematics of the Fundamental Formula of Gambling**_](https://saliu.com/formula.htm).  
    • The mathematical proof of the inexistence of God or the absurdity of the God concept.
-   There are [_**<u>NO perfect geometric shapes, forms</u> on Earth, in Universe**_](https://saliu.com/bbs/messages/636.html) <u>to justify a super intelligent, divine force in Cosmos; only humans create perfect or geometrical shapes in an attempt to have some control over randomness</u>.
-   [_**<u>Zeno's Paradox</u>: Achilles, Tortoise**_](https://saliu.com/aporia.html)  
    ~ The first philosophical, logical, and metaphysical solutions to the paradoxes of Zenon of Elea.
-   [_**Writer: Computer Program Generates Random Letters, Words, Sentences, Phrases, Passwords, Books**_](https://saliu.com/writer.html).

![Ion Saliu goes down in history as the discoverer of the Fundamentally Ultimate Truth: Randomness.](https://saliu.com/HLINE.gif)

**[Home](https://saliu.com/index.htm) | [Search](https://saliu.com/Search.htm) | [New Writings](https://saliu.com/bbs/index.html) | [Fundamental Formula](https://saliu.com/formula.htm) | [Odds, Generator](https://saliu.com/calculator_generator.html) | [Contents](https://saliu.com/content/index.html) | [Forums](https://forums.saliu.com/) | [Sitemap](https://saliu.com/sitemap/index.html)**

![Talk with AI chatbots about Saliu and achievements in mathematics, philosophy, gambling, software.](https://saliu.com/HLINE.gif)
