# Concurrent processing module for Wonder Grid Lottery System

using Base.Threads
using Base.Iterators

"""
Thread-safe data structures and concurrent processing utilities
"""

# Thread-safe global caches with locks
const CONCURRENT_FFG_CACHE = Dict{Int, Vector{Int}}()
const CONCURRENT_PAIRING_CACHE = Dict{Tuple{Int, Int}, Int}()
const FFG_CACHE_LOCK = ReentrantLock()
const PAIRING_CACHE_LOCK = ReentrantLock()

"""
Thread-safe pairing frequency calculator
"""
mutable struct ConcurrentPairingCalculator
    frequency_matrix::Matrix{Int}
    matrix_lock::ReentrantLock
    total_pairs::Atomic{Int}
    cache_hits::Atomic{Int}
    cache_misses::Atomic{Int}
    
    function ConcurrentPairingCalculator()
        new(
            zeros(Int, 39, 39),
            ReentrantLock(),
            Atomic{Int}(0),
            Atomic{Int}(0),
            Atomic{Int}(0)
        )
    end
end

"""
Thread-safe addition of combination to pairing frequency
"""
function add_combination_concurrent!(calc::ConcurrentPairingCalculator, combination::Vector{Int})
    sorted_combo = sort(combination)
    
    lock(calc.matrix_lock) do
        @inbounds for i in 1:4
            num1 = sorted_combo[i]
            for j in (i+1):5
                num2 = sorted_combo[j]
                calc.frequency_matrix[num1, num2] += 1
            end
        end
    end
    
    atomic_add!(calc.total_pairs, 10)  # 5 choose 2 = 10 pairs per combination
end

"""
Thread-safe pairing frequency retrieval with caching
"""
function get_pairing_frequency_concurrent(calc::ConcurrentPairingCalculator, num1::Int, num2::Int)::Int
    # Ensure consistent ordering
    if num1 > num2
        num1, num2 = num2, num1
    end
    
    cache_key = (num1, num2)
    
    # Check cache with lock
    lock(PAIRING_CACHE_LOCK) do
        if haskey(CONCURRENT_PAIRING_CACHE, cache_key)
            atomic_add!(calc.cache_hits, 1)
            return CONCURRENT_PAIRING_CACHE[cache_key]
        end
    end
    
    # Calculate frequency with matrix lock
    frequency = lock(calc.matrix_lock) do
        calc.frequency_matrix[num1, num2]
    end
    
    # Cache result with lock
    lock(PAIRING_CACHE_LOCK) do
        CONCURRENT_PAIRING_CACHE[cache_key] = frequency
    end
    
    atomic_add!(calc.cache_misses, 1)
    return frequency
end

"""
Thread-safe FFG calculation with caching
"""
function calculate_ffg_concurrent(key_number::Int)::Vector{Int}
    # Check cache first
    lock(FFG_CACHE_LOCK) do
        if haskey(CONCURRENT_FFG_CACHE, key_number)
            return CONCURRENT_FFG_CACHE[key_number]
        end
    end
    
    # Calculate FFG
    ffg_numbers = calculate_ffg_core(key_number)
    
    # Cache result
    lock(FFG_CACHE_LOCK) do
        CONCURRENT_FFG_CACHE[key_number] = ffg_numbers
    end
    
    return ffg_numbers
end

"""
Parallel combination generator
"""
struct ParallelCombinationGenerator
    key_number::Int
    ffg_numbers::Vector{Int}
    num_threads::Int
    batch_size::Int
    
    function ParallelCombinationGenerator(key_number::Int, num_threads::Int = nthreads(), batch_size::Int = 1000)
        ffg_numbers = calculate_ffg_concurrent(key_number)
        new(key_number, ffg_numbers, num_threads, batch_size)
    end
end

"""
Generate combinations in parallel using multiple threads
"""
function generate_combinations_parallel(generator::ParallelCombinationGenerator)::Vector{Vector{Int}}
    ffg_count = length(generator.ffg_numbers)
    
    if ffg_count < 5
        return Vector{Vector{Int}}()
    end
    
    # Calculate total number of combinations
    total_combinations = binomial(ffg_count, 5)
    
    if total_combinations == 0
        return Vector{Vector{Int}}()
    end
    
    # Create thread-safe result collection
    results = Vector{Vector{Vector{Int}}}(undef, generator.num_threads)
    for i in 1:generator.num_threads
        results[i] = Vector{Vector{Int}}()
    end
    
    # Generate all combination indices
    all_indices = collect(combinations_indices(ffg_count, 5))
    
    # Distribute work among threads
    work_per_thread = div(length(all_indices), generator.num_threads)
    remainder = length(all_indices) % generator.num_threads
    
    @threads for thread_id in 1:generator.num_threads
        start_idx = (thread_id - 1) * work_per_thread + 1
        end_idx = thread_id * work_per_thread
        
        # Distribute remainder among first threads
        if thread_id <= remainder
            start_idx += thread_id - 1
            end_idx += thread_id
        else
            start_idx += remainder
            end_idx += remainder
        end
        
        # Process assigned combinations
        thread_results = Vector{Vector{Int}}()
        sizehint!(thread_results, end_idx - start_idx + 1)
        
        for idx in start_idx:min(end_idx, length(all_indices))
            combo_indices = all_indices[idx]
            combination = [generator.ffg_numbers[i] for i in combo_indices]
            push!(thread_results, combination)
        end
        
        results[thread_id] = thread_results
    end
    
    # Combine results from all threads
    final_results = Vector{Vector{Int}}()
    sizehint!(final_results, total_combinations)
    
    for thread_results in results
        append!(final_results, thread_results)
    end
    
    return final_results
end

"""
Generate combination indices for parallel processing
"""
function combinations_indices(n::Int, k::Int)
    if k > n || k < 0
        return Vector{Vector{Int}}()
    end
    
    result = Vector{Vector{Int}}()
    indices = collect(1:k)
    
    while true
        push!(result, copy(indices))
        
        # Find rightmost index that can be incremented
        i = k
        while i > 0 && indices[i] == n - k + i
            i -= 1
        end
        
        if i == 0
            break
        end
        
        # Increment and adjust subsequent indices
        indices[i] += 1
        for j in (i+1):k
            indices[j] = indices[j-1] + 1
        end
    end
    
    return result
end

"""
Parallel backtesting engine
"""
struct ParallelBacktestingEngine
    num_threads::Int
    chunk_size::Int
    
    function ParallelBacktestingEngine(num_threads::Int = nthreads(), chunk_size::Int = 100)
        new(num_threads, chunk_size)
    end
end

"""
Run backtest in parallel across multiple threads
"""
function run_backtest_parallel(engine::ParallelBacktestingEngine, 
                              combinations::Vector{Vector{Int}}, 
                              test_draws::Vector{LotteryDraw})::BacktestResult
    
    if isempty(combinations) || isempty(test_draws)
        return BacktestResult(
            Dict("3/5" => 0.0, "4/5" => 0.0, "5/5" => 0.0),
            Dict("3/5" => 0.0, "4/5" => 0.0, "5/5" => 0.0),
            CostAnalysis(0, 0.0, 0.0)
        )
    end
    
    # Convert draws to sets for faster matching
    draw_sets = [Set(draw.numbers) for draw in test_draws]
    
    # Initialize thread-safe counters
    hit_counters = [Atomic{Int}(0) for _ in 1:3]  # 3/5, 4/5, 5/5
    
    # Process combinations in parallel chunks
    combination_chunks = partition(combinations, engine.chunk_size)
    
    @threads for chunk in collect(combination_chunks)
        # Local counters for this thread
        local_hits = [0, 0, 0]  # 3/5, 4/5, 5/5
        
        for combination in chunk
            combo_set = Set(combination)
            
            for draw_set in draw_sets
                matches = length(intersect(combo_set, draw_set))
                
                if matches >= 3
                    local_hits[matches - 2] += 1
                end
            end
        end
        
        # Update global counters atomically
        for i in 1:3
            atomic_add!(hit_counters[i], local_hits[i])
        end
    end
    
    # Calculate final results
    total_tests = length(combinations) * length(test_draws)
    hit_counts = [hit_counters[i][] for i in 1:3]
    
    hit_rates = Dict{String, Float64}(
        "3/5" => total_tests > 0 ? hit_counts[1] / total_tests : 0.0,
        "4/5" => total_tests > 0 ? hit_counts[2] / total_tests : 0.0,
        "5/5" => total_tests > 0 ? hit_counts[3] / total_tests : 0.0
    )
    
    # Calculate efficiency ratios
    efficiency_ratios = Dict{String, Float64}()
    for (tier, rate) in hit_rates
        efficiency_ratios[tier] = rate * 100  # Simple efficiency metric
    end
    
    # Create cost analysis
    cost_analysis = CostAnalysis(
        length(combinations),
        1.0,
        Float64(length(combinations))
    )
    
    return BacktestResult(hit_rates, efficiency_ratios, cost_analysis)
end

"""
Concurrent performance monitor with thread-safe operations
"""
mutable struct ConcurrentPerformanceMonitor
    start_time::Float64
    memory_start::Int
    operation_counts::Dict{String, Atomic{Int}}
    timing_data::Dict{String, Vector{Float64}}
    timing_locks::Dict{String, ReentrantLock}
    global_lock::ReentrantLock
    
    function ConcurrentPerformanceMonitor()
        new(
            time(),
            Base.gc_bytes(),
            Dict{String, Atomic{Int}}(),
            Dict{String, Vector{Float64}}(),
            Dict{String, ReentrantLock}(),
            ReentrantLock()
        )
    end
end

"""
Thread-safe operation timing start
"""
function start_operation_concurrent!(monitor::ConcurrentPerformanceMonitor, operation::String)
    lock(monitor.global_lock) do
        if !haskey(monitor.timing_data, operation)
            monitor.timing_data[operation] = Float64[]
            monitor.timing_locks[operation] = ReentrantLock()
            monitor.operation_counts[operation] = Atomic{Int}(0)
        end
    end
    
    return time()
end

"""
Thread-safe operation timing end
"""
function end_operation_concurrent!(monitor::ConcurrentPerformanceMonitor, operation::String, start_time::Float64)
    elapsed = time() - start_time
    
    # Update timing data with operation-specific lock
    if haskey(monitor.timing_locks, operation)
        lock(monitor.timing_locks[operation]) do
            push!(monitor.timing_data[operation], elapsed)
        end
        
        atomic_add!(monitor.operation_counts[operation], 1)
    end
end

"""
Get thread-safe performance statistics
"""
function get_performance_stats_concurrent(monitor::ConcurrentPerformanceMonitor)::Dict{String, Any}
    total_time = time() - monitor.start_time
    memory_used = Base.gc_bytes() - monitor.memory_start
    
    stats = Dict{String, Any}(
        "total_runtime" => total_time,
        "memory_used_bytes" => memory_used,
        "memory_used_mb" => memory_used / (1024 * 1024),
        "operations" => Dict{String, Any}(),
        "thread_count" => nthreads()
    )
    
    lock(monitor.global_lock) do
        for (operation, times) in monitor.timing_data
            if !isempty(times)
                # Create a copy of times to avoid race conditions
                times_copy = lock(monitor.timing_locks[operation]) do
                    copy(times)
                end
                
                if !isempty(times_copy)
                    stats["operations"][operation] = Dict{String, Any}(
                        "count" => monitor.operation_counts[operation][],
                        "total_time" => sum(times_copy),
                        "average_time" => sum(times_copy) / length(times_copy),
                        "min_time" => minimum(times_copy),
                        "max_time" => maximum(times_copy)
                    )
                end
            end
        end
    end
    
    return stats
end

"""
Concurrent Wonder Grid engine with parallel processing
"""
struct ConcurrentWonderGridEngine
    base_engine::WonderGridEngine
    pairing_calculator::ConcurrentPairingCalculator
    performance_monitor::ConcurrentPerformanceMonitor
    parallel_generator::ParallelCombinationGenerator
    parallel_backtester::ParallelBacktestingEngine
    num_threads::Int
    
    function ConcurrentWonderGridEngine(key_number::Int = 7, num_threads::Int = nthreads())
        base_engine = WonderGridEngine()
        pairing_calculator = ConcurrentPairingCalculator()
        performance_monitor = ConcurrentPerformanceMonitor()
        parallel_generator = ParallelCombinationGenerator(key_number, num_threads)
        parallel_backtester = ParallelBacktestingEngine(num_threads)
        
        new(base_engine, pairing_calculator, performance_monitor, 
            parallel_generator, parallel_backtester, num_threads)
    end
end

"""
Generate combinations using parallel processing
"""
function generate_combinations_concurrent(engine::ConcurrentWonderGridEngine, key_number::Int)::Vector{Vector{Int}}
    start_time = start_operation_concurrent!(engine.performance_monitor, "parallel_combination_generation")
    
    try
        # Create new generator for this key number if different
        if engine.parallel_generator.key_number != key_number
            generator = ParallelCombinationGenerator(key_number, engine.num_threads)
            return generate_combinations_parallel(generator)
        else
            return generate_combinations_parallel(engine.parallel_generator)
        end
    finally
        end_operation_concurrent!(engine.performance_monitor, "parallel_combination_generation", start_time)
    end
end

"""
Run backtest using parallel processing
"""
function run_backtest_concurrent(engine::ConcurrentWonderGridEngine, 
                                combinations::Vector{Vector{Int}}, 
                                test_draws::Vector{LotteryDraw})::BacktestResult
    start_time = start_operation_concurrent!(engine.performance_monitor, "parallel_backtesting")
    
    try
        return run_backtest_parallel(engine.parallel_backtester, combinations, test_draws)
    finally
        end_operation_concurrent!(engine.performance_monitor, "parallel_backtesting", start_time)
    end
end

"""
Thread pool manager for controlling concurrent operations
"""
mutable struct ThreadPoolManager
    max_threads::Int
    active_tasks::Atomic{Int}
    task_queue::Channel{Function}
    worker_tasks::Vector{Task}
    shutdown_flag::Atomic{Bool}
    
    function ThreadPoolManager(max_threads::Int = nthreads())
        manager = new(
            max_threads,
            Atomic{Int}(0),
            Channel{Function}(1000),  # Buffer for 1000 tasks
            Task[],
            Atomic{Bool}(false)
        )
        
        # Start worker threads
        start_workers!(manager)
        
        return manager
    end
end

"""
Start worker threads for the thread pool
"""
function start_workers!(manager::ThreadPoolManager)
    for i in 1:manager.max_threads
        worker_task = @async begin
            while !manager.shutdown_flag[]
                try
                    # Wait for task from queue
                    task_func = take!(manager.task_queue)
                    
                    # Execute task
                    atomic_add!(manager.active_tasks, 1)
                    try
                        task_func()
                    catch e
                        @warn "Task execution failed: $e"
                    finally
                        atomic_add!(manager.active_tasks, -1)
                    end
                catch e
                    if !isa(e, InvalidStateException)  # Channel closed
                        @warn "Worker thread error: $e"
                    end
                    break
                end
            end
        end
        
        push!(manager.worker_tasks, worker_task)
    end
end

"""
Submit task to thread pool
"""
function submit_task!(manager::ThreadPoolManager, task_func::Function)
    if !manager.shutdown_flag[]
        put!(manager.task_queue, task_func)
        return true
    end
    return false
end

"""
Wait for all tasks to complete
"""
function wait_for_completion(manager::ThreadPoolManager, timeout::Float64 = 60.0)
    start_time = time()
    
    while manager.active_tasks[] > 0 && (time() - start_time) < timeout
        sleep(0.1)
    end
    
    return manager.active_tasks[] == 0
end

"""
Shutdown thread pool
"""
function shutdown!(manager::ThreadPoolManager)
    manager.shutdown_flag[] = true
    close(manager.task_queue)
    
    # Wait for workers to finish
    for task in manager.worker_tasks
        try
            wait(task)
        catch e
            # Ignore task cancellation errors
        end
    end
    
    empty!(manager.worker_tasks)
end

"""
Scaling performance analyzer
"""
struct ScalingAnalyzer
    thread_counts::Vector{Int}
    performance_data::Dict{Int, Dict{String, Float64}}
    
    function ScalingAnalyzer(max_threads::Int = nthreads())
        thread_counts = [1, 2, 4, min(8, max_threads), max_threads]
        thread_counts = unique(sort(thread_counts))
        
        new(thread_counts, Dict{Int, Dict{String, Float64}}())
    end
end

"""
Analyze scaling performance across different thread counts
"""
function analyze_scaling_performance(analyzer::ScalingAnalyzer, key_number::Int, iterations::Int = 5)
    println("🔬 Analyzing scaling performance...")
    
    for thread_count in analyzer.thread_counts
        println("  Testing with $thread_count threads...")
        
        # Simulate different thread count (conceptual - actual threading is system-controlled)
        times = Float64[]
        
        for i in 1:iterations
            # Create engine with specified thread preference
            engine = ConcurrentWonderGridEngine(key_number, thread_count)
            
            start_time = time()
            combinations = generate_combinations_concurrent(engine, key_number)
            elapsed = time() - start_time
            
            push!(times, elapsed)
        end
        
        avg_time = sum(times) / length(times)
        min_time = minimum(times)
        max_time = maximum(times)
        
        analyzer.performance_data[thread_count] = Dict{String, Float64}(
            "average_time" => avg_time,
            "min_time" => min_time,
            "max_time" => max_time,
            "speedup" => thread_count > 1 ? analyzer.performance_data[1]["average_time"] / avg_time : 1.0,
            "efficiency" => thread_count > 1 ? (analyzer.performance_data[1]["average_time"] / avg_time) / thread_count : 1.0
        )
    end
    
    return analyzer.performance_data
end

"""
Display scaling analysis results
"""
function display_scaling_results(analyzer::ScalingAnalyzer)
    println("\n📊 Scaling Performance Analysis")
    println("=" ^ 60)
    
    println("Threads | Avg Time (ms) | Speedup | Efficiency | Min/Max (ms)")
    println("-" ^ 60)
    
    for thread_count in analyzer.thread_counts
        if haskey(analyzer.performance_data, thread_count)
            data = analyzer.performance_data[thread_count]
            
            avg_ms = round(data["average_time"] * 1000, digits=2)
            speedup = round(data["speedup"], digits=2)
            efficiency = round(data["efficiency"] * 100, digits=1)
            min_ms = round(data["min_time"] * 1000, digits=2)
            max_ms = round(data["max_time"] * 1000, digits=2)
            
            println("$(lpad(thread_count, 7)) | $(lpad(avg_ms, 13)) | $(lpad(speedup, 7))x | $(lpad(efficiency, 10))% | $(min_ms)/$(max_ms)")
        end
    end
    
    println("\n💡 Scaling Insights:")
    
    if length(analyzer.performance_data) >= 2
        best_speedup = maximum(data["speedup"] for data in values(analyzer.performance_data))
        best_efficiency = maximum(data["efficiency"] for data in values(analyzer.performance_data))
        
        println("  • Best speedup: $(round(best_speedup, digits=2))x")
        println("  • Best efficiency: $(round(best_efficiency * 100, digits=1))%")
        
        # Find optimal thread count
        optimal_threads = 1
        best_performance = 0.0
        
        for (threads, data) in analyzer.performance_data
            performance_score = data["speedup"] * data["efficiency"]
            if performance_score > best_performance
                best_performance = performance_score
                optimal_threads = threads
            end
        end
        
        println("  • Optimal thread count: $optimal_threads")
    end
end

"""
Clear all concurrent caches
"""
function clear_concurrent_caches!()
    lock(FFG_CACHE_LOCK) do
        empty!(CONCURRENT_FFG_CACHE)
    end
    
    lock(PAIRING_CACHE_LOCK) do
        empty!(CONCURRENT_PAIRING_CACHE)
    end
    
    GC.gc()
end

"""
Get concurrent cache statistics
"""
function get_concurrent_cache_stats()::Dict{String, Any}
    ffg_size = lock(FFG_CACHE_LOCK) do
        length(CONCURRENT_FFG_CACHE)
    end
    
    pairing_size = lock(PAIRING_CACHE_LOCK) do
        length(CONCURRENT_PAIRING_CACHE)
    end
    
    return Dict{String, Any}(
        "ffg_cache_size" => ffg_size,
        "pairing_cache_size" => pairing_size,
        "total_cached_items" => ffg_size + pairing_size,
        "thread_count" => nthreads()
    )
end