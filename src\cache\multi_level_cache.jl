# Multi-Level Cache System
# 多層快取系統 - 提供高效的多層快取管理

using Dates

"""
快取項目結構
"""
mutable struct CacheItem{T}
    value::T
    timestamp::DateTime
    access_count::Int
    last_access::DateTime
    size_bytes::Int
    
    function CacheItem{T}(value::T) where T
        now_time = now()
        size_bytes = estimate_size(value)
        new{T}(value, now_time, 1, now_time, size_bytes)
    end
end

"""
估算對象大小（簡化實現）
"""
function estimate_size(value)::Int
    if isa(value, Number)
        return 8
    elseif isa(value, String)
        return length(value) * 2  # Unicode 字符
    elseif isa(value, Vector)
        return length(value) * 8 + 24  # 估算
    elseif isa(value, Dict)
        return length(value) * 16 + 32  # 估算
    else
        return 64  # 默認估算
    end
end

"""
快取統計結構
"""
mutable struct CacheStatistics
    hits::Int
    misses::Int
    evictions::Int
    total_size_bytes::Int
    max_size_bytes::Int
    
    function CacheStatistics(max_size::Int = 1024 * 1024 * 100)  # 默認 100MB
        new(0, 0, 0, 0, max_size)
    end
end

"""
快取層級枚舉
"""
@enum CacheLevel begin
    L1_CACHE = 1  # 最快，最小
    L2_CACHE = 2  # 中等速度，中等大小
    L3_CACHE = 3  # 較慢，較大
end

"""
多層快取系統
"""
mutable struct MultiLevelCache
    l1_cache::Dict{String, CacheItem}
    l2_cache::Dict{String, CacheItem}
    l3_cache::Dict{String, CacheItem}
    
    l1_stats::CacheStatistics
    l2_stats::CacheStatistics
    l3_stats::CacheStatistics
    
    l1_max_items::Int
    l2_max_items::Int
    l3_max_items::Int
    
    function MultiLevelCache(;
        l1_max_items::Int = 100,
        l2_max_items::Int = 1000,
        l3_max_items::Int = 10000,
        l1_max_size::Int = 1024 * 1024,      # 1MB
        l2_max_size::Int = 10 * 1024 * 1024, # 10MB
        l3_max_size::Int = 100 * 1024 * 1024 # 100MB
    )
        new(
            Dict{String, CacheItem}(),
            Dict{String, CacheItem}(),
            Dict{String, CacheItem}(),
            CacheStatistics(l1_max_size),
            CacheStatistics(l2_max_size),
            CacheStatistics(l3_max_size),
            l1_max_items,
            l2_max_items,
            l3_max_items
        )
    end
end

"""
生成快取鍵
"""
function generate_cache_key(prefix::String, args...)::String
    key_parts = [prefix]
    for arg in args
        push!(key_parts, string(arg))
    end
    return join(key_parts, ":")
end

"""
從快取中獲取值
"""
function get_from_cache(cache::MultiLevelCache, key::String)
    # 首先檢查 L1 快取
    if haskey(cache.l1_cache, key)
        item = cache.l1_cache[key]
        item.access_count += 1
        item.last_access = now()
        cache.l1_stats.hits += 1
        return item.value
    end
    
    # 檢查 L2 快取
    if haskey(cache.l2_cache, key)
        item = cache.l2_cache[key]
        item.access_count += 1
        item.last_access = now()
        cache.l2_stats.hits += 1
        
        # 提升到 L1 快取
        promote_to_l1!(cache, key, item.value)
        return item.value
    end
    
    # 檢查 L3 快取
    if haskey(cache.l3_cache, key)
        item = cache.l3_cache[key]
        item.access_count += 1
        item.last_access = now()
        cache.l3_stats.hits += 1
        
        # 提升到 L2 快取
        promote_to_l2!(cache, key, item.value)
        return item.value
    end
    
    # 快取未命中
    cache.l1_stats.misses += 1
    return nothing
end

"""
將值存入快取
"""
function put_in_cache!(cache::MultiLevelCache, key::String, value, level::CacheLevel = L1_CACHE)
    if level == L1_CACHE
        put_in_l1!(cache, key, value)
    elseif level == L2_CACHE
        put_in_l2!(cache, key, value)
    else
        put_in_l3!(cache, key, value)
    end
end

"""
將值存入 L1 快取
"""
function put_in_l1!(cache::MultiLevelCache, key::String, value)
    # 檢查是否需要清理空間
    if length(cache.l1_cache) >= cache.l1_max_items
        evict_from_l1!(cache)
    end
    
    item = CacheItem{typeof(value)}(value)
    cache.l1_cache[key] = item
    cache.l1_stats.total_size_bytes += item.size_bytes
    
    # 檢查大小限制
    while cache.l1_stats.total_size_bytes > cache.l1_stats.max_size_bytes
        evict_from_l1!(cache)
    end
end

"""
將值存入 L2 快取
"""
function put_in_l2!(cache::MultiLevelCache, key::String, value)
    # 檢查是否需要清理空間
    if length(cache.l2_cache) >= cache.l2_max_items
        evict_from_l2!(cache)
    end
    
    item = CacheItem{typeof(value)}(value)
    cache.l2_cache[key] = item
    cache.l2_stats.total_size_bytes += item.size_bytes
    
    # 檢查大小限制
    while cache.l2_stats.total_size_bytes > cache.l2_stats.max_size_bytes
        evict_from_l2!(cache)
    end
end

"""
將值存入 L3 快取
"""
function put_in_l3!(cache::MultiLevelCache, key::String, value)
    # 檢查是否需要清理空間
    if length(cache.l3_cache) >= cache.l3_max_items
        evict_from_l3!(cache)
    end
    
    item = CacheItem{typeof(value)}(value)
    cache.l3_cache[key] = item
    cache.l3_stats.total_size_bytes += item.size_bytes
    
    # 檢查大小限制
    while cache.l3_stats.total_size_bytes > cache.l3_stats.max_size_bytes
        evict_from_l3!(cache)
    end
end

"""
提升到 L1 快取
"""
function promote_to_l1!(cache::MultiLevelCache, key::String, value)
    # 從 L2 或 L3 移除
    if haskey(cache.l2_cache, key)
        item = cache.l2_cache[key]
        delete!(cache.l2_cache, key)
        cache.l2_stats.total_size_bytes -= item.size_bytes
    elseif haskey(cache.l3_cache, key)
        item = cache.l3_cache[key]
        delete!(cache.l3_cache, key)
        cache.l3_stats.total_size_bytes -= item.size_bytes
    end
    
    # 添加到 L1
    put_in_l1!(cache, key, value)
end

"""
提升到 L2 快取
"""
function promote_to_l2!(cache::MultiLevelCache, key::String, value)
    # 從 L3 移除
    if haskey(cache.l3_cache, key)
        item = cache.l3_cache[key]
        delete!(cache.l3_cache, key)
        cache.l3_stats.total_size_bytes -= item.size_bytes
    end
    
    # 添加到 L2
    put_in_l2!(cache, key, value)
end

"""
從 L1 快取中驅逐項目（LRU 策略）
"""
function evict_from_l1!(cache::MultiLevelCache)
    if isempty(cache.l1_cache)
        return
    end
    
    # 找到最久未使用的項目
    oldest_key = ""
    oldest_time = now()
    
    for (key, item) in cache.l1_cache
        if item.last_access < oldest_time
            oldest_time = item.last_access
            oldest_key = key
        end
    end
    
    if !isempty(oldest_key)
        item = cache.l1_cache[oldest_key]
        delete!(cache.l1_cache, oldest_key)
        cache.l1_stats.total_size_bytes -= item.size_bytes
        cache.l1_stats.evictions += 1
        
        # 降級到 L2
        put_in_l2!(cache, oldest_key, item.value)
    end
end

"""
從 L2 快取中驅逐項目
"""
function evict_from_l2!(cache::MultiLevelCache)
    if isempty(cache.l2_cache)
        return
    end
    
    # 找到最久未使用的項目
    oldest_key = ""
    oldest_time = now()
    
    for (key, item) in cache.l2_cache
        if item.last_access < oldest_time
            oldest_time = item.last_access
            oldest_key = key
        end
    end
    
    if !isempty(oldest_key)
        item = cache.l2_cache[oldest_key]
        delete!(cache.l2_cache, oldest_key)
        cache.l2_stats.total_size_bytes -= item.size_bytes
        cache.l2_stats.evictions += 1
        
        # 降級到 L3
        put_in_l3!(cache, oldest_key, item.value)
    end
end

"""
從 L3 快取中驅逐項目
"""
function evict_from_l3!(cache::MultiLevelCache)
    if isempty(cache.l3_cache)
        return
    end
    
    # 找到最久未使用的項目
    oldest_key = ""
    oldest_time = now()
    
    for (key, item) in cache.l3_cache
        if item.last_access < oldest_time
            oldest_time = item.last_access
            oldest_key = key
        end
    end
    
    if !isempty(oldest_key)
        item = cache.l3_cache[oldest_key]
        delete!(cache.l3_cache, oldest_key)
        cache.l3_stats.total_size_bytes -= item.size_bytes
        cache.l3_stats.evictions += 1
        # L3 是最後一層，直接丟棄
    end
end

"""
清空所有快取
"""
function clear_cache!(cache::MultiLevelCache)
    empty!(cache.l1_cache)
    empty!(cache.l2_cache)
    empty!(cache.l3_cache)
    
    cache.l1_stats.total_size_bytes = 0
    cache.l2_stats.total_size_bytes = 0
    cache.l3_stats.total_size_bytes = 0
end

"""
獲取快取統計信息
"""
function get_cache_statistics(cache::MultiLevelCache)::Dict{String, Any}
    total_hits = cache.l1_stats.hits + cache.l2_stats.hits + cache.l3_stats.hits
    total_misses = cache.l1_stats.misses + cache.l2_stats.misses + cache.l3_stats.misses
    total_requests = total_hits + total_misses
    
    hit_rate = total_requests > 0 ? total_hits / total_requests : 0.0
    
    return Dict(
        "total_requests" => total_requests,
        "total_hits" => total_hits,
        "total_misses" => total_misses,
        "hit_rate" => hit_rate,
        "l1_items" => length(cache.l1_cache),
        "l2_items" => length(cache.l2_cache),
        "l3_items" => length(cache.l3_cache),
        "l1_size_mb" => cache.l1_stats.total_size_bytes / (1024 * 1024),
        "l2_size_mb" => cache.l2_stats.total_size_bytes / (1024 * 1024),
        "l3_size_mb" => cache.l3_stats.total_size_bytes / (1024 * 1024),
        "l1_evictions" => cache.l1_stats.evictions,
        "l2_evictions" => cache.l2_stats.evictions,
        "l3_evictions" => cache.l3_stats.evictions
    )
end

"""
Skip 計算專用快取
"""
mutable struct SkipCalculationCache
    cache::MultiLevelCache

    function SkipCalculationCache()
        # 針對 Skip 計算優化的快取配置
        cache = MultiLevelCache(
            l1_max_items = 200,      # Skip 查詢頻繁
            l2_max_items = 2000,
            l3_max_items = 20000,
            l1_max_size = 2 * 1024 * 1024,    # 2MB
            l2_max_size = 20 * 1024 * 1024,   # 20MB
            l3_max_size = 200 * 1024 * 1024   # 200MB
        )
        new(cache)
    end
end

"""
FFG 計算專用快取
"""
mutable struct FFGCalculationCache
    cache::MultiLevelCache

    function FFGCalculationCache()
        # 針對 FFG 計算優化的快取配置
        cache = MultiLevelCache(
            l1_max_items = 50,       # FFG 計算較重
            l2_max_items = 500,
            l3_max_items = 5000,
            l1_max_size = 5 * 1024 * 1024,    # 5MB
            l2_max_size = 50 * 1024 * 1024,   # 50MB
            l3_max_size = 500 * 1024 * 1024   # 500MB
        )
        new(cache)
    end
end

"""
配對分析專用快取
"""
mutable struct PairingCache
    cache::MultiLevelCache

    function PairingCache()
        # 針對配對分析優化的快取配置
        cache = MultiLevelCache(
            l1_max_items = 300,      # 配對查詢多樣
            l2_max_items = 3000,
            l3_max_items = 30000,
            l1_max_size = 3 * 1024 * 1024,    # 3MB
            l2_max_size = 30 * 1024 * 1024,   # 30MB
            l3_max_size = 300 * 1024 * 1024   # 300MB
        )
        new(cache)
    end
end

"""
獲取 Skip 值（帶快取）
"""
function get_skip_cached(skip_cache::SkipCalculationCache, number::Int, data_hash::String)
    key = generate_cache_key("skip", number, data_hash)
    cached_value = get_from_cache(skip_cache.cache, key)
    return cached_value
end

"""
存儲 Skip 值到快取
"""
function put_skip_cached!(skip_cache::SkipCalculationCache, number::Int, data_hash::String, skip_value::Int)
    key = generate_cache_key("skip", number, data_hash)
    put_in_cache!(skip_cache.cache, key, skip_value, L1_CACHE)
end

"""
獲取 FFG 值（帶快取）
"""
function get_ffg_cached(ffg_cache::FFGCalculationCache, number::Int, data_hash::String)
    key = generate_cache_key("ffg", number, data_hash)
    cached_value = get_from_cache(ffg_cache.cache, key)
    return cached_value
end

"""
存儲 FFG 值到快取
"""
function put_ffg_cached!(ffg_cache::FFGCalculationCache, number::Int, data_hash::String, ffg_value::Float64)
    key = generate_cache_key("ffg", number, data_hash)
    put_in_cache!(ffg_cache.cache, key, ffg_value, L2_CACHE)  # FFG 計算較重，直接存 L2
end

"""
獲取配對頻率（帶快取）
"""
function get_pairing_cached(pairing_cache::PairingCache, num1::Int, num2::Int, data_hash::String)
    key = generate_cache_key("pairing", min(num1, num2), max(num1, num2), data_hash)
    cached_value = get_from_cache(pairing_cache.cache, key)
    return cached_value
end

"""
存儲配對頻率到快取
"""
function put_pairing_cached!(pairing_cache::PairingCache, num1::Int, num2::Int, data_hash::String, frequency::Int)
    key = generate_cache_key("pairing", min(num1, num2), max(num1, num2), data_hash)
    put_in_cache!(pairing_cache.cache, key, frequency, L1_CACHE)
end

"""
快取一致性管理器
"""
mutable struct CacheConsistencyManager
    data_version::Int
    last_update::DateTime

    function CacheConsistencyManager()
        new(1, now())
    end
end

"""
更新數據版本（使快取失效）
"""
function update_data_version!(manager::CacheConsistencyManager)
    manager.data_version += 1
    manager.last_update = now()
end

"""
獲取當前數據版本
"""
function get_data_version(manager::CacheConsistencyManager)::Int
    return manager.data_version
end

"""
生成數據哈希（用於快取鍵）
"""
function generate_data_hash(data_version::Int, data_size::Int)::String
    return "v$(data_version)_s$(data_size)"
end

# 導出主要函數和結構
export MultiLevelCache, CacheLevel, L1_CACHE, L2_CACHE, L3_CACHE
export generate_cache_key, get_from_cache, put_in_cache!
export clear_cache!, get_cache_statistics
export SkipCalculationCache, FFGCalculationCache, PairingCache
export get_skip_cached, put_skip_cached!, get_ffg_cached, put_ffg_cached!
export get_pairing_cached, put_pairing_cached!
export CacheConsistencyManager, update_data_version!, get_data_version, generate_data_hash
