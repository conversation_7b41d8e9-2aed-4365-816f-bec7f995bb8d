---
created: 2025-07-24T21:26:41 (UTC +08:00)
tags: [best,strategy,gambling,lottery,mathematics,theory,software,sports betting,horse racing,blackjack,roulette,randomness,winning,real money,]
source: https://saliu.com/strategy-gambling-lottery.html
author: 
---

# The Best Strategy in Lottery, Gambling

> ## Excerpt
> The best strategy for gambling and lottery is based on mathematics.  <PERSON>'s theory, software in lottery, gambling may be the only honest and no-nonsense example leading to real winnings.

---
![<PERSON>'s theory, software in lottery, gambling is the only honest, no-nonsense strategy.](https://saliu.com/HLINE.gif)

### I. [The _Best Strategy_ for Lottery, Gambling Is Founded on Mathematics](https://saliu.com/strategy-gambling-lottery.html#mathematics)  
II. [The Best Strategy in Casino Gambling, Blackjack, Roulette](https://saliu.com/strategy-gambling-lottery.html#gambling)  
III. [The Best Strategy in Sports Betting, Football, Soccer Pools](https://saliu.com/strategy-gambling-lottery.html#sports)  
IV. [The Best Strategy in Horse Racing, Longshot Trifectas](https://saliu.com/strategy-gambling-lottery.html#horses)  
V. [The Best Lottery Strategy, Unusual Filter Settings](https://saliu.com/strategy-gambling-lottery.html#lottery)  
VI. [Conclusion](https://saliu.com/strategy-gambling-lottery.html#conclusion)

![The degree of certainty DC can be viewed as probability of a probability.](https://saliu.com/HLINE.gif)

## <u>1. The Mathematical Foundation of Gambling, Lottery Strategies</u>

-   Axiomatic ones, ever since I opened this Web site, I've been asked, indeed attacked at times, if _“is there possibly **any strategy** to win those cases of so-called **absolutely-random** events?”_
-   Actually, _everything is random_ to begin with. The differences are created by different _probabilities_, different _degrees of certainty_ that lead to various [_**degrees of randomness**_](https://saliu.com/bbs/messages/683.html).
-   I cool-headedly explain in this article that the _best strategy_ for gambling and lottery is a reality and it is based on _mathematics_. My theory and software in lottery, gambling, sports betting, horse racing, blackjack, roulette, etc. may be the only _honest_ and _no-nonsense_ example. There might be a few other cases, but I am not fully aware of.
-   If one does an extensive search on the topic of this article, one gets lists of thousands upon thousands of authors, developers, vendors of systems and software for gambling and lottery. My assessment is that most of the attacks I been served by Cyber World have been authored by developers, vendors I just referred to. To be sure, I have received also _honest_ negative opinions and questions from honest people who do not develop systems and/or software in my field.

Since I've recently (2015) received further questions and some expressions of doubt, I decided to write this article. I addressed these issues before, in one form or another. I did it at this very website, in my forums between 1999 and 2014. I also presented the foundation of my theories and software in lottery and gambling in various forums and newsgroups between 1998 and 2014. I also made presentations in the social media (Facebook, Twitter) from 2009 and ongoing (as of 2015). This article summarizes in one place my theories on strategies in gambling and lottery.

I do not offer detailed how-to, however. The overwhelming majority of requests target very detailed information on how to use my lottery software to develop strategies for just about any lottery game in the world! Who can possibly carry out such a task?! It is unspeakably crazy to even ask me for such "favors"! I will only present in this piece of writing the **mathematical foundation**. Mathematics never lies, never deceives, never cheats.

On the other hand, what I see on the Internet is the lack of mathematical foundation of various lottery or gambling systems, software. I am hated by some in the gambling community just for raising the issue of mathematics. Blackjack card counting community, the most notorious example, hates the guts in me for exposing the [_**lack of mathematical foundation of counting cards at blackjack**_](https://saliu.com/bbs/messages/274.html). Curse me as much as you want, but mathematics does not validate the _strategy of blackjack card-counting_.

One can only see long sales pitches and no concrete information on all sites dedicated to the topics of this material. (Sure, I have visited but a low percentage — but sufficient to say that proof to the contrary to what I say is very hard to find.) Several fragments separated by _"Buy Now"_ buttons — not one single sentence about what the system is founded on!

And here is the **best strategy** in gambling, lottery. The **mathematical foundation** is known all over the world now as _**Fundamental Formula of Gambling (FFG)**_. I introduced it in 1997 on one of the very first pages of _SALIU.COM_: [**<u><i>Fundamental Formula of Gambling</i>: Theory of Probability, Mathematics, Degree of Certainty, Chance</u>**](https://saliu.com/Saliu2.htm). I made a more detailed mathematical presentation on a later page: [**<u>Mathematics of <i>Fundamental Formula of Gambling</i>, Logarithms, God</u>**](https://saliu.com/formula.htm).

![Run the best software to calculate probability, degree of certainty, trials, chance, win, loss.](https://saliu.com/ScreenImgs/FFG1.jpg)

### -   _The degree of certainty DC rises exponentially with the increase in the number of trials N while the probability p is always the same or constant._
-   _DC = 1 – (1 – p) ^ N_

![Casino gambling is the best application of fundamental formula, winning streaks, losing streaks.](https://saliu.com/HLINE.gif)

## <u>2. The Best Casino Gambling Strategies: Blackjack Roulette, etc.</u>

The best strategy in casino gambling is the most innate to _**FFG**_. Despite what the fallacious [<u><i><b>gambler's fallacy</b></i></u>](https://saliu.com/gamblers-fallacy.html) proclaims, reality proves that the winning chance grows exponentially with the increase in the number of trials. Mathematics is not to be denied.

There are _losing streaks_ and there are _winning streaks_ — that's undeniable mathematics. The casinos and many gambling authors, however, grossly disregard probability theory. They established a "law" that "assures" _**very long losing streaks for the player**_ — but never for the gambling establishment. If there is honesty, things should closely resemble the calculations of my probability software **Streaks**. There should be also a <u>reversed gambler's fallacy</u>, _Lady Chance_ says — the player too will win _indefinitely_! What? Can _to be_ AND _not to be_ "live" simultaneously? The truth is always <u>in between</u>, for there is no extreme (_absolute certainty_ or _absolute nothingness_).

There is a popular freebie at this website: _**online random generator**_ for a wide variety of games. Most pages here have this entry in the footer: _**Odds, Generator**_. Take the famous game of roulette, where long streaks occur and they are the stuff the legends are made of. Run the roulette generator as many times as you want.

We talk here sane lifespans. You'll never witness indefinite streaks of any kind. In the overwhelming majority of cases, the streaks fall within one standard deviation from the norm (the probability calculated by mathematics). Contrast that to the opinion of a gambling authority and casino consultant who wrote to me in strong terms that getting _200 Heads/Tails_ in a row was equal to getting just 1 _Heads/Tails_! Another gambling author (hidden behind a nickname) posted against me to the extent that losing 1000 consecutive blackjack hands was matter-of-factish! It would take the lifespan of the Universe for such events to occur…

![The gambler's fallacy is disproved by gambling software as events fall within one standard deviation from norm.](https://saliu.com/ScreenImgs/gambler-fallacy.gif)

And thus, the best casino gambling strategy is based on **streaks**. There is a _gambling science_ and I call it the _mathematics of streaks_. I have applied it since the 1990s to the chagrin of casino executives, casino consultants, sore gambling authors. To my shock, even a [<u><i><b>chairman showed hostile reaction</b></i></u>](https://saliu.com/bbs/messages/588.html) in my oldest forum! _"Why do you all bother?"_ has always been my question. Don't you have that "shield" called the gambler's fallacy?! I've never bothered you or anyone else for that fallacy... or anything else, for that matter.

The worst situation for me occurred in the more tolerant Atlantic City: [<u><i><b>I was banned by a casino pit boss</b></i></u>](https://saliu.com/winning.html). I mean, I was physically prevented from gambling at a blackjack table. I wasn't even a card counter — it was obvious to everybody at the table, the dealer included. Instead of watching closely the cards facing up on the table, I was checking first the streaks in a plain notebook (paper). I also experienced hostile reaction at two roulette tables during the same gambling and researching action described in the link above.

-   I detail the **blackjack** strategy based on streaks on this Web page: [_**The Best Casino Gambling Systems: Blackjack, Roulette, Limited Martingale Betting, Progressions**_](https://saliu.com/occult-science-gambling.html#GamblingSystem).  
    -   This report analyzes software-generated streaks: [<u><i><b>Blackjack Streaks, Software, Probability of Consecutive Losses / Wins, Stats</b></i></u>](https://saliu.com/gambling-lottery-lotto/blackjack-report.htm).
-   The streak-based gambling system is also applicable to [<u><i><b>baccarat</b></i></u>](https://saliu.com/winning_bell.html).
-   I detail the **roulette** strategy based on streaks on this Web page: [_**The Best-Ever Roulette Strategy, Systems**_](https://saliu.com/best-roulette-systems.html) based on mathematics of progressions and free-for-all.
-   I detail the **craps** strategy based on streaks on this Web page: [<u><i><b>Craps Strategy, Systems Based on Fundamental Formula of Gambling</b></i></u>](https://saliu.com/bbs/messages/504.html).
-   See a real-life case of my _<u>Mental Blackjack System</u>_ based on streaks beat the house (casino) very consistently: [_**The Best Blackjack Strategy, System Tested with the Best Blackjack Software**_](https://saliu.com/blackjack-strategy-system-win.html). The screenshots cannot be faked and you can replicate the results with a free Windows 10 app from the _Store_.
-   I made a _disclaimer_ on my webpages and in public forums regarding my gambling strategies and systems based on streaks.
-   I do trust my _**streaks-based**_ gambling system to the maximum, however. Only the streaks are backed by mathematical formulas. Every _streak-length_ occurs as calculated by theory of probability: with a _**degree of certainty**_ within a _**number of trials**_ (e.g. BJ hands or roulette spins).
-   I made such a disclaimer only to extinguish potential fire by casinos and/or gambling authors (especially [_**blackjack card-counting**_ "bishops" or "gurus"](https://saliu.com/bbs/messages/511.html)). They have always attacked and threatened me, especially because of the [_**Fundamental Formula of Gambling**_](https://saliu.com/gambling-fights.html).
-   Without a disclaimer, they would have invented situations of huge losses caused by my _**streak-theory**_ gambling systems, strategies! In truth, a few of them have tried to threaten me that way. I would receive emails or read posts in forums, especially mine, that him gambler, or even her gambler, encountered indefinitely long losing streaks! As if I were a mathematical idiot and faint at heart! Not to mention there is NO sane way to verify casino gambling data... just their virulent "figures"...

The probability software that calculates the streaks and their length — based on probability formulas — is the most hated piece of software in the gambling business: **Streaks**.

![The best probability software calculates winning and losing streaks based on mathematical formulae.](https://saliu.com/ScreenImgs/streaks-gambling.gif)

![The best strategy in sports betting applies records, scores, results and streaks.](https://saliu.com/HLINE.gif)

## <u>3. The Best Strategy in Sports Betting, Football, Soccer Pools</u>

Sport betting is where I started in this field of gambling mathematics. It happened in my teenage years. Communist régimes only allowed football (soccer) prognostication and lottery games. There is absolutely no doubt that I was the first to apply the _results of previous games to predicting future games_. I made the _sport betting strategy_ public at my website in 2000 while in USA. Nobody else has done anything similar **before** that date. All strategies or software applicable to sports as in my predicting methods appeared a few years **after** I published my theory.

1) The fundamentals of my sports betting strategy are the most effective statistics-wise. It is based on the cold fact that the teams play differently at _home_ compared to _away_. Therefore, the best sports-prediction strategy should pit the _home_ record of the _home_ team against the away record of the _visiting_ team. Just for starters.

-   I present in great detail how the best sports betting strategy works, the soccer flavor: [<u><i><b>Software to Generate Full 1X2 Systems for Football Soccer Pools</b></i></u>](https://forums.saliu.com/software-1x2-football-soccer-pools.html).
-   I present in great detail how the best sports betting strategy works, the American football flavor: [<u><i><b>Sports Prognosticating or Betting for NFL American Football, Excel Sport Bet Spreadsheet</b></i></u>](https://saliu.com/bbs/messages/382.html).

2) The theory of streaks still applies. Since there is no absolute certainty, there will always be losing streaks and winning streaks. Increasing the bet is required after losing streaks, when the degree of certainty is higher. Also, the teams have their own streaks, including how in many games they cause upsets or surprises.

![The best strategy in horse racing betting plays longshot trifectas for several races.](https://saliu.com/HLINE.gif)

## <u>4. The Best Strategy in Horse Racing, Longshot Trifectas</u>

1) My first horse racing strategy started with the streaks theory. I tracked the [_**races at three horse tracks**_](https://saliu.com/horses.html). I recorded 6 winning trifectas. I played by excluding the horse numbers that had come out in the same position. I did hit the winning trifecta immediately, but that is not expected to happen very often. The payout, however, covers the losing situations. Again, after certain-length losing streaks, an increase in the bet is applied.

2) The best horse racing strategy, however, is a mixture of lotto and pick (daily) lotteries as played in the United States. The first software I developed was for lotto (jackpot) games, and then for pick lotteries. That made my move to horse racing software so much easier. The software is mainly for trifectas (top 3 finishers) and, in a more limited scope, for superfectas (top 4 finishers).

There are _off-track wagering (OTW)_ facilities all across USA. There are also plenty of horse-racing websites. The results at a large number of horse tracks are available for free in most cases. The best strategy for horse racing records the results of races that have at least 9 horses (for acceptable trifecta payouts). I discard of trifecta results with numbers larger than 10. The user is made aware of that detail in my software.

-   The best feature in horse betting is **high trifecta payouts** when **longshots win**. There are fewer longshot trifectas than _regular trifectas_ (when the _favorites are in top 3 finishers, but no longshots_).

It is a laborious process, but worth it. I record results from previous days from several horse tracks with good payout record. I shuffle the results, as everything is random. That's the data file I use in conjunction with a much larger file of randomly generated trifectas. I run my software with filters (restrictions that reduce the amount of outcomes). I get an output file consisting of trifectas that have not come out in the races I had recorded in the database. Longshot trifectas do come out and they do pay off!

A bankroll of $5000 is sufficient. This best strategy is played at a _OTW_. The data recording is laborious and patience is also required. The strategy requires a few days of play, if not winning. When a trifecta hits, it is crossed out on the printout.

3) I have never found _handicapping_ to be an acceptable horse racing strategy. The favorites do NOT win as in coin tossing. The best betting horse race, the Kentucky Derby, shows that the favorite has won around 1 in 5 of all races. I thoroughly tracked races across North America for one day. [_**The non-favorites won 169 horse races out of 272 or 62.13% of the time.**_](https://saliu.com/horseracing.html) The payouts were significantly higher than in the races when the favorites won.

Handicapping requires a big bankroll, as the favorites do not win 1 in 3 races. Therefore progressions like Martingale are required. It is disappointing when the favorite wins and the payout is really low.

![The best strategies in lottery, lotto are based on wacky filters to reduce many combinations.](https://saliu.com/HLINE.gif)

## <u>5. The Best Lottery Strategy, Unusual Filter Levels</u>

Lottery is the hardest of them all gambling events because of the high _odds_ or _probabilities_. Yet, I had my first American success in the mid-1980s. My lotto strategy was based on frequency of numbers and pairing. We hit _4 of 6_ two consecutive drawings. It made me buy a personal computer _before_ I even owned a car! It was a discontinued (cheap!) _Atari 800XL_ with 64 KB of RAM. I knew only software could handle lottery. That's how I became a self-made computer programmer. In fact, I founded the lottery programmer science.

**Filtering** (imposing _filters_ or _restrictions_) — that was the name of the game. Lottery cannot be handled in the same manner as gambling, where the odds are much lower and the bet is usually one outcome (e.g. a blackjack hand). I knew that groups of lottery numbers would repeat within a certain range of drawings, while other groups would not come out in various ranges of draws. Those groups define the [<u><i><b>lottery filters, lottery filtering, reduction: my discovery</b></i></u>](https://saliu.com/bbs/messages/919.html).

My personal computer didn't offer me the "luxury" that I have acquired in later years: More and more powerful computers — and more _affordable_. Nonetheless, it was in the 1985-1987 that I won the lottery most frequently than ever. I played in a group. I let my Atari generate 6/40 lotto combinations that passed the restrictions (filters) imposed by my quite small program. The computer would run for days and nights continuously. The combinations were randomly generated and consisted of 12 unsorted numbers. I would choose the last combonation generated by the computer. Then, I applied my by-now-famous [<u><i><b>12-number lotto wheel</b></i></u>](https://saliu.com/lottowheel.html), converting the combinations to 6-number tickets.

My decision was to change the 12-number lotto combination after each lottery drawing. The big event did happen: The lotto jackpot occurrence. I know exactly it was a Friday in February 1986. I had no car. One of the fellows in our lottery group gave me a ride to groceries every Friday. But before that, we would stop by a lottery agent and play our tickets. And his son had nothing better to do on February 12, 1986 but visit his father! So, my colleague said he did not have time to wait for me to wheel and fill out the lotto tickets for that drawing. We decided to play the previous combinations again. Had we played the new lotto combinations, we would have hit a 3-million dollar jackpot! The "American Tragedy" reads: [<u><i><b>History of Lottery, Experience, First Lotto Software, Systems, Strategies</b></i></u>](https://saliu.com/bbs/messages/532.html).

I was affected because I know how hard it is to hit the lotto jackpot just once. The repeats are even harder, given the huge odds. Just one jackpot is a life-changing experience like few others. But, looking at the bright side, that unfortunate event stimulated me a lot. I improved my software by orders of magnitude. I created software in different fields, especially probability theory, statistics, combinatorial mathematics, random number generating...

The first lottery strategy I derived from the _**Fundamental Formula of Gambling (FFG)**_ is presented on one of the earliest pages of my website: [<u><i><b>Lottery Software, Lottery Strategies, Lotto Systems</b></i></u>](https://saliu.com/LottoWin.htm). Each lottery combination has an equal probability **p** as the rest, but the combinations appear with different frequencies. The _**FFG median and standard deviation**_ are the key factors in the biased appearance. The lotto numbers tend to repeat more often when their _running skip_ is less than or equal to the _probability median_. The _probability median_ or _FFG median_ are calculated by the _**FFG**_ for the **degree of certainty DC = 50%**.

Many more lottery strategies followed, including for the _pick or digit lotteries_. There are numerous lottery strategies at this Web site, spread over numerous pages. Luckily, this site has a competent search facility, plus a well-organized content directory, plus a comprehensive sitemap. All are listed in the footer of this page and all Web pages of this site.

-   The best lottery strategy is based on _unusually high_ or _unusually low_ filters. Such filter settings occur less frequently, but eliminate a huge amount of lotto combinations. Worth repeating, winning the lotto jackpot is a life-changing experience.
-   The software should have a function that checks the lottery strategy in the past. That is, how many times the strategy hit in any range of lotto drawings. Also importantly, the strategy checking program should show the _skips_ or _misses between hits_ of the strategy and calculate the _median of skips_ or _skip median_. According to the _Federal Formula of Gambling_, everything repeats more often under the _FFG median_. Playing only lotto numbers with current (running) skips under the _FFG median_ [_**increases chances to hit lotto jackpots sevenfold**_](https://saliu.com/bbs/messages/923.html).
-   The skips of the lottery strategy are very useful to the player: He or she can skip a number of lottery drawings. By not playing a number of drawings, the lotto player can save real money. By contrast, the casino gambler must play every hand or spins in order to stay at the table. But, instead of skipping, the casino gambler plays the minimum bet, then increases the bet when the _**FFG**_ says the degree of certainty is higher.
-   A real-life example of the type of lottery strategy just presented can be read here: _"The Start Is the Hardest Part" in playing_ [_**Lottery Strategies, Lotto Systems**_](https://forums.saliu.com/lottery-strategies-start.html). instead of skipping, the casino gambler plays the minimum bet, then increases the bet when the _**FFG**_ indicates that the degree of certainty is higher.
-   Another real-life example of a _pick-4_ lottery strategy based on _<u>frequency groups</u>_ shows a profit of about $40,000 in 3 months' time: [_**Lottery Strategy, Systems, Software Based on Lotto Number Frequency**_](https://saliu.com/frequency-lottery.html).
-   Want more? My software and strategies, systems beat even a ridiculously tough lotto game: _Euromillions_. I generated and I posted 10 Euromillion combinations in the oldest lottery forum of the Internet. The 10-line set won prizes in almost half the draws since publication. Take that... I mean, read this, please: [_**Euromillions Strategies, Systems, Software, Winning Numbers**_](https://saliu.com/euro_millions.html#software).

![Playing just one lottery ticket every drawing is a losing, bad lotto strategy.](https://saliu.com/HLINE.gif)

-   There is another very popular question. _"Is it better to play one ticket in every lottery drawing, or wait a number of drawings and play an amount equal to the draws you skipped?"_ Most people opine that it makes no difference. It might not make a noticeable difference in cases of just one ticket played in just one year in lotto games with huge odds.
-   Mathematically, however, the chance (degree of certainty) is better if skipping lottery drawings and playing more tickets more rarely. See the presentation and the formulas: [_**Formulas, Software to Calculate Lottery, Lotto <u>Odds, Hypergeometric Distribution Probability</u>**_](https://saliu.com/oddslotto.html) (the <u>"Multum in parvo"</u> section). In games like roulette or dice rolling, the gain is over 30% when playing _multum in parvo_ (more in less).
-   I have tested extensively playing randomly-selected 1–2 combinations. The more one plays, the more one loses. By contrast, playing multiple sets of combinations generated by lottery strategies (also skipping drawings as per the skip chart of the strategy) leads to profits. Granted, there is a very, very low percentage of players who _win_ just by playing 1–2 tickets every drawing, for some length of time. Some of them even claim they won the very first time they played the lottery!

![The best mathematical lottery strategy plays more tickets less frequently, in fewer drawings.](https://saliu.com/HLINE.gif)

-   Given the enormity of the lottery phenomenon, a lot has to be written about it from the scientific viewpoint. I have written a lot about lottery myself. I only offer an abbreviated list of my Web pages on the topic of lottery strategy.
-   [<u><i><b>Filters in Lottery Software, Lotto Software</b></i></u>](https://saliu.com/filters.html).
-   [<u><i><b>Lotto Strategy: Sums, Odd Even, Low High Numbers</b></i></u>](https://saliu.com/strategy.html).
-   [**<u>Lottery Skip Systems</u>**](https://saliu.com/skip-strategy.html): _**Lotto, Powerball, Mega Millions, Euromillions, Horse Racing, Roulette, Football**_.
-   [**Theory, Analysis of _Deltas_ in Lotto, Lottery Software, Strategy, Systems**](https://saliu.com/delta-lotto-software.html).
-   [<u><i><b>Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies</b></i></u>](https://saliu.com/Newsgroups.htm).
-   [<u><i><b>Filters, Restrictions, Elimination, Reduction in Lotto, Lottery Software</b></i></u>](https://saliu.com/bbs/messages/42.html).
-   [<u><i><b>Step-By-Step Guide to Lotto, Lottery Filters in Software</b></i></u>](https://saliu.com/bbs/messages/569.html).
-   [<u><i><b>Pick-3 Lottery Strategy Software, System, Method, Play Pairs</b></i></u>](https://saliu.com/STR30.htm).
-   [<u><i><b>Vertical or Positional</b></i><b> Filters in Lottery Software</b></u>](https://saliu.com/bbs/messages/838.html).
-   [<u><i><b>MDI Editor Lotto Is the Best Lotto Lottery Software; You Be Judge</b></i></u>](https://saliu.com/bbs/messages/623.html).
-   [**Lottery Mathematics, Lotto Mathematics, Math, Maths**](https://saliu.com/gambling-lottery-lotto/lottery-math.htm): Probability, Appearance, Repeat, Affinity, Number Affiliation, Lotto Wheels, Lottery Systems, Strategies.
-   [_**Lottery, Software, Systems, Science, Mathematics**_](https://saliu.com/lottery.html).
-   [_**Neural Networking, Artificial Intelligence AI, Axiomatic Intelligence AxI in Lottery: Strategies, Systems, Software**_](https://saliu.com/neural-networking-lottery.html).
-   [_**Bookie Lottery, Good Odds, Payouts, Special Software**_](https://saliu.com/bookie-lottery-software.htm).
-   [_**Lottery Numbers: Loss, Cost, Drawings, House Advantage, Edge**_](https://saliu.com/lottery-numbers-loss.html)
    -   Playing random lottery numbers or favorite numbers guarantees losses because of the house edge. Only lottery strategies, systems, special software can win with consistency and make a profit.

![Download the best software for strategies in lotto, gambling, blackjack, roulette, horses, sports.](https://saliu.com/HLINE.gif)

## <u>6. Conclusions</u>

Axiomatic one, you just heard my long answer to the question raised at the beginning of this dissertation. I don't ask anybody to take me at my word. This is about mathematics. Everything mathematical can be verified or disproved. It has nothing to do with emotions, feelings, likes or dislikes, love or hate.

I do not encourage you to apply my theories and strategies, either. You are the only one who can decide what works for you. It is not a disclaimer, because I can tell you some. Many people have communicated with me and they informed me that they won using my ideas.

I can also attest that I am the first winner of my theory and strategies and systems and software. I have documented as much as I've been able to. I have also been as open as anyone. Not to mention that my theories and strategies have been available to read, even scrutinize, by everybody FREELY. My software was offered for free as well for a long period of my career – some programs still free.

Speaking of free: My site offers the best random generator for a multitude of lottery games, roulette, horse racing, sports. The outcomes are also optimized (for most lotto games). That online random number generator alone does improve the odds of winning and does create a larger number of winning players. It is undeniably mathematical. Since many, many less frequent outcomes are eliminated, there are fewer combinations to choose from; therefore there is a higher winning chance for a larger number of players.

Also, the generator allows for repeating the process very easily. The mathematics is: The chance to win in fewer trials is lower. The more combinations are generated, the higher the degree of certainty to hit big wins. The player doesn't pay anything while repeating the random process. The player saved money by not playing a large number of outcomes that would have lost anyway.

And here is one more rub: _"Why don't I win BIG?"_ There is no absolute guarantee. I always stress that absolute certainty is absolute absurdity. I'm talking now about honest people. It is not guaranteed that my theories, strategies, systems, software will make everybody a winner. But they are guaranteed to create more winners; therefore, everybody has a better chance to become a serious winner. And there is that _Sword of Damocles_ hanging above my head forever it seems like... threats of withholdings due to abusive application of law. They take everything from the bank, although gambling winnings require costs in real money!

As for those who hate me and attack me and denigrate me — they always wanted badly to do what I have done. We have the same interests. But them _googoos_ have been incapable of doing, mainly because of bad emotions — envy, jealousy turned into hatred. Meanwhile, I have succeeded in our common interests because I do not care about the negative reactions of others. Or, should I say, I pay attention to other people's reactions towards yours truly — but I am not deterred by others' mouth-foaming, or fall ecstatic when praise hits my eardrums! Besides, I hate nobody because of their achievements. If they are wrong, I say so honestly, mainly based on mathematics, without starting wildfires...

_"I received a phone call  
From one who claimed was tall.  
Then, he cursed me even worse  
For my gambling on the horse."_

Best of luck from _Parpaluck_ (Ion Saliu's royalty-name)!

-   Take a tour of the [**software**](https://saliu.com/infodown.html) download site.

![The best winning scientific strategies for gambling, lottery have always been free at Ion Saliu's.](https://saliu.com/HLINE.gif)

**[Home](https://saliu.com/index.htm) | [Search](https://saliu.com/Search.htm) | [New Writings](https://saliu.com/bbs/index.html) | [Fundamental Formula](https://saliu.com/formula.htm) | [Odds, Generator](https://saliu.com/calculator_generator.html) | [Contents](https://saliu.com/content/index.html) | [Forums](https://forums.saliu.com/) | [Sitemap](https://saliu.com/sitemap/index.html)**

![This is the site of truly winning lotteries, gambling, casino, blackjack, roulette, horses, sports.](https://saliu.com/HLINE.gif)
