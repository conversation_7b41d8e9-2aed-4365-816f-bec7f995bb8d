---
created: 2025-07-24T21:26:40 (UTC +08:00)
tags: [deltas,strategy,strategies,winning,wonder,grid,lotto,win,delta,software,lottery,statistics,drawings,draws,number,numbers,analysis,]
source: https://saliu.com/bbs/messages/648.html
author: 
---

# Lotto Deltas Strategy, Systems in Lottery Software

> ## Excerpt
> The deltas in lottery and lotto represent the difference between neighboring numbers in a lottery drawing or combination; used in lottery strategies, systems.

---
Posted by <PERSON><PERSON> <PERSON> on March 29, 2001.

In Reply to: [_**Powerful Lottery Strategy: Pairs, Frequency, Lotto Wonder Grid**_](https://saliu.com/bbs/messages/646.html) posted by _<PERSON><PERSON><PERSON>_ on March 28, 2001. See also the software updates at the end of this page.

To all:

I once ran the lottery deltas in Ion's MDIEditor Lotto and got some interesting results in the any/ver filter tables. One would have to do it all manually, but perhaps it would be instructive or at least interesting to do the same with those values. There might be statistical tendencies in the [****lottery skips systems****](https://forums.saliu.com/lottery-gambling-skips-systems.html) that might be generalizable enough to allow one to eliminate certain wheeled lotto combinations.

KM

: <PERSON> and fellow lottery players,  
: I have been looking not at the numbers but the skips of the numbers that hit and the sum of those lotto skips which has a good bell shaped curve. I have not been able to project that as a tool (lottery system) as yet. Any help here?  
: BigJer

: This may not be particularly what you're looking for, but it has been helpful in those instances when I've had far too many potential wheeled lotto combinations.

: I freely admit that this lottery strategy has its faults. However, since I cannot afford to play thousands of lotto tickets, I had to come up with criteria that at least decisive to cull down the herd, so to speak. It is as follows:

: Utilizing your _**Fundamental Formula of Gambling (FFG)**_ I get a pool of potential lotto numbers and wheel them with one of your wheels that automatically eliminates unlikely pairs, all odds, all evens and etc.

: Unfortunately that still leaves thousands of lottery combinations (tickets) that I cannot afford to play.

: Basically what I do is copy and paste the MDIEditor Lotto file into _SPSS_ statistical analysis software and filter out combinations from the original pool that do not meet the following criteria:

: Do wheeled combination / numbers in each position conform to historical positions based on entire history of lottery draw? If yes, then retain. Does the difference between the last and first lotto number, spread of line (deltas, average delta), conform to historical 80% statistical trend? If yes retain.

![The deltas (differences) in lotto, lottery software.](https://saliu.com/bbs/messages/HLINE.gif)

<big><u><b>• Updates</b></u></big>

_Klitser_ and Axiomatic Ones:

I love to give people names I believe are suited for book characters. Cyber space is a book; we only deal with characters, we see nobody. I hope you don't mind; therefore feel free to address me as _Jaqk Fowuru_ (a.k.a. _Parpaluck_).

-   The greatest random number/combination generator for lottery ever — _**IonSaliuGenerator**_ v7.0 — makes extraordinarily good usage of **lottery deltas** and also **standard deviation**. Run it in all confidence. See the link in the footer of this page ([_**Odds Calculator & Lottery Number Generator**_](https://saliu.com/calculator_generator.html)).
-   **Deltas** are considered, although in a limited form, across _**MDIEditor and Lotto WE**_.
-   I don't hide it. I write lottery software especially for myself; call it in–house software. It is very natural to keep a number of things as your trade secrets. My in–house software makes extensive usage of _lotto deltas_ in multiple forms. Also, the software applies the three fundamental forms of dispersion (_deviation_, _fluctuation_). They are: _standard deviation_; the _average of deviations_ disregarding the sign; _average deltas_ (_absolute differences_ between adjacent, neighboring numbers).
    
    There is a multitude of considerations for not releasing each and every piece of lotto software. Writing the documentation is a very difficult task. Then, things change constantly. It is very hard keeping up with updating the software and rewriting the documentation. Besides, a share of selfishness has always been a requirement of life.
    
    Here is a sample of a report generated by my dedicated lotto delta software (the _5 of 43_ game in Pennsylvania Lottery).
    
    ![Lottery software generates reports for lotto deltas or differences between numbers.](https://saliu.com/images/lotto-deltas.gif)
    
    _Del #1_ represents the 1st delta in a lotto combination or lottery drawing; that is, the unsigned difference between the 1st and 2nd number on the line (drawing). _Del #4_ represents the 4th delta in a lotto combination or lottery drawing; that is, the unsigned difference between the 4th and 5th number on the line (combination).
    
    If we enter the delta string _1 12 5 5_ exactly in specialized software, the program generates always 20 _5/43_ lotto combinations. If we enter the delta string _12 14 2 3_ exactly, the program generates always 12 _5/43_ lotto _combosnations_ (a favorite _lotteryspeak_ of mine!)
    
    We can also enter only some of the delta filters, while disabling other filters. _Disabling_ a filter simply means typing 0 (zero) at the screen prompt (or just pressing _Enter_ in my software). For example, _1, 12, 0, 0_ (we disregard delta #3 and delta #4) generates 4,060 lotto 5/43 combinations. I didn't see a repeat of those 2 deltas in 500 drawings my software analyzed. That means those combinations make a perfect candidate for the _**LIE elimination**_ function in my Bright lottery software packages. In reverse, you can wait for 2 even 3 deltas to repeat (see draw in line #14). In that case, you get a chance to win the jackpot with a few combos. The delta string _1 7 10 0_ would generate 300 combinations.
    
-   Interesting facts result from considering 2 consecutive numbers (_at least two_ consecutives); i.e. _delta = 1, position by position_. The delta string _1 0 0 0_ generates 111,930 _5 of 43 lotto_ combinations; _0 1 0 0_ generates 111,930 combos; string _0 0 1 0_ generates 111,930 _5 of 43 lotto_ combinations; _0 0 0 1_ generates 111,930 combos. All four cases constitute a good _kokostirk_ (candidate for _**LIE elimination**_).
-   I am yet to see all deltas in consecutive order; e.g. _1 2 3 4_ or _5 6 7 8_, etc. I ain't seen all 4 deltas to be equal to one another; e.g. _1 1 1 1_ or _6 6 6 6_. It must be so because [_**standard deviation rules**_](https://saliu.com/deviation.html)!
    
    The second part of the report shows the skips of groups of deltas (from 1 to 4) in the past of a lottery data file. For example the 2-delta group (_Two Deltas_) in line 1 (most recent drawing) skipped 4 lottery draws after last hit. You see in line #6 that _12_ and _5_ repeated in the same positions (_Del #2_ and _Del #3_).
    
    ![The best unique delta lotto, lottery software calculates deltas, generates combinations.](https://saliu.com/images/delta-lotto.gif)
    
    -   Read a detailed presentation of the lottery delta concept and dedicated software: [<u><b>Theory, Analysis of <i>Deltas</i> in Lotto, Lottery Software, Strategy, Systems</b></u>](https://saliu.com/delta-lotto-software.html).
    
    ![The role of deltas in winning lottery software, delta lotto systems are the best from Ion Saliu.](https://saliu.com/bbs/messages/HLINE.gif)
    
    ## [Resources in Lottery, Software, Systems, Lotto Wheels, Strategies](https://saliu.com/content/lottery.html)
    
    -   _**Download**_ [**the best lotto software, apps, programs**](https://saliu.com/infodown.html).
        
        Follow Ups:  
        
    -   [_**Wonder Grid Lottery Strategy Plays Pairs of Lotto Numbers**_](https://saliu.com/bbs/messages/638.html) **Ion Saliu** _3/24/2001._
    -   [Optimal lotto drawings range to analyze for lottery wonder grid system](https://saliu.com/bbs/messages/663.html) **Ion Saliu** _4/07/2001._
    -   [The optimal lottery drawings range to analyze - Illinois Lotto](https://saliu.com/bbs/messages/664.html) **Bill Smith** _4/09/2001._
    -   [BELLOTTO, BELLBET, BETUS: Software to generate random combinations for lotto, lottery, gambling](https://saliu.com/bbs/messages/665.html) **Ion Saliu** _4/09/2001._
    -   [The Wonder-grid lottery strategy hit the lotto jackpot in Belgium Lottery](https://saliu.com/bbs/messages/647.html) **El Loco** _3/29/2001._
    -   [_**Lottery Pairs System, Lotto Pair Strategy**_](https://saliu.com/bbs/messages/645.html) **lottoscorp** _3/28/2001._
    -   [Likely winning lotto numbers to wheel by best lotto wheels software](https://saliu.com/bbs/messages/644.html) **KM** _3/27/2001._
    -   [Lottery system: Lotto numbers, skips of lotto numbers, sum of gaps](https://saliu.com/bbs/messages/646.html) **BigJer** _3/28/2001._
    -   [Lottery Analysis in the Spirit of Ramanujan – The Great Indian Mathematician](https://saliu.com/bbs/messages/641.html) **Ramanujan** _3/25/2001._
    
    ![Lottery, lotto strategy = Lotto deltas, differences, standard deviation.](https://saliu.com/bbs/messages/HLINE.gif)
    
    Comments:  
    
    ![Ion Saliu: Software, Programs, Apps, Systems, Strategies.](https://saliu.com/bbs/messages/HLINE.gif)
    
    **[Home](https://saliu.com/index.htm) | [Search](https://saliu.com/Search.htm) | [New Writings](https://saliu.com/bbs/index.html) | [Software](https://saliu.com/infodown.html) | [Odds, Generator](https://saliu.com/calculator_generator.html) | [Contents](https://saliu.com/content/index.html) | [Forums](https://forums.saliu.com/) | [Sitemap](https://saliu.com/sitemap/index.html)**
    
    ![The web site of lotto deltas (statistical differences between lotto numbers).](https://saliu.com/bbs/messages/HLINE.gif)
