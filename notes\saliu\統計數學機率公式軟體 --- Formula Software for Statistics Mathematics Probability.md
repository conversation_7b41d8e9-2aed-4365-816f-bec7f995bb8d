---
created: 2025-07-24T22:46:27 (UTC +08:00)
tags: [FORMULA,software,standard deviation,binomial,mean,average,median,normal distribution,probability,mathematics,statistics,Gauss,curve,normal probability rule,gambling,certainty,]
source: https://saliu.com/formula.html
author: 
---

# 統計數學機率公式軟體 --- Formula Software for Statistics Mathematics Probability

> ## Excerpt
> Programming Super Formula software, gambling, probability, statistical calculations in standard deviation, binomial distribution, odds, median, mean, average.

---
![Formula Super Software is the best in probability, statistics, gambling, standard deviation.](https://saliu.com/HLINE.gif)

### 一、 [權威的機率、統計和賭博軟體](https://saliu.com/formula.html#Software) 二、 [賭博的基本公式：計算（N）](https://saliu.com/formula.html#Formula) 三、 [確定性程度（DC）](https://saliu.com/formula.html#Certainty) IV. [FFG 中位數的機率（p）](https://saliu.com/formula.html#Median) V. [二項分佈公式： _N 次試驗恰好 M 次成功_](https://saliu.com/formula.html#Binomial) VI. [_N 次試驗中至少 M 次成功_的機率](https://saliu.com/formula.html#Least) VII. [_N 次試驗中最多 M 次成功_的機率](https://saliu.com/formula.html#Most) VIII. [_在 N 中贏得 P 中至少 K 個 M 的_機率（賠率）](https://saliu.com/formula.html#Lotto) IX. [二項式標準差](https://saliu.com/formula.html#Deviation) 十、 [常態機率規則](https://saliu.com/formula.html#Probability) XI. [計算樂透中獎機率， _精確計算 M/N_](https://saliu.com/formula.html#Odds) XII. [使用超幾何分佈計算樂透賠率](https://saliu.com/formula.html#Hypergeometric) XIII. [其他：統計標準差、數字總和、文件打亂、隨機數](https://saliu.com/formula.html#Random)

![Probability, Statistics, Gambling Mathematics Software.](https://saliu.com/HLINE.gif)

## <u><span lang="zh-TW"><span data-immersive-translate-translation-element-mark="1"><span data-immersive-translate-translation-element-mark="1">1. 權威的機率、統計和賭博軟體</span></span></span></u>

**SuperFormula** 是一款公理化的軟體，是統計、機率、賠率、賭博數學等眾多領域的權威軟體。其功能分為 12 個類別。每個軟體類別都有其詳細的子類別。這款獨特的應用程式源自於許多人提出的創建軟體以自動化_**賭博基本公式 (FFG)**_ _**計算**_的要求。 FFG 發現了機率論乃至宇宙的最基本要素： _**確定性程度 DC**_ 、 _**機率 p**_ 和_**試驗次數 N**_ 之間的關係。

[![Buy the best software: Lottery, Powerball, Mega Millions, gambling roulette, blackjack, sports.](https://saliu.com/ScreenImgs/super-formula-1.gif)](https://saliu.com/membership.html)

• **FORMULA** ，版本 8.1，2003 年 4 月 22 日 ~ 已退役的 16 位元軟體。  
• **SuperFormula** ，版本 13.1，2011 年 1 月 ~ 32 位元軟體，取代 FORMULA。  
• **Shuffle** ，版本 8.0 ~ 2011 年 3 月。

![Calculate standard deviation, binomial distribution, odds, probabilities, median, mean, average.](https://saliu.com/images/standard-deviation-calculator.gif)

## <u><span lang="zh-TW"><span data-immersive-translate-translation-element-mark="1"><span data-immersive-translate-translation-element-mark="1">2.<i> 賭博的基本公式 </i>（ <i>FFG</i> ：來自 <i>p</i> 和 <i>DC 的 </i><i>N</i> ）</span></span></span></u>

此函數應用了_**賭博基本公式 (FFG)**_ 。它計算機率為 **p** 的事件以確定性程度 **DC** 出現所需的試驗次數 **N。**  
例如，需要拋多少次硬幣才能至少出現一次_正面_ （p = 1/2），且確定性達到 99%？答案是：拋 7 次。

我們也可以稱之為_宇宙的基本公式_或_萬物的公式_ 。

![Sign up to download the best software in its class, including mathematics, probability, statistics.](https://saliu.com/ScreenImgs/FFG1.jpg)

![Degree of certainty and probability calculated by Formula One software.](https://saliu.com/HLINE.gif)

## <u><span lang="zh-TW"><span data-immersive-translate-translation-element-mark="1"><span data-immersive-translate-translation-element-mark="1">3.<i> 確定性程度 </i>（來自 <i>p</i> 和 <i>N 的 </i><i>DC</i> ）</span></span></span></u>

此函數計算機率為 p 的事件在 N 次試驗中發生所需的確定性程度 DC。  
例如，在 10 次拋硬幣中至少出現一次_正面_ （p = 1/2）的確定性是多少？答：99.902%。  
機率 p 僅表示一次試驗中預期成功的次數。在實際機率情況或問題中，它並不能說明太多問題。例如，如果我們拋硬幣兩次，並不代表一定會擲出一次_正面_ 。我們可以拋硬幣十次，一次也看不到正面！

-   如果 p = 1 / N ，我們可以發現確定性程度 DC 與試驗次數 N 之間有一個有趣的關係。當 N 趨向於無窮大時，確定性程度有一個極限。當 N 趨向於無限大時，確定性程度 DC 的極限為 {1 — (1/e)}。  
    對於機率為 p = 1/N 的事件和等於 N 的試驗次數。  
    **e** 表示自然對數的底數，約等於 **2.718281828...**  
    極限 { **1 — (1/e** )} 約為 **0.63212055...(63.2%)**  
    這個[**_數學公式_**](https://saliu.com/formula.htm)被稱為 _**“Ion Saliu 的 N 次試驗悖論”**_ 。

![Probability, statistics, mathematics of median, average with Ion Saliu programs.](https://saliu.com/HLINE.gif)

## <u><span lang="zh-TW"><span data-immersive-translate-translation-element-mark="1"><span data-immersive-translate-translation-element-mark="1">4. <i>FFG 中位數</i>的機率（來自 <i>DC</i> 和 <i>N 的 </i><i>p</i> ）</span></span></span></u>

此函數在已知確定性程度 DC 和試驗次數 N 的情況下計算機率 p。  
在某些情況下，您擁有一系列 N 的統計中位數；因此確定度 DC=50%；但您不知道參數 p 的機率。程式會計算機率 p，從而得到確定度 DC 和試驗次數 N。

例如，LotWon 軟體產生的中獎報告會顯示一系列篩選條件及其中位數。如果未計算，您可以使用 QEdit 等編輯器進行列分組，然後按降序對列（篩選條件）進行排序。中位數表示已排序列的中間點。中位數也表示確定性為 50% 時跳過（SKIPS，即兩次中獎之間的試驗次數）的次數。我的軟體中並未描述所有篩選條件，因此沒有人能夠確定所有篩選條件的機率。

您可以使用**超級公式**的這個函數來確定濾鏡的賠率。其他過濾器也已描述，因此可以提前計算它們的機率。它們將證明_**賭博基本公式**_的有效性。例如，6/49 樂透中 _6 個中 3_ 的機率是 1/57。 FFG 計算這種情況 (DC=50%) 的中位數為 39。取真實的抽獎歷史，例如英國 6/49 樂透。對過去的 500 次抽獎進行中獎報告；按降序排列第 1 層的過濾器 _Threes_ 。中位數是 37 或接近 39。反之，當您看到中位數等於 37 時，您可以確定參數的機率為 _1/54_ （非常接近真實情況 _1/57_ ；但是，它幾乎等於_至少 1/53.nn_ ）。

![Binomial Distribution Formula software helps with analyses in stochastic events.](https://saliu.com/HLINE.gif)

## <u><span lang="zh-TW"><span data-immersive-translate-translation-element-mark="1"><span data-immersive-translate-translation-element-mark="1">5.<i> 二項分佈</i>公式（ <i>BDF</i> ）</span></span></span></u>

此函數計算機率為 **p** 的事件_**在 N 次試驗中恰好發生 M 次成功的**_機率 _**BDF**_ 。

例如，我們想確定在 10 次拋硬幣中恰好出現 5 _次_正面的機率。我們拋了 7 次硬幣，記錄了 5 _次正面_ 。我們拋了 8 次，又拋出了一次_正面_ （第 6 次）。我們必須停止拋硬幣；實驗失敗了。我們無法再在 10 次拋硬幣中剛好出現 5 _次正面_了。顯然，先前的事件影響了第 9 次拋硬幣的結果。  
事件序列意味著事件不會同時發生，而是相繼發生。

_**二項分佈公式**_揭示了一些有趣的事實。例如，10 次投擲中恰好出現 1 _次正面_的機率僅為 0.98%。 10 次投擲只出現 1 次_正面_和 9 次_反面的_機率相當低。

-   10 次拋硬幣中恰好擲出 5 _次_正面的機率是 24.6%。即使擲出正面的機率是 50%，10 次中恰好_擲出_ 5 _次正面_的情況並不常見！我們或許會認為，10 次拋硬幣中 5 次_正面_和 5 次_反面的_機率很低。然而並非如此！ 1000 次拋硬幣中出現 500 次_正面_和 500 次_反面的_機率甚至更低：僅 2.52%。
-   投擲 5 次_硬幣_出現 5 次正面的機率實際上代表的是_連續出現 5 次正面_的機率（3.125%）。
-   _數據大小有限制_ 。試驗次數 **N** 不得超過 _1754。_ 如果使用非常大的數字，就會發生溢出。階乘成長速度非常快，非常高！

_**對於 N 次試驗中恰好 M 次成功的**_廣義公式：

<big><b><span lang="zh-TW"><span data-immersive-translate-translation-element-mark="1"><span data-immersive-translate-translation-element-mark="1">BDF = C(N, M) * p <sup>M</sup> * (1 — p) <sup>N — M</sup></span></span></span></b></big>

**BDF** = 機率， _**N 次試驗中恰好 M 次成功**_的機率；  
**p** = 此現象的個別_**機率**_ （例如，p = 0.5 表示拋硬幣_正面朝上_ ）；  
**M** = _**準確的成功次數**_ （例如，拋硬幣 10 次，剛好有 5 _次反面_出現）；  
**N** = _**試驗次數**_ （例如，10 次試驗恰好有 5 次_反面結果_ ）。

![Calculate probability, odds, analyze statistics of M successes N trials cases.](https://saliu.com/HLINE.gif)

## <u><span lang="zh-TW"><span data-immersive-translate-translation-element-mark="1"><span data-immersive-translate-translation-element-mark="1">6. <i>N 次試驗中至少 M 次成功</i>的機率</span></span></span></u>

此函數計算機率為 **p** 的事件_**在 N 次試驗中至少 M 次成功的**_機率。

例如，我們想確定在 10 次投擲中至少出現 4 次_正面_的機率。從邏輯上講，以下情況都算_成功_ ：4 次_正面_ ；5 次_正面_ ；6 次_正面_ ；7 次_正面_ ；8 次_正面_ ；9 次_正面_ ；以及 10 次_正面_ 。顯然，這個機率比_剛好出現 4 次_正面的情況要好。  
存在資料類型限制。試驗次數 N 不得大於 _1754_ 。如果使用非常大的數字，則會導致溢出。

![Formula software calculates large cases of the factorial function or operator.](https://saliu.com/HLINE.gif)

## <u><span lang="zh-TW"><span data-immersive-translate-translation-element-mark="1"><span data-immersive-translate-translation-element-mark="1">7. <i>N 次試驗中最多 M 次成功的</i>機率</span></span></span></u>

此函數計算機率為 **p** 的事件_**在 N 次試驗中最多發生 M 次成功的**_機率。

例如，我們想確定 10 次投擲中最多出現 4 _次正面_的機率（10 次中不超過 4 次）。在 _N 次投擲中至少有 M 次正面_朝上，我們認為杯子是半滿的。為什麼不從悲觀的角度來看待這個問題呢：杯子有時可能是空的（或呈現出不同程度的空虛）！從邏輯上講，以下情況可視為_成功_ ：4 次_正面朝上_ ；3 次_正面__朝上_ ；2 次正面朝上；1 次_正面朝上_ ；以及 0 次_正面朝上_ 。機率可能高於 _10 次中恰好出現 4 次_或_至少出現 10 次正面朝上的_情況，但從玩家的角度來看，這並不會更好！

_數據大小有限制_ 。試驗次數 **N** 不得超過 **1754。** 如果使用非常大的數字，就會出現溢出。 _這都怪排列_ （由_階乘_運算子 _N!_ 計算）和計算機的限制…

![Run the most accurate and comprehensive software to calculate odds in lotto, lottery prizes.](https://saliu.com/HLINE.gif)

## <u><span lang="zh-TW"><span data-immersive-translate-translation-element-mark="1"><span data-immersive-translate-translation-element-mark="1">8. 在樂透、強力球和超級百萬中 <i>，從 N 到 P 至少贏得 K 個 M 的</i>機率（賠率）</span></span></span></u>

官方樂透賠率的計算方式是， _在 P 到 N 的機率中，M 到 K 的機率恰好是 1。_ 例如，在樂透 6/49 遊戲中，玩家每張彩券必須剛好投注 6 個號碼。彩券委員會從 49 個號碼中抽出 6 個中獎號碼。如果玩家只投注 6 個號碼，那麼_中獎 6 個號碼中 3 個的_機率是 _1/56.66_ 。例如，玩家可以從 10 個號碼中選擇 6 個組合進行投注。這樣，賠率就可以計算為， _在 49 個號碼中，10 個號碼中恰好中獎 3 個，_ _即 1/12.75_ 。

然而，在現實生活中，玩家會得到更好的待遇。委員會不會強迫玩家遵循_完全相同的_條件。現實生活中的條件是， _從 N 中至少中 M 個號碼中的 K 個_ 。委員會不在乎只玩 6 個號碼，還是玩隨機抽獎。他們不在乎你預期 _6 個號碼中 3 個_ ，但實際上 _6 個號碼中 4 個_ 。他們會按每張彩券的最高獎金支付給你。顯然，從玩家的角度來看 _，從 N 中至少中 M 個號碼中的 K 個_要比_從 N 中恰好中 M 個號碼中的 K 個_要好。

如果玩家玩了 57 張 6 號隨機彩票，那麼他應該預期會中一次 _3/6_ 。如果玩了 100 張 57 號彩票，那麼預期會中 100 _次 3/6_ 。不過，有時也可能中更高的獎項。這就是為什麼_從 49 張彩票中至少中 3/6_ 的機率是 _1/56.66_ 。

許多樂透輪盤愛好者可能會高興得尖叫起來。冷靜點，Wheely！之前的計算並不代表 54 條線（組合）就能保證在 49 個號碼的輪盤中 _3/6_ 一次中獎 100%！計算 100%保證的最低成功次數是完全不同的一回事。如果再考慮生成中獎的演算法，這本身就是一本書…

我在 [_**《惠靈基本公式》**_](https://saliu.com/wheel.html) 一文中寫道：  
_……\[恰好\] 贏得 **6 中 3 的**機率是 **1/57。 FFG** 計算出 **p = 1/57 的**中位數為 **39.16** ，四捨五入為 **40** ，即 **DC = 50%** 。這個數字 (40) 與英國 6/49 歷史文件中的實際情況有多接近？我用於此分析的文件包含 737 次抽獎（包含一些所謂的**額外**抽獎）。該文件包含從遊戲開始到 2003 年 1 月 1 日抽獎的 733 次常規抽獎。 6 **中 3 的**中位數約為 **39** 。_

就_標準差_而言，計算是正確的。然而， _FFG 中位數_恰好與_至少 6 分之 3 的機率（56 分之 1）_ 相符。真是太準了！別再說標準差了！我分析的所有資料檔案中都一直存在這個細微的差異。濾波中位數總是比 FFG 中位數低幾個點。現在看來，這完全說得通了。中位數是_至少 K/N 機率（M/N）_ 的結果，而不是_恰好是 K/N 機率（M/N）的_結果！

從現在起，我們或許會認為_**賭博的基本公式**_是賽局理論中_最精確的工具_ 。本網站上有一些關於_馬可夫鏈的文章：_ [_**彩票中的馬可夫鏈，樂透**_](https://saliu.com/Markov_Chains.html) 。  
在 Google 上搜尋_馬可夫鏈_ ，結果接近 10 萬條！這個話題很熱門！然而，我之前說過， _**FFG**_ 比_**馬可夫鏈**_快了好幾步。再說一次， _**賭博基本公式**_是博弈論中_最精確的工具_ 。與_**馬可夫鏈**_不同， _**FFG**_ 認為先前事件是未來事件的關鍵。根據著名的_**賭博基本公式**_ ，事件會精確地重複發生。

![Probability of binomial standard deviation with formula software reveals hidden gambling truths.](https://saliu.com/HLINE.gif)

## <u><span lang="zh-TW"><span data-immersive-translate-translation-element-mark="1"><span data-immersive-translate-translation-element-mark="1">9.<i> 二項標準差（BSD）</i></span></span></span></u>

此函數計算二項事件（即，以兩種且僅有兩種結果為特徵的實驗：贏或輸；成功或失敗）的二項標準差。這是標準差的_理論值_或_預期_值。標準差也可以_事後_計算：即實驗之後。其名稱不言自明。您可以在 LotWon 產生的 WS-3 報表中看到每個篩選器的標準差。濾波器會根據平均值上下波動。標準差計算所有偏離平均範數的偏差（波動）的正平均值。

_二項式標準差_具有很大的優勢。它顯示了預期的波動幅度。在開始拋硬幣之前，人們可以準確地知道在多次試驗（拋擲）中正面朝上的次數。或者，在玩 200 輪二十一點時，預計會有多少_手_牌獲勝。

![Normal Probability Rule Formula applied to stochastic events.](https://saliu.com/HLINE.gif)

## 10.常態機率規則

常態機率規則是比高斯曲線（或常態分佈曲線）更精確的統計工具。  
當我們計算二項式標準差時，結果就像是這樣的一份報告（針對 100 次拋硬幣）：

```
<span size="5" face="Courier New" color="#c5b358">The standard deviation for an event of probability
p =  .5
in  100  binomial experiments is:
BSD =  5

The expected (theoretical) number of successes is: <i>50</i>

Based on the <i>Normal Probability Rule</i>:

· 68.2% of the successes will fall within 1 Standard Deviation
from  50 - i.e., between  <i>45 - 55</i>
·· 95.4% of the successes will fall within 2 Standard Deviations
from  50 - i.e., between  <i>40 - 60</i>
··· 99.7% of the successes will fall within 3 Standard Deviations
from  50 - i.e., between  <i>35 - 65</i>. 
</span>
```

我一直在深入研究配對策略，尤其是在數位彩票中。我遇到的情況比傳統的常態機率規則好得多。這使得我採用了不同的方法來計算常態機率規則。傳統的規則是基於高斯或常態分佈曲線。這裡的關鍵字是_曲線，_ 意味著連續。然而，彩票或賭博是_離散的_ 。 _一刀切的_方法會導致結果不一致。在上面的例子中，我使用的新_常態機率規則_給了：

72.87% 的成功率將落在 1 個標準差之內  
從 0 到 45 到 55 之間  
需要注意的是，與二項分佈機率的情況一樣，存在數據限制：1500 次試驗。它涵蓋了相當廣泛的彩票和賭博案例。

![The lottery odds calculated as exactly are worse than odds as at least regarding lotto prizes.](https://saliu.com/HLINE.gif)

## <u><span lang="zh-TW"><span data-immersive-translate-translation-element-mark="1"><span data-immersive-translate-translation-element-mark="1">11. 計算<i>樂透中獎機率，精確計算 M/N</i></span></span></span></u>

計算彩票賠率，從 _0 / k_ 到 _m / k_ ， _恰好_為 。  
例如，樂透 49 遊戲抽取 6 個數字的賠率是： _6 個數字中 0 個_ ； _6 個數字中 1_ 個； _6 個數字中 2 個_ ；6 個數字_中 3 個_ ； _6 個數字中 4 個_ ； _6 個數字中 5 個_ ； _6 個數字中 6 個_ 。

機率計算為_恰好是 N 的 M 分之一_ （而不是_至少是 N 的 M 分之一_ ）。有時這樣的機率為 0：表示事件不可能發生；例如，對於 _6/10_ 的情況，機率_為 6 的 0 分之一_或 _6 的 1 分之一_ 。

此函數可處理任何類型的樂透遊戲，包括基諾、超級百萬和強力球。此函數整合了整個 **OddsCalc** 程式。該程式仍可獨立使用，但強烈建議使用整合的 **Scientia** 軟體包。

![Science is a collection of scientific programs in math, probability theory, statistics calculators.](https://saliu.com/ScreenImgs/Scientia.gif)

![Probability of Hypergeometric Distribution Software is followed by lottery commissions.](https://saliu.com/HLINE.gif)

## <u><span lang="zh-TW"><span data-immersive-translate-translation-element-mark="1"><span data-immersive-translate-translation-element-mark="1">12. 使用<i>超幾何分佈機率</i>全面計算樂透賠率</span></span></span></u>

超幾何分佈機率計算的樂透賠率是： _從 N 到 P 中 M 的 K 個機率_ 。此函數使用_超幾何分佈機率_公式（彩票委員會採用的官方方法）計算所有樂透遊戲（包括基諾和強力球）的賠率。  
例如：在樂透 49 遊戲中，開出 6 個中獎號碼，如果玩家投注 6 個號碼，並且彩票從 49 個號碼中開出 6 個號碼，那麼中獎號碼恰好是 6 _個中的 1 個的_機率是多少？在上述情況下： _(1,6,6,49)_ 的機率，或 _(4,6,6,49)_ 的機率，或 _(10,10,20,80 - 基諾) 的機率_ …

這款計算器可以幫助您計算不同長度和不同獎金組合的中獎機率。許多彩票玩家會選擇 12 個、18 個、20 個等號碼（ _選號_ ），並使用所謂的_樂透轉盤_或_簡化的彩票系統_ 。現在，在玩一個包含 18 個樂透號碼或選號的彩票池時，計算出 6 個中獎號碼的機率變得非常容易。

![Calculate statistics, statistical standard deviation, mean average, sums, median, randomize shuffle.](https://saliu.com/HLINE.gif)

## <u><span lang="zh-TW"><span data-immersive-translate-translation-element-mark="1"><span data-immersive-translate-translation-element-mark="1">13. 其他：統計標準差、數字總和、文件打亂、數字隨機化</span></span></span></u>

這是一些有用函數的集合：  
\- 將 N1 和 N2 之間的所有數字相加；  
\- 將資料檔案中的所有數字相加；  
\- 計算 N 的階乘（與 N 的排列相同）；  
\- 計算每次取 M 個元素的 N 個排列；  
\- 打亂連續或不連續的數字。

**A.** 將 N1 和 N2 之間的所有數字相加  
例如：從1到100的所有數字總和是5050；從33到100的所有數字總和是4522。該函數可以立即計算從1到十億，或從十億到萬億等的所有數字總和。它比使用手持計算器更快、更方便。

**B.** 將資料檔中的所有數字相加；計算統計標準差  
此函數計算先前以文字格式儲存在資料檔案中的所有數字的總和；例如  
17 34 2.5, 100, 99.99, 1000000, 71392.  
該檔案可以每行包含一個數字，也可以每行包含多個數字，每個數字之間以逗號、空格或兩者組合分隔。請勿在文件中留下任何空白！該函數也會計算：  
_平均值_ ； _中位數_ ； _最小值_和_最大值_ ； _統計標準差_ 。

賓州 Cash-5 資料檔有 2604 次抽獎（我的文件）。平均值為 20.0095。嗯，遊戲從 1 到 39 個數字抽取。理論平均值為 (1+39)/2 = 20。我在賓州彩券 pick-3 遊戲中的資料檔案有 3899 次抽獎。平均值為 4.4817。遊戲從 0 到 9 抽取 3 位數字。理論平均值為 (0+9)/2 = 4.5。我在賓夕法尼亞州彩票 pick-4 遊戲中的數據檔案有 6056 次抽獎。平均值為 4.4964。遊戲從 0 到 9 抽取 4 位數字。理論平均值為 (0+9)/2 = 4.5。我的賓州 lotto-6 資料檔有 475 次抽獎。平均值為 34.2589。遊戲從 1 到 69 個數字抽取。理論平均值為（1+69）/2 = 35。

那麼更複雜的強力球遊戲呢？曾經有一款強力球遊戲，包含 1 到 49 的 5 個常規號碼和 1 到 42 的強力球。理論平均值為加權平均值：{(((1+49)/2)\*5) + ((1+42)/2)} / 6 = 24.4167。我的強力球 49/5/42 資料檔有 514 次開獎。平均值為 24.356。目前的強力球遊戲包含 1 到 53 的 5 個常規號碼和 1 到 42 的強力球。理論平均值為加權平均值：{(((1+53)/2)\*5) + ((1+42)/2)} / 6 = 26.0833。我的強力球 53/5/42 資料檔有 54 次開獎。平均值為 25.605。即使抽取次數很少，實際平均值也接近理論值！

標準差的計算也非常接近賭博基本公式。我的軟體考慮到了這些數學要求。組合或集合遵循機率論和統計學規則。組合始終位於高斯鐘形曲線（常態分佈曲線）內。這是優化的一個重要因素。

這些數字的頻率各不相同，但平均值非常接近理論值！  
另請參閱 1-2-3-4-5-6 類型組合的有趣案例： [_**樂透組合 1 2 3 4 5 6 機率、賠率**_](https://saliu.com/bbs/messages/961.html) 。

**C.** 計算 N 的階乘（與 N 的排列相同）  
此函數計算 N 或 N 階乘的排列。  
N！ = 1 x 2 x 3 x ... x N  
例如：6 的排列 = 720（1x2x3x4x5x6 = 720）。  
N 不能大於 1500——溢出危險！

**D.** 計算每次取 M 個元素的 N 個排列  
此函數計算每次取 M 個的 N 個排列：  
A(N,M) = N x (N-1) x ... x (N - M + 1)  
例如：A(10,3) = 10 x 9 x 8 = 720。  
使用此功能可以計算賽馬中的總_三重彩_或_超級彩_組合；例如 1,2,3；4,2,7；10,6,5 等。

**E.** 連續或非連續數位的洗牌池  
**Shuffle** 可以打亂（隨機排列）一組從 1 到 N 的連續數字。如果使用者在輸入提示符號下輸入 10，程式將開始連續隨機化 10 個唯一的數字，從 1 到 10。序列如下所示：3、7、1、9、10、4、6、5、2、8。序列 1、2、3、4、5、6、7、8、9、10 非常罕見，據說只有全能數字才能產生它。  
還有另一種情況：不連續的數字組，例如：1、44、33、55 77 22 66 99 13 111 49 29 25 9 54。 Super **Formula** 的 XII 功能也可以處理這種隨機情況。不連續的數字組可以在螢幕提示下輸入。或者，可以先將數字儲存到文字檔案中。文字檔案可以用作 **SuperFormula 的**輸入裝置。數字必須以空格、逗號或兩者組合分隔。此外，數字可以放在多行或一列中。使用者選擇運行隨機化過程的次數。初始不連續數字組可以按任何順序排列，包括連續的。

**Shuffle** 會隨機化（隨機排列）數字、文字或數字檔案。檔案隨機化是我軟體使用者最需要的功能之一。程式採用三種隨機化方法：  
~ _水平_ ：打亂行內項目的順序，但不改變文件中行的順序；  
~ _垂直_ ：隨機化文件中行的順序，而不改變行內項目的順序；  
~ 將文件視為_單行/列_資料：隨機化文件中的所有項目，無論行和列。

文件中的行數可能不均勻；即項目數量不一致。

該程式還可以產生各種彩票遊戲的組合，包括強力球、超級百萬、歐洲百萬彩票。這些組合與現實生活中的彩票組合非常相似：軟體會產生無序的隨機數。

![Shuffle is the best software to randomize numbers or files.](https://saliu.com/ScreenImgs/shuffle-1.gif)

![Probability Theory, Live is a great book by Ion Saliu, including mathematical, scientific software: Super Formula.](https://saliu.com/probability-book-Saliu.jpg)閱讀 Ion Saliu 的第一本印刷版書籍： [**_《機率論，現場版！ 》_**](https://saliu.com/probability-book.html)  
~ 基於具有廣泛科學應用的寶貴數學發現，包括應用於科學、統計軟體的機率論：超級公式。

![Statistics, gambling mathematics software was created by founder of theory of probability of life.](https://saliu.com/HLINE.gif)

[

## <u><span lang="zh-TW"><span data-immersive-translate-translation-element-mark="1"><span data-immersive-translate-translation-element-mark="1">機率論、數學、統計、標準差、軟體資源</span></span></span></u>

](https://saliu.com/content/probability.html)查看有關機率論、數學、統計學、標準差以及軟體主題的頁面和資料的綜合目錄。

-   [_**標準差**_](https://saliu.com/deviation.html)的最佳介紹：理論、演算法、軟體。
-   最新內容： [_**標準差、變異數**_](https://saliu.com/standard_deviation.html) 、變異性、波動性、揮發性、變化、離散度、中位數、平均值。
-   新的 FORMULA 3.1 [_**軟體可計算選舉和政治中的標準差**_](https://saliu.com/bbs/messages/547.html) 。
-   在 _FFG 中值_附近的[_**鐘形（高斯）曲線內產生組合**_](https://saliu.com/bbs/messages/559.html) 。
-   升級到公式： [_**標準差、二項分佈**_](https://saliu.com/bbs/messages/269.html) 。
-   [_**機率、幾率、標準差、二項式軟體**_](https://saliu.com/bbs/messages/264.html) 。
-   [_**軟體，使用超幾何分佈機率計算彩票賠率的公式**_](https://saliu.com/bbs/messages/265.html) 。
-   [_**機率、幾率、公式、演算法、軟體計算器**_](https://saliu.com/bbs/messages/266.html) 。
-   _**下載數學、標準差、統計**_[**<u>軟體 </u>**](https://saliu.com/infodown.html) 。
    
    值得關注的是：  
    
-   [_**賭博基本公式（FFG）**_](https://saliu.com/Saliu2.htm) 。
-   [_**賭博 基本 公式 的 數學**_](https://saliu.com/formula.htm) .

![Statistics, probability, gambling Software, formulae are free at this site.](https://saliu.com/HLINE.gif)

**[首頁](https://saliu.com/index.htm) | [搜尋](https://saliu.com/Search.htm) | [新文章](https://saliu.com/bbs/index.html) | [基本公式](https://saliu.com/formula.htm) | [賠率產生器](https://saliu.com/calculator_generator.html) | [目錄](https://saliu.com/content/index.html) | [論壇](https://forums.saliu.com/) | [網站地圖](https://saliu.com/sitemap/index.html)**

![Thanks for visiting the site of statistics, probability, formula software.](https://saliu.com/HLINE.gif)
