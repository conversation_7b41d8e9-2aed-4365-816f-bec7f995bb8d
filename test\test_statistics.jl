using Test
using SaliuSystem0009.Statistics
using SaliuSystem0009.DataProcessor

@testset "Statistics.jl tests" begin
    # Create dummy LotteryDrawing data for testing
    drawings = [
        LotteryDrawing("2023-01-01", [1, 2, 3, 4, 5, 6]),
        LotteryDrawing("2023-01-02", [1, 7, 8, 9, 10, 11]),
        LotteryDrawing("2023-01-03", [2, 3, 7, 12, 13, 14]),
    ]

    # Test generate_w_report (output to console, so just check if it runs without error)
    @test_logs (:info, "Generating W* report (Number Frequency)...") generate_w_report(drawings)

    # Test calculate_median
    @test calculate_median([1, 2, 3, 4, 5]) == 3
    @test calculate_median([1, 2, 3, 4]) == 2.5

    # Test generate_md_report (output to console, so just check if it runs without error)
    @test_logs (:info, "Generating MD* report (Median Skips)...") generate_md_report(drawings)
end