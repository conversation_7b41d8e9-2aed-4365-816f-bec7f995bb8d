「Fundamental Formula of Gambling (FFG)」在跳躍系統的建構中扮演著**核心的數學基礎角色**，它提供了一套理論框架，幫助彩票玩家根據歷史數據識別出更有可能中獎的組合，從而減少投注量並最佳化策略。

以下是 FFG 如何影響跳躍系統建構的詳細闡述：

1. **FFG 的核心概念提供「跳躍」的理論依據**：
    
    - FFG 定義了**個體機率 (p)**、**試驗次數 (N)** 和**確定程度 (DC)** 之間的關係。在彩票中，「跳躍 (skip)」指的是**某個號碼或模式在兩次中獎之間錯過（未出現）的開獎次數**。FFG 的應用揭示了看似隨機的事件，如彩票號碼的出現，實際上遵循著可預測的統計規律。
    - FFG 的創始人 Ion Saliu 觀察到，彩票號碼在**FFG 中位數**（median）或更少次的開獎後重複出現的頻率更高。
2. **FFG 中位數作為「正常範圍」的指標**：
    
    - **FFG 中位數** 是指在 **50% 的確定程度 (DC)** 下，一個事件至少發生一次所需的試驗次數 (N)。例如，在 6/49 樂透遊戲中，FFG 計算的 3 個號碼中獎的機率中位數約為 39。
    - 彩票軟體（如 MDIEditor Lotto WE 和 SkipSystem）會計算每個號碼的跳躍中位數。這個中位數代表了**一半的情況下，號碼會在該跳躍值或更少的跳躍後再次出現**。
    - 基於 FFG 的分析表明，中獎組合主要來自 FFG 中位數附近的「鐘形曲線」區域。透過將組合生成限制在這個區域內，可以提高中獎率。
3. **動態過濾與策略優化**：
    
    - FFG 強調彩票是一個**動態過程**，號碼的出現並非靜態的完全隨機。雖然單一組合的機率始終相同，但從統計學角度來看，過去的開獎結果確實會影響未來事件的可能性，因此動態調整策略至關重要。
    - 跳躍系統透過設定過濾器，將投注範圍限制在那些**當前跳躍次數小於或等於 FFG 中位數的號碼**上。這種策略在 6/49 樂透中能顯著提高中獎機率，甚至達到**七倍**。
    - Ion Saliu 的彩票軟體（如 SkipSystem）預設值通常就是 FFG 中位數，用戶無需手動計算。這使得基於 FFG 的跳躍策略更容易應用.
    - 此外，軟體還允許玩家分析**連續兩次或三次跳躍的總和**，並與 FFG 中位數的兩倍或三倍進行比較，以發現更多的投注機會。例如，在 6/49 樂透中，如果兩個最近跳躍值的總和小於兩倍的 FFG 中位數 (12)，則該號碼是可玩的。
4. **「謊言消除」(LIE Elimination) 策略的結合**：
    
    - 儘管跳躍系統可以大幅減少組合，但生成的組合可能仍然包含許多「不必要」的組合，例如所有跳躍值都非常小或非常大的組合。
    - FFG 衍生的 **「謊言消除」(LIE Elimination)** 策略是一種反向思維，旨在**故意設定預計不會中獎的過濾器**，從而將這些極不可能中獎的組合從投注池中剔除。
    - 例如，可以將那些所有跳躍值都為兩位數（非常大）的組合，或頂部/底部頻率號碼的組合，設定為 LIE 檔案，並透過「清除 (Purge)」功能移除。這種組合使用進一步優化了跳躍系統的效率，將大量不必要的組合從投注中剔除。
5. **不同跳躍計算方式與 FFG 的兼容性**：
    
    - 在 Ion Saliu 的軟體中，「跳躍」有兩種計算方式：MDIEditor Lotto WE 和 LotWon 軟體將跳躍定義為兩次中獎之間**錯過**的開獎次數，而 SkipSystem 則定義為兩次中獎之間**經過**的開獎總數（包括最後一次命中）。
    - 儘管存在差異，但 FFG 始終是其背後的數學支撐。SkipSystem 的計算方式與 FFG 直接計算的「試驗次數 (N)」更為一致，因為 FFG 的 N 值不扣除 1。對於 LotWon 軟體，在應用 FFG 理論時，通常需要將跳躍值減去 1 來與 FFG 的 N 值兼容。

總之，FFG 透過提供對彩票號碼動態行為的數學理解，特別是**FFG 中位數的概念**，直接影響了跳躍系統的建構。它使玩家能夠識別出**號碼重複出現的統計傾向**，並將其應用於過濾、組合生成和逆向策略中，以**提高中獎機率和效率**。