---
created: 2025-07-24T21:26:41 (UTC +08:00)
tags: [strategy,reference,software,program,programs,lotto,lottery,combine strategies,editor,files,]
source: https://saliu.com/cross-lines.html
author: 
---

# Cross-Reference Lottery Strategies, Lotto Strategy Files

> ## Excerpt
> Cross-reference, combine lottery strategy files created by many types of lotto software to increase probability, winning chances by orders of magnitude.

---
## <u><i>Cross-Reference, Combine</i> Lottery Strategy Files Created by Various Types of Lottery Software</u>

## By <PERSON>, ★ _Founder of Lottery Strategizing Science_

![Analyze advanced tips on creating lotto, lottery strategy files, strategies.](https://saliu.com/HLINE.gif)

Last updated: March 2018.

<big>• FileLines</big> version VI.O, December 2018 – Scientific software to combine lottery strategy files  
<big>• SortFilterReports</big> version 3.0 ~ May 2016 – Lotto, lottery software to sort reports by column  
<big>• Sorting</big> version XIX.O ~ December 2018 – Scientific software to sort and format data files.

## <u>1. The necessity of combining lotto, lottery strategy files</u>

-   <u><b>FileLines</b></u> is a component of the **Bright / Ultimate** software packages, menu #2, function _T = Cross-Checking Strategies_.
-   The program writes to disk the lines of specified indexes in a file, usually a lottery strategy file created by the strategy checking functions. This represents one situation when working with **LotWon software** and also **MDIEditor Lotto WE**.
-   For example: You created the _WS_ files in the _**Command Prompt LotWon**_ lottery software, such as **Bright / Ultimate** software packages. You also generated the statistical reports in **MDIEditor Lotto**. You then created the lottery strategy file for the stats in **MDIEditor And Lotto** (_**Windows**_ lottery and lotto software).
-   You can't have one strategy file across the two platforms. You want to see the same line numbers in the filter report files for a more comprehensive lottery strategy. That's when **FileLines** steps in. The main (original) function is: _R = Common Lines from W\* Reports_.

![Combine lottery strategies, lotto strategy files in MDIEditor lotto software and Lotwon.](https://saliu.com/ScreenImgs/lottery-strategies.gif)

However, the strategy checking is done by type of application. There are situations when you want to see the lines (lottery drawings) that various strategies have <u>in common</u>. In other words, we want to know when different strategies would hit <u>simultaneously</u>. For that task, we need a function to create a new file consisting of the common numbers in various _HIT_ files created by various **Strategy\*.exe** programs. New function introduced in December 2018: _H = Common Lines from **.HIT** Files_.

![When multiple lottery strategies hit the odds are reduced tremendously.](https://saliu.com/ScreenImgs/lottery-strategies-hits.gif)

The numbers in a _HIT_ file actually show the lottery draw numbers when a particular strategy hit (won). Normally, a single-app strategy also creates a _skip report_. But this feature becomes impossible when we combine or cross-reference multiple strategies as it is the purpose of this incredible application: **File Lines**. And that's when this module steps in: Create a skips report based on the combined _HIT_ file we created from multiple lottery strategies. The output (report) will show the unordered skips, the skips in ascending order, and the all-important _median skip_.

And that's the goal of the December 2018 function _F = Format, Sort HIT Files & Calculate Skips_ as seen in this screenshot:

![Playing the lottery is a lot more effective when combining many strategies.](https://saliu.com/ScreenImgs/lottery-strategies-skips.gif)

The program cross-references strategy files, data files, any text files, for that matter. The user can decide what lines to cross-reference. It could be a _prompt input_ (typing), or a _file input_. The _file input_ is the most convenient method. The input file consists of a series of numbers representing what lines to cross-reference in the _source files_. The source files can be the _W\*_ or _MD\*_ _lotto filter files_, and/or _lottery data files_ (drawings history).

Creating the input file is as easy as typing numbers: one by line, or several numbers per line. If a line has several numbers, commas or blank space(s) must separate the numbers. A line must always end with _Enter_. The last number of the line must not be followed by a comma or blank space(s). The file must not have any blank lines. These are, in fact, the requirements of the data files in **LotWon** (_**Command Prompt**_ lottery software) and **MDIEditor Lotto WE**.

The main purpose of **File Lines** is the cross-referencing of all filter files to create cross-platform lotto strategy files. Every lottery strategy file in _**Command Prompt LotWon**_ and **MDIEditor And Lotto** has a column named _Line Number_. It refers to the corresponding line in the data file at the time the _WS_ filter files were created. We need a way to save the numbers in the _Line No_ column to a text file (the input to **FileLines**).

![Cross-reference files show parameters in different strategy files for the same draw.](https://saliu.com/ScreenImgs/cross-reference-data.gif)

There are two working procedures, as seen in the screenshot above:  
~ entirely **manually** (initially it was the only procedure available)  
~ **file**\-based data entry (introduced in 2018 — the highly recommended method).

## <u>2. Working with the <i>FileLines</i> Application</u>

In both procedures (methods) the program requires **source** files. These are the filter files (_W\*_, _MD\*_ in **Bright / Ultimate** lottery software or the statistical reports in **MDIEditor And Lotto**). The entire operation can be very tricky, especially in the beginning. First of all, the length of the reports must be equal both in _**LotWon**_ and **MDIEditor Lotto**. If you did the statistical report in **MDIEditor And Lotto** for 1000 drawings, then all four _WS_ files in _**LotWon**_ must be done for the same data file (on the same date) and for 1000 draws.

You can download a sample input file: [_**INP-FILE.TXT**_](https://saliu.com/pub/INP-FILE.TXT). It has 10 numbers, one per line; the largest number is 59. There is a duplicate number: 8. It is valid to write multiple copies of the same line. Simply put it, the input shows numbers that act as **indexes**. The indexes retrieve the corresponding **line numbers** from every **source** file and write them to a **report** file.

![All lotto programs and lottery software by Grand Master Programmer work great together.](https://saliu.com/ScreenImgs/cross-reference-input.gif)

You can extract lines from as many **text** files as you want to. Since this program is aimed primarily at lottery, the software works best with the special files created by the strategy-checking software. They usually have the _HIT_ extension; e.g. _ST5.HIT_ in the screenshot above. The lotto-specific _ST5.HIT_ input file can be downloaded by visitors with paid membership. The tiny text file is part of a ZIP package that also contains the filter-input files necessary for the recommended function of this program: _F = File Input_.

### <u>A. Manual Operation: <i>S = Screen Prompt</i></u>

**File Lines** needs to know in advance the _number of lottery filter files_ to open. For example, you created the input file from an _**MDIEditor & Lotto**_ strategy file for lotto-6. The 32-bit **Bright / Ultimate Software** creates 4 _W_ files. The next step is very important. **FileLines** needs to know in advance the _number of lines the header_ consists of. The source files (_WS_ or filter files, in this example) can have _headers_. The headers represent all the lines before the first bit of filter analysis appears.

Here you have some header lengths in the FILTER files:  
**W\*.\*, MD\*.\* = 13** lines in headers (18 lines in W3.7);  
**Strategy\* (MDIEditor And Lotto WE) = 13** lines in headers;  
**DE\*.1, FR\*.1, SK\*.\*, = 13** lines in headers;  
**PAIR\*.WS = 13** lines in header;  
**LieID\*.REP = 13** lines in headers;  
**DATA\* = 0** lines in headers (NO header — they are NOT filter files).

You can always open a report in a text editor and count the lines in the header, including blank lines. _**Notepad++**_ shows the line numbers in the left-most column.

The lottery strategy file in **MDIEditor And Lotto** with 13 lines in the header was created manually. The filter file automatically saved by the grand software application has no header. I do the statistical report. Go all the way down to the filter section. I select the entire section, including the header. I copy and paste to a new text file. I save it as the source file for **File Lines**.

You can open any number of filter files to select lines from. Make sure that all filter files were generated for the same number of past lottery draws as the largest index in the input file. The source files must have headers of equal lengths. You ran, say, the program for the 4 W6 filter files created by **Report6** lotto software. You generated one _report file_, e.g. _ALL-STR6.1_. You can run **FileLines** again using the data file as source. You want to know the actual lottery drawings corresponding to the lines in the strategy file. Parameters for **File Lines**: _Number of source files_: 1; _Length of header_: 0; report file: _ALL-STR6.2_.

You can combine all report files in one, using a text editor (load a file at the end of the preceding one). Better still, run _**Notepad++**_, drag-select all report names and open all of them at once. You can then move from tab to tab for the best viewing of multiple reports created from the same index file (e.g. _ST5.HIT_).

You can type the corresponding indexes at the prompt. It is quick-and-easy if you already know the drawings in the strategy file. Otherwise, the best method is creating an index-input file.

**FileLines** can print selectively or randomly line numbers from any text files. You can generate random numbers using **Permute Combine** combinatorial software. Generate, say, one number per line. If the text file you want to print lines from has 1000 lines, then the largest combination number is 1000; numbers per combination = 1. How many combinations to generate: a number between 1 and 1000. Save the input file.

**File Lines** can be tricky sometimes. It works with files different from one another, as far as the headers are concerned. The key is to know exactly the structure of the text files fed to the application. The files must be in text format. The source (lottery filter) files opened in one step must have headers of equal length and an equal number of lines. The largest index in the input file must be equal or less than the largest number of lines in the source files.

### <u>B. Script Operation: <i>F = File Input</i></u>

This highly recommended method was added in 2018. It increases the productivity by an order of magnitude. Instead of the tedious operation of entering the filter files manually, then the index input file, then choosing a name for the report — this function enters the data from a file you previously created. You took your time, entered the correct data, you avoided all errors. Your file will run the program flawlessly, almost automatically, and you can rerun the same filter-input file again and again...

![Software for lotto, lottery to cross-reference strategy files created by MDIEditor, DOS programs.](https://saliu.com/ScreenImgs/reference-strategies-file.gif)

The aforementioned ZIP file contains 9 sample files specifically created for the 5-number lotto games. The registered members can download it right here: [_**HeaderFilterFiles.zip**_](https://saliu.com/pub/HeaderFilterFiles.zip). The files are intelligently separated by software application and number of lines in the headers. You can easily adapt the file for any lottery game in the **Bright / Ultimate** grand applications. You can see the structure of one sample filter input file in the screenshot above. You saw also the names of all sample files in Section 1. A few more details —

-   **HEAD-WMD.13** ~ contains the _W\*.\*_ and _MD\*.\*_ filter files (a.k.a. winning reports) created by _W = Winning Reports (W\* & MD\*)_, main menu of **Bright / Ultimate**. Default strategy input file: _ST\*.HIT_.
-   **HEAD-SkDeFR.13** ~ the _SK\*.\*_, _DE\*.1_, _FR\*.\*_ filter files created by _S = Skips, Decades, Frequencies_, menu #2. Default strategy input file: _Skip\*.HIT_.
-   **HEAD-Del.13** ~ _Del\*.\*_ filter files created by _D = Deltas (DeltasLotto5.EXE)_, menu #2. Default strategy input file: _Del-STR\*.HIT_.
-   **HEAD-LieID.13** ~ _LieID\*.\*_ filter files created by _D = Dedicated LIE Elimination (LieID)_, main menu. Default strategy input file: _LieIDStrat\*.HIT_.
-   **HEAD-LieWS.9** ~ _LieDecade\*.WS_, _LieLastDigit\*.WS_, _LieHiLo\*.WS_, _LieOdEv\*.WS_, filter files created by _N = LIE StriNgs (LieStrings5.EXE)_, main menu. Default strategy input file: None; this type of software generates combinations that must be _**LIE Eliminated**_.
-   **HEAD-SkFrGr.13** ~ _SFG\*.\*_ filter files created by _Q = Skips & Frequency Groups-Only_, menu #2. Default strategy input file: _SFG\*.HIT_.
-   **HEAD-UsrGr.13** ~ _UG\*.\*_ filter files created by _G = Work with User's Number Groups_, menu #2. Default strategy input file: _UG5\*.HIT_.
-   **HEAD-W7.18** ~ _W\*.7_ filter files created by _W = Winning Reports (W\* & MD\*)_, main menu; applicable to pick-3 and pick-4 only.
-   **HEAD-MDI.13** ~ contains the _FiltersLotto\*_ filter report created by **MDIEditor And Lotto WE**. Default strategy input file: User created _ST-MDI.HIT_. The user checks a strategy, then opens the _Strategy_ file in **Notepadd++**. Select the first column that shows the line numbers when the specific lottery strategy hit.

Axiomatics, you should name the _HIT_ files based on the names you gave to your strategy-checking files. Obviously, you should name those files as meaningfully as you can. I published a Web page where I showed many winnings in the pick-4 digit game: [_**Lottery Strategy, Systems, Software Based on _Lotto Number Frequency_**_](https://saliu.com/frequency-lottery.html). I named the strategy-checking report _STR-FR4-1-220_. Accordingly, I named the strategy-input file _STR-FR4-1-220.HIT_. I have another text file that explains what my lottery strategies do, what their main parameters are, how they performed, etc.

The **File Lines** software adapts greatly to your situations by saving the data you work with to an _INI_ file. The defaults are offered based on that file, plus internal functions. You can easily edit the defaults in the yellow-background text line. Use the arrow keys, plus the _Home_, _End_ and _Insert_ keys. The _Insert_ key toggles between the insert and overwrite modes.

![Combine many lottery strategies and the player increases the chance to win big.](https://saliu.com/ScreenImgs/cross-reference-report.gif)

Finally, the program saves all your efforts to a **report** (see above). The naming procedure is also highly automated. The default name is based on the files you worked with. It has the _CROSS_ prefix to denominate a cross-referencing file. You can use any filename you want instead.

### <u>Recommended Steps in Running <span color="#ff0000"><i>FileLines</i></span></u>

-   **Update** your data file with real results (lottery drawings); always recreate the _D_\* file (the real data plus the SIMulated file).
-   Run the **filter reporting** functions for all entries specified in the _HEAD_ paragraph above. I do all the reports for 1000 drawings.
-   **Create a strategy** if you haven't created a bunch of them already.
-   **Check the strategy** in the respective type of software. Generate the strategy report and the _HIT_ file.
-   Run the **File Lines** app.
-   **Open the strategy report** in **Notepadd++**.
-   **Drag-select all CROSS reports** created by **FileLines** to open all of them at once.
-   Navigate from tab to tab to **observe patterns** of interest to further improve your lottery strategy.

## <u>3. Auxiliaries in combining strategy files and generating lottery strategies</u>

Most LotWon software users already know the shareware editor **QEdit**. It is a great program, albeit limited by the 16-bit DOS platform. It doesn't work in the newer 64-bit versions of Windows operating system.

The column-selecting feature is under the _Block_ menu (_Mark column_). The shortcut is _Alt+K_. Move the prompt in front of the column to select. If you know that the column is 4 digits wide but the first entry is only 2 digits wide, move the prompt two positions to the left. Press simultaneously _Alt+K_ and move the prompt to the end of the column (4 positions wide, this example). Do not select +/- at the end of the column. Press _Ctrl+J_ to _Go to line_. Type a number large enough to go to the last row of the column. The entire column was selected. Press _Alt+W_ to _write_ the selected block to a disk file. Save the new file. I add _.INP_ to its name, to remember easier that it is an input file to **File Lines**.

The best text editor is now another piece of great freeware: _**Notepad++**_. Download it from here:  
[_**Notepad++ text and programmer's editor**_](https://notepad-plus-plus.org/).

-   You should install **Notepad++** in one of the two locations to easily work with it:
-   **C:\\Program Files (x86)\\Notepad++\\notepad++** — for 64-bit Windows, or –
-   **C:\\Program Files\\Notepad++\\notepad++** — for 32-bit Windows

It makes it very easy to select a column. The software calls the operation _selecting a rectangular bloc of text_. You hold down the _Alt_ key, then click at the beginning of the block of text, then move the mouse pointer to the end of the rectangular bloc of text. Here is how I do it with a real strategy file:

![Select columns in lottery strategy report files with Notepad++ editor to sort them easily.](https://saliu.com/ScreenImgs/column-select.gif)

After I selected the column, I press _Ctrl+C_ to copy the rectangular bloc, then _Ctrl+N_ to open a new file, then _Ctrl+V_ to paste the bloc of text, and finally _Ctrl+S_ to save the file of indices.

## <u>4. Advanced tips on creating lotto, lottery strategy files, strategies</u>

The two text editors mentioned above can work well at creating pivot strategies. A pivot strategy consists of one filter as the key restriction. You can then add other filters to the pivot for a more restrictive strategy. Use the editors to select an entire column (including +/-) representing the pivot filter. Both editors have also a sort feature. _**QEdit**_: _Block_, then _Sort_; shortcut: _F3 function key_.

### <u>Important Update, February 2011</u>

I wrote specialized software to automate the process of sorting the filters (or columns, or blocks of text). The program name is **SortFilterReports**. The lotto software sorts the _W5_, _MD5_, _GR5_, _DE5_, _FE5_, _SK5_ reports by column, helping the user see more easily the filters — e.g. filters of wacky values. The sorting is done by type of winning reports. The lotto program also offers the correct choices (correct names) for filters to sort. Similar programs handle other lotto formats, especially the 6-number lotto game — components of the **Bright & Ultimate** software suites.

![Lottery software sorts numeric columns in ascending or descending order to discover strategies.](https://saliu.com/ScreenImgs/sort-lottery-reports.gif)

Indubitably, the best sorting software in the world is **Sorting**. It can sort text files and also numeric files _by column_. Options: _S = Sort Files as Text Lines_ and _C = Sort Columns (Numeric & Text)_.

![The best sorting software for lottery data, results files is named Sorting.](https://saliu.com/ScreenImgs/sort-column.gif)

If you sort in ascending order, the pivot filter will start at 0 (usually) and end with the largest value the filter has taken. If you want to use the filter as the _maximum_, select a range of values starting at 0. If there are more than 2 zeroes in 100 draws, then a pivot strategy would be _Max\_Filter=1_.

If you want to use the lottery filter as the _minimum_, select a range of values starting at the bottom of the column. If there are more than 2 higher in 100 draws, then a pivot strategy would be _Min\_Filter = High\_Value_.

If you want to use **both** the _minimum_ and the _maximum_ levels of a lottery filter, go somewhere to the median zone of the sorted column. For example, a value in that area that repeats: 50. Set _min\_Filter = 50_ AND _Max\_Filter = 51_. It is a very tight filter setting. IF such filter setting does not yield any lotto combinations, look for another one; e.g. 51.

You can be more restrictive and select values that only occurred once in 1000 drawings. That's for the patient type. Such restrictive pivot strategies may not generate a combination in many runs.

The pivot strategy will show how many other filters fared in the same lottery drawings. Several filters can be added to the key filter. The combined strategy will be much more powerful.

People do not want to share their lotto strategies. You don't, either. I have received numerous lottery strategies by email. I have been given permission to publish only the weak strategies, in most cases. If the author believes that the lottery strategies are really good, they are for my eyes only! Some people send me good lottery strategies because I am a fair guy. I reward the authors of good lottery / gambling strategies, or ideas of good strategies.

The main goal of those who appear willing to share lottery strategies is to get something much better in exchange. Usually, the detection of _triggers_ is the most sought-after item. That's the best guarded secret!

## [<u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>](https://saliu.com/content/lottery.html)

Click to visit a comprehensive directory of pages and materials deeply dedicated to: Lottery, software, strategies, systems, lotto wheels.

-   The Main [_**Lotto, Lottery, Software, Strategies**_](https://saliu.com/LottoWin.htm) Page.  
    Presenting software to create free winning lotto, lottery strategies, systems based on mathematics.
-   [_**Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies**_](https://saliu.com/Newsgroups.htm).  
    Read a genuine analysis of Excel spreadsheets applied to lottery and lotto developing of software, systems, and strategies.
-   [_**User's Guide to**_ **MDIEditor And Lotto WE**](https://saliu.com/MDI-lotto-guide.html).  
    ~ Also applicable to LotWon lottery, lotto software; plus Powerball, Mega Millions, Euromillions.
-   [_**Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps, Programs**_](https://saliu.com/forum/lotto-book.html).
    
    _**Pages dedicated to help, instructions, filters, strategies for the best lotto programs and lottery software in the world:**_
    
-   [_**Documentation, Help: MDIEditor Lotto WE, Lottery Software, Strategy Tutorial**_](https://saliu.com/mdi_lotto.html).
-   [_**MDI Editor Lotto**_ Is the Best Lotto Lottery Software; You Be Judge](https://saliu.com/bbs/messages/623.html).
-   [_**Filters, Restrictions, Elimination, Reduction in Lotto, Lottery Software**_](https://saliu.com/bbs/messages/42.html).
-   [_**Step-By-Step Guide to Lotto, Lottery Filters in Software**_](https://saliu.com/bbs/messages/569.html).
-   [_**Basic Manual for Lotto Software, Lottery Software**_](https://saliu.com/bbs/messages/818.html).
-   [**Vertical or Positional** _**Filters In Lottery Software**_](https://saliu.com/bbs/messages/838.html).
-   [_**Beginner's Basic Steps to**_ **LotWon** _**Lottery Software, Lotto Software**_](https://saliu.com/bbs/messages/896.html).
-   [**Dynamic** or **Static** _**Filters: Lottery Software, Lotto Analysis, Mathematics**_](https://saliu.com/bbs/messages/919.html).
-   [**Skips Lottery, Lotto, Gambling, Systems, Strategy**](https://saliu.com/skip-strategy.html)
-   [_**Lottery Systems on Skips Improve Lotto Odds Sevenfold**_](https://saliu.com/bbs/messages/923.html).
-   [_**Lottery Utility Software**_](https://saliu.com/lottery-utility.html): _**Pick-3, 4 Lottery, Lotto-5, 6, Powerball/Mega Millions/Thunderball, Euromillions**_.
-   [_**Lottery Strategy, Systems Based on Number Frequency**_](https://saliu.com/frequency-lottery.html)
-   Practical [_**Lottery and Lotto Filtering in Software**_](https://saliu.com/filters.html).
-   [_**Lotto, Lottery <u>Strategy in Reverse</u>: Not-to-Win Leads to Not-to-Lose or WIN**_](https://saliu.com/reverse-strategy.html).
-   [**Lotto Decades**: _**Software, Reports, Analysis, Strategies**_](https://saliu.com/decades.html)
-   [_**Analysis of Best Ranges for Lotto Number Frequency, Lottery Pairs, Pairings**_](https://saliu.com/lottery-lotto-pairs.html).
-   [_**Lotto Software for Groups of Numbers: Odd, Even, Low, High, Sums, Frequencies, User's Groups**_](https://saliu.com/lotto-groups.html).
-   [_**Theory, Analysis of**_ **Deltas** _**in Lotto, Lottery Software, Strategy, Systems**_](https://saliu.com/delta-lotto-software.html).
-   [_**The Best Strategy for Lottery, Gambling, Sports Betting, Horse Racing, Blackjack, Roulette**_](https://saliu.com/strategy-gambling-lottery.html).
-   [_**Pick-3 Lottery Strategy Software, System, Method, Play Pairs Last 100 Draws**_](https://saliu.com/STR30.htm).
-   _"The Start Is the Hardest Part"_: [_**Play a Lotto Strategy, Lotto Strategies**_](https://forums.saliu.com/lottery-strategies-start.html).
-   _**Download**_ [**<u>Lottery Strategy Software</u>**](https://saliu.com/infodown.html), _**Systems, Strategies**_, including updates and upgrades.

![Special software checks lottery strategies between several programs, apps.](https://saliu.com/HLINE.gif)

**[Home](https://saliu.com/index.htm) | [Search](https://saliu.com/Search.htm) | [New Writings](https://saliu.com/bbs/index.html) | [Fundamental Formula](https://saliu.com/formula.htm) | [Odds, Generator](https://saliu.com/calculator_generator.html) | [Contents](https://saliu.com/content/index.html) | [Forums](https://forums.saliu.com/) | [Sitemap](https://saliu.com/sitemap/index.html)**

![The lottery cannot be played successfully without software that creates lottery strategies.](https://saliu.com/HLINE.gif)
