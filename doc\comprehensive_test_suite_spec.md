# 綜合測試套件規格書

## 概述

本文件定義了 Julia 彩票系統的綜合測試套件，整合所有已驗證的計算邏輯，並提供完整的測試框架以確保系統的可靠性和準確性。

## 1. 測試架構設計

### 1.1 測試層級結構

```julia
# 測試套件主結構
struct ComprehensiveTestSuite
    unit_tests::Vector{UnitTest}
    integration_tests::Vector{IntegrationTest}
    validation_tests::Vector{ValidationTest}
    performance_tests::Vector{PerformanceTest}
    regression_tests::Vector{RegressionTest}
end

# 測試結果結構
struct TestResult
    test_name::String
    status::TestStatus
    execution_time::Float64
    error_message::Union{String, Nothing}
    details::Dict{String, Any}
end

@enum TestStatus begin
    PASSED
    FAILED
    SKIPPED
    ERROR
end
```

### 1.2 測試配置

```julia
# 測試配置結構
struct TestConfiguration
    test_data_path::String
    output_directory::String
    parallel_execution::Bool
    verbose_output::Bool
    performance_benchmarks::Dict{String, Float64}
    
    TestConfiguration() = new(
        "test/data",
        "test/results",
        true,
        false,
        Dict("skip_calculation" => 0.001, "ffg_calculation" => 0.005)
    )
end
```

## 2. 單元測試規格

### 2.1 Skip 計算測試

```julia
module SkipCalculationTests

using Test
include("../src/skip_analyzer.jl")

"""
測試 Skip 計算的準確性和一致性
"""
function test_skip_calculation_accuracy()
    @testset "Skip Calculation Accuracy" begin
        # 測試 SkipSystem 方法
        test_data = create_test_lottery_data()
        analyzer = SkipAnalyzer(test_data)
        
        # 測試已知結果
        @test get_current_skip(analyzer, 1) == expected_skip_value
        @test calculate_skips(analyzer, 1) == expected_skip_sequence
        
        # 測試邊界條件
        @test get_current_skip(analyzer, 999) == length(test_data)  # 從未出現的號碼
        
        # 測試一致性
        for number in 1:39
            skips = calculate_skips(analyzer, number)
            @test all(skip >= 0 for skip in skips)  # 所有 skip 值應為非負
        end
    end
end

"""
測試 Skip 計算方法的比較
"""
function test_skip_method_comparison()
    @testset "Skip Method Comparison" begin
        test_data = create_test_lottery_data()
        analyzer = SkipAnalyzer(test_data)
        
        for number in 1:10  # 測試前10個號碼
            skip_system = calculate_skips(analyzer, number)
            mdi_system = calculate_skips_mdi_method(analyzer, number)
            
            # 驗證關係：SkipSystem = MDIEditor + 1（對於歷史 skip）
            if length(skip_system) > 1 && length(mdi_system) > 1
                for i in 2:min(length(skip_system), length(mdi_system))
                    @test skip_system[i] == mdi_system[i] + 1
                end
            end
        end
    end
end

end  # module SkipCalculationTests
```

### 2.2 FFG 計算測試

```julia
module FFGCalculationTests

using Test
include("../src/ffg_calculator.jl")

"""
測試 FFG 中位數計算的準確性
"""
function test_ffg_median_accuracy()
    @testset "FFG Median Calculation" begin
        calc = FFGCalculator(0.5)  # 50% 確定性程度
        
        # 測試理論計算
        theoretical_median = calculate_theoretical_ffg_median(calc)
        expected_median = log(0.5) / log(1 - 5/39)  # 手動計算
        @test abs(theoretical_median - expected_median) < 0.001
        
        # 測試不同 DC 值
        dc_values = [0.25, 0.5, 0.75, 0.9]
        medians = Float64[]
        
        for dc in dc_values
            calc_dc = FFGCalculator(dc)
            median = calculate_theoretical_ffg_median(calc_dc)
            push!(medians, median)
        end
        
        # 驗證 DC 值越高，中位數越大（更保守）
        for i in 2:length(medians)
            @test medians[i] >= medians[i-1] * 0.8  # 允許一些容差
        end
    end
end

"""
測試 FFG 與歷史數據的整合
"""
function test_ffg_historical_integration()
    @testset "FFG Historical Integration" begin
        test_data = create_test_lottery_data()
        calc = FFGCalculator()
        
        for number in [1, 5, 10, 15, 20]  # 測試樣本號碼
            ffg_median = calculate_ffg_median(calc, number, test_data)
            
            # FFG 中位數應該是正數
            @test ffg_median > 0
            
            # 測試有利時機判斷
            current_skip = get_current_skip_for_number(test_data, number)
            is_favorable = is_favorable_timing(calc, number, current_skip, test_data)
            @test isa(is_favorable, Bool)
        end
    end
end

end  # module FFGCalculationTests
```

### 2.3 配對頻率測試

```julia
module PairingFrequencyTests

using Test
include("../src/pairing_engine.jl")

"""
測試配對頻率計算的準確性
"""
function test_pairing_frequency_accuracy()
    @testset "Pairing Frequency Accuracy" begin
        test_data = create_known_pairing_data()
        engine = PairingEngine(test_data)
        
        # 測試已知配對頻率
        @test get_pairing_frequency(engine, 1, 2) == expected_pair_12_frequency
        @test get_pairing_frequency(engine, 5, 10) == expected_pair_5_10_frequency
        
        # 測試總配對數量
        @test engine.total_pairings == expected_total_pairings
        
        # 測試配對分佈
        distribution = analyze_pairing_distribution(engine)
        @test distribution["total_pairs"] == 741  # C(39,2)
        @test distribution["min_frequency"] >= 0
        @test distribution["max_frequency"] >= distribution["min_frequency"]
    end
end

"""
測試 Wonder Grid 生成
"""
function test_wonder_grid_generation()
    @testset "Wonder Grid Generation" begin
        test_data = create_test_lottery_data()
        engine = PairingEngine(test_data)
        
        wonder_grid = generate_wonder_grid(engine)
        
        # 驗證結構
        @test length(wonder_grid) == 39
        
        for number in 1:39
            top_pairings = wonder_grid[number]
            @test isa(top_pairings, Vector{Int})
            @test length(top_pairings) <= 38  # 最多38個其他號碼
            @test all(1 <= p <= 39 for p in top_pairings)
            @test all(p != number for p in top_pairings)  # 不包含自己
        end
    end
end

end  # module PairingFrequencyTests
```

## 3. 整合測試規格

### 3.1 系統整合測試

```julia
module SystemIntegrationTests

"""
測試完整的彩票分析流程
"""
function test_complete_analysis_workflow()
    @testset "Complete Analysis Workflow" begin
        # 載入測試數據
        test_data = load_test_data("test/data/sample_lottery_data.csv")
        
        # 初始化所有組件
        skip_analyzer = SkipAnalyzer(test_data)
        ffg_calculator = FFGCalculator()
        pairing_engine = PairingEngine(test_data)
        wonder_grid_engine = WonderGridEngine(ffg_calculator, skip_analyzer, pairing_engine, test_data)
        
        # 執行完整分析
        analysis_result = perform_complete_analysis(wonder_grid_engine)
        
        # 驗證結果結構
        @test haskey(analysis_result, "key_numbers")
        @test haskey(analysis_result, "skip_analysis")
        @test haskey(analysis_result, "ffg_analysis")
        @test haskey(analysis_result, "pairing_analysis")
        
        # 驗證結果合理性
        key_numbers = analysis_result["key_numbers"]
        @test length(key_numbers) > 0
        @test all(1 <= n <= 39 for n in key_numbers)
    end
end

"""
測試組件間的數據一致性
"""
function test_component_data_consistency()
    @testset "Component Data Consistency" begin
        test_data = create_test_lottery_data()
        
        # 創建多個組件實例
        skip_analyzer1 = SkipAnalyzer(test_data)
        skip_analyzer2 = SkipAnalyzer(test_data)
        
        # 驗證相同輸入產生相同輸出
        for number in 1:10
            @test get_current_skip(skip_analyzer1, number) == get_current_skip(skip_analyzer2, number)
            @test calculate_skips(skip_analyzer1, number) == calculate_skips(skip_analyzer2, number)
        end
    end
end

end  # module SystemIntegrationTests
```

## 4. 驗證測試規格

### 4.1 理論驗證測試

```julia
module TheoreticalValidationTests

"""
與 Ion Saliu 原始理論的數值比較
"""
function test_saliu_theory_compliance()
    @testset "Saliu Theory Compliance" begin
        # 測試 FFG 理論一致性
        test_ffg_theory_consistency()
        
        # 測試 Skip 計算一致性
        test_skip_calculation_consistency()
        
        # 測試配對理論一致性
        test_pairing_theory_consistency()
    end
end

"""
歷史數據回測驗證
"""
function test_historical_backtest()
    @testset "Historical Backtest" begin
        historical_data = load_historical_data("test/data/historical_lottery_data.csv")
        
        # 分割數據：訓練集和測試集
        train_data = historical_data[1:end-100]
        test_data = historical_data[end-99:end]
        
        # 使用訓練數據建立模型
        model = build_analysis_model(train_data)
        
        # 在測試數據上驗證
        predictions = make_predictions(model, test_data)
        accuracy = evaluate_predictions(predictions, test_data)
        
        # 驗證準確性閾值
        @test accuracy["hit_rate"] >= minimum_acceptable_hit_rate
        @test accuracy["efficiency"] >= minimum_acceptable_efficiency
    end
end

end  # module TheoreticalValidationTests
```

## 5. 性能測試規格

### 5.1 性能基準測試

```julia
module PerformanceTests

using BenchmarkTools

"""
測試計算性能
"""
function test_calculation_performance()
    @testset "Calculation Performance" begin
        large_dataset = create_large_test_dataset(10000)  # 10000 筆數據
        
        # Skip 計算性能
        skip_analyzer = SkipAnalyzer(large_dataset)
        @benchmark get_current_skip($skip_analyzer, 1)
        
        # FFG 計算性能
        ffg_calculator = FFGCalculator()
        @benchmark calculate_ffg_median($ffg_calculator, 1, $large_dataset)
        
        # 配對計算性能
        pairing_engine = PairingEngine(large_dataset)
        @benchmark get_pairing_frequency($pairing_engine, 1, 2)
    end
end

"""
測試記憶體使用效率
"""
function test_memory_efficiency()
    @testset "Memory Efficiency" begin
        # 測試大數據集的記憶體使用
        large_dataset = create_large_test_dataset(50000)
        
        initial_memory = Base.gc_bytes()
        
        # 執行分析
        analyzer = SkipAnalyzer(large_dataset)
        for number in 1:39
            calculate_skips(analyzer, number)
        end
        
        final_memory = Base.gc_bytes()
        memory_usage = final_memory - initial_memory
        
        # 驗證記憶體使用在合理範圍內
        @test memory_usage < maximum_acceptable_memory_usage
    end
end

end  # module PerformanceTests
```

## 6. 測試執行框架

### 6.1 測試執行器

```julia
"""
綜合測試套件執行器
"""
function run_comprehensive_test_suite(config::TestConfiguration = TestConfiguration())
    println("🚀 開始執行綜合測試套件...")
    
    test_results = TestResult[]
    start_time = time()
    
    try
        # 執行單元測試
        println("📋 執行單元測試...")
        unit_results = run_unit_tests(config)
        append!(test_results, unit_results)
        
        # 執行整合測試
        println("🔗 執行整合測試...")
        integration_results = run_integration_tests(config)
        append!(test_results, integration_results)
        
        # 執行驗證測試
        println("✅ 執行驗證測試...")
        validation_results = run_validation_tests(config)
        append!(test_results, validation_results)
        
        # 執行性能測試
        println("⚡ 執行性能測試...")
        performance_results = run_performance_tests(config)
        append!(test_results, performance_results)
        
    catch e
        println("❌ 測試執行過程中發生錯誤: $e")
    end
    
    total_time = time() - start_time
    
    # 生成測試報告
    generate_test_report(test_results, total_time, config)
    
    return test_results
end
```

### 6.2 測試報告生成

```julia
"""
生成詳細的測試報告
"""
function generate_test_report(results::Vector{TestResult}, total_time::Float64, config::TestConfiguration)
    report_path = joinpath(config.output_directory, "test_report_$(Dates.format(now(), "yyyymmdd_HHMMSS")).html")
    
    # 統計結果
    passed = count(r -> r.status == PASSED, results)
    failed = count(r -> r.status == FAILED, results)
    errors = count(r -> r.status == ERROR, results)
    skipped = count(r -> r.status == SKIPPED, results)
    
    # 生成 HTML 報告
    html_content = generate_html_report(results, total_time, passed, failed, errors, skipped)
    
    # 寫入文件
    open(report_path, "w") do f
        write(f, html_content)
    end
    
    println("📊 測試報告已生成: $report_path")
    println("📈 測試結果統計:")
    println("   ✅ 通過: $passed")
    println("   ❌ 失敗: $failed") 
    println("   🚫 錯誤: $errors")
    println("   ⏭️  跳過: $skipped")
    println("   ⏱️  總時間: $(round(total_time, digits=2)) 秒")
end
```

## 7. 持續整合支援

### 7.1 自動化測試配置

```yaml
# .github/workflows/comprehensive_tests.yml
name: Comprehensive Test Suite

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v2
    
    - name: Setup Julia
      uses: julia-actions/setup-julia@v1
      with:
        version: '1.8'
    
    - name: Install dependencies
      run: julia --project -e 'using Pkg; Pkg.instantiate()'
    
    - name: Run comprehensive tests
      run: julia --project test/run_comprehensive_tests.jl
    
    - name: Upload test results
      uses: actions/upload-artifact@v2
      with:
        name: test-results
        path: test/results/
```

這個綜合測試套件規格提供了完整的測試框架，確保系統的可靠性和與 Ion Saliu 理論的一致性。
