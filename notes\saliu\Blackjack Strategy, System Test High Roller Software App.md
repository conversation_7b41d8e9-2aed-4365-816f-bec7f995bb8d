---
created: 2025-07-24T21:26:40 (UTC +08:00)
tags: [blackjack,mental blackjack system,basic strategy,software,3rd blackjack app,casino,dealer,player,odds,gambling,streaks,house advantage,Windows 10,]
source: https://saliu.com/blackjack-strategy-system-win.html
author: 
---

# Blackjack Strategy, System Test: High Roller Software App

> ## Excerpt
> Test Ion Saliu Mental Blackjack System to validate gambling streaks strategies with high roller blackjack software for big wins over casinos.

---
_"Winner, winner!_  
_Chicken dinner!"_  
(Chant attributed to a blackjack dealer in Las Vegas.)

Start: September 1, 2016  
End: September 6, 2016.

I wanted one more important **test** for my _**mental blackjack system**_, my _**gambling streaks theory**_, in general. Third-party software is the best way to go. I discovered possibly the best blackjack game for Windows 10: _**3rd Floor Blackjack**_. The application is totally free (subsidized by one non-intrusive ad). I downloaded it from the _Store_. The standalone game is also fast, responsive and easy-to-use.

The most important part: The blackjack app is not a product of some online casino. Usually, online gambling companies offer free blackjack software that is hugely biased toward the player. The player wins at sensational rates. The plot is to convince gamblers that they can win big — therefore convince them to open online accounts and gamble with real money. Of course, you heard the tune: The gambler loses the shirt off his back when playing with real money! I mean, gambling online with real money is a real disaster for the players!

_**3rd Floor Blackjack**_ is NOT the production of some Internet casino. I found the game to be fair. The software seems to apply the blackjack rules impartially. The company doesn't care who wins or who loses — it earns revenue from online advertising. Nobody can fake this gambling application. _"You can't fake steak!"_

I faithfully applied one of my free blackjack systems to this game: _**Shoe-Start Strategy**_: [_**The Best Blackjack Strategy:**_ **Shoe-Start System**](https://forums.saliu.com/card-counting-blackjack.html#shoe).

I applied it as in real-life casino conditions. They don't allow me to write Win(_W_)/Loss(_L_) in a notebook. So, I count in memory as in _**My Mental Blackjack System**_: [_**Blackjack System for Streaks Betting, Martingale Progressions**_](https://saliu.com/occult-science-gambling.html#GamblingSystem).

Apparently, _**3rd Floor Blackjack**_ uses a virtual _continuous shuffling machine_ (_CSM_). However, this _CSM_ is really fair. I strongly suspect that the _CSM_s in [_**real casinos are rigged (programmed to beat-to-bankruptcy blackjack players)**_](https://www.facebook.com/FreeSoftwareBlackjackRouletteLottery/posts/****************).

Thus, you are always at the start of a shoe. It doesn't really matter how many decks there are in the virtual shoe (_CSM_). I applied strictly my system, avoiding betting over my head. I encountered 2 streaks of 7 consecutive losses. I wept off the losses with 3 Martingale steps (at losses #5, #6, and #7). My longest winning streak was 5 (it happened 4 times). The ratio is what should be as calculated by my new software for blackjack odds. That is, the _house advantage (HA)_ is at least 7.5%. Don't get drunk with illusions and believe that HA is 0.5%... at worst!

The BJ app lets you take snapshots. I share one or two here. I started with $1000 and advanced to the highest level by winning certain amounts of hands. The lowest level is at $1 minimum bet, and $50 maximum limit. The highest level is at $50 minimum bet, and $5000 maximum bet. The player must advance from the lowest level to the highest by winning various amounts of hands. Of course, the player must not lose his/her initial bankroll!

I played a few hours over two days. I won over $2000. This is the best training one can get applying _**My Mental Blackjack System**_. I know, the real-life casino conditions are less favorable, with heat and pressure from the casino honchos. But this strategy can be still played with great success. **It is the only way to beat the game of blackjack.** There is nothing else — period.

I wrote down the outcomes as they occurred. It is NOT cheating, henchmen! I just added more information. If the loss was a dealer blackjack, I wrote _Lj_. If I won with a natural blackjack, I wrote _Wj_. In the case of bust, I scribbled _Lb_ or _Wb_. I recorded the pushes too (_P_).

I won over $1200 in less than 2 hours. And thusly I advanced to the **high-roller** level.

[![Casino Gambling Software: Blackjack, Roulette, Baccarat, Craps, Systems.](https://saliu.com/images/blackjack-highroller.gif)](https://saliu.com/free-casino-software.html)

I played again on another day: less than 2 hours. I won — again — more than $1200. Level of play: _High roller_ (50 minimum, 5000 maximum bet).

Total hands played: 104.  
Losses (_L_): 42  
Wins (_W_): 53  
Pushes (_P_): 9.

This session was biased toward me, the player. I recorded every hand. Again, **j** means _blackjack natural 21_, **b** is for _bust_, **d** is for _double-down_, **P** means _push_.

_Wj Wj Wd Lb Ld Wb L L W L L Wb L L P Wj W L W Wj W W W P Wd L W L L P L L P L P W Wb P W L L L W L P W Wd Wj L W Wj W Wb W Ld Wb Wbd L Lb Wj L L W W Wb Wb Ld Wb Ld Wb L L Ld Lb P Wb Wb W L L Lb Wb Wd Wb Wb W Wb Lj Lb W Wj Lb L L Lb Wb W W W P Lb Lb WB Wbd_

I played some 400 more hands. I reached the 10,000 level in chips. I think I have now a pretty good rundown.

-   1) The game was biased in my favor, the player. In good approximation, I won some 47% of the hands, the dealer won 46%, and the pushes were at 7%.
-   2) The bust ratio is pretty high on both sides in heads-up play. The casino (house) enjoys a tremendous advantage with several players at the table — they would prefer always 7 players at each table!
-   3) What turned the tide in my favor was _selective busting_. I avoided some situations that lead to busting (e.g. 15, 16 against dealer's _pat_ cards). I had good notes in front of me and saw when I busted with 15 or 16 before.
-   4) Of course, I won pretty big because I applied my _**gambling-streak theory**_. Yes, the Dealer had several 7-consecutive wins, but I wiped them out in limited-step martingaling. I did have one 7-win streak.

![The mental blackjack gambling system beat the casino and house advantage HA big time.](https://saliu.com/images/blackjack-winning-system.gif)

The main point here is the heads-up play can lead to win over the house. Also, the shoe-start works well for the player because of _natural 21_ and _double-down_ possibilities: [_**Blackjack Probability, Odds Natural, Insurance, Double Down**_](https://forums.saliu.com/blackjack-natural-odds-probability.html#double-down).

This blackjack app I present here employees a FAIR continuous shuffling machine (_CSM_). Meanwhile, I am afraid the _CSM_s used in the casinos are chip-programmed. Their software “knows” how to “mix” the cards so that the players ultimately lose. I've had first-hand experiences. I saw the dealer get too many 20 or 21 hands and bust way too little. I compared to manually-shuffled shoes with several players at the table (in the same casino).

I reached the **20,000** mark within one week's time. It is the snowball effect: You win more while building a bigger and bigger bankroll. You play with more and more confidence — applying the most mental gambling system with virtually no fear.

![Gambling streaks strategies beat unbiased blackjack app to win 20000 dollars in one week.](https://saliu.com/images/blackjack-winning-strategy.gif)

### Consistency

I left the game for a while. I came back to it after the stunning presidential election in the United States, November 8, 2016. I wanted to test the **consistency** of my blackjack strategy. I played for some three hours, doing my best to be... consistent (not betting above my head, given my advantage in chips). I won some additional 4,400 _dollars_, reaching above the 24,000-level in chips.

![Mental blackjack strategy system wins consistently thousands of dollars a day in heads-up play.](https://saliu.com/images/blackjack-winning-consistency.gif)

Then I played again in the first week of December 2016. I exceeded the $30,000 mark in a few hours' time of play. I even simulated a bathroom break!

![The best and free blackjack strategy beats the casino dealer with no doubt.](https://saliu.com/images/blackjack-highroller-strategy.gif)

### Verdict

No doubt, the _**shoe-start**_ represents the best situation for the blackjack player. And, absolutely undeniably, my _**mental blackjack system**_ based on streaks is the one and only way to win over the casino. The strategy is also consistent. _Heads-up_ play also improves the chance to win big over the casino.

Axiomatics, the real problem is: Do them casinos ALLOW me or you to play (even without a notebook)? That is the question…

I know the Las Vegas case. Sometimes they use brute force to kick you out. I wouldn't go there, to _Mafialand_. But even in _Trumpland_, _Home of the Bankrupt_, Atlantic City, the pit boss would engage the dealer in a long BS conversation… until I lose patience and leave the table… or until other players lose patience and turn hostile toward yours truly. Read: [_**Fearing Losses, Casinos Bar, Ban Winning Gamblers, Skilled Gambling Players**_](https://saliu.com/winning.html).

![How other players apply Ion Saliu's blackjack system based on gambling streaks.](https://saliu.com/HLINE.gif)

![There is a good and fair blackjack online app at Wizard of Odds site.](https://saliu.com/images/WizardofOddsBlackjack.jpg)

Probably a loyal follower of yours truly applies my blackjack gambling system... with a twist. That is, applying the streaks theory to card counting. There is truth to it, as he demonstrated at a site whose owner is quite hostile to yours truly. The self-proclaimed _"Wizard of Odds"_ has a forum and an online blackjack app. I tried the app myself. The BJ online generator is really capable and appears to be totally fair. I did win fairly easy a couple of thousand dollars.

Anyway, the respective player won at the rate of $1000 per hour applying my theory of gambling streaks. Not bad at all! Most of the winnings came, intriguingly, in <u>negative</u> counts! As you can see in the image, the count was -5 in a _double-down_ situation. There is a fundamental reason for that. Most _double-down_ hands come in negative counts, as the player pointed out. I demonstrated the fact several years ago.

-   [_**Card Counting Quirks?**_](https://wizardofvegas.com/forum/gambling/blackjack/39431-card-counting-quirks/2/#post934618)

-   <u>There is NO other way to win <big>big</big> at blackjack.</u> Keep in mind, the counting gurus promise only 1% gain — in the very long-run, millions, even billions, of hands. Let's say the counting strategy wins with playing just 1,000,000 dollars. Total gain: a meager 10,000! It’s not worth the high risk.
-   My _streak gambling strategy_ does involve risks as it requires a comfortable bankroll. However, the required bankroll is lower than in the case of card counting. Therefore, the risk with my system is lower, while potential gains are significantly higher. Lest we forget. _“Qui ne risque rien n'a rien.” “Wer nicht wagt, der nicht gewinnt.” “Nothing ventured, nothing gained. No pain, no gain.”_

![Players can really win at blackjack in negative counts because of more double-downs.](https://saliu.com/HLINE.gif)

## [<u>Blackjack: Software, Content, Resources, Systems, Basic Strategy, Card Counting</u>](https://saliu.com/content/blackjack.html)

See above: The comprehensive directory of the pages and materials on the subject of blackjack, baccarat, software, systems, and basic strategy.

-   [<u><b>Blackjack</b></u>: _**Basic Strategy, Card Counting, Charts, Probability, Odds, Software**_](https://saliu.com/blackjack.html).
    
    For the best mathematical casino gambling strategy (_approach_) for _non-system_ players, read:
    
-   [_**Casinos, Gambling, Win in Casino with Mathematical Systems**_](https://saliu.com/keywords/casino.htm).
-   [_**The Best Strategy for Lottery, Gambling, Sports Betting, Horse Racing, Blackjack, Roulette**_](https://saliu.com/strategy-gambling-lottery.html).
-   [**_The Best Blackjack Basic Strategy: Free Cards, Charts_**](https://saliu.com/bbs/messages/399.html).  
    ~ All playing decisions on one page — absolutely the best method of learning _Blackjack Basic Strategy (BBS)_ quickly (guaranteed and also free!)
-   [_**Gambling Mathematics in Blackjack Proves Deception of Card-Counting Systems**_](https://saliu.com/bbs/messages/274.html).
-   [_**Calculate Blackjack Probability, Odds: Natural 21, Insurance, Double-Down Hands, Pairs**_](https://forums.saliu.com/blackjack-natural-odds-probability.html).
-   [_**New Casino Games by Ion Saliu:**_ **Parpaluck Blackjack**](https://forums.saliu.com/new-blackjack-game.html).
-   [_**<u>ABC</u>: The Best Blackjack Card-Counting System by Ion Saliu**_](https://forums.saliu.com/card-counting-blackjack.html#abc-system) — the only system founded on mathematics and formulas.
-   _**Download Blackjack, Gambling**_ [**Software**](https://saliu.com/infodown.html).

![This is the blackjack site for all humans plus casino gamblers with the goal to win big-time.](https://saliu.com/HLINE.gif)

**[Home](https://saliu.com/index.htm) | [Search](https://saliu.com/Search.htm) | [New Writings](https://saliu.com/bbs/index.html) | [Fundamental Formula](https://saliu.com/formula.htm) | [Odds, Generator](https://saliu.com/calculator_generator.html) | [Contents](https://saliu.com/content/index.html) | [Forums](https://forums.saliu.com/) | [Sitemap](https://saliu.com/sitemap/index.html)**

![Learn correct blackjack odds, house edge, advantage, mathematical calculations by accurate program.](https://saliu.com/HLINE.gif)
