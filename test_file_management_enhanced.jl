include("src/WonderGridLotterySystem.jl")
using .WonderGridLotterySystem

println("Testing Enhanced File Management System")
println("=" ^ 50)

# Test file accessibility checking
fm = FileManager()
println("Testing file accessibility...")
result = check_file_accessibility(fm, "data/fan5.csv")
println("File accessibility: $(result.is_valid) - $(result.message)")

# Test file information gathering
println("\nTesting file information...")
info = get_file_info(fm, "data/fan5.csv")
println("File info:")
for (key, value) in info
    if key == "sample_lines"
        println("  $key: $(length(value)) lines")
        for (i, line) in enumerate(value)
            println("    Line $i: $(line[1:min(50, length(line))])...")
        end
    else
        println("  $key: $value")
    end
end

# Test reading raw data (without dates)
println("\nTesting raw data reading...")
raw_data = read_data5_raw(fm, "data/fan5.csv")
println("Successfully read $(length(raw_data)) raw lottery draws")
println("First draw: $(raw_data[1])")
println("Last draw: $(raw_data[end])")

# Test different file format functions
println("\nTesting different file format functions...")

# Create test data
test_draws = raw_data[1:20]

# Test SIM-5 format
sim_file = "test_sim5.csv"
write_sim5_file(fm, sim_file, test_draws)
sim_data = read_sim5_file(fm, sim_file)
println("SIM-5 format: wrote $(length(test_draws)), read $(length(sim_data)) draws")

# Test D5 format
d5_file = "test_d5.csv"
write_d5_file(fm, d5_file, test_draws)
d5_data = read_d5_file(fm, d5_file)
println("D5 format: wrote $(length(test_draws)), read $(length(d5_data)) draws")

# Test file backup
println("\nTesting file backup...")
backup_path = backup_file(fm, sim_file)
println("Created backup: $backup_path")

# Test backup cleanup
println("Testing backup cleanup...")
# Create multiple backups
for i in 1:3
    sleep(0.1)  # Small delay to ensure different timestamps
    backup_file(fm, sim_file)
end

removed_count = cleanup_backups(fm, sim_file, 2)  # Keep only 2 backups
println("Removed $removed_count old backup files")

# Test large file handling
println("\nTesting large file handling...")
large_data = generate_test_data(50000)
large_file = "test_large.csv"

start_time = time()
write_data5_file(fm, large_file, [draw.numbers for draw in large_data])
write_time = time() - start_time

start_time = time()
read_large_data = read_data5_raw(fm, large_file)
read_time = time() - start_time

println("Large file performance:")
println("  Write $(length(large_data)) draws: $(round(write_time, digits=3)) seconds")
println("  Read $(length(read_large_data)) draws: $(round(read_time, digits=3)) seconds")
println("  Data integrity: $(length(large_data) == length(read_large_data) ? "PASSED" : "FAILED")")

# Test error handling
println("\nTesting error handling...")
try
    check_file_accessibility(fm, "nonexistent_file.csv")
    println("Non-existent file check: handled gracefully")
catch e
    println("Error handling test failed: $e")
end

# Clean up test files
println("\nCleaning up test files...")
test_files = [sim_file, d5_file, large_file]
for file in test_files
    if isfile(file)
        rm(file, force=true)
        println("  Removed: $file")
    end
end

# Clean up remaining backup files
try
    cleanup_backups(fm, sim_file, 0)  # Remove all backups
    println("  Cleaned up backup files")
catch
    # Ignore errors if no backups exist
end

println("\nFile Management System Test Complete!")