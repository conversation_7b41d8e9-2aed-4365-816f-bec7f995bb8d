**PairGrid (配對網格)** 是一種在 Ion Saliu 彩票軟體（特別是 **Bright** 和 **Ultimate Software** 套件）中使用的程式，其主要功能是生成配對報告和自訂網格，並能根據號碼的頻率來生成樂透組合。這個工具與「**Wonder Grid**」策略緊密相關，該策略旨在透過分析號碼配對頻率來優化樂透投注。

以下是關於 PairGrid 和 Wonder Grid 策略的詳細實現細節：

### Wonder Grid 策略基礎

Wonder Grid 策略基於兩個核心要素：

1. **選擇關鍵（或最愛）號碼**：這部分遵循「**賭博基本公式 (FFG)**」的原理，該公式指出任何樂透號碼在抽獎次數少於或等於其機率中位數後，有至少 50% 的機率重複出現。因此，選擇一個符合此條件的號碼作為「關鍵號碼」是第一步。
2. **分析配對頻率**：策略的第二部分基於樂透號碼之間的配對趨勢。在一定範圍的抽獎中，每個樂透號碼都傾向於與其他特定號碼一起被抽出，形成比其他號碼對更頻繁出現的配對。

### 配對頻率分析

資料顯示，配對的頻率分佈具有特定模式：

- **前 10% 的配對**：佔每個號碼總頻率的 25%。
- **前 25% 的配對**：佔每個號碼總頻率的 50%。
- **前 50% 的配對**：佔每個號碼總頻率的 75%。
- **後 10% 的配對**：總和為零。 Wonder Grid 策略建議只使用關鍵號碼的「**前 25% 配對**」來進行投注，因為這些配對佔了該號碼全部配對頻率的一半。

### 軟體實現與相關工具

多款 Ion Saliu 的樂透軟體都支援 Wonder Grid 策略及其相關功能：

- **Super Utilities (超級工具)**：
    
    - 用於計算每個樂透號碼所有配對的頻率，並可生成報告 (功能 `F = Frequency Reports by Lotto Number` 和 `2 = Lotto Pairs Rundown`)。
    - 能夠創建 `BEST6` 檔案（包含最常配對的號碼組）和 `WORST6` 檔案（包含最不常配對的號碼組），這些檔案可用於過濾。
    - 其 `M = Make / Break / Position` 功能可以自動創建「**lotto wonder grid**」檔案。例如，對於 6/49 樂透遊戲，這會生成一個 49 行的檔案，每行以一個樂透號碼開頭，後面跟著其最頻繁的 5 個配對。
    - `Break5` 選項會將關鍵號碼固定在組合的第一個位置，然後將其餘配對號碼組合成 5 個號碼的組合。
    - `Break6` 選項則會將一行中的所有數字平均分配，生成更多組合，從而提高中獎機率。
- **MDIEditor Lotto WE**：
    
    - 這款綜合性軟體的統計模組會創建「**wonder-grid**」檔案，顯示遊戲中的每個號碼及其最頻繁的配對。
    - 用於處理大型資料檔案（建議至少 10,000 行），以確保篩選器值的準確性，這通常需要結合真實抽獎資料和模擬資料 `SIM` 檔案來構建 `D*` 檔案。
- **PairGrid (程式名稱：PairGrid6 / PairGridH3)**：
    
    - 是 **Bright** 和 **Ultimate Software** 套件中的一個專用程式，用於產生配對報告和自訂網格。
    - 具有頻率分析模組，並能根據頻率生成樂透組合。
    - **PairGrid** 和 **Super Utilities** 都可以生成包含「**固定位置最愛號碼**」的樂透組合。
- **Markov Chains Software (例如：MarkovLotto6)**：
    
    - 生成按頻率從熱到冷排序的配對列表 (`MarkovPairsPivL6` 和 `MarkovPairsNoPL6`)。
    - 這些程式中的功能 (`P` 和 `N`) 可以從這些配對列表中生成組合，這些組合也是「**LIE 消除**」功能的良好候選者。
    - 馬可夫鏈理論被認為是「lotto wonder grid」的子集。
- **其他相關程式和功能**：
    
    - **Bright6** (最全面的 6 號樂透軟體套件) 也包含了 Wonder Grid 的主要組件。
    - **LottoGroupSkips6** 也能生成包含配對在內的數字子組的跳躍報告。
    - **Cluster49-7** (用於 6/49 樂透的 7x7 群組生成) 也適用於配對過濾器。
    - 組合生成器（如 `Combine6` 和 `Lexico6`）可以強制所有組合包含前 N% 的配對，同時過濾掉後 M% 的配對。

### 效率與效益

Wonder Grid 策略在針對高額獎金時比隨機投注更有效。例如：

- 對於 6/49 樂透遊戲，使用關鍵號碼及其前 5 個配對來贏得 **6 中 6 頭獎** 的機率比隨機投注高出約 **1669 倍**。即使前 5 個配對的組合頻率低於 25%，Wonder Grid 的效率仍然遠優於隨機投注。
- 對於 **5 中 6** 的獎金，效率比隨機投注高出約 **26 倍**。
- 對於 **4 中 6** 的獎金，效率比隨機投注高出近 **2 倍**。
- 對於 **3 中 6** 的獎金，Wonder Grid 表現可能不如隨機投注，但其優勢在於瞄準更高額的獎金。

### 與其他策略的整合

- **LIE 消除 (反向策略)**：Wonder Grid 策略生成的許多組合（特別是那些僅基於最頻繁配對的組合）在接下來的抽獎中通常不會中獎。這些「預計不會中獎」的組合可以作為「**LIE 檔案**」被生成並用於篩選，從而減少要投注的組合數量，節省成本。
- **Purge (清除)**：生成大量組合後，可以使用 `Purge` 功能進一步減少要投注的票數，透過應用額外的篩選器來淘汰不想要的組合。

### 注意事項

- **分析範圍**：為了獲得最佳效果，建議的分析範圍應為樂透遊戲最大號碼的三倍（例如，6/49 遊戲約為 147 次抽獎）。
- **資料檔案大小**：統計報告（包括 Wonder Grid）需要非常大的資料檔案才能精確計算篩選器值，通常建議包含數十萬甚至數百萬的組合（結合真實抽獎和模擬資料）。若資料檔案太小，某些篩選器（如 `Ion5`）的值可能會重複出現異常高的數字，這不應被視為可靠的最大級別。
- **固定位置與最愛號碼**：PairGrid 等軟體支援生成包含用戶選擇的「最愛號碼」在「固定位置」的組合，這些最愛號碼通常基於頻率分析，有助於大幅減少組合數量。

總之，PairGrid 和 Wonder Grid 策略是 Ion Saliu 樂透軟體系統中的強大工具，透過精確的配對頻率分析和與其他過濾器（特別是 LIE 消除）的結合，它能夠大幅提高中高獎項的中獎機率，同時優化投注組合，降低成本。