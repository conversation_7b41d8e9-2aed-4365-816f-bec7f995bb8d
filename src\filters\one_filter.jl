# ONE Filter Implementation
# ONE 過濾器實現 - 分析單個號碼的出現模式

using Dates
using Statistics

# 引入必要的模組
include("../types.jl")
include("../skip_analyzer.jl")
include("../ffg_calculator.jl")
include("../filter_engine.jl")

"""
計算號碼在歷史數據中的出現次數
"""
function count_number_occurrences(engine::FilterEngine, number::Int)::Int
    if !(1 <= number <= 39)
        throw(ArgumentError("號碼必須在 1-39 範圍內，得到: $(number)"))
    end
    
    count = 0
    for draw in engine.historical_data
        if number in draw.numbers
            count += 1
        end
    end
    
    return count
end

"""
計算號碼出現的歷史頻率
"""
function calculate_occurrence_frequency(engine::FilterEngine, number::Int)::Float64
    if isempty(engine.historical_data)
        return 0.0
    end
    
    occurrences = count_number_occurrences(engine, number)
    return occurrences / length(engine.historical_data)
end

"""
計算號碼的理論出現機率
對於 Lotto 5/39，每個號碼的理論機率是 5/39
"""
function calculate_theoretical_probability(number::Int)::Float64
    return 5.0 / 39.0  # Lotto 5/39 的理論機率
end

"""
計算號碼的信心水準
基於歷史頻率與理論機率的比較，以及樣本大小
"""
function calculate_confidence_level(engine::FilterEngine, number::Int, current_skip::Int)::Float64
    if isempty(engine.historical_data)
        return 0.0
    end
    
    # 計算歷史頻率
    historical_freq = calculate_occurrence_frequency(engine, number)
    theoretical_prob = calculate_theoretical_probability(number)
    
    # 計算頻率偏差
    freq_deviation = abs(historical_freq - theoretical_prob) / theoretical_prob
    
    # 基於樣本大小的信心調整
    sample_size = length(engine.historical_data)
    sample_confidence = min(1.0, sample_size / 100.0)  # 100 筆數據達到基本信心
    
    # 基於當前 skip 的信心調整
    # 創建臨時的 skip analyzer 和 ffg calculator
    skip_analyzer = SkipAnalyzer(engine.historical_data)
    ffg_calculator = FFGCalculator()

    skip_confidence = try
        ffg_median = calculate_ffg_median(ffg_calculator, number, engine.historical_data)
        if current_skip <= ffg_median
            0.8 + 0.2 * (ffg_median - current_skip) / ffg_median  # 有利時機，高信心
        else
            0.6 - 0.3 * min(1.0, (current_skip - ffg_median) / ffg_median)  # 不利時機，低信心
        end
    catch
        0.5  # 如果計算失敗，使用中等信心
    end
    
    # 綜合信心水準
    base_confidence = 1.0 - min(0.5, freq_deviation)  # 頻率偏差越小，信心越高
    final_confidence = base_confidence * sample_confidence * skip_confidence
    
    return clamp(final_confidence, 0.0, 1.0)
end

"""
獲取號碼的歷史 skip 序列
"""
function get_historical_skips(engine::FilterEngine, number::Int)::Vector{Int}
    skip_analyzer = SkipAnalyzer(engine.historical_data)
    return calculate_skips(skip_analyzer, number)
end

"""
判斷當前時機是否有利
基於當前 skip 值與 FFG 中位數的比較
"""
function is_favorable_timing(engine::FilterEngine, number::Int, current_skip::Int)::Bool
    if isempty(engine.historical_data)
        return false
    end
    
    try
        ffg_calculator = FFGCalculator()
        ffg_median = calculate_ffg_median(ffg_calculator, number, engine.historical_data)
        
        # 當前 skip 小於等於 FFG 中位數時認為是有利時機
        return current_skip <= ffg_median
    catch
        # 如果計算失敗，使用保守策略
        return false
    end
end

"""
計算 ONE 過濾器結果
分析單個號碼的出現模式和當前狀態
"""
function calculate_one_filter(engine::FilterEngine, number::Int)::FilterResult
    start_time = time()
    
    if !(1 <= number <= 39)
        throw(ArgumentError("號碼必須在 1-39 範圍內，得到: $(number)"))
    end
    
    if isempty(engine.historical_data)
        throw(ArgumentError("歷史數據不能為空"))
    end
    
    # 檢查快取
    cache_key = "one_filter_$(number)"
    if engine.cache_enabled && haskey(engine.filter_cache, cache_key)
        cached_result = engine.filter_cache[cache_key]
        @info "使用快取結果: ONE 過濾器，號碼 $(number)"
        return cached_result
    end
    
    try
        # 計算當前 skip 值
        skip_analyzer = SkipAnalyzer(engine.historical_data)
        current_skip = get_current_skip(skip_analyzer, number)
        
        # 計算 FFG 中位數作為期望值
        ffg_calculator = FFGCalculator()
        expected_value = calculate_ffg_median(ffg_calculator, number, engine.historical_data)
        
        # 判斷是否有利
        is_favorable = is_favorable_timing(engine, number, current_skip)
        
        # 計算信心水準
        confidence = calculate_confidence_level(engine, number, current_skip)
        
        # 獲取歷史 skip 序列
        historical_skips = get_historical_skips(engine, number)
        
        # 計算執行時間
        calculation_time = time() - start_time
        
        # 創建結果
        result = FilterResult(
            "ONE_FILTER_$(number)",
            ONE_FILTER,
            current_skip,
            expected_value,
            is_favorable,
            confidence,
            historical_skips,
            calculation_time
        )
        
        # 儲存到快取
        if engine.cache_enabled
            engine.filter_cache[cache_key] = result
            manage_cache_size!(engine)
        end
        
        return result
        
    catch e
        @error "計算 ONE 過濾器時發生錯誤" number=number error=e
        rethrow(e)
    end
end

"""
批量計算多個號碼的 ONE 過濾器結果
"""
function calculate_one_filter_batch(engine::FilterEngine, numbers::Vector{Int})::Vector{FilterResult}
    results = FilterResult[]
    
    for number in numbers
        try
            result = calculate_one_filter(engine, number)
            push!(results, result)
        catch e
            @warn "跳過號碼 $(number)，計算失敗: $(e)"
        end
    end
    
    return results
end

"""
獲取所有號碼的 ONE 過濾器結果
"""
function calculate_all_one_filters(engine::FilterEngine)::Vector{FilterResult}
    return calculate_one_filter_batch(engine, collect(1:39))
end

"""
根據 ONE 過濾器結果篩選有利號碼
"""
function filter_favorable_numbers(results::Vector{FilterResult})::Vector{Int}
    favorable_numbers = Int[]
    
    for result in results
        if result.is_favorable && result.confidence_level >= 0.6
            # 從過濾器名稱中提取號碼
            number_str = split(result.filter_name, "_")[end]
            number = parse(Int, number_str)
            push!(favorable_numbers, number)
        end
    end
    
    return sort(favorable_numbers)
end

"""
獲取 ONE 過濾器的統計摘要
"""
function get_one_filter_summary(results::Vector{FilterResult})::Dict{String, Any}
    if isempty(results)
        return Dict("error" => "無結果數據")
    end
    
    favorable_count = count(r -> r.is_favorable, results)
    high_confidence_count = count(r -> r.confidence_level >= 0.7, results)
    
    confidence_levels = [r.confidence_level for r in results]
    current_skips = [r.current_value for r in results]
    expected_values = [r.expected_value for r in results]
    
    return Dict(
        "total_numbers" => length(results),
        "favorable_numbers" => favorable_count,
        "high_confidence_numbers" => high_confidence_count,
        "average_confidence" => mean(confidence_levels),
        "average_current_skip" => mean(current_skips),
        "average_expected_value" => mean(expected_values),
        "confidence_distribution" => Dict(
            "min" => minimum(confidence_levels),
            "max" => maximum(confidence_levels),
            "median" => median(confidence_levels)
        )
    )
end

# 導出函數
export calculate_one_filter, calculate_one_filter_batch, calculate_all_one_filters
export count_number_occurrences, calculate_confidence_level, is_favorable_timing
export filter_favorable_numbers, get_one_filter_summary
