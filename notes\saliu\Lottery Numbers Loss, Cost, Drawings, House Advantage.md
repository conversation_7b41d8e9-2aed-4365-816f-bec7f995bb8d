---
created: 2025-07-24T21:26:41 (UTC +08:00)
tags: [lottery,numbers,loss,drawings,draws,strategies,systems,winning the lottery,]
source: https://saliu.com/lottery-numbers-loss.html
author: 
---

# Lottery Numbers: Loss, Cost, Drawings, House Advantage

> ## Excerpt
> Playing random lottery numbers or favorite numbers guarantees substantial losses in the long run. Only lottery strategies, systems and good lottery software can win with consistency.

---
-   Axiomatic one, undoubtedly lottery is a **losing proposition** in the long perspective IF playing, as they advise, **randomly**. Even if playing a favorite number still ends up in a guaranteed loss.
-   A favorite number is actually a random number. The most famous favorites are the so-called _birthday numbers_. Other favorites include numbers considered to be _special_, or _magical_, or even _divine_. For example: _PI_ (3.141592653589....) or _PHI_ (the _golden number_ 0.6180339887498....). All these derived numbers are random and they appear with the same frequency as any lottery number, or set, or combination. See statistical reports: [_**Lottery Numbers from PI, PHI (Divine Proportion)**_](https://groups.google.com/forum/?hl=en#!topic/rec.gambling.lottery/07gM1VnVvFM) .
-   How much is to lose? We can calculate an **average loss** in lottery when playing a number of _drawings_ (known as _draws_ outside USA). The loss can be huge if a very large number of drawings are played with real money.
-   I analyzed a large amount of real-life results in the Pennsylvania Lottery in the _pick 3_ game. I knew well in advance the whereabouts of the results and data only confirmed my theory. Read: [_**Pick 3 <u>Straight</u> Sets Statistics**_](https://saliu.com/freeware/Pick3-Straight-Stats.html).
-   By contrast, around 45% of the roulette numbers lead the gamblers to profits in a few thousand spins. That is, with a sufficient bankroll, a player has a pretty good chance to make a profit, even if playing a random roulette number, or a favorite number. I analyzed about 8000 spins from _Hamburg Spielbank_ (casino). Quite a few numbers ended up making a profit: [_**roulette systems, magic numbers**_](https://download.saliu.com/roulette-systems.html).

The _average loss_, in lottery and casino gambling alike, is determined by <u>multiplying the <i>total cost</i> by the <i>house advantage</i> (<i>HA</i> or <i>house edge</i>)</u>.

The _cost_ consists of number of tickets played multiplied by the price per ticket.

To calculate the _house advantage (HA)_, we apply this simple formula based on _units paid UP_ over _total possibilities TP_:

**HA = 1 – (UP / TP)**  
(always expressed as a percentage)

For example, in the _pick 3_ game, they pay 500 units (e.g. dollars) for a straight win. Total straight sets (possibilities): 1000. _HA = 1 – (UP / TP) = HA = 1 – (500 / 1000) = 50%._ That's humongous! It's about 10 times worse than what the player faces in American roulette: 5.26%.

If one played 10,000 dollars (one pick-3 number in 10,000 lottery drawings), the average loss would amount to 5000 dollars. If playing, randomly, one _pick 3_ straight set in 100,000 lottery drawings, virtually every number (straight set) will end up very close to a 50,000-dollar loss.

I doubt there are 100,000 lottery drawings in any state lottery. But we can reduce to scale. Instead of 3 digits, we look at data for one digit only; e.g. the digit in the 1st position. In other words, we derived a new game, a _pick 1_ lottery game. Total possibilities are now _10_. To maintain the same _HA_, we assume the house pays _5_ units per win.

The data available for this game analysis is huge now: over 9000 drawings in my database (in Pennsylvania State Lottery). I run my one-of-a-kind statistical software known as **Frequency Rank**. I am interested only in the positional frequency, as this hypothetical game has only 1 digit. Here is the report:

```
<span size="5" face="Courier New" color="#c5b358">      The Pick-3 Digits Ranked by Frequency - By Position
      File: PA-3
      Drawings Analyzed: 9000 | Date: 08-03-2017
      Frequency norm based on probability: 10%

 Rank     Position 1         Position 2         Position 3    
       Digit  Hits   %    Digit  Hits   %    Digit  Hits   %  

   1     7   961  10.68%    5   931  10.34%    6   940  10.44%
   2     4   949  10.54%    1   924  10.27%    7   927  10.30%
   3     2   938  10.42%    8   913  10.14%    5   925  10.28%
   4     0   926  10.29%    0   897   9.97%    0   908  10.09%
   5     3   884   9.82%    9   896   9.96%    1   897   9.97%
   6     5   883   9.81%    3   895   9.94%    8   895   9.94%
   7     6   872   9.69%    6   895   9.94%    3   883   9.81%
   8     1   872   9.69%    4   888   9.87%    9   883   9.81%
   9     8   869   9.66%    7   885   9.83%    2   878   9.76%
  10     9   846   9.40%    2   876   9.73%    4   864   9.60%
</span>
```

You notice, every digit appeared with a frequency very close to the norm: _1/10_ or _10%_. A few numbers came out with a little better frequency, while others performed just below par. Each and every digit, however, ended up a **loser** money-wise.

Looking at the digits in the first position, the digit _7_ was the best performer with _961_ wins. Total winnings: 961 \* 5 = 4805. **Loss**: 9000 – 4805 = 4195. The digit _9_ was the worst performer: _846_ wins. Total winnings: 846 \* 5 = 4230. <u>Loss</u>: 9000 – 4230 = 4770.

You saw in the roulette report that some of the numbers ended up as winners (in the _straight-up_ bet). No doubt, I exposed that the particular roulette wheel in the Hamburg Casino was seriously biased (they replaced it later). The lottery drawing machines are less prone to bias, as they are not nearly as complicated mechanisms as the roulette wheels.

We saw that also the lottery machines "favored" some digits over others. There are a few percentages above the norm for some digits. The norm calculates that each digit (in this _pick-1_ game) should hit 900 times in 9000 "drawings". The best performer, the digit _7_ came out by 961 / 900 = 6.8% better than the norm. That "percentage advantage" is still a far cry from the house advantage of 50%. Meanwhile, a 6.8% bias in roulette does beat the HA of 2.7 or 5.3%!

The degree of certainty is very high that the numbers will come out with different frequencies. There will always be discrepancies measured in percentages. In games like roulette, some discrepancy percentages will be high enough to make a profit for the gambler who played those particular numbers. In lottery, however, that phenomenon will never occur. The positive discrepancies always come in small percentages; the house edge is always too high. The greedy lottery commissions could cut down the house edge to 25% — and they would still make an indecent amount of money!

The results above were computed for one ticket play. Adding more lottery tickets adds to the cost and total loss. Again, in roulette, playing more numbers can still make a profit for the gambler.

-   These analyses presume **random play** or playing **favorite numbers** (they are still random numbers). Without strategies/systems, loss is to be expected in lottery with a very high degree of certainty.
-   That's why lottery strategies are so important. Many trends develop in statistical series and strategies take advantage of trends. Only specialized software can detect favorable trends and generate a reduced amount of combinations to play selectively (by skipping lottery drawings). At this time in history (2017), there are only two capable applications: **Bright / Ultimate** software packages and **MDIEditor Lotto WE**.
-   Evidently, these findings do not apply to a tiny fraction of a percentage of lottery players. Very few players win big lotto jackpots and thus they make big profits. They can never lose the profits by continuing to play the lottery. It would take thousands (if not millions) of years to reach the amount of drawings analyzed in this material. The overwhelming majority of players, however, <u>lose</u> according to the findings here.

![The best lottery software covers 5, 6, 7-number lotto jackpot games.](https://saliu.com/ScreenImgs/lottery-software.gif)

![Ion Saliu's Theory of Probability Book founded on mathematics applied to strategy, systems for lottery, pick-3-4-5 lotteries.](https://saliu.com/probability-book-Saliu.jpg) [**Read Ion Saliu's book:** _**Probability Theory, Live!**_](https://saliu.com/probability-book.html)  
~ Founded on mathematical discoveries, also applied to creating strategy, systems in lottery software, lotto jackpot games.

![Losing big money in lottery is guaranteed if playing randomly without systems.](https://saliu.com/HLINE.gif)

[

## Resources in Lottery, Software, Systems, Lotto Wheels, Strategies

](https://saliu.com/content/lottery.html)[

## The Best Ever Lottery Strategy, Lotto Strategies.

](https://software.saliu.com/lottery-strategy-lotto-strategies.html)

-   The Main [_**Lotto, Lottery, Software, Strategy, Systems**_](https://saliu.com/LottoWin.htm) Page.  
    Presenting software to create free winning lotto, lottery strategies, systems based on mathematics. Get your lotto systems or wheels, the best lottery, lotto software, combinations, winning numbers.
-   [_**Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies**_](https://saliu.com/Newsgroups.htm).  
    Read a genuine analysis of Excel spreadsheets applied to lottery and lotto software development, systems, and strategies. Combining Excel analysis with powerful lottery and lotto software programmed by this author, _Parpaluck_.
-   [_**Neural Networking, Artificial Intelligence AI, Axiomatic Intelligence AxI in Lottery: Strategies, Systems, Software**_](https://saliu.com/neural-networking-lottery.html).
-   [_**<u>MDIEditor Lotto WE</u>: Lottery Software Manual, Book, ebook, Help**_](https://saliu.com/MDI-lotto-guide.html).  
    ~ Also applicable to LotWon lottery, lotto software; plus Powerball/Mega Millions, Euromillions.
-   [_**Manual, Tutorial of <u>Lottery Software</u>, Lotto Apps**_](https://saliu.com/forum/lotto-book.html).
-   [**Lottery Mathematics, Lotto Mathematics, Math, Maths**](https://saliu.com/gambling-lottery-lotto/lottery-math.htm): Probability, Appearance, Repeat, Affinity, Number Affiliation, Lotto Wheels, Lottery Systems, Strategies.
-   [_**Lottery, Software, Systems, Science, Mathematics**_](https://saliu.com/lottery.html).
-   [_**The Best Strategy in Lottery, Gambling**_](https://saliu.com/strategy-gambling-lottery.html).
-   [_**Software, Formulas to Calculate Lotto Odds**_](https://saliu.com/oddslotto.html) using the hypergeometric distribution probability.
-   [_**Lotto, Lottery, Balls, Memory, Probability Laws, Rules of Randomness**_](https://saliu.com/bbs/messages/575.html).
-   Download [**_Lottery Software: Lotto, Pick 3 4, Powerball, Mega Millions, Euromillions, Keno_**](https://saliu.com/free-lotto-lottery.html).

_Ion Saliu sings **Federal Lottery** on YouTube:_

<iframe src="//www.youtube.com/embed/Qbk5ZgXBZm0" frameborder="0" allowfullscreen=""></iframe>

![Losses amount to thousands of dollars in a lifetime for just one lottery game like pick-3.](https://saliu.com/HLINE.gif)

**[Home](https://saliu.com/index.htm) | [Search](https://saliu.com/Search.htm) | [New Writings](https://saliu.com/bbs/index.html) | [Fundamental Formula](https://saliu.com/formula.htm) | [Odds, Generator](https://saliu.com/calculator_generator.html) | [Contents](https://saliu.com/content/index.html) | [Forums](https://forums.saliu.com/) | [Sitemap](https://saliu.com/sitemap/index.html)**

![You can win the lottery only if playing systems, strategies with good lottery software.](https://saliu.com/HLINE.gif)
