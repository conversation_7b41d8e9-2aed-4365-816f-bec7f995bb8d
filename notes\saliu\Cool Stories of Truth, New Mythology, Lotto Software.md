---
created: 2025-01-03T19:52:31 (UTC +08:00)
tags: [cool stories,Truth,Science of Winning,fundamental formula of gambling,FFG,de Moivre,logarithm,gambling formula,software,lottery probability,mythology,]
source: https://fr.saliu.com/bbs/messages/862.html
author: 
---

# Cool Stories of Truth, New Mythology, Lotto Software

> ## Excerpt
> Download and run new powerful lottery software, lotto programs, read New Truth, New Mythology, Cool Stories in the Heat.

---
**_<PERSON>, <PERSON>, Promoter, and Servant of TRUTH_**

[![<PERSON> has won the lottery multiple times since 1980s in Romania and United States.](https://fr.saliu.com/AxiomIon.jpg)](https://fr.saliu.com/membership.html)  

## Cool Stories of the Truth - New Mythology, New Science of Winning  
New Powerful Lotto Software, Lottery _Wonder Grid_

## By <PERSON>, Axiomatic Mythologist At-Large

![The Truth and a new mythology, just a philosophical story in a mythical manner.](https://fr.saliu.com/bbs/messages/HLINE.gif)

First published on August 15, 2001.

### <u data-immersive-translate-walked="8069cbd4-6d8b-44ed-908c-244bba660c86" data-immersive-translate-paragraph="1">I. Philosophy, Truth and The Science of Winning</u>

You can read passages on my [_**Computational Philosophy**_](https://fr.saliu.com/psyche.htm) page on _**Fear\_Survival primordial essence**_. I described phenomena as _reactions_ of _**FearSurvival**_. Basically, we make conscious decisions. Sometimes, there is a force inside opposing some of our decisions. Consciously, we have a hard time trying to decode the inside opposition. I know by now that my decision may need rethinking or the addition of elements, etc. My understanding is not 100% accurate, possibly never will. That is, it takes a long time, under some circumstances, to come up with the correct interpretation. The difficulty, as I previously explained it, comes from the mixture of _design_ and _choice_ that humans are.

We are what we are _by design_, not only _by choice_, as we love to believe. Haven't you seen that many suckers on TV proudly declaring: _“If I were born again, I wouldn't be any other way! I would (consciously) choose the same way!”_ You would (almost) certainly be another way, sucker, because of the fairly random interaction of the two primordial factors. The chain of interaction is so complex that the probability is infinitely low the same path would be taken again.

I wanted to hit the casinos as big as I could afford to. Yet, there was some opposition inside. I thought over the validity of my systems. I am more unbiased on analyzing my ideas than one can even imagine. That's one reason why I keep _inventing_ idea after idea. _Nothing is perfect, nothing is final, but the eternal Change._ Sounds like religion, but it isn't. It is my motto. It was not disbelief in my systems. Was it money? Yup, to some extent. I could use a larger bankroll. But I learned how to start a walk in small steps. Then, there will come the time to run in huge strides.

My next interpretation was connected to the new directions in lotto strategy: the _wonder grid_ and the _killer lottery strategy_. Again, to some extent, the opposition was valid. It would have been a pity to postpone the creation of the new software.

Yet, the opposing reaction continued, albeit less intensely. I felt I needed a physical checkup. The only one had been in 1999. I was stung by an insect (a bee or a fly) back in 1995. A swelling appeared on the back of my right shoulder. I squeezed it and it almost disappeared. It re-appeared a couple of years ago and I applied the same self-help procedure. It disappeared, it reappeared. It is unsightly, especially when bathing in the sun. Well, I thought, one more reason to go to a doctor and take a physical.

I was accepted by a doctor and got an appointment. I do not trust doctors entirely. Perhaps they don't know no more what a patient is. They only grasp the concept of customer this day and age. I got first–hand experience. A doctor turned me into a vegetarian for a few months. Then I took a blood test. It was supposed to show a lower bad–cholesterol level. Instead, the new cholesterol level was almost a double–up! My bad–cholesterol was better when I was carnivorous than when I took the masochistic pain to become vegetarian! I love my bad cholesterol to be good; I hate when my good cholesterol is too good, therefore bad. Can you dig it, axiomatic one? It's like digging in the mud, you know...

There is a positive in doctor's appointments. A long doctor appointment is good for meditation and positive thinking and positive goal reinforcement. While doing all those medical chores, I was writing the new 32-bit lottery software. I needed to break lines longer than 6 numbers into 6-number combinations. I knew I had some helpful code somewhere. I kept the hard drive from my previous computer. It has valuable source code. Problem is, my new computer is screwed up, literally. Home users cannot open the screws, only computer service technicians can. I got sooo mad at the vendor! The ensuing problem was security related. If I took my PC to a servicing outlet just for unscrewing the screws (!), some might take a preying look at the contents of my files! So, I still resort at finding info I saved on floppies. Or look up my impressive computer library. I did a lot of digging. You probably know the feeling of looking for something you badly need to find. You only find the thing after you passed the point of disgust in your search.

![Discovering Fundamental Formula of Gambling, Pascal, de Moivre, Marcel Boll, little book of probability.](https://fr.saliu.com/bbs/messages/HLINE.gif)

Five years ago I moved to this place, moving along a large number of books and computer items. I left quite a few items in large boxes stored on my open porch. They lived through torrential rains, torrid days, and mythological snowfalls. That's where I found new, forgotten treasures. I did find helpful code in books on the BASIC language. Then I discovered Warren Weaver's _Lady Luck._ My buddy and collaborator John Civitan (author of messages in this forum) had sent me a copy of the book after I wrote about the _Fundamental Formula of Gambling_. I did not need to look for my old copy of the same useful book. Meanwhile, I found another treasure: A little book in Romanian. Don't they say great things come in small packages? It couldn't be truer than in this case.

The book was _**The Certainties of Hazard**_ by French academician Marcel Boll. The book was first published in French in 1941. My 100-page copy was the 1978 Romanian edition. It all came to life, like awakening from a dream. The book presented a table very similar to the table on my _**Fundamental Formula of Gambling**_ page. Then, in small print, the footnote (possibly a translator's note): _“The reader who is familiar with logarithms will remark immediately that **N** is the result of the formula: **N = log(1-pi) / log(1-p)**_.”

Hey, that's what I call the _**Fundamental Formula of Gambling (FFG)**_, indeed! Actually, the author, Marcel Boll did not want to take credit for it. The formula was largely developed by Abraham de Moivre. I gave him credit on my page: [_**Mathematics of Gambling Formula**_](https://fr.saliu.com/formula.htm). I saw the steps in Warren Weaver's _Lady Luck_. Then I remembered more clearly about de Moivre and his formula from my school years. Abraham de Moivre himself probably did not want to take credit for the formula. As a matter of fact, the relation only deals with one element: the probability of N consecutive successes (or failures). Everybody knows, that's _p ^ N_. It's like an axiom, a self-evident truth. Accordingly, nobody can take credit for an axiom. I thought Pascal deserves the most credit for establishing the formula _**p = n / N**_. From there, it's easy to establish _p ^ N_. Pascal founded the probability formula on Aristotelian logic, in turn influenced by Pythagorean mathematical reasoning, further influenced by Egyptian protomathematics...

I felt so relieved! No, nobody credited me with the _**FFG**_. I never asked myself for credit, because the _**Fundamental Formula of Gambling**_ was so obvious! And nobody had brought to my attention that _**FFG**_ was, actually printed in a book published years before my birth. My feeling is more like finding out the answer to a question. Imagine somebody asked you to represent the formula of the _golden number_. You search and search and can't find peace of mind. Until you come across a book. The book has the formula of the _golden number_. What a relief!

The common human reaction in such cases, however, is disappointment. Commonly, humans identify themselves with the Truth. Worse, our emotions become the Truth. Humans oppose the truth that a… Truth represents a relation (most often numerical), outside the humans and independent from the humans. As they say, _The truth is out there!_. Pure and simple. But some humans biologically identify with the truth, or what they perceive to be the truth. Admitting that their thoughts do not represent the truth would be equivalent to death. _“That relation is not true, therefore I am not true, and therefore I do not exist.”_ That's why so many people have always resisted any ideas different from their _perceived_ truths.

History is full of examples. I can only think now of the Dark Ages in the history of the Christian nations. The tiara-wearers in the Vatican believed they were chosen to BE the Truth. If somebody had ideas different from their perceived truths, it was interpreted as life threatening. And they saved no minute before destroying the threat. Didn't they murder brilliant minds such as Giordano Bruno? Or didn't they humiliate brilliant minds such as Galileo?

How about the Ayatollah ordering the murder of Salmon Rushdie for presenting a different viewpoint?

_The fundamental human sin is to identify oneself with the Truth._

### <u data-immersive-translate-walked="8069cbd4-6d8b-44ed-908c-244bba660c86" data-immersive-translate-paragraph="1">II. New Lotto Software for the Science of Winning</u>

While going through all this process, new software was born.  
The _UTIL-6_ has grown into _UTIL632_, superseded by great 32-bit lottery software that runs at _**Command Prompt**_: Super Utilities (main menu, function _U_). The main advantages are: speed, speed, speed. Unlike a real-mode DOS app (16-bit), Super Utilities has these advantages: size, size, size. The program can work with files up to 2GB in size!

[![Lottery Utilities Software, Tools, Lotto Wheels, Systems.](https://fr.saliu.com/ScreenImgs/super-utilities-lottery.gif)](https://fr.saliu.com/free-lotto-tools.html)

The options of the utmost importance are _F = Frequency Reports_ and _M = Make / Break / Position_. It is also confirmed by the feedback I've received. Many already know what the _Frequency_ function can do. And many of them want the feature missing in the freeware UTIL-6. That is, they want to create automatically the _lotto wonder grid_. The _M = Make / Break / Position_ function is new to everybody. The _Make_ program just carries out administrative chores. It automates the creation of different incarnations of the _D6_ file. The _Break_ part, however, is something else entirely, lottery-speak wise!

The _Make / Break / Position_ option:

[![Lottery Software: Jackpot Lotto, Pick Lotteries, Powerball, Mega Millions, Euromillions, Keno.](https://fr.saliu.com/ScreenImgs/make-break-lottery.gif)](https://fr.saliu.com/free-lotto-lottery.html)

The _Break_ part is in function _4 = Break 6+ lines to 6 numbers_. As most of you know, the _F - Frequency_ module creates, among other useful things, a _BEST6_ file, consisting of the _top N_ pairings of all the numbers in the lotto game. If you play a 6/49 lotto game, you can create a 49-line file with, say, the _most frequent 12 pairs_ for each of the 49 numbers. The _lottery wonder-grid_ is a particular case: a 49-line file with the _most frequent 5 pairs_. Many already know how effective the _wonder grid_ can be.

The underlying math has become obvious to just about everybody who visits my site and has a basic grasp of English. I want to take now one further step towards sharper clarity (I'm wearing now computer glasses: sharper close-range reading, sharper mid-range monitor viewing; no need for far range!) The mathematics I present now is valid regardless of the numbers in the lotto game. That is, the relations are the same, be it a 6/40 game, or a 6/49 game, or a 6/54 game, or a _6/nnn_ game. The only differences are in the _COW_ (_cost of winning_ = amount of tickets to play). Holy cow, how do I come up with such things?!

![Lottery wonder grid is founded on pairings of lotto numbers.](https://fr.saliu.com/ScreenImgs/positions-lotto.gif)

The _top 5_ pairings for any given lotto number represent around 25% of the entire frequency of that number's pair frequencies.

• In order to hit a _3 of 6_ prize, we need to add two top pairs to each lotto number. The probability is .25 ^2 = ¼ x ¼ = 1/16. Since the _wonder-grid_ has lines representing all the lotto numbers, it will contain the 6 winners of any draw. Therefore the final probability to hit _3 of 6_ is 6 x 1/16 = 1 in 2.7. Paradoxically, the _wonder grid_ fares worse than playing an equal amount of random combinations! In a lotto 6/49 game, the probability to hit _3 of 6_ is 1 in approx. 56. If playing 49 random combinations (the amount in the _wonder-grid_ for 6/49), the final probability is 49/56 = 1 in 1.14.

Random play is twice as effective as the _lotto wonder-grid_ IF the goal is to hit _3 of 6_. But the _wonder-grid_ performs better and greatly better IF aiming at higher prizes, the jackpot especially!

•• In order to hit a _4 of 6_ prize, we need to add 3 top pairs to each lotto number. The probability is .25 ^3 = ¼ x ¼ x ¼ = 1/64. Since the _wonder-grid_ has lines representing all the lotto numbers, it will contain the 6 winners of any draw. Therefore the final probability to hit _4 of 6_ is 6 x 1/64 = 1 in 10.7. In a lotto 6/49 game, the probability to hit _4 of 6_ is 1 in approximately 1032. If playing 49 random combinations (the amount in the 'wonder-grid' for 6/49), the final probability is 49/1032 = 1 in 21.

Now, the _wonder grid_ is almost twice as effective as random play.

••• In order to hit a _5 of 6_ prize, we need to add 4 top pairs to each lotto number. The probability is .25 ^4 = ¼ x ¼ x ¼ x ¼ = 1/256. Since the _wonder-grid_ has lines representing all the lotto numbers, it will contain the 6 winners of any draw. Therefore the final probability to hit _5 of 6_ is 6 x 1/256 = 1 in 42.7. In a lotto 6/49 game, the probability to hit _5 of 6_ is 1 in approx. 54200. If playing 49 random combinations (the amount in the 'wonder-grid' for 6/49), the final probability is 49/54200 = 1 in 1106.

Now, the _wonder grid_ is almost 26 times (2600%) more effective than random lottery play.

•••• In order to hit a _6 of 6_ prize, we need to add 5 top pairs to each lotto number. The probability is _.25 ^5 = ¼ x ¼ x ¼ x ¼ x ¼ = 1/1024_. Since the _wonder-grid_ has lines representing all the lotto numbers, it will contain the 6 winners of any draw. Therefore the final probability to hit _6 of 6_ is _6 x 1/1024 = 1 in 170.7_. In a lotto 6/49 game, the probability to hit _6 of 6_ is 1 in 13983816. If playing 49 random combinations (the amount in the _wonder-grid_ for 6/49), the final probability is _49/13983816 = 1 in 285384_.

Now, the _lottery wonder grid_ is almost 1669 times more effective than random play.

Even if the combined frequency of the _top 5_ pairs is worse than 25%, the _wonder-grid_ is still much more effective than random play. Say, the frequency is just 20% (1/5). The probability to hit the jackpot is _.2^5 = (1 in 3125) x 6 = 1 in 521_, or 548 times better than random play.

I haven't seen the combined _top 5_ frequency go under 20%.

![Using more lottery pairs increases the chance to win the lotto jackpot.](https://fr.saliu.com/bbs/messages/HLINE.gif)

What if we use more than five top pairs? Evidently, the probability increases accordingly. I presented the case of 25% of the top pairings in the post [_**Most Powerful Lottery Strategy? Pairs, Pairings, Frequency, Lotto**_ **Wonder-Grid**](https://fr.saliu.com/bbs/messages/638.html). The top 25% of the pairings have a combined probability of at least 50%. Instead of ¼ or 1/5, we work with ½ probabilities. For example, the final probability to hit the jackpot becomes _(1 in 32) x 6 = 1 in 5.3_. Of course, the COW is a lot more expensive! In a lotto 6/49 game, 25% of pairs represent 48/4 = 12. We need to break the 12-number lines down to 5-number combinations, and add the corresponding lotto number to each combination. Thus, a line in the _top 25 pairs_ turns into C(12,5) = 792 combinations. Since there are 49 lines (one line for each lotto game) in the _pairings_ file, the grand total becomes 792 x 49 = 38808 combinations.

• The first issue here is to diminish the number of lottery combinations to play. There are options. We don't need to play all the lotto numbers. You can analyze any lotto draw file. You'll notice that in at least 50% of the situations, the current draw contains at least one number from the previous one or two drawings. In my analysis of PA 6/69 game, the percentage is around 60%. The two previous drawings amount to no more than 12 numbers. So, we can go back to the _pairings_ file and delete all the lines BUT the lines representing the 12 (or fewer) numbers that came out in the previous two draws. The _COW_ is less expensive now.

There is also the option of using the LotWon filters to diminish even more the combinations to play. For example – we can eliminate the _worst_ pairings, so that they do not slip into _best pairing_ group. Super Utilities cannot do that, but other LotWon programs can. The new 32-bit combination generators can force all the combinations contain the _top N%_ pairs while filtering out all _bottom M%_ pairs. If we select, for example, the _bottom 25%_ pairs, they only have a combined frequency of 5%. Since we play all lotto numbers, the final probability is 6 x .05 = 0.3. Therefore eliminating the _bottom 25%_ pairs diminishes the power rating of the _best pairing_ feature by 30%. I think this is the _worst case scenario_. Sometimes the generators cannot find any combination to satisfy the two opposing criteria. No combination to play, therefore no cost of playing. But there are cases when only a handful of combinations meet the criteria. They also have a reasonable probability to hit the jackpot.

•• The second issue is the breaking of the lines in the pairings file. If each line has more than 6 numbers, it is very difficult to generate manually 6-number lines (combinations). That's where the new Super Utilities comes in handy. The program does break even the longest lines into 6-number combinations. There are two options:

1) Each pairing file starts with a lotto number, followed by its N pairs. The _Break5_ option of Super Utilities will write the key lotto number (the first in line) in the first position of every combination for that particular line. Then, it will break the remaining numbers (the pairs) into C(N,5) 5-number combination. In this example, the line represents the pairings for the lotto number 3:

\- 3 31 51 64 11 50 13 52 (its _top 7_ pairs).  
The _Break5_ function of Super Utilities converts the line above into the following matrix:

2) The _Break6_ option will wheel all the numbers in a line equally. Thus, an N-pair line turns into _C(N+1, 6)_ combinations. The same line for lotto number 3 is converted into C(8, 6) = 28 combinations. The _key_ number (3, in this example) does not appear in every combination. Here are some of the resulting combinations:

This option will always generate more combinations than _Break5_. The _COW_ is more expensive, but the jackpot probability is higher. This is the option employed by the new 32-bit lotto combination generators. When enabling the _worst pairs_ filter, there are no combinations to play sometimes. Using the C(N+1, 6) instead of C(N, 5) option increases the chance to generate extra combinations that can hit the jackpot.

You can also sort in ascending order the resulting _break_ files. You should never edit the _pairings_ file. UTIL632 recognizes the following number delimiters: space(s), tabs, and commas (,). Also, all the lines are equal in size (i.e. number of pairs). Be sure you do not add unrecognizable number delimiters. Be sure you do not delete pairs in some lines. You will work with a pairing file named _BEST6_ (the default name; you can choose any other). It is the file with the _best_ pairs (_top N pairings_). It's best not to touch that file. Or, as per above, you can delete some lines COMPLETELY (e.g. leave only the 12 numbers that came out in the previous two lotto drawings). Again, delete lines completely, but do not delete numbers in lines!

The resulting files can be huge. No problem. You can check for winners thousands and thousands of combinations. The 32-bit Super Utilities can handle files of up to 2GB in size. Sure, there are those who love _gotchas_. One can generate files for ALL the pairs in a lotto game. In the PA 6/69 game, there are 68 pairs for each number. Using the _Break6_ option, it amounts to 69 x C(69, 6) = 8,271,545,568 6-number lines!!! That's more than the 2GB size limit. Let alone it would take forever to generate all them lotto combinations! I know, gotcha-lovers, there are limits…

![Section presents finally Cool Stories of the Truth Science of Winning Lottery.](https://fr.saliu.com/bbs/messages/HLINE.gif)

### <u data-immersive-translate-walked="8069cbd4-6d8b-44ed-908c-244bba660c86">III. Cool Stories of the Truth - New Mythology</u>

_"It was hot and hotter. Whatever the gods stepped on turned into sand. And more sand and more sand. And the land was an endless wavy dune of melting sand. The gods needed the Truth. The Truth said that at a higher altitude it is so cool, they would feel like the Truth. They started to build a pyramid from sand. The pyramid was growing higher and higher. It felt cooler and cooler. The gods wanted to breath the truth, and eat the truth and become the truth. 'If we make ourselves the Truth, we shall live eternal pleasure!' When they reached the pin of the pyramid, their thoughts froze to ice. And everything on land froze. And the sand was ice, and the air was ice. Then time itself froze on land. The hours were ice…

The Number lit the Truth one night. The light reached the land. It started to melt the ice. The ice melted down into rivers and seas. And the melted ice fell on the land as plants, and trees, and flowers. The ice melted down to land as butterflies, and insects, and birds, and small animals, and frightening beasts. One little snowball was left. The Number said: 'I shall make it into Awareness. I want a breathing creature be aware of Me and the Truth. I shall make a witness to My process. For I want the scribe to inscribe the process in everything that is and is not. Thus it shall be written!'

_

_And there were men and women. And they started the tiresome labor of witnessing."  
(Tladouque, “Steps to Maya Pyramid”)_

![End of Cool Story of Truth and New Mythology.](https://fr.saliu.com/bbs/messages/HLINE.gif)

## [Resources in Lottery, Software, Systems, Lotto Wheels, Strategies](https://fr.saliu.com/content/lottery.html)

Lists the main pages and links to the best lottery, lotto, software, systems, and wheels. It's all about winning lotto, lottery systems, including for pick lotteries, Powerball, Mega Millions, Euromillions, Keno.

## [Resources in Theory of Probability, Mathematics, Statistics, Combinatorics](https://fr.saliu.com/content/probability.html)

Lists the main pages and links to the best in theory of probability, combinatorics, statistics, mathematics. The very original formulas and theories are always backed by specific computer programs. The computers are so much faster and more accurate than the humans!

## [Resources in Philosophy of Homo Computing-Beast](https://fr.saliu.com/content/philosophy.html)

-   Download [**lottery software, lotto programs**](https://fr.saliu.com/infodown.html).

![The Truth and a new mythology...just a story in a mythical manner.](https://fr.saliu.com/bbs/messages/HLINE.gif)

**[Home](https://fr.saliu.com/index.htm) | [Search](https://fr.saliu.com/Search.htm) | [New Writings](https://fr.saliu.com/bbs/index.html) | [Software](https://fr.saliu.com/infodown.html) | [Odds, Generator](https://fr.saliu.com/calculator_generator.html) | [Contents](https://fr.saliu.com/content/index.html) | [Forums](https://forums.saliu.com/) | [Sitemap](https://fr.saliu.com/sitemap/index.html)**

![The Science of Winning is based on Ion Saliu's Formula to create great lotto software.](https://fr.saliu.com/bbs/messages/HLINE.gif)
