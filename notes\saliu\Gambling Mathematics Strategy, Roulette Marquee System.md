---
created: 2025-07-24T21:26:41 (UTC +08:00)
tags: [Google,AdSense,advertising,Referrals,revenue,clicks,software,mathematics,gambling,lottery,lotto,roulette,casino,math boyz,systems,piracy,attacks,]
source: https://saliu.com/AdSense-referrals.html
author: 
---

# Gambling Mathematics Strategy, Roulette Marquee System

> ## Excerpt
> Essay starts with Google AdSense advertising, referrals, but it deals mostly with gambling mathematics in casino, roulette marquee strategy, systems.

---
  
First captured by the _WayBack Machine_ (_web.archive.org_) on October 12, 2007.

<big>• </big> You think it's all over. You put a great effort in achieving something of financial nature. Then, you have to do it all over again! As the wise baseball man, <PERSON><PERSON>, put it. _“Déja vue all over again”_, and _“It ain't over till it's over!”_

I implemented Google advertising known as _AdSense_ beginning May 2007. It is not an easy feast, believe me. If you work with a handful of Web pages — it is fun. But my website is huge. I did some pruning, but I still had to face 800+ pages! Copy-and-paste scripts FIVE times on each of the 800+ pages! It took me two months to accomplish the task! Well, I'll say it was worth the effort. The content of my site was already there — why not make some money for me? After all, I have expenses related to my Internet activity. Meanwhile, I offer lots of freebies, including software and systems. People pay with an arm and a leg, sometimes, to buy worthless systems and software packages from other developers. People can get unquestionably the best stuff here — and it is FREE!

This week of 8-14 of July 2007, Google sent me a message.

_Congratulations, your account has been upgraded to include the all-new Referrals 2.0 - click here to get started._

Good news, right? YES, it is. It would have been totally good news if the upgrade would have been automatic. That is, I would have not needed to do anything extra. Oh, well. It ain't simple if it is too simple. I have no choice but redo 800 web pages… all over again! Replace the referrals scripts by the code of version 2.0! I don't know how long it's gonna take. There is the possibility of higher ad revenue, though. There is also a number of confusions still in place — regarding the maximum number of links I may place on a page.

![Implementing Google AdSense advertising, referrals to earn money for casino roulette gambling.](https://saliu.com/HLINE.gif)

<big>• • </big> I take this opportunity to clarify a few things. First off, I do not ask, or encourage, any visitor to this web site to click on any ad. You do not do me a favor if you click on the ads without your real interest. You only click if you find an ad to serve your interest. I find ads of interest to me, but I am under strict contractual restrictions. You are not under contractual restrictions. But you must not click on ads just because you falsely believe you do me a favor. Things work well without false favors!

Or, you might think you could do me harm. You know, my _googoos_, the Kulais, Kokostirks, Kotskarrs, Karaklonchahs of the world… The likes of them run scripts that click the search box on my search page exactly 11 times a day. The very same 11 search strings every day, including misspellings! Searches such as _worl uk lottery incorporated_, _Download programme for lottery 6/49_, or _switched current filter\\"pdf\\"_... That's sabotage, in my book. Companies such as Google also treat it as sabotage. The law enforcement treats such acts of sabotage as crimes.

I am allowed, however, to encourage people to try the referrals to Google products. I do recommend you try the Google Referrals you see advertised at my website. I recommend them based on first-hand experience. I am a user of _Google AdSense, Google Pack, Google Toolbar, Google Applications_. I tried also _Google AdWords_, but it did not make financial sense for me. I got nothing to sell. _AdWords_ make sense for Internet publishers who sell expensive products or services at their Web sites.

-   Google _Referrals_ was the proverbial example of _too good to be true_ — it didn't last long. Google discontinued the mutually-profitable advertising service in a couple of years. Google is notorious for offering services only to discontinue them sooner rather than later. Google discontinued some 25 services in a decade's time. Yet, they are in business, while previous giants went the dinosaur's way (Lotus, WordPerfect, dBase III Ashton-Tate…)

![Gambling mathematics applied to lotto, lottery, roulette, casino.](https://saliu.com/HLINE.gif)

<big>• • • </big> Speaking of _googoos_. There are people in this grandiose world of ours who can't live simply because I live! There are several articles at this Web site showing various attacks against yours truly. I think it started with casino guys, and especially online casinos. My mathematically founded theories can do harm to the odds, therefore serious damage to the casinos. The attacks were disguised for the most part. However, in the year of graces: 2000, even a casino chairman felt compelled to explode and reveal the identity of the attacker: [_**Roulette Gambling Systems, Threats, Casino Chairman**_.](https://saliu.com/bbs/messages/579.html)

In another instance, a lower-level casino guy (online, this time) virulently attacked me soon after I released the _**Fundamental Formula of Gambling**_ (the by-now famous _**FFG**_). That's how the _Googoo war_ started: [Gambling Formula, Randomness, _Norman Wattenberger_, Abuse of Laws, _Narcissism_ of Bogus Complaints](https://saliu.com/gambling-fights.html).

The vast majority of the attackers, however, are sore competitors with piracy intentions. I was surprised to receive attacks from educators. They wanted me to shut down my FFG page just because they wanted to claim credit for it! Many of my lottery and gambling systems are pirated, albeit in the worst manner. The pirates try to get around the appearance of piracy and/or plagiarism — so they make changes to my systems that lead to grotesque results sometimes.

My software is extremely hard to pirate or plagiarize. Reason: I do NOT reveal the source code! As simple as that. You might stumble upon older pages at this Web site that deal with beggars begging me to give them the source code of my software; or, at least, publish the algorithms; or just describe in detail how the filters in my lottery software work! They reach levels of desperation and resort to blackmail. They call me paranoid! I enjoy those situations! I listen to that great _Black Sabbath_ song _Paranoid (Iron Man)_!

You will become familiar with a situation like this — if you aren't already familiar with it. So-called experts in gambling mathematics shout deafeningly that gambling is 100% losing proposition for the player. The player always loses because the gambling house has a mathematical advantage! That deafening mouth-foaming noise began with my public appearance on the gambling and lottery scene.

Meanwhile, the very same nay-Sayers aggressively promote their so-called "winning gambling systems"!!! On one hand, they scream _"Don't believe Ion Saliu! NO gambling system works!”_ On the other hand, they shamelessly lure people to their own winning gambling systems!

Well, most of those examples of schizophrenia are the casinos in disguise. I saw incredible TV shows on CD filmed inside casinos! Yes, big TV cameras showing how to apply winning gambling systems in the casinos! You know what? TV cameras are not allowed in a casino. Even the new, compact, digital cameras are NOT allowed in the casino. They are considered illegal devices! You wanna know the peak of absurdity? They would arrest me if I enter a casino with a notebook! I received plenty of “well wishing” messages in that regard…

Now, you know the identity of those who sell expensive gambling systems on CDs, based on _filmed on location inside a real casino_ (John Patrick, gambling author _and_ casino employee!) You know who may bring big TV cameras inside a casino. Ain't me…

Thus, it is undeniable truth. The casinos pay “famous” gambling experts to develop gambling systems and sell them to the public. Actually, the casinos would be more than happy to offer the systems for free. But, then, most people would smell the stink. It would be clear: The casinos attempt to make gamblers swallow systems. Hey, the casino fat cats shout over expensive cognac, the players believe in gambling systems? Well, then, give them gambling systems! But make sure it's our gambling systems only!

There is also a sizable number of lowlifes who want to be like me. Alas, their skulls are barely able to sustain their chicken-size brains! Sorry, but I can't find better metaphors for those poor creatures unable to glue together two coherent sentences, with good punctuation…

-   Axiomatic ones, my strong response, in the tough manner expressed on this page, certainly assured my venture stay on line, survive, and thrive.

![MDIEditor Lotto is intelligent lottery, gambling, probability software without peer.](https://saliu.com/ScreenImgs/mdi-lotto-software.gif)

<big>• • • • </big> Speaking of _math boyz_. That's the latest buzzword meant to spot me. No attack against me has worked so far. We exclude now and here the extremely insane case of physical elimination. There must be very, very few lowlifes who think in those insane terms.

So, Ion Saliu has not folded up and quit. Tough guy, huh? They have resorted lately to a shameless and insane association. Mathematics! Ion Saliu is a mathematician therefore he is mad. You know that puerile association. Don't even bother to get into a discussion on this topic with worm-sized-brained lowlifes…

Mathematics rules everything. From the enormity of the Universe to the tiniest particles in the atoms of your blood — laws that can be only expressed mathematically rule it all. Of course, there are extraordinarily many things we do NOT know. But everything that we do not know can only be expressed mathematically. Mathematics is the most objective (unbiased) expression.

Let's stick to gambling mathematics for now. The topic is so big that only a big book could satisfy proper discussion. I might write such a book, for at least there are serious requests in my inbox(es). So many things to do, so short a time. _Ars longa, vita brevis._

Ion Saliu has become the _math boyz_ lately. Yes, they use the plural. The implication points towards insanity. Ion Saliu's systems are the creation of a math boyz therefore they are to be ignored. A math boyz is nuts!

I know how many naïve people read this. Take it positively. I make an association with good faith. If there is good faith, there must be good naiveté as well. I wish I could give to some of you placebo answers, feel-good solutions. Such goodies are not possible without mathematics. Stop crying! You don't need to turn into a mathematician. I do not consider myself a mathematician. I only learned well enough things that I consider of major interest (and help) to me. Plato was far from being a mathematician. Yet, the frontispiece of his Academy read: _”Let no one enter here who is ignorant of mathematics.”_

The trends and streaks are the fundamental parameters in gambling and lottery. The probability of an individual event is also very important, but is an abstract element. Life does not consist of one and only one trial. Life is a complex chain of events (trials, in probability terms).

We all know that the probability of one roulette number to hit is 1/38 (double-zero wheels). Yet, if we play one number in 38 roulette spins, the winning probability is only 65%. Equivalently, the losing probability is 35%. If we play one roulette number in 42 spins, the winning probability is only 67%. If I play one roulette number in 50 spins, the winning probability is 74%. If we play one roulette number in 100 spins, the winning probability is 93%. It NEVER reaches 100% as a degree of certainty!

I analyzed real data from Hamburg Spielbank (casino), Germany. The hot numbers show, sometimes, a 10% gain over the odds. The mild numbers gain the most for _parpalucks_ equal to _2 \* N_ (about 80 spins).

The roulette bias is, actually, the lowest! It is so because only one number at a time is drawn. The more numbers drawn the more accented the bias. The strongest bias I've noticed so far is for lotto-6.

A lotto 6/49 case showed three jackpot hits in 100 drawings with 54,000 combinations (without enabling lottery filters). It beats the normal probability rule by far more than just three standard deviations! Did you get it, Shkitser (Purdue University) and Krokodick (University of Baltimore)? (Two of the “professors” who threw a challenge at gambling mathematicians and their systems. Shkitser, Krokodick and other luminaries participating in a relevant gambling newsgroup simply fled the scene!)

Luminaries or not, they make two pairs of confusions. They confuse the degree of certainty for probability; they confuse favorable cases for trials. The differences are fundamental. The probability is a lifeless measure: number of favorable cases over total cases. The probability represents the success ratio in one and only one trial. One and only one trial represents the extremely rare case when the degree of certainty is equal to the probability. For the degree of certainty measures the success ratio in a number of trials. Take the die example. The probability to roll one particular face is 1/6. The scholars jump at you and say that the probability is p = 1 to get one point face in 6 rolls (tries). Nyet! Six trials are not equivalent to 6 favorable cases! Matter of fact, the degree of certainty of getting one particular face in 6 die rolls is 66.5% (far from 100%).

![This the best roulette strategy based on the numbers displayed on table marquee.](https://saliu.com/HLINE.gif)

## <u>Great Roulette System Based on Marquee Numbers</u>

-   Here is some more from the _math boyz_. I have received info that more casinos turn off the lights of the marquees at their roulette tables. Guess whom they blame? Well, then, let me take a punch in response. You read my take on the Birthday Paradox and roulette: [__**Roulette Software, Systems: Wheel Positioning (Slots, Sectors), <u>Birthday Paradox</u>**__](https://saliu.com/RouletteWheel.html).
-   It is about the ratio between the unique numbers and the repeat numbers displayed on a roulette marquee. Personally, I have seen only one marquee showing 8 unique numbers at the top. I think I only saw two marquees with the latest 6 numbers being unique (no repeats).
-   Let's lay out a few probability figures here. By the way, you can make lots of calculations with my probability software. Titles I mention here: **SuperFormula**, **BirthdayParadox** and **Collisions**.
-   If I see a marquee with 6 unique numbers at the top, the probability is 55% that there will be no 7 consecutive numbers. I will play the last 6 numbers, expecting a repeat number with a probability of 55%. I lose that bet. Now, the probability to see an 8th unique number is: 64%. If I play the last 7 roulette numbers, I expect a repeat roulette number with a 64% probability. Still lost? If I play the last 8 roulette numbers, I expect a repeat-number with a 73% probability. By now, I should have won almost every time. If not, if I play the last 9 roulette numbers, I expect a repeat-number with an 80% probability.
-   Let's figure out a cost: _6+7+8+9 = 30 units_. Keep in mind that a martingale is possible after this point (a bad-case scenario). I have an 80% probability to win 36 units. You saw before, if I randomly play one roulette number for 38 spins, my winning probability is 65%. If you randomly play one roulette number for 30 spins, the winning probability is 55%.
-   Playing 6+7+8+9 units = 30 numbers leads to an 80% winning probability, in accordance to Ion Saliu's Paradox. Think about it next time you are at a roulette table. You can afford to play exactly 30 roulette bets. You are cautious and you play one number at a time for the next 30 roulette spins. The probability that you will lose all your money in 30 spins is: 45%. That is, the chance (degree of certainty, actually) is 45% that you will NOT win a single time. Playing 4 (four) wheel spins _6+7+8+9 units = 30 units_: Your chance to **lose** all your money is: **20%**. Your degree of hope is twice higher than in the first case scenario. You be the judge! Keep in mind, however, that the casinos abruptly black out the marquee at a roulette table! Or, shut down a roulette table! And, of course, they ask you sometimes to NOT use that innocuous paper notebook… or just plain paper...
-   View and/or download the complete reports at your leisure:
-   [_**Roulette Marquee Numbers, Wheel Bias Report by Column**_](https://saliu.com/freeware/roulette-marquee.html).
-   [_**Skips of Roulette Marquee Numbers, Bias, Win, Loss**_](https://saliu.com/freeware/roulette-marquee-skips.html).
-   Another great roulette strategy also free:
-   [_**Wheel Bias, Repeaters (Repeat-Numbers)**_](https://download.saliu.com/roulette-systems.html#roulette-system).

![On Google AdSense advertising, attacks from gambling authors, sore competitors, casino bosses.](https://saliu.com/HLINE.gif)

## [<u>Resources in Theory of Probability, Mathematics, Gambling</u>](https://saliu.com/content/probability.html)

Visit the outstanding resources in theory of probability, mathematics, statistics, combinatorics. The most original theories are based on truth exclusively. The convincing formulas must be backed by data. The best data validation is offered by software.

-   [_**Roulette System, Threat by Casino Chairman of MGM Grand**_](https://saliu.com/bbs/messages/579.html) **named John Schroder** _\- 3/05/2001._
-   [_**Response to Threat, Attack from a Casino Chairman and Gambling Writer**_](https://saliu.com/bbs/messages/580.html) **from Ion Saliu** _\- 3/05/2001._
-   [_**Casino Chairman Strengthens Threats Against Saliu Gambling System Players**_](https://saliu.com/bbs/messages/581.html) **by John Schroder** _\- 3/06/2001._
-   [_**Anti-gambler Advice: John Patrick, Casino Mole, Conspirator**_](https://saliu.com/bbs/messages/587.html) **Jackal (in truth, John Patrick, gambling author paid by casinos)** _\- 3/07/2001._
-   [_**James Bond Roulette Systems: Bet on Two Dozens or Columns, 2-to-1**_](https://saliu.com/bbs/messages/588.html) _\- 3/06/2001._
-   [_**Wizard of Odds Had High Praise for Ion Saliu's Gambling Theory**_](https://saliu.com/bbs/messages/204.html) **(about Michael Shackleford)** _\- 6/22/2000._
-   [_**Gamblers' Fallacy: Doctrine of Maturity of Chances, Wizard of Odds**_](https://saliu.com/bbs/messages/199.html).
-   [_**Gambling is science, hostile reactions Norman Wattenberger**_](https://saliu.com/gambling-fights.html).
-   Adverse Reaction to [_**Online Post on Odds Probability Calculator for Lottery, Gambling**_](https://saliu.com/forum/gamblingodds.html).
-   [The Best Casino Gambling Systems: Blackjack, Roulette, Limited Martingale Betting, Progressions](https://saliu.com/occult-science-gambling.html).
-   [_**Demise of Gambler's Fallacy, Reversed Gambler's Fallacy**_](https://saliu.com/gamblers-fallacy.html) Caused by Ion Saliu and Fundamental Formula of Gambling.
-   [_**Science of Winning and Laws, Casino Bans**_](https://saliu.com/winning.html).
-   [_**Beware! They use my name to sell gambling systems!**_](https://saliu.com/bbs/messages/627.html)
-   [_**Gambling Mathematics: Reaction, Legislation Regarding Online Gambling, Internet Casinos**_](https://saliu.com/gambling-mathematics.html).
-   [_**Caveats in Theory of Probability**_](https://saliu.com/probability-caveats.html).
-   [_**Theory, Mathematics of Roulette Systems, Strategies, Software**_](https://saliu.com/Roulette.htm).
-   [_**Roulette System Based on Wheel Halves, Sectors Bias Betting**_](https://saliu.com/RouletteHalves.html).
-   [Casino Test of _Super Roulette Strategy_, _Halves_ System: _Trump Taj Mahal_](https://saliu.com/bbs/messages/156.html).
-   Download Streaks, Collisions, SuperFormula, and [_**Scientific Software: Mathematics, Statistics, Probability, Combinatorics**_](https://saliu.com/free-science.html).

![Web page deals with gambling mathematics of a roulette strategy based on the marquee numbers.](https://saliu.com/HLINE.gif)

**[Home](https://saliu.com/index.htm) | [Search](https://saliu.com/Search.htm) | [New Writings](https://saliu.com/bbs/index.html) | [Fundamental Formula](https://saliu.com/formula.htm) | [Odds, Generator](https://saliu.com/calculator_generator.html) | [Contents](https://saliu.com/content/index.html) | [Forums](https://forums.saliu.com/) | [Sitemap](https://saliu.com/sitemap/index.html)**

![Implementing Google AdSense advertising, Referrals at website for good money in 2007.](https://saliu.com/HLINE.gif)
