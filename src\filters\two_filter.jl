# TWO Filter Implementation
# TWO 過濾器實現 - 分析號碼對的配對模式

using Dates
using Statistics

# 引入必要的模組
include("../types.jl")
include("../pairing_engine.jl")
include("../filter_engine.jl")

"""
生成號碼組合的所有配對
從給定的號碼列表中生成所有可能的配對組合
"""
function generate_pairs(numbers::Vector{Int})::Vector{Tuple{Int, Int}}
    if length(numbers) < 2
        return Tuple{Int, Int}[]
    end
    
    pairs = Tuple{Int, Int}[]
    n = length(numbers)
    
    for i in 1:n-1
        for j in i+1:n
            # 確保較小的號碼在前
            pair = numbers[i] < numbers[j] ? (numbers[i], numbers[j]) : (numbers[j], numbers[i])
            push!(pairs, pair)
        end
    end
    
    return pairs
end

"""
計算特定配對在歷史數據中的出現頻率
"""
function calculate_pair_frequency(engine::FilterEngine, pair::<PERSON><PERSON>{Int, Int})::Int
    if isempty(engine.historical_data)
        return 0
    end
    
    # 創建配對引擎來計算頻率
    pairing_engine = PairingEngine(engine.historical_data)
    return get_pairing_frequency(pairing_engine, pair[1], pair[2])
end

"""
計算配對的 Skip 值
配對 Skip 是指該配對上次出現到現在的間隔
"""
function calculate_pair_skip(engine::FilterEngine, pair::Tuple{Int, Int})::Int
    if isempty(engine.historical_data)
        return 0
    end
    
    # 尋找配對最後出現的位置（數據是按最新到最舊排序的）
    last_occurrence = 0

    for (i, draw) in enumerate(engine.historical_data)
        if pair[1] in draw.numbers && pair[2] in draw.numbers
            last_occurrence = i
            break  # 找到最近的出現位置就停止（第一個找到的就是最新的）
        end
    end
    
    if last_occurrence == 0
        # 配對從未出現過，返回總數據長度
        return length(engine.historical_data)
    else
        # 返回從最後出現到現在的間隔
        return last_occurrence - 1
    end
end

"""
計算期望的配對頻率
基於理論機率計算期望的配對出現頻率
"""
function calculate_expected_pair_frequency(engine::FilterEngine)::Float64
    if isempty(engine.historical_data)
        return 0.0
    end
    
    # 對於 Lotto 5/39，任意兩個號碼同時出現的理論機率
    # P(兩個特定號碼都被選中) = C(37,3) / C(39,5)
    total_combinations = binomial(39, 5)  # 總可能組合數
    favorable_combinations = binomial(37, 3)  # 包含特定兩個號碼的組合數
    
    theoretical_probability = favorable_combinations / total_combinations
    expected_frequency = theoretical_probability * length(engine.historical_data)
    
    return expected_frequency
end

"""
計算期望的配對數量
對於給定的號碼組合，計算期望的配對數量
"""
function calculate_expected_pairs_count(engine::FilterEngine, numbers::Vector{Int})::Float64
    if length(numbers) < 2
        return 0.0
    end
    
    # 計算可能的配對數量
    possible_pairs = binomial(length(numbers), 2)
    
    # 計算每個配對的期望出現機率
    expected_pair_frequency = calculate_expected_pair_frequency(engine)
    
    # 對於多個配對，使用更複雜的機率計算
    # 這裡使用簡化的線性近似
    return possible_pairs * (expected_pair_frequency / length(engine.historical_data))
end

"""
計算配對分數
基於實際頻率與期望頻率的比較
"""
function calculate_pair_score(actual_frequency::Int, expected_frequency::Float64)::Float64
    if expected_frequency <= 0
        return actual_frequency > 0 ? 2.0 : 1.0
    end
    
    return actual_frequency / expected_frequency
end

"""
計算配對信心水準
基於配對的歷史表現和統計顯著性
"""
function calculate_pairs_confidence(pair_scores::Vector{Float64})::Float64
    if isempty(pair_scores)
        return 0.0
    end
    
    # 計算配對分數的統計特性
    mean_score = mean(pair_scores)
    std_score = length(pair_scores) > 1 ? std(pair_scores) : 0.0
    
    # 基於平均分數和一致性計算信心
    base_confidence = min(1.0, mean_score / 2.0)  # 分數越高，信心越高
    consistency_bonus = std_score < 0.5 ? 0.2 : 0.0  # 一致性獎勵
    
    return clamp(base_confidence + consistency_bonus, 0.0, 1.0)
end

"""
獲取歷史配對數量序列
返回歷史上每次開獎中出現的配對數量
"""
function get_historical_pair_counts(engine::FilterEngine)::Vector{Int}
    if isempty(engine.historical_data)
        return Int[]
    end
    
    pair_counts = Int[]
    
    for draw in engine.historical_data
        # 計算該次開獎中的配對數量
        pairs = generate_pairs(draw.numbers)
        push!(pair_counts, length(pairs))
    end
    
    return pair_counts
end

"""
計算 TWO 過濾器結果
分析給定號碼組合的配對模式
"""
function calculate_two_filter(engine::FilterEngine, numbers::Vector{Int})::FilterResult
    start_time = time()
    
    if length(numbers) < 2
        throw(ArgumentError("TWO 過濾器至少需要 2 個號碼，得到: $(length(numbers))"))
    end
    
    if isempty(engine.historical_data)
        throw(ArgumentError("歷史數據不能為空"))
    end
    
    # 驗證號碼範圍
    if !all(1 <= n <= 39 for n in numbers)
        throw(ArgumentError("所有號碼必須在 1-39 範圍內"))
    end
    
    # 檢查快取
    cache_key = "two_filter_$(sort(numbers))"
    if engine.cache_enabled && haskey(engine.filter_cache, cache_key)
        cached_result = engine.filter_cache[cache_key]
        @info "使用快取結果: TWO 過濾器，號碼 $(numbers)"
        return cached_result
    end
    
    try
        # 生成所有配對
        pairs = generate_pairs(numbers)
        pair_scores = Float64[]
        
        # 計算每個配對的分數
        expected_frequency = calculate_expected_pair_frequency(engine)
        
        for pair in pairs
            actual_frequency = calculate_pair_frequency(engine, pair)
            score = calculate_pair_score(actual_frequency, expected_frequency)
            push!(pair_scores, score)
        end
        
        # 計算整體 TWO 過濾器值
        current_pairs_count = length(pairs)
        expected_pairs_count = calculate_expected_pairs_count(engine, numbers)
        
        # 判斷是否有利（配對數量適中更有利）
        is_favorable = current_pairs_count <= expected_pairs_count * 1.2
        
        # 計算信心水準
        confidence = calculate_pairs_confidence(pair_scores)
        
        # 獲取歷史配對數量
        historical_counts = get_historical_pair_counts(engine)
        
        # 計算執行時間
        calculation_time = time() - start_time
        
        # 創建結果
        result = FilterResult(
            "TWO_FILTER_$(length(numbers))_numbers",
            TWO_FILTER,
            current_pairs_count,
            expected_pairs_count,
            is_favorable,
            confidence,
            historical_counts,
            calculation_time
        )
        
        # 儲存到快取
        if engine.cache_enabled
            engine.filter_cache[cache_key] = result
            manage_cache_size!(engine)
        end
        
        return result
        
    catch e
        @error "計算 TWO 過濾器時發生錯誤" numbers=numbers error=e
        rethrow(e)
    end
end

"""
分析特定配對的詳細資訊
"""
function analyze_specific_pair(engine::FilterEngine, pair::Tuple{Int, Int})::Dict{String, Any}
    frequency = calculate_pair_frequency(engine, pair)
    skip_value = calculate_pair_skip(engine, pair)
    expected_freq = calculate_expected_pair_frequency(engine)
    score = calculate_pair_score(frequency, expected_freq)
    
    return Dict(
        "pair" => pair,
        "frequency" => frequency,
        "skip" => skip_value,
        "expected_frequency" => expected_freq,
        "score" => score,
        "is_above_average" => frequency > expected_freq
    )
end

"""
獲取號碼組合中表現最佳的配對
"""
function get_top_performing_pairs(engine::FilterEngine, numbers::Vector{Int}, top_n::Int = 5)::Vector{Dict{String, Any}}
    pairs = generate_pairs(numbers)
    pair_analyses = [analyze_specific_pair(engine, pair) for pair in pairs]
    
    # 按分數排序
    sort!(pair_analyses, by = x -> x["score"], rev = true)
    
    return pair_analyses[1:min(top_n, length(pair_analyses))]
end

"""
計算 TWO 過濾器的統計摘要
"""
function get_two_filter_summary(results::Vector{FilterResult})::Dict{String, Any}
    if isempty(results)
        return Dict("error" => "無結果數據")
    end
    
    # 篩選 TWO 過濾器結果
    two_filter_results = filter(r -> r.filter_type == TWO_FILTER, results)
    
    if isempty(two_filter_results)
        return Dict("error" => "無 TWO 過濾器結果")
    end
    
    favorable_count = count(r -> r.is_favorable, two_filter_results)
    confidence_levels = [r.confidence_level for r in two_filter_results]
    pair_counts = [r.current_value for r in two_filter_results]
    expected_values = [r.expected_value for r in two_filter_results]
    
    return Dict(
        "total_combinations" => length(two_filter_results),
        "favorable_combinations" => favorable_count,
        "average_confidence" => mean(confidence_levels),
        "average_pair_count" => mean(pair_counts),
        "average_expected_pairs" => mean(expected_values),
        "pair_count_distribution" => Dict(
            "min" => minimum(pair_counts),
            "max" => maximum(pair_counts),
            "median" => median(pair_counts)
        )
    )
end

# 導出函數
export calculate_two_filter, generate_pairs, calculate_pair_frequency
export calculate_pair_skip, analyze_specific_pair, get_top_performing_pairs
export get_two_filter_summary
