---
created: 2025-01-03T19:54:53 (UTC +08:00)
tags: [strategy,strategies,Winning,wonder,grid,wonder-grid,lotto,win,pairs,pairing,pairings,odds,software,lottery,lotto,system,systems,frequency,statistics,drawings,draws,number,numbers,jackpot,combinations,random,research,analysis]
source: https://ru.saliu.com/bbs/messages/644.html
author: 
---

# Likely Winning Lotto Numbers To Wheel By Wheeling Software

> ## Excerpt
> Lottery wonder grid strategy finds the best lotto numbers, wheeling them with one of the best lotto wheels that eliminate unlikely pairs, all odds, all evens.

---
Posted by [KM](mailto:<EMAIL>) on March 27, 2001.

In Reply to: [_**Most powerful lottery strategy? Pairs, pairings, frequency, lotto 'Wonder-grid'**_](https://ru.saliu.com/bbs/messages/638.html) posted by <PERSON> on March 24, 2001.

Ion:

This may not be particularly what you're looking for, but it has been helpful in those instances when I've had far too many potential wheeled combination.

I freely admit that this strategy has its faults. However, since I cannot afford to play thousands of combinations, I had to come up with a criteria that at least decisive to cull down the herd, so to speak. It is as follows:

Utilizing your ffg I get a pool of potential numbers and wheel them with one of your wheels that automatically eliminates unlikely pairs, all odds, all evens and etc.

: Let's take the lotto 6/49 game, the most common worldwide. The median is 6 (use my freeware FORMULA to do the calculations for a 50% degree of certainty). We need software to plot the skips for each lotto number. A skip shows how many drawings a number waits between hits. My freeware “MDIEditor and Lotto” and UTIL-6 do the best job at charting the lotto skips. One more reason why playing static lotto wheels is a no-no. See also my post on this message board: “The myths of lottery: frequency and systems”.

![All about lotto lottery: Software, systems, generate winning  combinations.](https://ru.saliu.com/bbs/messages/HLINE.gif)

## [Resources in Lottery Software, Systems, Lotto Wheeling](https://ru.saliu.com/content/lottery.html)

-   Download [**lottery software, lotto software**](https://ru.saliu.com/infodown.html).
    
    <u>Follow Ups</u>  
    
-   [_**Wonder Grid Lottery Strategy Plays Pairs of Lotto Numbers**_](https://ru.saliu.com/bbs/messages/638.html) **Ion Saliu** _3/24/2001._
-   [Optimal lotto drawings range to analyze for lottery wonder grid system](https://ru.saliu.com/bbs/messages/663.html) **Ion Saliu** _4/07/2001._
-   [The optimal lottery drawings range to analyze - Illinois Lotto](https://ru.saliu.com/bbs/messages/664.html) **Bill Smith** _4/09/2001._
-   [BELLOTTO, BELLBET, BETUS: Software to generate random combinations for lotto, lottery, gambling](https://ru.saliu.com/bbs/messages/665.html) **Ion Saliu** _4/09/2001._
-   [The Wonder-grid lottery strategy hit the lotto jackpot in Belgium Lottery](https://ru.saliu.com/bbs/messages/647.html) **El Loco** _3/29/2001._
-   [_**Lottery Pairs System, Lotto Pair Strategy**_](https://ru.saliu.com/bbs/messages/645.html) **lottoscorp** _3/28/2001._
-   [Lottery system: Lotto numbers, skips of lotto numbers, sum of gaps](https://ru.saliu.com/bbs/messages/646.html) **BigJer** _3/28/2001._
-   [Lotto deltas in powerful, winning lottery software](https://ru.saliu.com/bbs/messages/648.html) **KM** _3/29/2001._
-   [Lottery Analysis in the Spirit of Ramanujan – The Great Indian Mathematician](https://ru.saliu.com/bbs/messages/641.html) **Ramanujan** _3/25/2001._

![Lottery wonder grid strategy finds the best lotto numbers, wheeling them with efficient lotto wheels.](https://ru.saliu.com/bbs/messages/HLINE.gif)

Comments:  

![The best lotto wheels eliminate unlikely pairs, all odds, all evens, all low, all high.](https://ru.saliu.com/bbs/messages/HLINE.gif)

**[Home](https://ru.saliu.com/index.htm) | [Search](https://ru.saliu.com/Search.htm) | [New Writings](https://ru.saliu.com/bbs/index.html) | [Software](https://ru.saliu.com/infodown.html) | [Odds, Generator](https://ru.saliu.com/calculator_generator.html) | [Contents](https://ru.saliu.com/content/index.html) | [Forums](https://forums.saliu.com/) | [Sitemap](https://ru.saliu.com/sitemap/index.html)**

![SALIU.COM is the site for lottery systems and lotto wheels.](https://ru.saliu.com/bbs/messages/HLINE.gif)
