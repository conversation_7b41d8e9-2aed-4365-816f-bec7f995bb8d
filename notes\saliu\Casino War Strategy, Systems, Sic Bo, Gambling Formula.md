---
created: 2025-07-24T21:26:41 (UTC +08:00)
tags: [casino,casino war game,strategy,system,strategies,gambler's fallacy,gambling,house advantage,house edge,odds,sic bo,software,systems,]
source: https://forums.saliu.com/strategies-casino-war-sic-bo-gambling-formula.html
author: 
---

# Casino War Strategy, Systems, Sic Bo, Gambling Formula

> ## Excerpt
> Apply Fundamental Formula of Gambling FFG to create strategies, systems for Casino War, Casino Wars and Sic Bo unusual gambling games.

---
Hi all and axiomatic Ion:

I used to like one simple game in land-based casino, **_Casino War_**. Player and dealer gets one card each, the higher card wins. A is the highest card. If the first card ties, player can surrender (get half chips back), or _Go to War_ (player and dealer put the same amount of chips down, and get one more card each. higher card wins. if tie again, dealer pay extra chips...)

Can we play it as _Red/Black_ streak strategy in roulette? or should we use another kind of strategy?

Cheers, scintillatingly axiomatic kokodrilos!

_Orestes_

___

Kokodrilo: You really are a _big-time gambler_ (hence the royalty name of _kokodrilo_).

I hadn't heard of that _casino war_ game before. You probably gamble in the Far East.

First, we calculate the odds of a tie through the _**Birthday Paradox**_(running BirthdayParadox or Collisions for 13 elements, 2 at a time).

At first analysis, I thought it was a really bad game. The _tie_ probability is 7.7%, very much like the _push_ in blackjack. The same is true about some BJ games that offer the _pair bet_. The casino should pay something like 13 to 1 — or at least 12 to 1. They pay far less than that. On the other hand, a _push in blackjack means nobody loses; the Player gets his/her money back_.

This _Casino War_ game might be the only case where _**card counting** is valid_. A lot depends how the game is played. Is the game played heads-up: Dealer versus one player only? Is the deck of cards reshuffled after every decision (loss or win for the Player)? Is the tie for low cards (2 to 7) or for high cards (9 to Ace)? I make 8 the neutral card. Then, the Player can make a better educated decision.

Still, the **_Fundamental Formula of Gambling FFG_** is the best instrument to use for _Casino War_. As you, Jimmy, figured out: A strategy for _p = 1/2_ can be applied. Keep track of the W and L streaks as for red/black, high/low, etc. (at roulette), Player/Banker (at baccarat). I believe blackjack is worse than the two aforementioned casino games and FFG should be applied less aggressively.

Read a special page right here, in this _sui generis_ forum: [_**Casino War Strategy, Casino Wars Strategies from Fundamental Formula of Gambling**_](https://forums.saliu.com/casino-war-strategy.html).

I haven't heard _Casino Wars_ being offered by the U.S. casinos, specifically Atlantic City. I might write some software, some day.

Right now, I am a _spam killer_. As many visitors noticed, my forums are subject to intense, hateful spamming sometimes.

Best of luck, axiomatic kokodrilos!

_Ion Saliu_

___

How about the casino game _Sic Bo_? Any strategies can be generated by FFG?

Greetings Ion,

Borgata Casino starts to offer 2 new games:

**Casino War** \[new to Atlantic City casinos\]  
**Sic Bo** \[new to Borgata\].

Card counting in **_Casino War_** is NOT worthwhile in Borgata Casino. Borgata makes it harder to count cards on the game of **_Casino War_**. 6 cards are burnt whenever a tie occurs, i.e., when an aggressive player ties with dealer, for the next round, the dealer pulls out 8 cards from the shoe. 6 cards are burnt, and 2 cards are used — 1 for the player and 1 for the dealer.

The tie hands occur more frequently as the table is filled with 6 players; thus, more cards will be burnt, which adversely affect counting cards. For the time being, a Borgata's supervisor watches the game like a hawk. As a result, I didn't waste time to beat the new game.

However, the new game of **_Sic Bo_** in Borgata may be beaten. May I ask you to run a simulation to confirm my gut feeling please?

Here's a picture of Sic Bo:

![See image of Sic Bo layout, dice, bets, odds, payouts, chips, money, bank, bankroll.](https://saliu.com/ScreenImgs/sic-bo-strategies.jpg)

Sic Bo offers many betting options. Surprisingly, the house gets no edge on the _Single Dice Bet_ (a bet of one, two, three, four, five, or six. Look at the bottom part of the picture above).

All the other bets give the house some big edges:

From the smallest edge of  
(1) 3% on the bet of Big (dice score of 18 or more) or Small (dice score of 10 or less)…

to the largest edge of  
(2) 47% on the bet of Three Dice Total (i.e., 3 dice score of 5 or 16).

_Orestes_

___

Hmm... Sic Bo offers the zero house edge to a player when the player plays the _Single Dice Bet_.

_Single Dice Bet_ is this: the specific number 1, 2, 3, 4, 5, or 6 will appear on one, two, or all three dice.

For example, the hot number 5 comes out more frequently than the other numbers. I would bet on number 5. The odd of 5 comes out is 50/50 chance; i.e., the odd of 5 to come out is 1/6 on each die. There are 3 dice, thus, the math goes like this: 0.5=1/6+1/6+1/6.

Since there's no house edge on the Single Dice Bet (**SDB**), I think the double up system works the best on this game of Sic Bo. Borgata allows $1 bet on each **SDB**. The maximum bet is $3,000 on **SDB**. As long as the hot number 5 comes up within 12 bets, I will be a sure winner. See the excel spread sheet below:

1 $1  
2 $2  
3 $4  
4 $8  
5 $16  
6 $32  
7 $64  
8 $128  
9 $256  
10 $512  
11 $1,024  
12 $2,048  
13 $4,096

Here is the money maker. Once a while, a triple hot 5s will come up on all the 3 dice. That means I will get paid triple of my double up bet! For example, say this is the round #8. The triple 5s pop up. I will get paid $384 ($128 \* 3). I will get a net profit of $127 ($384 winning - $127 previous losses).

By the way, to circumvent the $3,000 bet restriction per player, I can call (with a cellphone) extra partners for help when "emergency" comes up. For example, more partners can keep me in the game from round #12 to round #20ish. See info below.

1 $1  
2 $2  
3 $4  
4 $8  
5 $16  
6 $32  
7 $64  
8 $128  
9 $256  
10 $512  
11 $1,024  
12 $2,048  
13 $4,096  
14 $8,192  
15 $16,384  
16 $32,768 .  
17 $65,536  
18 $131,072  
19 $262,144  
20 $524,288

The critical question is: How many times a hot number (i.e., 5) will NOT pop up in Sic Bo? 10? 15? 20?

Ion, please run some _sims_ to find out an answer for me.

Best regards,  
_Orestes_

___

Probability not to catch any specific number is 5/6\*5/6\*5/6 = 125/216 ~ 0.5787 = 57.87%. Suddenly there are over 50% chances for the house to win...  
PS: the casino game with the lowest house edge is craps — buy bets (buy 4 or 10, buy 6 or 8, etc.) — you have the Internet — just look it up.

_Gingle_

___

Gingle, axiomaticule, thanks for your answer on the _probability NOT to catch any specific number_. So... on the other hand, the probability to catch a specific number (say #5) is 42.13% \[100%-57.87%\]. Ahh... I made a mistake on my logic. The math and commonsense don't go hand in hand. The commonsense formula for catching a specific number (i.e., #5) is this: 1/6 + 1/6 +1/6 = 3/6 = 50%, but the real math shows 42.13%, not 50%. Amazing!

Now the next math question: what if I play a million hands of Sic Bo, how many rounds in the row I will lose my bet on a specific number (say #5)? Will I, just like the roulette bet on Red or Black, lose 18 in the row before I hit the #5?

By the way, why not Craps or Roulette? I prefer to play the double up system on Sic Bo because Sic Bo does pay 3 to 1 on a specific number (i.e., triple 5s may come out after a long drought). Just think of this: what if I double up for 12 times consecutively with the bet of $2,048, and the triple 5s pop up? Something to think about, right?

_Orestes_

___

Hello Orestes,

Unfortunately, the math for the probability to catch a specific number is not that simple subtraction. Here are chances for what can occur:

Number on one die: probability = 0.347 (34.7%)  
Number on two dice: probability = 0.069 (6.9%)  
Number on three dice: probability = 0.005 (0.5%)  
Dealer wins: Probability = 0.5787 (57.9%).

To answer your second question, if you play a million _sic bo_ hands at the specific number bet (probability to lose 57.87%), you have 0.999999999799845 (that is over 99.99%) chance to lose 18 or more in a row at least once.

_Gingle_

___

Gingle, what an eye opener! Based on your math, the game of Sic Bo is for suckers. Hmm... that's why Borgata gave me a free room while I was testing out Sic Bo. Gingle, thankyou very much. I'll not waste any more time on Sic Bo.

Here's another question for you. Will the double up system work profitably on the game of EZ-Baccarat?

I ask this question because I've seen a professional gambler (_CGG_) doing it for about 1.5 years.

CGG bets on banker only because there's no commission on the game of _EZ Baccarat_. Note: the gambler does pay a hidden commission indirectly… a casino will not pay on a Banker's winning hand of 3-card-7. For example, say you bet $1,000 on banker. The banker beats player when the banker's hand is 007 against the player's 006. The dealer treats this outcome as a _push_ and will not pay the winner, thus, the $1,000 is a hidden commission for the house.

CGG starts from $15 and doubles up all the way to $10,000. A floorperson said that CGG made averagely about $300 a day, and the casino still awaits CGG to hit a long drought to wipe him out of his huge bankroll. The casino is so certain that CGG will get wiped out... so much so that casino plays dumb even though CGG uses partners to circumvent the $10,000 bet limit.

_Orestes_

___

Hello,

I do not know anything about the _EZ baccarat_ game (rules etc.), but as long as there is a house edge, you will require more steps in order to double up your money.

Now in the given example of _CGG,_ I can tell you that his chances not to go totally bankrupt have increased. However, I would bet on the casino, don't you have a hunch that maybe they're right waiting for his losing streak?

PS: now that you waste no more time at _Sic Bo_, I hope no casino will come after me :-))

___

_Gingle_

Later Edit:  
I looked a little bit at this game. Looks like it is a game with very small house margin.  
If I'm correct, the bets should have the following probabilities:

**Banker** wins = 0.458 (45.8%)  
**Player** wins = 0.446 (44.6%)  
**Tie** \= 0.095 (9.5%)

That gives a total house margin of 1.1%, which is little compared to roulette or other games.  
Still, there is no skill involved in playing the game.

I hope I could help you.

___

Gingle, thanks again. OK I'm sold. No Sic Bo nor EZ-Baccarat for me. You just rescue me from the jaws of casinos. Hey you should run some math seminars in _casinolands_ to save some souls such as CGG's.

Oh, casinos will not come after you, and you're protected under the Good Samaritan Law. However, if casinos ever find out who you're, they'll just blacklist you and keep you _comp-less_. So long!

_Orestes_

___

_**Theory of Streaks**_: The Only Winning Gambling Strategy Founded on Mathematics

Gingle: _“PS: now that you waste no more time at Sic Bo, I hope no casino will come after me :-))”_

Gitser:

You mean, you will come after . . . yourself?! You are the casino, crocodilule! My antennae tell me that in English and... Romaneste! BRRRRRRRRAHAHAHAHA!!!!!!! I figured you out myself rather quickly, axiomaticule.

As of Orestes, he is also a casino guy. His latest IP just confirmed very closely the IP of another casino guy that I had run-ins with (both by email and in my previous BBNow forums).

But I offered you both the courtesy of having been treated as _kokodrilos_ — _big-time gamblers_.

Free advertising, especially for entities such as _Lizard of Odds_, is not accepted here. All casino guys always refer to their favorite mole: _“the wizard of odds”_. He has been on casino payroll for decades! Would he ever offer real gambling systems?! NOT! I put the “wizard-of-odds” issue to rest in the early 2000s. Let him keep trying to get those _200 heads in a row_...

-   [**_Wizard of Odds: Gambling Challenge in Casinos and Testing for Exorbitant Fees_**](https://saliu.com/bbs/messages/196.html)
-   [**_The Wizard of Odds: Phony Gambling Author Paid by the Casinos_**](https://saliu.com/bbs/messages/197.html).

On one hand, Lizard's Web site appears on the Internet as:  
_“Wizard of Odds: The last word on gambling strategy. The Wizard of Odds offers the **mathematically-correct strategies for every casino game** for both land and online casinos.”_

On the other hand, Grand Lizard has always stressed his take on the _gambler's fallacy_ (ad litteram from his websites):  
_“The Wizard of Odds explains why **betting systems won't make you a winner** in the casino.”_

If that is not schizophrenia, I don't know what is!

The real issue here is the infamous _gambler's fallacy_. That's what Gitser and Oritser want to stress here. That's how you automatically spot casino agents. They pretend that one guy teaches gambling mathematics to the other guy. Then they kiss each other's cheeks!

Gitser is mathematically correct. If he would play 1,000,000 (one million) Sic Bo hands, **_uninterruptedly_**, he could lose 18 consecutive hands at least once (if playing NOT to catch any specific number).

But both guys avoid the very important issue of the **_fallacy of gambler's fallacy_** (what I also call **_reversed gambler's fallacy_**). If Gitser would play 1,000,000 (one million) _Sic Bo_ or _Casino Wars_ hands, **_uninterruptedly_**, he could WIN 15 consecutive hands at least once (if playing NOT to catch any specific number).

Gitser and Oritser also refer to a mysterious casino winner, identified as _CGG_. Who knows who that guy is? If he continuously won so far, the **_reversed gambler's fallacy_** would make CGG win forever! In truth, both the **_gambler's fallacy_** and the _reversed gambler's fallacy_ simply represent mysticism. It is extremely opposite to mathematics.

Mathematically, the **_streaks_** occur according to **_mathematical formulas_**. Software can easily and precisely calculate the number of consecutive hits, in a number of trials, for a given probability. My software, _**Streaks**_, has been available for years now.

There is also that issue the casinos and their lizards of odds bluntly throw in the face of kokodrilos and all gamblers. It's about _millions of trials_ (blackjack hands, roulette spins, etc.) Evidently, no human can play 1,000,000 hands uninterruptedly. What the moles do is to extrapolate to all gambling trials ever played all over the world. Then, they assign all those millions of trials to one kokodrilo. You enter a blackjack game, and you already have played millions of hands. Therefore, you are going to lose 18 hands in a row, and then again … and again…

One cannot **_mathematically_** take into account simultaneous or parallel gambling events. I play at one roulette table. Meanwhile, thousands of spins take place at roulette tables in dozens of casinos around the world. I only take care of (keep records, etc.) the roulette spins at the table I am playing at.

And that's the hottest issue that **_rattles_** all them casino moles and agents. As soon as I write down in my notebook the first roulette spin, or BJ hand, the casino honchos jump at my throat. They yell you out the casino — the faint at heart will abide by the screams immediately!

That's the real issue here: <u>Mathematics of streaks</u>. There is no other way to beat the casinos. No skills can beat the casinos — only recording the streaks can. Even a true game of skills, poker, can turn obsolete if recording the hands would be allowed. The so-called masters of poker would be exposed after a few hands. The skilled bluffers can be exposed really easy: They tend to bet as if they always get the strongest hand!

As of our common friend _CGG_, he is already ahead in his gambling. Gitser sez: _“Now in the given example of CGG i can tell you that his chances not to go totally bankrupt have increased.”_ Correct, amice: CGG's chances to go bankrupt have _decreased_. He grew his bankroll. Therefore, he can withstand some long losing streaks. Or, Gitser, did you mean to say that CGG's chances of bankruptcy have _increased_? It would be truthful to your word...

CGG (or any gambler, for that matter) doesn't have to apply a dumb martingale. CGG should continue to record all his streaks and bet according to mathematics. There are only two types of parameters: winning streaks and non-winning streaks. It is always gonna be _win some, lose some_. Only the degrees of certainty are different at different points in the succession of trials.

If an event (like a gambling streak) has a degree of certainty equal to 90% to take place within a number of trials, that event will show a statistical appearance in parity with 90%. The casino lizards will always tell you that the favorable event will never occur. Only the unfavorable opposite will take place — although it has only a 10% degree of certainty!

Best of luck to all kokodrilos and to all true gamblers! You see, the Gitsers and Oritsers of the world will never accept my challenge and gamble at the same casino table where I gamble! The casinos will not allow them to...

_Ion Saliu_  
_Detective At-Large_  
[_**Catch Casino Moles: Intentionally-Losing Gambling Systems**_](https://saliu.com/bbs/messages/587.html)

___

Greetings Ion, I've forgiven you and many other APs who have accused me of being a casino spy! Most accusing APs have thought that I know too much... like a casino insider. For example, I was able to get my hand on a casino floorperson's handbook. I scanned some juicy pages and shared with some APs. Bang — that's my mortal sin — being a nice guy! I was banned from the chatroom and accused of being a casino spy. That's OK. We're all human & sinners. I love your site, and I've still learned something here. By the way, I'm in AC right now. I'll look for you, and I'll treat you for a comped-dinner.

Best regards, Carl's friend for over 3 decades.

_Orestes_

___

_**Sic Bo**_: <u>Correct House Advantage for <i>One-Number</i> Bet</u>

Orestes:

I apologize if I wrongfully assigned you an official casino status. Of course, there is no absolute certainty, as **_FFG_** proves. But I always rely on high degrees of certainty. I rely on patterns. In most cases, I've been right. Indeed, other people, who are NOT associated with me, come to the same conclusion when it comes to so-called casino moles or agents.

One strong indication is the infamous _gambler's fallacy_. The readers receive the strong message that mathematics in gambling is worse than futile: It is bankruptcy-prone.

Oritser, you start with a good feeling regarding the _One number bet_ in Sic Bo. Right there, you do seem an authentic kokodrilo. I read a little more about Sic Bo. Most bets are really NO-NO for a kokodrilo. The only exception is this one-number bet. I hope I understand the wager correctly.

The Player bets on one number. Three dice are cast. If Player's number shows on any one of the 3 dice, Player wins _1 to 1_, or _2 to 1_, or _3 to 1_. The OR operand is of the essence here.

We use that great piece of mathematical software known the world over as _**SuperFormula**_. We will run two functions for this analysis:  
_E = EXACTLY M successes in N trials_  
_L = AT LEAST M successes in N trials_.

If we assume that we bet 1 dollar on number 6 and number 6 comes out at least once on three dice, then Gitser's calculations were correct. In fact, most calculations for this Sic Bo bet are the same. We run the function _L: AT LEAST 1 success in 3 trials_ (dice faces). Result: 42.2%. The chance to lose is the complementary probability: 57.8%. Or, we can employ function _E: EXACTLY 0 successes in 3 trials_ (dice faces). The result: 57.8%.

But here is the subtlety. The casino pays **_bonuses_**. If Player's number (6 in this example) shows up on two faces, the house pays 2 dollars. If Player's number shows up on all three faces, the house pays 3 dollars. Evidently, those bonus situations must be **_added_** to total winning situations for the kokodrilo.

We apply again the function _L: AT LEAST 2 success in 3 trials_ (dice faces). Result: 7.4%.

Throwing N dice represent the numerical set case known as _Saliusian sets_ or _exponential sets_. The total number of possibilities when throwing 3 dice: 6 ^ 3 = 216. You can run another great piece of mathematical software (combinatorics): _**PermuteCombine**_. It generates all 216 dice faces in seconds. You can count by hand all lines (set elements) where #6 occurs exactly once, or at least once, or at least twice, or exactly twice, etc.

That percentage, 7.4, applied to 216 leads to around 16 lines that are paid extra. And one more extra situation: When the triple shows up (6-6-6), the payout is 3 to 1. Thus we have now 17 cases that are paid extra. If those lines were NOT paid extra, the probability to win will always be the same: 42.2%. Therefore, the casino would have a 7.8% house edge.

The casino, however, is very generous in this game! They add 17 winning cases to Player! The gambler should expect around 216 \* .422 = 91 winning situations. The 17 extras will lead to 108 winning situations. **_The real winning chance for Player is now virtually 50%._** That figure is in relation to the house edge.

**_The correct casino advantage for the one-number bet in Sic Bo is virtually 0%._** Indeed, NO house edge! Don't scream, casinos — and, especially, don't change a thing!

You _was_ right, kokodrilo … you was right! That Sic Bo bet looks like the best in the casino right now. Remember: You don't pay extra IF you lose the bet. Neither do you pay extra to participate in the _2 to 1_ or _3 to 1_ payment options.

The calculations resemble blackjack a lot. The winning probability is that percentage. But, then, you add the extras: double-down situations, splitting pairs, blackjacks (naturals). The BJ Dealer may not double down, or split pairs, or get paid _3 to 2_ for a natural.

Have I ever played Sic Bo? NOT! I dislike cumbersome games. Sic Bo is one of them. Craps is another one. You have to wait for all kinds of conditions to take place. Besides, you stand up at craps — keeping records is very hard. I don't like baccarat at all. Mini baccarat seems to be more attractive and it is faster. But the game is too cold. I hate when many players sit at the table and watch one another. They place bets, then they takes back their bets, then place their bets again . . . I hate them mini-baccarat tables!

_Ion Saliu_

[_**Formula Software for Statistics, Mathematics, Probability, Gambling**_](https://saliu.com/formula.html)  
[_**Generate Permutations, Combinations, Arrangements, Exponents**_](https://saliu.com/permutations.html).

___

Orestes: _“Best regards, Carl's friend for over 3 decades.”_

I will appreciate a clarification. The name Carl doesn't ring a bell. I vaguely remember a Carl with whom I worked as a temp some 12 years ago. I didn't know he was interested in gambling. Or, are you referring to... Carl Sagan? I did write about him recently — mostly I am very appreciative of his ideas and work.

Thanks for inviting me to Atlantic City. I'm still tied to home somehow, at least for a while. I want also to try the Pennsylvania casinos first. It's my home state, so I have better chances. I mean legal chances, in case the PA casinos would treat me as those in AC.

Again, I would not give up my little notebook and keeping record of blackjack hands, or roulette spins... or Sic Bo _one-number_ bets! Especially given my bankroll, I must play the best way possible. Mathematics is my only chance...

Best of luck to you in Atlantic City!

_Ion Saliu_

___

How about a 4-dice Sic Bo with a similar one-number bet?

You bet on one die face; e.g. _6_. If _6_ appears on one die, you get paid 1 to 1; if 6 appears on two dice, you get paid 2 to 1; if 6 appears on three dice, you get paid 3 to 1; if 6 appears on all four dice, you get paid 4 to 1.

Total elements of 4 dice: 6 ^ 4 = 1296.

We run the function _L: AT LEAST 1 success in 4 trials_ (dice faces). Result: 38.6%.  
_AT LEAST 2 successes in 4 trials_ (dice faces). Result: 11.6%.  
_AT LEAST 3 successes in 4 trials_ (dice faces). Result: 1.5%.  
_EXACTLY 4 on 4_ (6-6-6-6) = 1 situation that pays 3 extra units.

Winning situations without bonuses: 1296 \* .386 ~ 500  
2-in-4 situation: 1296 \* .116 ~ 150 extra winning cases  
3-in-4 situation: 1296 \* .015 ~ 19 \* 2 ~ 38 extra winning cases  
4-in-4 situation: 3 extra winning cases.

Total winning cases: 500 + 150 + 38 + 3 = 691.

Winning chance in rapport to house edge: **_\-53.3%_**.  
Therefore, the **_Player has the edge: 3.3% advantage_**!

Great idea, you had, Orestes! We discovered new gambling mathematics rules! We both deserve... big comps! Would I be afraid. of … poisoning? You betcha! I am listening right now to Black Sabbath's _Ironman (Paranoid)_

_Parpaluck_

___

Greetings Ion, Casino Guy, me? Hell no! Carl will vouch for me as a legit player. Here are the keywords: New York City Pistol License. Ask him about me---the crazy gambler with a New York City Pistol License. Carl mistook me a NYPD cop, lol! He knows whom I'm---Hurry, hurry, hurry. He's back to AC from Florida for this Summer. Best regards, Orestes.

Orestes

___

You wanna shoot me now, Carl? After all I've done for you? I gave you the best gambling odds ever: 50-50, with NO casino edge! Or, that's the whole point! I destroyed a casino game — the casinos got really mad at me beginning this day!!!

Serious, who's that mysterious Carl? You can email me some details. I googled on those keywords — lots of links to the laws of NYC for getting firearm legal permission. Apparently, the license for a gun in New York City costs an arm and a leg! And you say you are gambler who is licensed to bear firearms. I assume you enter a casino with a gun in your hand... like Joe.

But, if you talk about $262,144 and $524,288 bets, you must be some big... shot on Wall Street!

I wanna know more about that Carl, who, you say, knows me and I know him. Sounds like _Carlos The Jackal_, the Venezuelan terrorist who executed scores of assassination plots, abductions, and bombings.

I will post here a streak rundown for one number (57.8% and 42.2%) in Sic Bo, similar to the table on the blackjack page:

[_**Theory of Streaks: The Foundation of My Blackjack Gambling Strategy**_](https://saliu.com/blackjack.html).

Enjoy your comps and the best of luck to you at Sic Bo!

_Ion “Don't-Call-Me-Carl(os)” Saliu_  
PS  
I had to delete your quotations because of duplicate content — the search engines don't like it. I got enough headaches with spamming...  
IS

___

Orestes:

I can see where you are: Atlantic City _Free Public Library_. You are afraid to use your laptop from your free room at Borgata, right? Honestly, I don't think I'd ever accept a free-room offer by the casinos — regardless of national location or jurisdiction.

I can tell you, your nickname is fearsome alright. That inference to Greek tragedies, with brother Orestes and sister Electra killing their mother, Clytemnestra, as revenge for her killing their father, Agamemnon... Geez!!!

Best of luck, Oritser!

Ion Saliu,  
Playwright At-Large

Strange thing: The last 7 posts here were marked as Moderated. It happened overnight, automatically. The anti-spam function didn't even ask me, the administrator! I don't like that!

As a result, unregistered members were unable to read those messages, especially the one with the latest calculations regarding the Sic Bo NO-edge situation.

Parpaluck,  
Administrator At-Large

IMPORTANT EDIT: BEER IS NOT GOOD FOR A WINE/COGNAC MAN!

I did make a mistake in my calculations regarding the **_Sic Bo_** game, specifically the _Single Dice Bet (SDB)_.

Indeed, the odds of NOT-WINNING (losing) are calculated as EXACTLY 0 successes in 3 trials for probability p = 1/6; result: 57.87%. No arguing here. That translates to 216 \* 0.5787 = 125 situations. THAT'S A GIVEN. Nobody can change that negative result for the Player: 125 losing situations out of 216.

The winning situations for the player are calculated as at least 1 success in 3 trials for probability p = 1/6; result: 42.13%. That translates to 216 \* 0.4213 = 91 situations.

IF the casino would ALWAYS pay 1 to 1, the house edge would be: (125 – 91) / 216 = 34 / 216 = 15.74%.

But the casino tries to sweeten the deal for this particular bet. They do add 17 situations to the original 91 winning situations for the player. Now, the house odds calculations changed to:

(125 – 108) / 216 = 17 / 216 = 7.87%.

This is DEFINITELY the correct house edge at the casino game of Sic Bo, the Single Dice Bet (SDB).

IF the casinos would pay 12 to 1 for the triple (as I saw on some Sic Bo layouts), the edge would be even better for the player: (125 – 117) / 216 = 3.7%. But it would not be 0% even then. Still, _Sic Bo_ remains a little  worse than _French roulette_.

Still, **_Sic Bo_** is NOT that bad of a game. I proved mathematically that blackjack is, in truth, worse than roulette. See the new blackjack odds calculations:

[_**Blackjack Dealer Bust: New Software to Calculate Odds, House Edge**_](https://saliu.com/blackjackodds.html)

[_**Blackjack Odds Analyzed by Binomial Standard Deviation**_](https://www.facebook.com/LotteryPowerballMillionsSoftware/posts/670655603005233).

That's what science is all about. The truth is **_above_** anybody and anything. Shame has no place in searching for TRUTH.

Curious how Cyber World functions! As wrong mathematically as my latest writings here were, my website BENEFITTED! I noticed a serious increase in traffic and in advertising earnings! But, no, thanks! I wouldn't make mistakes intentionally in order to get an increased Web traffic!

Indeed, I was wrong... but for the right reason. I saw immediately that number: 108 favorable situations. Also, immediately, I reported them to total number of cases, 216.

Rather, I should have compared that new “favorable” number to the inflexible number of unfavorable cases: 125. 125 is a given, it never changes.

The house edge would have come all the way down to 0% only if the casino would have added enough extra favorable situations to 108 to equal 125; e.g. the triple paying 19 to 1. In that case: _91 + 15 + 19 = 125_.

The infamous _Gambler's Fallacy_ was the first bullet shot at yours truly, as it were. I was promoting the _**Fundamental Formula of Gambling (FFG)**_ and potentially winning gambling systems derived from the FFG. My opponents, most of them vociferous and aggressive, posed me with this “mathematical” situation:

“You lose once; the probability that you will lose next time is the same as before. You lose twice; the probability that you will lose the third time in a row is the same as before.... and so on... _ad infinitum_, if you will....” It's like a god of odds will always “create” losing outcomes for the player (always winning situations for the gambling house)!

I created two special pages dedicated to the _gambler's fallacy_ and the _reversed gambler's fallacy_:

[_**Gambler's Fallacy, Reversed Gamblers Fallacy, Probability**_](https://saliu.com/gamblers-fallacy.html)

[_**Gambler's Fallacy: Why Not Reversed Gambler's Fallacy As Well?**_](https://forums.saliu.com/reversed-gamblers-fallacy.html)

-   [_**The Best Blackjack Strategy, System Tested with Fair, Unbiased Blackjack Software**_](https://saliu.com/blackjack-strategy-system-win.html)  
    ☛ **There is NO other way to win <big>big</big> at blackjack.**  
    ☛ It applies Ion Saliu's _**Mental Blackjack System**_ founded on _**gambling-streaks strategies**_ with high-roller blackjack software.

Ion Saliu,  
Truthfully At-Large

_"A good man is an axiomatic man; an axiomatic man is a happy man. Be axiomatic!"_

![Casino Wars and Sic Bo games are better than blackjack, roulette, not craps or baccarat.](https://forums.saliu.com/HLINE.gif)

 **[![Forums for gambling software, strategy, systems, winning in real life casinos like in Atlantic City.](https://forums.saliu.com/go-back.gif) Back to Forums Index](https://forums.saliu.com/index.html)        [Socrates Home](https://saliu.com/index.htm)  [Search](https://saliu.com/Search.htm)**

![Jealous authors with bruised overly bloated egos envy Ion Saliu for his casino gambling theory.](https://forums.saliu.com/HLINE.gif)
