---
created: 2025-07-24T22:10:02 (UTC +08:00)
tags: [tutorial,guide,e-book,book,instructions,manual,lotto,software,lottery,pick-3,Powerball,Mega Millions,Euromillions,Keno,horse racing,]
source: https://saliu.com/bbs/messages/818.html
author: <PERSON>
---

# Software Tutorial for Lottery Lotto Powerball Mega Millions

> ## Excerpt
> Read a meaningful book, training tutorial to lottery, lotto software. MDIEditor and Lotto WE is the best software to win the lottery and lotto games.

---
![Create good lottery strategies by setting filters to win lotto jackpots.](https://saliu.com/bbs/messages/HLINE.gif)

### 0\. [Necessity of Tutorial, Manual, Book for Lottery Software](https://saliu.com/bbs/messages/818.html#book)  
I. Step ONE: [Create, Update, Use The Lottery Data Files (Drawings)](https://saliu.com/bbs/messages/818.html#data)  
II. Step TWO: [Generate The Winning Reports (W Files)](https://saliu.com/bbs/messages/818.html#reports)  
III. Step THREE: [Select Lottery _Strategies_](https://saliu.com/bbs/messages/818.html#strategies)  
IV. Resources: [Lotto Software, Lottery Software, Lotto Wheeling, Lottery Strategies](https://saliu.com/bbs/messages/818.html#software)

![Necessity of tutorial, manual, guide for lottery software, programs, lotto strategies.](https://saliu.com/bbs/messages/HLINE.gif)

## 0\. Necessity of tutorial, manual, book for lottery software

In Reply to: [Lotto, lottery software tutorials](https://saliu.com/bbs/messages/808.html) posted by Russ on June 08, 2001 ... read all updates ... we all evolve...in quantum leaps (rarely, but really!)

... That is, I would give a lot (of my gratitude) if someone would write an intelligible and comprehensive LotWon tutorial. Granted, the documentation for the old _LotWon_ software package left much to be desired. I am sincere and serious when I say there are obstacles in my way. As of lately7, I've been greatly upgrading the lottery software. There are new features that I barely understand, but I know they are **valid**. There is a gap between my freeware and my in-house lottery, lotto software. I do not remember all the workings in the freeware without refreshing my memory. That takes time, because the new lotto software has always a plethora of special features and functions. _O tempora! O mores!_

There are three steps. The first two are so simple and easy, that 99% of the lottery software users now face **no problem** whatsoever.

![The important step in lotto strategies is create data files of past winning lottery numbers.](https://saliu.com/bbs/messages/HLINE.gif)

## Step One: Create, update, use lottery data files (drawings)

Nothing in the computer field can be easier than typing short lines of numbers. The lines of numbers represent lottery drawings. No complicated database software is involved; just simple, easy-to-use text editors. LotWon is full-loaded with data files, some real, some samples. Sorry for the one-per-centers, but I swear I cannot make the process easier or more comprehensible than it is. The latest version of MDIEditor and Lotto WE is so intelligently thought-out and well organized! Just take a look:

[![Lottery Software: Jackpot Lotto, Pick Lotteries, Powerball, Mega Millions, Euromillions, Keno.](https://saliu.com/ScreenImgs/mdi-lotto-software.gif)](https://saliu.com/free-lotto-lottery.html)

Even more info on data files to be had: [Help Information](https://saliu.com/Help.htm): MDIEditor and Lotto WE, LotWon, SuperPower Software, Download, Install" (the main _Help_ page of this website).

![When lottery drawings files are correct, creating winning reports is a very easy step.](https://saliu.com/bbs/messages/HLINE.gif)

## Step Two: Generate the winning reports (the _WS_ files)

Menu option: _Winning reports_; or _Filters_ in **MDIEditor Lotto**.

At this step, at least 95% of the lottery software users now face no problem whatsoever. If the easier first step was done correctly, the second step should not be a problem at all. LotWon is full-loaded with WS files, some real, some samples. Sorry for the five-percenters, but I swear I cannot make the process easier or more comprehensible than it is. If somebody else can, I would be grateful.

![The most important step with lottery software is to create lotto strategies setting filter values.](https://saliu.com/bbs/messages/HLINE.gif)

## Step Three: Select Lottery _Strategies_

And now the real "problem" - or so some lottery software players present the situation. The third step is the most important one and the most difficult one. _Analyzing the WS reports and selecting the **lotto strategies**._

At this step, I estimate that 50% of the users have good understanding of reading the _WS_ files. Therefore they have good knowledge of setting the filters in any LotWon lottery software package. Moreover, there are software users who gained knowledge so advanced that I am surprised. Some users go in directions unknown to me before. Such lottery players are a minority, however. But I assume the percentage of the discoverers is higher. I nurture no illusion that the discoverers will share with me their most important findings. It is human nature and I follow to some extent that rule as well.

Back to _step 3 essentials_. I presented in previous posts on this board (_saliu.com/bbs/_) the basics of setting the filters based on the WS reports. Those who followed my recommendations have an advantage. I recommended to copy-and-paste the messages into a text file and edit it. _Then rewrite the file in your own words._ That way you become mentally more involved. You get a better grasp of the matter, _as if it were your own brainchild._ As a proof, I followed such a rule myself to learn computer programming.

There are two paths to follow when selecting playing lottery strategies.

_**3.1) Set one or very few filters to levels outside their normal ranges.**_  
The normal range is determined by the _median_ of the respective lottery filter. The newest pick-3 software makes it very easy to work with the median. The median is already calculated for the user. You can see it at the top of each filter (a column in the WS files). For other packages, you need to determine the median yourself. The calculations will be complicated. You will not do any calculations, however. You can use the WS files and the _**QEdit**_ editor. QEdit has some nice features I did not bother to implement in my editors. Why should I reinvent the wheel every time? One feature of QEdit is _column block or column selecting_. Read the manual for the shortcut, it probably is _Alt+k_. Go to the first drawing in the WS file. Move the cursor to the first digit of a filter (column). If a filter has four digits, make sure the cursor is four spaces from the rightmost digit. The column block must cover all the digits in the filter.

Press simultaneously _Alt+K_ then press the right arrow key until you reach the last digit in the column. Press the down arrow key until you reach the last line in the WS file. Press Alt+k again to conclude the column selection (blocking). Next, do a sort. Probably the shortcut is Shift+F3. The editor will sort the WS file based on the selected column (the key field, for those with database knowledge).

The selected column will be sorted from the lowest lottery filter value to the highest. The median is the number in the middle. If you did the WS reporting for 200 draws, the median will be acceptably in line 100 or 101 (in many cases, line #100 = line #101). QEdit has another feature: _Go to line #_. The shortcut probably is _Ctrl+g_, then type the line number. In this example, type 100 to go to line #100 in the selected column. Right down the median for the filter. Do that for the rest of the filters. Normally, you should carry out this procedure just once. The medians will be close to the first-time figures. It would be more accurate to do first a WS reporting for some 500 draws, if your real data file has that many.

<big>•</big> <u>Important update February 2011</u>  
I wrote specialized software to automate the process of sorting the filters (or columns, or blocks of text). The program name is SortFilterReports5. The lotto software sorts the W5, MD5, GR5, DE5, FE5, SK5 reports by column, helping the user see more easily the filters -- e.g. filters of _wacky values_. The sorting is done by type of winning reports. The lotto program also offers the correct choices (correct names) for filters to sort. There are now similar programs for the 6-number lotto games, pick 3, 4 lotteries, and horse racing. There is also a general-purpose program (**Sorting**) that can sort columns of numbers in any ASCII (text format) file. Sorting is offered as standalone and is also part of the integrated Bright software packages.

[![Lottery Utilities Software, Tools, Lotto Wheels, Systems.](https://saliu.com/ScreenImgs/lotto-b60.gif)](https://saliu.com/free-lotto-tools.html)

_**3.2) You have now the medians for every LotWon lotto software filter.**_  
Filters outside the normal ranges could be:  
• median multiplied by 3 or 4;  
•• median divided by 3 or 4.

For example, if a median is 12, you can set a tight filter to 12x4=48 (or rounded up to 50) for the _minimum value_ of the filter. Or, you can set an equally tight filter to 12/4=3+1 for the _MAXimum value_ of the respective filter. (Remember, the maximum level of a filter must be at least (minimum level)+1). If a filter is set to 4 times the median, it slashes in half four time the total combinations. In the pick-3 example: 1000 lottery combinations reduced to 500 in the first step; 500 slashed to 250; 250 halved to 125; finally, 125 reduced to 60+.

A sorted-by-column WS file can show you even more valuable information. Say, you sorted W3.1 by the Pairs-1 column. The median was 32. The median divided by 4 = 8. Go to line 1 of the column and see how many Pairs-1 are _lower than_ 8. You can see also what kind of levels other filters show for Pairs-1 less than 8. Other filters may show very low numbers as well. Other filters may show bigger numbers. You can choose as a playing strategy _Max\_Pair\_1=8+1=9_, plus other filters _at less tight levels_. For example, _Max\_Vr\_1=4_, _Max\_TV\_1=6_, _Val\_1=5_. This is just an example. You can find similar numbers in your sorted WS files.

The median multiplied by 4 = 128. Go to the last line of the column and see how many Pairs-1 are _larger than_ 128 (or 120 or 130; you can round up or down for more flexibility in your choices). You can see also what kind of levels other filters show for Pairs-1 greater than 128. Other lottery filters may show very high numbers as well. Other filters may show lower numbers. You can choose as a playing strategy _Min\_Pair\_1=130_, plus other filters _at less tight levels_. For example, _Min \_Vr\_1=1_, _Min \_TV\_1=5_, _Min\_Syn\_1=50_.

Using such tight levels for one or very few filters eliminates a huge amount of lotto combinations. Such levels occur more rarely. You should not play them in every drawing. They skip a number of drawings between hits. The newest pick-3 software makes it even easier for you. The lottery software package has also a _strategy-checking_ utility. It shows the levels of all the filters and the skip chart of the strategy.

Please read also the [_**Pick-3 Lottery Strategy, System, Method, Play, Pairs**_](https://saliu.com/STR30.htm) page. It shows why you should only play a lottery strategy if its current skip (the first number in the skip chart) is less than or equal to the median. For example, if the median of the strategy is 5, you should play it only if the first number in the string of skips is 0, or 1, or 2, or 3, or 4, or 5. If the current skip is larger, don't play the strategy; save the money. Since you can select a very, very large number of strategies, look for another lotto strategy. Look for a strategy that shows a current skip under the median.

_**3.3) The other path considers a higher probability of a filter level to occur.**_  
Look at the filters, from line one, back to previous lottery drawings. It is evident that the filters go up and down. It is law. Again, the pick-3 package makes it easier for you. It shows more evidently the filter movement. When a filter is higher than in the previous drawing, the filter has the + sign at the right. If the filter is lower than in the previous draw, it has a – sign attached. It is more visible. You can notice that in most cases the filters go from one trend to the opposite after two or three drawings. That is, after 2 or 3 + signs, the – sign comes up; or vice versa.

Based on that, we can look at each filter (column) in the WS files. The key position is line #1. If the sign in line #1 is -, and also in line #2, and line #3, (3 decreases in a row), we should expect a + (increase) the very next draw. If the sign in line #1 is +, and also in line #2, and line #3, (3 increases in a row), we should expect a - (decrease) the very next lottery draw. Let's take pick-3 as an example. Pair-1 in line #1 is 12 and it shows -, the 3rd consecutive – (decrease). We should expect a + in the very next drawing. An increase in a filter requires the use of the minimum level of the respective filter. In this example, I'll set Min\_Pair\_1=13. If I want to increase the probability, I can set Min\_Pair\_1=10, for example. Of course, the program will generate a larger amount of combinations.

Let's say now Pair-1 in line #1 is 123 and it shows +, the 3rd consecutive +. We should expect a - in the very next drawing. A decrease in a filter requires the use of the maximum level of the respective filter. In this example, I'll set Max\_Pair\_1=124. If I want to increase the probability, I can set Max\_Pair\_1=130, for example. Of course, the program will generate a larger amount of lotto combinations.

You can look for longer streaks, of either + or -. Just go the line #1 in each WS file. There are situations when the current streak can be 4-long, or 5-long, even longer in rare situations. You may want to consider first the longer like-sign streaks. Keep in mind, however, that the streaks shift direction after _up to_ 3 lottery drawings in most cases. Actually, streaks of 1 or 2 consecutive like-signs are the most frequent. I will not go any further in this direction.

You can combine lottery filters selected as in this path with the type of selection presented in path #1. You can set one tight filter (4 times the median, etc.). Then you set other filters as in path #2. For example, Min\_Pair\_1=120 (path #1), Max\_Vr\_1=7 (path #2), Min\_TV\_1=10 (path #1), Min\_Syn\_1=100 (path #1), Max\_Bun\_2=6 (path #2), Max\_Tot\_3=1500 (path #2), Max\_Any\_5=300 (path #2). And so on...

![Learn how to best use the best lottery software applications in the world.](https://saliu.com/bbs/messages/HLINE.gif)

• Very important! If the lottery commission **_changes the game format_**, you must create a **_new_** lotto/lottery data file! Do not mix game formats in one data file! For example, Pennsylvania changed from lotto 6/69 to lotto 6/49. I discarded of all previous lotto 6/69 drawings. I renamed the old 6/69 file and preserved it for archiving and researching purposes. I started from scratch a Pennsylvania lotto 6/49 history file. I recreated also the SIMulated data file, specifically for a 6/49 lotto game. The Powerball game changed its format from 49 regular numbers to 53 regular numbers. I proceeded as above. Changed the Powerball data files to contain only regular lotto numbers from 1 to 53. Please pay great attention to this requirement of SuperPower, LotWon, and MDIEditor and Lotto software. Do not mix various game formats in the same data file — ever!

• There is no such a thing as absolute certainty. Our data files may still hold an error or two, especially in the beginning. I recommend you run, from time to time, my general-purpose software PARSEL. The useful application can discover most types of **_errors_** in your lottery data files. The program is standalone and also comp\[onent of all **Bright** software packages.

• The latest version of **MDIEditor Lotto WE** has a comprehensive help facility. The Windows help file is better and more comprehensive than in any previous versions of my lottery software.  

![This is good training in lotto lottery, software, systems, generate winning combinations.](https://saliu.com/bbs/messages/HLINE.gif)

[

## Resources in Lottery, Software, Systems, Lotto Wheels, Strategies

](https://saliu.com/content/lottery.html)

-   The Main [_**Lotto, Lottery, Software, Strategies**_](https://saliu.com/LottoWin.htm) Page.  
    Presenting software to create free winning lotto, lottery strategies, systems based on mathematics. Get your lotto systems or wheels, the best lottery, lotto software, combinations, winning numbers.
-   [_**Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies**_](https://saliu.com/Newsgroups.htm).  
    Read a genuine analysis of Excel spreadsheets applied to lottery and lotto developing of software, systems, and strategies. Combining Excel analysis with powerful lottery and lotto software programmed by this author, _Parpaluck_.
-   [_**<u>MDIEditor Lotto WE</u>: Lottery Software Manual, Book, ebook, Help**_](https://saliu.com/MDI-lotto-guide.html).  
    ~ Also applicable to LotWon lottery, lotto software; plus Powerball, Mega Millions, Euromillions.
-   [_**Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps, Programs**_](https://saliu.com/forum/lotto-book.html).
    
    _**Pages dedicated to help, instructions, filters, strategies for the best lotto programs and lottery software in the world:**_
    
-   [_**Documentation, Help: MDIEditor Lotto WE, Lottery Software, Strategy Tutorial**_](https://saliu.com/mdi_lotto.html).
-   [_**MDI Editor Lotto**_ Is The Best Lotto Lottery Software; You Be Judge](https://saliu.com/bbs/messages/623.html).
-   [Filters, Restrictions, Elimination, Reduction in Lotto, Lottery Software](https://saliu.com/bbs/messages/42.html).
-   [Step-By-Step Guide to Lotto, Lottery Filters in Software](https://saliu.com/bbs/messages/569.html).
-   [_Vertical or Positional_ Filters In Lottery Software](https://saliu.com/bbs/messages/838.html).
-   [Beginner's Basic Steps to _LotWon_ Lottery Software, Lotto Software](https://saliu.com/bbs/messages/896.html).
-   [_Dynamic_ or _Static_ Filters: Lottery Software, Lotto Analysis, Mathematics](https://saliu.com/bbs/messages/919.html).
-   [**Skips Lottery, Lotto, Gambling, Systems, Strategy**](https://saliu.com/skip-strategy.html)
-   [Lotto Strategy Based on: Sums (Sum-Totals); Odd Even; Low High Numbers](https://saliu.com/strategy.html).
-   [Lottery Utility Software](https://saliu.com/lottery-utility.html): Pick-3, 4 Lottery, Lotto-5, 6, Powerball/Mega Millions/Thunderball, Euromillions.
-   [_**Filtering, Filters in Lottery Software, Lotto Software**_](https://saliu.com/filters.html).
-   _"The Start Is the Hardest Part"_: [Play a Lotto Strategy, Lottery Strategies](https://forums.saliu.com/lottery-strategies-start.html).
-   [**<u>Lotto wheels</u>**](https://saliu.com/lotto_wheels.html) for lotto games drawing 5, 6, or 7 numbers.  
    The most advanced theory of lotto wheels or reduced lottery systems. Get also original lotto wheels for lotto games drawing 5, 6, 7 numbers, Powerball, Mega Millions, Euromillions: Balanced, randomized, totally free.
-   Download [<u><b>Lotto Software, Lottery Applications</b></u>](https://saliu.com/infodown.html).

![After studying this tutorial for lottery software, you can win big money, even the jackpot.](https://saliu.com/bbs/messages/HLINE.gif)

<u>Follow Ups</u>  

-   [Read Lotto Software Book Entirely, Pick-3, Pick 4 Lotteries](https://saliu.com/bbs/messages/821.html) **Nik Barker** _6/14/2001._
-   [Book: Learn using the lottery software, Ion lotto software](https://saliu.com/bbs/messages/819.html) **DEZ** _6/13/2001._
-   [A book for using the great lotto and lottery software](https://saliu.com/bbs/messages/825.html) **Matt** _6/14/2001._
-   [Winning money in Illinois Pick-3 Lottery with Ion lotto software](https://saliu.com/bbs/messages/820.html) **DEZ** _6/13/2001._

![Get here tips, advice running software for lotto, Powerball, Keno, Mega Millions, horse racing.](https://saliu.com/bbs/messages/HLINE.gif)

**[Home](https://saliu.com/index.htm) | [Search](https://saliu.com/Search.htm) | [New Writings](https://saliu.com/bbs/index.html) | [Software](https://saliu.com/infodown.html) | [Odds, Generator](https://saliu.com/calculator_generator.html) | [Contents](https://saliu.com/content/index.html) | [Forums](https://forums.saliu.com/) | [Sitemap](https://saliu.com/sitemap/index.html)**

![This is THE SITE of lottery, lotto, gambling, software, books, ebook, software, strategy, systems.](https://saliu.com/bbs/messages/HLINE.gif)
