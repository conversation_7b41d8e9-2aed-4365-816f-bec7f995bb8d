# Basic Statistics Module
# 基礎統計模組 - 提供過濾器引擎所需的統計計算函數

using Statistics

"""
計算中位數
支援整數和浮點數向量
"""
function calculate_median(values::Vector{T})::Float64 where T <: Real
    if isempty(values)
        throw(ArgumentError("無法計算空向量的中位數"))
    end
    
    sorted_values = sort(values)
    n = length(sorted_values)
    
    if n % 2 == 0
        # 偶數個元素，取中間兩個的平均值
        return (sorted_values[n÷2] + sorted_values[n÷2 + 1]) / 2.0
    else
        # 奇數個元素，取中間元素
        return Float64(sorted_values[(n+1)÷2])
    end
end

"""
計算算術平均值
"""
function calculate_mean(values::Vector{T})::Float64 where T <: Real
    if isempty(values)
        throw(ArgumentError("無法計算空向量的平均值"))
    end
    
    return sum(values) / length(values)
end

"""
計算標準差
使用樣本標準差公式（分母為 n-1）
"""
function calculate_std_dev(values::Vector{T})::Float64 where T <: Real
    if isempty(values)
        throw(ArgumentError("無法計算空向量的標準差"))
    end
    
    if length(values) == 1
        return 0.0
    end
    
    mean_val = calculate_mean(values)
    variance = sum((x - mean_val)^2 for x in values) / (length(values) - 1)
    
    return sqrt(variance)
end

"""
計算變異數
使用樣本變異數公式（分母為 n-1）
"""
function calculate_variance(values::Vector{T})::Float64 where T <: Real
    if isempty(values)
        throw(ArgumentError("無法計算空向量的變異數"))
    end
    
    if length(values) == 1
        return 0.0
    end
    
    mean_val = calculate_mean(values)
    return sum((x - mean_val)^2 for x in values) / (length(values) - 1)
end

"""
計算頻率分佈
返回每個值的出現次數
"""
function calculate_frequency_distribution(values::Vector{T})::Dict{T, Int} where T
    freq_dist = Dict{T, Int}()
    
    for value in values
        freq_dist[value] = get(freq_dist, value, 0) + 1
    end
    
    return freq_dist
end

"""
計算相對頻率分佈
返回每個值的相對頻率（機率）
"""
function calculate_relative_frequency(values::Vector{T})::Dict{T, Float64} where T
    if isempty(values)
        return Dict{T, Float64}()
    end
    
    freq_dist = calculate_frequency_distribution(values)
    total_count = length(values)
    
    relative_freq = Dict{T, Float64}()
    for (value, count) in freq_dist
        relative_freq[value] = count / total_count
    end
    
    return relative_freq
end

"""
計算百分位數
percentile: 0-100 之間的百分位數
"""
function calculate_percentile(values::Vector{T}, percentile::Float64)::Float64 where T <: Real
    if isempty(values)
        throw(ArgumentError("無法計算空向量的百分位數"))
    end
    
    if !(0 <= percentile <= 100)
        throw(ArgumentError("百分位數必須在 0-100 之間，得到: $percentile"))
    end
    
    sorted_values = sort(values)
    n = length(sorted_values)
    
    if percentile == 0
        return Float64(sorted_values[1])
    elseif percentile == 100
        return Float64(sorted_values[end])
    end
    
    # 使用線性插值計算百分位數
    index = percentile / 100 * (n - 1) + 1
    lower_index = floor(Int, index)
    upper_index = ceil(Int, index)
    
    if lower_index == upper_index
        return Float64(sorted_values[lower_index])
    else
        weight = index - lower_index
        return (1 - weight) * sorted_values[lower_index] + weight * sorted_values[upper_index]
    end
end

"""
計算四分位數
返回 Q1, Q2 (中位數), Q3
"""
function calculate_quartiles(values::Vector{T})::Tuple{Float64, Float64, Float64} where T <: Real
    q1 = calculate_percentile(values, 25.0)
    q2 = calculate_percentile(values, 50.0)  # 中位數
    q3 = calculate_percentile(values, 75.0)
    
    return (q1, q2, q3)
end

"""
計算四分位距 (IQR)
"""
function calculate_iqr(values::Vector{T})::Float64 where T <: Real
    q1, _, q3 = calculate_quartiles(values)
    return q3 - q1
end

"""
檢測離群值
使用 IQR 方法檢測離群值
"""
function detect_outliers(values::Vector{T})::Vector{T} where T <: Real
    if length(values) < 4
        return T[]  # 樣本太小，無法檢測離群值
    end
    
    q1, _, q3 = calculate_quartiles(values)
    iqr = q3 - q1
    
    lower_bound = q1 - 1.5 * iqr
    upper_bound = q3 + 1.5 * iqr
    
    outliers = T[]
    for value in values
        if value < lower_bound || value > upper_bound
            push!(outliers, value)
        end
    end
    
    return outliers
end

"""
計算偏度 (Skewness)
衡量分佈的不對稱性
"""
function calculate_skewness(values::Vector{T})::Float64 where T <: Real
    if length(values) < 3
        throw(ArgumentError("計算偏度至少需要 3 個數據點"))
    end
    
    mean_val = calculate_mean(values)
    std_val = calculate_std_dev(values)
    
    if std_val == 0
        return 0.0
    end
    
    n = length(values)
    skewness = sum(((x - mean_val) / std_val)^3 for x in values) / n
    
    return skewness
end

"""
計算峰度 (Kurtosis)
衡量分佈的尖銳程度
"""
function calculate_kurtosis(values::Vector{T})::Float64 where T <: Real
    if length(values) < 4
        throw(ArgumentError("計算峰度至少需要 4 個數據點"))
    end
    
    mean_val = calculate_mean(values)
    std_val = calculate_std_dev(values)
    
    if std_val == 0
        return 0.0
    end
    
    n = length(values)
    kurtosis = sum(((x - mean_val) / std_val)^4 for x in values) / n - 3
    
    return kurtosis
end

"""
計算完整的描述性統計
返回包含所有基本統計指標的字典
"""
function calculate_descriptive_statistics(values::Vector{T})::Dict{String, Any} where T <: Real
    if isempty(values)
        return Dict("error" => "無法計算空向量的統計資訊")
    end
    
    stats = Dict{String, Any}()
    
    try
        stats["count"] = length(values)
        stats["mean"] = calculate_mean(values)
        stats["median"] = calculate_median(values)
        stats["std_dev"] = calculate_std_dev(values)
        stats["variance"] = calculate_variance(values)
        stats["min"] = minimum(values)
        stats["max"] = maximum(values)
        stats["range"] = maximum(values) - minimum(values)
        
        if length(values) >= 4
            q1, q2, q3 = calculate_quartiles(values)
            stats["q1"] = q1
            stats["q2"] = q2
            stats["q3"] = q3
            stats["iqr"] = q3 - q1
            
            outliers = detect_outliers(values)
            stats["outliers"] = outliers
            stats["outlier_count"] = length(outliers)
        end
        
        if length(values) >= 3
            stats["skewness"] = calculate_skewness(values)
        end
        
        if length(values) >= 4
            stats["kurtosis"] = calculate_kurtosis(values)
        end
        
        # 頻率分佈
        freq_dist = calculate_frequency_distribution(values)
        stats["frequency_distribution"] = freq_dist
        stats["unique_values"] = length(freq_dist)
        
    catch e
        stats["error"] = "計算統計資訊時發生錯誤: $e"
    end
    
    return stats
end

"""
比較兩組數據的統計差異
"""
function compare_statistics(values1::Vector{T}, values2::Vector{T})::Dict{String, Any} where T <: Real
    stats1 = calculate_descriptive_statistics(values1)
    stats2 = calculate_descriptive_statistics(values2)
    
    comparison = Dict{String, Any}()
    
    if haskey(stats1, "error") || haskey(stats2, "error")
        comparison["error"] = "無法比較包含錯誤的統計資訊"
        return comparison
    end
    
    comparison["group1"] = stats1
    comparison["group2"] = stats2
    
    # 計算差異
    comparison["differences"] = Dict(
        "mean_diff" => stats2["mean"] - stats1["mean"],
        "median_diff" => stats2["median"] - stats1["median"],
        "std_dev_diff" => stats2["std_dev"] - stats1["std_dev"],
        "range_diff" => stats2["range"] - stats1["range"]
    )
    
    return comparison
end

# 導出所有函數
export calculate_median, calculate_mean, calculate_std_dev, calculate_variance
export calculate_frequency_distribution, calculate_relative_frequency
export calculate_percentile, calculate_quartiles, calculate_iqr
export detect_outliers, calculate_skewness, calculate_kurtosis
export calculate_descriptive_statistics, compare_statistics
