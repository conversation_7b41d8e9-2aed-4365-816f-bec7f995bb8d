---
created: 2025-07-24T21:11:04 (UTC +08:00)
tags: [software,lottery,lotto,horse racing,update,program,lottery pairs,reversed lottery strategy,LIE elimination,]
source: https://saliu.com/software-news.html
author: 
---

# 軟體新聞：樂透、樂透、賽馬、配對策略 --- Software News: Lotto, Lottery, Horse Racing, Pairs Strategy

> ## Excerpt
> Updates to Bright lotto software, lottery software, horse racing programs. Pairings, LIE elimination, reduction, reversed lotto strategies are emphasized.

---
2012 年 5 月發布；後來更新。

## <u><span lang="zh-TW"><span data-immersive-translate-translation-element-mark="1"><span data-immersive-translate-translation-element-mark="1">一、 <i>PairGrid</i> ：用於配對和樂透網格的彩票軟體</span></span></span></u>

**Bright** 軟體包已更新。用戶發現了一些問題，軟體開發者已修復。此外，更新還添加了_**最佳/最差**_配對報告和組合生成的重要功能。此外，此功能還帶來了一些強大的 _**LIE 彩票策略**_ （或_**反向彩票策略**_ ）。

有五個 **Bright 軟體包**受到影響。檔案名稱分別代表遊戲： Bright3、Bright4、BrightH3、Bright5、Bright6。 Bright H3 指的是賽馬遊戲，主要用於產生三連勝。

其中一個問題影響了_**策略檢查**_功能。當某個特定策略在上次分析的彩票開獎中中獎時，會觸發錯誤。現在，軟體可以正確顯示所有使用該特定策略記錄中獎（中獎）的彩票開獎。

我還修復了賽馬的報道問題。我發現像著名的肯塔基賽馬會這樣的比賽存在一個問題。那場比賽的參賽馬匹非常偏頗：從每場 3 匹馬到 25 匹馬（84 或 85 年前的一年）。順便說一句，你應該在報道中避免報道那場比賽（年份）。也就是說，你應該對 80 或 82 場比賽進行 _WH/MH_ 報告。像肯塔基賽馬會歷史資料檔案的標準差或某些過濾器的值非常高。希望策略檢查不會再導致錯誤。我相信我在這方面已經盡力了。

## <u><span lang="zh-TW"><span data-immersive-translate-translation-element-mark="1"><span data-immersive-translate-translation-element-mark="1"><i>二、基於配對和網格的反向 </i>Pick 3 彩票策略</span></span></span></u>

我的軟體的忠實用戶（ _BrianPA_ ）在實施我所謂的 _**LIE**_ 或_**反向彩票策略方面**_發揮了非常重要的作用。他發現在選擇範圍時， _**最佳/最差對報告**_無法準確運行。例如，使用者想要產生從位置 #5 開始的 10 個最佳配對的報告。也就是說，用戶想要具有以下排名的最佳配對：#5、#6、…#13、#14。由於錯誤，軟體始終從位置 #1 開始（例如，從排名 #1 到排名 #10）。受影響的功能在主選單中顯示為 _**S = Super Utilities**_ 。該功能現在可以正常工作。

該程式名為 **PairGrid** 。我故意禁用了兩個功能，但教程中沒有提到。我決定啟用 **PairGrid** 程式中的所有功能，因為它們在製定 _**LIE**_ 彩票策略方面發揮著非常重要的作用。

![Pair grid software for lottery creates winning lotto strategy systems.](https://saliu.com/ScreenImgs/LIE-lottery-strategy.gif)

始終以函數 _**R = 配對報告**_開始。主報告名為 _Pair\*.REP_ （例如，6 位數樂透報告名為 _Pair6.REP_ ）。

以下是用戶報告的 5 號樂透的樣本，並納入了 _LIE_ 策略。

![Powerful lottery strategies eliminates combinations with the worst lotto pairs.](https://saliu.com/ScreenImgs/eliminate-pairs.gif)

讓我們一行一行地講解如何利用這些資訊製作出具有毀滅性的 _**LIE**_ 檔案。

第一行：如您所見，每個中獎號碼中最高對的排名被<big>放大了 </big> 。這正是我們在本例中要重點關注的。

最高排名中最低的配對排名是 <big>36</big> 。這意味著什麼？這意味著我們可以用每個數字的前 35 對創建一個 _TOP5_ 文件，然後使用 _**S = Super Utilities**_ （在 Bright 的主選單中）中的 _M = Make/Break/Position_ ，用 _1 個數字 + 4 對_拆分該文件，得到一個毀滅性的 _**LIE**_ 文件！

你可能覺得35個頂對就夠了。好吧，等等！

看第 14 行。你會看到最低的 _High Ranking_ 是 <big>40。</big> 所以現在你可以用一個包含 39 對的 _TOP5_ 檔案來當作 LIE 檔案了。這真是太棒了！ ！ ！

請注意：報告中的數字並非實際彩券號碼，僅反映數字與主號碼配對頻率的**排序** 。使用上述方法處理謊言檔案時，必須使用過濾器 _ID5_ （用於 _5 個數字中的 5 個數字_剔除）。

_TOP5_ 檔案也在 _**S = Super Utilities**_ 中創建，功能 _F = Frequence Reporting by Number_ 。對於 _TOP5，_ 選擇 _Number of pair_ = 35 和 _Starting location_ = 1。

_**超級實用程式**_中的函數 _F = 以數字報告頻率 (Frequency Reporting by Number_ ) 也會建立兩個更重要的檔案： _BEST_ 和 _WORST_ 。現在， _**LIE**_ 樂透策略的實現方式有所不同。我們將 _Screen\_0_ 應用於組合產生程式（例如 Lexico 、 Combine 、 Wheel ）。

例如，我們分別從位置 #1 和位置 #42（5/43 彩票的最後一個位置）開始，為 1 個配對建立 _BEST_ 和 _WORST_ 。每個檔案包含 43 行（彩票遊戲中的數字）。我們將 _BEST_ 和 _WORST_ 連接起來，例如 _LEAST5_ （包含 86 行）。我們在 _Data-5_ 之上用 _LEAST5_ 重新建立 _D5_ 。使用 _**Super Utilities**_ ，函數 _M = Make/Break/Position_ ，然後 _2 = Make D5 with LEAST5_ 。

接下來，在 _Screen\_0_ 中，我們啟用名為 _Enter LEAST-PAIRINGS_ = 86 的篩選器。軟體將產生遠低於遊戲總組合數的彩票組合數量。輸出檔案將成為一個很好的 _LIE 策略候選_ 。解釋：我們會在多個情況下（標有 \* 的位置）錯過最佳對排名（#1）或最差對排名（#42）。

![Software generates reports for the best and the worst lotto pairs, pairings.](https://saliu.com/ScreenImgs/best-lotto-pairs.gif)

如果我們為每對創建 _BEST_ 和 _WORST_ 檔案（即排名 #1 和 #2、#42 和 #41），我們可以增加獲勝頻率。

PairGrid 創建的另一份報告是 _Pair.WS_ 。

```
<span size="5" face="Courier New" color="#c5b358"> Line                        Four
 no.

 Median:                     250

 Average:                    243

    1     2  7  8 14 31      250
    2    17 25 26 29 32      250
    3     8 14 23 27 35      250
    4    33 34 35 40 43      250
    5    13 17 21 25 41      250
    6     9 17 26 32 41      250
    7     6 24 28 32 41      250
    8     3 24 30 31 33      250
    9     3  7 17 39 43       82
   10     6  8 11 18 31      250
....
   35     5  7 33 39 42      250
   36     8 11 14 23 42      250
   37     6 16 19 22 35      250
   38     3 19 32 33 41      250
   39    10 14 15 30 43        0 *
   40     3  5 14 30 33      250
</span>
```

_Four_ 過濾器的值通常在 250-500 之間（大約 95% 的時間）。也就是說，如果我們設定 _MAX\_Four = 250_ ，我們就會經常出錯。因此，輸出檔案是一個不錯的 _**LIE**_ （有時會有數百種彩票組合）。

但請看第 39 行：這是一個_**直接彩票策略**_的情況（與 _**LIE 或反向**_策略相反）。如果我們設定 _MAX\_Four = 1_ ，函數 _**P = Create File of Missing Pairs**_ 將產生一行（包含與上次彩票抽獎重複的 4 個配對等級）。輸出檔名： _Pair5.OUT_ 。然後我們運行函數 _**G = Generate Combosnations from Missing Pairs**_ ，以 _Pair5.OUT_ 作為輸入檔。產生的組合總數：顯然是 43。這樣的彩券策略很少命中（長跳），所以我們可以等待很多次彩券抽獎後再使用該策略。

我在 Pick-3 彩票軟體（ PairGrid3 ）中發現了一個非常特殊的情況。 Two _過濾_器的取值範圍在 10-20 之間，大約有 15%-20% 的機率。我設定了 _Min\_Two = 10_ 和 _MAX\_Two = 20。_ 輸出檔案包含約 400 個 Pick-3 直選結果。但這個文件大約 80% 的機率是錯誤的（ _LIE_ ）！

## <u><span lang="zh-TW"><span data-immersive-translate-translation-element-mark="1"><span data-immersive-translate-translation-element-mark="1">三、基於配對和網格的<i>直接</i>選 3 彩票策略</span></span></span></u>

公理一：配對始終是有效的**直接**彩票策略。也就是說，我們產生的是用於玩的組合，而不是用來_**消除 LIE 的**_組合。這類軟體的功能僅適用於 pic 彩券和賽馬三連勝。彩票程式需要處理太多的頂級配對才能建立_彩票網格_ 。例如，5/43 彩票需要 _C(42, 4) = 111930 個_配對來建立基本的彩票奇蹟網格（每個彩票號碼加上其前 4 個配對）。

我們與 Pair Grid3 合作進行三選一彩票。此外，還有一份報告： _PAIR3.SK_ 。它是由函數 _R = Pairing Report_ 建立。該報告顯示了三選一彩票中所有 55 個配對的頻率。這些配對由 2 個元素組成，因為_三選一彩票的神奇網格_至少由每個數字加上 2 個該數字的配對組成。

PICK-3 _字串配對_頻率和跳過抽牌分析：1000 對抽牌範圍（ _Parpaluck_ ）：24

  <u><span lang="zh-TW"><span data-immersive-translate-translation-element-mark="1">&nbsp;&nbsp;</span><span data-immersive-translate-translation-element-mark="1"><span data-immersive-translate-translation-element-mark="1">對： <i>1 - 1</i></span></span></span></u>  
跳過：37 28 37 7 53 59 46 6 83 71 16 61 3 55 7 9 10 22 9 21 20 34 33 63 40 44 60 13  
排序跳過：3 6 7 7 9 9 10 13 16 20 21 22 28 33 34 37 37 40 44 46 53 55 59 60 61 63 71 83  
\* 中位數跳過：33  
\* 總點擊數：28 = 2.8%

  <u><span lang="zh-TW"><span data-immersive-translate-translation-element-mark="1">&nbsp;&nbsp;</span><span data-immersive-translate-translation-element-mark="1"><span data-immersive-translate-translation-element-mark="1">對： <i>1 - 2</i></span></span></span></u>  
跳過：11 2 11 2 4 24 5 4 2 31 63 53 21 5 0 17 4 12 10 81 8 0 0 14 24 126 6 44 13 25 9 0 0 14 24 126 6 44 13 25 9 27 961 3 83 21 383 263 2 0 0 0 1  
已排序跳過：0 0 0 0 0 0 0 1 2 2 2 2 4 4 4 5 5 6 6 8 8 9 9 9 10 11 11 11 12 13 13 14 17 21 23 23 24 12 13 13 14 17 21 23 23 24 12 1253 14 17 21 23 23 24 243 246 81 104 126  
\* 中位數跳過：10  
\* 總點擊數：49 = 4.9% ...

  <u><span lang="zh-TW"><span data-immersive-translate-translation-element-mark="1">&nbsp;&nbsp;</span><span data-immersive-translate-translation-element-mark="1"><span data-immersive-translate-translation-element-mark="1">對子： <i>9 - 10</i></span></span></span></u>  
跳過：35 3 43 68 26 9 52 40 8 11 38 26 6 26 30 2 42 7 2 13 1 36 4 31 18 4 12 26 41 18 23 47 5 18 4 12 26 41 18 23 47 5 32 12 12 32  
排序跳過：1 2 2 2 3 4 4 5 6 7 8 9 10 11 11 12 13 17 18 18 23 26 26 26 26 28 30 31 34 35 35 36 38 26 28 30 31 34 35 35 36 38 40742 407 407 40743  
\* 中位數跳過：23  
\*\* 總點擊數：41 = 4.1%

  <u><span lang="zh-TW"><span data-immersive-translate-translation-element-mark="1">&nbsp;&nbsp;</span><span data-immersive-translate-translation-element-mark="1"><span data-immersive-translate-translation-element-mark="1">對子： <i>10 - 10</i></span></span></span></u>  
跳過：42 37 26 10 19 63 36 45 57 56 78 84 32 66 47 31 28 0 2 14 2 13 38 16 28 31 12  
排序跳過：0 2 2 10 12 13 14 16 19 26 28 28 31 31 32 36 37 38 42 45 47 56 57 63 66 78 84  
\* 中位數跳過：31  
\* 總點擊數：27 = 2.7%

我們可以看到，這些配對記錄了各種各樣的頻率。顯然，雙倍配對（從 _1 - 1_ 到 _10 - 10_ ）的頻率最低。我們尋找_頻率最高_且_跳躍中位數最低的_配對。通常，這樣的配對可以在 _PAIR-3.SK_ 報告的中間區域找到。

  <u><span lang="zh-TW"><span data-immersive-translate-translation-element-mark="1">&nbsp;&nbsp;</span><span data-immersive-translate-translation-element-mark="1"><span data-immersive-translate-translation-element-mark="1">對子： <i>5 - 8</i></span></span></span></u>  
跳過：3 1 4 10 26 0 21 4 7 23 11 16 11 27 9 3 44 12 41 6 3 4 45 0 14 3 3 30 4 6 7 26 51 29 0 7 3 3 30 4 6 7 26 51 29 0 7 103 7 0 0 10 5 66 20 24 6 9 4 19 5 7 9 18 1 0 5 26 1 2  
已排序跳過：0 0 0 0 0 0 1 1 1 2 3 3 3 3 3 3 3 4 4 4 4 4 5 5 5 6 6 6 7 7 7 7 8 8 9 9 9 10 10 10 10 7 7 8 8 9 9 9 10 10 10 10 10 12 10 1 20 21 23 24 26 26 26 27 29 30 33 33 41 44 45 51 66  
\* 中位數跳過：8  
\* 總點擊數： _67_ = 6.7%

  <u><span lang="zh-TW"><span data-immersive-translate-translation-element-mark="1">&nbsp;&nbsp;</span><span data-immersive-translate-translation-element-mark="1"><span data-immersive-translate-translation-element-mark="1">對子： <i>6 - 8</i></span></span></span></u>  
跳過：3 75 3 0 8 10 43 24 1 22 10 6 0 10 3 0 1 29 4 6 13 26 29 16 0 32 1 3 3 7 32 3 4 7 9 27 32 174 3 7 32 3 4 7 917 3 1757 3 1757 175 11 0 2 35 19 13 25 27 5 5 23 18 4 2 24 2 24 11  
已排序跳過：0 0 0 0 0 1 1 1 2 2 2 3 3 3 3 3 3 3 3 4 4 4 5 5 5 6 6 7 7 7 8 9 10 10 10 11 11 11 13 138 9 10 10 10 11 11 11 13 13 1832 13 24 24 25 26 27 27 29 29 31 32 32 35 43 44 71 75  
\* 中位數跳過：9 \* 總點擊數： _64_ = 6.4%

_5 - 8_ 和 _6 - 8_ 的配對_頻率較高_ ， _跳過中位數較低_ 。目前跳過次數低於中位數（本例分別_為 3_ 和 _3_ ）。假設我選擇 _6 - 8_ 配對。我再次<u>使用 Pair Grid3</u> ，函數 _U = Make Custom Grid from User Pairs_ 。程式將建立一個配對等級為 5 和 8 的網格，名為 _TopGrid3_ 。

![Pair grid software for lottery creates winning lotto strategy systems.](https://saliu.com/ScreenImgs/pair-grid-pick.gif)

我選擇生成函數 _1 = 1 位元 + 2 對_ 。程式產生了 54 個連勝。顯然，我需要進一步減少要玩的彩票數量。有很多方法可以有效且安全地做到這一點，包括上面提到的 _LIE 消除_選項。

如果我想要更高的中獎頻率，我會把上面的兩個配對結合在一起。現在我會建立一個 _TopGrid3_ ，包含每個選 3 數字和 3 個配對： _5、6、8_ 。函數 _1 = 1 個數字 + 2 對_現在產生了大約 150 個連續的號碼。同樣，我在我的功能強大的軟體中使用了幾種消除法，從而_**清除了**_產生的號碼數量。 （別被海量的選擇嚇到！）

![Missing lotto pairs can reduce drastically total combinations to play.](https://saliu.com/HLINE.gif)

## [<u><span lang="zh-TW"><span data-immersive-translate-translation-element-mark="1"><span data-immersive-translate-translation-element-mark="1">彩票軟體、系統、策略、樂透<i>奇蹟網格</i>資源</span></span></span></u>](https://saliu.com/content/lottery.html)

它列出了有關彩票、樂透、軟體、系統、策略、樂透輪盤等主題的主要頁面。

這裡有一些對您有用的鏈接，首先是介紹 **Bright 軟體**整合包的頁面。

-   _**全新 BRIGHT3 ：高性能整合**_ [**Pick-3 彩票軟體**](https://saliu.com/lottery3-software.html) 。
-   _**全新 BRIGHT4 ：高性能整合**_ [**Pick-4 彩票軟體**](https://saliu.com/lottery4-software.html) 。
-   _**全新 BRIGHTh3 ：高性能整合**_[**賽馬三重奏軟體**](https://saliu.com/horseracing-software.html) 。
-   _**全新 BRIGHT5 ：高性能整合**_ [**5 號樂透軟體**](https://saliu.com/lotto5-software.html) 。
-   _**全新 BRIGHT6 ：高性能整合**_ [**6 號樂透軟體**](https://saliu.com/lotto6-software.html) 。
-   _**Bright6 的視覺教學與**_[_**視覺教學、書籍、手冊：彩券軟體、樂透應用程式、程式**_](https://saliu.com/forum/lotto-book.html) 。
-   [_**<u>MDIEditor Lotto WE</u> ：彩票軟體手冊、書籍、電子書、幫助**_](https://saliu.com/MDI-lotto-guide.html)  
    ~ 也適用於 LotWon 彩票、樂透軟體（命令提示字元）；以及 Powerball、Mega Millions、Euromillions。
-   [_**實用軟體：Pick-3、4 Lottery、Lotto-5、6、Powerball、Mega Millions、Thunderball、Euromillions**_](https://saliu.com/lottery-utility.html) 。
-   [_**樂透、樂透策略的反向：贏、輸、中、錯**_](https://saliu.com/reverse-strategy.html) 。
-   [_**軟體分析，彩票號碼頻率，彩票對**_](https://saliu.com/lottery-lotto-pairs.html) 。
-   **樂透、強力球、超級百萬、歐洲百萬彩票、賽馬、輪盤、足球** ： [**<u> 軟體、系統、彩票跳過 </u>**](https://saliu.com/skip-strategy.html) 。
-   [_**樂透，幾十年：系統、軟體、分析、策略**_](https://saliu.com/decades.html) 。
-   [_**樂透、樂透策略是基於總數（總和）、奇數 - 偶數、低數 - 高數**_](https://saliu.com/strategy.html) 。
-   [_**彩票策略，基於數字頻率的系統**_](https://saliu.com/frequency-lottery.html) 。
-   [_**樂透數位組軟體：奇數、偶數、低、高、總和、頻率、使用者組**_](https://saliu.com/lotto-groups.html) 。
-   [_**有史以來最好的彩票策略、樂透策略**_](https://software.saliu.com/lottery-strategy-lotto-strategies.html) 。

![The lottery software groups pick lotteries, horse racing, jackpot lotto games.](https://saliu.com/HLINE.gif)

**[首頁](https://saliu.com/index.htm) | [搜尋](https://saliu.com/Search.htm) | [新文章](https://saliu.com/bbs/index.html) | [基本公式](https://saliu.com/formula.htm) | [賠率產生器](https://saliu.com/calculator_generator.html) | [目錄](https://saliu.com/content/index.html) | [論壇](https://forums.saliu.com/) | [網站地圖](https://saliu.com/sitemap/index.html)**

![Get source code to generate lotto combinations from 3 groups of numbers.](https://saliu.com/HLINE.gif)
