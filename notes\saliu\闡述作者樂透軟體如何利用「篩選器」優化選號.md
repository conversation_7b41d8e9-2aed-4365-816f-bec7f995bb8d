Ion Saliu 的樂透軟體利用「篩選器」來優化選號，其核心目標是**大幅減少需要投注的樂透組合數量，從而提高中獎機率並降低投注成本**。這套系統基於動態數學濾鏡理論，而非靜態規則。

以下是該軟體如何運用篩選器優化選號的闡述：

- **篩選器的本質與目的**：
    
    - 篩選器本質上是**「限制」**，用於在彩票軟體生成組合的過程中**淘汰不符合條件的組合**。它們可以設定「最低級別」（只允許高於該級別的組合）或「最高級別」（只允許低於該級別的組合）。
    - 目標是**消除大量的樂透組合**，從而顯著降低投注成本並潛在地提高獲勝機會。
- **資料檔案的重要性**：
    
    - 篩選器分析的準確性**極度依賴於足夠大的資料檔案** (`D*` 檔案)，這些檔案通常由真實開獎結果 (`DATA-*`) 和模擬開獎結果 (`SIM-*`) 合併而成，且模擬檔案必須經過隨機化處理以避免報告錯誤。例如，`Bright6` 需要至少 1,200 萬行的 `D6` 資料檔案。
- **篩選器類型與設定方法**：
    
    - **統計報告生成**：使用者首先透過 `Bright` 或 `MDIEditor Lotto WE` 等軟體生成多種統計報告（如 `W*` 和 `MD*` 報告）。這些報告顯示了各種篩選器的統計參數，是制定策略的基礎。通常建議生成 1000 期或 2000 期報告以發現更多有利策略。
    - **關鍵統計數據**：報告會顯示每個篩選器的**中位數 (median)**、平均值 (average) 和標準差 (standard deviation)。**中位數是設定篩選器等級的關鍵**，它代表了資料串列的中間值，50% 的數值在中位數或以下，50% 在中位數或以上。
    - **圍繞中位數設定**：例如，如果某篩選器的中位數為 4，可以設定 `minimum = 4` 和 `maximum = 5`。
    - **嚴格等級設定**：可以將少數篩選器設定到其正常範圍之外（例如，中位數乘以 3、4 甚至 5；或除以 3、4 甚至 5），這可以消除大量的樂透組合，但這種嚴格等級不應在每次開獎時都使用。
    - **趨勢觀察**：報告會顯示篩選器值相較於前一次開獎是升高 (+) 還是降低 (-)。軟體會顯示篩選器值的變動趨勢，通常在 2 或 3 個連續的相同趨勢後會反轉。這可用於設定最小或最大等級，以期望下一次抽獎的反向趨勢。
    - **LIE 消除 (Reversed Strategy)**：這是一種獨特的**反向策略**，**旨在故意設定預計不會中獎的過濾器**，從而消除大量低機率組合，以減少投注成本並從「虧損」中獲利。`Bright5.exe` 和 `Bright6` 等軟體中都實現了此功能。其基礎是邏輯定律「否定的否定就是肯定」。
    - **Purge (清除)**：在生成大量組合後，可以使用 `Purge` 功能進一步減少要投注的票數，透過應用額外的篩選器來淘汰不想要的組合。
    - **其他常見篩選器**：
        - **ONE, TWO, THREE, FOUR, FIVE, SIX 篩選器**：根據組合中重複出現的號碼組數量來進行過濾。
        - **Delta 過濾器**：分析相鄰號碼之間的差異 (`Del#1` 到 `Del#5`)。設定較高的 Delta 值可以大幅減少組合，因為極大的 Delta 值很少出現。
        - **跳躍系統 (Skip Systems)**：分析號碼兩次出現之間的間隔次數 (`skip`)。軟體可以識別出符合特定跳躍模式的號碼。
        - **Wonder Grid 策略**：基於號碼配對頻率的策略，例如選擇最常出現的配對。
        - **十位數 (Decades)**、**個位數 (Last Digits)**、**奇偶 (Odd Even)**、**高低 (Low High)**、**總和 (Sums)**、**使用者群組 (User's Groups)**、**頻率 (Frequency)**、**位置頻率 (Positional Frequency)** 等。
        - **固定位置的偏好號碼**：軟體可以生成包含使用者最喜歡號碼在固定位置的樂透組合。
- **策略組合與優化**：
    
    - **多策略並行**：作者建議不要只玩一個策略，而是**同時玩多個策略層**，甚至合併多個輸出檔案，即使有重複項也不清除，因為這些策略有時會同時生效。
    - **策略檢查**：使用軟體中的「策略檢查」(Check Strategies) 功能，分析特定濾鏡設定組合在過去開獎中的表現，了解其命中次數。
    - **交叉引用**：`FileLines` 等工具用於交叉引用不同軟體平台生成的策略文件，將它們合併為更全面的策略。
- **基礎數學理論**：
    
    - Ion Saliu 的彩票策略和軟體基於其數學理論，尤其是**賭博基本公式 (Fundamental Formula of Gambling, FFG)**。FFG 定義了確定性程度 (DC)、機率 (p) 和試驗次數 (N) 之間的關係。FFG 被認為是遊戲理論中最精確的工具，它考慮了先前事件對未來事件的影響，與馬可夫鏈不同。
    - **FFG 中位數**：該理論也用於計算篩選器的**理論中位數**，作為判斷趨勢和設定篩選器的依據。
    - **大數定律**：強調重複生成大量組合的重要性，以提高命中率。
    - **Ion Saliu 的 N 次試驗悖論 (Ion Saliu's Paradox of N Trials)**：這是一個重要的數學公式，它表明即使一個事件的機率是 1/N，在 N 次試驗中發生的確定性程度也不是 100%，而是趨近於 `1 - 1/e` (約 63.2%)。
- **相關軟體工具**：
    
    - **Bright** 和 **Ultimate Software** 系列：這些是整合性軟體包，包含執行上述所有功能所需的程式。
    - **MDIEditor Lotto WE**：一款功能全面的彩票軟體，用於資料管理、統計分析、報告生成和組合生成。
    - **Super Utilities**：包含多種實用工具，如模擬檔案生成、統計報告、組合生成以及 `Make/Break/Position` 功能，對於建立大型 `D*` 檔案和處理組合至關重要。
    - **SortFilterReports**：專門用於對濾鏡報告進行排序。
    - **FileLines**：用於交叉引用不同軟體平台生成的策略文件。
    - **SkipSystem**：用於創建基於跳躍模式的策略。
    - **SuperFormula**：彩票數學和機率論的權威軟體，用於計算 FFG、中位數、賠率、標準差等。
    - **Shuffle** 和 **PermuteCombine**：用於生成所有組合並隨機化資料檔案。
    - **Notepad++**：強烈建議使用的免費文字編輯器，用於檢視和編輯大型報告檔案，並方便複製貼上檔案名和資料。

透過運用這些動態篩選器、精確的資料分析和進階策略（如 LIE 消除），玩家可以大幅減少需要投注的樂透組合數量，理論上從機率上提高了中獎的機會，並將「虧損」轉化為潛在利潤。然而，**整個宇宙沒有絕對的確定性**，這是一個不可否認的數學定律 (FFG)。因此，持續的耐心和勤奮，以及對每次開獎結果的監控是成功的關鍵。