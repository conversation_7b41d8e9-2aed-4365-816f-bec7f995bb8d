# Performance reporting system for Wonder Grid Lottery System

"""
Performance report structure
"""
struct PerformanceReport
    strategy_name::String
    key_number::Int
    test_period::<PERSON><PERSON>{Date, Date}
    total_draws::Int
    total_combinations::Int
    
    # Hit statistics
    hit_rates::HitRates
    total_hits::Dict{String, Int}
    
    # Efficiency metrics
    efficiency_ratios::Dict{String, Float64}
    random_comparison::EfficiencyComparison
    
    # Cost analysis
    cost_analysis::CostAnalysis
    
    # Performance metrics
    generation_time::Float64
    backtest_time::Float64
    total_processing_time::Float64
    
    # Quality metrics
    success_rate::Float64
    data_integrity_score::Float64
    
    # Additional insights
    insights::Dict{String, Any}
end

"""
Comprehensive performance report generator
"""
struct PerformanceReporter
    engine::WonderGridEngine
    backtesting_engine::BacktestingEngine
    lie_engine::Union{LIEEliminationEngine, Nothing}
    
    function PerformanceReporter(engine::WonderGridEngine, backtesting_engine::BacktestingEngine, lie_engine=nothing)
        new(engine, backtesting_engine, lie_engine)
    end
end

"""
Generate comprehensive performance report for a key number
"""
function generate_performance_report(reporter::PerformanceReporter, key_number::Int, test_draws::Vector{LotteryDraw})::PerformanceReport
    start_time = time()
    
    # Generate combinations
    gen_start = time()
    combinations = generate_combinations(reporter.engine, key_number)
    generation_time = time() - gen_start
    
    # Run backtest
    backtest_start = time()
    backtest_result = run_backtest(reporter.backtesting_engine, combinations, test_draws)
    backtest_time = time() - backtest_start
    
    # Compare to random
    random_comparison = compare_to_random(reporter.backtesting_engine, backtest_result)
    
    # Calculate total hits
    total_tests = length(combinations) * length(test_draws)
    total_hits = Dict{String, Int}(
        "3/5" => round(Int, backtest_result.hit_rates["3/5"] * total_tests),
        "4/5" => round(Int, backtest_result.hit_rates["4/5"] * total_tests),
        "5/5" => round(Int, backtest_result.hit_rates["5/5"] * total_tests)
    )
    
    # Calculate quality metrics
    success_rate = 1.0  # Assume success if we got here
    data_integrity_score = calculate_data_integrity_score(combinations, test_draws)
    
    # Generate insights
    insights = generate_insights(key_number, backtest_result, random_comparison, combinations, test_draws)
    
    total_processing_time = time() - start_time
    
    # Get test period
    test_period = if !isempty(test_draws)
        (minimum([d.draw_date for d in test_draws]), maximum([d.draw_date for d in test_draws]))
    else
        (Date("2022-01-01"), Date("2022-01-01"))
    end
    
    return PerformanceReport(
        "Wonder Grid",
        key_number,
        test_period,
        length(test_draws),
        length(combinations),
        HitRates(backtest_result.hit_rates["3/5"], backtest_result.hit_rates["4/5"], backtest_result.hit_rates["5/5"]),
        total_hits,
        backtest_result.efficiency_ratios,
        random_comparison,
        backtest_result.cost_analysis,
        generation_time,
        backtest_time,
        total_processing_time,
        success_rate,
        data_integrity_score,
        insights
    )
end

"""
Calculate data integrity score
"""
function calculate_data_integrity_score(combinations::Vector{Vector{Int}}, test_draws::Vector{LotteryDraw})::Float64
    score = 1.0
    
    # Check combination validity
    valid_combinations = all(combo -> length(combo) == 5 && all(n -> 1 <= n <= 39, combo) && length(unique(combo)) == 5, combinations)
    if !valid_combinations
        score -= 0.3
    end
    
    # Check for duplicates
    unique_combinations = length(unique(combinations)) == length(combinations)
    if !unique_combinations
        score -= 0.2
    end
    
    # Check test draw validity
    valid_draws = all(draw -> length(draw.numbers) == 5 && all(n -> 1 <= n <= 39, draw.numbers), test_draws)
    if !valid_draws
        score -= 0.3
    end
    
    # Check chronological order
    if length(test_draws) > 1
        chronological = all(i -> test_draws[i].draw_date >= test_draws[i+1].draw_date, 1:length(test_draws)-1)
        if !chronological
            score -= 0.2
        end
    end
    
    return max(0.0, score)
end

"""
Generate insights from performance data
"""
function generate_insights(key_number::Int, backtest_result::BacktestResult, random_comparison::EfficiencyComparison, 
                          combinations::Vector{Vector{Int}}, test_draws::Vector{LotteryDraw})::Dict{String, Any}
    insights = Dict{String, Any}()
    
    # Performance insights
    best_tier = ""
    best_ratio = 0.0
    for (tier, ratio) in random_comparison.efficiency_ratios
        if ratio > best_ratio
            best_ratio = ratio
            best_tier = tier
        end
    end
    
    insights["best_performing_tier"] = best_tier
    insights["best_efficiency_ratio"] = best_ratio
    
    # Hit analysis
    total_hits = sum(values(backtest_result.hit_rates))
    insights["total_hit_rate"] = total_hits
    insights["most_frequent_tier"] = argmax(backtest_result.hit_rates)
    
    # Cost effectiveness
    cost_per_hit = backtest_result.cost_analysis.total_cost / max(1, round(Int, total_hits * length(combinations) * length(test_draws)))
    insights["cost_per_hit"] = cost_per_hit
    
    # Strategy assessment
    if best_ratio > 10.0
        insights["strategy_assessment"] = "Excellent"
    elseif best_ratio > 5.0
        insights["strategy_assessment"] = "Very Good"
    elseif best_ratio > 2.0
        insights["strategy_assessment"] = "Good"
    elseif best_ratio > 1.0
        insights["strategy_assessment"] = "Fair"
    else
        insights["strategy_assessment"] = "Poor"
    end
    
    # Jackpot analysis
    jackpot_rate = backtest_result.hit_rates["5/5"]
    if jackpot_rate > 0
        insights["jackpot_achieved"] = true
        insights["jackpot_frequency"] = 1.0 / jackpot_rate
    else
        insights["jackpot_achieved"] = false
        insights["estimated_jackpot_frequency"] = "Not achieved in test period"
    end
    
    # Recommendation
    if best_ratio > 5.0 && backtest_result.hit_rates["4/5"] > 0.001
        insights["recommendation"] = "Highly recommended for play"
    elseif best_ratio > 2.0
        insights["recommendation"] = "Recommended for play"
    elseif best_ratio > 1.0
        insights["recommendation"] = "Consider for play"
    else
        insights["recommendation"] = "Not recommended"
    end
    
    return insights
end

"""
Display formatted performance report
"""
function display_performance_report(report::PerformanceReport)
    println("=" ^ 60)
    println("WONDER GRID PERFORMANCE REPORT")
    println("=" ^ 60)
    
    println("Strategy: $(report.strategy_name)")
    println("Key Number: $(report.key_number)")
    println("Test Period: $(report.test_period[1]) to $(report.test_period[2])")
    println("Test Duration: $(report.total_draws) draws")
    println("Combinations Tested: $(report.total_combinations)")
    
    println("\n" * "-" ^ 40)
    println("HIT RATE ANALYSIS")
    println("-" ^ 40)
    
    println("Hit Rates:")
    println("  3/5 matches: $(round(report.hit_rates.three_of_five * 100, digits=4))%")
    println("  4/5 matches: $(round(report.hit_rates.four_of_five * 100, digits=4))%")
    println("  5/5 matches: $(round(report.hit_rates.five_of_five * 100, digits=4))%")
    
    println("\nTotal Hits:")
    println("  3/5 matches: $(report.total_hits["3/5"]) hits")
    println("  4/5 matches: $(report.total_hits["4/5"]) hits")
    println("  5/5 matches: $(report.total_hits["5/5"]) hits")
    
    println("\n" * "-" ^ 40)
    println("EFFICIENCY ANALYSIS")
    println("-" ^ 40)
    
    println("Strategy vs Random Play:")
    for (tier, ratio) in report.random_comparison.efficiency_ratios
        strategy_odds = report.random_comparison.strategy_odds[tier] * 100
        random_odds = report.random_comparison.random_odds[tier] * 100
        
        if ratio > 1.0
            println("  $tier: $(round(ratio, digits=2))x BETTER ($(round(strategy_odds, digits=4))% vs $(round(random_odds, digits=6))%)")
        elseif ratio < 1.0
            println("  $tier: $(round(ratio, digits=2))x worse ($(round(strategy_odds, digits=4))% vs $(round(random_odds, digits=6))%)")
        else
            println("  $tier: Equal performance")
        end
    end
    
    println("\n" * "-" ^ 40)
    println("COST ANALYSIS")
    println("-" ^ 40)
    
    println("Investment Analysis:")
    println("  Total combinations: $(report.cost_analysis.total_combinations)")
    println("  Cost per combination: \$$(report.cost_analysis.cost_per_combination)")
    println("  Total investment: \$$(report.cost_analysis.total_cost)")
    println("  Cost per hit: \$$(round(report.insights["cost_per_hit"], digits=2))")
    
    println("\n" * "-" ^ 40)
    println("PERFORMANCE METRICS")
    println("-" ^ 40)
    
    println("Processing Performance:")
    println("  Combination generation: $(round(report.generation_time, digits=3)) seconds")
    println("  Backtesting: $(round(report.backtest_time, digits=3)) seconds")
    println("  Total processing: $(round(report.total_processing_time, digits=3)) seconds")
    println("  Success rate: $(round(report.success_rate * 100, digits=1))%")
    println("  Data integrity: $(round(report.data_integrity_score * 100, digits=1))%")
    
    println("\n" * "-" ^ 40)
    println("STRATEGIC INSIGHTS")
    println("-" ^ 40)
    
    println("Key Findings:")
    println("  Best performing tier: $(report.insights["best_performing_tier"])")
    println("  Best efficiency ratio: $(round(report.insights["best_efficiency_ratio"], digits=2))x")
    println("  Strategy assessment: $(report.insights["strategy_assessment"])")
    println("  Jackpot achieved: $(report.insights["jackpot_achieved"] ? "YES" : "NO")")
    
    if report.insights["jackpot_achieved"]
        println("  Jackpot frequency: 1 in $(round(report.insights["jackpot_frequency"], digits=0))")
    else
        println("  Jackpot status: $(report.insights["estimated_jackpot_frequency"])")
    end
    
    println("\n" * "-" ^ 40)
    println("RECOMMENDATION")
    println("-" ^ 40)
    
    println("$(report.insights["recommendation"])")
    
    # Add recommendation reasoning
    best_ratio = report.insights["best_efficiency_ratio"]
    if best_ratio > 10.0
        println("Reasoning: Exceptional performance with >10x improvement over random play")
    elseif best_ratio > 5.0
        println("Reasoning: Strong performance with >5x improvement over random play")
    elseif best_ratio > 2.0
        println("Reasoning: Good performance with >2x improvement over random play")
    elseif best_ratio > 1.0
        println("Reasoning: Modest improvement over random play")
    else
        println("Reasoning: Performance does not exceed random play")
    end
    
    println("\n" * "=" ^ 60)
end

"""
Generate comparative report for multiple key numbers
"""
function generate_comparative_report(reporter::PerformanceReporter, key_numbers::Vector{Int}, test_draws::Vector{LotteryDraw})::Vector{PerformanceReport}
    reports = PerformanceReport[]
    
    println("Generating comparative performance report for $(length(key_numbers)) key numbers...")
    
    for (i, key_number) in enumerate(key_numbers)
        print("  Processing key number $key_number ($i/$(length(key_numbers)))... ")
        
        try
            report = generate_performance_report(reporter, key_number, test_draws)
            push!(reports, report)
            println("✓")
        catch e
            println("✗ Error: $e")
        end
    end
    
    return reports
end

"""
Display comparative analysis of multiple reports
"""
function display_comparative_analysis(reports::Vector{PerformanceReport})
    if isempty(reports)
        println("No reports to analyze")
        return
    end
    
    println("\n" * "=" ^ 80)
    println("COMPARATIVE PERFORMANCE ANALYSIS")
    println("=" ^ 80)
    
    # Summary table
    println("Performance Summary:")
    println("Key | 3/5 Rate | 4/5 Rate | 5/5 Rate | Best Ratio | Assessment | Recommendation")
    println("-" ^ 80)
    
    for report in reports
        key = report.key_number
        rate_3_5 = round(report.hit_rates.three_of_five * 100, digits=3)
        rate_4_5 = round(report.hit_rates.four_of_five * 100, digits=3)
        rate_5_5 = round(report.hit_rates.five_of_five * 100, digits=3)
        best_ratio = round(report.insights["best_efficiency_ratio"], digits=1)
        assessment = report.insights["strategy_assessment"]
        recommendation = report.insights["recommendation"] == "Highly recommended for play" ? "HIGH" :
                        report.insights["recommendation"] == "Recommended for play" ? "MED" :
                        report.insights["recommendation"] == "Consider for play" ? "LOW" : "NO"
        
        println("$(lpad(key, 3)) | $(lpad(rate_3_5, 8))% | $(lpad(rate_4_5, 8))% | $(lpad(rate_5_5, 8))% | $(lpad(best_ratio, 10))x | $(lpad(assessment, 10)) | $(lpad(recommendation, 12))")
    end
    
    # Rankings
    println("\n" * "-" ^ 50)
    println("TOP PERFORMERS")
    println("-" ^ 50)
    
    # Sort by best efficiency ratio
    sorted_reports = sort(reports, by = r -> r.insights["best_efficiency_ratio"], rev = true)
    
    println("Top 5 by Efficiency Ratio:")
    for i in 1:min(5, length(sorted_reports))
        report = sorted_reports[i]
        ratio = round(report.insights["best_efficiency_ratio"], digits=2)
        tier = report.insights["best_performing_tier"]
        println("  $i. Key $(report.key_number): $(ratio)x better ($tier tier)")
    end
    
    # Sort by 4/5 hit rate (most practical tier)
    sorted_by_4_5 = sort(reports, by = r -> r.hit_rates.four_of_five, rev = true)
    
    println("\nTop 5 by 4/5 Hit Rate:")
    for i in 1:min(5, length(sorted_by_4_5))
        report = sorted_by_4_5[i]
        rate = round(report.hit_rates.four_of_five * 100, digits=4)
        hits = report.total_hits["4/5"]
        println("  $i. Key $(report.key_number): $(rate)% ($hits hits)")
    end
    
    # Jackpot achievers
    jackpot_achievers = filter(r -> r.insights["jackpot_achieved"], reports)
    
    if !isempty(jackpot_achievers)
        println("\nJackpot Achievers:")
        for report in jackpot_achievers
            rate = round(report.hit_rates.five_of_five * 100, digits=4)
            hits = report.total_hits["5/5"]
            println("  Key $(report.key_number): $(rate)% ($hits hits)")
        end
    else
        println("\nJackpot Achievers: None in test period")
    end
    
    # Overall statistics
    println("\n" * "-" ^ 50)
    println("OVERALL STATISTICS")
    println("-" ^ 50)
    
    avg_3_5 = mean([r.hit_rates.three_of_five for r in reports]) * 100
    avg_4_5 = mean([r.hit_rates.four_of_five for r in reports]) * 100
    avg_5_5 = mean([r.hit_rates.five_of_five for r in reports]) * 100
    avg_ratio = mean([r.insights["best_efficiency_ratio"] for r in reports])
    
    println("Average Performance:")
    println("  3/5 hit rate: $(round(avg_3_5, digits=3))%")
    println("  4/5 hit rate: $(round(avg_4_5, digits=3))%")
    println("  5/5 hit rate: $(round(avg_5_5, digits=3))%")
    println("  Average efficiency ratio: $(round(avg_ratio, digits=2))x")
    
    # Recommendations
    highly_recommended = count(r -> r.insights["recommendation"] == "Highly recommended for play", reports)
    recommended = count(r -> r.insights["recommendation"] == "Recommended for play", reports)
    consider = count(r -> r.insights["recommendation"] == "Consider for play", reports)
    not_recommended = count(r -> r.insights["recommendation"] == "Not recommended", reports)
    
    println("\nRecommendation Distribution:")
    println("  Highly recommended: $highly_recommended")
    println("  Recommended: $recommended")
    println("  Consider: $consider")
    println("  Not recommended: $not_recommended")
    
    println("\n" * "=" ^ 80)
end

"""
Export performance report to file
"""
function export_performance_report(report::PerformanceReport, filename::String)
    open(filename, "w") do file
        println(file, "Wonder Grid Performance Report")
        println(file, "Generated: $(Dates.now())")
        println(file, "")
        println(file, "Strategy: $(report.strategy_name)")
        println(file, "Key Number: $(report.key_number)")
        println(file, "Test Period: $(report.test_period[1]) to $(report.test_period[2])")
        println(file, "Test Duration: $(report.total_draws) draws")
        println(file, "Combinations Tested: $(report.total_combinations)")
        println(file, "")
        println(file, "Hit Rates:")
        println(file, "3/5,$(report.hit_rates.three_of_five)")
        println(file, "4/5,$(report.hit_rates.four_of_five)")
        println(file, "5/5,$(report.hit_rates.five_of_five)")
        println(file, "")
        println(file, "Efficiency Ratios:")
        for (tier, ratio) in report.random_comparison.efficiency_ratios
            println(file, "$tier,$(ratio)")
        end
        println(file, "")
        println(file, "Performance Metrics:")
        println(file, "Generation Time,$(report.generation_time)")
        println(file, "Backtest Time,$(report.backtest_time)")
        println(file, "Total Processing Time,$(report.total_processing_time)")
        println(file, "Success Rate,$(report.success_rate)")
        println(file, "Data Integrity Score,$(report.data_integrity_score)")
        println(file, "")
        println(file, "Recommendation: $(report.insights["recommendation"])")
    end
end

"""
Generate comprehensive statistical report with theoretical vs empirical analysis
"""
function generate_statistical_report(reporter::PerformanceReporter, key_number::Int, 
                                   test_draws::Vector{LotteryDraw})::Dict{String, Any}
    # Generate performance report first
    performance_report = generate_performance_report(reporter, key_number, test_draws)
    
    # Calculate theoretical probabilities for Lotto 5/39
    theoretical_probs = calculate_theoretical_probabilities()
    
    # Calculate empirical probabilities from our strategy
    empirical_probs = Dict{String, Float64}(
        "3/5" => performance_report.hit_rates.three_of_five,
        "4/5" => performance_report.hit_rates.four_of_five,
        "5/5" => performance_report.hit_rates.five_of_five
    )
    
    # Calculate confidence intervals
    confidence_intervals = calculate_confidence_intervals(empirical_probs, length(test_draws))
    
    # Statistical significance tests
    significance_tests = perform_significance_tests(empirical_probs, theoretical_probs, length(test_draws))
    
    # Expected value analysis
    expected_value_analysis = calculate_expected_value_analysis(empirical_probs, theoretical_probs)
    
    # Risk analysis
    risk_analysis = calculate_risk_analysis(performance_report, test_draws)
    
    return Dict{String, Any}(
        "performance_report" => performance_report,
        "theoretical_probabilities" => theoretical_probs,
        "empirical_probabilities" => empirical_probs,
        "confidence_intervals" => confidence_intervals,
        "significance_tests" => significance_tests,
        "expected_value_analysis" => expected_value_analysis,
        "risk_analysis" => risk_analysis,
        "statistical_summary" => generate_statistical_summary(performance_report, empirical_probs, theoretical_probs)
    )
end

"""
Calculate theoretical probabilities for Lotto 5/39
"""
function calculate_theoretical_probabilities()::Dict{String, Float64}
    total_combinations = binomial(39, 5)  # 575,757
    
    # Exact theoretical probabilities for single combination
    prob_5_of_5 = 1.0 / total_combinations
    prob_4_of_5 = (binomial(5, 4) * binomial(34, 1)) / total_combinations
    prob_3_of_5 = (binomial(5, 3) * binomial(34, 2)) / total_combinations
    
    return Dict{String, Float64}(
        "3/5" => prob_3_of_5,
        "4/5" => prob_4_of_5,
        "5/5" => prob_5_of_5,
        "total_combinations" => Float64(total_combinations)
    )
end

"""
Calculate confidence intervals for empirical probabilities
"""
function calculate_confidence_intervals(empirical_probs::Dict{String, Float64}, 
                                      sample_size::Int, confidence_level::Float64 = 0.95)::Dict{String, Tuple{Float64, Float64}}
    # Using normal approximation for binomial confidence intervals
    z_score = confidence_level == 0.95 ? 1.96 : 
              confidence_level == 0.99 ? 2.576 : 1.645  # 90% default
    
    confidence_intervals = Dict{String, Tuple{Float64, Float64}}()
    
    for (tier, prob) in empirical_probs
        if tier in ["3/5", "4/5", "5/5"]
            # Standard error for proportion
            se = sqrt(prob * (1 - prob) / sample_size)
            margin_of_error = z_score * se
            
            lower_bound = max(0.0, prob - margin_of_error)
            upper_bound = min(1.0, prob + margin_of_error)
            
            confidence_intervals[tier] = (lower_bound, upper_bound)
        end
    end
    
    return confidence_intervals
end

"""
Perform statistical significance tests
"""
function perform_significance_tests(empirical_probs::Dict{String, Float64}, 
                                  theoretical_probs::Dict{String, Float64}, 
                                  sample_size::Int)::Dict{String, Dict{String, Any}}
    significance_tests = Dict{String, Dict{String, Any}}()
    
    for tier in ["3/5", "4/5", "5/5"]
        empirical = empirical_probs[tier]
        theoretical = theoretical_probs[tier]
        
        # Z-test for proportion
        if theoretical > 0 && theoretical < 1
            se = sqrt(theoretical * (1 - theoretical) / sample_size)
            z_score = (empirical - theoretical) / se
            
            # Two-tailed p-value (approximate)
            p_value = 2 * (1 - cdf_standard_normal(abs(z_score)))
            
            significance_tests[tier] = Dict{String, Any}(
                "z_score" => z_score,
                "p_value" => p_value,
                "is_significant_05" => p_value < 0.05,
                "is_significant_01" => p_value < 0.01,
                "effect_size" => (empirical - theoretical) / theoretical,
                "interpretation" => interpret_significance_test(z_score, p_value)
            )
        else
            significance_tests[tier] = Dict{String, Any}(
                "error" => "Cannot perform test - theoretical probability too extreme"
            )
        end
    end
    
    return significance_tests
end

"""
Approximate cumulative distribution function for standard normal
"""
function cdf_standard_normal(z::Float64)::Float64
    # Approximation using error function
    return 0.5 * (1 + erf(z / sqrt(2)))
end

"""
Interpret significance test results
"""
function interpret_significance_test(z_score::Float64, p_value::Float64)::String
    if p_value < 0.01
        direction = z_score > 0 ? "significantly better" : "significantly worse"
        return "Strategy performs $direction than random (p < 0.01)"
    elseif p_value < 0.05
        direction = z_score > 0 ? "significantly better" : "significantly worse"
        return "Strategy performs $direction than random (p < 0.05)"
    elseif p_value < 0.10
        direction = z_score > 0 ? "marginally better" : "marginally worse"
        return "Strategy performs $direction than random (p < 0.10)"
    else
        return "No significant difference from random play"
    end
end

"""
Calculate expected value analysis with prize structure
"""
function calculate_expected_value_analysis(empirical_probs::Dict{String, Float64}, 
                                         theoretical_probs::Dict{String, Float64})::Dict{String, Any}
    # Typical prize structure for Lotto 5/39 (adjustable)
    prize_structure = Dict{String, Float64}(
        "3/5" => 10.0,      # $10 for 3 matches
        "4/5" => 100.0,     # $100 for 4 matches  
        "5/5" => 100000.0   # $100,000 for 5 matches (jackpot)
    )
    
    # Calculate expected values
    strategy_ev = sum(empirical_probs[tier] * prize_structure[tier] for tier in ["3/5", "4/5", "5/5"])
    random_ev = sum(theoretical_probs[tier] * prize_structure[tier] for tier in ["3/5", "4/5", "5/5"])
    
    # Cost per combination (typically $1)
    cost_per_combination = 1.0
    
    # Net expected value
    strategy_net_ev = strategy_ev - cost_per_combination
    random_net_ev = random_ev - cost_per_combination
    
    # Return on investment
    strategy_roi = (strategy_ev / cost_per_combination - 1) * 100
    random_roi = (random_ev / cost_per_combination - 1) * 100
    
    return Dict{String, Any}(
        "prize_structure" => prize_structure,
        "strategy_expected_value" => strategy_ev,
        "random_expected_value" => random_ev,
        "expected_value_improvement" => strategy_ev - random_ev,
        "strategy_net_expected_value" => strategy_net_ev,
        "random_net_expected_value" => random_net_ev,
        "strategy_roi_percentage" => strategy_roi,
        "random_roi_percentage" => random_roi,
        "roi_improvement" => strategy_roi - random_roi,
        "break_even_analysis" => analyze_break_even(strategy_ev, cost_per_combination),
        "recommendation" => strategy_ev > random_ev ? 
            "Strategy has positive expected value advantage" : 
            "Strategy does not improve expected value over random"
    )
end

"""
Analyze break-even scenarios
"""
function analyze_break_even(expected_value::Float64, cost_per_combination::Float64)::Dict{String, Any}
    if expected_value >= cost_per_combination
        return Dict{String, Any}(
            "is_profitable" => true,
            "profit_per_combination" => expected_value - cost_per_combination,
            "break_even_plays" => 1,
            "message" => "Strategy is immediately profitable"
        )
    else
        # Calculate how many plays needed to break even on average
        loss_per_play = cost_per_combination - expected_value
        if expected_value > 0
            break_even_plays = cost_per_combination / expected_value
            return Dict{String, Any}(
                "is_profitable" => false,
                "loss_per_combination" => loss_per_play,
                "break_even_plays" => break_even_plays,
                "message" => "Need $(round(break_even_plays, digits=0)) plays on average to break even"
            )
        else
            return Dict{String, Any}(
                "is_profitable" => false,
                "loss_per_combination" => loss_per_play,
                "break_even_plays" => Inf,
                "message" => "Strategy has negative expected value - not profitable"
            )
        end
    end
end

"""
Calculate risk analysis
"""
function calculate_risk_analysis(performance_report::PerformanceReport, 
                               test_draws::Vector{LotteryDraw})::Dict{String, Any}
    # Volatility analysis
    hit_rates = [performance_report.hit_rates.three_of_five, 
                performance_report.hit_rates.four_of_five, 
                performance_report.hit_rates.five_of_five]
    
    volatility = std(hit_rates)
    
    # Drawdown analysis (periods without wins)
    drawdown_analysis = analyze_drawdowns(performance_report, test_draws)
    
    # Risk metrics
    total_investment = performance_report.cost_analysis.total_cost
    max_loss = total_investment  # Worst case: lose everything
    
    # Value at Risk (VaR) - simplified
    var_95 = calculate_value_at_risk(performance_report, 0.95)
    
    return Dict{String, Any}(
        "volatility" => volatility,
        "max_potential_loss" => max_loss,
        "value_at_risk_95" => var_95,
        "drawdown_analysis" => drawdown_analysis,
        "risk_level" => classify_risk_level(volatility, var_95, total_investment),
        "risk_recommendation" => generate_risk_recommendation(volatility, var_95, total_investment)
    )
end

"""
Analyze drawdown periods
"""
function analyze_drawdowns(performance_report::PerformanceReport, 
                         test_draws::Vector{LotteryDraw})::Dict{String, Any}
    # This is a simplified analysis - in practice would need detailed draw-by-draw results
    total_draws = length(test_draws)
    total_hits = sum(values(performance_report.total_hits))
    
    if total_hits == 0
        return Dict{String, Any}(
            "max_drawdown_period" => total_draws,
            "average_time_between_wins" => Inf,
            "longest_losing_streak" => total_draws,
            "analysis" => "No wins in test period"
        )
    end
    
    average_time_between_wins = total_draws / total_hits
    estimated_max_drawdown = average_time_between_wins * 2  # Rough estimate
    
    return Dict{String, Any}(
        "estimated_max_drawdown_period" => estimated_max_drawdown,
        "average_time_between_wins" => average_time_between_wins,
        "risk_assessment" => average_time_between_wins > 100 ? "High Risk" : 
                           average_time_between_wins > 50 ? "Medium Risk" : "Low Risk"
    )
end

"""
Calculate Value at Risk (VaR)
"""
function calculate_value_at_risk(performance_report::PerformanceReport, confidence_level::Float64)::Float64
    # Simplified VaR calculation based on hit rates
    total_cost = performance_report.cost_analysis.total_cost
    total_hit_rate = performance_report.hit_rates.three_of_five + 
                    performance_report.hit_rates.four_of_five + 
                    performance_report.hit_rates.five_of_five
    
    # Estimate potential loss at given confidence level
    if total_hit_rate > 0
        # Probability of no wins in a series
        no_win_probability = (1 - total_hit_rate) ^ 10  # Over 10 plays
        var = total_cost * no_win_probability * confidence_level
    else
        var = total_cost * confidence_level
    end
    
    return var
end

"""
Classify risk level
"""
function classify_risk_level(volatility::Float64, var::Float64, total_investment::Float64)::String
    var_ratio = var / total_investment
    
    if volatility > 0.1 || var_ratio > 0.8
        return "High Risk"
    elseif volatility > 0.05 || var_ratio > 0.5
        return "Medium Risk"
    else
        return "Low Risk"
    end
end

"""
Generate risk recommendation
"""
function generate_risk_recommendation(volatility::Float64, var::Float64, total_investment::Float64)::String
    risk_level = classify_risk_level(volatility, var, total_investment)
    
    if risk_level == "High Risk"
        return "High volatility detected. Consider smaller position sizes or diversification."
    elseif risk_level == "Medium Risk"
        return "Moderate risk level. Monitor performance closely and adjust as needed."
    else
        return "Low risk profile. Strategy shows stable performance characteristics."
    end
end

"""
Generate statistical summary
"""
function generate_statistical_summary(performance_report::PerformanceReport, 
                                     empirical_probs::Dict{String, Float64}, 
                                     theoretical_probs::Dict{String, Float64})::Dict{String, Any}
    # Calculate improvement ratios
    improvement_ratios = Dict{String, Float64}()
    for tier in ["3/5", "4/5", "5/5"]
        if theoretical_probs[tier] > 0
            improvement_ratios[tier] = empirical_probs[tier] / theoretical_probs[tier]
        else
            improvement_ratios[tier] = 0.0
        end
    end
    
    # Overall assessment
    avg_improvement = mean(values(improvement_ratios))
    best_improvement = maximum(values(improvement_ratios))
    worst_improvement = minimum(values(improvement_ratios))
    
    # Statistical significance summary
    significant_improvements = count(ratio -> ratio > 1.5, values(improvement_ratios))
    
    return Dict{String, Any}(
        "improvement_ratios" => improvement_ratios,
        "average_improvement" => avg_improvement,
        "best_improvement" => best_improvement,
        "worst_improvement" => worst_improvement,
        "significant_improvements" => significant_improvements,
        "overall_assessment" => avg_improvement > 1.2 ? "Strategy shows improvement" : 
                               avg_improvement > 0.8 ? "Strategy performs similarly" : 
                               "Strategy underperforms",
        "confidence_level" => significant_improvements >= 2 ? "High" : 
                             significant_improvements == 1 ? "Medium" : "Low"
    )
end

"""
Display comprehensive statistical report
"""
function display_statistical_report(statistical_report::Dict{String, Any})
    report = statistical_report["performance_report"]
    theoretical = statistical_report["theoretical_probabilities"]
    empirical = statistical_report["empirical_probabilities"]
    confidence_intervals = statistical_report["confidence_intervals"]
    significance_tests = statistical_report["significance_tests"]
    expected_value = statistical_report["expected_value_analysis"]
    risk_analysis = statistical_report["risk_analysis"]
    summary = statistical_report["statistical_summary"]
    
    println("=" ^ 80)
    println("COMPREHENSIVE STATISTICAL ANALYSIS REPORT")
    println("=" ^ 80)
    
    println("Strategy: $(report.strategy_name)")
    println("Key Number: $(report.key_number)")
    println("Analysis Period: $(report.test_period[1]) to $(report.test_period[2])")
    println("Sample Size: $(report.total_draws) draws")
    println("Combinations Analyzed: $(report.total_combinations)")
    
    println("\n" * "-" ^ 60)
    println("THEORETICAL vs EMPIRICAL PROBABILITY ANALYSIS")
    println("-" ^ 60)
    
    println("Probability Comparison:")
    println("Tier    | Theoretical  | Empirical    | Improvement | 95% Confidence Interval")
    println("-" ^ 75)
    
    for tier in ["3/5", "4/5", "5/5"]
        theo = theoretical[tier] * 100
        emp = empirical[tier] * 100
        improvement = summary["improvement_ratios"][tier]
        ci_lower, ci_upper = confidence_intervals[tier]
        
        println("$(rpad(tier, 7)) | $(rpad(string(round(theo, digits=6)), 12))% | $(rpad(string(round(emp, digits=4)), 12))% | $(rpad(string(round(improvement, digits=2)), 11))x | [$(round(ci_lower*100, digits=4))%, $(round(ci_upper*100, digits=4))%]")
    end
    
    println("\n" * "-" ^ 60)
    println("STATISTICAL SIGNIFICANCE TESTING")
    println("-" ^ 60)
    
    for tier in ["3/5", "4/5", "5/5"]
        if haskey(significance_tests[tier], "z_score")
            test = significance_tests[tier]
            println("$tier Tier Analysis:")
            println("  Z-score: $(round(test["z_score"], digits=3))")
            println("  P-value: $(round(test["p_value"], digits=6))")
            println("  Effect size: $(round(test["effect_size"] * 100, digits=2))%")
            println("  Result: $(test["interpretation"])")
            println()
        end
    end
    
    println("-" ^ 60)
    println("EXPECTED VALUE ANALYSIS")
    println("-" ^ 60)
    
    println("Prize Structure:")
    for (tier, prize) in expected_value["prize_structure"]
        println("  $tier: \$$(prize)")
    end
    
    println("\nExpected Value Comparison:")
    println("  Strategy Expected Value: \$$(round(expected_value["strategy_expected_value"], digits=2))")
    println("  Random Play Expected Value: \$$(round(expected_value["random_expected_value"], digits=2))")
    println("  Improvement: \$$(round(expected_value["expected_value_improvement"], digits=2))")
    
    println("\nReturn on Investment:")
    println("  Strategy ROI: $(round(expected_value["strategy_roi_percentage"], digits=2))%")
    println("  Random Play ROI: $(round(expected_value["random_roi_percentage"], digits=2))%")
    println("  ROI Improvement: $(round(expected_value["roi_improvement"], digits=2))%")
    
    println("\nBreak-Even Analysis:")
    break_even = expected_value["break_even_analysis"]
    println("  $(break_even["message"])")
    if haskey(break_even, "profit_per_combination")
        println("  Profit per combination: \$$(round(break_even["profit_per_combination"], digits=2))")
    elseif haskey(break_even, "loss_per_combination")
        println("  Loss per combination: \$$(round(break_even["loss_per_combination"], digits=2))")
    end
    
    println("\n" * "-" ^ 60)
    println("RISK ANALYSIS")
    println("-" ^ 60)
    
    println("Risk Metrics:")
    println("  Volatility: $(round(risk_analysis["volatility"], digits=4))")
    println("  Maximum Potential Loss: \$$(round(risk_analysis["max_potential_loss"], digits=2))")
    println("  Value at Risk (95%): \$$(round(risk_analysis["value_at_risk_95"], digits=2))")
    println("  Risk Level: $(risk_analysis["risk_level"])")
    
    println("\nDrawdown Analysis:")
    drawdown = risk_analysis["drawdown_analysis"]
    println("  Average time between wins: $(round(drawdown["average_time_between_wins"], digits=1)) draws")
    println("  Estimated max drawdown period: $(round(drawdown["estimated_max_drawdown_period"], digits=1)) draws")
    println("  Risk assessment: $(drawdown["risk_assessment"])")
    
    println("\n" * "-" ^ 60)
    println("STATISTICAL SUMMARY")
    println("-" ^ 60)
    
    println("Performance Summary:")
    println("  Average improvement ratio: $(round(summary["average_improvement"], digits=2))x")
    println("  Best improvement: $(round(summary["best_improvement"], digits=2))x")
    println("  Worst improvement: $(round(summary["worst_improvement"], digits=2))x")
    println("  Significant improvements: $(summary["significant_improvements"])/3 tiers")
    println("  Overall assessment: $(summary["overall_assessment"])")
    println("  Confidence level: $(summary["confidence_level"])")
    
    println("\n" * "-" ^ 60)
    println("RECOMMENDATIONS")
    println("-" ^ 60)
    
    println("Expected Value: $(expected_value["recommendation"])")
    println("Risk Management: $(risk_analysis["risk_recommendation"])")
    println("Statistical Confidence: $(summary["overall_assessment"])")
    
    # Final recommendation
    if expected_value["strategy_expected_value"] > expected_value["random_expected_value"] && 
       summary["significant_improvements"] >= 1 && 
       risk_analysis["risk_level"] != "High Risk"
        println("\nFINAL RECOMMENDATION: RECOMMENDED FOR PLAY")
        println("Strategy shows statistical improvement with acceptable risk profile.")
    elseif expected_value["strategy_expected_value"] > expected_value["random_expected_value"]
        println("\nFINAL RECOMMENDATION: CONSIDER WITH CAUTION")
        println("Strategy shows improvement but may have elevated risk or limited statistical significance.")
    else
        println("\nFINAL RECOMMENDATION: NOT RECOMMENDED")
        println("Strategy does not demonstrate sufficient improvement over random play.")
    end
    
    println("\n" * "=" ^ 80)
end

"""
Export statistical report to CSV format
"""
function export_statistical_report_csv(statistical_report::Dict{String, Any}, filename::String)
    report = statistical_report["performance_report"]
    theoretical = statistical_report["theoretical_probabilities"]
    empirical = statistical_report["empirical_probabilities"]
    expected_value = statistical_report["expected_value_analysis"]
    summary = statistical_report["statistical_summary"]
    
    open(filename, "w") do file
        # Header
        println(file, "Wonder Grid Statistical Analysis Report")
        println(file, "Generated,$(Dates.now())")
        println(file, "Key Number,$(report.key_number)")
        println(file, "Test Period,$(report.test_period[1]) to $(report.test_period[2])")
        println(file, "Sample Size,$(report.total_draws)")
        println(file, "")
        
        # Probability analysis
        println(file, "Probability Analysis")
        println(file, "Tier,Theoretical,Empirical,Improvement Ratio,Confidence Interval Lower,Confidence Interval Upper")
        
        confidence_intervals = statistical_report["confidence_intervals"]
        for tier in ["3/5", "4/5", "5/5"]
            ci_lower, ci_upper = confidence_intervals[tier]
            println(file, "$tier,$(theoretical[tier]),$(empirical[tier]),$(summary["improvement_ratios"][tier]),$ci_lower,$ci_upper")
        end
        
        println(file, "")
        println(file, "Expected Value Analysis")
        println(file, "Metric,Value")
        println(file, "Strategy Expected Value,$(expected_value["strategy_expected_value"])")
        println(file, "Random Expected Value,$(expected_value["random_expected_value"])")
        println(file, "Expected Value Improvement,$(expected_value["expected_value_improvement"])")
        println(file, "Strategy ROI,$(expected_value["strategy_roi_percentage"])")
        println(file, "Random ROI,$(expected_value["random_roi_percentage"])")
        
        println(file, "")
        println(file, "Summary")
        println(file, "Average Improvement,$(summary["average_improvement"])")
        println(file, "Overall Assessment,$(summary["overall_assessment"])")
        println(file, "Confidence Level,$(summary["confidence_level"])")
    end
end

"""
Generate batch statistical reports for multiple key numbers
"""
function generate_batch_statistical_reports(reporter::PerformanceReporter, 
                                           key_numbers::Vector{Int}, 
                                           test_draws::Vector{LotteryDraw})::Vector{Dict{String, Any}}
    statistical_reports = Dict{String, Any}[]
    
    println("Generating batch statistical reports for $(length(key_numbers)) key numbers...")
    
    for (i, key_number) in enumerate(key_numbers)
        print("  Processing key number $key_number ($i/$(length(key_numbers)))... ")
        
        try
            statistical_report = generate_statistical_report(reporter, key_number, test_draws)
            push!(statistical_reports, statistical_report)
            println("✓")
        catch e
            println("✗ Error: $e")
        end
    end
    
    return statistical_reports
end

"""
Display batch statistical summary
"""
function display_batch_statistical_summary(statistical_reports::Vector{Dict{String, Any}})
    if isempty(statistical_reports)
        println("No statistical reports to analyze")
        return
    end
    
    println("\n" * "=" ^ 90)
    println("BATCH STATISTICAL ANALYSIS SUMMARY")
    println("=" ^ 90)
    
    # Extract key metrics
    key_numbers = [Int(report["performance_report"].key_number) for report in statistical_reports]
    avg_improvements = [Float64(report["statistical_summary"]["average_improvement"]) for report in statistical_reports]
    strategy_evs = [Float64(report["expected_value_analysis"]["strategy_expected_value"]) for report in statistical_reports]
    random_evs = [Float64(report["expected_value_analysis"]["random_expected_value"]) for report in statistical_reports]
    risk_levels = [String(report["risk_analysis"]["risk_level"]) for report in statistical_reports]
    
    # Summary table
    println("Statistical Performance Summary:")
    println("Key | Avg Improvement | Strategy EV | Random EV | EV Advantage | Risk Level | Recommendation")
    println("-" ^ 90)
    
    for i in 1:length(statistical_reports)
        report = statistical_reports[i]
        key = key_numbers[i]
        avg_imp = round(avg_improvements[i], digits=2)
        strat_ev = round(strategy_evs[i], digits=2)
        rand_ev = round(random_evs[i], digits=2)
        ev_adv = round(strat_ev - rand_ev, digits=2)
        risk = risk_levels[i]
        
        # Determine recommendation
        recommendation = if strat_ev > rand_ev && avg_improvements[i] > 1.1 && risk != "High Risk"
            "RECOMMEND"
        elseif strat_ev > rand_ev
            "CONSIDER"
        else
            "AVOID"
        end
        
        println("$(lpad(key, 3)) | $(lpad(avg_imp, 15))x | $(lpad(strat_ev, 11)) | $(lpad(rand_ev, 9)) | $(lpad(ev_adv, 12)) | $(lpad(risk, 10)) | $(lpad(recommendation, 12))")
    end
    
    # Overall statistics
    println("\n" * "-" ^ 70)
    println("OVERALL BATCH STATISTICS")
    println("-" ^ 70)
    
    println("Performance Distribution:")
    println("  Average improvement ratio: $(round(mean(avg_improvements), digits=2))x")
    println("  Best improvement ratio: $(round(maximum(avg_improvements), digits=2))x")
    println("  Worst improvement ratio: $(round(minimum(avg_improvements), digits=2))x")
    println("  Standard deviation: $(round(std(avg_improvements), digits=3))")
    
    println("\nExpected Value Distribution:")
    println("  Average strategy EV: \$$(round(mean(strategy_evs), digits=2))")
    println("  Average random EV: \$$(round(mean(random_evs), digits=2))")
    println("  Average EV advantage: \$$(round(mean(strategy_evs) - mean(random_evs), digits=2))")
    
    println("\nRisk Distribution:")
    risk_counts = Dict{String, Int}()
    for risk in risk_levels
        risk_counts[risk] = get(risk_counts, risk, 0) + 1
    end
    for (risk, count) in risk_counts
        println("  $risk: $count key numbers")
    end
    
    # Top performers
    println("\n" * "-" ^ 70)
    println("TOP STATISTICAL PERFORMERS")
    println("-" ^ 70)
    
    # Sort by average improvement
    sorted_indices = sortperm(avg_improvements, rev=true)
    
    println("Top 5 by Average Improvement:")
    for i in 1:min(5, length(sorted_indices))
        idx = sorted_indices[i]
        key = key_numbers[idx]
        improvement = round(avg_improvements[idx], digits=2)
        ev_advantage = round(strategy_evs[idx] - random_evs[idx], digits=2)
        println("  $i. Key $key: $(improvement)x improvement, \$$ev_advantage EV advantage")
    end
    
    # Sort by expected value advantage
    ev_advantages = strategy_evs .- random_evs
    sorted_ev_indices = sortperm(ev_advantages, rev=true)
    
    println("\nTop 5 by Expected Value Advantage:")
    for i in 1:min(5, length(sorted_ev_indices))
        idx = sorted_ev_indices[i]
        key = key_numbers[idx]
        ev_advantage = round(ev_advantages[idx], digits=2)
        improvement = round(avg_improvements[idx], digits=2)
        println("  $i. Key $key: \$$ev_advantage advantage, $(improvement)x improvement")
    end
    
    println("\n" * "=" ^ 90)

    return Dict(
        "total_wins" => total_hits,
        "win_frequency" => total_hits / total_draws,
        "analysis" => "Based on overall win frequency"
    )
end


"""
Generate and display clear comparison between Wonder Grid and random selection
"""
function display_wonder_grid_vs_random_comparison(reporter::PerformanceReporter, key_number::Int, 
                                                test_draws::Vector{LotteryDraw})
    # Generate Wonder Grid analysis
    wg_statistical_report = generate_statistical_report(reporter, key_number, test_draws)
    
    # Generate random play analysis for comparison
    random_combinations = generate_random_combinations(210)  # Same number as Wonder Grid
    random_backtest = run_backtest(reporter.backtesting_engine, random_combinations, "Random Play")
    
    # Extract key metrics
    wg_report = wg_statistical_report["performance_report"]
    wg_empirical = wg_statistical_report["empirical_probabilities"]
    theoretical = wg_statistical_report["theoretical_probabilities"]
    
    random_empirical = Dict{String, Float64}(
        "3/5" => random_backtest.hit_rates["3/5"],
        "4/5" => random_backtest.hit_rates["4/5"],
        "5/5" => random_backtest.hit_rates["5/5"]
    )
    
    println("=" ^ 100)
    println("WONDER GRID vs RANDOM SELECTION - COMPREHENSIVE COMPARISON")
    println("=" ^ 100)
    
    println("Test Configuration:")
    println("  Period: $(wg_report.test_period[1]) to $(wg_report.test_period[2])")
    println("  Sample Size: $(wg_report.total_draws) draws")
    println("  Combinations: $(wg_report.total_combinations) each strategy")
    println("  Wonder Grid Key Number: $key_number")
    
    println("\n" * "-" ^ 80)
    println("PERFORMANCE COMPARISON TABLE")
    println("-" ^ 80)
    
    println("Metric                    | Theoretical | Random Play | Wonder Grid | WG Advantage")
    println("-" ^ 80)
    
    for tier in ["3/5", "4/5", "5/5"]
        theo_pct = round(theoretical[tier] * 100, digits=6)
        random_pct = round(random_empirical[tier] * 100, digits=4)
        wg_pct = round(wg_empirical[tier] * 100, digits=4)
        
        # Calculate advantage
        if random_empirical[tier] > 0
            advantage = wg_empirical[tier] / random_empirical[tier]
            advantage_str = "$(round(advantage, digits=2))x"
        else
            advantage_str = wg_empirical[tier] > 0 ? "∞" : "N/A"
        end
        
        println("$(rpad("$tier Hit Rate (%)", 25)) | $(rpad(theo_pct, 11)) | $(rpad(random_pct, 11)) | $(rpad(wg_pct, 11)) | $(advantage_str)")
    end
    
    # Efficiency ratios
    println("\n" * "-" ^ 80)
    println("EFFICIENCY RATIOS (vs Theoretical)")
    println("-" ^ 80)
    
    for tier in ["3/5", "4/5", "5/5"]
        random_ratio = random_empirical[tier] / theoretical[tier]
        wg_ratio = wg_empirical[tier] / theoretical[tier]
        
        println("$tier: Random = $(round(random_ratio, digits=2))x, Wonder Grid = $(round(wg_ratio, digits=2))x")
    end
    
    # Expected value comparison
    println("\n" * "-" ^ 80)
    println("EXPECTED VALUE COMPARISON")
    println("-" ^ 80)
    
    wg_ev_analysis = wg_statistical_report["expected_value_analysis"]
    
    # Calculate random EV
    random_ev = sum(random_empirical[tier] * wg_ev_analysis["prize_structure"][tier] for tier in ["3/5", "4/5", "5/5"])
    wg_ev = wg_ev_analysis["strategy_expected_value"]
    
    println("Expected Value per \$1 Play:")
    println("  Random Play: \$$(round(random_ev, digits=4))")
    println("  Wonder Grid: \$$(round(wg_ev, digits=4))")
    println("  Wonder Grid Advantage: \$$(round(wg_ev - random_ev, digits=4))")
    
    # ROI comparison
    random_roi = (random_ev - 1.0) * 100
    wg_roi = wg_ev_analysis["strategy_roi_percentage"]
    
    println("\nReturn on Investment:")
    println("  Random Play ROI: $(round(random_roi, digits=2))%")
    println("  Wonder Grid ROI: $(round(wg_roi, digits=2))%")
    println("  ROI Improvement: $(round(wg_roi - random_roi, digits=2))%")
    
    # Visual comparison
    println("\n" * "-" ^ 80)
    println("VISUAL PERFORMANCE COMPARISON")
    println("-" ^ 80)
    
    for tier in ["3/5", "4/5", "5/5"]
        println("\n$tier Matches:")
        
        # Create simple bar chart
        random_bar_length = min(50, round(Int, random_empirical[tier] * 10000))
        wg_bar_length = min(50, round(Int, wg_empirical[tier] * 10000))
        
        println("  Random:     $("█" ^ random_bar_length) $(round(random_empirical[tier] * 100, digits=4))%")
        println("  Wonder Grid: $("█" ^ wg_bar_length) $(round(wg_empirical[tier] * 100, digits=4))%")
    end
    
    # Summary recommendation
    println("\n" * "-" ^ 80)
    println("SUMMARY & RECOMMENDATION")
    println("-" ^ 80)
    
    total_wg_advantage = sum(wg_empirical[tier] for tier in ["3/5", "4/5", "5/5"])
    total_random = sum(random_empirical[tier] for tier in ["3/5", "4/5", "5/5"])
    
    overall_advantage = total_wg_advantage / max(total_random, 1e-10)
    
    if overall_advantage > 2.0 && wg_ev > random_ev
        recommendation = "🟢 STRONGLY RECOMMENDED: Wonder Grid shows significant advantage"
    elseif overall_advantage > 1.5 || wg_ev > random_ev
        recommendation = "🟡 RECOMMENDED: Wonder Grid shows moderate advantage"
    elseif overall_advantage > 1.0
        recommendation = "🟡 CONSIDER: Wonder Grid shows slight advantage"
    else
        recommendation = "🔴 NOT RECOMMENDED: Wonder Grid does not outperform random play"
    end
    
    println(recommendation)
    println("\nKey Findings:")
    println("  • Overall performance advantage: $(round(overall_advantage, digits=2))x")
    println("  • Expected value improvement: \$$(round(wg_ev - random_ev, digits=4)) per play")
    println("  • Best performing tier: $(argmax(Dict(tier => wg_empirical[tier] / max(random_empirical[tier], 1e-10) for tier in ["3/5", "4/5", "5/5"])))")
    
    if wg_empirical["5/5"] > 0 && random_empirical["5/5"] == 0
        println("  • 🎉 Wonder Grid achieved jackpot wins while random play did not!")
    end
    
    println("\n" * "=" ^ 100)
end

"""


"""








