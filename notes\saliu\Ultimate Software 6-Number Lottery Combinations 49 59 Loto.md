---
created: 2025-01-03T19:54:00 (UTC +08:00)
tags: [lotto,software,best,programs,6-number,games,world,6 winning numbers,jackpot,pick-6,loto,]
source: https://saliu.com/ultimate-lotto6-software.html
author: 
---

# Ultimate Software 6-Number Lottery Combinations 49 59 Loto

> ## Excerpt
> Ultimate Software Lotto 6 is excellent for lotto games that draw 6 winning numbers, like 6/49 or 51; the most sought after lottery software programs.

---
## <u><i>Ultimate Software</i> for All 6-Number Lotto Games (e.g. <i>6 from 49</i> or <i>6 from 59</i>)</u>

## By <PERSON>, _Founder of Lottery Mathematics, Lotto Programming Science_

![Ultimate Lotto-6 is the most sought after suite, package of lottery software applications.](https://saliu.com/HLINE.gif)

![Presenting the main Ultimate 6-Number lottery books, chapters, functions.](https://saliu.com/HLINE.gif)

### 0\. [Introductory Notes to the _Ultimate Lotto Software_ for 6 Winning Numbers; Download, Install, Run](https://saliu.com/ultimate-lotto6-software.html#download)  
I. [Main Menu: Data Files, Reports, Strategies, Combination Generators](https://saliu.com/ultimate-lotto6-software.html#main)  
II. [Menu #2: Skips, Delta, Lotto Decades, Last Digits, Wheels](https://saliu.com/ultimate-lotto6-software.html#book-2)  
III. [Menu #3: _12-Number_ Combinations Wheeled to 6-number Lotto Games](https://saliu.com/ultimate-lotto6-software.html#menu-3)  
IV. [Menu #4: Specialty Programs for Probability, Odds, Combinatorics, Lexicographic Order, Wheeling](https://saliu.com/ultimate-lotto6-software.html#book-4)

![How to download the 6-49, 6 of 51 pick-6 lotto applications, install, run, generate combinations.](https://saliu.com/HLINE.gif)

## <u>0. Introductory Notes to the <i>Ultimate Lotto Software</i> for 6 Winning Numbers; Download, Install, Run</u>

<big><b>ULTIMATE LOTTO-6</b></big> ~ version **1.2** ~ April 2017 ~ _software category 5.1_.-   This package is definitely the ultimate, never-to-be-duplicated software for lotto games drawing **6** winning numbers from **1 to N**. **N** can be any number, for example: _40, 45, 49, 51, 54_, etc. Specific data files of _past drawings_ or _past winning numbers_ make possible for the programs in this comprehensive application to work with any lotto-6 game in the world.
-   Downloading this collection of high-power programs requires the special _**Ultimate Software**_ membership. It is available only to members who paid for the _**Permanent Software Download**_ subscription. Click on the top banner to learn the terms, conditions, and availability.
-   Your membership page has clear instructions on downloading, installing, and running the software. All _**Ultimate Software**_ applications are available for download from your membership page. In addition, you can download a special lottery software utility to work with data files efficiently.

The presentation of the **four menus** and their applications and functions is next. The programs themselves have their own menus. The menus in all my programs are self-describing, but there are also specialized pages with more detailed information.

## <u>I. Main Menu: Data Files, Reports, Strategies, Combination Generators</u>

![The best winning lotto 6 ultimate software has a main menu full of powerful programs.](https://saliu.com/images/ultimate-lotto-software-60.gif)

This menu comprises the most important programs and functions of the _**Ultimate 6-Number Lotto Software**_ bundle. There is a _sine qua non_ step: _Create the **data file**_ of _past drawings_ or _past winning lotto numbers_. You already know and fulfilled this step as a user of [_**Bright-6 High-Powered Lotto-6 Software**_](https://saliu.com/lotto6-software.html).

There is also a visual tutorial, with plenty of screenshots, applicable to all my lottery software:-   [_**Lotto Software Book**_](https://saliu.com/forum/lotto-book.html).

### _T_: Tutorial Online

It takes you to this Web page via your default Web browser. The online information is always up to date, as it is much prompter to edit a Web page than rewriting a manual bundled with the software.

### _E_: MDIEditor Lotto

Starts the separate GUI application **MDIEditor And Lotto WE** (for 64-bit Windows). The 32-bit OS version can be run from menu #3.

### Functions _I_, _E_: Edit Data Files

The _history file_ or _data file_ is always saved in text (ASCII) format and consists of the lotto numbers previously drawn in the game. You can create the file simply by typing the winning numbers drawing by drawing - one draw per line.

-   The data file must have exactly 6 numbers per line. The numbers must be separated by one or more blank spaces. You can also use commas _**,**_ as the field separators. The universal field separator should be the _blank space_.
-   The final step is to _sort_ the drawings (the lines in your lottery files) in _ascending order_.
-   Here is an example of the contents of my file for a _6/49_ lotto game after sorting and formatting (_PA-6_, included):

   **```
   1  17  21  22  24  30 (draw #1, or the most recent)
  17  19  29  42  48  49 (2nd recent drawing)
   2  22  34  36  37  45 (3rd recent draw)
   4   8   9  17  23  41
   8  23  27  47  48  49
```**
… all the way down to the last line in the file, representing the oldest draw in the current game format.

I created a specialized piece of software to maximize the efficiency of working with data files: **LotteryData**. Please visit the following link, as it also deals with the important topics of lottery _strategies_ and the _winning reports_:

-   [_**Software to Manage Lottery Data Files**_](https://forums.saliu.com/lottery-strategies-start.html#software).

### _S_: Sort Data Files

The application sorts the lotto data files in _ascending_ order; it only formats nicely the pick-3, pick-4 and horse racing files.

This function is a great utility in conjunction with the data files. The numbers in every combination (line) in a lotto data file must be sorted in ascending order.

This task can be also performed specifically for 6-number lotto data files in _**Super Utilities**_, option _T = Sort or Add-up Data Files_. I always use the **LotteryData** utility mentioned above.

For additional useful information, read:  

-   [_**<u>MDIEditor Lotto WE</u>: Lottery Software Manual, Book, ebook, Help**_](https://saliu.com/MDI-lotto-guide.html)
-   [**Help** on _**Software for Lottery, Lotto, Gambling: Download, Install, Run, Maintain Results Files**_](https://saliu.com/Help.htm)
-   [_**State Lottery, Lotto Drawings, Results, Past Winning Numbers: Online, over Internet**_](https://saliu.com/bbs/messages/920.html).

After creating, updating, and sorting the data files, one more important operation is required: **concatenation** of the _real_ data file with a _simulated_ data file.

The concatenation is done in _**Super Utilities**_, option _M = Make/Break/Position_, then _Make_, then option _1_ or _2_: _Make D6 from DATA-6 and SIM-6_. _D6_ is the final data file the software needs to generate reports, strategies, combinations, etc.

-   This version of the _**Ultimate Software**_ requires a _D6_ data file of at least _12000000_ (_12 million_) lotto combinations (6-number lines).

Here is the best procedure of creating the _SIM-6_ and _D6_ files to meet the size requirement of _12000000_ (_12 million_) comb_ion_ations. For example, my _6 from 49_ lotto game has _13983816_ total combinations. I generated all 6-number lines in lexicographical order by running my combinatorial software **PermuteCombine** or **Combinations**. I named the output file _ALL-6-49_ (easy to remember).

Then, I shuffled that lexicographical file _ALL-6-49_ in **Shuffle** (option _F = Files_, then _V = Shuffle Vertically_). The result was _SIM-6_, with all 6/49 lotto combinations, _13983816_ lines randomly arranged.

If your lotto game has fewer than 12 million combinations, generate the necessary balance in _**Super Utilities**_, option _S_, then _Generate SIM file_. Add this second SIM6-2 file to the shuffled file of all lotto combinations in your game (concatenate _SIM6-1+SIM6-2_ to _SIM-6_). See also the info for menu #2, option _B = Concatenate files_.

\*\* It is of the essence to shuffle (randomize) all your SIM files. Do not use, under any circumstances, SIM files in lexicographical order! Otherwise, the winning reports (W, MD) will turn into big headaches! Some filters will be so unusually high that several columns will run into one another. The _Check Strategy_ functions will NOT run any longer \*\*

### _W_: Winning Reports (W6, MD6 Files)

The creation of the winning reports, _W6_ & _MD6_, is of paramount importance. Press _W_ to generate the reports. Type 200 (or more drawings) for the length of the report. Type _D6_ for the name of the data file, and the default for total drawings to use. I recommend a large number of drawings to analyze, even 1000, at least in the beginning. It helps find more lottery strategies.

-   Type or accept the defaults _W6.1_ to _W6.4_ and _MD6.1_ to _MD6.4_ for the report names.

The four report files will show a number of parameters named **filters**. Based on the reports, you feed the combination generating program (options _L_ or _R_) or the wheel generating program (function _W_ in menu #2) with the filters. The process of setting filter values is known as **strategy selection**.

For more information, read:

-   [_**<u>MDIEditor Lotto WE</u>: Lottery Software Manual, Book, ebook, Help**_](https://saliu.com/MDI-lotto-guide.html)
-   [_**Filtering, Filters in Lottery Software, Lotto Software**_](https://saliu.com/filters.html)
-   [_**Lotto, Lottery Software Tutorial**_](https://saliu.com/bbs/messages/818.html) - _"My kingdom for a good tutorial!"_
-   [_**Software to Create Reports of Lotto Strategies**_](https://forums.saliu.com/lottery-strategies-start.html).
-   [_**The Best Ever Lottery Strategy, Lotto Strategies**_](https://software.saliu.com/lottery-strategy-lotto-strategies.html) — a must-read ebook on strategies.

### _O_: Sort Filter Reports by Column

The program sorts the _W6_, _MD6_, _GR6_, _DE6_, _FR6_, _SK6_ reports by column, helping the user see more easily the filters — e.g. filters of _wacky_ values (very low, or extremely high).  
The sorting is done by type of winning reports. The program also offers the correct choices (correct names) for filters (columns) to sort on.

For more information, read:

-   [_**Filtering, Filters in Lottery Software, Lotto Software**_](https://saliu.com/filters.html)
-   [_**Cross-Reference Lottery, Lotto Strategy Files**_](https://saliu.com/cross-lines.html).

### _C_: Check Strategies (Filter Settings)

The function analyzes the 4 _W6_ + 4 _MD6_ reports to establish any type of strategy, between _minimum_ and _maximum_ values. The strategy report will show how many times a particular lotto strategy hit in the past lottery drawings. In other words, we check how a collection of filter settings would have fared in the past.

The program also creates the strategy files in the correct format (error-free). The strategy files are named _ST6.000_ (default). You need to remember the ST file names! It is a very good idea to create a simple text file where you record various lottery strategies: ST names and what the strategies are based on.

\*\* Potential errors when checking for lotto strategies.  
#1: Do NOT use _SIM_ files in _lexicographical order_. Always shuffle all your _SIM_ files.  
#2: Do NOT mix different game formats in your lotto files; that includes the file with your real lottery drawings and your _SIM_ulated files.  
#3: Sometimes some filters can get way, way out of range. The value can be wider than what I planned as the maximum length for that particular filter. I wanted the reports to be as readable as possible.

If #3 happens, the strategy checking functions will trigger errors. You can fix the error by opening your winning reports one by one in the text editor. You will notice that two neighboring columns are no longer separated by at least one blank space. The older 16-bit lotto software added the % character at the end of the column.

The culprit is the number in the column which is no longer aligned with numbers above and below. You need delete one (very rarely more than one) character at the end of that lengthy number. Make sure that there is one blank space between the numbers in the two columns and that they are properly aligned in the respective columns. Here is a visual example:

1234    23  
12345123 = strategy-checking error

Corrected W/MD file:  
1234   23  
1234 123

Repeat the procedure with all your lotto winning reports, as necessary. \*\*

### _H_: Strategy Hits in the Past

The program generates lotto-6 combinations for situations when a particular strategy (as in the _ST6\*.\*_ files) hit in the past drawings, or history of the game. The program needs an input file created by **Strategy6** (the previous function). The input file consists of the draw IDs for the hit situations. Otherwise, the user will manually input the filter settings and the drawings IDs.

### _L_: Lexicographic Combinations  
Option _R_: Randomized Combinations

This is the ultimate goal of LotWon lotto software: Generate winning lotto combinations. We keep records of past lotto draws (maintain data files), then analyze the data files and generate the winning reports. Then, we analyze the W reports and select filter values for our lotto strategies. We finally apply the lotto strategies to the combination generators.

Each lotto combination generating program has several functions of its own.

\* _L_ - Program name: **Lexico6** – it generates 6-number lotto combinations in _**lexicographical order**_. That is, the program starts the generation at lexicographic index #1 (i.e. _1, 2, 3, 4, 5, 6_) and ends at the last index in the lotto set (e.g. _44 45 46 47 48 49_).  
Functions in this program:

**N = Normal Lexicographic - NO ranges - NO favorites**

-   generates every combination in the lotto set, from lexicographical index #1 to the last index.

**1 = 1 favorite number - NO ranges**

-   generates every combination in the lotto set, from lexicographical index #1 to the last index; also, each and every combination will contain one _favorite number_ chosen by the software user.

**2 = 2 favorite numbers - NO ranges**

-   generates every combination in the lotto set, from lexicographical index #1 to the last index; also, each and every combination will contain _two favorite numbers_ chosen by the software user.

**R = Combinations between positional RANGES**

-   generates every lotto combination by positional _ranges_; e.g. numbers in 1st position between 1 and 18; numbers in 2nd position between 6 and 30; numbers in 6th position between 40 and 49.

**P = PURGE an output file of combinations**

-   takes an output lotto combination file previously generated and eliminates additional combinations by applying further filtering.

\* _R_ - Program name: **Combine6** – it generates 6-number lotto combinations in _**randomized manner**_. That is, the program starts and ends the generation anywhere in the lotto set, instead of lexicographically.  
Functions in this program:

**0 = NO favorite numbers, NO shuffle**

-   generates randomized lotto combinations, without any favorite numbers, or clusters (shuffled combinations).

**1 = 1 favorite number - NO shuffle**

-   generates randomized lotto combinations; also, each and every random combination will contain _one favorite number_ chosen by the software user.

**2 = 2 favorite numbers - NO shuffle**

-   generates randomized lotto combinations; also, each and every random combination will contain _two favorite numbers_ chosen by the software user.

**S = SHUFFLE numbers (ALL #s in game)**

-   generates ALL lotto numbers in one cluster or group, 6 numbers per each line; e.g. a 6-49 lotto game will have clusters of 9 lines (combinations), 6 numbers per line; the last line will repeat 3 numbers from previous combinations.

\* Both lotto applications above, as well as **Wheel6, Combine6-12** (plus others) have two more functions that also eliminate unwanted combinations:  

-   inner filters (they eliminate around 95% of all combinations - enable it rarely);
-   LIE elimination: You noticed that, more often than not, your lotto output files do NOT have winning numbers - not 6 winners, not 5, not 4, not 3... You can open the _LIE_ file and generate combinations that do NOT contain groups of lotto numbers existent in the _LIE_ file.

For more information, read:

-   [_**Lottery <u>Strategy in Reverse or LIE Elimination</u>: Not-to-Win Leads to Not-to-Lose or WIN**_](https://saliu.com/reverse-strategy.html).
-   [_**Professors Play All Lotto Numbers and Win the Jackpot**_](https://saliu.com/all-lotto-numbers.html)

### _U_: Super Utilities

This piece of software bundles several utilities for 6-number lotto games. Each sub-program in turn has several features of its own.

**S = Files (SIM-6, Count Lines)**

-   Simulate a SIM-6 file
-   Count lines in text files.

**D = Duplicates: Strip and Wheel**

-   The function retrieves the input data, finds duplicate lines, and eliminates them. The duplication is considered from _6 of 6_ numbers, to sub-groups of numbers; i. e. from _5 of 6_ to _1 of 6_. Hence, _wheeling_ in the function name.

**F = Frequency Reporting by Number**

-   The sub-program counts how many times each lotto-6 number came out in the specified range of past drawings. Then, it plots a _skip chart_ - number of drawings between hits. Also, the function saves to files the _most frequent pairings_ (_BEST6_) and the _least frequent pairings_ (_WORST6_).

**W = Check for Winners**

-   The function checks for groups of 3, 4, 5, and 6 winning lotto-6 numbers in two manners:  
    1) an OUTPUT file against a data file with real draws  
    2) POOLS of numbers against a data file with real lotto drawings.  
    This function is the recommend method to check if your output files had any winners in past drawings, especially the drawings when you played the combinations.

**T = Sort or Add-up Data Files**

-   Sort your DATA-6 file in _ascending_ order. The recommended method is the one I use. I referred to it at the beginning of this section: the **LotteryData** utility.

-   Add-up the numbers in each lotto combination in a data file to _sum-totals_. The recommended method is performed by the standalone application **Sums** presented in the next section (menu #2).
    
    **G = Generate Combinations, Favorites, Least-Number-Groups**
    
    -   The function generates lotto-6 combinations lexicographically. You can play favorite numbers: 1, 2, 3, 4; or NO favorites. You can also eliminate (least) singles, pairings, triples, quads. This function does not require a data file of past drawings.
    
    **M = Make/Break/Position (D6, Positional Ranges, Break Long Lines to 6-Number Combinations)**
    
    -   Make D6 without LEAST6
    -   Make D6 with LEAST6
    -   Make D6 with LEAST & BEST6
    -   _**Break 6+ combinations to 6-number lines**_
    -   _**Generate lotto combinations by positional ranges (positional limits)**_.
    
    **1 = Singles Rundown**
    
    -   Calculate the frequency of every _single_ in a 6-number lotto game;
    -   sort the singles by frequency in descending order;
    -   create a _least single_ file (singles that have not come out in the specified range of lottery drawings).
    
    **2 = Pairs Rundown**
    
    -   Calculate the frequency of every _pair_ (_pairing_) in a 6-number lotto game;
    -   sort the pairs by frequency in descending order;
    -   create a _least pairing_ file (pairs that have not come out in the specified range of lotto drawings).
    
    **3 = Triplets Rundown**
    
    -   Calculate the frequency of every _triplet_ in a 6-number lotto game;
    -   sort the triplets by frequency in descending order;
    -   create a l_east triplet_ file (triplets that have not come out in the specified range of lottery drawings).
    
    **4 = Quadruplets Rundown**
    
    -   Calculate the frequency of every _quadruplet_ in a 6-number lotto game;
    -   sort the quadruplets by frequency in descending order;
    -   create a _least quadruplet_ file (quadruplets that have not come out in the specified range of lotto drawings).
        
        **5 = Quintets Rundown**
        
        -   Calculate the frequency of every _quintet_ in a 6-number lotto game;
        -   sort the quintets by frequency in descending order;
        -   create a _least quintet_ file (quintets that have not come out in the specified range of lottery drawings).

This important piece of 6-number lotto software is amply presented on its dedicated page:

-   [_**Lotto Software for Singles, Pairs, Triples, Quadruples, Quintuples, Sextuples**_](https://saliu.com/gambling-lottery-lotto/lotto-software.htm).

### The next three functions — _D, N, M_ — represent valuable new software, highly requested by members of the _Super Forums_.

### _D_: Dedicated LIE Elimination (_LieID_)

This program applies the **_LIE elimination_** feature introduced in the BRIGHT-6 lotto software, specifically the combination generators. The main function generates the reports showing the levels of the ID filters for both the **_pairings_** and **_number frequencies_**.

For ample information, you definitely want to read:

-   [_**Lotto Strategy in Reverse for Pairs, Number Frequency**_](https://saliu.com/lie-lottery-strategies-pairs.html).

### _N_: LIE StriNgs (_LieStrings6_)

The program shows the lotto-6 draws as strings of skips, high/low, odd/even, decades, last digits — to be used in the _**LIE elimination**_ strategies. The string report files are to be used as INPUT to combination generating (**G**), with the filters being fed automatically. The _output_ files are then used as _LIE_ files in the combination generators (e.g. **Lexico6**).

For ample information, you definitely want to read:

-   [_**Reversed Lottery Strategy for Lotto Decades, Last Digits, Odd Even, Low High, Skips**_](https://saliu.com/lie-lotto-strategies-decades.html).

### _M_: _Markov Chains_, Pairs, Followers

This complex piece of software deals with the famous _Markov chains_, random pairing, trendy number frequencies (.e.g. _hot_, _cold_ lotto numbers).

-   1) The software creates first reports for: Followers, Pairings, Number Frequencies.
-   2) The program generates lists of: Followers, Pairings, Frequencies — sorted in _descending order_, from _Hot_ to _Cold_ based on frequency.
-   3) The application generates combinations based on several methods related to this topic.
    
    For ample information, you definitely want to read:
    
-   [_**Theory of Markov Chains in Lottery, Lotto, Followers, Pairs, Software**_](https://saliu.com/markov-chains-lottery.html)
-   [_**Markov Chains: Lottery, Lotto, Software, Algorithms, Programming**_](https://saliu.com/Markov_Chains.html).

## <u>II. Menu #2: Skips, Delta, Lotto Decades, Last Digits, Wheels</u>

![Special additions to the best ever 6-number lotto software include last digits and 5 deltas.](https://saliu.com/images/ultimate-lotto-software-61.gif)

### _D_: Deltas

This is another important addition to the grand collection _**Ultimate Lotto Software**_. The delta application shows the lotto drawings as strings of **deltas** or _differences_ between adjacent numbers. The program creates first the delta report for a lotto data file (results). The program also generates lotto combinations based on user-input deltas.

For ample information, you definitely want to read:

-   [_**Theory, Analysis of <u>Deltas</u> in Lotto Software, Strategy, Systems**_](https://saliu.com/delta-lotto-software.html)
-   [**Lottery Deltas** _**Can Build Effective Lotto Strategies, Systems**_](https://saliu.com/bbs/messages/648.html)

### _S_: Skips, Decades, Last Digits, Frequencies

The app shows the lotto-6 drawings as strings of skips, high/low, odd/even, increase/decrease from previous draw. The program also generates reports for _lotto decades_, _last digits_ (**new** program), and a report of _frequencies_ from 3 groups: _hot_, _mild_, _cold_. You can use the skips, high/low, odd/even, decades, last digits, frequencies as _filters_ in the lotto combination generators or the **purge** function. The lotto software can check if or when a strategy hit in the past. The _Strategy Hits_ function reports how many combinations a particular lotto strategy would have generated in winning situations.

For amplified information, you definitely want to read:

-   [**Reversed** _**Lottery Strategy for Lotto Decades, Last Digits, Odd Even, Low High, Skips**_](https://saliu.com/lie-lotto-strategies-decades.html)
-   [_**Lotto Decades: Software, Reports, Analysis, Strategies**_](https://saliu.com/decades.html)
-   [_**Lottery Strategy, Systems Based on Number Frequency**_](https://saliu.com/frequency-lottery.html)
-   [_**Lottery, Lotto, Combinations, Tables of Frequency Systems**_](https://saliu.com/frequency-tables.html).

### _Q_: Skips & Frequency Groups-Only

The program shows the 6-number drawings as strings of **skips** and **frequencies**. Both parameters are divided into 3 groups of numbers: _hot_, _warm_, _cold_. You can use the _skips_ and _frequency groups_ as filters, or generate combinations for the _**LIE Elimination**_ strategy.

This application is similar to function _S = Skips, Decades, Last Digits, Frequencies_. The _skips_, however, are divided in 3 groups, rather than dealing with them number by number. Please read the pages referred to above.

### _W_: Randomized Lotto Wheels On-the-Fly

Generate lotto-6 combinations which assure a minimum guarantee - _6 of 6_ to _2 of 6_. You can use it to generate _reduced lotto systems_ (_lotto wheels_) for 10, 12, 18, 20, 30, etc. numbers. Better still, you should use this program to wheel **all** the numbers in your lotto game, while employing _filters_.

Functions in this program:  

**0 = NO favorite numbers**

-   generates randomized lotto wheels, without any favorite numbers;

**1 = 1 favorite number**

-   generates randomized lotto wheels; also, each and every wheeled random combination will contain one FAVORITE number chosen by the software user;

**2 = 2 favorite numbers**

-   generates randomized lotto wheels; also, each and every wheeled random combination will contain two FAVORITE numbers chosen by the software user.

For more info, read:

-   [_**Lotto Wheels for Lotto Games Drawing 5, 6, or 7 Numbers: Free, Balanced, Randomized**_](https://saliu.com/lotto_wheels.html)
-   [_**Lotto Wheeling Software, Winning Report Generator**_](https://saliu.com/bbs/messages/wheel.html).

### _H_: Wheels from Files

This program takes an input file of lotto-6 combinations and converts the combinations to _k of 6_ lotto wheel format - from _1 of 6_ to _5 of 6_; also, a number of combinations can be input in a random manner, without any wheeling.

The function is useful if you want to reduce the number of lotto combinations in an output file previously generated. For example, you generated thousands of combinations in **Combine6** (function _R = Random_ on main menu) with light filtering; you want to play only a handful of lotto combinations that have no more than k numbers in common (lotto wheeling); evidently, you settle for a lower-tier lottery prize.

Read more details:  

-   [_**Lotto Wheels for Lotto Games Drawing 5, 6, or 7 Numbers: Balanced and Randomized**_](https://saliu.com/lotto_wheels.html)
-   [_**Software to Verify Lotto Wheels for Missing Combinations; Generate Reduced Lotto Systems**_](https://saliu.com/check-wheels.html)
-   [_**Lotto Wheeling Software, Winning Report Generator**_](https://saliu.com/bbs/messages/wheel.html).

### _F_: Rank Lottery Numbers by Frequency

This program generates frequency reports two ways: 1.- _Regardless of position_; 2.- _**Position by position**_. The lotto numbers are ranked by frequency in descending order, from hot, to mild, to cold.

This multi-game application is amply presented on its dedicated page and in other specialty materials:

-   [_**Lottery Strategy, Systems Based on Number Frequency**_](https://saliu.com/frequency-lottery.html).

### _K_: Create Lotto, Lottery, and Gambling _Skip_ Systems

This program creates lottery and gambling systems based on two or three consecutive skips; the most recent skips make it into a particular system.

This multi-game application is amply presented on its dedicated page and in other specialty materials:

-   [**<u>Lottery Skip Systems</u>**](https://saliu.com/skip-strategy.html): _**Lotto, Powerball, Mega Millions, Euromillions**_.

### _M_: Sum-up Lottery Data Files and Games

The app calculates the number of lottery combinations that add-up to a _sum-total_. It also calculates the sums of each draw in lottery files, plus _root sum_, and _standard deviation_. You can generate such lottery combinations and save them. The program creates summary reports for the game: Every sum-total and its amount of combinations, plus percentages... and much more.

See:

-   [_**Basics of a Lottery, Lotto Strategy Based on: Sums (Sum-Totals); Odd or Even; Low or High Numbers**_](https://saliu.com/strategy.html)
-   [_**Lottery, Lotto Sums, Sum-Totals**_](https://saliu.com/forum/lottery-sums.html)
-   [_**Lotto, Lottery Software to Calculate Sum-Totals, Odd-Even, Low-High Patterns**_](https://saliu.com/bbs/messages/626.html).

### _V_: Verify Data Files

This program _parses_ the lottery data files to make sure they comply with the format required by LotWon lotto software. It is highly recommended to check your data files periodically to make sure they are error-free — because _errare humanum est_.

For more information, read:

-   [_**Software to Correct Errors in Lottery, Lotto Results Files**_](https://saliu.com/bbs/messages/2.html).

### _C_: Check Winners

Check for winning numbers in output files against real drawing lotto files. The combinations to play were saved first to output text files by the combination generators.

This task can be also performed specifically for 6-number lotto data files in _**Super Utilities**_ (main menu), option _W = Check for Winners_ = the best method.

### _T_: Cross-Checking Lottery Strategies

The program writes to disk the lines of specified indexes in a file, usually a strategy file created by the function _C = Check Strategies_ (main menu). You created the _W\*.\*_ files in the Command Prompt LotWon lottery software. You also generated the statistical reports in **MDIEditor And Lotto WE**. You then created the strategy file for the _Stats_ function in **MDIEditor Lotto**. You want to see the same line numbers in _WS\*.\*_ files for a more comprehensive lotto-6 strategy; i.e. possibilities to eliminate even more combinations.

Read more:

-   [_**Cross-Reference Lottery, Lotto Strategy Files**_](https://saliu.com/cross-lines.html).

### _U_: Text File Reverser

-   The program reverses the order in text files: the bottom becomes the top. Useful in arranging the lottery data files in the order required by LotWon lotto software.
-   Uncooperative lottery sites publish lottery histories (drawings, results) in an unnatural order: The most recent drawing goes to the bottom, instead of the TOP. LotWon lottery software requires starting with the most recent draw, and go all the way down to the oldest drawing in that lottery game (bottom of file).
    
    For more detailed information, do read:
    
-   [_**Software to Reverse Order in Lottery, Lotto Results, Drawings Files**_](https://saliu.com/bbs/messages/539.html)
-   [_**The Definitive File Reverser, Shuffler, and Text Viewer Software**_](https://saliu.com/programming.html).

### _B_: Concatenate Text Files, Make Big File

This function takes a number of text (ASCII) files and _concatenates_ (_combines_) them; i.e. it puts all the files into one. The function is useful when you need to combine multiple output lotto files (_OUT6_) or _LIE_ files and work with one file, instead of working with the files step by step (e.g. **_PURGING_** output lottery files).

### _P_: Pairing Reports, Custom Grids, Lotto Combinations

This program generates a series of reports on the pairings of the lotto-6 game; it also generates pairings in the manner of lotto-6 combination generators in Bright-6.  
The _**Reports**_ option will show how many times each pairing came out; it also shows the winning pair reports in the manner of the W6/MD6 files created by **Report6**.

The program is especially useful in conjunction with the _**LIE**_ (_**reversed**_) strategy feature present in all lotto combination generators. The pairings do not lead to winning 6-number lotto combinations the very next draw, far more often than not.

We can see that no lotto drawing has all top 5 pairings only. We can generate lotto combinations for the top 18 or 24 top pairings — and we will be wrong many times. That output file qualifies as a _**LIE**_ file. We can apply, without possibility of error, the ID6 LIE filter. In a vast majority of cases, even ID5 will be a very safe filter. Sometimes, even ID4 will be a safe filter.

The filters ID3 or even less ID2 can still offer winning combinations, albeit rarely. Filter ID1 can never be applied. The grid files always contain all numbers in the lotto game. If we create the _pure wonder-grid_ with the top-5 pairings, it will not hit the jackpot in thousands of drawings. The ID1 filter always eliminates all combinations. Maybe ID3 will generate 3- or 4-winning-number combinations. It is worth trying, though — there aren't many combinations to play. If too many, we can apply more filters by _**Purge**_ (in **Lexico6**).

Also, you can create any pairing grid file in _**Super Utilities**_. You simply copy and paste any amount of numbers from the file entitled _PAIRS6_. Or, you can create directly a file named _TOP6_. Copy and paste has the advantage of creating top files which are not necessarily the top pairs. For example, you copy and paste the numbers corresponding to the pairs in the range 12 – 30.

Application name: **PairGrid6**.  
Replaces: _SkipPair6_ (still in the package — just type its name at the _**command prompt**_, after _e**X**iting_ the main menu).

For valuable information, read:

-   [_**Software News: Lotto, Lottery, Horse Racing, Pairs Programs, LIE Strategy**_](https://saliu.com/software-news.html).
-   [_**Lotto, Lottery Strategy in Reverse: Not-to-Win Leads to Not-to-Lose or WIN**_](https://saliu.com/reverse-strategy.html).
-   [_**Lottery Pairs and Repeat Probability**_](https://saliu.com/forum/lottery-pairs.html).

### _O_: Bingo

There was a question regarding _**bingo**_ in the _Super Forums_. I wrote a special program, but there are no strategies for bingo. The game is inflexible. The players cannot choose their own numbers. The numbers are already printed on the cards.

Read more:

-   [**Bingo** — _**Strategy, Systems, Software?**_](https://forums.saliu.com/bingo-systems-software.html)

### _G_: Work with User's Number Groups

This is a big lotto program that works with groups of numbers: _odd, even, low, high, frequency numbers, sums or sum-totals_.

The application has a plethora of functions and its own Web page.

Please read the dedicated presentation:

-   [_**Lotto Software for Groups of Numbers: Odd, Even, Low, High, Sums, Frequency**_](https://saliu.com/lotto-groups.html)

## <u>III. Menu #3: <i>12-Number</i> Combinations Wheeled to 6-number Lotto Games</u>

![Ultimate lotto can generate 12 numbers per combination to wheel for lotto-6 format.](https://saliu.com/images/ultimate-lotto-software-62.gif)

\* Axiomatic one, some of the functions below represent **standalone** programs. Other functions belong to older programs, but the names of the functions were less obvious to some software users. For example, several users asked me how to generate lotto combinations from groups or pools of numbers. There are no programs with relevant names. Instead, these functions are well represented in large programs that provide a wide variety of functions. In this particular example, the combination generators from pools or groups of numbers belong to the _**Super Utility**_ software.

### _N_: 6-# Combos from Pools of Numbers

Generate 6-number lotto combinations from pools or groups of numbers. Program name: **SoftwareLotto6** (_**Super Utilities**_ on main menu), option _M: Make/Break/Position_.

The groups of lottery numbers can be listed in files, in one line or multiple lines. For example, **SkipSystem** created for you a pool of lotto numbers for the 6/49 game. The file in text format consists of one line of 12 numbers. You want to generate lotto-6 combinations from those 12 numbers. Total of lotto combinations of 12 numbers taken 6 at a time is 924. In **SoftwareLotto6**, you select option _M: Make/Break/Position_. Then, option _Break_, then option _2 = All 6 Numbers Equally_. The function will generate your 924 lotto combinations in a matter of seconds.

The same function can also generate lotto combinations from multiple lines of 6+ numbers each. For example, you had 49 lines, for each of your lotto 6/49 game; each line has 12 other lotto numbers, as the best pairs of each of the 49 numbers. Since the lines will have common numbers, your lotto combinations will still be unique. The special lottery software takes care also of eliminating duplicate combinations. The _Position_ feature is even more potent.

You can apply in this function a powerful filter: the _Least_ feature. Actually, you will eliminate all pairings in a special file _WORST6_ created by the same _**Super Utility**_ (option _F: Frequency_). It is recommended now, in strong terms, to use _WORST6_ instead of _LEAST6_.

For in-depth information, study:

-   [**<u>Lottery Skip Systems</u>**](https://saliu.com/skip-strategy.html): _**Lotto, Powerball, Mega Millions, Euromillions**_
-   [_**Analysis of Best Ranges for Lotto Number Frequency, Lottery Pairs, Pairings**_](https://saliu.com/lottery-lotto-pairs.html).

### _P_: 6-# Combinations by Positions

Generate 6-number lotto combinations from 6 lines of numbers representing the 6 positions in a lotto-6 combination.

Program name: **SoftwareLotto6**, option _M: Make/Break/Position_.

You can generate lotto combinations based on positions or _positional ranges_. If you run the statistical functions of my lottery software (plenty of them!) you will see that the lotto numbers are strongly biased regarding the position. You can read at SALIU.COM a lot about ranges or positional ranges in lotto. You will see living proof that the lotto numbers follow the _**Fundamental Formula of Gambling (FFG)**_. Each position has lotto numbers based on the _FFG median_.

You can apply in this function a powerful filter: the _Least_ feature. Actually, you will eliminate all pairings in a special file _WORST6_ created by the same **SoftwareLotto6** (option _F: Frequency_). It is recommended now, in strong terms, to use _WORST6_ instead of _LEAST6_.

For best information, read:

-   [_**Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies, Positional Ranges**_](https://saliu.com/Newsgroups.htm)
-   [**<u>Lottery Skip Systems</u>**](https://saliu.com/skip-strategy.html): _**Lotto, Powerball, Mega Millions, Euromillions**_
-   [_**Analysis of Best Ranges for Lotto Number Frequency, Lottery Pairs, Pairings**_](https://saliu.com/lottery-lotto-pairs.html).

### _F_: 6# Combos from 3 Frequency Groups

Generate 6-number lotto combinations based on frequency: _hot_ numbers, _mild_ numbers, and _cold_ numbers. Run first the _Frequency Reporting_ module, and then the combination generator (_lexicographic_ or _random_).

Program name: **SkipDecaFreq6** (function _D: Deltas_, menu #2), options: _L = Combinations, Lexicographic_; _R = Random Combinations_. Then, select the screen pertaining to the filters based on the 3 lotto frequency groups.

Read further:

-   [_**Lottery Strategy, Systems Based on Number Frequency**_](https://saliu.com/frequency-lottery.html).

### _D_: 6-Number Combinations by Lotto Decades

Generate 6-number lotto combinations decade by decade; e.g. a 6/49 lotto game has 5 decades, from single digits to numbers beginning at 40.

Program name: **SkipDecaFreq6**, options: _L = Combinations_, _Lexico_; _R = Random Combinations_. Then, select the screen pertaining to the filters based on the lotto decades.

Please read much more:

-   [_**Lotto Decades: Software, Reports, Analysis, Strategies**_](https://saliu.com/decades.html).
    
    <big>The next 7 functions — <i><b>R, W, G, T, S, H, L</b></i> — have a common special presentation page:</big>
    
-   [_**Lotto Strategy, Software: 12-numbers Combinations Wheeled to 6-number Lotto Games**_](https://saliu.com/12-number-lotto-combinations.html).

### _R_: Generate BRIGHT-12 Reports

Generate special reports for lotto-6 regarding the _skips_ of the subgroups of numbers: Singles (Ones), Pairs, Triples, Quads, Quintets, Sextet.

### _W_: View BRIGHT-12 Filter Report

The editor opens the first report of the four created by the preceding function: _GroupSkips6.1_. You can then open the other 3 reports to determine a lotto strategy.

### _G_: Generate 6-Number Combinations

This program generates random combinations for lotto-6, including lexicographical, random, and file-purging. The data file must still be in the 6-numbers-per-line format. The filters for this program are reported by **LottoGroupSkips6**: _ONES, PAIRS, TRIPLES, QUADS,_ _QUINTETS,_ and _SEXTET_.

### _T_: Generate 12-Number Combinations

Generate random combinations for lotto-6, but _12 numbers per combination_. Both the _minimum_ and the _maximum_ levels of the following filters are applicable in the latest version of a tough program: _ONES, PAIRS, TRIPLES, QUADS,_ _QUINTETS,_ and _SEXTET_. High values for the minimum levels of the filters will visibly slow down the combination generating processes, especially in the case of 6-number lotto games.

### _S_: Strategy-Checking BRIGHT-12

Check the BRIGHT-12 reports generated by function **R** to see if they had any hits in past drawings.

### _H_: Strategy Hits BRIGHT-12

See how many combinations the strategies in the previous function would have generated in the _hit_ (win) situations.

### _L_: Purge Numbers from _LIE_ Files

This purge function is also very useful for a little known _**reversed**_ strategy; i.e. in conjunction with _LIE_ files. A _LIE_ file is predicted to lose or to have _NO HITS_ in the next lottery drawing (or a few drawings in the near future). For example, a 1000-line _LIE_ file has NO 4 winners; you would set the QUAD filter to 1000. But first you need to press _Y_ (for _yes_) to enable the specific _LIE_ function when the prompt appears on screen.

Program names: **Combine6**, **Combine6-12**, **Combine12**, **Lexico6**, **Wheel6**, **SkipDecaFreq6**.

For more details, read:

-   [_**Lotto, Lottery Strategy in Reverse: Not-to-Win Leads to Not-to-Lose**_](https://saliu.com/reverse-strategy.html).

### _B_: Breakdown Lines of 6+ Numbers

The big application takes one or more lines consisting of numbers and breaks down each line into smaller number-groups: from 1-number groups to groups of 7 numbers per combination (line). For example, a line with 6 or more numbers can be broken into unique (no-repeat): single numbers, 2-number groups (pairs), 3-number groups (triples), 4-number groups (quads), 5-number groups (quintets) and 6-number groups (sextets).

Program name: **BreakDownNumbers**, option: _6 = Groups of 6 numbers per combonation_.

Read special articles:

-   [_**Lotto Strategy, Software: 12-numbers Combinations Wheeled to 6-number Lotto Games**_](https://saliu.com/12-number-lotto-combinations.html).
-   [_**Lottery Strategy by Lotto Software on Positional Frequency**_](https://forums.saliu.com/lotto-software-position-frequency.html).

### _E_: Ranges of Numbers

The application **Range-6** (new software) generates 6-number lotto combinations, where the numbers in each position belong to specific RANGES (e.g. 1-15, 16-31, ..., 40-49).

-   \* See files _Range6.C_ for 6 ranges in 49-number lotto & _Range6.N_ for lotto-6 \*
-   \*\* There are two types of numeric ranges you can employ:
-   C = Ranges between a _lower_ limit and an _upper_ limit (e.g. _1 18_, _19 36_...);
-   N = _Non-contiguous_ numbers (e.g. _1 3 17 24 33 40_ - then _4 9 27 35 44 49_...).
    
    Read special lotto programming pages:
    
-   [_**Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies**_](https://saliu.com/Newsgroups.htm).
-   [_**Lottery Strategy by Lotto Software on Positional Frequency**_](https://forums.saliu.com/lotto-software-position-frequency.html).

### _7_: 7x7 Clusters for 6/49 Lotto

Generate random combinations for 6/49 lotto, but 7-by-7 clusters (matrices), like the _Shuffle_ option in **Combine6** (function _R = Randomized Combinations_, main menu). The data files must still be in the 6-numbers-per-line format as in the **Bright6** programs. The following filters are applicable in this type of software: PAIRS, TRIPLES, QUADS, QUINTETS, SEXTET. The FILTERS for this app are reported by **LottoGroupSkips6**.

Program name: **Cluster49-7**.

For a specialty presentation, visit:

-   [_**Lotto Software: 7-by-7-Number Matrices (Perfect Squares) in Lotto 6 49 Games**_](https://saliu.com/7by7-lotto-6-49.html).

## <u>IV. Menu #4: Specialty Programs for Probability, Odds, Combinatorics, Lexicographical Order, Wheeling</u>

![Special programs enhance the best-ever software for all 6-number lottery games of the world.](https://saliu.com/images/ultimate-lotto-software-63.gif)

### _S_: _Super Formula_, Definitive Probability, Statistics, and Gambling Software

_**Super Formula**_ is the definitive software for statistics, probability, odds, gambling mathematics... and much more. The functions are grouped in 12 categories. Each software category has its own detailed sub-categories. This unique application grew from the request by many people to create software to automate the calculations in the _**Fundamental Formula of Gambling (FFG)**_. _**FFG**_ discovered the most fundamental elements of theory of probability and also the Universe: The relation between the _**degree of certainty (DC)**_, _**probability p**_, and _**number of trials N**_.

There is ample information of the dedicated page:

-   [_**Formula Software for Statistics, Mathematics, Probability, Gambling**_](https://saliu.com/formula.html).

### _D_: _Birthday Paradox_ Strategies

I had written an original essay touching a few issues of creating winning systems from the _Birthday Paradox_, or the _probability of repetition_ (_duplication_). Such systems would apply to games of chance such as lotto, lottery, roulette... and more. There are lottery players and gamblers who now realize how important the probability of repetition is.

Can such mathematical knowledge be applied to gambling, especially lottery? I was skeptical when I first heard about it and was asked about it. The difficulty of achieving thorough understanding of this phenomenon was caused by a parameter I call _number of elements_. Indeed, the roulette numbers repeat. You can see them all the time, if the casinos turn on the electronic displays known as the marquees. But is there a rule, a mathematical formula that enables us to calculate the repeat probability?

I thought more deeply on this repetition fact. For example, I look at a sequence of 8 roulette numbers as an eight-element string. The degree of certainty is better than 50% that such string should contain one repetition (duplication). One of the eight numbers should be a repeat with a 50-50 chance. The same is true about lottery drawings. In this case, the element is the index of the combination (or set) drawn. Every lotto combination, for example, is defined by an _index_ or _lexicographical order_, or _lexicographic rank_.

With this new knowledge in mind, I studied some real data: Lottery drawings and roulette spins. I was somehow surprised to discover that repetition occurs close to that cutoff point of the 50-50 chance! I should also point out that the strength of the analysis and system creation is stronger at the beginning of the game. For lottery and lotto, the beginning is clear: A game starts with the first drawing of a game format.

App names: **Collisions, BirthdayParadox**.

For specialty information, study:

-   [_**Applications of**_ **Birthday Paradox**: _**Lottery, Lotto, Roulette**_](https://saliu.com/birthday-paradox.html)
-   [**Birthday Paradox**: _**Combinatorics, Probability of Duplication, Coincidences, Collisions, Repetition**_](https://saliu.com/birthday.html).

### _P_: Generate All Possible Types of Sets

This software generates ALL possible types of sets: _Exponents, permutations, arrangements, combinations_ - and _Powerball, Mega Millions, Euromillions combinations_. The software generates the sets in _lexicographical order_ or _randomly_. The sets can be _numerical_ or be composed of _words_ (_text_).

For specialty information, visit:

-   [_**Combinatorics: Permutations, Combinations, Factorial, Exponents Generate**_](https://saliu.com/permutations.html)
-   [_**Comprehensive Generating: Exponents, Permutations, Arrangements, Combinations, Powerball, Mega Millions, Euromillions, Horse Racing**_](https://saliu.com/forum/numbers-words.html).

### _R_: Shuffle or Randomize Elements

_Shuffle_ lotto combinations files (text files); then go to the line equal to the _probability median_ (_FFG = 50%_). The program can also shuffle numbers in a highly randomized manner. There is a plethora of randomization functions in this program! The program can generate lotto combinations that mimic the official lottery drawings. Included are modules for Powerball, Mega Millions, Euromillions.

For specialty information, read:

-   [_**Random Numbers: Algorithms, Shuffle, Randomize, Software**_](https://saliu.com/random-numbers.html)
-   [_**Greatly Improved Shuffle, Randomize**_](https://forums.saliu.com/shuffle-randomize-software.html).

### _L_: Software for Lexicographical Order

The program finds the _lexicographical order_ (_index_, or _rank_) of a given set and conversely finds the set for a specified index (rank, or numeral, or lexicographic order). Applicable to these set types: _Exponents, permutations, arrangements, combinations, Powerball (5+1), Euromillions (5+2) combinations_.

For specialized information, read:

-   [_**Lexicographical Order: Index, Rank, Algorithms, Combinations, Permutations**_](https://saliu.com/lexicographic.html)
-   [_**Lexicographical Order: Lotto, Powerball, Mega Millions, Euromillions**_](https://saliu.com/forum/lexicographical.html).

### _B_: Generate Combinations inside FFG Median Bell

This multi-game software generates combinations in the _FFG median_ zone and inside the _bell (Gauss) curve_. The program can be used for: pick-3 4 lotteries, horse racing, lotto-5, -6, -7, Powerball, Mega Millions '5+1', Euromillions '5+2', roulette, sports betting, and soccer pools.

For specialized information, read:

-   [_**Median Bell Random Number, Combination Generator**_](https://saliu.com/median_bell.html)
-   [_**Winning Combinations Come Predominantly from Inside the FFG Median Bell**_](https://saliu.com/random-picks.html).

### _F_: Lexicographically-Index File

This program takes a lotto data file (drawings) and adds _indexes_ (_ranks_) to the corresponding combinations in the file. The indexes are calculated based on the combination lexicographical order or index for that lotto game.

For in-depth coverage, read:

-   [_**Combination Lexicographical Order, Index of Lotto, Lottery Drawings Files**_](https://saliu.com/combination.html)
-   [_**Combinations Generator: Any Lotto, Powerball, Mega Millions, Euromillions, Horseracing, Two-In-One Lotto Games**_](https://saliu.com/combinations.html).

### _O_: Probability, Odds Calculator

The probability software calculates all the _**odds**_ of any lotto game, including Powerball, Mega Millions and Euromillions games. For example, the odds of a lotto _6/49_ game: _0 of 6_; _1 of 6_; _2 of 6_; _3 of 6_; _4 of 6_; _5 of 6_; _6 of 6_. The probability is calculated _**as exactly**_ and _**as at least**_ _M of N_.

The _Generalized_ option calculates the odds for any two-in-one lotto games, including Powerball, Mega Millions, and Euromillions. The _Horseracing_ option calculates the odds for _exactas_ (top 2 finishers), _trifectas_ (top 3 finishers), _superfectas_ (top 4 finishers), etc. The horse racing odds are calculated also as _**straight**_ and _**boxed**_.

Read all the details:

-   [_**Calculate Odds, Probability to Win Lottery, Lotto, Powerball, Mega Millions, Euromillions**_](https://saliu.com/oddslotto.html)
-   [_**Probability, Odds, Formulae, Algorithm, Software Calculator**_](https://saliu.com/bbs/messages/266.html).

### _V_: Universal Lotto Combination Generator

Lotto software generates combinations for absolutely any type of lotto game, plus horse racing straight sets. Specifically to this program: the combinations can be generated in _steps_. That is, the user has the choice to generate lotto combinations with constant gaps or skips between them. For example, starting at the very top of a combination set (the lexicographical order #1), then step 90, the following combination generated will have lexicographic order #91,…, and so on, to the very last combination in the lotto set.

Most certainly, no other lottery program can generate lotto combinations in steps. Furthermore, this incredible lotto software application even generates combinations within a _range_ of numbers, or between any lexicographic order indexes (_ranks_).

Read all the details:

-   [_**Combinations Generator: Lotto, Powerball, Mega Millions, Euromillions, Horse Racing, Two-In-One Lotto Games**_](https://saliu.com/combinations.html).

### _G_: _Wonder-Grid_ Checking for Lotto-6 Pairings

The lotto application checks the past performance of the _GRID6_ files. The program starts a number of draws back in the _DATA-6_ lotto file and creates 3 _GRID6_ files: for (_N / 2_), _N_, and (_N \* 2_) draws. Each range of analysis N creates its own report file (_ChkGrid6.\*_).

It can be used well in conjunction with the _LIE_ (_reversed_ lotto strategy) feature in the combination generators. The _wonder grid_ skips more lotto drawings compared to the winning situations.

For more details, read:

-   [_**Lottery Wonder-Grid Revisited: New Lotto Pairing Research, Software**_](https://saliu.com/bbs/messages/grid.html)
-   [_**Lotto Wonder-grid: Lottery Reports and Lotto Software**_](https://saliu.com/bbs/messages/9.html).

### _K_: Wonder-Grid Pairs, Statistical Reports

This program creates all the statistical files for 3-drawing ranges - calculated by the _**FFG**_. The files have the extensions 1 to 3 in their names. The following files are created: _FREQ6_, _PAIRS6, GRID6, BEST6, WORST6, LEAST6_. _GRID6_ has each digit plus its 5 pairs; _BEST6_ = the top (N-1)/6 pairs; _WORST6_ has the worst (N-1)/5 pairs; _LEAST6_ has the pairs with a frequency equal to 0. At the end, the program combines all _GRID6.\* BEST6.\*, WORST6.\*, LEAST6.\*_ to _GRID6, BEST6, WORST6, LEAST6_.

This app is useful in conjunction with the _LIE_ (_**reversed**_) strategy feature present in all lotto combination generators. The pairings do not lead immediately to winning combinations more often than not.

For more details, read:

-   [_**Lottery Wonder-Grid Revisited: New Lotto Pairing Research, Software**_](https://saliu.com/bbs/messages/grid.html)
-   [_**Lotto, Lottery Software, Utilities**_](https://saliu.com/lottery-utility.html).

### _W_: Lotto Wheeling Software: Fill Out Lottery Wheels with Player's Lotto Picks

The ultimate lotto wheeling program takes a source wheel file and replaces the _system numbers_ with user's lotto picks to a destination file.

For more information and your free lotto wheels, visit:

-   [_**Lotto Wheeling Software: Fill out Lottery Wheels with Player's Lotto Picks**_](https://saliu.com/bbs/messages/857.html)
-   [_**Create, Make Lotto Wheels, Lottery Wheeling Software**_](https://saliu.com/lottowheel.html).

### _E_: Lotto Wheels Based on Odds and Lexicographic Order

The special lotto wheeling software generates lotto wheels based on the lotto odds. The user selects first the numbers in the lotto game; e.g. 49, then 6. Next, the user selects the _guarantee_; e.g. 4 to indicate a guarantee such as _4 of 6_, or _4 of 6_, or _4 of 7_, etc. Finally, you select the _starting_ lexicographical index; the program automatically calculates the lowest index and the highest lexicographic rank possible.

For specialty information:

-   [_**Wheels, Balanced Lotto Wheels, Lexicographic Order Index**_](https://saliu.com/bbs/messages/772.html).

### _C_: Check Abbreviated Lotto Systems or Wheels

The special lotto wheeling software verifies the lotto wheels for missing combinations; if missing, the software will plug-in the lines needed to complete the lotto wheel. Also, the program generates original abbreviated lotto systems.

See:

-   [_**Software to Verify Lotto Wheels for Missing Combinations; Generate Reduced Lotto Systems**_](https://saliu.com/check-wheels.html).

### _H_: Play-Last-N-Draws as a Lotto Wheel

The lottery utilities check for winning numbers in files of real drawings. A data file will be checked against itself as if playing the last N draws before current draw. For example, check the wins when I play the last 57 draws in a 6/49 lotto game. The odds of _3 of 6_ are _1 in 57_ as calculated by the **OddsCalc** probability program. How many _3 of 6_ hits (and other hits) will be recorded if playing in 100 future 6/49 lottery drawings?

View:

-   [_**Wheeling All Lotto Numbers Formula: Play Last N Lottery Draws**_](https://saliu.com/wheel.html).

### _U_: Old Lotto-6 Utilities

This piece of software was superseded by _**Super Utilities**_ for _lotto five_ (option _U_ in the main menu). Just nostalgia, I guess! _O tempora! O mores!_

Program name: **Util632**.

You might want to read:

-   [_**Lotto, Lottery Software, Utilities**_](https://saliu.com/lottery-utility.html).

[

## <u>Resources in Lottery Software, Systems, Lotto Wheeling</u>

](https://saliu.com/content/lottery.html)

![These powerful programs for pick-6 lotto, Loto 6 help you tremendously winning lottery jackpots.](https://saliu.com/HLINE.gif)

**[Home](https://saliu.com/index.htm) | [Search](https://saliu.com/Search.htm) | [New Writings](https://saliu.com/bbs/index.html) | [Odds, Generator](https://saliu.com/calculator_generator.html) | [Contents](https://saliu.com/content/index.html) | [Forums](https://forums.saliu.com/) | [Sitemap](https://saliu.com/sitemap/index.html)**

![Ultimate winning lotto 6 software was created by the Founder of lottery programming mathematics.](https://saliu.com/HLINE.gif)
