# Compact Data Structures
# 緊湊數據結構 - 減少記憶體使用，提高數據處理效率

using Dates

"""
緊湊彩票開獎結構
使用位操作和緊湊編碼來減少記憶體使用
"""
struct CompactLotteryDraw
    # 使用 64 位整數來存儲所有信息
    # 位分配：
    # - 位 0-5:   號碼1 (0-39, 6位)
    # - 位 6-11:  號碼2 (0-39, 6位)
    # - 位 12-17: 號碼3 (0-39, 6位)
    # - 位 18-23: 號碼4 (0-39, 6位)
    # - 位 24-29: 號碼5 (0-39, 6位)
    # - 位 30-45: 開獎ID (0-65535, 16位)
    # - 位 46-63: 日期偏移 (從基準日期開始的天數, 18位)
    data::UInt64
    
    function CompactLotteryDraw(data::UInt64)
        new(data)
    end
end

# 基準日期：2000年1月1日
const BASE_DATE = Date(2000, 1, 1)

"""
將普通 LotteryDraw 轉換為緊湊格式
"""
function compact_lottery_draw(draw::LotteryDraw)::CompactLotteryDraw
    # 確保號碼已排序
    sorted_numbers = sort(draw.numbers)
    
    # 檢查號碼範圍
    for num in sorted_numbers
        if num < 1 || num > 39
            throw(ArgumentError("號碼必須在 1-39 範圍內: $num"))
        end
    end
    
    if length(sorted_numbers) != 5
        throw(ArgumentError("必須有 5 個號碼"))
    end
    
    # 計算日期偏移
    date_offset = Dates.value(draw.draw_date - BASE_DATE)
    if date_offset < 0 || date_offset > 262143  # 2^18 - 1
        throw(ArgumentError("日期超出支持範圍"))
    end
    
    # 檢查開獎ID範圍
    if draw.draw_id < 0 || draw.draw_id > 65535  # 2^16 - 1
        throw(ArgumentError("開獎ID超出支持範圍: $(draw.draw_id)"))
    end
    
    # 編碼數據
    data = UInt64(0)
    
    # 編碼號碼 (每個號碼減1以適應0-38範圍)
    data |= UInt64(sorted_numbers[1] - 1) << 0
    data |= UInt64(sorted_numbers[2] - 1) << 6
    data |= UInt64(sorted_numbers[3] - 1) << 12
    data |= UInt64(sorted_numbers[4] - 1) << 18
    data |= UInt64(sorted_numbers[5] - 1) << 24
    
    # 編碼開獎ID
    data |= UInt64(draw.draw_id) << 30
    
    # 編碼日期偏移
    data |= UInt64(date_offset) << 46
    
    return CompactLotteryDraw(data)
end

"""
將緊湊格式轉換回普通 LotteryDraw
"""
function decode_compact_draw(compact::CompactLotteryDraw)::LotteryDraw
    data = compact.data
    
    # 解碼號碼
    numbers = Vector{Int}(undef, 5)
    numbers[1] = Int((data >> 0) & 0x3F) + 1   # 6位掩碼
    numbers[2] = Int((data >> 6) & 0x3F) + 1
    numbers[3] = Int((data >> 12) & 0x3F) + 1
    numbers[4] = Int((data >> 18) & 0x3F) + 1
    numbers[5] = Int((data >> 24) & 0x3F) + 1
    
    # 解碼開獎ID
    draw_id = Int((data >> 30) & 0xFFFF)  # 16位掩碼
    
    # 解碼日期
    date_offset = Int((data >> 46) & 0x3FFFF)  # 18位掩碼
    draw_date = BASE_DATE + Day(date_offset)
    
    return LotteryDraw(numbers, draw_date, draw_id)
end

"""
緊湊數據集合
"""
struct CompactLotteryDataset
    draws::Vector{CompactLotteryDraw}
    metadata::Dict{String, Any}
    
    function CompactLotteryDataset(draws::Vector{CompactLotteryDraw})
        metadata = Dict{String, Any}(
            "count" => length(draws),
            "created_at" => now(),
            "memory_saved_bytes" => calculate_memory_savings(length(draws))
        )
        new(draws, metadata)
    end
end

"""
計算記憶體節省量
"""
function calculate_memory_savings(count::Int)::Int
    # 原始 LotteryDraw 大約使用：
    # - Vector{Int}(5): 40 + 5*8 = 80 bytes
    # - Date: 8 bytes
    # - Int (draw_id): 8 bytes
    # - 結構開銷: ~16 bytes
    # 總計: ~112 bytes
    
    # CompactLotteryDraw 使用：
    # - UInt64: 8 bytes
    # 總計: 8 bytes
    
    original_size = count * 112
    compact_size = count * 8
    return original_size - compact_size
end

"""
從普通數據集創建緊湊數據集
"""
function create_compact_dataset(draws::Vector{LotteryDraw})::CompactLotteryDataset
    compact_draws = Vector{CompactLotteryDraw}()
    sizehint!(compact_draws, length(draws))
    
    for draw in draws
        try
            compact_draw = compact_lottery_draw(draw)
            push!(compact_draws, compact_draw)
        catch e
            @warn "無法壓縮開獎數據: $draw, 錯誤: $e"
        end
    end
    
    return CompactLotteryDataset(compact_draws)
end

"""
從緊湊數據集恢復普通數據集
"""
function decode_compact_dataset(compact_dataset::CompactLotteryDataset)::Vector{LotteryDraw}
    draws = Vector{LotteryDraw}()
    sizehint!(draws, length(compact_dataset.draws))
    
    for compact_draw in compact_dataset.draws
        try
            draw = decode_compact_draw(compact_draw)
            push!(draws, draw)
        catch e
            @warn "無法解碼緊湊開獎數據, 錯誤: $e"
        end
    end
    
    return draws
end

"""
驗證數據完整性
"""
function verify_data_integrity(original::Vector{LotteryDraw}, compact_dataset::CompactLotteryDataset)::Bool
    if length(original) != length(compact_dataset.draws)
        return false
    end
    
    decoded = decode_compact_dataset(compact_dataset)
    
    for (i, (orig, dec)) in enumerate(zip(original, decoded))
        if orig.numbers != dec.numbers || orig.draw_date != dec.draw_date || orig.draw_id != dec.draw_id
            @warn "數據不一致在索引 $i: 原始=$orig, 解碼=$dec"
            return false
        end
    end
    
    return true
end

"""
緊湊數據統計
"""
function get_compact_dataset_stats(compact_dataset::CompactLotteryDataset)::Dict{String, Any}
    count = length(compact_dataset.draws)
    
    if count == 0
        return Dict(
            "count" => 0,
            "memory_usage_bytes" => 0,
            "memory_saved_bytes" => 0,
            "compression_ratio" => 0.0
        )
    end
    
    # 計算記憶體使用
    compact_memory = count * 8  # 每個 CompactLotteryDraw 8 bytes
    original_memory = count * 112  # 估算原始記憶體使用
    memory_saved = original_memory - compact_memory
    compression_ratio = compact_memory / original_memory
    
    # 分析日期範圍
    decoded_draws = decode_compact_dataset(compact_dataset)
    dates = [draw.draw_date for draw in decoded_draws]
    min_date = minimum(dates)
    max_date = maximum(dates)
    
    # 分析開獎ID範圍
    draw_ids = [draw.draw_id for draw in decoded_draws]
    min_id = minimum(draw_ids)
    max_id = maximum(draw_ids)
    
    return Dict(
        "count" => count,
        "memory_usage_bytes" => compact_memory,
        "memory_saved_bytes" => memory_saved,
        "compression_ratio" => compression_ratio,
        "memory_savings_percentage" => (memory_saved / original_memory) * 100,
        "date_range" => (min_date, max_date),
        "draw_id_range" => (min_id, max_id),
        "created_at" => compact_dataset.metadata["created_at"]
    )
end

"""
緊湊數據訪問器 - 無需完全解碼即可訪問特定字段
"""
struct CompactDataAccessor
    dataset::CompactLotteryDataset
end

"""
獲取指定索引的號碼（無需完全解碼）
"""
function get_numbers_at(accessor::CompactDataAccessor, index::Int)::Vector{Int}
    if index < 1 || index > length(accessor.dataset.draws)
        throw(BoundsError("索引超出範圍: $index"))
    end
    
    data = accessor.dataset.draws[index].data
    
    numbers = Vector{Int}(undef, 5)
    numbers[1] = Int((data >> 0) & 0x3F) + 1
    numbers[2] = Int((data >> 6) & 0x3F) + 1
    numbers[3] = Int((data >> 12) & 0x3F) + 1
    numbers[4] = Int((data >> 18) & 0x3F) + 1
    numbers[5] = Int((data >> 24) & 0x3F) + 1
    
    return numbers
end

"""
獲取指定索引的開獎ID（無需完全解碼）
"""
function get_draw_id_at(accessor::CompactDataAccessor, index::Int)::Int
    if index < 1 || index > length(accessor.dataset.draws)
        throw(BoundsError("索引超出範圍: $index"))
    end
    
    data = accessor.dataset.draws[index].data
    return Int((data >> 30) & 0xFFFF)
end

"""
獲取指定索引的日期（無需完全解碼）
"""
function get_date_at(accessor::CompactDataAccessor, index::Int)::Date
    if index < 1 || index > length(accessor.dataset.draws)
        throw(BoundsError("索引超出範圍: $index"))
    end
    
    data = accessor.dataset.draws[index].data
    date_offset = Int((data >> 46) & 0x3FFFF)
    return BASE_DATE + Day(date_offset)
end

"""
檢查指定索引是否包含某個號碼（無需完全解碼）
"""
function contains_number_at(accessor::CompactDataAccessor, index::Int, number::Int)::Bool
    if index < 1 || index > length(accessor.dataset.draws)
        return false
    end
    
    if number < 1 || number > 39
        return false
    end
    
    data = accessor.dataset.draws[index].data
    target = UInt64(number - 1)  # 轉換為0-38範圍
    
    # 檢查每個號碼位置
    for shift in [0, 6, 12, 18, 24]
        if ((data >> shift) & 0x3F) == target
            return true
        end
    end
    
    return false
end

"""
創建緊湊數據訪問器
"""
function create_compact_accessor(compact_dataset::CompactLotteryDataset)::CompactDataAccessor
    return CompactDataAccessor(compact_dataset)
end

# 導出主要函數和結構
export CompactLotteryDraw, CompactLotteryDataset, CompactDataAccessor
export compact_lottery_draw, decode_compact_draw
export create_compact_dataset, decode_compact_dataset
export verify_data_integrity, get_compact_dataset_stats
export create_compact_accessor, get_numbers_at, get_draw_id_at, get_date_at
export contains_number_at, calculate_memory_savings
