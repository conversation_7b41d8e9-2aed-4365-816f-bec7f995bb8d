#!/usr/bin/env julia

"""
Comprehensive unit test suite for Wonder Grid Lottery System
Tests all core components including data validation, FFG calculations, pairing analysis,
Wonder Grid strategy execution, LIE elimination, and performance tests
"""

using Dates
using Base.Threads

# Include all required modules
include("src/wonder_grid_engine.jl")
include("src/lie_elimination.jl")
include("src/backtesting.jl")
include("src/performance_reporting.jl")
include("src/configuration.jl")
include("src/result_display.jl")
include("src/performance_optimization.jl")
include("src/concurrent_processing.jl")

"""
Main comprehensive unit test runner
"""
function run_comprehensive_unit_tests()
    println("🧪 Wonder Grid Comprehensive Unit Test Suite")
    println("=" ^ 80)
    
    test_results = Dict{String, Dict{String, Any}}()
    
    # Test 1: Data validation tests
    println("\n📊 Test Suite 1: Data Validation")
    println("-" ^ 50)
    test_results["data_validation"] = test_data_validation()
    
    # Test 2: FFG calculation tests
    println("\n🔢 Test Suite 2: FFG Calculations")
    println("-" ^ 50)
    test_results["ffg_calculations"] = test_ffg_calculations()
    
    # Test 3: Pairing analysis tests
    println("\n🔗 Test Suite 3: Pairing Analysis")
    println("-" ^ 50)
    test_results["pairing_analysis"] = test_pairing_analysis()
    
    # Test 4: Wonder Grid strategy tests
    println("\n🎯 Test Suite 4: Wonder Grid Strategy")
    println("-" ^ 50)
    test_results["wonder_grid_strategy"] = test_wonder_grid_strategy()
    
    # Test 5: LIE elimination tests
    println("\n🚫 Test Suite 5: LIE Elimination")
    println("-" ^ 50)
    test_results["lie_elimination"] = test_lie_elimination()
    
    # Test 6: Backtesting engine tests
    println("\n🏃 Test Suite 6: Backtesting Engine")
    println("-" ^ 50)
    test_results["backtesting_engine"] = test_backtesting_engine()
    
    # Test 7: Performance reporting tests
    println("\n📈 Test Suite 7: Performance Reporting")
    println("-" ^ 50)
    test_results["performance_reporting"] = test_performance_reporting()
    
    # Test 8: Configuration system tests
    println("\n⚙️  Test Suite 8: Configuration System")
    println("-" ^ 50)
    test_results["configuration_system"] = test_configuration_system()
    
    # Test 9: Result display tests
    println("\n🖥️  Test Suite 9: Result Display")
    println("-" ^ 50)
    test_results["result_display"] = test_result_display()
    
    # Test 10: Performance optimization tests
    println("\n⚡ Test Suite 10: Performance Optimization")
    println("-" ^ 50)
    test_results["performance_optimization"] = test_performance_optimization()
    
    # Test 11: Concurrent processing tests
    println("\n🔄 Test Suite 11: Concurrent Processing")
    println("-" ^ 50)
    test_results["concurrent_processing"] = test_concurrent_processing()
    
    # Test 12: Large dataset performance tests
    println("\n📊 Test Suite 12: Large Dataset Performance")
    println("-" ^ 50)
    test_results["large_dataset_performance"] = test_large_dataset_performance()
    
    # Generate comprehensive test report
    println("\n📋 Generating Comprehensive Test Report")
    println("-" ^ 50)
    generate_test_report(test_results)
    
    println("\n✅ Comprehensive Unit Test Suite Complete!")
    println("=" ^ 80)
    
    return test_results
end

"""
Test data validation functionality
"""
function test_data_validation()
    test_results = Dict{String, Any}("passed" => 0, "failed" => 0, "details" => [])
    
    try
        # Test 1.1: Lottery draw validation
        println("  Testing lottery draw validation...")
        
        # Valid draw
        valid_draw = LotteryDraw(Date("2023-01-01"), [1, 5, 12, 23, 39])
        @assert valid_draw.draw_date == Date("2023-01-01")
        @assert valid_draw.numbers == [1, 5, 12, 23, 39]
        
        push!(test_results["details"], "✅ Valid lottery draw creation")
        test_results["passed"] += 1
        
        # Test invalid numbers (out of range)
        try
            invalid_draw = LotteryDraw(Date("2023-01-01"), [0, 5, 12, 23, 40])
            push!(test_results["details"], "❌ Should reject invalid number range")
            test_results["failed"] += 1
        catch
            push!(test_results["details"], "✅ Correctly rejects invalid number range")
            test_results["passed"] += 1
        end
        
        # Test 1.2: Combination validation
        println("  Testing combination validation...")
        
        # Valid combination
        valid_combo = [1, 5, 12, 23, 39]
        @assert length(valid_combo) == 5
        @assert all(n -> 1 <= n <= 39, valid_combo)
        @assert length(unique(valid_combo)) == 5
        
        push!(test_results["details"], "✅ Valid combination structure")
        test_results["passed"] += 1
        
        # Test duplicate numbers
        invalid_combo = [1, 1, 12, 23, 39]
        @assert length(unique(invalid_combo)) != 5
        
        push!(test_results["details"], "✅ Duplicate number detection")
        test_results["passed"] += 1
        
        # Test 1.3: Date validation
        println("  Testing date validation...")
        
        # Valid date range
        start_date = Date("2020-01-01")
        end_date = Date("2023-12-31")
        @assert start_date < end_date
        
        push!(test_results["details"], "✅ Date range validation")
        test_results["passed"] += 1
        
        # Test 1.4: Key number validation
        println("  Testing key number validation...")
        
        valid_keys = [1, 13, 25, 39]
        for key in valid_keys
            @assert 1 <= key <= 39
        end
        
        invalid_keys = [0, 40, -5, 100]
        for key in invalid_keys
            @assert !(1 <= key <= 39)
        end
        
        push!(test_results["details"], "✅ Key number range validation")
        test_results["passed"] += 1
        
        println("  ✅ Data validation tests completed")
        
    catch e
        push!(test_results["details"], "❌ Data validation error: $e")
        test_results["failed"] += 1
        println("  ❌ Data validation tests failed: $e")
    end
    
    return test_results
end

"""
Test FFG calculation functionality
"""
function test_ffg_calculations()
    test_results = Dict{String, Any}("passed" => 0, "failed" => 0, "details" => [])
    
    try
        # Test 2.1: Basic FFG calculation
        println("  Testing basic FFG calculation...")
        
        engine = WonderGridEngine()
        
        # Test known key numbers
        test_keys = [7, 13, 21, 29]
        
        for key_number in test_keys
            ffg_numbers = calculate_ffg(engine, key_number)
            
            # Verify FFG properties
            @assert isa(ffg_numbers, Vector{Int})
            @assert all(n -> 1 <= n <= 39, ffg_numbers)
            @assert issorted(ffg_numbers)
            @assert length(unique(ffg_numbers)) == length(ffg_numbers)  # No duplicates
            
            # Verify key number is not in FFG (it should be excluded)
            @assert !(key_number in ffg_numbers)
        end
        
        push!(test_results["details"], "✅ Basic FFG calculation for multiple keys")
        test_results["passed"] += 1
        
        # Test 2.2: FFG cycle detection
        println("  Testing FFG cycle detection...")
        
        # Test that FFG terminates (doesn't infinite loop)
        for key_number in 1:39
            start_time = time()
            ffg_numbers = calculate_ffg(engine, key_number)
            calculation_time = time() - start_time
            
            # Should complete quickly (< 1 second)
            @assert calculation_time < 1.0
            
            # Should have reasonable number of results
            @assert length(ffg_numbers) <= 38  # Maximum possible (39 - key_number)
        end
        
        push!(test_results["details"], "✅ FFG cycle detection and termination")
        test_results["passed"] += 1
        
        # Test 2.3: FFG consistency
        println("  Testing FFG consistency...")
        
        # Same key should produce same FFG
        key_number = 13
        ffg1 = calculate_ffg(engine, key_number)
        ffg2 = calculate_ffg(engine, key_number)
        
        @assert ffg1 == ffg2
        
        push!(test_results["details"], "✅ FFG calculation consistency")
        test_results["passed"] += 1
        
        # Test 2.4: Edge cases
        println("  Testing FFG edge cases...")
        
        # Test boundary key numbers
        boundary_keys = [1, 2, 38, 39]
        
        for key_number in boundary_keys
            ffg_numbers = calculate_ffg(engine, key_number)
            @assert isa(ffg_numbers, Vector{Int})
            # Some boundary cases might produce empty FFG, which is valid
        end
        
        push!(test_results["details"], "✅ FFG edge case handling")
        test_results["passed"] += 1
        
        println("  ✅ FFG calculation tests completed")
        
    catch e
        push!(test_results["details"], "❌ FFG calculation error: $e")
        test_results["failed"] += 1
        println("  ❌ FFG calculation tests failed: $e")
    end
    
    return test_results
end

"""
Test pairing analysis functionality
"""
function test_pairing_analysis()
    test_results = Dict{String, Any}("passed" => 0, "failed" => 0, "details" => [])
    
    try
        # Test 3.1: Pairing frequency calculation
        println("  Testing pairing frequency calculation...")
        
        engine = WonderGridEngine()
        
        # Add test combinations
        test_combinations = [
            [1, 5, 12, 23, 39],
            [3, 7, 15, 28, 35],
            [1, 5, 16, 24, 38],  # Contains pair (1,5) again
            [2, 11, 19, 31, 37]
        ]
        
        for combo in test_combinations
            add_combination_to_pairing!(engine, combo)
        end
        
        # Test specific pair frequency
        pair_1_5_freq = get_pairing_frequency(engine, 1, 5)
        @assert pair_1_5_freq == 2  # Appears in 2 combinations
        
        pair_3_7_freq = get_pairing_frequency(engine, 3, 7)
        @assert pair_3_7_freq == 1  # Appears in 1 combination
        
        pair_nonexistent_freq = get_pairing_frequency(engine, 1, 3)
        @assert pair_nonexistent_freq == 0  # Doesn't appear together
        
        push!(test_results["details"], "✅ Pairing frequency calculation")
        test_results["passed"] += 1
        
        # Test 3.2: Pairing matrix properties
        println("  Testing pairing matrix properties...")
        
        # Test symmetry: freq(a,b) == freq(b,a)
        @assert get_pairing_frequency(engine, 1, 5) == get_pairing_frequency(engine, 5, 1)
        @assert get_pairing_frequency(engine, 3, 7) == get_pairing_frequency(engine, 7, 3)
        
        push!(test_results["details"], "✅ Pairing matrix symmetry")
        test_results["passed"] += 1
        
        # Test 3.3: Pairing statistics
        println("  Testing pairing statistics...")
        
        # Calculate total pairs
        total_pairs = 0
        for combo in test_combinations
            total_pairs += binomial(5, 2)  # 10 pairs per combination
        end
        
        @assert engine.total_pairs == total_pairs
        
        push!(test_results["details"], "✅ Pairing statistics tracking")
        test_results["passed"] += 1
        
        # Test 3.4: Most frequent pairs
        println("  Testing most frequent pairs identification...")
        
        most_frequent_pairs = get_most_frequent_pairs(engine, 3)
        
        @assert isa(most_frequent_pairs, Vector)
        @assert length(most_frequent_pairs) <= 3
        
        # Verify pairs are sorted by frequency (descending)
        if length(most_frequent_pairs) > 1
            for i in 1:(length(most_frequent_pairs)-1)
                freq1 = most_frequent_pairs[i][3]  # frequency is third element
                freq2 = most_frequent_pairs[i+1][3]
                @assert freq1 >= freq2
            end
        end
        
        push!(test_results["details"], "✅ Most frequent pairs identification")
        test_results["passed"] += 1
        
        println("  ✅ Pairing analysis tests completed")
        
    catch e
        push!(test_results["details"], "❌ Pairing analysis error: $e")
        test_results["failed"] += 1
        println("  ❌ Pairing analysis tests failed: $e")
    end
    
    return test_results
end

"""
Test Wonder Grid strategy execution
"""
function test_wonder_grid_strategy()
    test_results = Dict{String, Any}("passed" => 0, "failed" => 0, "details" => [])
    
    try
        # Test 4.1: Combination generation
        println("  Testing combination generation...")
        
        engine = WonderGridEngine()
        key_number = 13
        
        combinations = generate_combinations(engine, key_number)
        
        @assert isa(combinations, Vector{Vector{Int}})
        
        if !isempty(combinations)
            # Verify all combinations are valid
            for combo in combinations
                @assert length(combo) == 5
                @assert all(n -> 1 <= n <= 39, combo)
                @assert length(unique(combo)) == 5  # No duplicates
                @assert issorted(combo)  # Should be sorted
            end
            
            # Verify no duplicate combinations
            unique_combinations = unique(combinations)
            @assert length(unique_combinations) == length(combinations)
            
            push!(test_results["details"], "✅ Valid combination generation ($(length(combinations)) combinations)")
            test_results["passed"] += 1
        else
            push!(test_results["details"], "⚠️  No combinations generated for key $key_number")
            test_results["passed"] += 1  # This might be valid for some keys
        end
        
        # Test 4.2: Multiple key numbers
        println("  Testing multiple key numbers...")
        
        test_keys = [7, 13, 21, 29]
        key_results = Dict{Int, Int}()
        
        for key in test_keys
            combos = generate_combinations(engine, key)
            key_results[key] = length(combos)
            
            # Each key should produce consistent results
            combos2 = generate_combinations(engine, key)
            @assert length(combos) == length(combos2)
            @assert combos == combos2
        end
        
        push!(test_results["details"], "✅ Multiple key number consistency")
        test_results["passed"] += 1
        
        # Test 4.3: Strategy properties
        println("  Testing strategy properties...")
        
        # Test that combinations are based on FFG numbers
        key_number = 7
        ffg_numbers = calculate_ffg(engine, key_number)
        combinations = generate_combinations(engine, key_number)
        
        if !isempty(combinations) && !isempty(ffg_numbers)
            # All combination numbers should come from FFG
            for combo in combinations
                for num in combo
                    @assert num in ffg_numbers
                end
            end
            
            push!(test_results["details"], "✅ Combinations based on FFG numbers")
            test_results["passed"] += 1
        else
            push!(test_results["details"], "⚠️  Insufficient data for FFG-combination verification")
            test_results["passed"] += 1
        end
        
        # Test 4.4: Performance characteristics
        println("  Testing performance characteristics...")
        
        # Generation should be reasonably fast
        start_time = time()
        combinations = generate_combinations(engine, 13)
        generation_time = time() - start_time
        
        @assert generation_time < 5.0  # Should complete within 5 seconds
        
        push!(test_results["details"], "✅ Performance characteristics ($(round(generation_time, digits=3))s)")
        test_results["passed"] += 1
        
        println("  ✅ Wonder Grid strategy tests completed")
        
    catch e
        push!(test_results["details"], "❌ Wonder Grid strategy error: $e")
        test_results["failed"] += 1
        println("  ❌ Wonder Grid strategy tests failed: $e")
    end
    
    return test_results
end

"""
Test LIE elimination functionality
"""
function test_lie_elimination()
    test_results = Dict{String, Any}("passed" => 0, "failed" => 0, "details" => [])
    
    try
        # Test 5.1: LIE engine creation
        println("  Testing LIE engine creation...")
        
        # Create test historical data
        historical_draws = [
            LotteryDraw(Date("2023-01-01"), [1, 5, 12, 23, 39]),
            LotteryDraw(Date("2023-01-02"), [3, 7, 15, 28, 35]),
            LotteryDraw(Date("2023-01-03"), [2, 11, 19, 31, 37]),
            LotteryDraw(Date("2023-01-04"), [4, 9, 16, 25, 33]),
            LotteryDraw(Date("2023-01-05"), [6, 13, 20, 29, 38])
        ]
        
        lie_engine = LIEEliminationEngine(historical_draws, 0.1)
        
        @assert isa(lie_engine, LIEEliminationEngine)
        @assert lie_engine.threshold == 0.1
        @assert length(lie_engine.historical_draws) == 5
        
        push!(test_results["details"], "✅ LIE engine creation")
        test_results["passed"] += 1
        
        # Test 5.2: Pattern analysis
        println("  Testing pattern analysis...")
        
        patterns = analyze_patterns(lie_engine)
        
        @assert isa(patterns, Dict)
        @assert haskey(patterns, "number_frequencies")
        @assert haskey(patterns, "sum_distribution")
        @assert haskey(patterns, "odd_even_patterns")
        
        # Verify number frequencies
        number_freqs = patterns["number_frequencies"]
        @assert isa(number_freqs, Dict{Int, Int})
        @assert all(1 <= k <= 39 for k in keys(number_freqs))
        @assert all(v >= 0 for v in values(number_freqs))
        
        push!(test_results["details"], "✅ Pattern analysis")
        test_results["passed"] += 1
        
        # Test 5.3: Combination elimination
        println("  Testing combination elimination...")
        
        # Create test combinations
        test_combinations = [
            [1, 5, 12, 23, 39],  # Matches historical draw exactly
            [2, 8, 14, 26, 32],  # Different pattern
            [1, 2, 3, 4, 5],     # Consecutive numbers (unusual)
            [10, 15, 20, 25, 30] # Arithmetic sequence (unusual)
        ]
        
        filtered_combinations = eliminate_combinations(lie_engine, test_combinations)
        
        @assert isa(filtered_combinations, Vector{Vector{Int}})
        @assert length(filtered_combinations) <= length(test_combinations)
        
        # Verify all remaining combinations are valid
        for combo in filtered_combinations
            @assert length(combo) == 5
            @assert all(n -> 1 <= n <= 39, combo)
            @assert length(unique(combo)) == 5
        end
        
        push!(test_results["details"], "✅ Combination elimination ($(length(test_combinations)) → $(length(filtered_combinations)))")
        test_results["passed"] += 1
        
        # Test 5.4: Threshold sensitivity
        println("  Testing threshold sensitivity...")
        
        # Test different thresholds
        thresholds = [0.05, 0.1, 0.2, 0.5]
        elimination_counts = []
        
        for threshold in thresholds
            lie_engine_thresh = LIEEliminationEngine(historical_draws, threshold)
            filtered = eliminate_combinations(lie_engine_thresh, test_combinations)
            push!(elimination_counts, length(test_combinations) - length(filtered))
        end
        
        # Higher thresholds should generally eliminate more combinations
        # (though this isn't guaranteed for all datasets)
        @assert all(count >= 0 for count in elimination_counts)
        
        push!(test_results["details"], "✅ Threshold sensitivity testing")
        test_results["passed"] += 1
        
        # Test 5.5: Integration with Wonder Grid
        println("  Testing Wonder Grid integration...")
        
        wg_engine = WonderGridEngine()
        combinations = generate_combinations(wg_engine, 13)
        
        if !isempty(combinations)
            # Apply LIE elimination
            original_count = length(combinations)
            filtered_combinations = eliminate_combinations(lie_engine, combinations)
            filtered_count = length(filtered_combinations)
            
            @assert filtered_count <= original_count
            
            # Verify filtered combinations are still valid
            for combo in filtered_combinations[1:min(10, length(filtered_combinations))]
                @assert length(combo) == 5
                @assert all(n -> 1 <= n <= 39, combo)
            end
            
            push!(test_results["details"], "✅ Wonder Grid integration ($original_count → $filtered_count)")
            test_results["passed"] += 1
        else
            push!(test_results["details"], "⚠️  No combinations for LIE integration test")
            test_results["passed"] += 1
        end
        
        println("  ✅ LIE elimination tests completed")
        
    catch e
        push!(test_results["details"], "❌ LIE elimination error: $e")
        test_results["failed"] += 1
        println("  ❌ LIE elimination tests failed: $e")
    end
    
    return test_results
end

"""
Test backtesting engine functionality
"""
function test_backtesting_engine()
    test_results = Dict{String, Any}("passed" => 0, "failed" => 0, "details" => [])
    
    try
        # Test 6.1: Backtesting engine creation
        println("  Testing backtesting engine creation...")
        
        engine = BacktestingEngine()
        @assert isa(engine, BacktestingEngine)
        
        push!(test_results["details"], "✅ Backtesting engine creation")
        test_results["passed"] += 1
        
        # Test 6.2: Basic backtest execution
        println("  Testing basic backtest execution...")
        
        # Create test data
        test_combinations = [
            [1, 5, 12, 23, 39],
            [3, 7, 15, 28, 35],
            [2, 11, 19, 31, 37]
        ]
        
        test_draws = [
            LotteryDraw(Date("2023-01-01"), [1, 5, 12, 23, 39]),  # Perfect match with first combo
            LotteryDraw(Date("2023-01-02"), [1, 5, 12, 28, 35]),  # 3 matches with first combo
            LotteryDraw(Date("2023-01-03"), [10, 14, 18, 22, 26]) # No significant matches
        ]
        
        result = run_backtest(engine, test_combinations, test_draws)
        
        @assert isa(result, BacktestResult)
        @assert haskey(result.hit_rates, "3/5")
        @assert haskey(result.hit_rates, "4/5")
        @assert haskey(result.hit_rates, "5/5")
        
        # Verify hit rates are valid probabilities
        for (tier, rate) in result.hit_rates
            @assert 0.0 <= rate <= 1.0
        end
        
        # We should have at least one 5/5 hit (perfect match)
        @assert result.hit_rates["5/5"] > 0.0
        
        push!(test_results["details"], "✅ Basic backtest execution")
        test_results["passed"] += 1
        
        # Test 6.3: Hit rate calculations
        println("  Testing hit rate calculations...")
        
        # Manual verification of expected results
        total_tests = length(test_combinations) * length(test_draws)
        
        # Count expected hits manually
        expected_5_5 = 1  # One perfect match
        expected_3_5 = 1  # One 3-match
        
        calculated_5_5 = round(Int, result.hit_rates["5/5"] * total_tests)
        calculated_3_5 = round(Int, result.hit_rates["3/5"] * total_tests)
        
        @assert calculated_5_5 >= expected_5_5  # Should have at least the perfect match
        
        push!(test_results["details"], "✅ Hit rate calculations")
        test_results["passed"] += 1
        
        # Test 6.4: Empty data handling
        println("  Testing empty data handling...")
        
        # Test with empty combinations
        empty_result = run_backtest(engine, Vector{Vector{Int}}(), test_draws)
        @assert all(rate == 0.0 for rate in values(empty_result.hit_rates))
        
        # Test with empty draws
        empty_draws_result = run_backtest(engine, test_combinations, LotteryDraw[])
        @assert all(rate == 0.0 for rate in values(empty_draws_result.hit_rates))
        
        push!(test_results["details"], "✅ Empty data handling")
        test_results["passed"] += 1
        
        # Test 6.5: Random comparison
        println("  Testing random comparison...")
        
        random_comparison = compare_to_random(engine, result)
        
        @assert isa(random_comparison, EfficiencyComparison)
        @assert haskey(random_comparison.efficiency_ratios, "3/5")
        @assert haskey(random_comparison.strategy_odds, "3/5")
        @assert haskey(random_comparison.random_odds, "3/5")
        
        # Efficiency ratios should be positive
        for ratio in values(random_comparison.efficiency_ratios)
            @assert ratio >= 0.0
        end
        
        push!(test_results["details"], "✅ Random comparison")
        test_results["passed"] += 1
        
        println("  ✅ Backtesting engine tests completed")
        
    catch e
        push!(test_results["details"], "❌ Backtesting engine error: $e")
        test_results["failed"] += 1
        println("  ❌ Backtesting engine tests failed: $e")
    end
    
    return test_results
end

"""
Test performance reporting functionality
"""
function test_performance_reporting()
    test_results = Dict{String, Any}("passed" => 0, "failed" => 0, "details" => [])
    
    try
        # Test 7.1: Performance reporter creation
        println("  Testing performance reporter creation...")
        
        wg_engine = WonderGridEngine()
        bt_engine = BacktestingEngine()
        reporter = PerformanceReporter(wg_engine, bt_engine)
        
        @assert isa(reporter, PerformanceReporter)
        @assert reporter.engine === wg_engine
        @assert reporter.backtesting_engine === bt_engine
        
        push!(test_results["details"], "✅ Performance reporter creation")
        test_results["passed"] += 1
        
        # Test 7.2: Performance report generation
        println("  Testing performance report generation...")
        
        # Create test data
        test_draws = [
            LotteryDraw(Date("2023-01-01"), [1, 7, 14, 21, 28]),
            LotteryDraw(Date("2023-01-02"), [3, 9, 15, 22, 29]),
            LotteryDraw(Date("2023-01-03"), [5, 11, 17, 24, 31])
        ]
        
        key_number = 7
        report = generate_performance_report(reporter, key_number, test_draws)
        
        @assert isa(report, PerformanceReport)
        @assert report.key_number == key_number
        @assert report.total_draws == length(test_draws)
        @assert report.total_combinations > 0
        
        # Verify report structure
        @assert isa(report.hit_rates, HitRates)
        @assert isa(report.total_hits, Dict)
        @assert isa(report.efficiency_ratios, Dict)
        @assert isa(report.cost_analysis, CostAnalysis)
        
        push!(test_results["details"], "✅ Performance report generation")
        test_results["passed"] += 1
        
        # Test 7.3: Statistical report generation
        println("  Testing statistical report generation...")
        
        statistical_report = generate_statistical_report(reporter, key_number, test_draws)
        
        @assert isa(statistical_report, Dict)
        @assert haskey(statistical_report, "performance_report")
        @assert haskey(statistical_report, "theoretical_probabilities")
        @assert haskey(statistical_report, "empirical_probabilities")
        @assert haskey(statistical_report, "statistical_summary")
        
        # Verify theoretical probabilities
        theoretical = statistical_report["theoretical_probabilities"]
        @assert haskey(theoretical, "3/5")
        @assert haskey(theoretical, "4/5")
        @assert haskey(theoretical, "5/5")
        @assert all(0.0 <= prob <= 1.0 for prob in values(theoretical) if isa(prob, Float64))
        
        push!(test_results["details"], "✅ Statistical report generation")
        test_results["passed"] += 1
        
        # Test 7.4: Comparative reporting
        println("  Testing comparative reporting...")
        
        key_numbers = [7, 13, 21]
        comparative_reports = generate_comparative_report(reporter, key_numbers, test_draws)
        
        @assert isa(comparative_reports, Vector{PerformanceReport})
        @assert length(comparative_reports) <= length(key_numbers)  # Some might fail
        
        # Verify each report
        for comp_report in comparative_reports
            @assert isa(comp_report, PerformanceReport)
            @assert comp_report.key_number in key_numbers
        end
        
        push!(test_results["details"], "✅ Comparative reporting ($(length(comparative_reports)) reports)")
        test_results["passed"] += 1
        
        println("  ✅ Performance reporting tests completed")
        
    catch e
        push!(test_results["details"], "❌ Performance reporting error: $e")
        test_results["failed"] += 1
        println("  ❌ Performance reporting tests failed: $e")
    end
    
    return test_results
end

"""
Test configuration system functionality
"""
function test_configuration_system()
    test_results = Dict{String, Any}("passed" => 0, "failed" => 0, "details" => [])
    
    try
        # Test 8.1: Configuration creation
        println("  Testing configuration creation...")
        
        config = WonderGridConfig()
        @assert isa(config, WonderGridConfig)
        @assert config.auto_key_selection == true
        @assert config.analysis_depth == "standard"
        @assert config.show_progress == true
        
        push!(test_results["details"], "✅ Configuration creation")
        test_results["passed"] += 1
        
        # Test 8.2: Configuration validation
        println("  Testing configuration validation...")
        
        # Valid configuration
        is_valid, errors = validate_configuration(config)
        @assert is_valid == true
        @assert isempty(errors)
        
        # Invalid configuration
        invalid_config = WonderGridConfig()
        invalid_config.key_number = 50  # Invalid
        invalid_config.lie_threshold = 1.5  # Invalid
        
        is_valid, errors = validate_configuration(invalid_config)
        @assert is_valid == false
        @assert !isempty(errors)
        
        push!(test_results["details"], "✅ Configuration validation")
        test_results["passed"] += 1
        
        # Test 8.3: Configuration presets
        println("  Testing configuration presets...")
        
        presets = ["beginner", "standard", "advanced", "performance"]
        
        for preset in presets
            preset_config = get_configuration_preset(preset)
            @assert isa(preset_config, WonderGridConfig)
            
            # Verify preset-specific properties
            if preset == "beginner"
                @assert preset_config.analysis_depth == "basic"
                @assert preset_config.include_lie_elimination == false
            elseif preset == "advanced"
                @assert preset_config.analysis_depth == "comprehensive"
                @assert preset_config.include_lie_elimination == true
            end
        end
        
        push!(test_results["details"], "✅ Configuration presets")
        test_results["passed"] += 1
        
        # Test 8.4: Configuration manager
        println("  Testing configuration manager...")
        
        test_config_file = "test_unit_config.txt"
        manager = ConfigurationManager(test_config_file)
        
        # Modify and save
        manager.config.key_number = 17
        manager.config.analysis_depth = "comprehensive"
        save_configuration!(manager)
        
        # Load in new manager
        manager2 = ConfigurationManager(test_config_file)
        @assert manager2.config.key_number == 17
        @assert manager2.config.analysis_depth == "comprehensive"
        
        # Cleanup
        if isfile(test_config_file)
            rm(test_config_file)
        end
        
        push!(test_results["details"], "✅ Configuration manager")
        test_results["passed"] += 1
        
        println("  ✅ Configuration system tests completed")
        
    catch e
        push!(test_results["details"], "❌ Configuration system error: $e")
        test_results["failed"] += 1
        println("  ❌ Configuration system tests failed: $e")
    end
    
    return test_results
end

"""
Test result display functionality
"""
function test_result_display()
    test_results = Dict{String, Any}("passed" => 0, "failed" => 0, "details" => [])
    
    try
        # Test 9.1: Display manager creation
        println("  Testing display manager creation...")
        
        manager = ResultDisplayManager()
        @assert isa(manager, ResultDisplayManager)
        @assert manager.show_progress == true
        @assert manager.export_format == "csv"
        
        push!(test_results["details"], "✅ Display manager creation")
        test_results["passed"] += 1
        
        # Test 9.2: Progress indicators
        println("  Testing progress indicators...")
        
        progress = ProgressIndicator(100, "Test Operation")
        @assert progress.total == 100
        @assert progress.current == 0
        @assert progress.description == "Test Operation"
        
        # Test progress updates
        for i in [25, 50, 75, 100]
            update_progress!(progress, i)
            @assert progress.current == i
        end
        
        push!(test_results["details"], "✅ Progress indicators")
        test_results["passed"] += 1
        
        # Test 9.3: Export functionality
        println("  Testing export functionality...")
        
        test_combinations = [
            [1, 5, 12, 23, 39],
            [3, 7, 15, 28, 35]
        ]
        
        test_metadata = Dict("key_number" => 13, "test" => true)
        
        # Test CSV export
        csv_file = "test_unit_export.csv"
        export_combinations_csv(test_combinations, csv_file, test_metadata)
        @assert isfile(csv_file)
        
        # Test TXT export
        txt_file = "test_unit_export.txt"
        export_combinations_txt(test_combinations, txt_file, test_metadata)
        @assert isfile(txt_file)
        
        # Test JSON export
        json_file = "test_unit_export.json"
        export_combinations_json(test_combinations, json_file, test_metadata)
        @assert isfile(json_file)
        
        # Cleanup
        for file in [csv_file, txt_file, json_file]
            if isfile(file)
                rm(file)
            end
        end
        
        push!(test_results["details"], "✅ Export functionality")
        test_results["passed"] += 1
        
        # Test 9.4: Batch export
        println("  Testing batch export...")
        
        exported_files = batch_export_combinations(
            test_combinations,
            "test_unit_batch",
            ["csv", "txt"],
            test_metadata
        )
        
        @assert length(exported_files) == 2
        @assert all(isfile(file) for file in exported_files)
        
        # Cleanup
        for file in exported_files
            if isfile(file)
                rm(file)
            end
        end
        
        push!(test_results["details"], "✅ Batch export")
        test_results["passed"] += 1
        
        println("  ✅ Result display tests completed")
        
    catch e
        push!(test_results["details"], "❌ Result display error: $e")
        test_results["failed"] += 1
        println("  ❌ Result display tests failed: $e")
    end
    
    return test_results
end

"""
Test performance optimization functionality
"""
function test_performance_optimization()
    test_results = Dict{String, Any}("passed" => 0, "failed" => 0, "details" => [])
    
    try
        # Test 10.1: Optimized data structures
        println("  Testing optimized data structures...")
        
        # Test OptimizedCombination
        combo = OptimizedCombination([1, 5, 12, 23, 39])
        @assert isa(combo, OptimizedCombination)
        
        converted = convert(Vector{Int}, combo)
        @assert converted == [1, 5, 12, 23, 39]
        
        push!(test_results["details"], "✅ Optimized data structures")
        test_results["passed"] += 1
        
        # Test 10.2: Caching system
        println("  Testing caching system...")
        
        # Clear caches
        clear_all_caches!()
        
        # Test FFG caching
        key_number = 13
        ffg1 = calculate_ffg_optimized(key_number)
        ffg2 = calculate_ffg_optimized(key_number)  # Should use cache
        
        @assert ffg1 == ffg2
        
        cache_stats = get_cache_stats()
        @assert cache_stats["ffg_cache_size"] >= 1
        
        push!(test_results["details"], "✅ Caching system")
        test_results["passed"] += 1
        
        # Test 10.3: Optimized engine
        println("  Testing optimized engine...")
        
        opt_engine = OptimizedWonderGridEngine()
        @assert isa(opt_engine, OptimizedWonderGridEngine)
        
        combinations = generate_combinations_optimized(opt_engine, 7)
        @assert isa(combinations, Vector{Vector{Int}})
        
        # Verify combinations are valid
        if !isempty(combinations)
            for combo in combinations[1:min(5, length(combinations))]
                @assert length(combo) == 5
                @assert all(n -> 1 <= n <= 39, combo)
            end
        end
        
        push!(test_results["details"], "✅ Optimized engine")
        test_results["passed"] += 1
        
        # Test 10.4: Performance monitoring
        println("  Testing performance monitoring...")
        
        perf_stats = get_performance_stats(opt_engine.performance_monitor)
        @assert isa(perf_stats, Dict)
        @assert haskey(perf_stats, "total_runtime")
        @assert haskey(perf_stats, "operations")
        
        push!(test_results["details"], "✅ Performance monitoring")
        test_results["passed"] += 1
        
        println("  ✅ Performance optimization tests completed")
        
    catch e
        push!(test_results["details"], "❌ Performance optimization error: $e")
        test_results["failed"] += 1
        println("  ❌ Performance optimization tests failed: $e")
    end
    
    return test_results
end

"""
Test concurrent processing functionality
"""
function test_concurrent_processing()
    test_results = Dict{String, Any}("passed" => 0, "failed" => 0, "details" => [])
    
    try
        # Test 11.1: Thread-safe data structures
        println("  Testing thread-safe data structures...")
        
        calc = ConcurrentPairingCalculator()
        @assert isa(calc, ConcurrentPairingCalculator)
        @assert calc.total_pairs[] == 0
        
        # Add combination
        add_combination_concurrent!(calc, [1, 5, 12, 23, 39])
        @assert calc.total_pairs[] == 10  # 5 choose 2
        
        push!(test_results["details"], "✅ Thread-safe data structures")
        test_results["passed"] += 1
        
        # Test 11.2: Concurrent caching
        println("  Testing concurrent caching...")
        
        clear_concurrent_caches!()
        
        key_number = 7
        ffg1 = calculate_ffg_concurrent(key_number)
        ffg2 = calculate_ffg_concurrent(key_number)
        
        @assert ffg1 == ffg2
        
        cache_stats = get_concurrent_cache_stats()
        @assert cache_stats["ffg_cache_size"] >= 1
        
        push!(test_results["details"], "✅ Concurrent caching")
        test_results["passed"] += 1
        
        # Test 11.3: Parallel processing
        println("  Testing parallel processing...")
        
        if nthreads() > 1
            generator = ParallelCombinationGenerator(7, min(2, nthreads()))
            combinations = generate_combinations_parallel(generator)
            
            @assert isa(combinations, Vector{Vector{Int}})
            
            if !isempty(combinations)
                # Verify combinations are valid
                for combo in combinations[1:min(5, length(combinations))]
                    @assert length(combo) == 5
                    @assert all(n -> 1 <= n <= 39, combo)
                end
            end
            
            push!(test_results["details"], "✅ Parallel processing ($(length(combinations)) combinations)")
        else
            push!(test_results["details"], "⚠️  Parallel processing skipped (single thread)")
        end
        
        test_results["passed"] += 1
        
        # Test 11.4: Concurrent engine
        println("  Testing concurrent engine...")
        
        concurrent_engine = ConcurrentWonderGridEngine(7, min(2, nthreads()))
        @assert isa(concurrent_engine, ConcurrentWonderGridEngine)
        
        combinations = generate_combinations_concurrent(concurrent_engine, 7)
        @assert isa(combinations, Vector{Vector{Int}})
        
        push!(test_results["details"], "✅ Concurrent engine")
        test_results["passed"] += 1
        
        println("  ✅ Concurrent processing tests completed")
        
    catch e
        push!(test_results["details"], "❌ Concurrent processing error: $e")
        test_results["failed"] += 1
        println("  ❌ Concurrent processing tests failed: $e")
    end
    
    return test_results
end

"""
Test large dataset performance
"""
function test_large_dataset_performance()
    test_results = Dict{String, Any}("passed" => 0, "failed" => 0, "details" => [])
    
    try
        # Test 12.1: Large combination generation
        println("  Testing large combination generation...")
        
        engine = WonderGridEngine()
        
        # Test with key that should generate many combinations
        key_number = 13
        
        start_time = time()
        combinations = generate_combinations(engine, key_number)
        generation_time = time() - start_time
        
        @assert generation_time < 10.0  # Should complete within 10 seconds
        
        if !isempty(combinations)
            # Test with larger subset
            large_subset = combinations[1:min(1000, length(combinations))]
            
            # Verify all combinations are valid
            for combo in large_subset
                @assert length(combo) == 5
                @assert all(n -> 1 <= n <= 39, combo)
                @assert length(unique(combo)) == 5
            end
            
            push!(test_results["details"], "✅ Large combination generation ($(length(combinations)) combinations in $(round(generation_time, digits=3))s)")
        else
            push!(test_results["details"], "⚠️  No combinations generated for performance test")
        end
        
        test_results["passed"] += 1
        
        # Test 12.2: Large dataset backtesting
        println("  Testing large dataset backtesting...")
        
        if !isempty(combinations)
            # Create larger test dataset
            large_draws = [
                LotteryDraw(Date("2023-01-01") + Day(i), 
                           sort(rand(1:39, 5)) |> unique |> collect |> x -> length(x) == 5 ? x : [1,2,3,4,5])
                for i in 1:50
            ]
            
            # Filter to valid draws
            valid_draws = [draw for draw in large_draws if length(draw.numbers) == 5]
            
            if length(valid_draws) >= 10
                bt_engine = BacktestingEngine()
                test_combinations = combinations[1:min(100, length(combinations))]
                
                start_time = time()
                result = run_backtest(bt_engine, test_combinations, valid_draws[1:10])
                backtest_time = time() - start_time
                
                @assert backtest_time < 30.0  # Should complete within 30 seconds
                @assert isa(result, BacktestResult)
                
                push!(test_results["details"], "✅ Large dataset backtesting ($(length(test_combinations))×$(length(valid_draws[1:10])) tests in $(round(backtest_time, digits=3))s)")
            else
                push!(test_results["details"], "⚠️  Insufficient valid draws for large dataset test")
            end
        else
            push!(test_results["details"], "⚠️  No combinations for large dataset backtesting")
        end
        
        test_results["passed"] += 1
        
        # Test 12.3: Memory usage with large datasets
        println("  Testing memory usage with large datasets...")
        
        # Force garbage collection before measurement
        GC.gc()
        memory_before = Base.gc_bytes()
        
        # Perform memory-intensive operation
        if !isempty(combinations)
            large_subset = combinations[1:min(500, length(combinations))]
            
            # Create multiple copies to test memory usage
            memory_test_data = [copy(combo) for combo in large_subset for _ in 1:10]
            
            GC.gc()
            memory_after = Base.gc_bytes()
            memory_used = memory_after - memory_before
            
            # Memory usage should be reasonable (< 100MB for this test)
            memory_mb = memory_used / (1024 * 1024)
            @assert memory_mb < 100.0
            
            push!(test_results["details"], "✅ Memory usage test ($(round(memory_mb, digits=2)) MB for $(length(memory_test_data)) items)")
        else
            push!(test_results["details"], "⚠️  No combinations for memory usage test")
        end
        
        test_results["passed"] += 1
        
        # Test 12.4: Performance optimization impact
        println("  Testing performance optimization impact...")
        
        # Compare standard vs optimized performance
        standard_engine = WonderGridEngine()
        optimized_engine = OptimizedWonderGridEngine()
        
        # Standard generation
        start_time = time()
        standard_combinations = generate_combinations(standard_engine, 7)
        standard_time = time() - start_time
        
        # Optimized generation
        start_time = time()
        optimized_combinations = generate_combinations_optimized(optimized_engine, 7)
        optimized_time = time() - start_time
        
        if standard_time > 0 && optimized_time > 0
            speedup = standard_time / optimized_time
            push!(test_results["details"], "✅ Performance optimization impact ($(round(speedup, digits=2))x speedup)")
        else
            push!(test_results["details"], "⚠️  Performance comparison inconclusive")
        end
        
        test_results["passed"] += 1
        
        println("  ✅ Large dataset performance tests completed")
        
    catch e
        push!(test_results["details"], "❌ Large dataset performance error: $e")
        test_results["failed"] += 1
        println("  ❌ Large dataset performance tests failed: $e")
    end
    
    return test_results
end

"""
Generate comprehensive test report
"""
function generate_test_report(test_results::Dict{String, Dict{String, Any}})
    println("Generating comprehensive test report...")
    
    # Calculate overall statistics
    total_passed = sum(results["passed"] for results in values(test_results))
    total_failed = sum(results["failed"] for results in values(test_results))
    total_tests = total_passed + total_failed
    
    success_rate = total_tests > 0 ? (total_passed / total_tests) * 100 : 0.0
    
    # Create report
    report_filename = "comprehensive_unit_test_report.txt"
    
    open(report_filename, "w") do file
        println(file, "WONDER GRID COMPREHENSIVE UNIT TEST REPORT")
        println(file, "=" ^ 60)
        println(file, "Generated: $(Dates.now())")
        println(file, "Julia Version: $(VERSION)")
        println(file, "Available Threads: $(nthreads())")
        println(file, "")
        
        println(file, "OVERALL SUMMARY")
        println(file, "-" ^ 30)
        println(file, "Total Test Suites: $(length(test_results))")
        println(file, "Total Tests Passed: $total_passed")
        println(file, "Total Tests Failed: $total_failed")
        println(file, "Overall Success Rate: $(round(success_rate, digits=1))%")
        println(file, "")
        
        println(file, "TEST SUITE BREAKDOWN")
        println(file, "-" ^ 30)
        
        for (suite_name, results) in sort(collect(test_results))
            suite_total = results["passed"] + results["failed"]
            suite_rate = suite_total > 0 ? (results["passed"] / suite_total) * 100 : 0.0
            
            println(file, "")
            println(file, "$(uppercase(replace(suite_name, "_" => " "))):")
            println(file, "  Passed: $(results["passed"])")
            println(file, "  Failed: $(results["failed"])")
            println(file, "  Success Rate: $(round(suite_rate, digits=1))%")
            
            if !isempty(results["details"])
                println(file, "  Details:")
                for detail in results["details"]
                    println(file, "    $detail")
                end
            end
        end
        
        println(file, "")
        println(file, "RECOMMENDATIONS")
        println(file, "-" ^ 30)
        
        if success_rate >= 95.0
            println(file, "✅ Excellent: All core components are functioning correctly")
        elseif success_rate >= 85.0
            println(file, "✅ Good: Most components are working well, minor issues detected")
        elseif success_rate >= 70.0
            println(file, "⚠️  Fair: Some components need attention")
        else
            println(file, "❌ Poor: Significant issues detected, review required")
        end
        
        if total_failed > 0
            println(file, "")
            println(file, "FAILED TESTS REQUIRE ATTENTION:")
            for (suite_name, results) in test_results
                if results["failed"] > 0
                    println(file, "  - $suite_name: $(results["failed"]) failed tests")
                end
            end
        end
        
        println(file, "")
        println(file, "=" ^ 60)
        println(file, "End of Report")
    end
    
    println("✅ Test report saved to: $report_filename")
    
    # Display summary
    println("\n📊 TEST SUMMARY")
    println("=" ^ 40)
    println("Total Tests: $total_tests")
    println("Passed: $total_passed")
    println("Failed: $total_failed")
    println("Success Rate: $(round(success_rate, digits=1))%")
    
    if success_rate >= 95.0
        println("🎉 Excellent test results!")
    elseif success_rate >= 85.0
        println("✅ Good test results!")
    elseif success_rate >= 70.0
        println("⚠️  Fair test results - some issues detected")
    else
        println("❌ Poor test results - significant issues detected")
    end
end

# Run the comprehensive unit tests
if abspath(PROGRAM_FILE) == @__FILE__
    run_comprehensive_unit_tests()
end