# LIE (Loss Into Elimination) strategy implementation

"""
LIEEliminationEngine for reducing combinations through elimination
"""
mutable struct LIEEliminationEngine
    pairing_engine::PairingEngine
    elimination_threshold::Float64
    
    LIEEliminationEngine(pairing_engine::PairingEngine) = new(pairing_engine, 0.1)  # Bottom 10%
end

"""
Generate combinations expected not to win in short term
"""
function generate_lie_combinations(engine::LIEEliminationEngine, key_number::Int)::Vector{Vector{Int}}
    # Get all pairings for analysis
    all_pairings = calculate_all_pairings(engine.pairing_engine)
    
    # Find pairings involving the key number
    key_pairings = Tuple{Int, Int}[]
    for (pair, _) in all_pairings
        if pair[1] == key_number || pair[2] == key_number
            push!(key_pairings, pair)
        end
    end
    
    # Sort by frequency (ascending - least frequent first)
    sort!(key_pairings, by = pair -> all_pairings[pair])
    
    # Get bottom threshold percentage
    num_bottom = max(1, round(Int, length(key_pairings) * engine.elimination_threshold))
    bottom_pairings = key_pairings[1:min(num_bottom, length(key_pairings))]
    
    # Extract numbers from bottom pairings
    bottom_numbers = Int[]
    for pair in bottom_pairings
        other_number = pair[1] == key_number ? pair[2] : pair[1]
        if !(other_number in bottom_numbers)
            push!(bottom_numbers, other_number)
        end
    end
    
    # Generate combinations from least frequent pairings
    lie_combinations = Vector{Vector{Int}}()
    
    if length(bottom_numbers) >= 4
        # Generate more comprehensive LIE combinations
        max_numbers = min(12, length(bottom_numbers))  # Increased from 8 to 12
        working_numbers = bottom_numbers[1:max_numbers]
        
        # Generate all possible 4-number combinations from bottom numbers
        for combo in Combinatorics.combinations(working_numbers, 4)
            full_combo = sort([key_number; combo])
            push!(lie_combinations, full_combo)
        end
        
        # Also generate some mixed combinations (bottom + some middle-tier numbers)
        if length(bottom_numbers) >= 2
            # Get some middle-tier numbers (not top, not bottom)
            all_other_numbers = [i for i in 1:39 if i != key_number]
            middle_start = max(1, round(Int, length(key_pairings) * 0.3))
            middle_end = min(length(key_pairings), round(Int, length(key_pairings) * 0.7))
            
            if middle_end > middle_start
                middle_pairings = key_pairings[middle_start:middle_end]
                middle_numbers = Int[]
                for pair in middle_pairings
                    other_number = pair[1] == key_number ? pair[2] : pair[1]
                    if !(other_number in middle_numbers) && length(middle_numbers) < 6
                        push!(middle_numbers, other_number)
                    end
                end
                
                # Generate mixed combinations: 2 from bottom + 2 from middle
                if length(middle_numbers) >= 2 && length(bottom_numbers) >= 2
                    for bottom_combo in Combinatorics.combinations(bottom_numbers[1:min(4, length(bottom_numbers))], 2)
                        for middle_combo in Combinatorics.combinations(middle_numbers[1:min(4, length(middle_numbers))], 2)
                            mixed_combo = sort([key_number; bottom_combo; middle_combo])
                            push!(lie_combinations, mixed_combo)
                        end
                    end
                end
            end
        end
    end
    
    return unique(lie_combinations)  # Remove duplicates
end

"""
Apply elimination filter to remove LIE combinations
"""
function apply_elimination_filter(engine::LIEEliminationEngine, combinations::Vector{Vector{Int}})::Vector{Vector{Int}}
    if isempty(combinations)
        return combinations
    end
    
    # Find the key number (appears in all combinations)
    key_number = find_key_number(combinations)
    
    if key_number == 0
        @warn "Could not identify key number, returning original combinations"
        return combinations
    end
    
    # Generate LIE combinations to eliminate
    lie_combinations = generate_lie_combinations(engine, key_number)
    lie_set = Set(lie_combinations)
    
    # Filter out LIE combinations
    filtered_combinations = Vector{Vector{Int}}()
    for combo in combinations
        if !(combo in lie_set)
            push!(filtered_combinations, combo)
        end
    end
    
    return filtered_combinations
end

"""
Find the key number that appears in all combinations
"""
function find_key_number(combinations::Vector{Vector{Int}})::Int
    if isempty(combinations)
        return 0
    end
    
    # Count occurrences of each number
    number_counts = Dict{Int, Int}()
    total_combinations = length(combinations)
    
    for combo in combinations
        for number in combo
            number_counts[number] = get(number_counts, number, 0) + 1
        end
    end
    
    # Find number that appears in all combinations
    for (number, count) in number_counts
        if count == total_combinations
            return number
        end
    end
    
    return 0  # No key number found
end

"""
Calculate cost savings from elimination
"""
function calculate_cost_savings(engine::LIEEliminationEngine, original_count::Int, filtered_count::Int)::Float64
    if original_count == 0
        return 0.0
    end
    
    eliminated_count = original_count - filtered_count
    savings_ratio = eliminated_count / original_count
    return savings_ratio * 100.0  # Return as percentage
end