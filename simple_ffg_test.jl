# Simple FFG Test

using Dates

# Include necessary types and functions
include("src/types.jl")
include("src/ffg_calculator.jl")

println("=== Simple FFG Test ===")

# Test basic FFG calculation
calc = FFGCalculator()
result = calculate_theoretical_ffg_median(calc)
println("FFG median for DC=50%: $result")

# Test different DC values
for dc in [0.25, 0.5, 0.75, 0.9]
    calc_dc = FFGCalculator(dc)
    result_dc = calculate_theoretical_ffg_median(calc_dc)
    println("FFG median for DC=$(Int(dc*100))%: $result_dc")
end

println("Test completed successfully!")
