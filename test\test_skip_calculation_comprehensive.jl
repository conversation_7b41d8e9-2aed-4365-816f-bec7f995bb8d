# Comprehensive Skip Calculation Tests
# Skip 計算綜合測試 - 全面測試 Skip 計算的準確性和性能

using Test
using Dates
using Statistics

# 引入必要的模組
include("../src/types.jl")
include("../src/skip_analyzer.jl")
include("../src/filter_engine.jl")
include("test_data_manager.jl")
include("test_configuration.jl")

"""
Skip 計算準確性測試
測試 Skip 計算的數學準確性
"""
function test_skip_calculation_accuracy(test_data::Vector{LotteryDraw})::Dict{String, Any}
    @testset "Skip Calculation Accuracy" begin
        engine = FilterEngine(test_data)
        skip_analyzer = SkipAnalyzer(test_data)
        
        results = Dict{String, Any}()
        accuracy_scores = Float64[]
        
        # 測試所有號碼的 Skip 計算
        for number in 1:39
            try
                # 使用過濾器引擎計算
                filter_result = calculate_one_filter(engine, number)
                current_skip_filter = filter_result.current_value
                
                # 使用 Skip 分析器計算
                current_skip_analyzer = get_current_skip(skip_analyzer, number)
                
                # 驗證一致性
                is_consistent = current_skip_filter == current_skip_analyzer
                
                if is_consistent
                    push!(accuracy_scores, 1.0)
                else
                    push!(accuracy_scores, 0.0)
                    @warn "號碼 $number Skip 計算不一致: 過濾器=$current_skip_filter, 分析器=$current_skip_analyzer"
                end
                
                # 驗證 Skip 序列
                skip_sequence = calculate_skips(skip_analyzer, number)
                
                # 檢查序列的合理性
                if !isempty(skip_sequence)
                    # Skip 值應該都是非負數
                    @test all(skip >= 0 for skip in skip_sequence)
                    
                    # 當前 Skip 應該等於序列的第一個值（如果存在）
                    if !isempty(skip_sequence) && current_skip_analyzer > 0
                        # 注意：當前 Skip 可能不在序列中，因為序列是歷史 Skip
                        @test current_skip_analyzer >= 0
                    end
                end
                
            catch e
                @warn "測試號碼 $number 時發生錯誤: $e"
                push!(accuracy_scores, 0.0)
            end
        end
        
        # 計算整體準確性
        overall_accuracy = mean(accuracy_scores)
        results["overall_accuracy"] = overall_accuracy
        results["tested_numbers"] = 39
        results["consistent_calculations"] = count(score -> score == 1.0, accuracy_scores)
        
        @test overall_accuracy >= 0.95  # 要求 95% 以上的準確性
        
        println("  ✅ Skip 計算準確性: $(round(overall_accuracy * 100, digits=1))%")
        
        return results
    end
end

"""
Skip 方法比較測試
比較不同 Skip 計算方法的結果
"""
function test_skip_method_comparison(test_data::Vector{LotteryDraw})::Dict{String, Any}
    @testset "Skip Method Comparison" begin
        results = Dict{String, Any}()
        
        # 方法 1：直接遍歷計算
        function calculate_skip_direct(data::Vector{LotteryDraw}, number::Int)::Int
            for (i, draw) in enumerate(data)
                if number in draw.numbers
                    return i - 1
                end
            end
            return length(data)
        end
        
        # 方法 2：使用 Skip 分析器
        skip_analyzer = SkipAnalyzer(test_data)
        
        # 方法 3：使用過濾器引擎
        engine = FilterEngine(test_data)
        
        comparison_results = []
        
        # 比較前 10 個號碼的結果
        for number in 1:10
            direct_skip = calculate_skip_direct(test_data, number)
            analyzer_skip = get_current_skip(skip_analyzer, number)
            
            try
                filter_result = calculate_one_filter(engine, number)
                filter_skip = filter_result.current_value
                
                # 記錄比較結果
                comparison = Dict(
                    "number" => number,
                    "direct_method" => direct_skip,
                    "analyzer_method" => analyzer_skip,
                    "filter_method" => filter_skip,
                    "all_consistent" => (direct_skip == analyzer_skip == filter_skip)
                )
                
                push!(comparison_results, comparison)
                
                # 驗證一致性
                @test direct_skip == analyzer_skip == filter_skip
                
            catch e
                @warn "比較號碼 $number 時發生錯誤: $e"
            end
        end
        
        # 計算一致性統計
        consistent_count = count(comp -> comp["all_consistent"], comparison_results)
        consistency_rate = consistent_count / length(comparison_results)
        
        results["comparison_results"] = comparison_results
        results["consistency_rate"] = consistency_rate
        results["tested_numbers"] = length(comparison_results)
        
        @test consistency_rate >= 0.95  # 要求 95% 以上的一致性
        
        println("  ✅ Skip 方法一致性: $(round(consistency_rate * 100, digits=1))%")
        
        return results
    end
end

"""
邊界條件測試
測試各種邊界條件下的 Skip 計算
"""
function test_skip_boundary_conditions()::Dict{String, Any}
    @testset "Skip Boundary Conditions" begin
        results = Dict{String, Any}()
        boundary_tests = []
        
        # 測試 1：空數據
        empty_data = LotteryDraw[]
        empty_analyzer = SkipAnalyzer(empty_data)
        
        for number in [1, 20, 39]
            skip = get_current_skip(empty_analyzer, number)
            @test skip == 0  # 空數據應該返回 0
            
            sequence = calculate_skips(empty_analyzer, number)
            @test isempty(sequence)  # 空數據應該返回空序列
        end
        
        push!(boundary_tests, Dict("test" => "empty_data", "status" => "passed"))
        
        # 測試 2：單筆數據
        single_data = [LotteryDraw([1, 2, 3, 4, 5], Date(2022, 1, 1), 1)]
        single_analyzer = SkipAnalyzer(single_data)
        
        # 包含的號碼
        for number in [1, 2, 3, 4, 5]
            skip = get_current_skip(single_analyzer, number)
            @test skip == 0  # 最新開獎包含該號碼，Skip 應該是 0
        end
        
        # 不包含的號碼
        for number in [6, 7, 8, 9, 10]
            skip = get_current_skip(single_analyzer, number)
            @test skip == 1  # 不包含該號碼，Skip 應該是數據長度
        end
        
        push!(boundary_tests, Dict("test" => "single_data", "status" => "passed"))
        
        # 測試 3：重複組合
        duplicate_data = [
            LotteryDraw([1, 2, 3, 4, 5], Date(2022, 1, 3), 3),
            LotteryDraw([6, 7, 8, 9, 10], Date(2022, 1, 2), 2),
            LotteryDraw([1, 2, 3, 4, 5], Date(2022, 1, 1), 1)
        ]
        duplicate_analyzer = SkipAnalyzer(duplicate_data)
        
        # 重複號碼的 Skip 應該是 0（最新開獎包含）
        for number in [1, 2, 3, 4, 5]
            skip = get_current_skip(duplicate_analyzer, number)
            @test skip == 0
        end
        
        push!(boundary_tests, Dict("test" => "duplicate_data", "status" => "passed"))
        
        # 測試 4：極值號碼
        extreme_data = [
            LotteryDraw([1, 2, 3, 4, 39], Date(2022, 1, 2), 2),
            LotteryDraw([35, 36, 37, 38, 39], Date(2022, 1, 1), 1)
        ]
        extreme_analyzer = SkipAnalyzer(extreme_data)
        
        # 測試最小和最大號碼
        skip_1 = get_current_skip(extreme_analyzer, 1)
        skip_39 = get_current_skip(extreme_analyzer, 39)
        
        @test skip_1 == 0  # 號碼 1 在最新開獎中
        @test skip_39 == 0  # 號碼 39 在最新開獎中
        
        push!(boundary_tests, Dict("test" => "extreme_numbers", "status" => "passed"))
        
        results["boundary_tests"] = boundary_tests
        results["total_boundary_tests"] = length(boundary_tests)
        results["passed_boundary_tests"] = count(test -> test["status"] == "passed", boundary_tests)
        
        println("  ✅ 邊界條件測試: $(results["passed_boundary_tests"])/$(results["total_boundary_tests"]) 通過")
        
        return results
    end
end

"""
性能基準測試
測試 Skip 計算的性能表現
"""
function test_skip_calculation_performance(test_data::Vector{LotteryDraw}, iterations::Int = 100)::Dict{String, Any}
    @testset "Skip Calculation Performance" begin
        results = Dict{String, Any}()
        
        # 預熱
        skip_analyzer = SkipAnalyzer(test_data)
        for _ in 1:10
            get_current_skip(skip_analyzer, 1)
        end
        
        # 測試單個 Skip 計算性能
        single_skip_times = Float64[]
        for _ in 1:iterations
            start_time = time()
            get_current_skip(skip_analyzer, rand(1:39))
            execution_time = (time() - start_time) * 1000  # 轉換為毫秒
            push!(single_skip_times, execution_time)
        end
        
        # 測試批量 Skip 計算性能
        batch_skip_times = Float64[]
        for _ in 1:10  # 較少迭代，因為批量計算較慢
            start_time = time()
            for number in 1:39
                get_current_skip(skip_analyzer, number)
            end
            execution_time = (time() - start_time) * 1000
            push!(batch_skip_times, execution_time)
        end
        
        # 測試 Skip 序列計算性能
        sequence_times = Float64[]
        for _ in 1:50
            start_time = time()
            calculate_skips(skip_analyzer, rand(1:39))
            execution_time = (time() - start_time) * 1000
            push!(sequence_times, execution_time)
        end
        
        # 計算統計
        results["single_skip"] = Dict(
            "mean_ms" => mean(single_skip_times),
            "median_ms" => median(single_skip_times),
            "max_ms" => maximum(single_skip_times),
            "std_ms" => std(single_skip_times)
        )
        
        results["batch_skip"] = Dict(
            "mean_ms" => mean(batch_skip_times),
            "median_ms" => median(batch_skip_times),
            "max_ms" => maximum(batch_skip_times)
        )
        
        results["sequence_calculation"] = Dict(
            "mean_ms" => mean(sequence_times),
            "median_ms" => median(sequence_times),
            "max_ms" => maximum(sequence_times)
        )
        
        # 性能要求檢查
        @test mean(single_skip_times) < 1.0  # 單個 Skip 計算應該小於 1ms
        @test mean(batch_skip_times) < 50.0  # 批量計算應該小於 50ms
        @test mean(sequence_times) < 10.0    # 序列計算應該小於 10ms
        
        println("  ✅ Skip 計算性能:")
        println("    - 單個計算: $(round(mean(single_skip_times), digits=3))ms")
        println("    - 批量計算: $(round(mean(batch_skip_times), digits=1))ms")
        println("    - 序列計算: $(round(mean(sequence_times), digits=1))ms")
        
        return results
    end
end

"""
執行完整的 Skip 計算綜合測試
"""
function run_comprehensive_skip_tests(data_manager::TestDataManager)::Dict{String, Any}
    println("🧪 開始執行 Skip 計算綜合測試...")
    
    comprehensive_results = Dict{String, Any}()
    
    # 使用中等大小的測試數據
    test_data = get_test_data(data_manager, "medium")
    
    try
        # 執行各項測試
        comprehensive_results["accuracy"] = test_skip_calculation_accuracy(test_data)
        comprehensive_results["method_comparison"] = test_skip_method_comparison(test_data)
        comprehensive_results["boundary_conditions"] = test_skip_boundary_conditions()
        comprehensive_results["performance"] = test_skip_calculation_performance(test_data)
        
        # 計算整體評分
        accuracy_score = comprehensive_results["accuracy"]["overall_accuracy"]
        consistency_score = comprehensive_results["method_comparison"]["consistency_rate"]
        boundary_score = comprehensive_results["boundary_conditions"]["passed_boundary_tests"] / 
                        comprehensive_results["boundary_conditions"]["total_boundary_tests"]
        
        overall_score = (accuracy_score + consistency_score + boundary_score) / 3
        comprehensive_results["overall_score"] = overall_score
        
        println("\n📊 Skip 計算綜合測試結果:")
        println("  - 準確性評分: $(round(accuracy_score * 100, digits=1))%")
        println("  - 一致性評分: $(round(consistency_score * 100, digits=1))%")
        println("  - 邊界測試評分: $(round(boundary_score * 100, digits=1))%")
        println("  - 整體評分: $(round(overall_score * 100, digits=1))%")
        
        if overall_score >= 0.95
            println("🎉 Skip 計算測試：優秀")
        elseif overall_score >= 0.90
            println("✅ Skip 計算測試：良好")
        elseif overall_score >= 0.80
            println("⚠️ Skip 計算測試：需要改進")
        else
            println("❌ Skip 計算測試：需要重大修復")
        end
        
        return comprehensive_results
        
    catch e
        @error "Skip 計算綜合測試失敗: $e"
        comprehensive_results["error"] = string(e)
        return comprehensive_results
    end
end

# 導出主要函數
export test_skip_calculation_accuracy, test_skip_method_comparison
export test_skip_boundary_conditions, test_skip_calculation_performance
export run_comprehensive_skip_tests
