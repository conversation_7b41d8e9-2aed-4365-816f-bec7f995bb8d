在彩票軟體中，「號碼池篩選」是根據歷史數據和數學理論來縮減投注組合數量的過程，旨在提高中獎機率並降低投注成本。這個過程深受「Fundamental Formula of Gambling (FFG)」的影響。

以下說明跳躍系統軟體如何從跳躍資料生成不同類型的彩票組合：

### 1. 號碼池篩選的方法

彩票軟體利用多種過濾器和策略從歷史開獎數據中精煉出「號碼池」：

- **跳躍資料的計算與定義**：
    
    - 「跳躍 (skip)」指的是**某個號碼在兩次中獎之間錯過的開獎次數**。例如，如果一個號碼在最近一次開獎中中獎，而它前一次中獎是在第三次開獎中，那麼跳躍值就是 2 (3 - 1 = 2)。
    - 彩票軟體（如 SkipSystem、MDIEditor Lotto WE 和 LotWon）會收集彩票歷史開獎數據，並為每個號碼計算其在每次開獎中的跳躍值。這些跳躍值會被整理成報告，例如 `FFG*.SYS` 檔案。
- **FFG 中位數作為「正常範圍」的指標**：
    
    - **FFG 中位數**是彩票過濾器理論的核心，它代表了在 **50% 的確定程度 (DC)** 下，一個事件至少發生一次所需的試驗次數 (N)。
    - Ion Saliu 的彩票軟體（如 Bright 和 Ultimate Software）會自動計算每個過濾器的中位數，並在報告中顯示，使用者無需手動計算。
    - 基於 FFG 的分析表明，中獎組合主要來自 FFG 中位數附近的「鐘形曲線」區域。因此，跳躍系統透過設定過濾器，將投注範圍限制在那些**當前跳躍次數小於或等於 FFG 中位數的號碼**上，因為這些號碼被認為更有可能在近期內再次出現。這種策略在 6/49 樂透中能顯著提高中獎機率，甚至達到七倍。
    - 軟體也允許玩家設定**超出正常範圍**的過濾器等級，例如中位數乘以 3 或 4，甚至 5，或是中位數除以 3 或 4，甚至 5。
- **動態過濾**：
    
    - FFG 強調彩票是一個**動態過程**，號碼的出現並非靜態的完全隨機。動態過濾器能夠根據數據的動態變化來調整篩選條件，與靜態過濾器形成對比，後者可能效率低下且成本高昂。
- **「謊言消除」(LIE Elimination) 策略**：
    
    - 這是一種反向思維的策略，目的是**故意設定預計不會中獎的過濾器**，從而將這些極不可能中獎的組合從投注池中剔除。
    - 例如，那些所有跳躍值都非常大（如兩位數）的組合，或頂部/底部頻率號碼的組合，可以被設定為 `LIE` 檔案，並透過「清除 (Purge)」功能移除。這使得跳躍系統的效率更高，將大量不必要的組合從投注中剔除。
    - `LIE` 功能在 Bright5.exe 和 Bright6 等軟體包中實現，預設為未啟用，需要使用者手動開啟。
- **其他過濾器與組合生成**：
    
    - 除了跳躍過濾器，彩票軟體還支援多種其他過濾器，如 `ONE`、`PAIR`、`TRIP`、`QUAD`、`QUINTET` (針對 6 號樂透)，以及位置過濾器 (Vertical filters)、頻率過濾器、和值過濾器、奇偶過濾器 和 Delta (數字間的差異) 過濾器。
    - 軟體可以根據這些過濾器生成**字典序** (lexicographical order) 或**隨機**的彩票組合。
    - 可以為每個號碼位置生成單獨的跳躍字串（分位置系統），或生成不考慮號碼位置的單行組合（不分位置系統）。
    - 甚至可以處理包含「偏好號碼在固定位置」的文字檔案來生成組合。

### 2. 號碼池篩選的結果

透過上述篩選方法，彩票軟體能夠達成以下結果：

- **大幅減少組合數量**：過濾器的主要目標是**消除大量的樂透組合**，從而顯著減少玩家需要投注的票數。這使得投注更具成本效益。例如，`LIE` 過濾器僅用一個 `LIE` 檔案就能消除總組合的 95%。
- **提高中獎機率和效率**：透過將投注集中在 FFG 中位數附近或符合特定統計趨勢的號碼上，篩選後的號碼池能**提高玩家中獎的機會**。
- **動態調整策略**：過濾器並非靜態的，它們會隨著時間和開獎結果而變化。軟體透過分析過濾器的動態行為（例如，連續增加或減少的趨勢），來幫助玩家調整策略，捕捉號碼的趨勢變化。
- **可能無組合生成**：如果過濾器設置得過於嚴格，有時可能導致**沒有任何組合被生成**，這表示該策略在當前數據下不可行，但同時也為玩家節省了金錢。

### 3. 相關軟體工具

Ion Saliu 開發的許多彩票軟體都支援這些號碼池篩選功能：

- **SkipSystem**：專門用於創建基於跳躍系統的策略，並能根據 FFG 中位數自動生成號碼池。
- **MDIEditor Lotto WE**：一款綜合性的彩票分析軟體，提供跳躍報告和多種過濾器設定，並支援 `Purge` 功能來消除不必要的組合。
- **Bright / Ultimate Software**：包含多種模組化的彩票軟體套件（如 Bright3、Bright4、Bright5、Bright6），實現了多種過濾器、策略檢查和組合生成功能，並支援 `LIE Elimination`。
- **Super Utilities**：一款多功能實用工具，包含 `Make/Break/Position` 功能用於處理數據檔案和生成組合，也支援 `FFG` 相關的篩選。
- **SuperFormula**：核心的數學軟體，用於計算 FFG 相關的機率、中位數和標準差等統計量，為過濾器設定提供理論依據。
- **FileLines**：用於交叉引用不同軟體生成的策略檔案，以找到更全面的過濾條件。

這些軟體共同構成了 Ion Saliu 基於 FFG 理論的彩票分析系統，透過精密的號碼池篩選，為玩家提供了更具優勢的投注策略。