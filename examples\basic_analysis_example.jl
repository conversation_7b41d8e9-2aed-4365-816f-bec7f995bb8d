# Wonder Grid Lottery System - 基本分析範例
# 這個範例展示如何使用系統進行基本的彩票數據分析

using Dates

# 引入 Wonder Grid 系統
include("../src/types.jl")
include("../src/filter_engine.jl")
include("../src/optimized_filter_engine.jl")
include("../src/filters/one_filter.jl")
include("../src/filters/two_filter.jl")
include("../src/filters/three_filter.jl")
include("../src/filters/four_filter.jl")
include("../src/filters/five_filter.jl")

println("🎯 Wonder Grid Lottery System - 基本分析範例")
println("=" ^ 60)

# ==========================================
# 1. 準備示例數據
# ==========================================

println("\n📊 步驟 1: 準備示例數據")

# 創建一些示例開獎數據（模擬真實的彩票開獎）
sample_draws = [
    LotteryDraw([1, 8, 15, 22, 29], Date(2023, 1, 1), 1),
    LotteryDraw([3, 12, 18, 25, 31], Date(2023, 1, 2), 2),
    LotteryDraw([5, 9, 16, 23, 35], Date(2023, 1, 3), 3),
    LotteryDraw([2, 11, 19, 27, 33], Date(2023, 1, 4), 4),
    LotteryDraw([7, 14, 21, 28, 36], Date(2023, 1, 5), 5),
    LotteryDraw([4, 10, 17, 24, 30], Date(2023, 1, 6), 6),
    LotteryDraw([6, 13, 20, 26, 32], Date(2023, 1, 7), 7),
    LotteryDraw([1, 9, 18, 25, 34], Date(2023, 1, 8), 8),
    LotteryDraw([3, 11, 16, 29, 37], Date(2023, 1, 9), 9),
    LotteryDraw([5, 12, 19, 22, 38], Date(2023, 1, 10), 10),
    LotteryDraw([2, 8, 17, 26, 35], Date(2023, 1, 11), 11),
    LotteryDraw([4, 13, 21, 28, 39], Date(2023, 1, 12), 12),
    LotteryDraw([6, 10, 15, 24, 31], Date(2023, 1, 13), 13),
    LotteryDraw([7, 14, 20, 27, 33], Date(2023, 1, 14), 14),
    LotteryDraw([1, 12, 18, 23, 36], Date(2023, 1, 15), 15)
]

println("✅ 創建了 $(length(sample_draws)) 筆示例開獎數據")

# 顯示前 5 筆數據
println("\n📋 前 5 筆開獎數據:")
for (i, draw) in enumerate(sample_draws[1:5])
    println("  第 $i 期: $(join(draw.numbers, ", ")) ($(draw.draw_date))")
end

# ==========================================
# 2. 創建分析引擎
# ==========================================

println("\n🔧 步驟 2: 創建分析引擎")

# 創建標準引擎
standard_engine = FilterEngine(sample_draws)
println("✅ 標準引擎創建完成")

# 創建優化引擎（推薦使用）
optimized_engine = OptimizedFilterEngine(
    sample_draws,
    use_compact_data = true,    # 啟用緊湊數據結構
    enable_caching = true,      # 啟用快取
    auto_cleanup = true         # 自動清理
)
println("✅ 優化引擎創建完成")

# 顯示引擎信息
engine_info = get_optimized_engine_info(optimized_engine)
println("\n📊 優化引擎信息:")
println("  - 數據點數: $(engine_info["data_points"])")
println("  - 緊湊數據: $(engine_info["compact_data_available"] ? "啟用" : "禁用")")
println("  - 快取系統: $(engine_info["enable_caching"] ? "啟用" : "禁用")")

# ==========================================
# 3. Skip 分析（ONE 過濾器）
# ==========================================

println("\n🎯 步驟 3: Skip 分析（ONE 過濾器）")

# 分析幾個熱門號碼的 Skip 值
hot_numbers = [1, 5, 10, 15, 20, 25]

println("\n📈 Skip 分析結果:")
skip_results = Dict{Int, Int}()

for number in hot_numbers
    # 使用優化引擎計算 Skip
    skip_value = calculate_skip_optimized(optimized_engine, number)
    skip_results[number] = skip_value
    
    # 也可以使用標準引擎進行比較
    standard_result = calculate_one_filter(standard_engine, number)
    
    println("  號碼 $number: Skip = $skip_value (標準引擎: $(standard_result.current_value))")
end

# 找出 Skip 值最小的號碼（最有可能出現）
sorted_by_skip = sort(collect(skip_results), by=x->x[2])
println("\n🔥 按 Skip 值排序（越小越可能出現）:")
for (i, (number, skip)) in enumerate(sorted_by_skip)
    println("  $i. 號碼 $number: Skip = $skip")
end

# ==========================================
# 4. 配對分析（TWO 過濾器）
# ==========================================

println("\n🤝 步驟 4: 配對分析（TWO 過濾器）")

# 分析號碼配對情況
test_combinations = [
    [1, 5, 10],
    [15, 20, 25],
    [2, 8, 16],
    [7, 14, 21]
]

println("\n📊 配對分析結果:")
for combination in test_combinations
    # 使用 TWO 過濾器分析配對
    pairing_result = calculate_two_filter(standard_engine, combination)
    
    println("  組合 $(join(combination, "-")): 配對數 = $(pairing_result.current_value)")
    
    # 分析具體的配對頻率
    println("    具體配對:")
    for i in 1:length(combination)
        for j in i+1:length(combination)
            freq = calculate_pairing_frequency_optimized(optimized_engine, combination[i], combination[j])
            println("      $(combination[i])-$(combination[j]): $freq 次")
        end
    end
end

# ==========================================
# 5. 高階過濾器分析
# ==========================================

println("\n🔬 步驟 5: 高階過濾器分析")

# 測試組合
test_combo = [1, 8, 15, 22, 29]
println("\n🧪 分析組合: $(join(test_combo, ", "))")

# THREE 過濾器
three_result = calculate_three_filter(standard_engine, test_combo)
println("  THREE 過濾器結果: $(three_result.current_value)")

# FOUR 過濾器
four_result = calculate_four_filter(standard_engine, test_combo)
println("  FOUR 過濾器結果: $(four_result.current_value)")

# FIVE 過濾器
five_result = calculate_five_filter(standard_engine, test_combo)
println("  FIVE 過濾器結果: $(five_result.current_value)")

# ==========================================
# 6. 性能分析
# ==========================================

println("\n⚡ 步驟 6: 性能分析")

# 比較標準引擎和優化引擎的性能
println("\n📊 性能比較測試:")

# 測試 Skip 計算性能
test_numbers = [1, 5, 10, 15, 20]

# 標準引擎性能測試
start_time = time()
for number in test_numbers
    calculate_one_filter(standard_engine, number)
end
standard_time = (time() - start_time) * 1000

# 優化引擎性能測試
start_time = time()
for number in test_numbers
    calculate_skip_optimized(optimized_engine, number)
end
optimized_time = (time() - start_time) * 1000

println("  標準引擎耗時: $(round(standard_time, digits=2))ms")
println("  優化引擎耗時: $(round(optimized_time, digits=2))ms")

if optimized_time > 0
    speedup = standard_time / optimized_time
    println("  性能提升: $(round(speedup, digits=2))x")
else
    println("  性能提升: 無法計算（時間太短）")
end

# 獲取系統性能摘要
try
    performance_summary = get_global_performance_summary()
    println("\n📈 系統性能摘要:")
    println("  - 性能等級: $(performance_summary["performance_grade"])")
    if haskey(performance_summary, "cache_hit_rate")
        println("  - 快取命中率: $(round(performance_summary["cache_hit_rate"] * 100, digits=1))%")
    end
catch e
    println("  ⚠️ 無法獲取性能摘要: $e")
end

# ==========================================
# 7. 記憶體使用分析
# ==========================================

println("\n💾 步驟 7: 記憶體使用分析")

# 獲取引擎統計信息
try
    engine_stats = get_engine_statistics(optimized_engine)
    
    println("\n📊 記憶體使用統計:")
    if haskey(engine_stats, "compact_data")
        compact_stats = engine_stats["compact_data"]
        println("  - 記憶體使用: $(round(compact_stats["memory_usage_bytes"] / 1024, digits=1)) KB")
        println("  - 記憶體節省: $(round(compact_stats["memory_saved_bytes"] / 1024, digits=1)) KB")
        println("  - 節省比例: $(round(compact_stats["memory_savings_percentage"], digits=1))%")
    end
    
    # 快取統計
    println("\n💾 快取統計:")
    println("  - 整體命中率: $(round(engine_stats["cache_hit_rate"] * 100, digits=1))%")
    
catch e
    println("  ⚠️ 無法獲取記憶體統計: $e")
end

# ==========================================
# 8. 實用分析函數
# ==========================================

println("\n🛠️ 步驟 8: 實用分析函數")

# 定義一個實用的分析函數
function analyze_number_comprehensive(engine, number::Int)
    println("\n🔍 號碼 $number 的綜合分析:")
    
    # Skip 分析
    skip = calculate_skip_optimized(engine, number)
    println("  Skip 值: $skip")
    
    # 出現頻率分析
    frequency = 0
    for draw in engine.historical_data
        if number in draw.numbers
            frequency += 1
        end
    end
    total_draws = length(engine.historical_data)
    frequency_rate = (frequency / total_draws) * 100
    
    println("  出現次數: $frequency / $total_draws")
    println("  出現率: $(round(frequency_rate, digits=1))%")
    
    # 最後出現時間
    last_occurrence = 0
    for (i, draw) in enumerate(engine.historical_data)
        if number in draw.numbers
            last_occurrence = i
        end
    end
    
    if last_occurrence > 0
        days_since = total_draws - last_occurrence
        last_date = engine.historical_data[last_occurrence].draw_date
        println("  最後出現: 第 $last_occurrence 期 ($last_date)")
        println("  距今: $days_since 期")
    else
        println("  最後出現: 從未出現")
    end
    
    # 預測評級
    if skip <= 2
        rating = "🔥 極熱"
    elseif skip <= 5
        rating = "🌡️ 熱門"
    elseif skip <= 10
        rating = "😐 普通"
    else
        rating = "❄️ 冷門"
    end
    
    println("  預測評級: $rating")
    
    return Dict(
        "skip" => skip,
        "frequency" => frequency,
        "frequency_rate" => frequency_rate,
        "last_occurrence" => last_occurrence,
        "rating" => rating
    )
end

# 分析幾個號碼
analysis_numbers = [1, 15, 25, 39]
comprehensive_results = Dict{Int, Any}()

for number in analysis_numbers
    result = analyze_number_comprehensive(optimized_engine, number)
    comprehensive_results[number] = result
end

# ==========================================
# 9. 生成分析報告
# ==========================================

println("\n📋 步驟 9: 生成分析報告")

function generate_analysis_report(results::Dict{Int, Any})
    println("\n📊 Wonder Grid 分析報告")
    println("=" ^ 40)
    println("分析時間: $(now())")
    println("數據範圍: $(sample_draws[1].draw_date) 至 $(sample_draws[end].draw_date)")
    println("總期數: $(length(sample_draws))")
    
    println("\n🎯 號碼分析摘要:")
    sorted_results = sort(collect(results), by=x->x[2]["skip"])
    
    for (number, result) in sorted_results
        println("號碼 $number:")
        println("  Skip: $(result["skip"]) | 頻率: $(result["frequency"]) | 評級: $(result["rating"])")
    end
    
    # 推薦號碼
    hot_numbers = [num for (num, result) in results if result["skip"] <= 5]
    println("\n🔥 推薦關注號碼 (Skip ≤ 5):")
    if !isempty(hot_numbers)
        println("  $(join(hot_numbers, ", "))")
    else
        println("  暫無特別推薦的號碼")
    end
    
    println("\n📈 系統建議:")
    println("  1. 關注 Skip 值較小的號碼")
    println("  2. 結合歷史頻率進行判斷")
    println("  3. 考慮號碼的配對關係")
    println("  4. 定期更新數據以獲得最新分析")
end

# 生成報告
generate_analysis_report(comprehensive_results)

# ==========================================
# 10. 總結
# ==========================================

println("\n🎉 步驟 10: 分析總結")

println("\n✅ 基本分析範例完成！")
println("\n📚 本範例展示了以下功能:")
println("  1. ✅ 數據準備和引擎創建")
println("  2. ✅ Skip 分析（ONE 過濾器）")
println("  3. ✅ 配對分析（TWO 過濾器）")
println("  4. ✅ 高階過濾器使用")
println("  5. ✅ 性能比較測試")
println("  6. ✅ 記憶體使用分析")
println("  7. ✅ 綜合號碼分析")
println("  8. ✅ 分析報告生成")

println("\n🚀 下一步建議:")
println("  - 使用真實的歷史數據進行分析")
println("  - 探索並行計算功能")
println("  - 嘗試 Wonder Grid 生成")
println("  - 查看其他範例項目")

println("\n📖 相關文檔:")
println("  - API 參考: doc/api_reference.md")
println("  - 用戶手冊: doc/user_manual.md")
println("  - 更多範例: examples/")

println("\n🎯 Wonder Grid Lottery System - 讓數據驅動您的分析！")

# 清理資源
println("\n🧹 清理系統資源...")
try
    # 執行自動清理
    cleaned = auto_cleanup!(GLOBAL_MEMORY_POOL)
    if cleaned > 0
        println("✅ 清理了 $cleaned 個記憶體池項目")
    else
        println("✅ 記憶體池無需清理")
    end
catch e
    println("⚠️ 清理過程中出現問題: $e")
end

println("\n🎉 範例執行完成！感謝使用 Wonder Grid Lottery System！")
