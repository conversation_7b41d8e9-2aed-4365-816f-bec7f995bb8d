# Data Management Logic Verification Test
# Tests data sorting (newest first), data merging logic, and error handling completeness

using Test
using Dates
using CSV
using DataFrames

# Include necessary types and functions
include("src/types.jl")
include("src/file_management.jl")
include("src/validation.jl")

"""
Test data sorting (newest first) functionality
"""
function test_data_sorting()
    println("=== Data Sorting Verification ===")
    
    # Create test data with mixed dates
    test_data = [
        "2022-01-03,1,5,10,15,20",
        "2022-01-01,2,6,11,16,21", 
        "2022-01-05,3,7,12,17,22",
        "2022-01-02,4,8,13,18,23",
        "2022-01-04,5,9,14,19,24"
    ]
    
    # Write test file
    test_file = "test_data_sorting.csv"
    open(test_file, "w") do f
        for line in test_data
            println(f, line)
        end
    end
    
    try
        # Read file using FileManager
        fm = FileManager()
        draws = read_data5_file(fm, test_file)
        
        # Verify sorting (newest first)
        expected_order = [
            Date(2022, 1, 5),
            Date(2022, 1, 4), 
            Date(2022, 1, 3),
            Date(2022, 1, 2),
            Date(2022, 1, 1)
        ]
        
        actual_order = [draw.draw_date for draw in draws]
        
        @test actual_order == expected_order
        println("✓ Data sorted correctly (newest first)")
        
        # Verify data integrity
        @test length(draws) == 5
        @test all(length(draw.numbers) == 5 for draw in draws)
        println("✓ Data integrity maintained during sorting")
        
    finally
        # Clean up
        try
            if isfile(test_file)
                rm(test_file)
            end
        catch e
            @warn "Could not clean up test file: $e"
        end
    end
    
    return true
end

"""
Test data merging logic
"""
function test_data_merging()
    println("\n=== Data Merging Logic Verification ===")
    
    # Create real data file
    real_data = [
        "2022-01-05,1,5,10,15,20",
        "2022-01-03,2,6,11,16,21",
        "2022-01-01,3,7,12,17,22"
    ]
    
    # Create simulated data file
    sim_data = [
        "2022-01-06,4,8,13,18,23",
        "2022-01-04,5,9,14,19,24",
        "2022-01-02,6,10,15,20,25"
    ]
    
    real_file = "test_real_data.csv"
    sim_file = "test_sim_data.csv"
    
    # Write test files
    open(real_file, "w") do f
        for line in real_data
            println(f, line)
        end
    end
    
    open(sim_file, "w") do f
        for line in sim_data
            println(f, line)
        end
    end
    
    try
        # Create data directory if it doesn't exist
        if !isdir("data")
            mkdir("data")
        end
        
        # Merge files
        fm = FileManager()
        merged_file = merge_data_files(fm, real_file, sim_file)
        
        # Read merged file
        merged_draws = read_data5_file(fm, merged_file)
        
        # Verify merged data
        @test length(merged_draws) == 6  # 3 real + 3 simulated
        
        # Verify chronological order (newest first)
        expected_dates = [
            Date(2022, 1, 6),  # From sim
            Date(2022, 1, 5),  # From real
            Date(2022, 1, 4),  # From sim
            Date(2022, 1, 3),  # From real
            Date(2022, 1, 2),  # From sim
            Date(2022, 1, 1)   # From real
        ]
        
        actual_dates = [draw.draw_date for draw in merged_draws]
        @test actual_dates == expected_dates
        
        println("✓ Data merging works correctly")
        println("✓ Merged data maintains chronological order")
        
        # Clean up merged file
        if isfile(merged_file)
            rm(merged_file)
        end
        
    finally
        # Clean up test files
        for file in [real_file, sim_file]
            try
                if isfile(file)
                    rm(file)
                end
            catch e
                @warn "Could not clean up test file $file: $e"
            end
        end
    end
    
    return true
end

"""
Test error handling completeness
"""
function test_error_handling()
    println("\n=== Error Handling Verification ===")
    
    fm = FileManager()
    validator = DataValidator()
    
    # Test 1: Non-existent file
    result = validate_data5_file(validator, "non_existent_file.csv")
    @test !result.is_valid
    @test occursin("does not exist", result.message)
    println("✓ Non-existent file error handled correctly")
    
    # Test 2: Invalid number range
    invalid_data = [
        "2022-01-01,0,5,10,15,20",  # Number 0 is invalid
        "2022-01-02,1,5,10,15,40"   # Number 40 is invalid
    ]
    
    invalid_file = "test_invalid_data.csv"
    open(invalid_file, "w") do f
        for line in invalid_data
            println(f, line)
        end
    end
    
    try
        result = validate_data5_file(validator, invalid_file)
        @test !result.is_valid
        @test occursin("validation errors", result.message)
        println("✓ Invalid number range error handled correctly")
    finally
        if isfile(invalid_file)
            rm(invalid_file)
        end
    end
    
    # Test 3: Duplicate numbers
    duplicate_data = [
        "2022-01-01,1,1,10,15,20"  # Duplicate number 1
    ]
    
    duplicate_file = "test_duplicate_data.csv"
    open(duplicate_file, "w") do f
        for line in duplicate_data
            println(f, line)
        end
    end
    
    try
        result = validate_data5_file(validator, duplicate_file)
        @test !result.is_valid
        println("✓ Duplicate numbers error handled correctly")
    finally
        if isfile(duplicate_file)
            rm(duplicate_file)
        end
    end
    
    # Test 4: Wrong number count
    wrong_count_data = [
        "2022-01-01,1,5,10,15",     # Only 4 numbers
        "2022-01-02,1,5,10,15,20,25" # 6 numbers
    ]
    
    wrong_count_file = "test_wrong_count_data.csv"
    open(wrong_count_file, "w") do f
        for line in wrong_count_data
            println(f, line)
        end
    end
    
    try
        result = validate_data5_file(validator, wrong_count_file)
        @test !result.is_valid
        println("✓ Wrong number count error handled correctly")
    finally
        if isfile(wrong_count_file)
            rm(wrong_count_file)
        end
    end
    
    # Test 5: Chronological order validation
    wrong_order_data = [
        "2022-01-01,1,5,10,15,20",  # Older date first (should be newest first)
        "2022-01-03,2,6,11,16,21"
    ]
    
    wrong_order_file = "test_wrong_order_data.csv"
    open(wrong_order_file, "w") do f
        for line in wrong_order_data
            println(f, line)
        end
    end
    
    try
        result = validate_data5_file(validator, wrong_order_file)
        @test !result.is_valid
        @test occursin("Chronological order violation", result.message)
        println("✓ Chronological order error handled correctly")
    finally
        if isfile(wrong_order_file)
            rm(wrong_order_file)
        end
    end
    
    return true
end

"""
Test file format support (both date-prefixed and numbers-only)
"""
function test_file_format_support()
    println("\n=== File Format Support Verification ===")
    
    fm = FileManager()
    
    # Test 1: Date-prefixed format
    date_format_data = [
        "2022-01-03,1,5,10,15,20",
        "2022-01-02,2,6,11,16,21",
        "2022-01-01,3,7,12,17,22"
    ]
    
    date_file = "test_date_format.csv"
    open(date_file, "w") do f
        for line in date_format_data
            println(f, line)
        end
    end
    
    try
        draws = read_data5_file(fm, date_file)
        @test length(draws) == 3
        @test draws[1].draw_date == Date(2022, 1, 3)  # Newest first
        @test draws[1].numbers == [1, 5, 10, 15, 20]
        println("✓ Date-prefixed format supported")
    finally
        if isfile(date_file)
            rm(date_file)
        end
    end
    
    # Test 2: Numbers-only format
    numbers_only_data = [
        "1,5,10,15,20",
        "2,6,11,16,21",
        "3,7,12,17,22"
    ]
    
    numbers_file = "test_numbers_only.csv"
    open(numbers_file, "w") do f
        for line in numbers_only_data
            println(f, line)
        end
    end
    
    try
        draws = read_data5_file(fm, numbers_file)
        @test length(draws) == 3
        @test all(isa(draw.draw_date, Date) for draw in draws)  # Dates should be generated
        @test draws[end].numbers == [1, 5, 10, 15, 20]  # First line becomes last after sorting
        println("✓ Numbers-only format supported")
    finally
        if isfile(numbers_file)
            rm(numbers_file)
        end
    end
    
    return true
end

"""
Run all data management verification tests
"""
function run_data_management_verification_tests()
    println("Starting Data Management Logic Verification Tests...")
    
    try
        test_data_sorting()
        test_data_merging()
        test_error_handling()
        test_file_format_support()
        
        println("\n🎉 All data management verification tests passed!")
        return true
    catch e
        println("\n❌ Data management verification tests failed: $e")
        return false
    end
end

# Run tests if this file is executed directly
if abspath(PROGRAM_FILE) == @__FILE__
    run_data_management_verification_tests()
end
