---
created: 2025-07-24T21:26:41 (UTC +08:00)
tags: [lotto,lottery,software,odds,formula,probability,strategy,software,system,statistics,drawings,analysis,numbers,winning,combinations,randomness,lotto jackpot,random play,]
source: https://saliu.com/bbs/messages/919.html
author: 
---

# Dynamic, Static Lottery Filters, Software, Analysis

> ## Excerpt
> Study dynamic, static filtering in lotto, lottery analysis software. The odds of winning lotto jackpots improve dramatically with dynamic lottery filters created by the founder of lotto mathematics.

---
Published on February 14, 2002.

-   I made the case that we must look at everything dynamically, for everything in the Universe is dynamic. I do not want to get into the fundamental philosophy now. I only want to remind readers that I founded my gambling theory on _dynamic_, while rejecting _static_. I started my early lottery software on the foundation of _eliminating conditions_, or _restrictive patterns_, or, simply, _lottery restrictions_. Such conditions would _restrict_ or _eliminate_ certain lottery combinations from the output.

<u>One day in the early 1990s WEB I was out of coffee filters!</u> I still needed my morning coffee, for I can't write a single line of code without at least a drop of coffee! I brewed it, but the result that day tasted terrible! The filters are so important, I deduced. That's how I came up with the concept of _**lottery filters**_.

The lottery filters are now commonly used. You can hear about them in coffee shops, in smoking or non-smoking areas. I can hear them over the Internet as well. Many visits to my Web site originate in searches around _**lottery filters**_. Yet, the filters are widely misunderstood. They were meant to be dynamic. Alas, most people treat the filters as static entities! _“Play balanced amounts of odd and even numbers! Two odds, four evens! Equally lows and highs! Better to consider the lotto sums as well!”_

Then, there are those who would convert all the plows to swords to decapitate the “heretics” who believe in lottery strategies! The latter are the “inquisitors”. They scream:  
_“The probability of every combination is equally and forever _**p**_. Therefore any attempt to strategize a lotto game yields null as a necessary result.”_

But they do not accept that there is also another fundamental element of probability: _**degree of certainty (DC)**_ (see the _**Fundamental Formula of Gambling (FFG)**_.

The “heretics” are faced with two very serious problems. The static filters yield an impractical number of lotto combinations to play. In addition, the losing streaks (skips) are very difficult to track. The amount of combinations and the skips make the static lottery filters an exercise in futility. It is a no-no.

On the other hand, the “inquisitors” have a skin-deep understanding of probability. Let them take a pick-3 lottery game with a long history. Let them fry their brains studying the _**Fundamental Formula of Gambling (FFG)**_.

They can choose any lottery game. I picked pick-3 because human life is frankly and sadly short. The individual probability of a pick-3 combination is _1 in 1000_. The individual probability, however, is static. It does not describe a dynamic phenomenon, such as a series of events in time we call _lottery drawings_ (some use the word _draws_, because they are older). Let them take groups of 1000 draws. They will not find every pick-3 combination in the group. They have an equal p, why not an equal frequency? Only around 63% of the lottery combinations can be found in any 1000-draw group. (The Fundamental Formula of Gambling is always validated in such matters.)

There are combinations not to be found in 3000, 4000, even 5,000 consecutive lottery drawings! What gives? Statically, the combinations are equal in status. Dynamically, the combinations are smaller parts of a higher process guided by different laws. In _**FFG**_, p (the individual probability) is a part of a system. _P_ is not squashed, but incorporated in a larger process.

I want now to find common ground between the “heretics” and the “inquisitors”. I'll do it my way, for I prefer to prove both sides wrong! I want to show that using lotto filters clearly beats random playing. At the same time, the static filters are futile. They cannot be used in real life, for they are too expensive and lead to losses.

I offered for years free software programs (including source code and algorithms) to find the combination of a given index (or lexicographical order): NthIndex. I have created even more powerful software. The programs are LexicographicIndexes, SEQUENCE, NthIndex, and DrawIndex.

In fact, **DrawIndex** can take any lottery results (drawing) file and show the lexicographic indexes of the combinations.

![Software to calculate the combination lexicographical order of drawings in any lottery results file.](https://saliu.com/ScreenImgs/DrawIndex.gif)

I ran my software to generate multiple lotto combinations and save them to file. I applied the test to PA 6/69 lotto game (a very tough game, indeed; the lotto jackpot odds are _1 in 119,877,472_). I generated first 1000 combinations around the mid-point index: 59,938,736. That is, the lotto program generated combinations between the counts of 59,938,236 and 59,939,236. Next, I checked for winners in the output file against real draws in PA 6/69 lotto (350 real draws at my discretion). The _mid-point output_ achieved 3 _5 of 6_ hits and 140 _4 of 6 hits_. The game odds are _1 in 317,000_ for _5 of 6_ and _1 in 4092_ for _4 of 6_. The _5 of 6_ hits were recorded in 350 x 1000 = 350,000 combinations. Therefore, the _mid-point_ strategy was about 3 times better than random lotto play in the _5 of 6_ case. For the _4 of 6_ case, _1 in 4092_ applied to 350,000 combinations translates into 85. The mid-point strategy hit 140 times, or about one and a half times better.

The next test was applied against the _**FFG median**_, instead of _the mid-point_. The _FFG median_ is calculated in _**FFG**_ for a _**degree of certainty, DC**_, equal to **50%**. The _FFG median_ is 83,092,731 for a 6/69 lotto game. A number of 1000 combinations were generated in the 83,092,231 – 83,093,231 range. The results were undoubtedly better. The lotto strategy yielded 6 _5 of 6_ hits and 238 _4 of 6_ hits. Compared to purely random play, the _FFG median_ strategy was 6 times better for _5 of 6_ and about 3 times better for _4 of 6_.

The following is a report for a Powerball game that draws 5 regular numbers from 59 and one Power Ball from 39.
