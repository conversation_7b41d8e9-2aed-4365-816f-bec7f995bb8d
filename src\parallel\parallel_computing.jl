# Parallel Computing Module
# 並行計算模組 - 實現多執行緒並行處理

using Base.Threads
using Dates

# 引入基礎類型和優化組件
include("../types.jl")
include("../memory/memory_pool.jl")
include("../monitoring/performance_monitor.jl")

"""
並行任務結果
"""
struct ParallelTaskResult{T}
    result::T
    thread_id::Int
    execution_time_ms::Float64
    success::Bool
    error_message::String
    
    function ParallelTaskResult{T}(result::T, thread_id::Int, execution_time_ms::Float64) where T
        new{T}(result, thread_id, execution_time_ms, true, "")
    end
    
    function ParallelTaskResult{T}(error_msg::String, thread_id::Int, execution_time_ms::Float64) where T
        new{T}(nothing, thread_id, execution_time_ms, false, error_msg)
    end
end

"""
並行執行統計
"""
mutable struct ParallelExecutionStats
    total_tasks::Int
    successful_tasks::Int
    failed_tasks::Int
    total_execution_time_ms::Float64
    max_execution_time_ms::Float64
    min_execution_time_ms::Float64
    threads_used::Set{Int}
    
    function ParallelExecutionStats()
        new(0, 0, 0, 0.0, 0.0, Inf, Set{Int}())
    end
end

"""
更新並行執行統計
"""
function update_stats!(stats::ParallelExecutionStats, result::ParallelTaskResult)
    stats.total_tasks += 1
    push!(stats.threads_used, result.thread_id)
    
    if result.success
        stats.successful_tasks += 1
    else
        stats.failed_tasks += 1
    end
    
    stats.total_execution_time_ms += result.execution_time_ms
    stats.max_execution_time_ms = max(stats.max_execution_time_ms, result.execution_time_ms)
    stats.min_execution_time_ms = min(stats.min_execution_time_ms, result.execution_time_ms)
end

"""
並行計算所有 Skip 值
"""
function calculate_all_skips_parallel(historical_data::Vector{LotteryDraw}, numbers::Vector{Int} = collect(1:39))::Dict{Int, ParallelTaskResult{Int}}
    results = Dict{Int, ParallelTaskResult{Int}}()
    results_lock = ReentrantLock()
    
    @threads for number in numbers
        thread_id = threadid()
        start_time = time()
        
        try
            # 計算 Skip 值
            skip_value = calculate_skip_sequential(historical_data, number)
            execution_time = (time() - start_time) * 1000
            
            result = ParallelTaskResult{Int}(skip_value, thread_id, execution_time)
            
            lock(results_lock) do
                results[number] = result
            end
            
        catch e
            execution_time = (time() - start_time) * 1000
            error_msg = string(e)
            
            result = ParallelTaskResult{Int}(error_msg, thread_id, execution_time)
            
            lock(results_lock) do
                results[number] = result
            end
        end
    end
    
    return results
end

"""
序列計算 Skip 值（用於並行任務）
"""
function calculate_skip_sequential(historical_data::Vector{LotteryDraw}, number::Int)::Int
    for (i, draw) in enumerate(historical_data)
        if number in draw.numbers
            return i - 1
        end
    end
    return length(historical_data)
end

"""
並行分析配對頻率
"""
function analyze_pairings_parallel(historical_data::Vector{LotteryDraw}, number_pairs::Vector{Tuple{Int,Int}})::Dict{Tuple{Int,Int}, ParallelTaskResult{Int}}
    results = Dict{Tuple{Int,Int}, ParallelTaskResult{Int}}()
    results_lock = ReentrantLock()
    
    @threads for pair in number_pairs
        thread_id = threadid()
        start_time = time()
        
        try
            # 計算配對頻率
            frequency = calculate_pairing_frequency_sequential(historical_data, pair[1], pair[2])
            execution_time = (time() - start_time) * 1000
            
            result = ParallelTaskResult{Int}(frequency, thread_id, execution_time)
            
            lock(results_lock) do
                results[pair] = result
            end
            
        catch e
            execution_time = (time() - start_time) * 1000
            error_msg = string(e)
            
            result = ParallelTaskResult{Int}(error_msg, thread_id, execution_time)
            
            lock(results_lock) do
                results[pair] = result
            end
        end
    end
    
    return results
end

"""
序列計算配對頻率（用於並行任務）
"""
function calculate_pairing_frequency_sequential(historical_data::Vector{LotteryDraw}, num1::Int, num2::Int)::Int
    frequency = 0
    for draw in historical_data
        if num1 in draw.numbers && num2 in draw.numbers
            frequency += 1
        end
    end
    return frequency
end

"""
並行過濾器計算
"""
function calculate_filters_parallel(historical_data::Vector{LotteryDraw}, filter_requests::Vector{Tuple{String, Vector{Int}}})::Dict{String, ParallelTaskResult{Float64}}
    results = Dict{String, ParallelTaskResult{Float64}}()
    results_lock = ReentrantLock()
    
    @threads for (filter_type, numbers) in filter_requests
        thread_id = threadid()
        start_time = time()
        
        try
            # 根據過濾器類型計算
            filter_value = if filter_type == "ONE" && length(numbers) >= 1
                Float64(calculate_skip_sequential(historical_data, numbers[1]))
            elseif filter_type == "TWO" && length(numbers) >= 2
                # 計算配對數量
                pair_count = 0
                for i in 1:length(numbers)
                    for j in i+1:length(numbers)
                        freq = calculate_pairing_frequency_sequential(historical_data, numbers[i], numbers[j])
                        if freq > 0
                            pair_count += 1
                        end
                    end
                end
                Float64(pair_count)
            else
                0.0
            end
            
            execution_time = (time() - start_time) * 1000
            
            result = ParallelTaskResult{Float64}(filter_value, thread_id, execution_time)
            
            lock(results_lock) do
                results["$(filter_type)_$(join(numbers, "_"))"] = result
            end
            
        catch e
            execution_time = (time() - start_time) * 1000
            error_msg = string(e)
            
            result = ParallelTaskResult{Float64}(error_msg, thread_id, execution_time)
            
            lock(results_lock) do
                results["$(filter_type)_$(join(numbers, "_"))"] = result
            end
        end
    end
    
    return results
end

"""
執行緒安全的結果收集器
"""
mutable struct ThreadSafeResultCollector{T}
    results::Vector{T}
    lock::ReentrantLock
    
    function ThreadSafeResultCollector{T}() where T
        new{T}(Vector{T}(), ReentrantLock())
    end
end

"""
安全添加結果
"""
function add_result!(collector::ThreadSafeResultCollector{T}, result::T) where T
    lock(collector.lock) do
        push!(collector.results, result)
    end
end

"""
獲取所有結果
"""
function get_results(collector::ThreadSafeResultCollector{T})::Vector{T} where T
    lock(collector.lock) do
        return copy(collector.results)
    end
end

"""
負載平衡器
"""
struct LoadBalancer
    max_threads::Int
    current_load::Vector{Int}  # 每個執行緒的當前負載
    load_lock::ReentrantLock
    
    function LoadBalancer(max_threads::Int = nthreads())
        new(max_threads, zeros(Int, max_threads), ReentrantLock())
    end
end

"""
獲取最佳執行緒
"""
function get_best_thread(balancer::LoadBalancer)::Int
    lock(balancer.load_lock) do
        # 找到負載最小的執行緒
        min_load = minimum(balancer.current_load)
        best_thread = findfirst(x -> x == min_load, balancer.current_load)
        
        # 增加該執行緒的負載
        balancer.current_load[best_thread] += 1
        
        return best_thread
    end
end

"""
釋放執行緒負載
"""
function release_thread_load!(balancer::LoadBalancer, thread_id::Int)
    lock(balancer.load_lock) do
        if thread_id <= length(balancer.current_load)
            balancer.current_load[thread_id] = max(0, balancer.current_load[thread_id] - 1)
        end
    end
end

"""
並行批次處理
"""
function parallel_batch_process(data::Vector{T}, batch_size::Int, process_func::Function)::Vector{Any} where T
    if isempty(data)
        return []
    end
    
    # 分割數據為批次
    batches = []
    for i in 1:batch_size:length(data)
        end_idx = min(i + batch_size - 1, length(data))
        push!(batches, data[i:end_idx])
    end
    
    # 並行處理批次
    results = Vector{Any}(undef, length(batches))
    
    @threads for i in 1:length(batches)
        try
            results[i] = process_func(batches[i])
        catch e
            results[i] = Dict("error" => string(e), "batch_index" => i)
        end
    end
    
    return results
end

"""
並行性能基準測試
"""
function parallel_performance_benchmark(historical_data::Vector{LotteryDraw})::Dict{String, Any}
    benchmark_results = Dict{String, Any}()
    
    # 測試數據
    test_numbers = collect(1:39)
    test_pairs = [(i, j) for i in 1:10 for j in i+1:15]
    
    # 基準測試：Skip 計算
    println("🔄 並行 Skip 計算基準測試...")
    start_time = time()
    skip_results = calculate_all_skips_parallel(historical_data, test_numbers[1:10])
    skip_time = (time() - start_time) * 1000
    
    skip_stats = ParallelExecutionStats()
    for result in values(skip_results)
        update_stats!(skip_stats, result)
    end
    
    benchmark_results["skip_calculation"] = Dict(
        "total_time_ms" => skip_time,
        "tasks_completed" => skip_stats.successful_tasks,
        "tasks_failed" => skip_stats.failed_tasks,
        "threads_used" => length(skip_stats.threads_used),
        "avg_task_time_ms" => skip_stats.total_execution_time_ms / skip_stats.total_tasks,
        "max_task_time_ms" => skip_stats.max_execution_time_ms,
        "min_task_time_ms" => skip_stats.min_execution_time_ms
    )
    
    # 基準測試：配對分析
    println("🔄 並行配對分析基準測試...")
    start_time = time()
    pairing_results = analyze_pairings_parallel(historical_data, test_pairs[1:20])
    pairing_time = (time() - start_time) * 1000
    
    pairing_stats = ParallelExecutionStats()
    for result in values(pairing_results)
        update_stats!(pairing_stats, result)
    end
    
    benchmark_results["pairing_analysis"] = Dict(
        "total_time_ms" => pairing_time,
        "tasks_completed" => pairing_stats.successful_tasks,
        "tasks_failed" => pairing_stats.failed_tasks,
        "threads_used" => length(pairing_stats.threads_used),
        "avg_task_time_ms" => pairing_stats.total_execution_time_ms / pairing_stats.total_tasks,
        "max_task_time_ms" => pairing_stats.max_execution_time_ms,
        "min_task_time_ms" => pairing_stats.min_execution_time_ms
    )
    
    # 基準測試：過濾器計算
    println("🔄 並行過濾器計算基準測試...")
    filter_requests = [
        ("ONE", [1]), ("ONE", [5]), ("ONE", [10]),
        ("TWO", [1, 2]), ("TWO", [5, 10]), ("TWO", [15, 20])
    ]
    
    start_time = time()
    filter_results = calculate_filters_parallel(historical_data, filter_requests)
    filter_time = (time() - start_time) * 1000
    
    filter_stats = ParallelExecutionStats()
    for result in values(filter_results)
        update_stats!(filter_stats, result)
    end
    
    benchmark_results["filter_calculation"] = Dict(
        "total_time_ms" => filter_time,
        "tasks_completed" => filter_stats.successful_tasks,
        "tasks_failed" => filter_stats.failed_tasks,
        "threads_used" => length(filter_stats.threads_used),
        "avg_task_time_ms" => filter_stats.total_execution_time_ms / filter_stats.total_tasks,
        "max_task_time_ms" => filter_stats.max_execution_time_ms,
        "min_task_time_ms" => filter_stats.min_execution_time_ms
    )
    
    # 整體統計
    total_tasks = skip_stats.total_tasks + pairing_stats.total_tasks + filter_stats.total_tasks
    total_successful = skip_stats.successful_tasks + pairing_stats.successful_tasks + filter_stats.successful_tasks
    total_time = skip_time + pairing_time + filter_time
    
    benchmark_results["overall"] = Dict(
        "total_tasks" => total_tasks,
        "successful_tasks" => total_successful,
        "success_rate" => total_successful / total_tasks,
        "total_time_ms" => total_time,
        "available_threads" => nthreads(),
        "parallel_efficiency" => total_successful / (total_time / 1000) / nthreads()
    )
    
    return benchmark_results
end

"""
獲取並行計算能力信息
"""
function get_parallel_capabilities()::Dict{String, Any}
    return Dict(
        "available_threads" => nthreads(),
        "thread_pool_size" => nthreads(),
        "supports_threading" => nthreads() > 1,
        "julia_version" => VERSION,
        "threading_enabled" => Base.Threads.nthreads() > 1
    )
end

"""
分散式任務調度器
"""
mutable struct DistributedTaskScheduler
    task_queue::Vector{Any}
    completed_tasks::Vector{Any}
    failed_tasks::Vector{Any}
    queue_lock::ReentrantLock
    max_concurrent_tasks::Int
    active_tasks::Int

    function DistributedTaskScheduler(max_concurrent::Int = nthreads() * 2)
        new(
            Vector{Any}(),
            Vector{Any}(),
            Vector{Any}(),
            ReentrantLock(),
            max_concurrent,
            0
        )
    end
end

"""
添加任務到調度器
"""
function add_task!(scheduler::DistributedTaskScheduler, task::Any)
    lock(scheduler.queue_lock) do
        push!(scheduler.task_queue, task)
    end
end

"""
獲取下一個任務
"""
function get_next_task!(scheduler::DistributedTaskScheduler)
    lock(scheduler.queue_lock) do
        if !isempty(scheduler.task_queue) && scheduler.active_tasks < scheduler.max_concurrent_tasks
            scheduler.active_tasks += 1
            return popfirst!(scheduler.task_queue)
        end
        return nothing
    end
end

"""
標記任務完成
"""
function mark_task_completed!(scheduler::DistributedTaskScheduler, task::Any, result::Any)
    lock(scheduler.queue_lock) do
        push!(scheduler.completed_tasks, (task, result))
        scheduler.active_tasks = max(0, scheduler.active_tasks - 1)
    end
end

"""
標記任務失敗
"""
function mark_task_failed!(scheduler::DistributedTaskScheduler, task::Any, error::Any)
    lock(scheduler.queue_lock) do
        push!(scheduler.failed_tasks, (task, error))
        scheduler.active_tasks = max(0, scheduler.active_tasks - 1)
    end
end

"""
分散式 Wonder Grid 生成
"""
function generate_wonder_grid_distributed(historical_data::Vector{LotteryDraw}, grid_size::Int = 100)::Dict{String, Any}
    scheduler = DistributedTaskScheduler()

    # 創建任務：為每個號碼生成預測
    for number in 1:39
        task = Dict(
            "type" => "wonder_grid_prediction",
            "number" => number,
            "data" => historical_data
        )
        add_task!(scheduler, task)
    end

    # 並行執行任務
    results = Dict{Int, Any}()
    results_lock = ReentrantLock()

    @threads for _ in 1:min(nthreads(), 39)
        while true
            task = get_next_task!(scheduler)
            if task === nothing
                break
            end

            try
                # 執行 Wonder Grid 預測
                number = task["number"]
                data = task["data"]

                # 計算該號碼的各種指標
                skip_value = calculate_skip_sequential(data, number)

                # 計算與其他號碼的配對頻率
                pairing_scores = Dict{Int, Int}()
                for other_number in 1:39
                    if other_number != number
                        freq = calculate_pairing_frequency_sequential(data, number, other_number)
                        pairing_scores[other_number] = freq
                    end
                end

                # 計算預測評分
                prediction_score = calculate_prediction_score(skip_value, pairing_scores)

                result = Dict(
                    "number" => number,
                    "skip_value" => skip_value,
                    "pairing_scores" => pairing_scores,
                    "prediction_score" => prediction_score,
                    "thread_id" => threadid()
                )

                lock(results_lock) do
                    results[number] = result
                end

                mark_task_completed!(scheduler, task, result)

            catch e
                mark_task_failed!(scheduler, task, string(e))
            end
        end
    end

    # 生成 Wonder Grid
    wonder_grid = generate_grid_from_results(results, grid_size)

    return Dict(
        "wonder_grid" => wonder_grid,
        "individual_results" => results,
        "completed_tasks" => length(scheduler.completed_tasks),
        "failed_tasks" => length(scheduler.failed_tasks),
        "total_numbers_analyzed" => length(results)
    )
end

"""
計算預測評分
"""
function calculate_prediction_score(skip_value::Int, pairing_scores::Dict{Int, Int})::Float64
    # 簡化的預測評分算法
    skip_score = 1.0 / (skip_value + 1)  # Skip 越小評分越高

    # 配對評分：與熱門號碼配對的評分
    pairing_score = 0.0
    if !isempty(pairing_scores)
        max_pairing = maximum(values(pairing_scores))
        avg_pairing = sum(values(pairing_scores)) / length(pairing_scores)
        pairing_score = (max_pairing + avg_pairing) / 2.0
    end

    # 綜合評分
    return skip_score * 0.6 + pairing_score * 0.4
end

"""
從結果生成 Wonder Grid
"""
function generate_grid_from_results(results::Dict{Int, Any}, grid_size::Int)::Vector{Vector{Int}}
    # 按預測評分排序號碼
    sorted_numbers = sort(collect(keys(results)),
                         by = num -> results[num]["prediction_score"],
                         rev = true)

    # 生成網格
    grid = Vector{Vector{Int}}()
    numbers_per_row = 5

    for i in 1:numbers_per_row:min(grid_size, length(sorted_numbers))
        end_idx = min(i + numbers_per_row - 1, length(sorted_numbers))
        row = sorted_numbers[i:end_idx]

        # 如果行不足 5 個號碼，用評分較低的號碼填充
        while length(row) < numbers_per_row && length(row) < length(sorted_numbers)
            remaining = setdiff(sorted_numbers, row)
            if !isempty(remaining)
                push!(row, remaining[1])
            else
                break
            end
        end

        push!(grid, row)
    end

    return grid
end

"""
分散式歷史數據分析
"""
function analyze_historical_data_distributed(historical_data::Vector{LotteryDraw})::Dict{String, Any}
    scheduler = DistributedTaskScheduler()

    # 創建分析任務
    analysis_tasks = [
        Dict("type" => "frequency_analysis", "data" => historical_data),
        Dict("type" => "skip_analysis", "data" => historical_data),
        Dict("type" => "pairing_analysis", "data" => historical_data),
        Dict("type" => "trend_analysis", "data" => historical_data)
    ]

    for task in analysis_tasks
        add_task!(scheduler, task)
    end

    # 並行執行分析
    analysis_results = Dict{String, Any}()
    results_lock = ReentrantLock()

    @threads for _ in 1:min(nthreads(), length(analysis_tasks))
        while true
            task = get_next_task!(scheduler)
            if task === nothing
                break
            end

            try
                result = execute_analysis_task(task)

                lock(results_lock) do
                    analysis_results[task["type"]] = result
                end

                mark_task_completed!(scheduler, task, result)

            catch e
                mark_task_failed!(scheduler, task, string(e))
            end
        end
    end

    return Dict(
        "analysis_results" => analysis_results,
        "completed_analyses" => length(scheduler.completed_tasks),
        "failed_analyses" => length(scheduler.failed_tasks),
        "execution_summary" => Dict(
            "total_data_points" => length(historical_data),
            "analysis_types" => length(analysis_tasks),
            "threads_used" => nthreads()
        )
    )
end

"""
執行分析任務
"""
function execute_analysis_task(task::Dict{String, Any})::Dict{String, Any}
    data = task["data"]
    analysis_type = task["type"]

    if analysis_type == "frequency_analysis"
        return perform_frequency_analysis(data)
    elseif analysis_type == "skip_analysis"
        return perform_skip_analysis(data)
    elseif analysis_type == "pairing_analysis"
        return perform_pairing_analysis(data)
    elseif analysis_type == "trend_analysis"
        return perform_trend_analysis(data)
    else
        throw(ArgumentError("未知的分析類型: $analysis_type"))
    end
end

"""
執行頻率分析
"""
function perform_frequency_analysis(data::Vector{LotteryDraw})::Dict{String, Any}
    frequency_counts = Dict{Int, Int}()
    for number in 1:39
        frequency_counts[number] = 0
    end

    for draw in data
        for number in draw.numbers
            frequency_counts[number] += 1
        end
    end

    total_draws = length(data)
    frequency_percentages = Dict{Int, Float64}()
    for (number, count) in frequency_counts
        frequency_percentages[number] = (count / total_draws) * 100
    end

    # 找出最熱門和最冷門的號碼
    sorted_by_freq = sort(collect(frequency_counts), by = x -> x[2], rev = true)
    hot_numbers = [x[1] for x in sorted_by_freq[1:min(10, length(sorted_by_freq))]]
    cold_numbers = [x[1] for x in sorted_by_freq[max(1, end-9):end]]

    return Dict(
        "frequency_counts" => frequency_counts,
        "frequency_percentages" => frequency_percentages,
        "hot_numbers" => hot_numbers,
        "cold_numbers" => cold_numbers,
        "total_draws_analyzed" => total_draws
    )
end

"""
執行 Skip 分析
"""
function perform_skip_analysis(data::Vector{LotteryDraw})::Dict{String, Any}
    skip_values = Dict{Int, Int}()
    skip_histories = Dict{Int, Vector{Int}}()

    for number in 1:39
        skip_values[number] = calculate_skip_sequential(data, number)
        skip_histories[number] = calculate_skip_history(data, number)
    end

    # 計算 Skip 統計
    all_skips = collect(values(skip_values))
    avg_skip = sum(all_skips) / length(all_skips)
    max_skip = maximum(all_skips)
    min_skip = minimum(all_skips)

    return Dict(
        "current_skips" => skip_values,
        "skip_histories" => skip_histories,
        "average_skip" => avg_skip,
        "max_skip" => max_skip,
        "min_skip" => min_skip,
        "numbers_analyzed" => length(skip_values)
    )
end

"""
計算 Skip 歷史
"""
function calculate_skip_history(data::Vector{LotteryDraw}, number::Int)::Vector{Int}
    skip_history = Int[]
    last_occurrence = 0

    for (i, draw) in enumerate(data)
        if number in draw.numbers
            if last_occurrence > 0
                push!(skip_history, i - last_occurrence - 1)
            end
            last_occurrence = i
        end
    end

    return skip_history
end

"""
執行配對分析
"""
function perform_pairing_analysis(data::Vector{LotteryDraw})::Dict{String, Any}
    # 分析前 20 個號碼的配對（減少計算量）
    pairing_matrix = Dict{Tuple{Int,Int}, Int}()

    for i in 1:20
        for j in i+1:20
            freq = calculate_pairing_frequency_sequential(data, i, j)
            pairing_matrix[(i, j)] = freq
        end
    end

    # 找出最熱門的配對
    sorted_pairs = sort(collect(pairing_matrix), by = x -> x[2], rev = true)
    hot_pairs = sorted_pairs[1:min(10, length(sorted_pairs))]

    return Dict(
        "pairing_matrix" => pairing_matrix,
        "hot_pairs" => hot_pairs,
        "total_pairs_analyzed" => length(pairing_matrix)
    )
end

"""
執行趨勢分析
"""
function perform_trend_analysis(data::Vector{LotteryDraw})::Dict{String, Any}
    if length(data) < 10
        return Dict("error" => "數據量不足進行趨勢分析")
    end

    # 分析最近 10 次開獎的趨勢
    recent_data = data[max(1, end-9):end]
    recent_numbers = Set{Int}()

    for draw in recent_data
        for number in draw.numbers
            push!(recent_numbers, number)
        end
    end

    # 計算趨勢評分
    trend_scores = Dict{Int, Float64}()
    for number in 1:39
        # 簡化的趨勢評分：最近出現的號碼評分較高
        if number in recent_numbers
            trend_scores[number] = 1.0
        else
            trend_scores[number] = 0.0
        end
    end

    return Dict(
        "recent_numbers" => collect(recent_numbers),
        "trend_scores" => trend_scores,
        "analysis_period" => length(recent_data),
        "trending_up" => collect(recent_numbers),
        "trending_down" => setdiff(1:39, recent_numbers)
    )
end

# 導出主要函數和結構
export ParallelTaskResult, ParallelExecutionStats
export calculate_all_skips_parallel, analyze_pairings_parallel, calculate_filters_parallel
export ThreadSafeResultCollector, add_result!, get_results
export LoadBalancer, get_best_thread, release_thread_load!
export parallel_batch_process, parallel_performance_benchmark
export get_parallel_capabilities, update_stats!
export DistributedTaskScheduler, add_task!, get_next_task!
export mark_task_completed!, mark_task_failed!
export generate_wonder_grid_distributed, analyze_historical_data_distributed
