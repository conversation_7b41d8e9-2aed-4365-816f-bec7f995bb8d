using WonderGridLotterySystem
using Statistics
using Dates

println("Testing Pairing Engine System")
println("=" ^ 40)

# Load lottery data
fm = FileManager()
data = read_data5_file(fm, "data/fan5.csv")
println("Loaded $(length(data)) lottery draws for pairing analysis")

# Create pairing engine
engine = PairingEngine(data)

# Test pairing frequency calculations
println("\nCalculating All Pairing Frequencies...")
println("=" ^ 50)

start_time = time()
all_pairings = calculate_all_pairings(engine)
calculation_time = time() - start_time

total_possible_pairs = binomial(39, 2)  # C(39,2) = 741
println("Total possible pairs: $total_possible_pairs")
println("Calculated pairings: $(length(all_pairings))")
println("Calculation time: $(round(calculation_time, digits=3)) seconds")

# Analyze pairing frequency distribution
pairing_frequencies = collect(values(all_pairings))
println("\nPairing Frequency Statistics:")
println("  Total pairing occurrences: $(sum(pairing_frequencies))")
println("  Mean frequency per pair: $(round(mean(pairing_frequencies), digits=2))")
println("  Median frequency per pair: $(round(median(pairing_frequencies), digits=2))")
println("  Min frequency: $(minimum(pairing_frequencies))")
println("  Max frequency: $(maximum(pairing_frequencies))")
println("  Standard deviation: $(round(std(pairing_frequencies), digits=2))")

# Find most and least frequent pairs
sorted_pairs = sort(collect(all_pairings), by = x -> x[2], rev = true)

println("\nTop 20 Most Frequent Pairs:")
for i in 1:20
    pair, freq = sorted_pairs[i]
    percentage = round(100 * freq / sum(pairing_frequencies), digits=2)
    println("  $i. $(pair[1])-$(pair[2]): $freq times ($(percentage)%)")
end

println("\nBottom 20 Least Frequent Pairs:")
for i in 1:20
    pair, freq = sorted_pairs[end-i+1]
    percentage = round(100 * freq / sum(pairing_frequencies), digits=2)
    println("  $i. $(pair[1])-$(pair[2]): $freq times ($(percentage)%)")
end

# Test top pairings for specific numbers
println("\n" * "=" ^ 50)
println("Top Pairings Analysis for Key Numbers")
println("=" ^ 50)

test_numbers = [8, 14, 29, 36, 37]  # Top 5 favorable numbers

for number in test_numbers
    println("\nNumber $number Analysis:")
    
    # Get top 25% pairings
    top_25_pairings = get_top_pairings(engine, number, 0.25)
    top_10_pairings = get_top_pairings(engine, number, 0.10)
    top_50_pairings = get_top_pairings(engine, number, 0.50)
    
    println("  Top 10% pairings ($(length(top_10_pairings))): $(join(top_10_pairings, ", "))")
    println("  Top 25% pairings ($(length(top_25_pairings))): $(join(top_25_pairings, ", "))")
    println("  Top 50% pairings ($(length(top_50_pairings))): $(join(top_50_pairings, ", "))")
    
    # Calculate actual frequencies for top 25% pairings
    total_freq = 0
    pairing_details = []
    
    for paired_number in top_25_pairings
        pair_key = number < paired_number ? (number, paired_number) : (paired_number, number)
        freq = get(all_pairings, pair_key, 0)
        total_freq += freq
        push!(pairing_details, (paired_number, freq))
    end
    
    # Sort by frequency
    sort!(pairing_details, by = x -> x[2], rev = true)
    
    println("  Top 25% pairing details:")
    for (i, (paired_num, freq)) in enumerate(pairing_details)
        println("    $i. Number $paired_num: $freq times")
    end
    
    # Calculate what percentage of total pairings this represents
    all_number_pairings = []
    for other_num in 1:39
        if other_num != number
            pair_key = number < other_num ? (number, other_num) : (other_num, number)
            freq = get(all_pairings, pair_key, 0)
            push!(all_number_pairings, freq)
        end
    end
    
    total_number_freq = sum(all_number_pairings)
    percentage = round(100 * total_freq / total_number_freq, digits=1)
    println("  Top 25% pairings represent $(percentage)% of total frequency for number $number")
end

# Test Wonder Grid generation
println("\n" * "=" ^ 50)
println("Wonder Grid Generation Test")
println("=" ^ 50)

start_time = time()
wonder_grid = generate_wonder_grid(engine)
generation_time = time() - start_time

println("Generated Wonder Grid for all 39 numbers in $(round(generation_time, digits=3)) seconds")
println("Wonder Grid structure verification:")
println("  Numbers with grids: $(length(wonder_grid))")
println("  Expected: 39")

# Verify grid completeness
all_numbers_present = all(haskey(wonder_grid, i) for i in 1:39)
println("  All numbers present: $(all_numbers_present ? "YES" : "NO")")

# Show sample wonder grid entries
println("\nSample Wonder Grid Entries:")
sample_numbers = [1, 10, 20, 30, 39]
for number in sample_numbers
    pairings = wonder_grid[number]
    println("  Number $number: $(join(pairings[1:min(5, length(pairings))], ", "))$(length(pairings) > 5 ? "..." : "")")
end

# Verify the 25% rule for Wonder Grid
println("\n" * "=" ^ 50)
println("Wonder Grid 25% Rule Verification")
println("=" ^ 50)

verification_results = []

for number in 1:10  # Test first 10 numbers
    grid_pairings = wonder_grid[number]
    
    # Calculate total frequency for this number's pairings
    grid_freq = 0
    for paired_number in grid_pairings
        pair_key = number < paired_number ? (number, paired_number) : (paired_number, number)
        freq = get(all_pairings, pair_key, 0)
        grid_freq += freq
    end
    
    # Calculate total frequency for all pairings of this number
    total_freq = 0
    for other_num in 1:39
        if other_num != number
            pair_key = number < other_num ? (number, other_num) : (other_num, number)
            freq = get(all_pairings, pair_key, 0)
            total_freq += freq
        end
    end
    
    percentage = total_freq > 0 ? round(100 * grid_freq / total_freq, digits=1) : 0.0
    push!(verification_results, (number, percentage))
    
    println("  Number $number: Top pairings represent $(percentage)% of total frequency")
end

avg_percentage = round(mean([x[2] for x in verification_results]), digits=1)
println("\nAverage percentage for top 25% pairings: $(avg_percentage)%")
println("Expected: ~50% (according to Wonder Grid theory)")

# Performance benchmarks
println("\n" * "=" ^ 50)
println("Performance Benchmarks")
println("=" ^ 50)

# Test pairing calculation performance
println("Pairing calculation performance:")
start_time = time()
for _ in 1:5
    test_pairings = calculate_all_pairings(engine)
end
elapsed_time = time() - start_time
println("  5 iterations: $(round(elapsed_time, digits=3)) seconds")
println("  Average per calculation: $(round(elapsed_time / 5, digits=3)) seconds")

# Test top pairings performance
println("\nTop pairings extraction performance:")
start_time = time()
for number in 1:39
    top_pairings = get_top_pairings(engine, number, 0.25)
end
elapsed_time = time() - start_time
println("  All 39 numbers: $(round(elapsed_time, digits=3)) seconds")
println("  Average per number: $(round(elapsed_time / 39 * 1000, digits=2)) ms")

# Test dynamic updates
println("\n" * "=" ^ 50)
println("Dynamic Update Test")
println("=" ^ 50)

# Get initial pairing count for a specific pair
test_pair = (10, 27)  # Most frequent pair
initial_freq = get(all_pairings, test_pair, 0)
println("Initial frequency for pair $(test_pair[1])-$(test_pair[2]): $initial_freq")

# Create a new draw containing this pair
new_draw = LotteryDraw([10, 27, 15, 25, 35], Date("2025-07-22"), length(data) + 1)
println("Adding new draw: $(new_draw.numbers)")

# Update pairings
update_pairings(engine, new_draw)
updated_pairings = calculate_all_pairings(engine)
updated_freq = get(updated_pairings, test_pair, 0)

println("Updated frequency for pair $(test_pair[1])-$(test_pair[2]): $updated_freq")
println("Frequency change: $(updated_freq - initial_freq)")

# Test memory efficiency
println("\n" * "=" ^ 50)
println("Memory Efficiency Analysis")
println("=" ^ 50)

# Calculate memory usage estimate
pairs_count = length(all_pairings)
estimated_memory = pairs_count * (sizeof(Tuple{Int,Int}) + sizeof(Int))
println("Estimated memory usage for pairing cache: $(round(estimated_memory / 1024, digits=2)) KB")
println("Memory per pair: $(round(estimated_memory / pairs_count, digits=1)) bytes")

println("\nPairing Engine testing completed successfully!")