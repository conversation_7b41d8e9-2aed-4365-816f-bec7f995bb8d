---
created: 2025-07-24T21:26:40 (UTC +08:00)
tags: [Markov Chains,games,gambling,software,formula,probability,lottery,lotto,mathematics,random,lotto-6,source code,programming,algorithm,number,follower,lotto pair,]
source: https://saliu.com/Markov_Chains.html
author: 
---

# Markov Chains, Lottery, Lotto, Software, Algorithms, Program

> ## Excerpt
> Apply Markov chains mathematics, analysis to computer programming, software for lottery, lotto, gambling, algorithms for number followers, lottery pairs.

---
![See here the first application of Markov Chains mathematics to lottery software, lotto games.](https://saliu.com/HLINE.gif)

### I. [First Lotto Program Based on _Markov Chains_: _markov.exe_](https://saliu.com/Markov_Chains.html#MarkovLottery)  
II. [<PERSON>'s Algorithm for Enhanced _Markov Chains_ Lottery Software](https://saliu.com/Markov_Chains.html#Algorithms)

![Study the most efficient Markov chain algorithm adding lottery pairs to lotto number followers.](https://saliu.com/HLINE.gif)

## <u>1. The First Lotto Program Based on <i>Markov Chains</i>: <span color="#ff0000"><i>markov.exe</i></span> by <PERSON>ristiano Lopes</u>

-   First _capture_ by the _WayBack Machine_ (_web.archive.org_) on January 13, 2003.

-   [_**thornc Cristiano Lopes programming**_](https://saliu.com/programming.html) post in one of my oldest lottery and gambling forums was a surprise to me. We hadn't discussed anything about _**Markov chains**_ and his writing _dedicated_ lotto software.

Well, let me detail the presentation of **Cristiano Lopes**. First of all, the probability is high this is his real name. And these are his real intentions. Such traits are rare in Cyberland. I made him a gambling author. He created a nice utility: CoolRevGui. I urged him to write a distributable piece of software. The result is a really fine file viewer, file reverser, and file shuffler.

I had written DOS utilities to perform such tasks. But CoolRevGui offers the convenience of GUI, specifically choosing files via point-and-click. A sidebar here. Back in the 1980s, Apple Computer introduced Macintosh. The tough guys of the computing world quickly came up with a joke. They said Mac was for yuppies. Yuppies could never use two hands in performing any task. Reason: they always have a glass of Bourbon in the left hand… Since I consider life a tough endeavor, I thought I might as well take the tough path on the _computing Appalachian Trail_ (read: _**Command Prompt**_ software programming)!

Now I'm on my way of making Cristiano a tough competitor. That is, my only competitor — _I feel so all-alone, when many others get stoned_ (from Bob Dylan). I don't hide behind false modesty. I do not have a real competitor in gambling and theory of games. The fundamental truth is that we are far better off when we have competitors. Regardless of the headaches competitors bring about, competition is the only source of high quality adrenaline. Physiologically, creativity is impossible without intense flowing of adrenaline. (One can make a parallel with intense flow in procreation during wartimes.)

This week I had the first sign Cristiano was on his way to becoming my biggest competitor. We discussed briefly the _**Markov chains**_. I have written a few paragraphs on _Markov chains_ in a couple of messages. Cristiano had prior experience with the subject while a student in computer science. I advised him to use an algorithm in his textbooks to write a simple _Markov chains_ program applied to lotto data files. The start is the most important thing. The start is the hardest part. Every subsequent step is a piece of cake. We are stopped in the tracks by the toxic shame of failure at step 1.01. (I look sometimes at the very first incarnation of LotWon: November 1988. Compared to my current software, _LotWon 1.01_ would generate the feeling of embarrassment to myself. But to me, my old lotto software generates a feeling of nostalgia; and a feeling of pride for having the courage to step on step one and see beyond step one.)

So, he sent to me a little program named _**Markov**_. The program takes a lotto-6 data file, performs _**Markov chains correlating**_, and outputs a new data file. The output file tries to offer the highest-probability combinations in future lotto-6 drawings. Cristiano asked me to test the program. I did so with a really tough lotto-6 game: Pennsylvania lottery 6/69 game. I tried a few ranges of past draws to input to the application. One of the ranges yielded a result way above random expectation. I created an input file with 35 combinations. They represented draw #3 through draw #37. The program generated an output file of 20 combinations. The last line had only 4 numbers, so I deleted it.

The 19-combination output file yielded 2 3-hit combinations within the next two draws. Those were line #1 and line #2 in the data file. They acted as _future drawings_. That's a total of 38 combinations played. The probability of _3 of 6_ in a 6/69 game is _1 in 151_. A total of 302 combinations are needed to get 2 _3 of 6_ hits. The tiny _**Markov**_ program was **7.95 times** (302 / 38 = 795%) better than random expectation! That's a huge improvement by any standards. (You may note that **35** is approximately equal to **N/2** in a 69-number game. Search for _wonder grid_ and _CheckGrid_ for similarities in my software.)

Cristiano sent me various versions of the source code of CollRevGui. In **C** programming language, but if I can see then I can read **C**. This time, however, he did not send me the source code. The Internet is flooded by source code for Markov chains programs! Most of them try to create meaningful text from previous pieces of text. Cristiano's program, however, is the first attempt to apply _Markov chains correlating_ to lotto games. It is also the first attempt to generate focused prediction of lotto combinations based on _Markov chains_. Other programming attempts I've seen are vague, at best.

_“... you may however share it with someone in order to test it in different lottery data sets.  
Its usage is quite simple, you just type:

**MARKOV < DrawsFile > OutputFile**

If there is no error message at the end of the run, just open the **OutputFile** in a text editor (**Notepad**, **MDIEditor And Lotto WE**, etc.)

An example with a lottery data file:

**MARKOV** < **LOTTO-6.DAT** > **MARKOV-6.OUT**

Separate all parameters by one blank space. Don't leave out the < and > signs. They signify **input** and **output**, respectively.

-   My **Markov chains lotto software** is not very robust yet; a few bugs and errors may appear. At most, it will generate 1000 combinations each time. You may need to delete the last line from the output file, because for some strange reason it gets truncated and doesn't have the 6 numbers!  
    
-   The program should work with any **6/??** lottory, the first lines are heavily biased to the top draws in the drawings file.  
    _-   _I made some tests and guess what, it consistently hits **4in6** and **3in6** for future draws based on real past drawings. In some tests I have even hit **5 in 6**, didn't get past that... yet!”_

I would hope Cristiano goes beyond step 1.00 of his Markov lottery software. A future program is recommended to be distributable, even if for testing only. He need not reveal the source code. The adrenaline will always write better source code. For me, at least...

A few suggestions to make the Markov lotto program more **functional** and **usable** automatically -

-   Add a few lines describing what the program does and its basic requirements;
-   Add a simple box where the user is asked to enter the _InputFile_ of lotto drawings;
-   Add an input box where the user selects number of lottery draws to analyze in the _InputFile_;
-   Add a simple box where the user is asked to type the _OutputFile_ filename;
-   If the run was successful, add a final dialog, informing: _Success! Run it again (Y/N?)_.

![Ion Saliu's improved Markov-chains algorithm based on pairs of lotto numbers picked randomly.](https://saliu.com/HLINE.gif)

## <u>2. Ion Saliu's Algorithm for <i>Enhanced Markov Chains Lottery Software</i></u>

The _Markov chains theory_ follows random paths. My theory as applied to the _**lotto wonder grid**_ takes the path indicated by the _**Fundamental Formula of Gambling (FFG)**_. Insofar as pick lottery is concerned, _**FFG**_ outruns Markov by several steps. Another poster here, _Shalaska_ the sports bettor, knows better what I am talking about.

The _**Fundamental Formula of Gambling (FFG)**_ is by far the most precise instrument in theory of games and gambling. _**FFG**_ offers far more accurate (focused) results than _Markov chains_ correlating.

-   Looking at the last line in the output file — only 4 numbers, instead of 6 — it offers a hint. It appears that Cristiano worked with 2-number _“prefixes”_ and 1-number _“suffix”_ (_"follower"_). That is, Cristiano's _Markov chains_ algorithm breaks every lotto combination in the input into 2-number groups and records what number followed that group. These are the 4 groups per a 6-number lottery draw: 1–2, 2–3, 3–4, 4–5. The 4–5 _“prefix”_ is the last group with a _“follower”_ (_“suffix”_) — that would be the 6th number in the lotto drawing.
-   Another _Markov chain_ algorithm can have 1-number _prefix_ and 1-number _follower_. Then, the _Markov chains_ program randomly takes 1 _prefix_ and randomly adds to it numbers from the corresponding _follower_ row to create a 6-number lottery combination. This algorithm somehow resembles the _**pairing method**_ in my _[**wonder grid** lotto strategy](https://saliu.com/bbs/messages/9.html)_. Markov algorithms with _prefixes_ of 3 numbers or more would require very large data files and would be very, very slow to process.
-   An **enhanced** _Markov chain_ program would create rows of _pairings_, instead of _followers_. A lotto combonation (a favorite concept of mine!) has, let's say, 6 numbers: _1 2 3 4 5 6_. In a _Markov chain_ algorithm, #5 has exactly one follower: #6. Number 6, however has **no** _follower_! In the _wonder grid_ lottery strategy, every number in the combination has 5 _pairs_, including #6.
-   The _lottery pair_ software creates a _row_ for each lotto number in the game with its _top pairings_ (including all pairs).
-   The **enhanced** _Markov chains_ software would still _randomly_ pick a number from the corresponding row of pairs (say, the top 10 pairings).
-   One particular _Markov chain_ method (_N = Pairs NO Pivot Number_) is more powerful than the _classical_ Markov algorithm for lottery, since there are 5 times more _pairs_ than _followers_ (in a 6-number lotto combination). As a matter of fact, _follower_ is not the correct term, more often than not. _The lotto combinations are always sorted in ascending order._ Thus, the software does not know if lotto #6 was drawn before #2 — and _vice versa_. The program only knows that #2 and #6 were drawn together — that is, they were **paired** in a particular lottery drawing.
-   As a matter of fact, my _Markov-Chains_\-inspired lottery software is a lot more complex. It implements several combination-generating methods based on: _Number Frequencies, Pairings, Followers_ (one traditional _Markov_ method, _C_, another one, _M_, is an original algorithm of mine).
-   For the latest in Markov Chains lottery, algorithms, software, read:
-   [_**Markov Chains, Followers, Pairs, Lottery, Lotto, Software**_](https://saliu.com/markov-chains-lottery.html).

![Ion Saliu's Markov chains lotto software is more effective than regular Markov based on followers.](https://saliu.com/images/MarkovChainsLottery.gif)

And here is, axiomaticus, a most effective function in lottery of an application of a Markov-Chains algorithm — _M = Generate Combinations from Pairings as Markov Chains_:

![Ion Saliu's Markov chains lotto software is more effective than regular Markov based on followers.](https://saliu.com/images/MarkovChainsPairs.gif)

— and this is a most effective method of a lotto Markov-Chains algorithm — _F = Pairings & Most Frequent Numbers_:

![Ion Saliu's Markov chains lotto software is more effective than regular Markov based on followers.](https://saliu.com/images/MarkovChainsPairs.gif)

![Ion Saliu's Markov chains lotto software is more effective than regular Markov based on followers.](https://saliu.com/images/MarkovChainsPairsHotNumbers.gif)

-   Download [**_markov.exe_ lotto program**](https://saliu.com/freeware/markov.exe) from Ion Saliu's freeware site.
-   The software is **32-bit**, therefore it runs in **32-bit and 64-bit Windows** at the _**Command Prompt**_. It doesn't run in GUI Windows as it requires command-line parameters.
-   Read first a help file for the _Markov chains_ lottery software: [_**Markov Lotto Program: Help, Instructions**_](https://saliu.com/freeware/markov-lotto-help.html).

![Markov-chains software algorithms should consider pair of lotto numbers instead of followers.](https://saliu.com/HLINE.gif)

By the way —  
_**CoolRevGui**_ was written for Windows **NT**, an older platform. It works great with files under 32K. It may work fine with files over 32K, depending on the OS and its components. In my case, and other cases, it does not work properly with very large text files.

The _**GUI**_ versions change their objects and components (e.g. _dialog boxes_) quite often — thusly creating incompatibility. On the other hand, my 32-bit _**command prompt**_ lottery software has worked flawlessly with all 32-bit and 64-bit versions of Windows, from _Windows 95_ to _Win 10_. Way to go, axiomatic one! You deserve a **perfect 10**, Parpaluck, as in _Windows 10, 2015_ and thereafter!

![Markov chains lottery software runs faster and better at Windows command prompt instead of GUI.](https://saliu.com/HLINE.gif)

## <u>Resources in Theory of Probability, Mathematics, Statistics, Combinatorics</u>

-   [**Theory of Probability**](https://saliu.com/theory-of-probability.html): _**Best introduction, formulae, algorithms, software**_.
-   _**Download**_ **BirthdayParadox, Collisions, PermuteCombine, Markov**, _**scientific, probability, mathematical**_ [software](https://saliu.com/infodown.html).
-   [_**Fundamental Formula of Gambling**_](https://saliu.com/Saliu2.htm).
-   [Mathematics of _**Fundamental Formula of Gambling**_](https://saliu.com/formula.htm).
-   [_**The Best Casino Gambling Systems: Blackjack, Roulette, Limited Martingale Betting, Progressions**_](https://saliu.com/occult-science-gambling.html).
-   [_**Software, Formulae Calculate Lotto Odds, Hypergeometric Distribution Probability**_](https://saliu.com/oddslotto.html) (_exactly_ and _at least_).
-   [_**Online Probability, Odds Calculator**_](https://saliu.com/online_odds.html), _Including Any Lotto, Lottery_.
-   [_**Software, Formulae to Calculate Lotto Odds, Hypergeometric Distribution Probability**_](https://saliu.com/bbs/messages/266.html).
-   [_**Standard Deviation, Gauss, Normal, Binomial, Distribution**_](https://saliu.com/formula.html)  
    Calculate: Median, degree of certainty, standard deviation, binomial, hypergeometric, average, sums, probabilities, odds.
-   [_**Lotto**_ **<u>Wonder Grid</u>**, _**Super Loto Strategy, System**_.](https://saliu.com/bbs/messages/grid.html)
-   [_**Randomness, Degree of Randomness, Degrees of Certainty**_.](https://saliu.com/bbs/messages/683.html)
-   Software presentation:
-   [_**CoolRevGui**_: _Definitive File Reverser, Shuffler, Text Viewer Software_](https://saliu.com/programming.html).
-   [_**UPDOWN**_: _Reverse Order in Lottery Results, Text Files_](https://saliu.com/bbs/messages/539.html).

![The first real lotto software to apply Markov Chains to lottery games for 6 winning numbers.](https://saliu.com/HLINE.gif)

**[Home](https://saliu.com/index.htm) | [Search](https://saliu.com/Search.htm) | [New Writings](https://saliu.com/bbs/index.html) | [Fundamental Formula](https://saliu.com/formula.htm) | [Odds, Generator](https://saliu.com/calculator_generator.html) | [Contents](https://saliu.com/content/index.html) | [Forums](https://forums.saliu.com/) | [Sitemap](https://saliu.com/sitemap/index.html)**

![Read an article on gambling, mathematics, Markov chains for lottery, algorithms, programs.](https://saliu.com/HLINE.gif)
