using Test
using SaliuSystem0009
using SaliuSystem0009.DataProcessor
using SaliuSystem0009.Strategy
using SaliuSystem0009.Evaluator
using SaliuSystem0009.Generator

@testset "Integration tests" begin
    # 1. Prepare dummy data file
    dummy_data_path = "integration_test_data.csv"
    open(dummy_data_path, "w") do f
        write(f, "2023-01-01,1,2,3,4,5,6\n")
        write(f, "2023-01-02,7,8,9,10,11,12\n")
        write(f, "2023-01-03,1,2,3,4,5,7\n")
        write(f, "2023-01-04,10,11,12,13,14,16\n")
    end

    # 2. Read and validate data
    drawings = parse_data_file(dummy_data_path)
    validated_drawings = validate_and_clean_data(drawings)
    @test length(validated_drawings) == 4

    # 3. Create a strategy (e.g., numbers between 1 and 10)
    strategy = create_strategy("IntegrationTestStrategy", [Filter("NumberRange", 1, 10)])

    # 4. Evaluate the strategy
    hits = check_strategy(strategy, validated_drawings)
    @test hits == 2 # Drawings from 2023-01-01 and 2023-01-03 should hit

    # 5. Generate and filter combinations
    all_combinations = generate_combinations(strategy) # This will generate all 6/49 combinations
    filtered_combinations = filter_combinations(all_combinations, strategy)
    
    # Since the strategy is NumberRange(1,10), only combinations with numbers <= 10 should remain
    # This test might be slow due to generating all combinations first
    # A more targeted test would be to provide a small set of combinations
    @test all(all(n -> 1 <= n <= 10, c) for c in filtered_combinations)

    # 6. Perform LIE elimination (example)
    eliminated_count = lie_elimination(strategy, validated_drawings)
    @test eliminated_count == 2 # Drawings from 2023-01-01 and 2023-01-03 should be eliminated

    # Clean up dummy data file
    rm(dummy_data_path)
end