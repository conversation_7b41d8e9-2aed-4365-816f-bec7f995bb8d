---
created: 2025-07-24T22:10:02 (UTC +08:00)
tags: [horse racing,horse,race,software,best,programs,trifectas,horse numbers,post positions,]
source: https://saliu.com/ultimate-horse-software.html
author: 
---

# Horse Racing Ultimate Software for Trifecta Betting

> ## Excerpt
> Ultimate Trifecta is the best software for any horse races, where the horses have numbers by post positions, from 1 to 8, 9, 10, 12, 20.

---
## <u><i>Ultimate Software</i> for Horse Racing Trifectas, OTW Wagering</u>

## By <PERSON>, ★ _Founder of Equestrian Mathematics, Trifectas Programming Science_

![Ultimate Lottery 3 is the only lotto software program to win pick-3 daily.](https://saliu.com/HLINE.gif)

![The trifecta horse racing software starts here with a comprehensive presentation.](https://saliu.com/HLINE.gif)

### 0\. [Introductory Notes to the _Ultimate Horse Racing Software_ for _Trifectas (Triactors)_; Download, Install, Run](https://saliu.com/ultimate-horse-software.html#download)  
I. [Main Menu: Data Files, Reports, Strategies, Trifecta Generators](https://saliu.com/ultimate-horse-software.html#main)  
II. [Menu #2: <PERSON><PERSON>, <PERSON>, Horse-Number Decades, Last Digits, Wheels](https://saliu.com/ultimate-horse-software.html#book-2)  
III. [Menu #3: Generate Trifectas, Pairings (_Exactas_), Under/Over Strategies, _Boxed_ Trifectas](https://saliu.com/ultimate-horse-software.html#menu-3)  
IV. [Menu #4: Specialty Programs for Probability, Odds, Combinatorics, Lexicographic Order, Wheeling](https://saliu.com/ultimate-horse-software.html#book-4)

![Page instructs users how to download the horse race trifectas application, install, run.](https://saliu.com/HLINE.gif)

## <u>0. Introductory Notes to the <i>Ultimate Horse Racing Software</i> for Trifectas (Triactors); Download, Install, Run</u>

<big><b>ULTIMATE HORSE RACING</b></big> ~ version **2.0** ~ April 2017 ~ _software category 5.3_.-   This package is definitely the ultimate, never-to-be-duplicated software for horse racing where the _trifecta_ wager is available (i.e. the _top 3 finishers_ in a race). Specific data files of _past races_ or _past winning numbers_ make possible for the programs in this grand application to work at any horse race track in the world, live or at off-track wagering (OTW).
-   Downloading this collection of high-power programs requires the special _**Ultimate Software**_ membership. It is available only to members who paid for the _**Permanent Software Download**_ subscription. Click on the top banner to learn the terms, conditions, and availability.
-   Your membership page has clear instructions on downloading, installing, and running the software. All _**Ultimate Software**_ applications are available for download from your membership page. In addition, you can download a special software utility to work with data files efficiently.

The presentation of the **four menus** and their applications and functions is next. The programs themselves have their own menus. The menus in all my programs are self-describing, but there are also specialized pages with more detailed information.

## <u>I. Main Menu: Data Files, Reports, Strategies, Trifecta Generators</u>

![Screenshot of trifecta, exacta book #1 of the best winning horse-racing ultimate software.](https://saliu.com/images/ultimate-horses-software-30.gif)

This menu comprises the most important programs and functions of the _**Ultimate Horse Racing Trifecta Software**_ bundle. There is a _sine qua non_ step: _Create the **data file**_ of _past races_ or _past winning horse numbers_. You already know and fulfilled this step as a user of [_**BrightH3: High-Powered Trifecta Software for Horse-Race Betting**_](https://saliu.com/horseracing-software.html).

There is also a visual tutorial, with plenty of screenshots, applicable to all my software for lottery and horse racing:-   [_**Lottery Software Visual Manual**_](https://saliu.com/forum/lotto-book.html) (the fundamental steps apply also to trifecta software).

### _T_: Tutorial Online

It takes you to this Web page via your default Web browser. The online information is always up to date, as it is much prompter to edit a Web page than rewriting a manual bundled with the software.

### _E_: MDIEditor Lotto

Starts the separate GUI application **MDIEditor And Lotto WE** (for 64-bit Windows). The 32-bit OS version can be run from menu #3.

### Functions _I_, _E_: Edit Data Files

The _history file_ or _data file_ is always saved in text (ASCII) format and consists of the 3-horse numbers recorded in previous races as winners. You can create the file simply by typing the winning numbers race by race - one trifecta result per line.

-   The data file must have exactly 3 horse numbers per line. The numbers must be separated by one or more blank spaces. You can also use commas _**,**_ as the field separators. The universal field separator should be the _blank space_.
-   The final step is to _format_ the race results (the lines in your horse data files).

Here is an example of the contents of my file for the _Kentucky Derby_ horse race after formatting (_D-Ke20.CLN_, included):

```
<span size="5" face="Courier New" color="#c5b358"> 16   4   3 (race #1, or the most recent)
 19   6   5 (2nd recent race)
 16  19  13 (3rd recent race)
  4   2  10
  8  16   2
</span>
```

… all the way down to the last line in the file, representing the oldest race result (winning 3-horse numbers).

I created a specialized piece of software to maximize the efficiency of working with data files: **LotteryData**. Please visit the following link, as it also deals with the important topics of lottery _strategies_ and the _winning reports_:

-   [_**Software to Manage Lottery Data Files**_](https://forums.saliu.com/lottery-strategies-start.html#software) (horse racing too).

### _S_: Sort Data Files

The program formats nicely the pick 3, pick-4 and _horse racing_ files as in the sample above. The lines are **not** sorted in ascending order as the **position** counts in horse races.

This task can be also performed specifically for _Horse Racing Trifecta_ data files in _**Super Utilities**_, option _T = Sort or Add-up Data Files_. I always use the **LotteryData** utility mentioned above. For additional useful information, read:  

-   [_**<u>MDIEditor Lotto WE</u>: Lottery Software Manual, Book, ebook, Help**_](https://saliu.com/MDI-lotto-guide.html)
-   [**Help** on _**Software for Lottery, Lotto, Gambling: Download, Install, Run, Maintain Results Files**_](https://saliu.com/Help.htm)
-   [_**State Lottery, Lotto Drawings, Results, Past Winning Numbers: Online, over Internet**_](https://saliu.com/bbs/messages/920.html).

After creating, updating, and formatting the data files, one more important operation is required: **concatenation** of the _real_ data file with a _simulated_ data file.

The concatenation is done in _**Super Utilities**_, option _M = Make/Break/Position_, then _Make_, then option _1_ or _2_: _Make DH3 from DATA-H3 and SIM-H3_. _DH3_ is the final data file the software needs to generate reports, strategies, combinations (trifectas), etc.

-   This version of the _**Ultimate Horse Software**_ requires a _Dh3_ data file of at least _130000_ (_one hundred thirty-thousand_) races (lines of 3 winning horse-numbers).

Here is the best procedure for creating the _SIM-H3_ and _DH3_ files to meet the size requirement of 130000 (one hundred thirty-thousand) races (lines). Simply run _**Super Utilities**_ (**SoftwarePickH3**), option _S = Files (SIM-H3, count lines)_. Generate 130000 straight trifecta sets. You'll have a SIM-H3 file in seconds.

\*\* It is of the essence to use only **randomized** SIM files. Do not use, under any circumstances, SIM files in lexicographical order! Otherwise, the winning reports (W, MD) will turn into big headaches! Some filters will be so unusually high that several columns will run into one another. The _Check Strategy_ functions will NOT run any longer \*\*

### _W_: Winning Reports (WH3, MDH3 Files)

The creation of the winning reports, is of paramount importance. Press _W_ to generate the reports.

-   **S = Super** reporting (_WH3.x_ and _MDH3.x_): a DH3 file against itself (like in the lotto software)
-   Type 200 (or more) for the length of each report. Type _DH3_ for the name of the data file, and the default for _total races to analyze_.
-   Type or accept the defaults _WH3.1_ to _WH3.6_ and _MDH3.1_ to _MDH3.6_ for the report names.
    
    The 6 + 6 W/MD report files will show a number of parameters or _**FILTERS**_. Based on the reports, you feed the trifecta generating program (**L** or **R**, main menu) with the filters. The process of setting filter values is known as _**strategy selection**_.
    
    For more information, read:  
    
-   [_**<u>MDIEditor Lotto WE</u>: Lottery Software Manual, Book, ebook, Help**_](https://saliu.com/MDI-lotto-guide.html)
-   [_**Filtering, Filters in Lottery Software, Lotto Software**_](https://saliu.com/filters.html)
-   [_**Lotto, Lottery Software Tutorial**_](https://saliu.com/bbs/messages/818.html) - _"My kingdom for a good tutorial!"_
-   [_**Software to Create Reports for Lottery Strategies**_](https://forums.saliu.com/lottery-strategies-start.html).
-   [_**The Best Ever Lottery Strategy, Lotto Strategies**_](https://software.saliu.com/lottery-strategy-lotto-strategies.html) — a must-read ebook on strategies.

### _O_: Sort Filter Reports by Column

The program sorts the _WH3_, _MDH3_, _GRH3_, _DEH3_, _FRH3_, _SKH3_ reports by column, helping the user see more easily the filters — e.g. filters of _wacky_ values (very low, or extremely high).  
The sorting is done by type of winning reports. The program also offers the correct choices (correct names) for filters (columns) to sort on.

For more information, read:  

-   [_**Filtering, Filters in Lottery Software, Horse Races Software**_](https://saliu.com/filters.html)
-   [_**Cross-Reference Lottery, Horseracing Strategy Files**_](https://saliu.com/cross-lines.html).

### _C_: Check Strategies (Filter Settings)

The function analyzes the 6 + 6 W/MD reports to establish any type of strategy, between _minimum_ and _maximum_ values. The strategy report will show how many times a particular horse-racing strategy hit in past races. In other words, we check how a collection of filter settings would have fared in the past.

The program also creates the strategy files in the correct format (error-free). The strategy files are named _STH3.000_ (default). You need to remember the ST file names! It is a very good idea to create a simple text file where you record various horse racing strategies: ST names and what the strategies are based on.

\*\* Potential errors when checking for strategies.  
#1: Do NOT use _SIM_ files in _lexicographical order_. Always shuffle all your _SIM_ files.  
#2: Do NOT mix different race formats in your data files; that includes the file with your real races and your _SIM_ulated files.  
#3: Sometimes some filters can get way, way out of range. The value can be wider than what I planned as the maximum length for that particular filter. I wanted the reports to be as readable as possible.

If #3 happens, the strategy checking functions will trigger errors. You can fix the error by opening your winning reports one by one in the text editor. You will notice that two neighboring columns are no longer separated by at least one blank space.

The culprit is the number in the column which is no longer aligned with numbers above and below. You need delete one (very rarely more than one) character at the end of that lengthy number. Make sure that there is one blank space between the numbers in the two columns and that they are properly aligned in the respective columns. Here is a visual example:

1234    23  
12345123 = strategy-checking error

Corrected W/MD file:  
1234   23  
1234 123

Repeat the procedure with all your trifecta winning reports, as necessary. \*\*

### _H_: Strategy Hits in the Past

The program generates straight trifectas for situations when a particular strategy (as in the _STH3\*.\*_ files) hit in the past races. The program needs an input file created by **StrategyH3** (the previous function). The input file consists of the race IDs for the hit situations. Otherwise, the user will manually input the filter settings and the races IDs (the ID is the line # when the strategy hit).

### _L_: Lexicographic Straight Trifectas  
Option _R_: Randomized Straight Trifecta Sets

This is the ultimate goal of LotWon software: Generate winning _combinations_. We keep records of past races (maintain data files), then analyze the data files and generate the winning reports. Then, we analyze the W reports and select filter values for our horse racing strategies. We finally apply the strategies to the combination (set) generators.

Each trifecta generating program has several functions of its own.

\* _L_ - Program name: **LexicoH3** – it generates horse racing trifecta straight sets in _**lexicographical order**_. That is, the program starts the generation at lexicographic index #1 (_1, 2, 3_) and ends at the last index in the specific race (e.g. _20 19 18_ in a Kentucky Derby 20-horse race).  
Functions in this program:

**N = Normal Lexicographic - NO ranges - NO favorites**

-   generates every trifecta in the 3-horse race, from lexicographical index #1 to the last index.

**1 = 1 favorite number - NO ranges**

-   generates every trifecta, from lexicographical index #1 to the last index; also, each and every combination will contain one _favorite number_ chosen by the software user.

**2 = 2 favorite numbers - NO ranges**

-   generates every trifecta, from lexicographical index #1 to the last index; also, each and every trifecta will contain _two favorite numbers_ chosen by the software user.

**3 = 3 favorite numbers - NO ranges**

-   generates every trifecta, from lexicographical index #1 to the last index; also, each and every trifecta will contain _three favorite numbers_ chosen by the bettor.

**R = Combinations between positional RANGES**

-   generates every horse trifecta by positional _ranges_; e.g. numbers in 1st position between 1 and 3; numbers in 2nd position between 5 and 9; numbers in 3rd position between 16 and 20.

**P = PURGE an output file of combinations**

-   takes an output trifecta file previously generated and eliminates additional sets by applying further filtering.

\* _R_ - Program name: **CombineH3** – it generates horse racing trifecta straight sets in _**randomized manner**_. That is, the program starts and ends the generation anywhere in the trifecta matrix, instead of lexicographically.  
Functions in this program:

**0 = NO favorite numbers, NO shuffle**

-   generates randomized trifectas, without any favorite numbers, or clusters (shuffled combinations).

**1 = 1 favorite number - NO shuffle**

-   generates randomized trifecta straight sets; also, each and every random set will contain _one favorite number_ chosen by the software user.

**2 = 2 favorite numbers - NO shuffle**

-   generates randomized horse racing trifecta combinations; also, each and every random trifecta will contain _two favorite numbers_ chosen by the horse racing bettor.

**3 = 3 favorite numbers - NO shuffle**

-   generates randomized horse racing trifecta sets; also, each and every random combination will contain _three favorite numbers_ chosen by the user.

**S = SHUFFLE numbers (all horse numbers in the race format)**

-   The function generates, for example, 10 horse numbers in one _cluster_ or group, 3 numbers per each line; i.e. a horse-racing game will have clusters of 4 lines (straight sets), 3 numbers per line; the last line will repeat 2 numbers from previous lines.
-   If you select 9 as _number of horses per race_, this option will generate perfect 3x3 _Sudoku_ squares. You can generate millions of 3x3 _Sudoku_ matrices, save them, then use them to manually fill _Sudoku_ puzzles. Be forewarned that about one trillion distinct 3X3 _Sudoku_ squares are possible!
-   Since a staggeringly huge amount of distinct (unique) clusters is possible, we must employ filters. The filters will restrict drastically the number of clusters generated. Patience is needed as we deal with possibly trillions of clusters.

\* Both programs above have two super functions before the combination generation starts:

-   **S = Super** generating
-   **F = Fixed** generating.

\* Both horse racing programs above have two more functions that also eliminate unwanted 3-number sets (triactors):  

-   inner filters (they eliminate around 95% of all straight sets - enable it rarely);
-   LIE elimination: You noticed that, more often than not, your horse racing output files do NOT have winning numbers - not 3 straight winners, not 3 boxed winners, not even 2-number winners. You can open the _LIE_ file and generate trifectas that do NOT contain groups of numbers existent in the _LIE_ file.

For more information, read:  

-   [_**Lottery <u>Strategy in Reverse or LIE Elimination</u>: Not-to-Win Leads to Not-to-Lose or WIN**_](https://saliu.com/reverse-strategy.html).
-   [_**Professors Play All Lotto Numbers and Win the Jackpot**_](https://saliu.com/all-lotto-numbers.html)

### _U_: Super Utilities

This piece of software bundles several utilities for horse-race wagering. Each sub-program in turn has several features of its own.

**S = Files (SIM-H3, Count Lines)**

-   Simulate a SIM-H3 file
-   Count lines in text files.

**D = Duplicates: Strip and Wheel**

-   The function retrieves the input data, finds duplicate lines, and eliminates them.

**F = Frequency Reporting by Digit**

-   The sub-program counts how many times each horse number came out in the specified range of past races. Then, it plots a _skip chart_ - number of races between hits. Also, the function saves to files the _most frequent pairings_ (_BESTH3_) and the _least frequent pairings_ (_WORSTH3_).

**W = Check for Winners**

-   The function checks for groups of 3 winning horse numbers in two manners:  
    1) an OUTPUT file against a data file with real draws  
    2) POOLS of numbers against a data file with real horse races results (past winning trifectas).  
    This function is the recommend method to check if your output files had any winners in past races, especially the races when you played the trifectas.

**T = Sort or Add-up Data Files**

-   Nicely formats your horse racing trifecta files horizontally; plus, sorts in ascending order vertically, by multiple columns. The recommended method is the one I use. I referred to it at the beginning of this section: the ****LotteryData**** utility.
-   Add-up the numbers in each trifecta in a data file to _sum-totals_. The recommended method is performed by the standalone application **Sums** presented in the next section (menu #2).

**G = Generate Combinations, Favorites, Least-Number-Groups**

-   The function generates trifectas lexicographically. You can play favorite numbers: 1, 2, 3; or NO favorites. You can also eliminate (least) singles and pairings. This function does not require a data file of past races.

**M = Make/Break/Position (DH3, Positional Ranges, Break Long Lines to Trifecta Straight Sets)**

-   Make DH3 without LEASTH3
-   Make DH3 with LEASTH3
-   Make DH3 with LEAST & BESTH3
-   _**Break 3+ horse numbers to 3-number lines**_
-   _**Generate trifectas by positional ranges (positional limits)**_.

**1 = Singles Rundown**

-   Calculate the frequency of every _single_ in a format (e.g. 10 horse numbers);
-   sort the singles by frequency in descending order;
-   create a _least single_ file (singles that have not come out in the specified range of races).

**2 = Pairs Rundown**

-   Calculate the frequency of every _pair_ (_pairing_) in a horse racing format;
-   sort the pairs by frequency in descending order;
-   create a _least pairing_ file (pairs that have not come out in the specified range of races).

This important piece of _Horse Racing_ software is amply presented on its dedicated page:

-   [_**Lottery Software for Singles, Pairs, Triples, Quadruples, Quintuples, Sextuples**_](https://saliu.com/gambling-lottery-lotto/lotto-software.htm).

### The next three functions — _D, N, M_ — represent valuable new software, highly requested by members of the _Super Forums_.

### _D_: Dedicated LIE Elimination (_LieID_)

This program applies the **_LIE elimination_** feature introduced in the BRIGHTh3 lottery software, specifically the combination generators. The main function generates the reports showing the levels of the ID filters for both the **_pairings_** and **_number frequencies_**.

For ample information, you definitely want to read:

-   [_**Lottery Strategy in Reverse for Pairs, Number Frequency**_](https://saliu.com/lie-lottery-strategies-pairs.html).

### _N_: LIE StriNgs (_LieStringsH3_)

The program shows the horse races as strings of skips, high/low, odd/even, decades, last numbers — to be used in the _**LIE elimination**_ strategies. The string report files are to be used as INPUT to combination generating (**G**), with the filters being fed automatically. The _output_ files are then used as _LIE_ files in the combination generators (e.g. **LexicoH3**).

For ample information, you definitely want to read:

-   [_**Reversed Lottery Strategy for Lotto Decades, Last Digits, Odd Even, Low High, Skips**_](https://saliu.com/lie-lotto-strategies-decades.html).

### _M_: _Markov Chains_, Pairs, Followers

This complex piece of software deals with the famous _Markov chains_, random pairing, trendy number frequencies (.e.g. _hot_, _cold_ horse numbers).

-   1) first, the software creates reports for: Followers, Pairings, Number Frequencies.
-   2) the program generates lists of: Followers, Pairings, Frequencies — sorted in _descending order_, from _Hot_ to _Cold_ based on frequency.
-   3) the application generates trifectas based on several methods in this specific field.
    
    For ample information, you definitely want to read:
    
-   [_**Theory of Markov Chains in Lottery, Lotto, Followers, Pairs, Software**_](https://saliu.com/markov-chains-lottery.html)
-   [_**Markov Chains: Lottery, Lotto, Software, Algorithms, Programming**_](https://saliu.com/Markov_Chains.html).

## <u>II. Menu #2: Skips, Delta, Lottery Decades, Last Digits, Wheels</u>

![Special additions to the best ever horse racing software include last digits and 2 deltas.](https://saliu.com/images/ultimate-horses-software-31.gif)

### _D_: Deltas

This is another important addition to the grand collection _**Ultimate Horseracing Software**_. The delta application shows the horse races as strings of **deltas** or _differences_ between adjacent numbers. The program creates first the delta report for a data file (results). The program also generates trifectas based on user-input deltas.

For ample information, you definitely want to read:

-   [_**Theory, Analysis of <u>Deltas</u> in Lottery Software, Strategy, Systems**_](https://saliu.com/delta-lotto-software.html)

### _S_: Skips, Decades, Last Digits, Frequencies

The program shows the horse races as strings of skips, high/low, odd/even, increase/decrease from previous draw. The program also generates reports for _horse-number decades_, _last digits_, and a report of _frequencies_ from 3 groups: _hot_, _mild_, _cold_. You can use the skips, high/low, odd/even, decades, last digits, frequencies as _filters_ in the trifecta (triactor) generators or the **purge** function. The software can check if or when a strategy hit in the past. The _Strategy Hits_ function reports how many sets a particular horse-racing strategy would have generated in winning situations.

For amplified information, you definitely want to read:

-   [**Reversed** _**Lottery Strategy for Lotto Decades, Last Digits, Odd Even, Low High, Skips**_](https://saliu.com/lie-lotto-strategies-decades.html)
-   [_**Lottery Decades: Software, Reports, Analysis, Strategies**_](https://saliu.com/decades.html)
-   [_**Lottery Strategy, Systems Based on Number Frequency**_](https://saliu.com/frequency-lottery.html)

### _Q_: Skips & Frequency Groups-Only

The program shows the horse races as strings of **skips** and **frequencies**. Both parameters are divided into 3 groups of numbers: _hot_, _warm_, _cold_. You can use the _skips_ and _frequency groups_ as filters, or generate trifectas for the _**LIE Elimination**_ strategy.

This application is similar to function _S = Skips, Decades, Last Digits, Frequencies_. The _skips_, however, are divided in 3 groups, rather than dealing with them number by number. Please read the pages referred to above.

### _H_: Wheels from Files

This program takes an input file of trifectas and converts the sets to _k of 3_ as lottery wheel format - from _1 of 3_ to _3 of 3_; also, a number of combinations can be input in a random manner, without any wheeling.

The function is useful if you want to reduce the number of trifectas in an output file previously generated. For example, you generated hundreds of combinations in **CombineH3** (function _R = Random_ on main menu) with light filtering; you want to play only a handful of triactor combinations that have no more than k numbers in common (as in lotto wheeling); evidently, you settle for a lower-tier prize, if available.

Read more details:  

-   [_**Lottery Wheels for Lotto Games Drawing 5, 6, or 7 Numbers: Balanced and Randomized**_](https://saliu.com/lotto_wheels.html)
-   [_**Lottery Wheeling Software, Winning Report Generator**_](https://saliu.com/bbs/messages/wheel.html).

### _F_: Rank Horse Numbers by Frequency

This program generates frequency reports two ways: 1.- _Regardless of position_; 2.- _**Position by position**_. The horse numbers are ranked by frequency in descending order, from _hot_, to _mild_, to _cold_.

This multi-game application is amply presented on its dedicated page and in other specialty materials:

-   [_**Lottery Strategy, Systems Based on Number Frequency**_](https://saliu.com/frequency-lottery.html).

### _K_: Create Lottery, Horse Racing _Skip_ Systems

This program creates lottery and horse-racing systems based on two or three consecutive _skips_; the most recent skips make it into a particular system.

This multi-game application is amply presented on its dedicated page and in other specialty materials:

-   [**<u>Lottery Skip Systems</u>**](https://saliu.com/skip-strategy.html): _**Lotto, Powerball, Mega Millions, Euromillions, Horse Racing**_.

### _M_: Sum-up Horse Data Files

The application calculates the amount of trifectas that add-up to a _sum-total_. It also calculates the sums of each race in data files, plus _root sum_, and _standard deviation_. You can generate such combinations (triactors) and save them. The program creates summary reports for the game: Every sum-total and its amount of trifectas, plus percentages... and much more.

See:

-   [_**Basics of a Lottery Strategy Based on: Sums (Sum-Totals); Odd or Even; Low or High Numbers**_](https://saliu.com/strategy.html)
-   [_**Lottery, Lotto Sums, Sum-Totals**_](https://saliu.com/forum/lottery-sums.html)
-   [_**Lottery Software to Calculate Sum-Totals, Odd-Even, Low-High Patterns**_](https://saliu.com/bbs/messages/626.html).

### _V_: Verify Data Files

This program _parses_ the horse racing data files to make sure they comply with the format required by this much-envied type of software. It is highly recommended to check your data files periodically to make sure they are error-free — because _errare humanum est_.

For more information, read:

-   [_**Software to Correct Errors in Lottery, Horse-Racing Results Files**_](https://saliu.com/bbs/messages/2.html).

### _C_: Check Winners

Check for winning numbers in output files against real race-results files. The trifectas to play were saved first to output text files by the _arrangements_ generators.

This task can be also performed specifically for horse racing trifecta data files in _**Super Utilities**_ (main menu), option _W = Check for Winners_ = the best method.

### _T_: Cross-Checking Lottery Strategies

The app writes to disk the lines of specified indexes in a file, usually a strategy file created by the function _C = Check Strategies_ (main menu). You created the _W\*.\*_ files in the Command Prompt software. You also generated the statistical reports in **MDIEditor And Lotto WE**. You then created the strategy file for the _Stats_ function in **MDIEditor Lotto**. You want to see the same line numbers in _WS\*.\*_ files for a more comprehensive horse racing strategy; i.e. possibilities to reduce even more the output to play.

Read more:

-   [_**Cross-Reference Lottery, Horse Racing Strategy Files**_](https://saliu.com/cross-lines.html).

### _U_: Text File Reverser

-   The program reverses the order in text files: the bottom becomes the top. Useful in arranging the data files in the order required by LotWon software, including for horse races.
-   Uncooperative sites publish histories (drawings, results) in an unnatural order: The most recent race goes to the bottom, instead of the TOP. LotWon software requires starting with the most recent draw, and going all the way down to the oldest race in that game format (bottom of file).
    
    For more detailed information, do read:
    
-   [_**Software to Reverse Order in Lottery, Horse Results Files**_](https://saliu.com/bbs/messages/539.html)
-   [_**Definitive File Reverser, Shuffler, Text Viewer Software**_](https://saliu.com/programming.html).

### _G_: User's Groups, O/E, L/H, Sums

This is a big trifecta program that works with groups of horse numbers: _odd, even, low, high, frequency groups, sums_.

The application has many functions and its own Web page.

Please read the dedicated presentation:

-   [_**Lotto Software for Groups of Numbers/Digits: Odd, Even, Low, High, Sums, Frequency**_](https://saliu.com/lotto-groups.html)

## <u>III. Menu #3: Generate Sets, Pairings, Under/Over Strategies, Boxed Trifectas</u>

![Ultimate Horse Races Software has game specific programs, especially boxed trifectas or triactors.](https://saliu.com/images/ultimate-horses-software-32.gif)

\* Axiomatic one, some of the functions below represent **standalone** programs. Other functions belong to older programs, but the names of the functions were less obvious to some software users. For example, several users asked me how to generate horse racing trifectas from groups or pools of horse numbers. There are no programs with relevant names. Instead, these functions are well represented in large programs that provide a wide variety of functions. In this particular example, the trifecta generators from pools or groups of numbers belong to the _**Super Utility**_ software.

### _N_: 3-Number Sets from Pools of Horse Numbers

Generate horse racing trifecta sets from pools or groups of numbers.

Program name: **SoftwarePickH3** (_**Super Utilities**_ on main menu), option _M: Make/Break/Position_.

The groups of horse numbers can be listed in files, in one line or multiple lines. For example, **SkipSystem** created for you a pool of numbers. The file in text format consists of one line of 7 numbers. You want to generate trifectas from those 7 numbers. Total of straight trifectas from 7 numbers: 7 \* 6 \* 5 = 210. In **SoftwarePickH3**, you select option _M: Make/Break/Position_. Then, option _Break_, then option _2 = All 3 Numbers Equally_.

The same function can also generate trifectas from multiple lines of 3+ numbers each. For example, you had 10 lines, for each number of your horse race; each line has 6 other numbers, as the best pairs of each of the 10 numbers.

You can apply in this function a powerful filter: the _Least_ feature. Actually, you will eliminate all pairings in a special file _WORSTH3_ created by the same _**Super Utility**_ (option _F: Frequency_). It is recommended now, in strong terms, to use _WORSTH3_ instead of _LEASTH3_.

For in-depth information, study:

-   [**<u>Lottery Skip Systems</u>**](https://saliu.com/skip-strategy.html): _**Lotto, Powerball, Mega Millions, Euromillions, Horse Races**_
-   [_**Analysis of Best Ranges for Lotto Number Frequency, Lottery Pairs, Pairings**_](https://saliu.com/lottery-lotto-pairs.html)
-   [_**Lottery**_ **Wonder Grid** _**Revisited: New Lotto Pairing Research, Software**_](https://saliu.com/bbs/messages/grid.html).

### _P_: 3-Number Sets by Positions

Generate horse racing trifectas from 3 lines of numbers representing the 3 positions in a trifecta.

Program name: **SoftwarePickH3**, option _M: Make/Break/Position_.

You can generate straight trifectas based on positions or _positional ranges_ (_positional limits_). If you run the statistical functions of my software, you will see that the numbers are strongly biased regarding the position. You can read at SALIU.COM a lot about ranges or positional ranges. You will see living proof that the horse numbers follow the _**Fundamental Formula of Gambling (FFG)**_. Each position has numbers based on the _FFG median_.

You can apply in this function a powerful filter: the _Least_ feature. Actually, you will eliminate all pairings in a special file _WORSTH3_ created by the same **SoftwarePickH3** (option _F: Frequency_).

For best information, read:

-   [_**Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies, Positional Ranges**_](https://saliu.com/Newsgroups.htm)
-   [**<u>Lottery Skip Systems</u>**](https://saliu.com/skip-strategy.html): _**Lotto, Powerball, Mega Millions, Euromillions, Horse Races**_
-   [_**Analysis of Best Ranges for Lotto Number Frequency, Lottery Pairs, Pairings**_](https://saliu.com/lottery-lotto-pairs.html).

### _F_: 3-Number Combinations from 3 Frequency Groups

Generate trifectas based on frequency: _hot_ numbers, _mild_ numbers, and _cold_ numbers. Run first the _Frequency Reporting_ module, and then the straight sets generator (_lexicographic_ or _random_).

Program name: **SkipDecaFreqH3** (function _D: Deltas_, menu #2), options: _L = Combinations, Lexicographic_; _R = Random Combinations_. Then, select the screen pertaining to the filters based on the 3 horse-number frequency groups.

Read further:

-   [_**Lottery Strategy, Systems Based on Number Frequency**_](https://saliu.com/frequency-lottery.html).

### _D_: 3-Number Sets by Decades

Generate horse racing trifecta sets decade by decade. The following 5 "decades" are possible in this version of the software: _1-4, 5-8, 9-12, 13-16, 17-20_.

Program name: **SkipDecaFreqH3**, options: _L = Combinations_, _Lexico_; _R = Random Combinations_. Then, select the screen pertaining to the filters based on the horse-number _decades_.

Please read much more:

-   [_**Lotto Decades: Software, Reports, Analysis, Strategies**_](https://saliu.com/decades.html).

### _G_: Pairing Reports, Custom Grids, Straight Trifectas

The program plots the horse racing report of _horse-number-pairings_. It also builds a custom grid consisting of pairings chosen by the user. For example, the user can build a grid with pairing rank #8 and pairing rank #9 (instead of only #1 and #2 as in the _wonder grid_). You will notice that pairs #6+#7, or #7+#8, or #8+#9 come out with good frequency. You want to play them after a number of skips.

The _Custom Grid_ will generate trifectas to 2 output files similar to the output files in _Make/Break/Position_ in _**Super Utilities**_.

I noticed in my horse-racing real data file that pairing ranks #6 and #7 performed very well in generating hits for the very next race. Also, 2-way combinations of pair-ranks #1, #6, and #7 fared very well (from one race to the next).

The program also finds the pairings that have not come out within a range of races (function **F**, like the **W** reporting in the main menu). Then, function **G** generates trifectas for the missing pairings by setting the TWO filter.

Finally, the **C** function will check the 2 output files to see how they fared in the past (100 or 200, etc.) races.

This program can be useful also in conjunction with the _**LIE**_ (_**reversed**_) strategy feature present in all trifecta generators. The pairings do not lead to winning straight sets the very next race (draw), far more often than not.

Program name: **PairGridH3**.  
Replaces: _SkipPairH_ (still in the package — just type its name at the _**command prompt**_, after _e**X**iting_ the main menu).

For valuable information, read:

-   [_**Software News: Lottery, Horse Racing, Pairs Programs, LIE Strategy**_](https://saliu.com/software-news.html).
-   [_**Lotto, Lottery Strategy in Reverse: Not-to-Win Leads to Not-to-Lose or WIN**_](https://saliu.com/reverse-strategy.html).
-   [_**Lottery Pairs and Repeat Probability**_](https://saliu.com/forum/lottery-pairs.html).

### _U_: Under/Over Strategies

A special trifecta generator based on the _FFG median_. Two FFG medians are considered (for 10 horses):

-   **Under** = the latest one: from horse race #1 to #499;  
    
-   **Over** = the next one: from race #500 to end of file.
    
    For more details, read:
    
-   [_**Winning Pattern for Under/Over Strategies**_](https://saliu.com/almighty_number.html)

### _R_: Boxed & Random Trifectas

The program generates trifecta combinations either **boxed** or **random**. Usual filters can be applied, although they behave differently for the _boxed_ generation. That's so because our results (real races) are always in _straight trifectas_ format.

By the way, the following is the best method of record-keeping. Record only results from races with 9 or more horses, but do NOT write numbers greater than 10! If the results have numbers greater than 10, discard the race. Play only in races with 9 or more horses, as the payouts are much more favorable to wagering (bigger payouts, especially for longshot trifectas). _Exception: I play up to 20 horse numbers in big races, such as the Kentucky Derby. Open the **D-Ke20.CLN** file, which has all the results in history, up to year of grace 2015. I deleted, however, 2 races with numbers larger than 20._

Program name: **BoxRandomH3**.

For more details, read:

-   [_**Revealing Strategy, Systems for Gambling, Lottery, Horse Racing**_](https://saliu.com/generosity.html).

## <u>IV. Menu #4: Specialty Programs for Probability, Odds, Combinatorics, Lexicographical Order, Wheeling</u>

![Many specialty programs enhance the best-ever software for horse racing trifecta betting, wagering.](https://saliu.com/images/ultimate-horses-software-33.gif)

### _S_: _Super Formula_, Definitive Probability, Statistics, and Gambling Software

_**Super Formula**_ is the definitive software for statistics, probability, odds, gambling mathematics... and much more. The functions are grouped in 12 categories. Each software category has its own detailed sub-categories. This unique application grew from the request by many people to create software to automate the calculations in the _**Fundamental Formula of Gambling (FFG)**_. _**FFG**_ discovered the most fundamental elements of theory of probability and also the Universe: The relation between the _**degree of certainty (DC)**_, _**probability p**_, and _**number of trials N**_.

There is ample information of the dedicated page:

-   [_**Formula Software for Statistics, Mathematics, Probability, Gambling**_](https://saliu.com/formula.html).

### _D_: _Birthday Paradox_ Strategies

I had written an original essay touching a few issues of creating winning systems from the _Birthday Paradox_, or the _probability of repetition_ (_duplication_). Such systems would apply to games of chance such as lottery, horse racing, roulette... and more. There are lottery players and gamblers who now realize how important the probability of repetition is.

Can such mathematical knowledge be applied to gambling, especially lottery or horse-racing? I was skeptical when I first heard about it and was asked about it. The difficulty of achieving thorough understanding of this phenomenon was caused by a parameter I call _number of elements_. Indeed, the roulette numbers repeat. You can see them all the time, if the casinos turn on the electronic displays known as the marquees. But is there a rule, a mathematical formula that enables us to calculate the repeat probability?

I thought more deeply on this repetition fact. For example, I look at a sequence of 8 roulette numbers as an eight-element string. The degree of certainty is better than 50% that such string should contain one repetition (duplication). One of the eight numbers should be a repeat with a 50-50 chance. The same is true about horse races. In this case, the element is the index of the combination (or set) drawn.

I studied some real data: Lottery drawings and roulette spins. I was somehow surprised to discover that repetition occurs close to that cutoff point of the 50-50 chance! I should also point out that the strength of the analysis and system creation is stronger at the beginning of the game. For lottery and lotto, the beginning is clear: A game starts with the first race of a game format.

Program names: **Collisions, BirthdayParadox**.

For specialty information, study:

-   [_**Applications of**_ **Birthday Paradox**: _**Lottery, Lotto, Roulette**_](https://saliu.com/birthday-paradox.html)
-   [**Birthday Paradox**: _**Combinatorics, Probability of Duplication, Coincidences, Collisions, Repetition**_](https://saliu.com/birthday.html).

### _P_: Generate All Possible Types of Sets

This software generates ALL possible types of sets: _Exponents, permutations, arrangements, combinations_ - and _Powerball, Mega Millions, Euromillions combinations_. The software generates the sets in _lexicographical order_ or _randomly_. The sets can be _numerical_ or be composed of _words_ (_text_).

The _horse racing trifectas_ belong to the _arrangements_ category (the _order_ or position counts; the _arrangements_, like the _combinations_, cannot have _duplicate_ elements).

For specialty information, visit:

-   [_**Combinatorics: Permutations, Combinations, Factorial, Exponents Generate**_](https://saliu.com/permutations.html)
-   [_**Comprehensive Generating: Exponents, Permutations, Arrangements, Combinations, Powerball, Mega Millions, Euromillions, Horse Racing**_](https://saliu.com/forum/numbers-words.html).

### _R_: Shuffle or Randomize Elements

_Shuffle_ trifecta files (text files); then go to the line equal to the _probability median_ (_FFG = 50%_). The program can also shuffle numbers in a highly randomized manner. There is a plethora of randomization functions in this program!

For specialty information, read:

-   [_**Random Numbers: Algorithms, Shuffle, Randomize, Software**_](https://saliu.com/random-numbers.html)
-   [_**Greatly Improved Shuffle, Randomize**_](https://forums.saliu.com/shuffle-randomize-software.html).

### _L_: Software for Lexicographical Order

The program finds the _lexicographical order_ (_index_, or _rank_) of a given set and conversely finds the set for a specified index (rank, or numeral, or lexicographic order). Applicable to these set types: _Exponents, permutations, arrangements, combinations, Powerball (5+1), Euromillions (5+2) combinations_.

For specialized information, read:

-   [_**Lexicographical Order: Index, Rank, Algorithms, Combinations, Permutations**_](https://saliu.com/lexicographic.html)
-   [_**Lexicographical Order: Lotto, Powerball, Mega Millions, Euromillions**_](https://saliu.com/forum/lexicographical.html).

### _B_: Generate Combinations inside FFG Median Bell

This multi-game software generates sets in the _FFG median_ zone and inside the _bell (Gauss) curve_. The program can be used for: pick-3 4 lotteries, horse racing, lotto-5, -6, -7, Powerball, Mega Millions '5+1', Euromillions '5+2', roulette, sports betting, soccer pools.

For specialized information, read:

-   [_**Median Bell Random Number, Combination Generator**_](https://saliu.com/median_bell.html)
-   [_**Winning Combinations Come Predominantly from Inside the FFG Median Bell**_](https://saliu.com/random-picks.html).

### _F_: Lexicographically-Index File

This program takes a horse data file (results) and adds _indexes_ (_ranks_) to the corresponding trifectas in the file. The indexes are calculated based on the combination lexicographical order or index for that race format.

For in-depth coverage, read:

-   [_**Combination Lexicographical Order, Index of Lotto, Lottery Drawings Files**_](https://saliu.com/combination.html)
-   [_**Combinations Generator: Any Lotto, Powerball, Mega Millions, Euromillions, Horseracing, Two-In-One Lotto Games**_](https://saliu.com/combinations.html).

### _O_: Probability, Odds Calculator

The probability software calculates all the _**odds**_ of any many games, including Powerball, Mega Millions, Euromillions, Horse Racing games. For example, the odds for 10 horses in the race:

-   _to win_ probability (top 1) = 1 in 10
-   _exacta_ probability (top 2) = 1 in 90
-   _trifecta_ probability (top 3) = 1 in 720
-   _superfecta_ probability (top 4) = 1 in 5040
-   _quintofecta_ probability (top 5) = 1 in 30240
    
    Read all the details:
    
-   [_**Calculate Odds, Probability to Win Lottery, Lotto, Powerball, Mega Millions, Euromillions**_](https://saliu.com/oddslotto.html)
-   [_**Probability, Odds, Formulae, Algorithm, Software Calculator**_](https://saliu.com/bbs/messages/266.html).

### _V_: Universal Combination Generator

Lotto and horse-racing software generates combinations for absolutely any type of lottery game, plus horse racing _exactas_ (top 2 finishers), trifectas, _superfectas_ (top 4 finishers), etc. Specifically to this program: the combinations can be generated in _steps_. That is, the user has the choice to generate combinations with constant gaps or skips between them. For example, starting at the very top of a combination set (the lexicographical order #1), then step 90, the following sets generated will have lexicographic order #91,…, and so on, to the very last combination in the set (e.g. trifecta _20 19 18_).

Read all the details:

-   [_**Combinations Generator: Lotto, Powerball, Mega Millions, Euromillions, Horse Racing, Two-In-One Lotto Games**_](https://saliu.com/combinations.html).

### _G_: _Wonder-Grid_ Checking for Horse-Number Pairings

The program checks the past performance of the _GRIDH3_ files. The program starts a number of races back in the _DATA-H3_ file and creates 3 _GRIDH3_ files: for _10_, _20_, and _30_ races. Each range of analysis N creates its own report file (_ChkGridH3.\*_).

It can be used well in conjunction with the _LIE_ (_reversed_ lottery strategy) feature in the combination generators. The _wonder grid_ skips more races compared to the winning situations.

For more details, read:

-   [_**Lottery Wonder-Grid Revisited: New Lotto Pairing Research, Software**_](https://saliu.com/bbs/messages/grid.html)
-   [_**Lotto Wonder-grid: Lottery Reports and Software**_](https://saliu.com/bbs/messages/9.html).

### _K_: Wonder-Grid Pairs, Statistical Reports

This program creates all the statistical files for 3-race ranges - calculated by the _**FFG**_. The files have the extensions 1 to 3 in their names. The following files are created: _FREQH3_, _PAIRSH3, GRIDH3, BESTH3, WORSTH3, LEASTH3_. _GRIDH3_ has each number plus its 2 top pairs; _BESTH3_ = the top (N-1)/3 pairs; _WORSTH3_ has the worst (N-1)/2 pairs; _LEASTH3_ has the pairs with a frequency equal to 0. At the end, the program combines all _GRIDH3.\* BESTH3.\*, WORSTH3.\*, LEASTH3.\*_ to _GRIDH3, BESTH3, WORSTH3, LEASTH3_.

This program is useful in conjunction with the _LIE_ (_**reversed**_) strategy feature present in all horse output generators. The pairings do not lead immediately to winning trifectas more often than not.

For more details, read:

-   [_**Lottery Wonder-Grid Revisited: New Lotto Pairing Research, Software**_](https://saliu.com/bbs/messages/grid.html)
-   [_**Lotto, Lottery Software, Utilities**_](https://saliu.com/lottery-utility.html).

### _H_: Play-Last-N-Draws as a Lottery Wheel

The horse racing utilities check for winning numbers in files of real race results. A data file will be checked against itself as if playing the last N races before the current one. For example, check the wins when I play the last 12 draws in a 5/43 lotto game. The odds of _2 of 5_ are _1 in 12_ as calculated by the OddsCalc super program. How many _2 of 5_ hits (and other hits) will be recorded if playing in 100 future 5/43 lottery drawings?

View:

-   [_**Wheeling All Lotto Numbers Formula: Play Last N Lottery Draws**_](https://saliu.com/wheel.html).

### _U_: Old Horse Racing Utilities

This piece of horse-racing software was superseded by _**Super Utilities**_ (option _U_ in the main menu). Just nostalgia, I guess! _O tempora! O mores!_

Program name: **UtilH32**.

You might want to read:

-   [_**Lotto, Lottery Software, Utilities**_](https://saliu.com/lottery-utility.html).

[

## <u>Resources in <i>Horse Racing</i>: Theory, Software, Systems, Betting</u>

](https://saliu.com/content/horses.html)

![There are lots of applications in the Ultimate Software for horse racing tracks paying trifecta.](https://saliu.com/HLINE.gif)

**[Home](https://saliu.com/index.htm) | [Search](https://saliu.com/Search.htm) | [New Writings](https://saliu.com/bbs/index.html) | [Fundamental Formula](https://saliu.com/formula.htm) | [Odds, Generator](https://saliu.com/calculator_generator.html) | [Contents](https://saliu.com/content/index.html) | [Forums](https://forums.saliu.com/) | [Sitemap](https://saliu.com/sitemap/index.html)**

![Ultimate winning horses software was created by the Founder of horse-racing programming science.](https://saliu.com/HLINE.gif)
