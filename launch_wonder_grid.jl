#!/usr/bin/env julia

"""
Wonder Grid Lottery System Launcher
Provides multiple ways to start and use the Wonder Grid system
"""

using Dates

"""
Display system banner
"""
function display_banner()
    println("╔══════════════════════════════════════════════════════════════╗")
    println("║                    WONDER GRID LOTTERY SYSTEM                   ║")
    println("║                           Version 1.0                           ║")
    println("║                                                                  ║")
    println("║    Advanced lottery number generation using mathematical         ║")
    println("║    patterns, statistical analysis, and machine learning         ║")
    println("╚══════════════════════════════════════════════════════════════╝")
    println()
    println("🚀 System Features:")
    println("   • Wonder Grid mathematical strategy")
    println("   • LIE (Lottery Information Elimination) filtering")
    println("   • Advanced statistical backtesting")
    println("   • Performance optimization and concurrent processing")
    println("   • Comprehensive reporting and analysis")
    println("   • Multiple export formats (CSV, TXT, JSON)")
    println()
end

"""
Quick start launcher
"""
function quick_start()
    println("🚀 Quick Start Mode")
    println("=" ^ 30)
    
    include("wonder_grid_system.jl")
    
    # Initialize system with default configuration
    system = WonderGridSystem()
    
    # Load sample data
    println("📊 Loading sample historical data...")
    load_historical_data!(system, generate_sample_historical_data(150))
    
    # Execute workflow with popular key number
    println("🎯 Executing Wonder Grid workflow with key number 13...")
    result = execute_wonder_grid_workflow(system, 13)
    
    if result !== nothing
        println("\n🎉 Quick Start Complete!")
        println("📊 Generated $(length(result["combinations"])) combinations")
        println("⏱️  Total time: $(round(result["execution_time"], digits=2)) seconds")
        
        if haskey(result, "exported_files")
            println("📁 Files exported: $(length(result["exported_files"]))")
            for file in result["exported_files"]
                println("   • $file")
            end
        end
    end
end

"""
Interactive launcher
"""
function interactive_launcher()
    println("🎮 Interactive Mode")
    println("=" ^ 25)
    
    include("wonder_grid_system.jl")
    
    # Initialize system
    system = WonderGridSystem()
    
    # Start interactive menu
    interactive_system_menu(system)
end

"""
Batch processing launcher
"""
function batch_processing()
    println("📦 Batch Processing Mode")
    println("=" ^ 30)
    
    include("wonder_grid_system.jl")
    
    # Initialize system
    system = WonderGridSystem()
    
    # Load historical data
    load_historical_data!(system, generate_sample_historical_data(200))
    
    # Process multiple key numbers
    key_numbers = [7, 13, 21, 29, 31]
    
    println("Processing $(length(key_numbers)) key numbers: $(join(key_numbers, ", "))")
    
    batch_results = Dict{Int, Any}()
    
    for key_number in key_numbers
        println("\n🔑 Processing key number $key_number...")
        
        result = execute_wonder_grid_workflow(system, key_number)
        if result !== nothing
            batch_results[key_number] = result
            println("✅ Key $key_number: $(length(result["combinations"])) combinations")
        else
            println("❌ Key $key_number: Failed to generate combinations")
        end
    end
    
    # Summary
    println("\n📊 Batch Processing Summary")
    println("=" ^ 40)
    
    total_combinations = 0
    total_time = 0.0
    
    for (key, result) in batch_results
        combinations_count = length(result["combinations"])
        execution_time = result["execution_time"]
        
        total_combinations += combinations_count
        total_time += execution_time
        
        println("Key $key: $combinations_count combinations ($(round(execution_time, digits=2))s)")
    end
    
    println("\nTotal: $total_combinations combinations in $(round(total_time, digits=2)) seconds")
    println("Average: $(round(total_combinations / length(batch_results), digits=0)) combinations per key")
end

"""
Testing launcher
"""
function testing_launcher()
    println("🧪 Testing Mode")
    println("=" ^ 20)
    
    println("Available test suites:")
    println("1. Quick system test")
    println("2. Comprehensive unit tests")
    println("3. Integration and end-to-end tests")
    println("4. Performance benchmarks")
    println("5. All tests")
    
    print("Select test suite (1-5): ")
    choice = strip(readline())
    
    if choice == "1"
        include("wonder_grid_system.jl")
        system = WonderGridSystem()
        load_historical_data!(system, generate_sample_historical_data(50))
        run_quick_system_test(system)
        
    elseif choice == "2"
        println("Running comprehensive unit tests...")
        include("test_comprehensive_unit_tests.jl")
        
    elseif choice == "3"
        println("Running integration and end-to-end tests...")
        include("test_integration_end_to_end.jl")
        
    elseif choice == "4"
        println("Running performance benchmarks...")
        include("wonder_grid_system.jl")
        system = WonderGridSystem()
        load_historical_data!(system, generate_sample_historical_data(100))
        run_performance_tests(system)
        
    elseif choice == "5"
        println("Running all test suites...")
        include("run_all_tests.jl")
        
    else
        println("❌ Invalid selection")
    end
end

"""
Configuration launcher
"""
function configuration_launcher()
    println("⚙️  Configuration Mode")
    println("=" ^ 25)
    
    include("src/configuration.jl")
    
    println("Configuration options:")
    println("1. Interactive configuration setup")
    println("2. Load configuration preset")
    println("3. Display current configuration")
    println("4. Validate configuration file")
    
    print("Select option (1-4): ")
    choice = strip(readline())
    
    if choice == "1"
        config = interactive_configuration_setup()
        
        # Save configuration
        manager = ConfigurationManager()
        manager.config = config
        save_configuration!(manager)
        
        println("✅ Configuration saved successfully")
        
    elseif choice == "2"
        println("Available presets: beginner, standard, advanced, performance")
        print("Enter preset name: ")
        preset_name = strip(readline())
        
        if preset_name in ["beginner", "standard", "advanced", "performance"]
            config = get_configuration_preset(preset_name)
            
            # Save configuration
            manager = ConfigurationManager()
            manager.config = config
            save_configuration!(manager)
            
            println("✅ Configuration preset '$preset_name' loaded and saved")
        else
            println("❌ Invalid preset name")
        end
        
    elseif choice == "3"
        manager = ConfigurationManager()
        display_configuration(manager.config)
        
    elseif choice == "4"
        manager = ConfigurationManager()
        is_valid, errors = validate_configuration(manager.config)
        
        if is_valid
            println("✅ Configuration is valid")
        else
            println("❌ Configuration validation failed:")
            display_validation_errors(errors)
        end
        
    else
        println("❌ Invalid selection")
    end
end

"""
Help and documentation
"""
function show_help()
    println("📚 Wonder Grid System Help")
    println("=" ^ 35)
    
    println("\nLaunch Options:")
    println("  1. Quick Start    - Generate combinations with default settings")
    println("  2. Interactive    - Full interactive system with menus")
    println("  3. Batch          - Process multiple key numbers automatically")
    println("  4. Testing        - Run system tests and validation")
    println("  5. Configuration  - Setup and manage system configuration")
    println("  6. Help           - Show this help information")
    println("  7. Exit           - Exit the launcher")
    
    println("\nSystem Components:")
    println("  • Wonder Grid Engine    - Core mathematical strategy")
    println("  • LIE Elimination      - Pattern-based filtering")
    println("  • Backtesting Engine   - Historical validation")
    println("  • Performance Reporter - Statistical analysis")
    println("  • Result Display       - Export and visualization")
    println("  • Optimization Layer   - Performance enhancements")
    println("  • Concurrent Processing - Multi-threaded execution")
    
    println("\nFile Structure:")
    println("  • wonder_grid_system.jl     - Main integrated system")
    println("  • src/                      - Core component modules")
    println("  • test_*.jl                 - Test suites")
    println("  • demo_*.jl                 - Demonstration scripts")
    println("  • wonder_grid_config.txt    - System configuration")
    
    println("\nFor detailed documentation, see the individual module files.")
end

"""
Main launcher menu
"""
function main_launcher()
    display_banner()
    
    while true
        println("🎯 Wonder Grid System Launcher")
        println("=" ^ 40)
        println("1. Quick Start")
        println("2. Interactive Mode")
        println("3. Batch Processing")
        println("4. Testing Mode")
        println("5. Configuration")
        println("6. Help")
        println("7. Exit")
        
        print("\nSelect launch option (1-7): ")
        choice = strip(readline())
        
        try
            if choice == "1"
                quick_start()
            elseif choice == "2"
                interactive_launcher()
            elseif choice == "3"
                batch_processing()
            elseif choice == "4"
                testing_launcher()
            elseif choice == "5"
                configuration_launcher()
            elseif choice == "6"
                show_help()
            elseif choice == "7"
                println("👋 Thank you for using Wonder Grid Lottery System!")
                break
            else
                println("❌ Invalid option. Please select 1-7.")
            end
            
            if choice != "7"
                println("\nPress Enter to return to main menu...")
                readline()
            end
            
        catch e
            println("❌ Error: $e")
            println("Please try again or contact support.")
            
            println("\nPress Enter to continue...")
            readline()
        end
    end
end

# Command line argument handling
if length(ARGS) > 0
    arg = lowercase(ARGS[1])
    
    if arg == "quick" || arg == "q"
        display_banner()
        quick_start()
    elseif arg == "interactive" || arg == "i"
        display_banner()
        interactive_launcher()
    elseif arg == "batch" || arg == "b"
        display_banner()
        batch_processing()
    elseif arg == "test" || arg == "t"
        display_banner()
        testing_launcher()
    elseif arg == "config" || arg == "c"
        display_banner()
        configuration_launcher()
    elseif arg == "help" || arg == "h"
        display_banner()
        show_help()
    else
        println("❌ Unknown argument: $(ARGS[1])")
        println("Available arguments: quick, interactive, batch, test, config, help")
    end
else
    # No arguments - show main launcher
    main_launcher()
end