---
created: 2025-07-24T22:03:59 (UTC +08:00)
tags: [lotto wheels,lotto wheeling,abbreviated,reduced,lotto,systems,lottery wheel,free,software,lottery,wheeler,Powerball,Mega Millions,Euromillions,create,make,]
source: https://saliu.com/lottowheel.html
author: 
---

# 彩票輪盤：創作、製作彩票輪盤系統 --- Lotto Wheels: Create, Make Lottery Wheeling Systems

> ## Excerpt
> Make, create lotto wheels manually or use the best lottery wheeling software to create efficient lotto wheels, reduced lottery systems, all free, balanced.

---
**_Da Super Lottery Wheeler 和策略師_**

[![<PERSON> teaches you how to create the best lotto wheels mathematically with combinatorics.](https://saliu.com/AxiomIon.jpg)](https://saliu.com/free-lotto-tools.html)  

## <u><span lang="zh-TW"><span data-immersive-translate-translation-element-mark="1"><span data-immersive-translate-translation-element-mark="1">樂透輪盤號碼，玩<i>樂透輪盤 </i>，創造自己的彩券系統</span></span></span></u>

## 作者：<PERSON>，★ _Lotto Wheeling Mathematics 創辦人_

![You make lotto wheels manually or use the best lottery wheeling software to create lotto wheels.](https://saliu.com/HLINE.gif)

## <u><span lang="zh-TW"><span data-immersive-translate-translation-element-mark="1"><span data-immersive-translate-translation-element-mark="1">一、樂透輪盤簡介：樂透輪盤的數學基礎</span></span></span></u>

2002 年 11 月 21 日首次由 _WayBack Machine_ ( _web.archive.org_ ) 捕獲。

-   樂透號碼轉輪或玩樂透轉輪（ _簡稱簡化系統_或_精簡彩票系統_ ）是所謂的_彩票專家玩家_的主要工具。這個概念已經存在了兩百多年。早在彩票出現之前（尤其是從法律角度），數學家就率先研究了這個問題。我將向您展示一種最簡單、數學上最可靠的手動創建樂透轉輪的方法。

讓我們以兩個最基本的輪盤及其元素為例： **9 個**數字和 **12 個**數字。我在 1986-1987 年間（非常便宜！）賣掉了這套輪盤方法和一套不錯的樂透輪盤。後來我停止了銷售，因為我買了一台 IBM 相容電腦，並開始編寫正式的彩票軟體，包括樂透輪盤軟體。我做了一件善事。我剩下了一些廉價的樂透輪盤宣傳冊。我把它們送到了賓州葛底斯堡的一家加油站。這家加油站也是一家持牌彩票代理商（我有時加油時會去那裡買彩票）。幾週後，我又去了這家加油站。彩券代理商問我是否還有更多的樂透輪盤宣傳冊。她說： _「賣得非常火爆！還有更多嗎？」_ 沒有！不幸的是，我甚至連一本都沒給自己留！ ！ ！樂透輪盤宣傳冊是印製出來的。我當時沒有個人電腦（只有極少數人擁有；而且，他們中的大多數人都住在美國）。

您或許能找到買過我那本16頁樂透轉盤手冊的人。這套實用手冊我收了3到5美元。有人告訴我（悄悄話！）我的資料現在成了收藏品。如果您真的有興趣，可以穀歌一下……另外，您可能聽說過羅伯特·塞羅蒂奇。他也是移民到美國的，出版過一些關於樂透轉盤的書。羅伯特·塞羅蒂奇也曾出版過樂透轉盤簡報。一位使用我的彩票軟體的用戶給我發了塞羅蒂奇的簡報，令我大吃一驚。羅伯特·塞羅蒂奇未經許可就發表了我手冊中介紹的樂透轉盤方法。當然，他署名我是作者。問題是，他偷偷地印製了我的方法，沒有徵得我的許可（更別提付費了！）。加州商業改進局注意到了這件事！

話雖如此，我於 20 世紀 80 年代創建的初始樂透轉盤已與 **MDIEditor 和 Lotto WE** 捆綁銷售。現在，一個更全面的**免費軟體**組合也已推出，涵蓋了 5 位數和 7 位數的樂透轉盤。更值得一提的是，我的彩券免費軟體還包含強力球、超級百萬和歐洲百萬的轉盤。請關注 _「資源」_ 部分中的連結。

### <u><span lang="zh-TW"><span data-immersive-translate-translation-element-mark="1"><span data-immersive-translate-translation-element-mark="1"><i>9 號</i>樂透輪，用於抽取 6 個號碼的彩票遊戲</span></span></span></u>

我們可以將這些數字分成 3 組，每組 3 個數字： _A、B、C。 A_ 組 _\= 1,2,3_ ； _B 組 = 4,5,6_ ； _C 組 = 7,8,9_ 。我們可以將這 3 組數字每次組合 2 個，總共組合成 C (3, 2) = 3 種組合，每組 6 個數字： _AB、AC、BC_ 。將這些組轉換為其初始組合，我們得到：

1,2,3, 4,5,6  
1,2,3, 7,8,9  
4,5,6, 7,8,9  

彩券委員會將抽出6個中獎號碼。抽獎過程是隨機的，因此這6個中獎號碼將隨機分佈在3個3位數的號碼組中。中獎號碼的分佈有三種可能性：

0-3-3 = 6 中獎者 = 33%  
1-2-3 = 6 人中 5 人獲勝 = 33%  
2-2-2 = 6 人中 4 人獲勝 = 33%

由於所有號碼組都互相配對，我們的 3 組合樂透轉盤中至少有 4 個中獎號碼。據說，樂透轉盤保證**最低中獎機率** ： _6 個號碼中 4 個_ 。也就是說，如果 6 個中獎號碼在我們選擇的 9 個號碼中，轉盤保證至少一個組合包含至少 4 個中獎號碼。在 33%的中獎機率中，9 個號碼的樂透轉盤會中大獎（ _6 個號碼中 6 個中獎_ ）。

此外，這款 9 個號碼的 6 號樂透轉盤還能保證<u>至少 </u> _5 個號碼中 4 個_ 。 5 個中獎號碼的分佈不會比 _1-2-2_ 或 _1-1-3_ 更差。以下是 3 組號碼中可能的分佈：

0-2-3 = 6 中 5 位獲勝者 = 33%  
1-1-3 = 6 人中 4 人獲勝 = 33%  
1-2-2 = 6 人中 4 人獲勝 = 33%

### <u><span lang="zh-TW"><span data-immersive-translate-translation-element-mark="1"><span data-immersive-translate-translation-element-mark="1"><i>12 號</i>樂透輪，用於抽取 6 個號碼的彩票遊戲</span></span></span></u>

毋庸置疑，這絕對是史上最佳的彩券轉盤。它涵蓋**範圍廣** （12 個號碼或雙倍中獎）； **完美平衡** （所有號碼均等轉盤——每個號碼轉 3 次）；低賠率彩票系統擁有**極佳的最低保底賠率** ： _6 中 4_ ；以及贏得**高額獎金**的最佳機會（ _6 中 5_ ，以及贏得 _6 中 6_ 或頭獎的絕佳機會）。絕對沒有任何彩票系統能夠超越這個賠率非常接近的彩票轉盤： **6** 個賠率，而不是 5 條賠率的超幾何彩票賠率。

我們可以將這 12 點分成 4 組，每組 3 個數字： _A、B、C、D。 A_ 組 _\= 1,2,3_ ； _B 組 = 4,5,6_ ； _C 組 = 7,8,9_ ； _D 組 = 10,11,12_ 。我們可以將這 4 組數字每次組合 2 個，總共組合成 C (4, 2) = 6 個 6 個數字的組合： _AB、AC、AD、BC、BD、CD_ 。將這些數字組合轉換為初始組合，將產生一個宏偉的創意：

1,2,3, 4,5,6  
1,2,3, 7,8,9  
1,2,3, 10,11,12  
4,5,6, 7,8,9  
4,5,6, 10,11,12  
7,8,9, 10,11,12

彩券委員會將抽出6個中獎號碼。抽獎過程是隨機的，因此這6個中獎號碼將隨機分佈在4個3位數的號碼組中。中獎號碼的分佈有5種可能性：

0-0-3-3 = 6 中獎者 = 20%  
0-1-2-3 = 6 人中 5 人獲勝 = 20%  
0-2-2-2 = 6 人中 4 人獲勝 = 20%  
1-1-2-2 = 6 人中 4 人獲勝 = 20%  
1-1-1-3 = 6 人中 4 人獲勝 = 20%  

Since all the groups are paired with each other, our 12-combination lotto wheel can't have fewer than 4 winning numbers. Thusly, the lotto wheel assures a so-called 'minimum guarantee': _4 winners out of 6_. That is, if the 6 winning numbers are among the 12 we selected, the wheel guarantees that at least one combination will contain at least 4 of the winning numbers. In 20% of the winning distribution possibilities, the 12-number lotto wheel hits the jackpot (_6 winners of 6_)!

Those two reduced lottery systems are the only lotto wheels totally based on solid mathematics. The two systems offer tremendous leverage. Personally, I will never play any other lotto wheel. Actually, I settled on the 12-number wheel only. The 12-6 lotto wheel provides very efficient leverage. This extraordinary phenomenon in lotto wheeling is known as the **_Parpaluck effect_**.

The special advantage only occurs in lotto games that draw an **even** amount of winning numbers: Lotto-4, lotto-6, lotto-8 (if ever implemented, etc.) For example, lotto-4 games draw 4 winning numbers. The optimal wheel has 8 numbers (the double of the winning numbers). Divide the 8 numbers in groups of 2; a total of 4 groups results; wheel the 4 groups two at a time; the final result is a 6-line lotto wheel, with 4 numbers per combo. The distribution of the winning numbers in the 4 groups can be: 0-0-2-2 (first prize); 1-1-1-1 (the worst distribution = third prize); etc.

These are the optimal cases of wheeling lotto numbers. No, the lotto wheels cannot be created… equal! Take the 12-number situation. We can group the numbers 2 at a time, for a total of 6 groups of 2 numbers each. We combine the 6 groups 3 at a time, for a total of C(6, 3) = 20 combinations, 6 lotto numbers at a time. The distribution of the 6 winners gets worse, like in this case:

1-1-1-1-1-1 = 3 winners of 6!

Now, we constructed a larger lotto wheel — 20 6-number combinations — but it only guarantees _3 winners out of 6_! Pay more to get less! This negative phenomenon in lotto wheeling is known as the _**Kokostirk-Roberts effect**_.

We all know of another popular lotto format: Drawing 5 winning numbers. How about them **_5-number lotto wheels_**? Short answer: No, we can't construct the same high level of efficiency as in the case of 12 number lotto wheels drawing 6 winning numbers. But we can come acceptably close. Again, we must put the numbers together, in groups. I built a 5-number wheel on the same mathematical principles.

The double of the 5 winning lotto numbers is 10. Therefore, the optimal number pool must have 10 elements. Drawback: We can't arrange the numbers in equal-size groups (because 5 is a prime number). So, my best method was to create two groups of 3 numbers each, and two groups of 2 numbers each. Group A=1,2,3; group B=4,5; group C=6,7,8; group D=9,10. We can combine the 4 groups 2 at a time in a total of C (4, 2) = 6 combinations of 5 numbers: AB, AC, AD, BC, BD, CD. We deleted one number from the 6-number resulting combination; also, we added one number to the resulting 4-number _combonation_ (a favorite term of mine). Converting the groups to their initial compositions, we get:

1,2,3, 4,5  
1,2,3, 6,7  
1,2,3, 9,10  
4,5, 6,7,8  
4,5,8, 9,10  
6,7,8, 9,10

The random distribution of the 5 winning numbers in a lotto-5 draw is more complicated than in a lotto-6 game. We can't group the numbers equally. Thus, the 3-number group can have 3, or 2, or 1 of the winners in a drawing.

0-0-2-3 = 5 winners of 5 = 14+%  
0-1-2-2 = 4 winners of 5 = 14+%  
0-1-2-2 = 4 winners of 5 = 14+%  
0-1-2-2 = 4 winners of 5 = 14+%  
1-1-1-2 = 3 winners of 5 = 14+%  
1-1-1-2 = 3 winners of 5 = 14+%  
1-1-1-2 = 3 winners of 5 = 14+%  

I believe this 5-number lotto wheel has good leverage too. For starters, it assures the _3 of 5_ minimum guarantee alright. In fact, the _3 of 5_ prize is offered in 43% of the favorable cases (when our 10 picks contain the 5 lotto winners in a drawing). In 43% of the favorable cases, this lotto wheel assures at least one _4 of 5_ winners. Not to be neglected, this lotto wheel assures '5 of 5' winners with a 14% degree of certainty.

## <u>II. Tools of Lotto Wheeling: Mathematics, Theory, Software</u>

以上是一個新的子集，由包含集 C(12,6)=924 個每個包含 6 個數字的組合構成。在我的集合理論中，如果包含集有一個主補全規則，則當且僅當它有一個從主補全規則推導出的特定補全規則時，才能構造一個子集。

Knowledgeable players will not play combinations such as _1,2,3,4,5,6_. I detailed in another message how wasteful such play really is. The players will usually select their own numbers, also known as _picks_. For example: _7,13,24,8,33,35,42,28,19,37,40,49_. The next important step is wheeling the picks. That is, the player needs to replace the theoretical numbers _1,2,3,4,…, 12_ by the picks _7,13,24,8,…,49_. The process can be done manually, but it is tedious and error prone. The players use software to wheel their lotto numbers. Software wheeling cost money, up until I released freeware to wheel lotto numbers.

Previous software wheelers were also limited in scope. They could only wheel their own abbreviated lotto systems. Generally, they were unable to wheel third-party wheels or Powerball systems. My lottery wheeler software is the most comprehensive to date. It can wheel any kind of wheel, for up to 100 numbers per combination. It can also wheel Powerball, Mega Millions and Euromillions _lotto wheels_ or _abbreviated lottery systems_.

[![LottoWheeler is the best software to convert lotto wheels to real lottery picks.](https://saliu.com/ScreenImgs/lotto-wheeler.gif)](https://saliu.com/membership.html)

Enter the scene:  
• **FillWheel** - August 2002 - Freeware.  
• **LottoWheeler** - September 2006 - The best - Free 32-bit **lotto wheeling** software.  
That's the easiest way to convert the theoretical lotto wheels or systems (_1,2,3,4,5,6_ etc.) to combinations of real picks (_29,13,33,2,44,39_ etc.)

-   _****Lotto Wheeler****_ is your best choice, regardless of price. The lotto-wheeling software covers the Powerball, Mega Millions, CA SuperLotto, and Euromillions lottery games as well. That is, if you have lotto wheels for those games. Hint: You can find some at this Web site! Please check the _Resources_ section of this page.

使用我自己的軟體，我設計出了最高效的樂透轉盤。例如，我的 10 個號碼的樂透轉盤，有 3 種組合，提供 _6 個號碼中 4 個的_最低保證，完全符合樂透的賠率。之前同類最佳的轉盤有 5 個組合。

A few more wheels from yours truly. They were created by my lotto software **WheelCheck6** (also available for 5-number lottos: **WheelCheck5**):

\* 10 numbers, 100% _4 of 6_, 3 combinations (exactly the lotto odds)  
1 2 3 4 5 6  
1 3 4 8 9 10  
2 5 6 7 9 10  

\* 11 numbers, 100% _4 of 6_, 5 combinations (20% over the lotto odds)  
1 2 3 4 5 6  
1 2 8 9 10 11  
1 3 5 8 10 11  
2 3 5 7 9 10  
4 6 7 8 9 11

Nobody had been able to construct such lotto wheels before my software. The two wheels, however, mushroomed all over the Internet, a few months _**after**_ I published them!

Be aware, however, that the lotto wheels can do more harm than good. Read my article _"The myth of lotto wheels or abbreviated lotto systems"_ (follow the _Resources_ section). The player must use a good strategy of picking lotto numbers, before using lotto wheels. Please read the main lotto lottery strategy page at this site.

My own software, _**LotWon**_, _**SuperPower**_ and _**MDIEditor and Lotto WE**_, comes with a number of lotto wheels or systems. Please keep in mind that those systems were not optimized according to my own set theory. The wheels contain more combinations than the respective lotto odds. Such systems aimed at hitting better prizes. They are meant to be used in conjunction with the lottery strategies I have presented at this website.

The 6-40 lotto game back then (1986) required to play at least two tickets for $1. We played a total of 36 combinations, for $6 each. I had won twice before with one of my lotto colleagues (_4 of 6_). That time, I applied my 9-number lotto system. The three of us in the lotto group also won once another _4 of 6_ third prize. That prize usually paid over $100 per winning ticket.

We played 12-number lotto wheels. I used two wheels for the same 12-number group. The first lotto wheel used the group in lexicographic order, from 1 to 12, in 6 combinations. Then, I shuffled the 12-number set and applied the wheel to it. I always used the same shuffled set to save time. In total, we played 3 groups of 12 numbers each.

The program I wrote generated 12-number combinations randomly. The numbers were not sorted: RAM, speed, and retyping were the main reasons. I wrote the last 20 lotto drawings at the beginning of the program, in the _READ/DATA_ section. I hadn't seen any 4-number group repeating from the last 20 drawings. Of course, the average no-repeat is much longer than 20 draws. Trying to eliminate all 4-number groups in 12-number combinations would be a daunting task (virtually, _mission impossible_, given the home-computing technology at that time). So, I had to be selective. I eliminated 1-2-3-4 and 9-10-11-12 for sure; then, a few more groups in between, like 4-5-6-7, 8-9-10-11.

The computer (Atari 800XL) had a real hard time! I let it run until I saw at least 10 combinations on screen (no disk to save the output!) I wrote down the last three 12-number combos (2 lines each on screen). Then, wheel each 12-number lotto combination twice (manually). It was laborious (you can try it for yourself) and it took some time. Then, fill out the play slips. There were some errors every time, especially filling out the lotto play-cards.

![These are the only winning strategies applicable to lotto wheels for 12 and 18 numbers.](https://saliu.com/HLINE.gif)

-   Axiomatic one, I created three great strategies that increase the efficiency of some lotto wheels by an order of magnitude. The super strategies are applicable to an 18-number lotto wheel in 48 lines (tickets).
-   The strategies, however, can be also applied to the 12-number wheel I presented on this very page.
-   The strategies are based on number frequency, best pairings, and best triples, respectively.
-   [_**<u>The Best Lotto Wheels for 18 Numbers</u>: 4-in-6 minimum guarantee; higher chances at jackpot**_](https://saliu.com/bbs/best-18-number-lotto-wheels.html).
-   Moreover, more reduced lottery systems are available, including links to freely download the 6-number lotto wheels presented on this very page. It's all you need to play lottery wheeling for real. Don't miss the opportunity — and the best of luck to you!

![Selecting the lotto numbers to play a wheel is the most important element. The best are the most frequent lottery numbers.](https://saliu.com/HLINE.gif)

## [<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)

它列出了有關彩票、樂透、軟體、輪盤和系統主題的主要頁面。

See the applications in new and old lottery strategies of the lotto wheels and lotto wheeling techniques presented here:

-   [_**Lotto Strategy, Software: 12-Numbers Combinations in 6-Number Lotto Games**_](https://saliu.com/12-number-lotto-combinations.html).  
    
-   There is also software that applies this lottery strategy to pick-5 lotto:
-   [_**Lotto Software, Wheels, 10-Number Combinations in Lotto 5 Games**_](https://saliu.com/lotto-10-5-combinations.html).
-   _**The Main**_ [_**Lottery, Software, Strategies**_](https://saliu.com/LottoWin.htm), _**Lotto Wheeling Page**_.  
    
-   [_**Neural Networking, Artificial Intelligence AI, Axiomatic Intelligence AxI in Lottery: Strategies, Systems, Software**_](https://saliu.com/neural-networking-lottery.html).
-   [**<u>Lotto Wheels</u>**](https://saliu.com/lotto_wheels.html) for _**Lottery Games Drawing 5, 6, 7 Numbers**_.  
    
-   [_**Myth of Lotto Wheels, Abbreviated, Reduced Lottery Systems**_](https://saliu.com/bbs/messages/11.html).  
    Statistical analysis of real lottery data proves the negative effect of playing static (unbalanced or split) lotto wheels.
-   [_**<u>Positional</u> Lotto Wheels, Reduced Lottery Systems <u>Position-by-Position</u>**_](https://saliu.com/positional-lotto-wheels.html).
-   **Free** [_**Lottery Wheeling Software for Players of Lotto Wheels**_](https://saliu.com/bbs/messages/857.html).
-   Fill out lotto wheels with player's picks (numbers to play); presenting _FillWheel_, **LottoWheeler** lottery wheeling software.
-   _**WHEEL-632 available as the**_ [_**Best <u>On-The-Fly Wheeling</u> Software**_](https://saliu.com/bbs/messages/wheel.html) — applies real lottery filtering.
-   [_**Software to Verify Lotto Wheels**_](https://saliu.com/check-wheels.html) _for missing combinations and generate lotto 5, 6 abbreviated (reduced) systems._
-   [_**Copyright Lottery Numbers, Combinations, Lotto Wheels, Mathematical Relations, Formulas, Equations**_](https://saliu.com/copyright.html).
-   [_**Balanced Lotto Wheels, Lexicographical Order Lottery Wheeling**_](https://saliu.com/bbs/messages/772.html).
-   [_**Powerball Wheels**_](https://saliu.com/powerball_wheels.html).
-   [_**Mega Millions Wheels**_](https://saliu.com/megamillions_wheels.html).
-   [_**Euromillions Wheels**_](https://saliu.com/euro_millions_wheels.html).
    -   _**Download Lotto Wheeling Software,**_ [**<u>Lottery Software</u>**](https://saliu.com/infodown.html):
    -   **Wheel-632, Wheel-532**, the best on-the-fly wheeling software; applies real lottery filtering.
    -   ~ Superseded by the most powerful integrated packages Pick532, Pick532 (now demoted!) and, especially, the **<u>Bright / Ultimate</u>** software packages.  
        
    -   **Combinations**, the universal combinations generator for any lotto, Keno, Powerball, Mega Millions, Euromillions;  
        
    -   **LexicoWheels**, generator of lotto wheels based on lexicographical indexing; applies to any lotto, Keno, Powerball, Mega Millions, Euromillions or two-in-one lotto games.  
        
    -   **WheelCheck5, WheelCheck6**, software to verify lotto wheels for missing combinations and generate lotto-5 and lotto6 systems.  
        
    -   **LottoWheeler**, lottery wheeling software: fill out lotto wheels with player lotto numbers. Supersedes the old-time favorite **FillWheel** (still offered). The two pieces of software replace the theoretical lotto numbers in the SYS/WHEEL files by your picks (the lotto numbers you want to play).
    -   **Shuffle, SuperFormula** to shuffle (randomize, randomly arrange) your lotto picks. Repeat the randomizing procedure several times. The randomized wheels can be used directly, without shuffling your lotto picks first.

![Lottery wheeling master teaches how to create the best 12-number lotto wheel, 18 numbers 6 per line.](https://saliu.com/HLINE.gif)

**[Home](https://saliu.com/index.htm) | [Search](https://saliu.com/Search.htm) | [New Writings](https://saliu.com/bbs/index.html) | [Fundamental Formula](https://saliu.com/formula.htm) | [Odds, Generator](https://saliu.com/calculator_generator.html) | [Contents](https://saliu.com/content/index.html) | [Forums](https://forums.saliu.com/) | [Sitemap](https://saliu.com/sitemap/index.html)**

![The 12 and 9-number lotto wheels assure best chances for high lottery prizes, including the jackpot.](https://saliu.com/HLINE.gif)
