using Test
using SaliuSystem0009.Generator
using SaliuSystem0009.Strategy

@testset "Generator.jl tests" begin
    # Create a dummy strategy for testing
    strategy = Strategy("TestStrategy", [Filter("NumberRange", 1, 49)])

    # Test generate_combinations
    combinations = generate_combinations(strategy)
    @test length(combinations) > 0 # Should generate many combinations for 6/49
    @test all(length(c) == 6 for c in combinations)
    @test all(all(1 <= n <= 49 for n in c) for c in combinations)

    # Test filter_combinations
    # Create a strategy that filters for numbers between 1 and 10
    filter_strategy = Strategy("FilterStrategy", [Filter("NumberRange", 1, 10)])
    
    # Some combinations that should pass and some that should fail
    test_combinations = [
        [1, 2, 3, 4, 5, 6], # Should pass
        [7, 8, 9, 10, 11, 12], # Should fail (11, 12 out of range)
        [1, 2, 3, 4, 5, 10] # Should pass
    ]

    filtered = filter_combinations(test_combinations, filter_strategy)
    @test length(filtered) == 2
    @test [1, 2, 3, 4, 5, 6] in filtered
    @test [1, 2, 3, 4, 5, 10] in filtered
    @test !([7, 8, 9, 10, 11, 12] in filtered)
end