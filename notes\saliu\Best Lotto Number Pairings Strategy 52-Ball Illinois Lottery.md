---
created: 2025-07-24T06:53:50 (UTC +08:00)
tags: [lotto,loto,draw,range,analysis,lottery,software,lottery,system,statistics,drawings,draws,data file,data files,standard deviation,3 standard deviations,analyze,analyse,method,numbers,winning,combinations]
source: https://saliu.com/bbs/messages/664.html
author: 
---

# Best Lotto Number Pairings Strategy 52-Ball Illinois Lottery

> ## Excerpt
> BELLOTTO lottery combination generating software is much easier to use but how many lottery drawings to analyze (in Illinois the lotto 6 game has 52 balls).

---
Posted by <PERSON> on April 09, 2001.

In Reply to: [Optimal drawings range to analyze](https://saliu.com/bbs/messages/663.html) posted by <PERSON> on April 07, 2001.

Axiomaticule, **BELLOTTO** does seem to be much easier to use but does it take into account the DATA-6 (real world) lottory results file or the LEAST-6 least likely pairings file? I spent a considerble amount of time going back some 340 drawings (In Illinois the Lotto has 52 balls and is run twice a week on Wed. & Sat.) and creating the required DATA-6 file like the tutorial instructs. I want to make sure that this effort and the past outcomes are accounted for. Does **BELLOTTO** do that?

Also, I notice that a lot of posters to this board play a 6/49 lotto; how much better are the odds in that game than what Illinois has (6/52)?

Thank you for any reply.

: “El Loco” asked this meaningful question:

: “Maybe I should stick to analyzing the last 100 drawings in the future as well (instead of 200).”

: I believe there is an optimal range, correlated to the Fundamental Formula of Gambling (FFG). Simplifying, I recommend the following values:  
: 1) For lotto: the range should represent three times the biggest lotto number in the game. For example, in a lotto-49 game, the analysis should cover the most recent 150 drawings (49 x 3 = 147; I always use round figures). An intuitive explanation will consider a number of 3 unique pairs that make up a lotto combination. Lotto-5 and lotto-7 are close to the same situation.  
: 2) For digit lotteries: use the same intuitive method. In the pick-3 game, there are C(3,2) = 3 pairs. Each pair has two possibilities, since the digit position counts. The digits 1 and 2 have two pairings: 1-2 and 2-1. as a result, there are almost 6 pairs, if we break down a pick-3 combination. The range of analysis should be 60 (6 pairs x 10 digits). I also analyze 100 drawings in the pick-3 lottery.

: I read Scrooge’s strategy. It is interesting; it has a mathematical foundation. Karl M, of course, has a solid approach, using some complex statistical science. My approach does not follow that path. But he is also in the stock market analysis, hence his preference. I wonder what path would Wall Street professionals take. Soon after I posted my strategy, at least one major brokerage firm literally had a grip on my website. I checked the access-log file; the firm occupied a long segment in the server file! Maybe they rushed to the lotteries and took advantage of the incredible return: over a million for 19,000! (Of course, it also depends on how many winners my strategy creates in the same lottery! Fear not: at this time, my website only gets 7,000+ hits a week!)

![Ion Saliu's Theory of Probability Book founded on valuable mathematics applied to lotto number analysis.](https://saliu.com/bbs/messages/probability-book-Saliu.jpg) [Read Ion Saliu's first book in print: **_Probability Theory, Live!_**](https://saliu.com/probability-book.html)  
~ Founded on valuable mathematics, including applications to lotto number-pairing analyses.

![Best lotto pairings strategy for Illinois Lotto with 52 balls.](https://saliu.com/bbs/messages/HLINE.gif)

## [Resources in Lottery Software, Systems, Lotto Wheeling](https://saliu.com/content/lottery.html)

-   The Main [_**Lotto, Lottery, Software, Strategy Systems**_](https://saliu.com/LottoWin.htm) Page.  
    Presenting software to create free winning lotto, lottery strategies, systems based on mathematics. Get your lotto systems or wheels, the best lottery, lotto software, combinations, winning numbers.
-   [_**Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies**_](https://saliu.com/Newsgroups.htm).  
    Read a genuine analysis of Excel spreadsheets applied to lottery and lotto software development, systems, and strategies. Combining Excel analysis with powerful lottery and lotto software programmed by this author, _Parpaluck_.
-   [_**<u>MDIEditor Lotto WE</u>: Tutorial, Software User Guide, Manual, Book, ebook**_](https://saliu.com/MDI-lotto-guide.html).  
    ~ Also applicable to LotWon lottery, lotto software; plus Powerball/Mega Millions, Euromillions.
-   [_**Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps, Programs**_](https://saliu.com/forum/lotto-book.html).
-   [_**<u>Skip Systems</u> Software**_](https://saliu.com/skip-strategy.html): _**Lotto, Lottery, Powerball, Mega Millions, Euromillions**_.
-   [**<u>Lottery Utility Software</u>**](https://saliu.com/lottery-utility.html): _**Pick-3, 4 Lottery, Lotto-5, 6, Powerball, Mega Millions, Thunderball, Euromillions**_.
-   [**Lotto wheels**](https://saliu.com/lotto_wheels.html) for lottery games drawing 5, 6, or 7 numbers.  
    The most advanced theory of lotto wheels or reduced lottery systems. Get also original lotto wheels for lotto games drawing 5, 6, or 7 numbers: Balanced, randomized, and free.
-   Download [**lottery software, lotto software**](https://saliu.com/infodown.html).

![Optimal lotto loto draw range analysis.](https://saliu.com/bbs/messages/HLINE.gif)

Follow Ups:  

-   [BELLOTTO, BELLBET, BETUS: Software to generate random combinations for lotto, lottery, gambling](https://saliu.com/bbs/messages/665.html) **Ion Saliu** _4/09/2001._

![Pairs, pairings, pairing frequency: BEST Winning lotto, lottery strategy, systems.](https://saliu.com/bbs/messages/HLINE.gif)

Comments:  

![Lottery combination generating software is much easier to use but how many lottery drawings going back to analyze (in Illinois the lotto 6 game has 52 balls).](https://saliu.com/bbs/messages/HLINE.gif)

**[Home](https://saliu.com/index.htm) | [Search](https://saliu.com/Search.htm) | [New Writings](https://saliu.com/bbs/index.html) | [Software](https://saliu.com/infodown.html) | [Odds, Generator](https://saliu.com/calculator_generator.html) | [Contents](https://saliu.com/content/index.html) | [Forums](https://forums.saliu.com/) | [Sitemap](https://saliu.com/sitemap/index.html)**

![Ion Saliu: Software, Programs, Apps, Systems, Strategies.](https://saliu.com/bbs/messages/HLINE.gif)
