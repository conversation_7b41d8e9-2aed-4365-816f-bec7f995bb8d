---
created: 2025-07-24T21:26:40 (UTC +08:00)
tags: [lotto,software,numbers,groups,odd,even,low,high,frequency,sums,sum totals,software user,]
source: https://saliu.com/lotto-groups.html
author: 
---

# Lotto Software for Odd, Even, Low, High Numbers, Sums

> ## Excerpt
> Lotto software works with groups of numbers, user created or computer generated. The lotto number groups are odd, even, low, high numbers, plus sums.

---
First captured by the _WayBack Machine_ (_web.archive.org_) on October 4, 2011.

-   <big>UserGroups5</big> ~ 5-number lotto (e.g. _5 of 43_)
-   <big>UserGroups6</big> ~ 6-number lotto (e.g. _6 from 49_)
-   <big>UserGroups3</big> ~ pick-3 lotteries (000 to 999)
-   <big>UserGroups4</big> ~ 4-digit lottery (0000 to 9999)
-   <big>UserGroupsH3</big> ~ horse racing trifectas (.e.g. _13 4 9_)
    -   These lotto applications, pick lottery programs are not offered as standalone. The programs are components of the **Ultimate** software suites: _**Menu #2, option G = Work with User's Number Groups**_.

![Pick lottery software divides the 0 to 9 digits in odd, even, low, high, sums, frequency groups.](https://saliu.com/ScreenImgs/pick-4-lottery-groups.gif)

_**“Generate lotto combinations from groups of numbers / digits”**_ — by far, the most frequent software request I have received over the past two decades. Inveterate lottery players want to distribute the lotto numbers between odd or even, low or high, or differentiated by sum-totals.

I categorized such parameters _static lottery filters_. I still don't hold them in the highest regards. I always prefer the dynamic filters that my lottery software introduced more than a quarter of a century ago.

I added to the mix the dynamic lotto filters based on number frequencies. The lotto numbers are sorted by frequency in descending order, from the _most frequent_ (a.k.a. _hot numbers_) to the _least frequent_ (a.k.a. _cold numbers_). The minimum number of lotto frequency groups is 2; the maximum number of lotto frequency groups is 5 or 6, depending on the game type.

This new type of lotto software has two main functions:  
1) <u>Reporting</u>: Convert previous lottery drawings to number groups (odd, even, low, high, frequencies); show also the sum total of each lotto draw.  
2) <u>Combination Generating</u>: Let the user generate lotto combinations based on the number groups acting as **filters**. For example, the player wants each lotto combination consist of {_3 odd_ AND _3 even_} AND {_3 low_ AND _3 high_} numbers.

The new lotto software also provides the most accurate tool to <u>calculate total combinations</u> for various lotto groups and sum-totals. There are no formulas in most situations, except for algorithms. From now on, you can simply select the _Count Combinations_ functions. The programs will quickly and precisely calculate total possible combinations for any case you choose. You should know that some cases yield 0 (zero) lotto combinations. Here are a few interesting cases:

<u>5/43</u>; sum-total with most lotto combinations: 110;  
3-odd, 2 even: 0 combinations for sum = 110;  
2-odd, 3 even: 8,952 combinations for sum = 110.

<u>6/49</u>; sum-total with most lotto combinations: 150;  
3-odd, 3 even: 0 combinations for sum = 150;  
4-odd, 2 even: 82,034 combinations for sum = 150.

Axiomatic one, feel free to impress your friends, colleagues, and acquaintances! The good sport will tell them who taught you the trick!

![The lotto software offers the most accurate tool to calculate total combinations for various lotto groups and sum-totals.](https://saliu.com/HLINE.gif)

This is the menu of the new lotto software:

![The main menu of UserLottoGroups, lotto software for odd / even, low / high, sums, sum-totals.](https://saliu.com/ScreenImgs/UserGroups6.gif)

## <u>E = Generate <i>Odd/Even</i> Groups</u>

The lotto numbers are divided into 2 groups: Odd (e.g. 1, 3, 5, ..., 49) and Even (e.g. 2, 4, 6, ..., 48). The numbers are saved to a 2-line text file:

1 3 5 7 9 11 13 15 17 19 21 23 25 27 29 31 33 35 37 39 41 43 45 47 49  
2 4 6 8 10 12 14 16 18 20 22 24 26 28 30 32 34 36 38 40 42 44 46 48

## <u>L = Generate <i>Low/High</i> Groups</u>

The lotto numbers are divided into 2 groups: Low (e.g. 1, 2, 3, ..., 24) and High (e.g. 25, 26, 27, ..., 49). The numbers are saved to a 2-line text file:

1 2 3 4 5 6 7 8 9 10 11 12 13 14 15 16 17 18 19 20 21 22 23 24  
25 26 27 28 29 30 31 32 33 34 35 36 37 38 39 40 41 42 43 44 45 46 47 48 49

## <u>F = Generate <i>Frequency Groups</i></u>

The lotto numbers are divided into 2 to 5 or 6 groups, based on number frequency. The numbers are saved to text files consisting of 2 to 5 (or 6) lines. Here is the sample of a 3-group frequency file:

8 9 28 37 44 47 13 14 15 19 20 22 23 24 25 26  
27 5 29 35 7 1 46 4 6 16 17 18 10 30 31 32  
33 34 11 36 21 38 39 40 41 42 43 12 45 2 3 48 49

## <u>A = Generate <i>All Numbers in One Line</i></u>

The lotto numbers are written to a text file consisting of one line of numbers; e.g. all 49 numbers, from 1 to 49 in lexicographical order.

## <u>C = Create Your <i>File of Number Groups</i></u>

The user has the option to create his/her groups of numbers, based on personal criteria. One restriction: The minimum number of groups (lines) is 2; the maximum number of groups is 5 (for 5-number lottos) or 6 (for 6-number games).

There is another important requirement.  
\*\* Your group file must contain ALL the numbers in your lotto game. For example, a 6/49 game must contain 49 numbers in the text file with your groups.

Let's say you want to play only 18 numbers, 9 odd and 9 even. You want to generate all lotto combinations and then wheel in the lines (with _**WheelIn**_ or _**Super Utilities**_, _W = Duplicates: Strip & Wheel_). You load first the text file this program created for you in option E; e.g. _OddEven49.txt_.

Press _Ctrl+Home_ to be sure you are at the top of the file. Press _Enter_ twice to create 2 blank lines. You already know what odd and what even numbers to play. Cut your odd numbers, one at a time, from the corresponding line and paste them to the 1st blank line. Do the same for the even numbers. Save As your new file to something like _OddEven6-18.txt_, will look exactly like this:

1 5 7 13 23 25 33 41 47  
6 8 14 16 18 26 30 38 48  
3 9 11 15 17 19 21 27 29 31 35 37 39 43 45 49  
2 4 10 12 20 22 24 28 32 34 36 40 42 44 46

You have all 49 lotto numbers in that file. You want to play 3 odds and 3 evens (only from the first 2 groups or lines). When you generate the combinations, you will type the following at the corresponding prompts (_"How many numbers from group # n"_): 3, 3, 0, 0.

You can also create another file based on lotto number _frequency_ or number _skips_. That file will have, say, 11 odd numbers and 7 even numbers. You want to play 4 odds and 2 evens: 4, 2, 0, 0.

This lotto program does not allow playing from only one group of lottery numbers. Again, you can add a blank line at the top of the file. Cut and paste only the numbers you want to play and paste them to the top line. Say, you pasted another 18 numbers you want to play. Save the new file as _Singles6-18.txt_. Now you'll play 6 numbers from line #1 and no numbers (0) from line 2. Type at the corresponding prompts: 6, 0.

Most lottery players will wheel the lotto combinations generated. I tested for myself. The 3, 3, 0, 0 odd-and-even-even situation generated 7056 combinations (for 18 numbers). I shuffled the output file with **Shuffle** (option _F = Files, then Vertically_). Then, I input the randomized (shuffled) file to **Super Utilities** (option _D = Duplicates_). I wheel-stripped the file to 60 combinations.

It is a pretty good _4 of 6_ lotto wheel, after only one shuffle. Of course, the minimum guarantee is 100% satisfied only when the lottery drawing has 3 odd numbers AND 3 even numbers. In fact, in such a situation, the lotto wheel offers far better prizes, possibly _5 of 6_. The worst-case scenario is _0 + 6_ or _6 + 0_ (for total odd and even numbers).

![The lotto software offers the most accurate tool to calculate total combinations for various lotto groups and sum-totals.](https://saliu.com/HLINE.gif)

## <u>R = Reports for <i>Past Drawings</i></u>

This function creates the user group reports for odd-even, low-high, frequencies, sum-totals. The report generator is similar the _W_ function in the main menu of _**Bright / Ultimate**_ software packages.

The two report files will show a number of parameters or _filters_. Based on the reports, you feed the combination generating modules (U or G). The procedure of setting filter values is known as _strategy selection_.

These are samples of the 2 reports, _UG6.1_ and _UG6.2_ for lotto-6:

![Program 3 represents generators of lotto 6 combinations from groups or pools of numbers.](https://saliu.com/ScreenImgs/odd-even-lotto.gif)

![These are static filters, do not repeat from lottery draw to next drawing: eliminate them from play.](https://saliu.com/HLINE.gif)

-   If you look at the reports, you notice that groups of filters do NOT repeat from draw to draw. In fact, they skip several consecutive lottery drawings. If you look at the _SUM_ parameter, it doesn't repeat by itself, let alone in combination with other filters.
-   Therefore these filters make for excellent candidates in the _**reversed lottery strategies (LIE elimination)**_. That is, the combinations generated by such restrictions <u>should not be played</u> — they are a waste of tickets (money). See the dedicated Web page in the _Resources_ section.

![The lotto software generates lottery combinations from several lotto groups and sum-totals.](https://saliu.com/HLINE.gif)

## <u>G = Generate Combinations based on Reports</u>

![Program 3 represents generators of lotto 6 combinations from groups or pools of numbers.](https://saliu.com/ScreenImgs/UserGroups63.gif)

This function generates 6-number lotto combinations based on the reports for user's groups: _sums, odd/even, low/high, plus: 2-, 3-, 4-, 5-, 6-group frequencies_. Those parameters (the columns of the 2 reports) will act as _FILTERS_. You will type the filter value as usually, at the corresponding prompts.

There is one important restriction. There are 5 lotto frequency groups. You can only use one frequency group at a time; otherwise the lotto numbers get mixed up.

This function has 2 options:  
L = <u>Generate</u> combinations in lexicographic order;  
P = <u>Purge</u> an output file of previously generated combinations.

Furthermore, each function has 2 options:  
C = <u>Count</u> combinations only (default);  
D = <u>Save</u> the combinations to a disk file.

The default option C allows for a quick calculation of a strategy (filter settings). It offers precise answers, very much like using a formula. For example: How many 6-49 lotto combinations for _3 odd, 3 even AND 3 low, 3 high_. Here is the most precise... _educated guess_: 1,532,168.

The filters are chosen from a screen-prompt facility. If you press X, no filter screen will be enabled; therefore, all filters will be set to 0. Consequently, all combinations in a lotto game will be generated (e.g. 13,983,816 for _49 taken 6 at a time_). Again, that feature acts like a formula. It allows you to quickly calculate total combinations for different _5 of N_ or _6 of N_ cases.

![Menu 4: Software generates all types of lotto combinations, including all 6/49 games.](https://saliu.com/ScreenImgs/UserGroups64.gif)

## <u>U = Generate Combinations from <i>User's Groups</i></u>

![Program 4: Software generates lotto combinations from groups of lotto numbers created by a player.](https://saliu.com/ScreenImgs/UserGroups62.gif)

You already have available files with groups of numbers that you or this program created; e.g. _OddEven49.txt_ or _Freq6-49.txt_. This function will open the file and load data, line by line. You then will instruct the program how many numbers to use from each group. Total amount of numbers: EXACTLY 6, to build a 6-number lotto combination. For example, you have an odd/even input file; you want to use 2 odd numbers and 4 evens. Or, you can have a frequency input file consisting of 6 lines (i.e. frequency groups); you want 2+2+1+1+0+0, respectively.

This function, however, does NOT use the same screen-prompt facility. The function does not know what filters the lines contain. You only instruct the program how many numbers to take from each group in order to build a 5- or 6-number lotto _combonation_ (a favorite term in my _lottospeak_). Here is a situation: [_**lotto decades**_](https://saliu.com/decades.html). Write the numbers in a text file, decade by decade. Then, choose how many numbers to play from each _lotto decade_.

![Let's create lottery strategies and generate lotto combinations for winning drawings.](https://saliu.com/HLINE.gif)

## <u>S = <i>Strategy</i> Checking</u>

![Lotto #5: Software checks strategies for odd/even, low/high, lottery sums.](https://saliu.com/ScreenImgs/UserGroups65.gif)

This function analyzes the 2 UG6 reports to establish any type of strategy, between MINIMUM and MAXIMUM values. The reports and the data file must be SYNCHRONIZED in order to get accurate strategy reports. Enter the same data file you used to generate the 2 UG6 reports and the same amount of drawings.

The filters are chosen from the common screen-prompt facility.

## <u>H = Strategy Hits in the Past</u>

![Lotto #6: Software analyses how many combinations the lottery strategy generated.](https://saliu.com/ScreenImgs/UserGroups66.gif)

The app generates lotto-6 combinations for situations when a particular strategy (based on number groups) hit in the past. The program needs an input file created by the function S (the previous function). The input file consists of the draw IDs for the hit situations. Otherwise, the user will manually input the filter settings and the drawings IDs.

The filters are chosen from the common screen-prompt facility. If you press X, no filter screen will be enabled; therefore, all filters will be set to 0. Consequently, all combinations in a lotto game will be generated (e.g. 13, 983,816 for _49 taken 6 at a time_) — as many times as strategy hits!

![Integrated lottery software pick-6 or 6-number lotto games.](https://saliu.com/HLINE.gif)

This new type of lotto software has a common feature with all lottery apps written by Ion Saliu (_**Parpaluck**_ or _**LotWon**_ software): <u>Interoperability</u>. The user can generate lotto combinations in one program or function, and save the numbers to disk. Then, open that output file and _**purge**_ it. That is, further reduce the amount of lottery combinations in another program or function.

The **Bright / <u>Ultimate</u>** lotto software packages are available from the downloads site:  
-   [**Lottery software, lotto software**](https://saliu.com/infodown.html) - then category **5.1**.

![When it comes to lotto software for 6-number games, the best program is Bright.](https://saliu.com/HLINE.gif)

-   <u>Closely related materials (pages at this Web site)</u>
-   _**<u>BRIGHT5</u>: High-Powered**_ [_**Lotto Software for 5-Number Games**_](https://saliu.com/lotto5-software.html).
-   _**<u>BRIGHT6</u>: High-Powered**_ [_**Lottery Software for 6-Number Lotto**_](https://saliu.com/lotto6-software.html).

[

## <u>Resources in Lottery Software, Lotto Strategies, Systems</u>

](https://saliu.com/content/lottery.html)

-   [**Visual Book, Manual for Lotto Software**](https://saliu.com/forum/lotto-book.html): _**Tutorial Based on Screenshots**_.
-   [**<u>MDIEditor Lotto</u>**: _**Software Tutorial, User Guide, Instructions**_](https://saliu.com/MDI-lotto-guide.html).
-   _"My kingdom for a good tutorial!"_ [_**Lotto, Lottery Software Tutorial**_](https://saliu.com/bbs/messages/818.html).
-   [_**Filtering, Filters in Lottery Software, Lotto Programs**_](https://saliu.com/filters.html).
-   [_**Lotto, Lottery Strategy in Reverse: <u>Not-to-Win</u> Leads to <u>Not-to-Lose</u> or <u>WIN</u>**_](https://saliu.com/reverse-strategy.html) (_**LIE Elimination**_).
-   [_**Lottery, Lotto Strategy Based on: Sums (Sum-Totals); Odd or Even; Low or High Numbers**_](https://saliu.com/strategy.html).
-   [_**Lottery Strategy, Systems Based on Number <u>Frequency</u>**_](https://saliu.com/frequency-lottery.html).
-   [_**The Best Strategy for Lottery, Lotto, Gambling**_](https://saliu.com/strategy-gambling-lottery.html).
-   [_**Cross-Reference Lottery, Lotto Strategy Files**_](https://saliu.com/cross-lines.html).
-   [_**Lottery, Lotto Sums, Sum-Totals**_](https://saliu.com/forum/lottery-sums.html).
-   [_**Lotto, Lottery Software to Calculate Sum-Totals, Odd-Even, Low-High Patterns**_](https://saliu.com/bbs/messages/626.html).
-   [_**Lotto Program for Groups of Numbers**_](https://forums.saliu.com/lotto-software-odd-even-low-high.html).

![Finally, lotto software to work with all groups of numbers: odd, even, low, high, frequency, sums, sum totals, decades.](https://saliu.com/HLINE.gif)

**[Home](https://saliu.com/index.htm) | [Search](https://saliu.com/Search.htm) | [New Writings](https://saliu.com/bbs/index.html) | [Fundamental Formula](https://saliu.com/formula.htm) | [Odds, Generator](https://saliu.com/calculator_generator.html) | [Contents](https://saliu.com/content/index.html) | [Forums](https://forums.saliu.com/) | [Sitemap](https://saliu.com/sitemap/index.html)**

![Get now this powerful lotto 5, 6 software for your number groups.](https://saliu.com/HLINE.gif)
