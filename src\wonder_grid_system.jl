# Wonder Grid Lottery System - 完整整合系統
# 主要系統入口點，整合所有組件

using Dates
using Statistics

# 核心類型定義
include("types.jl")

# 基礎組件
include("utils.jl")
include("validation.jl")
include("configuration.jl")

# 數據結構
include("data/compact_structures.jl")

# 記憶體管理
include("memory/memory_pool.jl")

# 快取系統
include("cache/multi_level_cache.jl")

# 過濾器引擎
include("filter_engine.jl")
include("filters/one_filter.jl")
include("filters/two_filter.jl")
include("filters/three_filter.jl")
include("filters/four_filter.jl")
include("filters/five_filter.jl")

# 優化引擎
include("optimized_filter_engine.jl")

# 並行計算
include("parallel/parallel_computing.jl")

# 性能監控
include("monitoring/performance_monitor.jl")

# 自動調優
include("tuning/auto_tuner.jl")

# 統計分析
include("statistics/basic_stats.jl")

# Wonder Grid 引擎
include("wonder_grid_engine.jl")

# 全局變量初始化
const GLOBAL_MEMORY_POOL = MemoryPool()
const GLOBAL_PERFORMANCE_MONITOR = PerformanceMonitor()
const GLOBAL_AUTO_TUNER = AutoTuner()

"""
    WonderGridSystem

完整的 Wonder Grid Lottery System 整合類。

這個類整合了所有系統組件，提供統一的接口來訪問：
- 過濾器引擎（標準和優化版本）
- 並行計算功能
- 性能監控和調優
- Wonder Grid 生成
- 統計分析

# 字段
- `config`: 系統配置
- `historical_data`: 歷史開獎數據
- `standard_engine`: 標準過濾器引擎
- `optimized_engine`: 優化過濾器引擎
- `performance_monitor`: 性能監控器
- `auto_tuner`: 自動調優器
- `initialized`: 初始化狀態
- `initialization_time`: 初始化時間

# 範例
```julia
# 創建系統實例
system = WonderGridSystem(historical_data)

# 計算 Skip 值
skip = calculate_skip(system, 1)

# 生成 Wonder Grid
grid = generate_wonder_grid(system, 50)

# 獲取性能報告
report = get_performance_report(system)
```
"""
mutable struct WonderGridSystem
    # 配置
    config::Dict{String, Any}
    
    # 數據
    historical_data::Vector{LotteryDraw}
    
    # 引擎
    standard_engine::Union{FilterEngine, Nothing}
    optimized_engine::Union{OptimizedFilterEngine, Nothing}
    
    # 監控和調優
    performance_monitor::PerformanceMonitor
    auto_tuner::AutoTuner
    
    # 狀態
    initialized::Bool
    initialization_time::Float64
    
    # 內部構造函數
    function WonderGridSystem()
        new(
            Dict{String, Any}(),
            LotteryDraw[],
            nothing,
            nothing,
            GLOBAL_PERFORMANCE_MONITOR,
            GLOBAL_AUTO_TUNER,
            false,
            0.0
        )
    end
end

"""
    WonderGridSystem(historical_data::Vector{LotteryDraw}; kwargs...)

創建並初始化 Wonder Grid System。

# 參數
- `historical_data`: 歷史開獎數據
- `use_optimized_engine`: 是否使用優化引擎（默認：true）
- `enable_parallel`: 是否啟用並行計算（默認：true）
- `enable_monitoring`: 是否啟用性能監控（默認：true）
- `enable_auto_tuning`: 是否啟用自動調優（默認：true）

# 返回值
- `WonderGridSystem`: 初始化完成的系統實例
"""
function WonderGridSystem(
    historical_data::Vector{LotteryDraw};
    use_optimized_engine::Bool = true,
    enable_parallel::Bool = true,
    enable_monitoring::Bool = true,
    enable_auto_tuning::Bool = true,
    config::Dict{String, Any} = Dict{String, Any}()
)
    println("🚀 初始化 Wonder Grid Lottery System...")
    start_time = time()
    
    # 驗證輸入數據
    if isempty(historical_data)
        throw(ArgumentError("歷史數據不能為空"))
    end
    
    # 創建系統實例
    system = WonderGridSystem()
    system.historical_data = historical_data
    system.config = merge(get_default_config(), config)
    
    try
        # 初始化標準引擎
        println("📊 初始化標準過濾器引擎...")
        system.standard_engine = FilterEngine(historical_data)
        
        # 初始化優化引擎
        if use_optimized_engine
            println("⚡ 初始化優化過濾器引擎...")
            system.optimized_engine = OptimizedFilterEngine(
                historical_data,
                use_compact_data = true,
                enable_caching = true,
                auto_cleanup = true
            )
        end
        
        # 配置性能監控
        if enable_monitoring
            println("📈 配置性能監控...")
            configure_performance_monitoring!(system.performance_monitor, system.config)
        end
        
        # 配置自動調優
        if enable_auto_tuning
            println("🎯 配置自動調優...")
            configure_auto_tuning!(system.auto_tuner, system.config)
        end
        
        # 標記初始化完成
        system.initialized = true
        system.initialization_time = time() - start_time
        
        println("✅ Wonder Grid System 初始化完成")
        println("   初始化時間: $(round(system.initialization_time * 1000, digits=2))ms")
        println("   數據點數: $(length(historical_data))")
        println("   優化引擎: $(use_optimized_engine ? "啟用" : "禁用")")
        println("   並行計算: $(enable_parallel ? "啟用" : "禁用")")
        
        return system
        
    catch e
        println("❌ 系統初始化失敗: $e")
        rethrow(e)
    end
end

"""
    get_default_config() -> Dict{String, Any}

獲取系統默認配置。
"""
function get_default_config()
    return Dict{String, Any}(
        "performance" => Dict(
            "enable_caching" => true,
            "cache_size_multiplier" => 1.0,
            "enable_compact_data" => true,
            "auto_cleanup" => true,
            "cleanup_interval_minutes" => 30
        ),
        "monitoring" => Dict(
            "enable_performance_monitoring" => true,
            "enable_detailed_logging" => false,
            "log_level" => "info",
            "enable_auto_tuning" => true,
            "tuning_interval_minutes" => 60
        ),
        "parallel" => Dict(
            "enable_parallel_computing" => true,
            "max_parallel_tasks" => Threads.nthreads(),
            "batch_size" => 50
        )
    )
end

"""
    calculate_skip(system::WonderGridSystem, number::Int) -> Int

計算指定號碼的 Skip 值。

自動選擇最佳的計算方法（優化引擎優先）。
"""
function calculate_skip(system::WonderGridSystem, number::Int)::Int
    if !system.initialized
        throw(ErrorException("系統尚未初始化"))
    end
    
    # 記錄性能監控
    start_time = time()
    
    try
        # 優先使用優化引擎
        if system.optimized_engine !== nothing
            result = calculate_skip_optimized(system.optimized_engine, number)
        else
            result = calculate_one_filter(system.standard_engine, number).current_value
        end
        
        # 記錄性能數據
        execution_time = (time() - start_time) * 1000
        record_performance_data!(system.performance_monitor, "skip_calculation", execution_time)
        
        return result
        
    catch e
        println("❌ Skip 計算失敗: $e")
        rethrow(e)
    end
end

"""
    calculate_pairing_frequency(system::WonderGridSystem, num1::Int, num2::Int) -> Int

計算兩個號碼的配對頻率。
"""
function calculate_pairing_frequency(system::WonderGridSystem, num1::Int, num2::Int)::Int
    if !system.initialized
        throw(ErrorException("系統尚未初始化"))
    end
    
    start_time = time()
    
    try
        if system.optimized_engine !== nothing
            result = calculate_pairing_frequency_optimized(system.optimized_engine, num1, num2)
        else
            result = calculate_pairing_frequency_sequential(system.historical_data, num1, num2)
        end
        
        execution_time = (time() - start_time) * 1000
        record_performance_data!(system.performance_monitor, "pairing_calculation", execution_time)
        
        return result
        
    catch e
        println("❌ 配對頻率計算失敗: $e")
        rethrow(e)
    end
end

"""
    generate_wonder_grid(system::WonderGridSystem, grid_size::Int) -> Vector{Vector{Int}}

生成 Wonder Grid。
"""
function generate_wonder_grid(system::WonderGridSystem, grid_size::Int)::Vector{Vector{Int}}
    if !system.initialized
        throw(ErrorException("系統尚未初始化"))
    end
    
    println("🎯 生成 Wonder Grid (大小: $grid_size)...")
    start_time = time()
    
    try
        # 使用分散式生成（如果支援並行）
        if Threads.nthreads() > 1
            result = generate_wonder_grid_distributed(system.historical_data, grid_size)
            wonder_grid = result["wonder_grid"]
        else
            # 序列生成
            wonder_grid = generate_wonder_grid_sequential(system.historical_data, grid_size)
        end
        
        execution_time = (time() - start_time) * 1000
        record_performance_data!(system.performance_monitor, "wonder_grid_generation", execution_time)
        
        println("✅ Wonder Grid 生成完成 (耗時: $(round(execution_time, digits=2))ms)")
        
        return wonder_grid
        
    catch e
        println("❌ Wonder Grid 生成失敗: $e")
        rethrow(e)
    end
end

"""
    get_performance_report(system::WonderGridSystem) -> Dict{String, Any}

獲取系統性能報告。
"""
function get_performance_report(system::WonderGridSystem)::Dict{String, Any}
    if !system.initialized
        throw(ErrorException("系統尚未初始化"))
    end
    
    report = Dict{String, Any}()
    
    # 基本系統信息
    report["system_info"] = Dict(
        "initialized" => system.initialized,
        "initialization_time_ms" => round(system.initialization_time * 1000, digits=2),
        "data_points" => length(system.historical_data),
        "optimized_engine_enabled" => system.optimized_engine !== nothing,
        "parallel_threads" => Threads.nthreads()
    )
    
    # 性能監控數據
    try
        performance_summary = get_global_performance_summary()
        report["performance"] = performance_summary
    catch e
        report["performance"] = Dict("error" => "無法獲取性能數據: $e")
    end
    
    # 引擎統計
    if system.optimized_engine !== nothing
        try
            engine_stats = get_engine_statistics(system.optimized_engine)
            report["engine_statistics"] = engine_stats
        catch e
            report["engine_statistics"] = Dict("error" => "無法獲取引擎統計: $e")
        end
    end
    
    # 記憶體使用
    try
        memory_stats = get_all_pool_statistics(GLOBAL_MEMORY_POOL)
        report["memory"] = memory_stats
    catch e
        report["memory"] = Dict("error" => "無法獲取記憶體統計: $e")
    end
    
    # 自動調優報告
    try
        tuning_report = get_global_tuning_report()
        report["auto_tuning"] = tuning_report
    catch e
        report["auto_tuning"] = Dict("error" => "無法獲取調優報告: $e")
    end
    
    return report
end

"""
    system_health_check(system::WonderGridSystem) -> Bool

執行系統健康檢查。
"""
function system_health_check(system::WonderGridSystem)::Bool
    println("🏥 執行系統健康檢查...")
    
    health_issues = []
    
    # 檢查初始化狀態
    if !system.initialized
        push!(health_issues, "系統未初始化")
        return false
    end
    
    # 檢查數據完整性
    if isempty(system.historical_data)
        push!(health_issues, "歷史數據為空")
    end
    
    # 檢查引擎狀態
    if system.standard_engine === nothing
        push!(health_issues, "標準引擎未初始化")
    end
    
    # 檢查記憶體使用
    try
        total_memory_gb = Sys.total_memory() / (1024^3)
        free_memory_gb = Sys.free_memory() / (1024^3)
        memory_usage_percent = (1 - free_memory_gb / total_memory_gb) * 100
        
        if memory_usage_percent > 90
            push!(health_issues, "記憶體使用過高: $(round(memory_usage_percent, digits=1))%")
        end
    catch e
        push!(health_issues, "無法檢查記憶體狀態: $e")
    end
    
    # 檢查性能狀態
    try
        performance_summary = get_global_performance_summary()
        if haskey(performance_summary, "performance_grade")
            grade = performance_summary["performance_grade"]
            if grade == "差"
                push!(health_issues, "系統性能等級較低")
            end
        end
    catch e
        push!(health_issues, "無法檢查性能狀態: $e")
    end
    
    # 輸出檢查結果
    if isempty(health_issues)
        println("✅ 系統健康檢查通過")
        return true
    else
        println("⚠️ 發現健康問題:")
        for (i, issue) in enumerate(health_issues)
            println("  $i. $issue")
        end
        return false
    end
end

"""
    cleanup_system!(system::WonderGridSystem)

清理系統資源。
"""
function cleanup_system!(system::WonderGridSystem)
    println("🧹 清理系統資源...")
    
    try
        # 清理記憶體池
        cleaned = auto_cleanup!(GLOBAL_MEMORY_POOL)
        if cleaned > 0
            println("✅ 清理了 $cleaned 個記憶體池項目")
        end
        
        # 清理快取
        if system.optimized_engine !== nothing
            # 這裡可以添加快取清理邏輯
            println("✅ 清理引擎快取")
        end
        
        # 強制垃圾回收
        GC.gc()
        println("✅ 執行垃圾回收")
        
    catch e
        println("⚠️ 清理過程中出現問題: $e")
    end
end

# 導出主要函數
export WonderGridSystem, calculate_skip, calculate_pairing_frequency, generate_wonder_grid
export get_performance_report, system_health_check, cleanup_system!
