# Memory Pool Management
# 記憶體池管理 - 減少記憶體分配開銷，提高性能

using Dates

"""
記憶體池統計
"""
mutable struct MemoryPoolStats
    allocations::Int
    deallocations::Int
    reuses::Int
    peak_usage::Int
    current_usage::Int
    total_size_bytes::Int
    
    function MemoryPoolStats()
        new(0, 0, 0, 0, 0, 0)
    end
end

"""
記憶體池項目
"""
mutable struct PoolItem{T}
    data::T
    in_use::Bool
    last_used::DateTime
    allocation_time::DateTime
    
    function PoolItem{T}(data::T) where T
        now_time = now()
        new{T}(data, false, now_time, now_time)
    end
end

"""
記憶體池
"""
mutable struct MemoryPool{T}
    pool::Vector{PoolItem{T}}
    max_size::Int
    stats::MemoryPoolStats
    cleanup_threshold::Int
    
    function MemoryPool{T}(max_size::Int = 1000, cleanup_threshold::Int = 100) where T
        new{T}(Vector{PoolItem{T}}(), max_size, MemoryPoolStats(), cleanup_threshold)
    end
end

"""
從池中獲取可重用的向量
"""
function get_reusable_vector(pool::MemoryPool{Vector{T}}, size::Int = 0)::Vector{T} where T
    # 尋找可用的項目
    for item in pool.pool
        if !item.in_use && length(item.data) >= size
            item.in_use = true
            item.last_used = now()
            pool.stats.reuses += 1
            
            # 清空向量但保持容量
            empty!(item.data)
            return item.data
        end
    end
    
    # 沒有找到合適的項目，創建新的
    if length(pool.pool) < pool.max_size
        new_vector = Vector{T}()
        if size > 0
            sizehint!(new_vector, size)
        end
        
        item = PoolItem{Vector{T}}(new_vector)
        item.in_use = true
        push!(pool.pool, item)
        
        pool.stats.allocations += 1
        pool.stats.current_usage += 1
        pool.stats.peak_usage = max(pool.stats.peak_usage, pool.stats.current_usage)
        
        return new_vector
    else
        # 池已滿，直接創建新向量
        new_vector = Vector{T}()
        if size > 0
            sizehint!(new_vector, size)
        end
        return new_vector
    end
end

"""
從池中獲取可重用的字典
"""
function get_reusable_dict(pool::MemoryPool{Dict{K,V}})::Dict{K,V} where {K,V}
    # 尋找可用的項目
    for item in pool.pool
        if !item.in_use
            item.in_use = true
            item.last_used = now()
            pool.stats.reuses += 1
            
            # 清空字典但保持容量
            empty!(item.data)
            return item.data
        end
    end
    
    # 沒有找到合適的項目，創建新的
    if length(pool.pool) < pool.max_size
        new_dict = Dict{K,V}()
        
        item = PoolItem{Dict{K,V}}(new_dict)
        item.in_use = true
        push!(pool.pool, item)
        
        pool.stats.allocations += 1
        pool.stats.current_usage += 1
        pool.stats.peak_usage = max(pool.stats.peak_usage, pool.stats.current_usage)
        
        return new_dict
    else
        # 池已滿，直接創建新字典
        return Dict{K,V}()
    end
end

"""
將對象返回到池中
"""
function return_to_pool!(pool::MemoryPool{T}, obj::T) where T
    # 尋找對應的項目
    for item in pool.pool
        if item.data === obj && item.in_use
            item.in_use = false
            item.last_used = now()
            pool.stats.deallocations += 1
            pool.stats.current_usage -= 1
            return true
        end
    end
    
    # 如果沒有找到，可能是直接創建的對象，忽略
    return false
end

"""
清理池中長時間未使用的項目
"""
function cleanup_pool!(pool::MemoryPool{T}, max_idle_minutes::Int = 30) where T
    if length(pool.pool) < pool.cleanup_threshold
        return 0
    end
    
    current_time = now()
    cutoff_time = current_time - Minute(max_idle_minutes)
    
    cleaned_count = 0
    i = 1
    while i <= length(pool.pool)
        item = pool.pool[i]
        if !item.in_use && item.last_used < cutoff_time
            deleteat!(pool.pool, i)
            cleaned_count += 1
        else
            i += 1
        end
    end
    
    return cleaned_count
end

"""
獲取池統計信息
"""
function get_pool_statistics(pool::MemoryPool{T})::Dict{String, Any} where T
    reuse_rate = pool.stats.allocations > 0 ? pool.stats.reuses / pool.stats.allocations : 0.0
    
    return Dict(
        "type" => string(T),
        "pool_size" => length(pool.pool),
        "max_size" => pool.max_size,
        "current_usage" => pool.stats.current_usage,
        "peak_usage" => pool.stats.peak_usage,
        "allocations" => pool.stats.allocations,
        "deallocations" => pool.stats.deallocations,
        "reuses" => pool.stats.reuses,
        "reuse_rate" => reuse_rate,
        "utilization" => length(pool.pool) / pool.max_size
    )
end

"""
記憶體池管理器
"""
mutable struct MemoryPoolManager
    vector_pools::Dict{Type, MemoryPool}
    dict_pools::Dict{Tuple{Type,Type}, MemoryPool}
    last_cleanup::DateTime
    cleanup_interval_minutes::Int
    
    function MemoryPoolManager(cleanup_interval::Int = 15)
        new(
            Dict{Type, MemoryPool}(),
            Dict{Tuple{Type,Type}, MemoryPool}(),
            now(),
            cleanup_interval
        )
    end
end

"""
獲取或創建向量池
"""
function get_vector_pool!(manager::MemoryPoolManager, ::Type{T})::MemoryPool{Vector{T}} where T
    if !haskey(manager.vector_pools, T)
        manager.vector_pools[T] = MemoryPool{Vector{T}}()
    end
    return manager.vector_pools[T]
end

"""
獲取或創建字典池
"""
function get_dict_pool!(manager::MemoryPoolManager, ::Type{K}, ::Type{V})::MemoryPool{Dict{K,V}} where {K,V}
    key = (K, V)
    if !haskey(manager.dict_pools, key)
        manager.dict_pools[key] = MemoryPool{Dict{K,V}}()
    end
    return manager.dict_pools[key]
end

"""
從管理器獲取可重用向量
"""
function get_reusable_vector(manager::MemoryPoolManager, ::Type{T}, size::Int = 0)::Vector{T} where T
    pool = get_vector_pool!(manager, T)
    return get_reusable_vector(pool, size)
end

"""
從管理器獲取可重用字典
"""
function get_reusable_dict(manager::MemoryPoolManager, ::Type{K}, ::Type{V})::Dict{K,V} where {K,V}
    pool = get_dict_pool!(manager, K, V)
    return get_reusable_dict(pool)
end

"""
將向量返回到管理器
"""
function return_vector_to_pool!(manager::MemoryPoolManager, vec::Vector{T}) where T
    pool = get_vector_pool!(manager, T)
    return return_to_pool!(pool, vec)
end

"""
將字典返回到管理器
"""
function return_dict_to_pool!(manager::MemoryPoolManager, dict::Dict{K,V}) where {K,V}
    pool = get_dict_pool!(manager, K, V)
    return return_to_pool!(pool, dict)
end

"""
自動清理所有池
"""
function auto_cleanup!(manager::MemoryPoolManager)
    current_time = now()
    if current_time - manager.last_cleanup >= Minute(manager.cleanup_interval_minutes)
        total_cleaned = 0
        
        # 清理向量池
        for (type, pool) in manager.vector_pools
            cleaned = cleanup_pool!(pool)
            total_cleaned += cleaned
        end
        
        # 清理字典池
        for (types, pool) in manager.dict_pools
            cleaned = cleanup_pool!(pool)
            total_cleaned += cleaned
        end
        
        manager.last_cleanup = current_time
        return total_cleaned
    end
    
    return 0
end

"""
獲取所有池的統計信息
"""
function get_all_pool_statistics(manager::MemoryPoolManager)::Dict{String, Any}
    stats = Dict{String, Any}()
    
    # 向量池統計
    vector_stats = []
    for (type, pool) in manager.vector_pools
        push!(vector_stats, get_pool_statistics(pool))
    end
    stats["vector_pools"] = vector_stats
    
    # 字典池統計
    dict_stats = []
    for (types, pool) in manager.dict_pools
        push!(dict_stats, get_pool_statistics(pool))
    end
    stats["dict_pools"] = dict_stats
    
    # 總體統計
    total_pools = length(manager.vector_pools) + length(manager.dict_pools)
    total_allocations = sum(pool.stats.allocations for pool in values(manager.vector_pools)) +
                       sum(pool.stats.allocations for pool in values(manager.dict_pools))
    total_reuses = sum(pool.stats.reuses for pool in values(manager.vector_pools)) +
                  sum(pool.stats.reuses for pool in values(manager.dict_pools))
    
    stats["summary"] = Dict(
        "total_pools" => total_pools,
        "total_allocations" => total_allocations,
        "total_reuses" => total_reuses,
        "overall_reuse_rate" => total_allocations > 0 ? total_reuses / total_allocations : 0.0,
        "last_cleanup" => manager.last_cleanup
    )
    
    return stats
end

# 全局記憶體池管理器實例
const GLOBAL_MEMORY_POOL = MemoryPoolManager()

"""
便利函數：獲取可重用向量
"""
function get_temp_vector(::Type{T}, size::Int = 0)::Vector{T} where T
    return get_reusable_vector(GLOBAL_MEMORY_POOL, T, size)
end

"""
便利函數：獲取可重用字典
"""
function get_temp_dict(::Type{K}, ::Type{V})::Dict{K,V} where {K,V}
    return get_reusable_dict(GLOBAL_MEMORY_POOL, K, V)
end

"""
便利函數：返回向量到池
"""
function return_temp_vector!(vec::Vector{T}) where T
    return return_vector_to_pool!(GLOBAL_MEMORY_POOL, vec)
end

"""
便利函數：返回字典到池
"""
function return_temp_dict!(dict::Dict{K,V}) where {K,V}
    return return_dict_to_pool!(GLOBAL_MEMORY_POOL, dict)
end

# 導出主要函數和結構
export MemoryPool, MemoryPoolManager, GLOBAL_MEMORY_POOL
export get_reusable_vector, get_reusable_dict, return_to_pool!
export cleanup_pool!, get_pool_statistics, auto_cleanup!, get_all_pool_statistics
export get_temp_vector, get_temp_dict, return_temp_vector!, return_temp_dict!
