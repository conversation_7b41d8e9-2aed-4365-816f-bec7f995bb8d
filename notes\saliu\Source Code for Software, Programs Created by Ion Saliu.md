---
created: 2025-07-24T22:09:35 (UTC +08:00)
tags: [source code,software,programs,Basic,language,PowerBasic,compiler,permanent membership]
source: https://saliu.com/software-code.html
author: <PERSON>
---

# Source Code for Software, Programs Created by <PERSON>

> ## Excerpt
> <PERSON> makes available the source code of some of his software programs. Programming language: BASIC, ready to run in PowerBasic.

---
<big>•</big> Read all relevant information on the main [Software Download](https://saliu.com/infodown.html) page. It is not recommended to reach this page before accessing first "Free Lottery, Lotto, Gambling, Scientific Software: FTP Downloads". If problems occur, you may want to go back there and read carefully the instructions.

<big>• The software code and the resources in this protected area are NOT free to download. Paid membership (the Permanent or Lifetime type only) is required in order to download this collection of software code, lottery results, etc.</big> The membership fee is negligible when compared to similar offerings.

<big>•</big> The software source code you download, however, is FREE to run for an unlimited period of time. It has no crippled features, and no strings attached. It is not shareware: It is totally freeware. Please read the terms and conditions: [Download Great Free Software: Paid Membership Required](https://saliu.com/membership.html).

<big>•</big> You may also download here real drawings (draws) for the multi-jurisdiction lotto games known as Powerball (U.S.), Mega Millions (U.S.), and Euromillions (countries of the European Union). The drawings are well formatted in text files that are ready to use by LotWon lottery software and MDIEditor And Lotto WE.

[BJODDS](https://saliu.com/BjOdds/BJODDS) — includes the source code.  
BjOdds — New blackjack software precisely generates all favorable cases (busted hands) and all possible cases (total blackjack actions). Thus, mathematically precise calculations of the blackjack odds and house edge are possible for the first time ever. The historic algorithms are here for everybody to see, read, comprehend, and fully verify. The old bust odds, as previously calculated by John Scarne, had a wide margin of approximation. Be sure to read the ReadMe.txt file soon after you downloaded and uncompressed your package.

This great piece of software has a special price, plus terms of service. This software package has a dedicated tutorial here:

[BjOdds: Blackjack Software to Calculate Precisely the Bust Odds, House Edge, House Advantage](https://saliu.com/blackjackodds-software.html).  
BJODDS is a compressed, self-extracting file.  
Version 21, August 2010, 32-bit DOS software (i.e. _**command prompt**_, including 64-bit Vista / Windows 7).

[BreakDownNumbers](https://saliu.com/code/BreakDownNumbers) lotto tool software.  
BreakDownNumbers takes one or more lines consisting of numbers and breaks down each line into smaller number-groups: from 1-number groups to groups of 7 numbers per combination (line).  
The integers in the original lines must be in text format, the numbers being separated by spaces or commas; 1, 2, 3,... or 1 2 3...  
Each number-group will be sorted in ascending order (per line); the duplicate groups will be stripped; i.e. the groups will be unique.  
Read more on the message board: _Strategy Error: Pairs, Triples, Quads, Quints_.  
32-bit software running under 32-bit DOS 7+ (command prompt under Win 95/98/Me/NT4/2000/XP/Vista/Windows 7).  
Version 1.0, July 23, 2010 ~ source code included:  
[BreakDownNumbers.BAS](https://saliu.com/code/BreakDownNumbers.BAS).  
Programming language: Basic; ready to run in PowerBasic Console Compiler, 32-bit.

[LexicographicAlgorithms.bas](https://saliu.com/code/LexicographicAlgorithms.bas), incorporates two algorithms to calculate the combination lexicographical order, or rank, or index; reversely, generate the combination for a given lexicographic order or rank. Also known as the problem of the lotto combination sequence number (CSN). The first algorithm, by B. P. Buckles and M. Lybanon; the second, by Ion Saliu.  
[LexicographicAlgorithms](https://saliu.com/code/LexicographicAlgorithms) is the compiled program: [Algorithms, Software to Calculate Combination Lexicographical Order, Rank](https://saliu.com/bbs/messages/348.html); and: [Lexicographical Order: Index, Rank, Algorithms, Combinations, Permutations](https://saliu.com/lexicographic.html).  
Version 1.0, October 2009, 32-bit software running under DOS 7+ (command prompt in Win 95/98/Me/NT4/2000/XP/Vista).  
Programming language: Basic; ready to run in PowerBasic Console Compiler, 32-bit.

[Fibonacci.bas](https://saliu.com/code/Fibonacci.bas), mathematics software that generates Fibonacci numbers and calculates the golden ratio between two consecutive terms of the Fibonacci series.  
Fibonacci is the compiled program: [Pi Day, Pi, Divine Proportion, Golden Proportion, Golden Number, Phi, Fibonacci Series](https://saliu.com/bbs/messages/958.html); and: [Fibonacci Progressions: Mathematics, Gambling, Software, Golden Number](https://saliu.com/Fibonacci.html).  
Programming language: Basic; ready to run in PowerBasic Console Compiler, 32-bit.

[FileLines.BAS](https://saliu.com/code/FileLines.BAS), lottery software that writes to disk the lines of specified indexes in a file, usually a strategy file created by STRAT\*. This represents one situation when working with LotWon and also MDIEditor & Lotto. For example: You created the WS files in the DOS (command prompt) LotWon. You also generated the statistical reports in MDIEditor & Lotto. You then created the strategy file for the stats in MDIEditor & Lotto. You can't have one strategy file across the two platforms. You want to see the same line numbers in WS files for a more comprehensive strategy.  
FileLines is the compiled program: ["Cross-reference strategy files created by LotWon and MDIEditor & Lotto WE"](https://saliu.com/cross-lines.html). Download also a sample input file: [INP-FILE.TXT](https://saliu.com/pub/INP-FILE.TXT).  
Programming language: Basic; ready to run in PowerBasic Console Compiler, 32-bit.

[PARSEL.BAS](https://saliu.com/code/PARSEL.BAS). Software that parses the lottery data files to make sure they comply with the format required by LotWon software.  
PARSEL is the compiled program: [Software to correct most errors in lottery, lotto data files](https://saliu.com/bbs/messages/2.html).  
Programming language: Basic; ready to run in PowerBasic Console Compiler, 32-bit.

[UpDown.bas](https://saliu.com/code/UPDOWN.BAS). The lottery, lotto software that reverses the order in ASCII files: The bottom of the file becomes the top of a new file. Useful application in arranging the lottery data files in the order required by LotWon software. Nasty lottery sites publish lottery histories in unnatural order: The most recent drawing goes to the bottom, instead of the TOP. As a Lotwonista, you always start with the most recent draw, and go all the way down to the oldest drawing (bottom of file).  
UpDown is the compiled program: [Software to reverse order in lottery, lotto results, drawings files](https://saliu.com/bbs/messages/539.html).  
Programming language: Basic; ready to run in PowerBasic Console Compiler, 32-bit.

[Writer.bas](https://saliu.com/code/Writer.bas) generates random words and sentences based on the 26 letters of the Latin alphabet, including W and Y, plus a blank space. The generation is random, except for one restriction: a word must contain at least one speech gene – a vowel, that is. Can be also used as a strong password generator – you can just add a digit or two to or inside a word.  
Writer, is the compiled program: [Computer program that writes random words, sentences, passwords, and...books!](https://saliu.com/writer.html)  
Programming language: Basic; ready to run in PowerBasic Console Compiler, 32-bit.

[SuperRoulette.BAS](https://saliu.com/code/SuperRoulette.BAS), possibly the most advanced and promising act in roulette software programming.  
SuperRoulette, the compiled program, accompanies the incredible Super Roulette Strategy With The Best Roulette Systems Ever Released = Now Free! Read this carefully, for you could strike real BIG: [The Super Roulette Strategy](https://saliu.com/best-roulette-systems.html).  
Programming language: Basic; ready to run in PowerBasic Console Compiler, 32-bit.

[SPINS.BAS](https://saliu.com/code/SPINS.BAS), roulette statistical software.  
SPINS, the compiled program, is an excellent roulette spin generating application, plus a statistical analyzer of the roulette spins. This program is also a great complement to the free roulette system #1 presented on the [_**Theory, Mathematics of Roulette Systems, Strategies, Software**_](https://saliu.com/Roulette.htm). According to the system, it is recommended to play after 2 consecutive '-' in the 'Result +/-' column.  
Programming language: Basic; ready to run in PowerBasic Console Compiler, 32-bit.

[Streaks.bas](https://saliu.com/code/Streaks.bas), software for theory of probability.  
Streaks. exe, the compiled program, calculates the number of like streaks in a number of trials. For example: how many streaks of exactly 5 consecutive heads in 1000 coin tosses?  
Read more: [Gambling Mathematics: Reaction And Legislation Regarding Online Gambling, Internet Casinos](https://saliu.com/gambling-mathematics.html).  
Programming language: Basic; ready to run in PowerBasic Console Compiler, 32-bit.

[SORTING.BAS](https://saliu.com/code/SORTING.BAS), lottery, lotto, horseracing software.  
SORTING. exe, the compiled program, sorts the real lottery drawings or combinations in ascending order. It handles lotto-4, lotto-5, lotto-6, lotto-7, Powerball '5+1' and Euromillions '5+2', Quinto 5-digit lottery. The improved version formats also the pick-3, pick-4 and horse racing (trifectas) data files.  
Programming language: Basic; ready to run in PowerBasic Console Compiler, 32-bit.

[RandomNumbers.bas](https://saliu.com/code/RandomNumbers.bas), source code for a BASIC language programme to generate truly random and unique numbers.  
Read more: [True random numbers generator: BASIC programming source code, algorithm](https://saliu.com/random-numbers.html).  
Programming language: Basic; ready to run in PowerBasic Console Compiler, 32-bit.

![ The software source code you download, however, is free to use for an unlimited period of time.](https://saliu.com/HLINE.gif)

![Download your software, source code, gambling systems, strategy for casino roulette, baccarat, and blackjack.](https://saliu.com/HLINE.gif)

**[Home](https://saliu.com/index.htm) | [Search](https://saliu.com/Search.htm) | [New Writings](https://saliu.com/bbs/index.html) | [Fundamental Formula](https://saliu.com/formula.htm) | [Odds, Generator](https://saliu.com/calculator_generator.html) | [Contents](https://saliu.com/content/index.html) | [Forums](https://forums.saliu.com/) | [Sitemap](https://saliu.com/sitemap/index.html)**

![Exit the best site of software downloads for lottery, gambling, science, and chance!](https://saliu.com/HLINE.gif)
