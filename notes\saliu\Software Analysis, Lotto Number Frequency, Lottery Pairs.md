---
created: 2025-07-24T21:26:40 (UTC +08:00)
tags: [software,system,systems,lotto,lottery,pairs,pairing,numbers,range,frequency,analysis,parpaluck,drawings,draws,past,history,]
source: https://saliu.com/lottery-lotto-pairs.html
author: 
---

# Software Analysis, Lotto Number Frequency, Lottery Pairs

> ## Excerpt
> Software creates powerful lotto, lottery systems based on number, pair frequency. The range (span) of lotto and lottery pairing frequency analyses is essential.

---
### <u>I. The <i>Range</i> of Analysis and <i>Pivot</i> Past Draw</u>

My software allows for any amount of past drawings (or combinations) to be analyzed. You can do an analysis or a report for 2 past draws, 10, 100, 1000, 10000 ... or any other number in between.

Given a range of drawings, each lotto number shows a clear bias towards being drawn with the rest of the lotto numbers. The newest version of my Super Utilities calculates the frequencies of all lotto pairings for every lotto number. It is a major component of Bright6.exe (2011), definitely the most powerful collection of lotto software applications. The following pairing report was done by function _F = Frequency Reports by Lotto Number_.

![Software to create powerful lotto, lottery systems based on number and pair frequency.](https://saliu.com/ScreenImgs/lotto-pairings.gif)

The range of analysis is essential and it depends on the purpose of the report or analysis. This truth became evident the first time when I introduced the _**wonder grid**_ for lotto-6:

[_**Wonder Grid Lottery Strategy Plays Pairs of Lotto Numbers**_](https://saliu.com/bbs/messages/638.html).

I've seen very different results for different ranges of analysis. The purpose of analysis for the _wonder-grid_ was the number of drawings necessary to count the pairs and to select the top-5 pairs for every lotto-6 number. The _wonder grid_ loses it almost completely, if the range (or parpaluck) is too long.

I discovered also another essential element: The _starting point_ of analysis. That is, what is the best past drawing where I should start tracking the pairs' frequencies? In other words, I should delete all the top drawings in the data file up until the most favorable past lottery draw. The probability is the highest for the draw that HIT LAST for the respective strategy. The last hit (or the highest last winning) represents the _PIVOT_ draw. That drawing becomes line #1 in a data file (e.g. _Data-6.2_; _Data-6_ is the original data file; it is up-to-the-date).

I presented a series of _lotto wonder-grid_ reports on this page: [**_**Lotto Wonder Grid**_** _**Revisited: New Pairing Research**_](https://saliu.com/bbs/messages/grid.html).

```
<span size="5" face="Courier New" color="#c5b358">                   PICK-3 Winning Number Checking 
                   Files: OUT3 ( 19 ) against PA-3 ( 100 )
                   Date: 06-24-2003

    Line   Combination                        Straight   Boxed 
     no.     Checked                           Winner    Winner

      1       0 4 9             in draw #                  22 
      1       0 4 9             in draw #                  77 
      2       0 9 4             in draw #                  22 
      2       0 9 4             in draw #                  77 
      4       1 1 6             in draw #                  2 
      4       1 1 6             in draw #        67 *
      6       1 6 1             in draw #        2 *
      6       1 6 1             in draw #                  67 
      7       1 6 8             in draw #        93 *
      9       1 8 6             in draw #                  93 
     10       4 0 9             in draw #        22 *
     10       4 0 9             in draw #                  77 
     11       4 9 0             in draw #                  22 
     11       4 9 0             in draw #        77 *
     12       6 1 1             in draw #                  2 
     12       6 1 1             in draw #                  67 
     13       6 1 8             in draw #                  93 
     14       6 8 1             in draw #                  93 
     16       8 1 6             in draw #                  93 
     17       8 6 1             in draw #                  93 
     18       9 0 4             in draw #                  22 
     18       9 0 4             in draw #                  77 
     19       9 4 0             in draw #                  22 
     19       9 4 0             in draw #                  77 

         Total Hits:                             5         19
</span>
```

I get the same number of straight hits consistently, for either 18 or 19 combinations. No other strategy is involved. Even if playing the same grid every draw for the next 100 draws, the _COW (cost of winning)_ is 1800 (1900). The five wins amount to 2500. The result is profit. Not only does this strategy beat random play, it also beats the monstrous _house edge_ or _house advantage (HA)_ imposed by the lottery (50%)! Random play will yield 1.8 straight wins (1800 / 1000).

Of course, the **skips** are omnipresent. Nothing can avoid skipping — it's mathematical! The strategy above does not hit immediately in most situations. The player can safely sit out 5 drawings between hits. But this pick-3 pair strategy should hit within the next 12-13 draws, based on my FFG calculations. The hit in draw #93 \* in the report above indicates a hit after 7 drawings (100 – 93).

I prefer changing the pairing sets after the first hit. I redo the _wonder grid_ at the winning drawing. This is a very favorable case. I skipped 5 draws; I paid for 2 draws: $38. I won straight. I discard of the lottery strategy. If I continued to play, I would skip 5 drawings again. 93 – 77 – 5 = 11. 18 \* 11 = $198: Still a good outcome.

### <u>II. Calculating the <i>Range</i> of Analysis Based on <i>Fundamental Formula of Gambling</i></u>

The other type of pairing reporting in Super Utilities can be performed by function _2 = Lotto Pairs Rundown_.

![The range (span) of lotto and lottery pairing frequency analyses is of the essence.](https://saliu.com/ScreenImgs/lotto-pairs.gif)

The individual probability, p, is the cornerstone of _**FFG**_. The probability must be calculated as accurately as possible. The degree of certainty, DC, is also important, but secondary to p. I noticed serious discrepancies for different probabilities.

Many here remember that I did not have an answer for the length of the report for the lottery wonder-grid. I tried all kinds of ranges, such as N/2, N\*2, N\*3, up to N\*5 (N represents the largest number in the lotto game; N=10 in the pick 3, 4 lotteries).

There are ways to calculate p very precisely. The probabilities are different if one lotto number (digit) or lottery pairs are considered. I did not make such a distinction early in the (lotto pairing) game.

**<u>1) Probability for <i>one lotto number (lottery digit)</i></u>**  
This is the easiest case. We can calculate easily total number of combinations (straight pick-3, 4 sets) for the game. Then, we need to calculate how many of the combinations (sets) contain one particular number.

For pick-3: each digit appears in 271 straight sets. _p = 271/1000_  
For pick-4: each digit appears in 3439 straight sets. _p = 3439/10000_

For lotto games: I will exemplify for 6/49 lotto games.

One lotto number is combined with the rest of the numbers (N-1) taken 5 at a time. _C(48, 5) = 1712304_ combinations. That's how many times one _particular 6/49 lotto number appears in the total amount of combinations. P = 1712304 / 13983816 = 1 in 8.17_.

That is also the result of 49/6. In the lotto cases, p for one number is more simply calculated as M/N (e.g. 6/49; the reverse, 49/6 is the probability expressed as _1 in something_).

<u><b>2) <i>Pair</i> probability</b></u>  
Calculations are trickier here.  
For the pick games, the number of pairs is equal to C(10, 2) + 10 = 45 + 10 = 55 pairs; _p = 1/55_

For a lotto 6/49 game. Each 2-number group (lotto pair) is combined with the rest of the lotto numbers (N-2) taken 4 at a time. _C(47, 4) = 178365_ combinations. (Incidentally, that's how many combinations are eliminated by the _TWO_ filter in **Power6.EXE** for _min\_TWO = 1_).  
_p = 178365 / 13983816 = 1 in 79_.

<u><b>3) Choosing the <i>degree of certainty</i> (DC)</b></u>  
I'll give more details for pick-3.  
I want to work with integers regarding total number of digits. I convert DC in FFG to number of digits that show up within various numbers of draws N.

I do the calculations using **SuperFormula.EXE**, option _F – FFG_). I select case _2 = The program calculates p_. Type _271_ for the 1st element, _1000_ for the 2nd element. Type the degree of certainty as an integer, not percentage.

```
<span size="5" face="Courier New" color="#c5b358">p = 271/1000
DC = 30 = N = 2 :  3 digits 
DC = 50 = N = 3 :  5 digits
DC = 70 = N = 4 :  7 digits
DC = 80 = N = 6 :  8 digits
</span>
```

I take the _pivot_ draw the one with the last hit regarding playing the most frequent numbers.

The _DC=30%_ generated 4 digits with frequencies above 0. Two of the digits repeated the next drawing (DC=50, N=3). The same three digits repeated a total of 4 times in the next 4 drawings, including a boxed combination (N=6, DC=80). The probability p for pick-3 is calculated as _regardless of position_, so only the boxed play is valid.

Pick-3 pair analysis:

```
<span size="5" face="Courier New" color="#c5b358">p = 1/55
DC = 25 = N = 16 :  14 pairs 
DC = 51 = N = 39 :  28 pairs 
DC = 76 = N = 78 :  42 pairs 
</span>
```

From N=16 to N=39 (a balance of 13 drawings) the pair strategy should hit. It's important to do the grid for the pivot draw, as in the pick-3 report at I.

Actually, in the report above I generated the top-3 pairs, while eliminating the worst-7 pairs. That is a tough strategy: it is budget-oriented.

Lotto 6/49 pair analysis:

```
<span size="5" face="Courier New" color="#c5b358">p = 1/79
DC = 26 = N = 24 :  20 pairs 
DC = 50 = N = 54 :  39 pairs 
DC = 74 = N = 105 :  58 pairs 
</span>
```

So, I believe the optimal range for the lotto-6 wonder grid should be done for 24 drawings back, starting at the pivot draw. The pairs should register a major hit within the next 54 – 24 = 30 drawings, or thereabouts.

It would very interesting to see other people's results. **Super Utilities** allow generation of lotto combinations based on the best pairings, while eliminating the worst lotto pairs. Option _M = Make/Break/Position_, then _Break 5/6_).

Hopefully, other people will contribute here. There will be NO copyright infringement in this case!

```
<span size="5" face="Courier New" color="#c5b358">p = 271/1000 
DC = 30: N = 2 :  3 digits 
DC = 50 :  N = 3 :  5 digits 
DC = 70 :  N = 4 :  7 digits 
DC = 80 :  N = 6 :  8 digits
</span>
```

There is a strong correlation between the _degree of certainty_ and _number of elements drawn_. The probability is presented in the _**Ion Saliu's Paradox of N Trials**_. If there are _N_ elements in a set and the individual probability is expressed as _p = 1/N_, then the _degree of certainty_ for one element to appear tends to _**1-(1/e)**_ or approximately _**63.2%**_. At the same time, we will notice that approximately 63.2% of the elements come out in N trials. That's because a number of elements repeat, while other elements do not come out. My program OccupancySaliuParadox.EXE proves that correlation between DC and number of elements drawn.

There are 10 pick-3 digits. 30% means 3 digits. I listed the number of digits to show how many digits to expect to be drawn in various numbers of draws. Usually those values do not match in reality, when the number of drawings is too small. But if you do for 10 pick-3 drawings, you'll notice that 6 or 7 digits come out, while 4 or 3 digits don't show up. DC and number of digits come close to each other.

If you generate a _TOP5_ grid file, it usually starts at pair #1. You can start at another position; e.g. start at pos. #4, with 12 pairs. Pair #4, 5, … #15.

I go now with position #1. That's how I did it for pick-3. Digit, plus pos. 1, 2, 3. Then, I eliminated pairs 4, 5, 6, 7, 8, 9, 10 (WORST7). I saw also some GREAT results for lotto-5.

In _Break_ of _**Super Utilities**_ I use the option that has one fixed number (digit): The first one in each line.

### <u>III. Where to Start: The <i>Pivot</i> Drawing</u>

For lotto, I start at draw #100. I delete 100 draws from data-5. Save to file data-5.2. I check to see if the grid had hits in the 100 draws that I eliminated from my data-5. If there is no major hit, I go back one more draw. I delete the first line in data-5.2. After a while, I'll come over a situation where the grid registered a major hit in the “next” 101 drawings (the top 101 in my original data-5). For example, the last major hit was in draw #17 in data-5. I delete 16 draws in data-5 and save it again as data-5.2. I create the new grid for data-5.2. Hopefully, the grid did not hit in the “next” 16 drawings in data-5. Otherwise, I'll have to wait a few more drawings before playing.

For pick lottery, I usually start at draw #50; also, at drawing #100.

The starting point makes a huge difference. I started at line #1 in my pick-3 data file (November 2006). I recorded 22 straight wins for _parpaluck = 16_. I deleted the top 100 draws in Pa-3 and saved the file as Pa-3.2. I chose the draw for the oldest hit in the winning report. I recreated the _BREAK3.2_ output file for the same _parpaluck_. This time, I recorded 39 straight hits. I chose the next _parpaluck_ for the same _PA-3.2_ data file. I also applied the worst-7 pair elimination. The results were much, much better.

```
<span size="5" face="Courier New" color="#c5b358">                   PICK-3 Winning Number Checking (Parpaluck = 39)
                   Files: BREAK3.2 (6) against PA-3 (100)
                   Date: 11-21-2006

    Line   Combination                        Straight   Boxed 
     no.     Checked                           Winner    Winner

      1       1 5 3             in draw #                  87 
      1       1 5 3             in draw #                  93 
      2       1 3 5             in draw #        87 *
      2       1 3 5             in draw #                  93 
      3       5 1 3             in draw #                  87 
      3       5 1 3             in draw #        93 *
      4       3 1 5             in draw #                  87 
      4       3 1 5             in draw #                  93 
      5       5 3 1             in draw #                  87 
      5       5 3 1             in draw #                  93 
      6       3 5 1             in draw #                  87 
      6       3 5 1             in draw #                  93 

         Total Hits:                             2         10 
</span>
```

I think this method offers the best results. Of course, people will try other positions and other ranges. The key fact is mathematical, though: Some numbers repeat in the near future, while other numbers don't show up. The repetition and non-appearance are different from range to range. It is the relation between _p_, _DC_ and _N_ in the _**Fundamental Formula of Gambling (FFG)**_.

I created a special page with the parameters calculated with _**SuperFormula.EXE**_. The tables cover the following games: pick-3, pick-4, lotto 5/39, and lotto 6/49. For other lottery formats, you need to edit the source file (the HTML code). Insert your calculations in the corresponding table cells. Print the tables for handy reference.

☛ [_**FFG: Numbers (Digits), Degree of Certainty, Number of Trials (Drawings)**_](https://saliu.com/FFGNumbersDCTables.htm).

I am not saying that it's me who has the grasp of lottery pairing. I am still shocked how well the pairings can perform at some points in the history file. As the example above, I go back 100 lottery drawings. The pairs perform very well even for the previous 100 real drawings. Those previous 100 were out of the _range of analysis_ (_parpaluck_).

There is another method of working with the _parpaluck_. I presented it with the Powerball skip system:

☛ [_**Powerball, Mega-Millions strategy, system, based on pools of numbers derived from skips**_](https://saliu.com/powerball-systems.html).

But for that you need a complete history file: From the beginning of the lottery game, without interruption.

![Software for lottery & lotto results, drawings, draws, previous numbers, history.](https://saliu.com/HLINE.gif)

[

### Resources in Lottery, Software, Systems, Lotto Wheels, Strategies

](https://saliu.com/content/lottery.html)-   The Main [_**Lotto, Lottery, Software, Strategy**_](https://saliu.com/LottoWin.htm) Page  
    Presenting software to create free winning lotto, lottery strategies, systems based on mathematics. Get your lotto systems or wheels, the best lottery, lotto software, combinations, winning numbers.
-   [_**Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies**_](https://saliu.com/Newsgroups.htm)  
    Read a genuine analysis of Excel spreadsheets applied to lottery and lotto developing of software, systems, and strategies. Combining Excel analysis with powerful lottery and lotto software programmed by this author, _Parpaluck_.
-   [_**<u>MDIEditor Lotto WE</u>: Lottery Software Manual, Book, ebook, Help**_](https://saliu.com/MDI-lotto-guide.html).  
    ~ Also applicable to LotWon lottery, lotto software; plus Powerball/Mega Millions, Euromillions.
-   _**Visual Tutorial to Bright6.EXE and the**_ [_**Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps, Programs**_](https://saliu.com/forum/lotto-book.html).
-   _"My kingdom for a good lotto tutorial!"_ [_**Lotto, Lottery Strategy Tutorial**_](https://saliu.com/bbs/messages/818.html).
-   [_**Documentation, Help: MDIEditor Lotto WE, Lottery Software, Strategy Tutorial**_](https://saliu.com/mdi_lotto.html).
-   [**<u>Lottery Skip Systems</u>**](https://saliu.com/skip-strategy.html): _**Lotto, Powerball, Mega Millions, Euromillions, Horse Racing, Roulette, Football**_.
-   [_**Lottery Utility Software**_](https://saliu.com/lottery-utility.html): Pick-3, 4 Lottery, Lotto-5, 6, Powerball, Mega Millions, Euromillions.
-   [_**Practical lottery filtering in lotto software**_](https://saliu.com/filters.html).
-   [_**Lotto wheels for lotto games**_](https://saliu.com/lotto_wheels.html) drawing 5, 6, or 7 numbers  
    The most advanced theory of lotto wheels or reduced lottery systems. Get also original lotto wheels for lotto games drawing 5, 6, or 7 numbers: Balanced, randomized, and free.
-   [_**Software News: Lotto, Lottery, Horse Racing, Pairs Strategy**_](https://saliu.com/software-news.html).
-   [_**Updates to the Bright lotto, lottery, and horse-racing software**_](https://saliu.com/forum/software-updates.html) bundles.  
    Updates to several Bright software packages: pick-3 and pick-4 lotteries, horse racing, 5- and 6-number lotto games.
-   Download [**lottery software, lotto software**](https://saliu.com/infodown.html).

![The most powerful lottery software based on lotto number frequency, stats.](https://saliu.com/HLINE.gif)

**[Home](https://saliu.com/index.htm) | [Search](https://saliu.com/Search.htm) | [New Writings](https://saliu.com/bbs/index.html) | [Fundamental Formula](https://saliu.com/formula.htm) | [Odds, Generator](https://saliu.com/calculator_generator.html) | [Contents](https://saliu.com/content/index.html) | [Forums](https://forums.saliu.com/) | [Sitemap](https://saliu.com/sitemap/index.html)**

![Analysis of best ranges in lotto number frequency, lottery pairs.](https://saliu.com/HLINE.gif)
