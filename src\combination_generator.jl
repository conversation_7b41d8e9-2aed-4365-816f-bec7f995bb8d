# Advanced combination generation algorithms for Wonder Grid strategy

"""
CombinationGenerator - Advanced algorithms for generating lottery combinations
"""
mutable struct CombinationGenerator
    pairing_engine::PairingEngine
    generation_cache::Dict{String, Vector{Vector{Int}}}
    performance_metrics::Dict{String, Float64}
    
    function CombinationGenerator(pairing_engine::PairingEngine)
        new(pairing_engine, Dict{String, Vector{Vector{Int}}}(), 
            Dict{String, Float64}("total_generations" => 0.0, "cache_hits" => 0.0))
    end
end

"""
Create CombinationGenerator from WonderGridEngine
"""
function CombinationGenerator(engine)  # Use duck typing to avoid circular dependency
    return CombinationGenerator(engine.pairing_engine)
end

"""
Generate C(10,4) = 210 combinations per key number using top 25% pairings
"""
function generate_standard_combinations(generator::CombinationGenerator, key_number::Int)::Vector{Vector{Int}}
    start_time = time()
    
    # Validate input
    if !(1 <= key_number <= 39)
        throw(ArgumentError("Key number must be between 1 and 39"))
    end
    
    # Check cache first
    cache_key = "standard_$(key_number)"
    if haskey(generator.generation_cache, cache_key)
        generator.performance_metrics["cache_hits"] += 1
        return generator.generation_cache[cache_key]
    end
    
    # Get top 25% pairings (approximately 10 numbers for Lotto 5/39)
    pairing_analysis = identify_top_25_percent_pairings(generator.pairing_engine, key_number)
    top_pairings = pairing_analysis["top_numbers"]
    
    # Ensure we have exactly 10 pairings for C(10,4) = 210 combinations
    if length(top_pairings) < 10
        # Extend with additional pairings if needed
        extended_analysis = identify_top_pairings(generator.engine.pairing_engine, key_number, 0.5)
        extended_pairings = extended_analysis["top_numbers"]
        
        # Add additional pairings to reach 10
        for pairing in extended_pairings
            if !(pairing in top_pairings) && length(top_pairings) < 10
                push!(top_pairings, pairing)
            end
        end
        
        # If still not enough, add remaining numbers
        if length(top_pairings) < 10
            all_numbers = collect(1:39)
            filter!(n -> n != key_number && !(n in top_pairings), all_numbers)
            
            for num in all_numbers
                push!(top_pairings, num)
                if length(top_pairings) >= 10
                    break
                end
            end
        end
    elseif length(top_pairings) > 10
        # Limit to exactly 10 for standard C(10,4) = 210 combinations
        top_pairings = top_pairings[1:10]
    end
    
    # Generate all C(10,4) = 210 combinations
    combinations = Vector{Vector{Int}}()
    
    for combo in Combinatorics.combinations(top_pairings, 4)
        # Create 5-number combination with key number included
        full_combo = sort([key_number; combo])
        push!(combinations, full_combo)
    end
    
    # Validate results
    if length(combinations) != 210
        @warn "Generated $(length(combinations)) combinations instead of expected 210 for key $key_number"
    end
    
    # Verify all combinations include key number
    if !all(combo -> key_number in combo, combinations)
        throw(AssertionError("Not all combinations contain the key number"))
    end
    
    # Cache the result
    generator.generation_cache[cache_key] = combinations
    
    # Update performance metrics
    generation_time = time() - start_time
    generator.performance_metrics["total_generations"] += 1
    generator.performance_metrics["last_generation_time"] = generation_time
    
    return combinations
end

"""
Generate combinations with custom pairing count (flexible C(n,4))
"""
function generate_flexible_combinations(generator::CombinationGenerator, key_number::Int, 
                                       pairing_count::Int)::Vector{Vector{Int}}
    start_time = time()
    
    # Validate inputs
    if !(1 <= key_number <= 39)
        throw(ArgumentError("Key number must be between 1 and 39"))
    end
    
    if pairing_count < 4
        throw(ArgumentError("Need at least 4 pairings to generate combinations"))
    end
    
    if pairing_count > 20
        @warn "Large pairing count ($pairing_count) may generate many combinations"
    end
    
    # Check cache
    cache_key = "flexible_$(key_number)_$(pairing_count)"
    if haskey(generator.generation_cache, cache_key)
        generator.performance_metrics["cache_hits"] += 1
        return generator.generation_cache[cache_key]
    end
    
    # Get required number of top pairings
    percentage = min(1.0, pairing_count / 38.0)  # 38 other numbers available
    pairing_analysis = identify_top_pairings(generator.pairing_engine, key_number, percentage)
    top_pairings = pairing_analysis["top_numbers"]
    
    # Adjust to exact count needed
    if length(top_pairings) < pairing_count
        # Add more pairings if available
        extended_analysis = identify_top_pairings(generator.pairing_engine, key_number, 1.0)
        all_pairings = extended_analysis["top_numbers"]
        
        for pairing in all_pairings
            if !(pairing in top_pairings) && length(top_pairings) < pairing_count
                push!(top_pairings, pairing)
            end
        end
    elseif length(top_pairings) > pairing_count
        top_pairings = top_pairings[1:pairing_count]
    end
    
    # Generate C(n,4) combinations
    combinations = Vector{Vector{Int}}()
    expected_count = binomial(length(top_pairings), 4)
    
    for combo in Combinatorics.combinations(top_pairings, 4)
        full_combo = sort([key_number; combo])
        push!(combinations, full_combo)
    end
    
    # Cache and track performance
    generator.generation_cache[cache_key] = combinations
    generator.performance_metrics["total_generations"] += 1
    generator.performance_metrics["last_generation_time"] = time() - start_time
    
    return combinations
end

"""
Generate combinations from top 25% pairings with efficiency optimization
"""
function generate_efficient_combinations(generator::CombinationGenerator, key_number::Int;
                                        max_combinations::Int = 210,
                                        min_pairing_coverage::Float64 = 0.3)::Vector{Vector{Int}}
    start_time = time()
    
    # Get pairing analysis
    pairing_analysis = identify_top_25_percent_pairings(generator.pairing_engine, key_number)
    
    # Check if pairing coverage meets minimum requirement
    if pairing_analysis["frequency_coverage"] < min_pairing_coverage * 100
        @warn "Key number $key_number has low pairing coverage: $(round(pairing_analysis["frequency_coverage"], digits=1))%"
    end
    
    top_pairings = pairing_analysis["top_numbers"]
    
    # Optimize pairing count based on max_combinations constraint
    if max_combinations == 210
        # Standard case: use exactly 10 pairings
        target_pairings = 10
    else
        # Calculate optimal pairing count for desired combination limit
        target_pairings = 4  # Minimum
        while target_pairings <= length(top_pairings) && binomial(target_pairings, 4) <= max_combinations
            target_pairings += 1
        end
        target_pairings -= 1  # Step back to last valid count
    end
    
    # Generate combinations with optimal pairing count
    return generate_flexible_combinations(generator, key_number, target_pairings)
end

"""
Generate combinations with quality-based pairing selection
"""
function generate_quality_combinations(generator::CombinationGenerator, key_number::Int;
                                     quality_threshold::Float64 = 0.5)::Vector{Vector{Int}}
    start_time = time()
    
    # Get comprehensive pairing analysis
    all_pairings = get_all_pairings_for_number(generator.pairing_engine, key_number)
    
    # Filter pairings by quality (frequency relative to average)
    if !isempty(all_pairings)
        frequencies = [pair[2] for pair in all_pairings]
        avg_frequency = mean(frequencies)
        quality_threshold_freq = avg_frequency * quality_threshold
        
        quality_pairings = [pair[1] for pair in all_pairings if pair[2] >= quality_threshold_freq]
        
        # Ensure minimum count for combinations
        if length(quality_pairings) < 4
            quality_pairings = [pair[1] for pair in all_pairings[1:min(10, length(all_pairings))]]
        end
        
        # Limit to reasonable count
        if length(quality_pairings) > 15
            quality_pairings = quality_pairings[1:15]
        end
        
        # Generate combinations
        combinations = Vector{Vector{Int}}()
        for combo in Combinatorics.combinations(quality_pairings, 4)
            full_combo = sort([key_number; combo])
            push!(combinations, full_combo)
        end
        
        return combinations
    else
        # Fallback to standard generation
        return generate_standard_combinations(generator, key_number)
    end
end

"""
Generate combinations with cost optimization (reduced set)
"""
function generate_cost_optimized_combinations(generator::CombinationGenerator, key_number::Int;
                                            target_cost::Float64 = 100.0,
                                            cost_per_combination::Float64 = 1.0)::Vector{Vector{Int}}
    
    max_combinations = round(Int, target_cost / cost_per_combination)
    
    if max_combinations >= 210
        # Standard generation if budget allows
        return generate_standard_combinations(generator, key_number)
    elseif max_combinations >= 35
        # Use fewer pairings to reduce combinations
        # C(7,4) = 35, C(8,4) = 70, C(9,4) = 126
        target_pairings = 4
        while binomial(target_pairings + 1, 4) <= max_combinations && target_pairings < 10
            target_pairings += 1
        end
        
        return generate_flexible_combinations(generator, key_number, target_pairings)
    else
        # Very limited budget: use only top pairings
        pairing_analysis = identify_top_10_percent_pairings(generator.pairing_engine, key_number)
        top_pairings = pairing_analysis["top_numbers"]
        
        if length(top_pairings) >= 4
            # Use minimum viable set
            min_pairings = top_pairings[1:4]
            combinations = Vector{Vector{Int}}()
            
            # Generate single combination with top 4 pairings
            full_combo = sort([key_number; min_pairings])
            push!(combinations, full_combo)
            
            return combinations
        else
            throw(ArgumentError("Insufficient budget for viable combination generation"))
        end
    end
end

"""
Batch generate combinations for multiple key numbers efficiently
"""
function generate_batch_combinations(generator::CombinationGenerator, 
                                   key_numbers::Vector{Int})::Dict{Int, Vector{Vector{Int}}}
    start_time = time()
    
    results = Dict{Int, Vector{Vector{Int}}}()
    successful_generations = 0
    
    for key_number in key_numbers
        if 1 <= key_number <= 39
            try
                combinations = generate_standard_combinations(generator, key_number)
                results[key_number] = combinations
                successful_generations += 1
            catch e
                @warn "Failed to generate combinations for key number $key_number: $e"
                results[key_number] = Vector{Vector{Int}}()
            end
        else
            @warn "Invalid key number: $key_number"
            results[key_number] = Vector{Vector{Int}}()
        end
    end
    
    # Update performance metrics
    batch_time = time() - start_time
    generator.performance_metrics["last_batch_time"] = batch_time
    generator.performance_metrics["last_batch_size"] = length(key_numbers)
    generator.performance_metrics["last_batch_success_rate"] = successful_generations / length(key_numbers)
    
    return results
end

"""
Generate combinations with mathematical validation
"""
function generate_validated_combinations(generator::CombinationGenerator, key_number::Int)::Dict{String, Any}
    start_time = time()
    
    # Generate combinations
    combinations = generate_standard_combinations(generator, key_number)
    
    # Perform comprehensive validation
    validation_results = Dict{String, Any}()
    
    # Basic validation
    validation_results["total_combinations"] = length(combinations)
    validation_results["expected_combinations"] = 210
    validation_results["count_matches_expected"] = length(combinations) == 210
    
    # Key number inclusion validation
    key_in_all = all(combo -> key_number in combo, combinations)
    validation_results["key_number_in_all"] = key_in_all
    
    # Uniqueness validation
    unique_combinations = unique(combinations)
    validation_results["all_unique"] = length(unique_combinations) == length(combinations)
    validation_results["unique_count"] = length(unique_combinations)
    
    # Format validation
    valid_format = all(combo -> (length(combo) == 5 && 
                                all(n -> 1 <= n <= 39, combo) && 
                                length(unique(combo)) == 5), combinations)
    validation_results["valid_format"] = valid_format
    
    # Mathematical validation
    pairing_analysis = identify_top_25_percent_pairings(generator.pairing_engine, key_number)
    top_pairings = pairing_analysis["top_numbers"]
    expected_mathematical = binomial(min(10, length(top_pairings)), 4)
    validation_results["mathematical_correctness"] = length(combinations) == expected_mathematical
    
    # Distribution analysis
    number_frequency = Dict{Int, Int}()
    for combo in combinations
        for num in combo
            number_frequency[num] = get(number_frequency, num, 0) + 1
        end
    end
    
    validation_results["number_distribution"] = number_frequency
    validation_results["key_number_frequency"] = get(number_frequency, key_number, 0)
    validation_results["key_number_appears_in_all"] = number_frequency[key_number] == length(combinations)
    
    # Performance metrics
    validation_time = time() - start_time
    validation_results["validation_time"] = validation_time
    validation_results["combinations"] = combinations
    
    # Overall validation status
    validation_results["overall_valid"] = (validation_results["count_matches_expected"] &&
                                         validation_results["key_number_in_all"] &&
                                         validation_results["all_unique"] &&
                                         validation_results["valid_format"] &&
                                         validation_results["mathematical_correctness"])
    
    return validation_results
end

"""
Get combination generation statistics and performance metrics
"""
function get_generation_statistics(generator::CombinationGenerator)::Dict{String, Any}
    return Dict{String, Any}(
        "cache_size" => length(generator.generation_cache),
        "performance_metrics" => copy(generator.performance_metrics),
        "cache_hit_rate" => generator.performance_metrics["cache_hits"] / 
                           max(1, generator.performance_metrics["total_generations"]),
        "average_generation_time" => get(generator.performance_metrics, "last_generation_time", 0.0),
        "total_cached_combinations" => isempty(generator.generation_cache) ? 0 : 
                                       sum(length(combos) for combos in values(generator.generation_cache))
    )
end

"""
Clear generation cache to free memory
"""
function clear_generation_cache!(generator::CombinationGenerator)
    empty!(generator.generation_cache)
    generator.performance_metrics["cache_hits"] = 0.0
end

"""
Optimize combination generation for specific constraints
"""
function optimize_combination_generation(generator::CombinationGenerator, key_number::Int;
                                       max_cost::Float64 = 210.0,
                                       min_coverage::Float64 = 0.3,
                                       quality_preference::Float64 = 0.7)::Dict{String, Any}
    
    # Analyze different generation strategies
    strategies = Dict{String, Any}()
    
    # Strategy 1: Standard generation
    standard_combos = generate_standard_combinations(generator, key_number)
    strategies["standard"] = Dict{String, Any}(
        "combinations" => length(standard_combos),
        "cost" => Float64(length(standard_combos)),
        "description" => "Standard C(10,4) = 210 combinations"
    )
    
    # Strategy 2: Cost-optimized
    if max_cost < 210.0
        cost_combos = generate_cost_optimized_combinations(generator, key_number, target_cost=max_cost)
        strategies["cost_optimized"] = Dict{String, Any}(
            "combinations" => length(cost_combos),
            "cost" => Float64(length(cost_combos)),
            "description" => "Cost-optimized for budget \$$max_cost"
        )
    end
    
    # Strategy 3: Quality-based
    quality_combos = generate_quality_combinations(generator, key_number, quality_threshold=quality_preference)
    strategies["quality_based"] = Dict{String, Any}(
        "combinations" => length(quality_combos),
        "cost" => Float64(length(quality_combos)),
        "description" => "Quality-based with $(quality_preference) threshold"
    )
    
    # Recommend best strategy
    pairing_analysis = identify_top_25_percent_pairings(generator.pairing_engine, key_number)
    coverage = pairing_analysis["frequency_coverage"] / 100.0
    
    if coverage >= min_coverage && max_cost >= 210.0
        recommended = "standard"
    elseif max_cost < 210.0
        recommended = "cost_optimized"
    else
        recommended = "quality_based"
    end
    
    return Dict{String, Any}(
        "key_number" => key_number,
        "pairing_coverage" => coverage,
        "strategies" => strategies,
        "recommended_strategy" => recommended,
        "recommendation_reason" => "Based on coverage $(round(coverage*100, digits=1))% and budget \$$max_cost"
    )
end