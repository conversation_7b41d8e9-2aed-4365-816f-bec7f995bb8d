---
created: 2025-07-23T18:23:37 (UTC +08:00)
tags: [archive,message board,forum,lotto,lottery,software,systems,strategy,Powerball,Mega Millions,BellBet.]
source: https://wap.saliu.com/bbs/bbs6.htm
author: 
---

# #6: Analysis of Lottery Software, Lotto Systems, Strategies

> ## Excerpt
> Archive #6 of the forums offers analyses of the science of lottery and lotto software, programming lottery systems and strategies, Powerball, Mega Millions.

---
-   [6/49 Lottery Filter Odds](https://wap.saliu.com/bbs/messages/705.html) - **<PERSON>** _4/22/2001._
-   Lotto 6/49 Odds, Lottery Filter, Probability: LotWon lottery mathematics implemented lottery, lotto filters. Imitated by many, especially LotWin - never duplicated by other lotto software developers!

-   [Error 62 in lottery software](https://wap.saliu.com/bbs/messages/703.html) Strat3 solved - _4/21/2001._
-   I now solved the lottery software problem by following your latest suggestion to generate another SIM-3 file. I had to "simulate" 8200 pick-3 lottery drawings to make it work. Use the 32-bit lotto software and lottery software, including strategy checking.

-   [Free Lotteries on the Web](https://wap.saliu.com/bbs/messages/673.html) - **<PERSON> <PERSON>** _4/12/2001_
-   Computers can generate truly random numbers if the software uses good randomizing functions. Applicable to the free Internet lottos, lotteries. Play free lotto over the Internet.

-   [Re: Free Lotteries on the Web](https://wap.saliu.com/bbs/messages/683.html) - _4/14/2001._
-   Analysis of free web, Internet lotteries, random numbers, combination, combinations generators; randomness.

-   [On Lotto, lottery software](https://wap.saliu.com/bbs/messages/685.html) - **Nik Barker** _4/14/2001_
-   Software, systems and strategies applied to PowerBall, power ball, ThunderBall, thunder ball (UK Lottery) lotto games. ThunderBall (UK Lottery) software, systems, strategies.

-   [Powerball-type lotto games](https://wap.saliu.com/bbs/messages/687.html) – BELLOTTO - _4/15/2001_
-   Software, systems and strategies applied to Powerball, Mega Millions, Thunderball, Tattslotto, lotto, lottery games. Draw 5 numbers, then one more number that can be equal to the previous five.

-   [Back Issues of Wine Spectator Magazine](https://wap.saliu.com/bbs/messages/659.html) - **Wine Spectator** _4/05/2001_
-   Wine made a contribution to the birth of philosophy in Ancient Greece. Well reflected in Plato's Symposium. Yes, Socrates was a big time wine drinker!

-   [Wine is not an object of worship](https://wap.saliu.com/bbs/messages/661.html) here - _4/07/2001._
-   This is not a place for worshipping anything. That is, even if _wine_ is in the title, wine is not adulated here. Many people have the tendency to adore objects, even persons, in a religious manner.

-   [Logic Horse Betting System](https://wap.saliu.com/bbs/messages/649.html). **Gene Nichols** _3/30/2001_
-   I am taking Ion's advice and going public with this horse betting LOGIC system to see if any of you can improve on it by checking it with some of Ion's horseracing software for checking these types of betting systems.

-   [Better ways to play horse racing](https://wap.saliu.com/bbs/messages/662.html) - _4/07/2001._
-   An analysis of betting systems in horse racing starting from the so-called Logic Horse Bet System. Wise martingale betting is the real logical system at horse races.

-   [Texas Loto Pick5 -- 5/39 Lotto](https://wap.saliu.com/bbs/messages/642.html) - **Gary** _3/26/2001._
-   Assemble yourself a LotWon lotto-5 package, including for Texas Lottery pick-5 lotto game.

-   [Assemble yourself a LotWon lotto-5 software](https://wap.saliu.com/bbs/messages/643.html) package - _3/26/2001._
-   What lotto software programs do I need to get started with the Texas Pick 5 lotto game, uses 5/39?

-   [Social lottery or numerical sociology](https://wap.saliu.com/bbs/messages/609.html) - _3/11/2001._
-   The Everything is ruled by Almighty Randomness; everything follows the theory of probability, including social, parasites, society, civilization.

-   [Re: Social lottery or numerical sociology](https://wap.saliu.com/bbs/messages/629.html) - **KM** _3/21/2001._
-   The passage below is somewhat reminiscent of Aristotle's theory of correspondence; higher principles reflected in lower mundane phenomena. His was a literal metaphysical view to be sure, but nonetheless it is interesting to note how your observation is true re. how the realms of pseudo randomness and sociology seem to have such similarities.

-   [SuperPower lottery, lotto software](https://wap.saliu.com/bbs/messages/584.html)\- **Cristi Curelaru** _3/07/2001._
-   What means the titles of the columns in winning lottery reports (UPS, etc)? What are the meaning of layers in the reports and lotto strategy?

-   [Understanding the meaning of the lottery filters](https://wap.saliu.com/bbs/messages/586.html) is not necessary - _3/07/2001._
-   I have received many questions on the meaning of the filters in my lottery software. They only have a meaning to me, and no meaning to others. Actually, I tried to make the lottery filters as "meaningless" as possible to others. You get it, I need to keep some things as secret as possible!

-   [Question About Lottery](https://wap.saliu.com/bbs/messages/582.html) - **Trey** _3/06/2001._
-   My state has a pick four lottery where you pick four different numbers between 00 and 100. I was wandering what the best way is to choose lottery numbers to better my odds of winning. I was told by someone to use the Gaussian method to eliminate the not likely lotto combinations to better my odds.

-   [An answer on new lottery and lotto software](https://wap.saliu.com/bbs/messages/585.html)\- _3/07/2001._
-   I have no clue what 4-digit game you are talking about. The 4-digit lottery my software and systems cover draws 4 digits from 0 to 9. A valid pick-4 combination looks something like 1-2-3-4, or 0966, or 9,4,3,1… Your lottery game is not in my book!

-   [BELLOTTO – Generate lotto and Powerball combinations](https://wap.saliu.com/bbs/messages/572.html) inside the bell (Gauss) curve, around the median - _2/25/2001._
-   BELLOTTO generates Lotto, PowerBall, ThunderBall, Combinations inside the Bell or Gauss Curve, around the probability median.

-   [BETUS – Sports betting, the American way](https://wap.saliu.com/bbs/messages/571.html) - _2/24/2001._
-   BETUS is suitable for betting on American sports. There are two choices only: bet on the favorite to win by more than the point spread; or- bet on the underdog to lose by less than the point spread. This program is an enhancement of TEAMS. The sport-betting combinations display sport team names, instead of numbers.

-   [Creating input files for BETUS sport bet software](https://wap.saliu.com/bbs/messages/574.html)\- _2/26/2001._
-   Start the editor:
    
    C:\\BETUS EDIT  
    You are now in a blank screen.  
    Creating a file is as easy as typing text, one line at a time. The BETUS input files consist of two team names per line. A line of text represents ONE GAME.
    

-   [Lotto, lottery filter setting](https://wap.saliu.com/bbs/messages/567.html) - **GD** _2/20/2001._
-   I have downloaded MDIEditor & Lotto WE, and I have been able to analyze several hundred cases of lottery data with it easily. I think I understand how to use MDIEditor & Lotto stats, to generate optimized lotto-5 combinations fairly well.

-   [A Survival Guide to "MDIEditor and Lotto" lottery software](https://wap.saliu.com/bbs/messages/569.html)\- _2/22/2001._
-   Read a survival Guide to MDIEditor and Lotto software; user's guide, tutorial of the best lottery, lotto free software.

-   [Re: A Survival Guide to "MDIEditor and Lotto"](https://wap.saliu.com/bbs/messages/570.html) - **GD** _2/23/2001._
-   Thanks for the Survival Guide to MDIEditor and Lotto, excellence in software!

-   [Bellbet software generator results](https://wap.saliu.com/bbs/messages/564.html) - **gfunk** _2/19/2001._
-   I have been testing BELLBET and the first 4 days of running 4 sets of ten lottery numbers have had 3 sets of BOXED pick-3 lottery wins.

-   [Re: Bellbet software lottery, lotto combination generator](https://wap.saliu.com/bbs/messages/566.html) - _2/20/2001._
-   I would suggest using BELLBET in the following manner. The chance is very slim to get the straight hit in the first, or the second, or the first two dozen runs of BELLBET. I should run the program between 20 and 30 times. I would choose combinations after the last run. It is like playing 30 drawings for free!

-   [BELLBET (v2.0) – Generate combinations inside the bell (Gauss) curve](https://wap.saliu.com/bbs/messages/559.html), around the median - _2/15/2001._
-   Combination generating software and probability, statistics calculator: standard deviation, binomial distribution, Gauss curve, normal distribution, odds, median. Combinations in the bell curve.

-   [The Admirer and The Critic are playing games (lottery, casino, soccer…)](https://wap.saliu.com/bbs/messages/555.html) - **Karl Malmberg** _2/06/2001._
-   I ran across a free site that might interest you or some of your admirers. Basically one can custom design their own lotto wheels there. They're probably not as good as yours, but if anyone is still scratching their head over how to input any/ver filters for your lottery wheels then perhaps this one is the next best thing.

-   [The Admirer and The Critic are playing games (lottery, casino, soccer…)](https://wap.saliu.com/bbs/messages/556.html) 2- _2/10/2001._
-   It is about Ion Saliu's gambling theories, mathematics, lotto, lottery systems, software. He has admirers but also critics and ferocious enemies, including casino executives.

-   [Combination-generator for sports betting](https://wap.saliu.com/bbs/messages/553.html) - _2/04/2001._
-   Generate combinations of teams for sports betting, like lotto, lottery combinations. Free sport software unlike any other, anywhere.

-   [Perechi de numere loto](https://wap.saliu.com/bbs/messages/550.html) = lotto number pairing - **Attila D.** _1/31/2001._
-   Un sistem de loto bazat pe cele mai frecvente perechi ale numerelor loto, loterie. Soft generator de perechi de numere loto. A lottery system based on all lotto pairs in a lotto 6 game.

-   [Show all lotto pairs](https://wap.saliu.com/bbs/messages/552.html) and their frequencies - _2/02/2001._
-   Attila asked if it was possible to show not only the most frequent and the least frequent pairings in a lotto game. He wanted to know the most frequent pairing and also the next best pair. As a matter of fact, my lottery software had ALL the pairings in an array. The problem was formatting and displaying all the lotto pairs, especially under DOS. I just upgraded UTIL-6 to dump all the lotto-6 pairs to a disk file: PAIRS6.

-   [Generator aleator de numere pentru orice loto, loterie, curse de cai, ruleta, pronosport; Calculator de probabilitate](https://wap.saliu.com/bbs/messages/777.html) - _8/27/2003._
-   Cel mai bun sistem, generator aleator de numere, alegere combinatii orice loto, loterie, curse de cai, ruleta, pronosport, pronostic, 1x2. Calculator, soft complex, complet de probabilitate. The lotto number generator plus sports betting for players in Romania.

-   [ODDSCALC – Calculate the odds of lotto games](https://wap.saliu.com/bbs/messages/549.html) - _1/28/2001._
-   The definitive probability software to calculate real odds in lotto, keno, PowerBall games based on mathematics and theory of probability, hypergeometric distribution.

-   [New FORMULA 3.1, the standard deviation, politics](https://wap.saliu.com/bbs/messages/547.html) - _1/27/2001._
-   FORMULA software: probability calculations for standard deviation, binomial distribution, Gauss curve, normal distribution, odds, median.

-   [Standard Deviation, Variance, Fluctuation, Volatility](https://wap.saliu.com/standard_deviation.html), Median, Mean Average. - **Ion Saliu _\- 10/05/03._**
-   Standard deviation, variance, variability, fluctuation, volatility, dispersion, spread, variation: standard deviation, mean, average, median, sum. Get free formulas, calculations, plus software.

-   [Powerball software](https://wap.saliu.com/bbs/messages/530.html) - **L. Shuler** _1/17/2001._
-   Software, systems and strategies applied to Powerball, Thunderball, Tattslotto, lotto, lottery games. Draw 5 numbers, then one more number that can be equal to the previous five.

-   [Re: Powerball software](https://wap.saliu.com/bbs/messages/533.html) - _1/17/2001._
-   The latest version of _MDIEditor and Lotto WE_ works directly with Powerball, Thunderball, Mega Millions, Big Game, Euromillions types of lotto, lottery games. Very powerful and comprehensive lottery/gambling software - but still FREE, with no strings attached.

-   [Hard pressed - lottery fairness](https://wap.saliu.com/bbs/messages/528.html) - **Barry Whittington** _1/17/2001._
-   Please don't consider me a skeptic of you and your software when I ask how much money you've actually won playing the lottery, using your software or not. If I'm skeptical of anything it's the so called "randomness" of the drawings. Particularly of Powerball after the combination 09-19-29-38-49 was played 3 drawings ago.

-   [A brief history of my lotto experience](https://wap.saliu.com/bbs/messages/532.html) - _1/17/2001._
-   One day, a Porto Rican gave me some lottery materials and tickets. He said he had a hunch I was a lottery expert! I could not drive at that time and I rode a bicycle to work. The Porto Rican would give me sometimes a ride to the food store. But first we would stop by a lottery agency and buy lotto tickets. We won the very first two tries, '4 out of 6' Pretty good back then (1985).

-   [Re: A brief history of my lotto experience](https://wap.saliu.com/bbs/messages/534.html) - **Barry Whittington** _1/18/2001._
-   I'm not speaking of the lottery being rigged in the same way as was done in Pennsylvania. It's certainly not rigged each and every week for one specific person to win. It is however rigged so that the prize money is distributed evenly in all regions.

-   [A brief history of my lotto, lottery experience](https://wap.saliu.com/bbs/messages/535.html) - _1/18/2001._
-   Barry, probably some people think you are too suspicious, perhaps paranoid, as far as the state lotteries are concerned. They surely think the same thing about me. Just read what I say about Internet casinos and…the state lotteries!

-   [Re: A brief history of my lotto experience](https://wap.saliu.com/bbs/messages/529.html) - **BigJer** _1/17/2001._
-   I don't think the lottery cares at all if there are any winners as they get the percentage for the state if nobody wins or if 10 people win. I think you need to better understand the odds involved in the lottery games you play.

-   [Re: A brief history of my lottery experience](https://wap.saliu.com/bbs/messages/531.html) - **Barry Whittington** _1/17/2001._
-   Do you ever let the lottery's computer generate your numbers? If you did you would see that they avoid giving certain Powerball or lotto numbers and obviously combinations of those numbers. They know ahead of time what Powerball numbers will be drawn and their computer portions out the lotto numbers accordingly.

![Forum, Message Board for Lotto, Lottery, Gambling: Archives.](https://wap.saliu.com/bbs/HLINE.gif)

Comments:  

![Bulletin Board: Analysis of Lottery Software, Lotto Systems, Strategies.](https://wap.saliu.com/bbs/HLINE.gif)

**[Home](https://wap.saliu.com/index.htm) | [Contact](https://wap.saliu.com/contact.htm) | [Search](https://wap.saliu.com/Search.htm) | [Help](https://wap.saliu.com/Help.htm) | [What's New](https://wap.saliu.com/WhatNew.htm) | [Software](https://wap.saliu.com/infodown.html) | [Odds, Generator](https://wap.saliu.com/calculator_generator.html) | [Contents](https://wap.saliu.com/content/index.html) | [Forums](https://forums.saliu.com/) | [Sitemap](https://wap.saliu.com/sitemap/index.html) | [Shop Online](https://wap.saliu.com/shopping.htm)**

![Forum messages VI for gambling, systems, lottery, lotto, software.](https://wap.saliu.com/bbs/HLINE.gif)
