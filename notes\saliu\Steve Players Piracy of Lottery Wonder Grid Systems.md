---
created: 2025-07-23T18:21:40 (UTC +08:00)
tags: [lotto,software,lottery,wonder grid,strategy,systems,Steve Player,piracy,fraud,lottery players,]
source: https://forums.saliu.com/steve-player-lottery-piracy.html
author: 
---

# <PERSON> of Lottery Wonder Grid Systems

> ## Excerpt
> Lottery players report fraud and piracy by <PERSON> of the lotto wonder-grid system developed by <PERSON>, founder of Lottery Strategy Science.

---
![Lottery player asks questions about wonder grid for pick 3 digit lotteries.](https://forums.saliu.com/HLINE.gif)

### I. [Questions on Pick 3 _Wonder Grid_ Lottery Strategy](https://forums.saliu.com/steve-player-lottery-piracy.html#Pick3)  
II. [Answer from <PERSON>, Creator of _Lottery Wonder Grid_](https://forums.saliu.com/steve-player-lottery-piracy.html#WonderGrid)  
III. [Criminally-Incorrigible Steve Player, Lottery System Scammer, Pirate](https://forums.saliu.com/steve-player-lottery-piracy.html#Scam)

![<PERSON>' a notorious scammer of lottery systems, also pirates <PERSON>'s lottery strategies.](https://forums.saliu.com/HLINE.gif)

### I. Questions on Pick 3 _Wonder Grid_ Lottery Strategy

_"Hi, axiomatic ones!

First of all thank you, <PERSON> <PERSON>iu, for your softwares and the main lottery strategies you've shared.

For 8 months I painstakingly back-tested my technique based on the WONDER GRID specifically for PICK-3. I'm not sure if it will work for any US pick-3 but I'm using it in the Philippines pick-3 since I'm stationed here. I have been applying this lottery strategy in real life for the last 7 weeks and no negative weeks so far.

I used the last 30 draws to generate the wonder grid. I'll leave it to the readers to determine the PIVOT DRAW or draw from where one should start creating the grid. Nevertheless, what I did is generate all the combinations from that set of 10. For example if line 2 says 2-1-8, then I get the remaining 5 combinations. i don't bet on doubles or triples (i.e. 122 or 333) simply coz its statistically least probable. I bet the combos starting from the 24th to the 27th draws after the pivot.

To Parpaluck, yeah I can send you a part of my winnings as a way to show my gratitude. You already have my email should you choose to send a mailing address or an account number. However, I will only do so every 8th month since the betting here is at P10/set of 3 (US$0.20) and pot is only P4,500 (US$95). If there are no duplicates that's P600 per set of 60 combinations and playing a succession of 4 lottery draws will cost P2400.

Every wire transfer will cost $25 domestic charge and another $4 for international charge. So it would be wiser to collect first and then send more than a marginal amount.

Hope this helps. I tried it on the Philippines pick-4 but the success is not that appealing just yet. I'm still working on that right pivot point for this game. I'll let you know once I've made significant progress.

_

_Until then, cheers! Good betting to you all!"_

### II. Answer from Ion Saliu, the True Creator of _Lottery Wonder Grid_

_**marge\_devela**_:

Thanks for your input, axiomatic colleague of mine. You are on the correct path regarding two of the three fundamental parameters of the wonder grid:

1) **parpaluck** – for _how many past draws to calculate_ the top pairings;  
2) **pivot draw** – at _what point in the past drawings file_ to generate the pairings.

There is a 3rd parameter: **potrocel** or the _cycle of fruition_. That is, for how many drawings to play the same set of top pairs.

It's OK to keep all your winnings to yourself. I was aiming at big time jackpot winners. But there are some legal sensitivities there. I hope to compensate some of my expenses and especially my time by my new adventure in offering advertising at my website.

When I saw the title of your post I thought you were the person who emailed me a few days ago. The person referred to alleged plagiarism by Steve Player of my _**lottery wonder grid**_.

Quote:  
_"I am in possession of a Steve Player system for pick 3 that is called "Pick-3 Winsheets" he uses a ton of mumbo jumbo to get around copyright infringement. After you weigh through all the bull it is nothing but a Saliu Wonder Grid. Too bad he is able to get away with that stuff. If you would like to have it I will send it to you."_

[![View screen shot of the wonder grid for pick-3 with winning records.](https://saliu.com/ScreenImgs/wonder-grid-pick-lottery.gif)](https://saliu.com/bbs/messages/grid.html)

Apparently, you are not the same sender I am referring to.

Best of luck to everybody, except for the likes of Steve (NOT)Player!

-   **[_**Wonder Grid Lottery Strategy Plays Pairs of Lotto Numbers**_](https://saliu.com/bbs/messages/638.html)**.

### III. The Criminally Incorrigible Steve Player, Lottery System Scammer, Pirate

The _cheated-by-Steve-plyer_ person sent me this follow-up:

_"I am a slow reader, so it took me ten minutes…to realize that \[Steve Player's “system”\] was not even a good copy of Wonder Grids. He covers it up with a pile of bull to hide the plagiarism. I feel like an idiot falling for this stunt ($79.95)."_

_Steve Pliers_ _**“Pick-3 Winsheets”**_ is not even a lottery system. It is a very laborious pencil-and-paper tracking of pick-3 pairings. Count the pick-3 pairs manually and writte them down in rows and columns!

That feature is a simple report in my free software Util-332, option _Satistics (Frequency)_. The program does first the frequency reporting for individual digits, then for pairs, then for boxes (3 digit combinations).

The big difference is in price, of course! Util-332 costs pennies, compared to US$79.95 in Pliers' rip-off! Not to mention that Util-332 does a hundred times more tasks than that…

The fundamental differences: Util-332 is much, much faster, it is less laborious and it is 100% precise. The paper-and-pencil maddening chore is highly error-prone. My lottery software also offers a pairing parameter the Steve Not-Player can't even comprehend: Skip median. Steve Player dangerously advises his customers to play a “hot” pick-3 pair, totally ignoring the median skip!

Another thing stolen from the wonder-grid concept: Add every pick-3 digit to a “hot” pair. The wonder grid is far more effective than that, for it is more solidly founded on mathematics. To make it even worse, Pliers' advises his victimized customers to play only boxes! For starters, playing boxed pick-3 adds an extra 2% to the house edge. But the worst is in the profit. You can read at SALIU.COM that I simply ignore the boxed hits of the wonder grid. The real profit comes in straight hits.

This Steve Player is incorrigible! I wrote about his rip-offs a few years back. Start your reading here:

-   **[Martingale Pick-3, Anyone? Flawed Lottery Systems by Steve Player](https://saliu.com/bbs/messages/188.html)**.

Why don't people google first? But, then, how can some people be so incredibly credulous? Why don't they demand more concrete facts about a system, before buying? I have read some web pages that offer lottery or gambling systems for sale. How can an individual, with just an average intellect, fall in such stinky swamps? On the other hand, Google, with its crazy _updates_, push my great Web pages down, while lifting to the top of search results a lot of GARBAGE in lottery systems, software, strategies! _C'est la vie!_

By the way –  
The integrated lottery software package PICK 332 offers a multitude of combination generating functions. You can use Power332 with the favorites/exclusions: from _1 of 5_ to _3 of 5_.

If you want one pick-3 pair to appear in every pick 3 set, use option _Favorites_, then _2 of 5_, then type the 1st digit of the pair, then the 2nd digit in the pair 4 times. You can do the opposite with exclusions (e.g. exclude one pick-3 pair from every lottery combination to play).

The _favorites / exclusions_ functions work far better with horse racing. I do something like: one of the top-5 favorite horses in every trifecta, but never three of them together (the payout is miserable!) I want also at least one of the five longest shots in the trifectas…

The integrated lottery software package PICK 332 was greatly upgraded and renamed <big>Bright3</big>. The Util-332 is still included in the package, but it was greatly superseded by the Super Utilities (on the main menu, out of 4 full-loaded menus).

There are now 7 <big>Bright3</big> software suites — for pick-3, pick-4 digit lotteries, 5-number lotto, lotto 6, horse racing, roulette, sports betting.

You think you saved lots of money by using my low-priced software and free systems. But why commit the sin of utilizing your savings in buying expensive garbage?!

Ion Saliu

[![Run the best 3-digit lotto software named Bright pick-3 programs.](https://saliu.com/ScreenImgs/pick31.gif)](https://saliu.com/lottery3-software.html)

-   **[Australian roulette system commits piracy of Ion Saliu's roulette system based on wheel half, sector, hemisphere](https://saliu.com/roulette-millions.html)**.
-   **[Piracy, scams on eBay regarding lottery and gambling systems](https://download.saliu.com/eBay-scams.html)**.
-   **[Piracy: Unauthorized Software Offering, Distribution](https://saliu.com/bbs/software-piracy.html)**.

![Run legitimate lottery software to create lotto systems based on pairs, not Steve Player piracy.](https://forums.saliu.com/HLINE.gif)

 **[![Forums for gambling software, strategy, systems, winning in real life casinos like in Atlantic City.](https://forums.saliu.com/go-back.gif) Back to Forums Index](https://forums.saliu.com/index.html)        [Socrates Home](https://saliu.com/index.htm)  [Search](https://saliu.com/Search.htm)**

![Exit the best site of software, systems, strategies, mathematics of lotto last digits.](https://forums.saliu.com/HLINE.gif)
