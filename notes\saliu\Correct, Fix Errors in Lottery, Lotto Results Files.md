---
created: 2025-07-24T23:08:11 (UTC +08:00)
tags: [lotto,lottery,software,errors,fix,correct,drawings,numbers,horse racing,pick lotteries,lotto,Keno,Powerball,Mega Millions,Euromillions,]
source: https://saliu.com/bbs/messages/2.html
author: 
---

# Co<PERSON><PERSON>, Fix <PERSON>rrors in Lottery, Lotto Results Files

> ## Excerpt
> Run software to correct, fix most errors in lotto, lottery data files, results, drawings files, since data entry is error prone.

---
**_PARSEL_** **~ special software to correct most errors in lottery and lotto drawings (results) files. It handles pick-3, pick-4, Quinto, lotto-4, lotto-5, lotto-6, lotto-7, Powerball, Mega Millions, CA SuperLotto, Euromillions, horse racing. It is now a component of all _Bright / Ultimate_ integrated software packages.**

![The software checks for correctness the results, past winning numbers files for many lotto and lottery games.](https://saliu.com/ScreenImgs/correct-lottery-results.gif)

Doing the taxes is a harshly taxing process, right? There is the other side of the story, for Al<PERSON><PERSON> <PERSON> only allows unities of two opposites. The tax preparation software has a feature named _find errors_. That feature made me think of software to find errors in lottery data (draws) files. There must have been tons of errors in the lottery files! I have received many error reports from LotWon users. I've seen some of the data files in question. But there was no need to go too far. My own lottery data files were bugged, each and every one of them! I didn't expect that many errors! The horseracing files, especially, were extremely erroneous. Some results were typed at the scene: the off-track wagering facility.

The errors in my lottery and gambling data files were the result of typing. It was _WEB: Web Era, Before_. I used to get the lottery results from leaflets published by the lottery commissions, from newspapers, live TV lotto drawings, over the telephone, etc. The more sources, the higher the risk to mistype. The Internet makes it a lot easier to get the lottery results and create/update the data files. Long live copy-and-paste! I showed on the 'Help' page one way of using the Internet to update the lottery data files. Another method is described in my article _[Dynamic or Static Filters in Lottery, Lotto Analysis Software](https://saliu.com/bbs/messages/919.html)_.

PARSEL is the first program that corrects most errors in lottery draw files. It handles 3-, 4-, or 5-digit daily or pick lotteries, 4-, 5-, 6-, 7-number lottos, Powerball, Mega Millions, CA SuperLotto, Euromillions, and horse racing. The program makes sure that the lottery drawing files comply with the LotWon requirements. For example, a _lotto 6/49 data file must have exactly 6 unique numbers per line; every number must be in the range 1 to 49; the file must not have any blank lines_. The program does not change the data files; rather, it creates a log file listing all erroneous lines and the specific problems. One type of problem cannot be trapped by the software: typos within the correct range. For example, the user typed 13 when the real lotto number drawn was 23. Only an attentive human can correct that!

The program will show only the first problem encountered in a line. Some lines can have several problems. You'll load the log file and the corresponding lottery data file. You'll check the erroneous lines one by one and make the corrections. I had situations when I ran PARSEL several times for the same data file. It is very important not to let errors slip to SORTING. Disaster strikes if one of your data files has EXTRA numbers in one line. The entire results file gets corrupted. Do not run SORTING before PARSEL displays the message _No errors found_.

You may also want to download the program SPACE34 to LotWon-correct the pick-3 and pick-4 draws listed at Pennsylvania Lottery Web site and other sites. The pick draws are listed as integers: _123_, instead of _1,2,3_ or _1 2 3_. Perhaps other sites list the pick draws the same way.

I've been recreating my data files from the ground up. I only use the Internet databases. I recommend everybody apply the same action. When the newly reborn lottery files are filled up, parse them with PARSEL. If no errors were found, use SORTING to sort the draws in ascending order or nicely format the pick lottery drawings.

_”For perfection and error are equivocally present in Almighty Number. May Its Almighty Parse our frivolous perfectionism so that is always pointing to our incredulous errors.”_

## [Resources in Lottery, Software, Systems, Lotto Wheels, Strategies](https://saliu.com/content/lottery.html)

-   The Main [_**Lotto, Lottery, Software, Strategy, Systems**_](https://saliu.com/LottoWin.htm) Page.  
    Presenting software to create free winning lotto, lottery strategies, systems based on mathematics. Get your lotto systems or wheels, the best lottery, lotto software, combinations, winning numbers.
-   [Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies](https://saliu.com/Newsgroups.htm).  
    Read a genuine analysis of Excel spreadsheets applied to lottery and lotto software development, systems, and strategies. Combining Excel analysis with powerful lottery and lotto software programmed by this author, _Parpaluck_.
-   [_**<u>MDIEditor Lotto WE</u>: Lottery Software Manual, Book, ebook, Help**_](https://saliu.com/MDI-lotto-guide.html).  
    ~ Also applicable to LotWon lottery, lotto software; plus Powerball/Mega Millions, Euromillions.
-   [_**Manual, Tutorial of <u>Lottery Software</u>, Lotto Apps**_](https://saliu.com/forum/lotto-book.html).
-   [**Skips Lottery, Lotto, Gambling, Systems, Strategy**](https://saliu.com/skip-strategy.html)
-   [Lottery Utility Software](https://saliu.com/lottery-utility.html): Pick-3, 4 Lottery, Lotto-5, 6, Powerball/Mega Millions/Thunderball, Euromillions.
-   [**Lotto wheels**](https://saliu.com/lotto_wheels.html) _**for lottery games drawing 5 6 7 numbers**_.  
    The most advanced theory of lotto wheels or reduced lottery systems. Get also original lotto wheels for lotto games drawing 5, 6, or 7 numbers: Balanced, randomized, and free.
-   Download [**lottery software, lotto software**](https://saliu.com/infodown.html).

![Keep lotto, lottery data files in good shape, in correct formats.](https://saliu.com/bbs/messages/HLINE.gif)

**[Home](https://saliu.com/index.htm) | [Search](https://saliu.com/Search.htm) | [New Writings](https://saliu.com/bbs/index.html) | [Software](https://saliu.com/infodown.html) | [Odds, Generator](https://saliu.com/calculator_generator.html) | [Contents](https://saliu.com/content/index.html) | [Forums](https://forums.saliu.com/) | [Sitemap](https://saliu.com/sitemap/index.html)**

![PARSEL software corrects, fixes most errors in lotto, lottery data files, results, drawings files.](https://saliu.com/bbs/messages/HLINE.gif)
