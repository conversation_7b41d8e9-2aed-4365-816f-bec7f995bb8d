---
created: 2025-07-24T22:10:02 (UTC +08:00)
tags: [lottery,software,best,programs,3-digit,games,world,3 winning digits,daily lottery,000,999,]
source: https://saliu.com/ultimate-pick3-software.html
author: 
---

# Ultimate Lottery Software 3 Winning Digits 000 to 999

> ## Excerpt
> Ultimate Lottery 3 is the best software for lottery games drawing 3 winning digits, from 000 to 999; the only lotto software program to win pick-3 daily.

---
## <u><i>Ultimate Software</i> for 3-Digit Pick 3 Lottery Games (from <i>000</i> to <i>999</i>)</u>

## By <PERSON>, ★ _Founder of Lottery Mathematics, Lotto Programming Science_

![Ultimate Lottery 3 is the only lotto software program to win pick-3 daily.](https://saliu.com/HLINE.gif)

![The pick-3 lottery software starts here with a comprehensive presentation.](https://saliu.com/HLINE.gif)

### 0\. [Introductory Notes to the _Ultimate Lottery Software_ for 3 Winning Digits; Download, Install, Run](https://saliu.com/ultimate-pick3-software.html#download)  
I. [Main Menu: Data Files, Reports, Strategies, Combination Generators](https://saliu.com/ultimate-pick3-software.html#main)  
II. [Menu #2: Skips, Delta, Lotto Decades, Last Digits, Wheels](https://saliu.com/ultimate-pick3-software.html#book-2)  
III. [Menu #3: Generate Sets, Pairings, Under/Over Strategies, Match Date (_911 NY_)](https://saliu.com/ultimate-pick3-software.html#menu-3)  
IV. [Menu #4: Specialty Programs for Probability, Odds, Combinatorics, Lexicographic Order, Wheeling](https://saliu.com/ultimate-pick3-software.html#book-4)

![Page instructs users how to download the 3-digit pick3 lottery application, install, run.](https://saliu.com/HLINE.gif)

## <u>0. Introductory Notes to the <i>Ultimate Lottery Software</i> for 3 Winning Digits; Download, Install, Run</u>

<big><b>ULTIMATE PICK-3</b></big> ~ version **2.0** ~ April 2017 ~ _software category 5.1_.-   This package is definitely the ultimate, never-to-be-duplicated software for lottery games drawing **3** winning digits from **000 to 999**. Specific data files of _past drawings_ or _past winning numbers_ make possible for the programs in this grand application to work with any pick 3 lottery in the world.
-   Downloading this collection of high-power programs requires the special _**Ultimate Software**_ membership. It is available only to members who paid for the _**Permanent Software Download**_ subscription. Click on the top banner to learn the terms, conditions, and availability.
-   Your membership page has clear instructions on downloading, installing, and running the software. All _**Ultimate Software**_ applications are available for download from your membership page. In addition, you can download a special lottery software utility to work with data files efficiently.

The presentation of the **four menus** and their applications and functions is next. The programs themselves have their own menus. The menus in all my programs are self-describing, but there are also specialized pages with more detailed information.

## <u>I. Main Menu: Data Files, Reports, Strategies, Combination (Set) Generators</u>

![Screenshot of 3-digit lottery book #1 of the best winning pick 3 ultimate software.](https://saliu.com/images/ultimate-lottery-software-30.gif)

This menu comprises the most important programs and functions of the _**Ultimate 3-Digit Lotto Software**_ bundle. There is a _sine qua non_ step: _Create the **data file**_ of _past drawings_ or _past winning lottery numbers_. You already know and fulfilled this step as a user of [_**Bright3 High-Powered Pick-3 Software**_](https://saliu.com/lottery3-software.html).

There is also a visual tutorial, with plenty of screenshots, applicable to all my lottery software:-   [_**Lottery Software Book**_](https://saliu.com/forum/lotto-book.html).

### _T_: Tutorial Online

It takes you to this Web page via your default Web browser. The online information is always up to date, as it is much prompter to edit a Web page than rewriting a manual bundled with the software.

### _E_: MDIEditor Lotto

Starts the separate GUI application **MDIEditor And Lotto WE** (for 64-bit Windows). The 32-bit OS version can be run from menu #3.

### Functions _I_, _E_: Edit Data Files

-   The _history file_ or _data file_ is always saved in text (ASCII) format and consists of the pick 3 digits previously drawn in the game. You can create the file simply by typing the winning digits drawing by drawing - one draw per line.
-   The data file must have exactly 3 digits per line. The digits must be separated by one or more blank spaces. You can also use commas _**,**_ as the field separators. The universal field separator should be the _blank space_.
-   The final step is to _sort_ the drawings (the lines in your lotto files) in _ascending order_.

Here is an example of the contents of my file for the _Pick 3_ lottery game after sorting and formatting (_PA-3_, evening drawings only):

```
<span size="5" face="Courier New" color="#c5b358">  5  7  5 (draw #1, or the most recent)
  9  7  5 (2nd recent drawing)
  7  8  2 (3rd recent draw)
  3  1  9
  8  9  1
</span>
```

… all the way down to the last line in the file, representing the oldest draw in the current game format.

-   I created a specialized piece of software to maximize the efficiency of working with data files: **LotteryData**. Please visit the following link, as it also deals with the important topics of lottery _strategies_ and the _winning reports_:
-   [_**Software to Manage Lottery Data Files**_](https://forums.saliu.com/lottery-strategies-start.html#software).

### _S_: Sort Data Files

The program sorts the lotto data files in _ascending_ order; it only formats nicely the _Pick 3_, pick-4 and horse racing files.

This task can be also performed specifically for 3-digit lottery data files in _**Super Utilities**_, option _T = Sort or Add-up Data Files_. I always use the **LotteryData** utility mentioned above.

For additional useful information, read:  

-   [_**<u>MDIEditor Lotto WE</u>: Lottery Software Manual, Book, ebook, Help**_](https://saliu.com/MDI-lotto-guide.html)
-   [**Help** on _**Software for Lottery, Lotto, Gambling: Download, Install, Run, Maintain Results Files**_](https://saliu.com/Help.htm)
-   [_**State Lottery, Lotto Drawings, Results, Past Winning Numbers: Online, over Internet**_](https://saliu.com/bbs/messages/920.html).

After creating, updating, and sorting the data files, one more important operation is required: **concatenation** of the _real_ data file with a _simulated_ data file.

The concatenation is done in _**Super Utilities**_, option _M = Make/Break/Position_, then _Make_, then option _1_ or _2_: _Make D3 from DATA-3 and SIM-3_. _D3_ is the final data file the software needs to generate reports, strategies, sets (combinations), etc.

-   This version of the _**Ultimate Lottery Software**_ requires a _D3_ data file of at least _130000_ (_one hundred thirty-thousand_) pick-3 straight sets (lines).

Here is the best procedure for creating the _SIM-3_ and _D3_ files to meet the size requirement of 130000 (one hundred thirty-thousand) straight sets (lines). Simply run _**Super Utilities**_ (**SoftwarePick3**), option _S = Files (SIM-3, count lines)_. Generate 130000 pick-3 straight sets. You'll have a SIM-3 file in seconds.

\*\* It is of the essence to use only **randomized** SIM files. Do not use, under any circumstances, SIM files in lexicographical order! Otherwise, the winning reports (W, MD) will turn into big headaches! Some filters will be so unusually high that several columns will run into one another. The _Check Strategy_ functions will NOT run any longer \*\*

### _W_: Winning Reports (W3, MD3 Files)

The creation of the winning reports, is of paramount importance. Press _W_ to generate the reports.

There are 2 options for the digit lottery games:

-   **S = Super** reporting (_W3.x_ and _MD3.x_): a D3 file against itself (like in the lotto software)
-   **F = Fixed** reporting (_WF3.x_ and _MF3.x_): a D3 file (e.g. _PA-3_) against a **fixed** file (**F3.1** to **F3.4**) ~ specific to the digit (pick3, pick-4) lottery software.
-   The fixed **F3** files were especially created by Ion Saliu. They consist of 6 layers each, each layer consisting of 1000 lines (straight pick 3 sets). Do not make any changes to those special F3 files (they are not easy to create!)
-   Type 200 (or more) for the length of each report. Type _D3_ for the name of the data file, and the default for _total drawings to analyze_.
-   Type or accept the defaults _W3.1_ to _W3.7_ and _MD3.1_ to _MD3.6_ for the report names in the **S** option.
-   Type or accept the defaults _WF3.1_ to _W3.6_ and _MF3.1_ to _MF3.6_ for the report names in the **F** option.
    
    The 7 + 6 W/MD and 6 + 6 WF/MF report files will show a number of parameters or FILTERS. Based on the reports, you feed the straight set generating program (**L** or **R**, main menu) with the filters. The process of setting filter values is known as _**strategy selection**_.
    
    For more information, read:  
    
-   [_**<u>MDIEditor Lotto WE</u>: Lottery Software Manual, Book, ebook, Help**_](https://saliu.com/MDI-lotto-guide.html)
-   [_**Filtering, Filters in Lottery Software, Lotto Software**_](https://saliu.com/filters.html)
-   [_**Lotto, Lottery Software Tutorial**_](https://saliu.com/bbs/messages/818.html) - _"My kingdom for a good tutorial!"_
-   [_**Software to Create Reports for Lottery Strategies**_](https://forums.saliu.com/lottery-strategies-start.html).
-   [_**The Best Ever Lottery Strategy, Lotto Strategies**_](https://software.saliu.com/lottery-strategy-lotto-strategies.html) — a must-read ebook on strategies.

### _O_: Sort Filter Reports by Column

The program sorts the _W3_, _MD3_, _GR3_, _DE3_, _FR3_, _SK3_ reports by column, helping the user see more easily the filters — e.g. filters of _wacky_ values (very low, or extremely high).  
The sorting is done by type of winning reports. The program also offers the correct choices (correct names) for filters (columns) to sort on.

For more information, read:  

-   [_**Filtering, Filters in Lottery Software, Lotto Software**_](https://saliu.com/filters.html)
-   [_**Cross-Reference Lottery, Lotto Strategy Files**_](https://saliu.com/cross-lines.html).

### _C_: Check Strategies (Filter Settings)

The function analyzes the 7 + 6 W/MD and 6 + 6 WF/MF reports to establish any type of strategy, between _minimum_ and _maximum_ values. The strategy report will show how many times a particular pick-3 strategy hit in the past lottery drawings. In other words, we check how a collection of filter settings would have fared in the past.

The program also creates the strategy files in the correct format (error-free). The strategy files are named _ST3.000_ (default). You need to remember the ST file names! It is a very good idea to create a simple text file where you record various lottery strategies: ST names and what the strategies are based on.

\*\* Potential errors when checking for lotto strategies.  
#1: Do NOT use _SIM_ files in _lexicographical order_. Always shuffle all your _SIM_ files.  
#2: Do NOT mix different game formats in your lotto files; that includes the file with your real lottery drawings and your _SIM_ulated files.  
#3: Sometimes some filters can get way, way out of range. The value can be wider than what I planned as the maximum length for that particular filter. I wanted the reports to be as readable as possible.

If #3 happens, the strategy checking functions will trigger errors. You can fix the error by opening your winning reports one by one in the text editor. You will notice that two neighboring columns are no longer separated by at least one blank space.

The culprit is the number in the column which is no longer aligned with numbers above and below. You need delete one (very rarely more than one) character at the end of that lengthy number. Make sure that there is one blank space between the numbers in the two columns and that they are properly aligned in the respective columns. Here is a visual example:

1234    23  
12345123 = strategy-checking error

Corrected W/MD file:  
1234   23  
1234 123

Repeat the procedure with all your lottery winning reports, as necessary. \*\*

### _H_: Strategy Hits in the Past

The program generates straight pick-3 sets for situations when a particular strategy (as in the _ST3\*.\*_ files) hit in the past drawings, or history of the game. The program needs an input file created by **Strategy3** (the previous function). The input file consists of the draw IDs for the hit situations. Otherwise, the user will manually input the filter settings and the drawings IDs.

### _L_: Lexicographic Straight Pick 3 Sets  
Option _R_: Randomized Pick-3 Straight Sets

This is the ultimate goal of LotWon lottery software: Generate winning _straight sets_. We keep records of past lottery draws (maintain data files), then analyze the data files and generate the winning reports. Then, we analyze the W reports and select filter values for our lottery strategies. We finally apply the lottery strategies to the combination (set) generators.

Each pick-3 set generating program has several functions of its own.

\* _L_ - Program name: **Lexico3** – it generates 3-digit lottery straight sets in _**lexicographical order**_. That is, the program starts the generation at lexicographic index #1 (_0, 0, 0_) and ends at the last index in the pick3 set (_9 9 9_).  
Functions in this program:

**N = Normal Lexicographic - NO ranges - NO favorites**

-   generates every straight set in the 3-digit game, from lexicographical index #1 to the last index.

**1 = 1 favorite digit - NO ranges**

-   generates every straight set, from lexicographical index #1 to the last index; also, each and every combination will contain one _favorite digit_ chosen by the software user.

**2 = 2 favorite digits - NO ranges**

-   generates every combination in the pick 3 game, from lexicographical index #1 to the last index; also, each and every straight set will contain _two favorite digits_ chosen by the software user.

**3 = 3 favorite digits - NO ranges**

-   generates every combination in the pick 3 game, from lexicographical index #1 to the last index; also, each and every straight set will contain _three favorite digits_ chosen by the lottery player.

**R = Combinations between positional RANGES**

-   generates every lottery straight set by positional _ranges_; e.g. digits in 1st position between 1 and 3; digits in 2nd position between 2 and 6; digits in 3rd position between 7 and 9.

**P = PURGE an output file of combinations**

-   takes an output pick-3 file previously generated and eliminates additional sets by applying further filtering.

\* _R_ - Program name: Combine3 – it generates 3-digit lottery straight sets in _**randomized manner**_. That is, the program starts and ends the generation anywhere in the lotto set, instead of lexicographically.  
Functions in this program:

**0 = NO favorite digits, NO shuffle**

-   generates randomized pick 3 sets, without any favorite digits, or clusters (shuffled combinations).

**1 = 1 favorite number - NO shuffle**

-   generates randomized pick3 sets; also, each and every random combination will contain _one favorite number_ chosen by the software user.

**2 = 2 favorite digits - NO shuffle**

-   generates randomized 3-digit lotto combinations; also, each and every random combination will contain _two favorite digits_ chosen by the lottery player.

**3 = 3 favorite digits - NO shuffle**

-   generates randomized 3-digit lottery sets; also, each and every random combination will contain _three favorite digits_ chosen by the user.

**S = SHUFFLE digits (the 10 digits in the pick 3)**

-   generates ALL lottery-3 digits in one cluster or group, 3 digits per line; i.e. a pick-3 lottery game will have clusters of 4 lines (straight sets), 3 digits per line; the last line will repeat 2 digits from previous straight sets.

\* Both programs above have two super functions before the combination generation starts:

-   **S = Super** generating
-   **F = Fixed** generating.

\* Both lottery programs above have two more functions that also eliminate unwanted combinations:  

-   inner filters (they eliminate around 95% of all combinations - enable it rarely);
-   LIE elimination: You noticed that, more often than not, your lottery output files do NOT have winning digits - not 3 straight winners, not 3 boxed winners, not even a front/back pair. You can open the _LIE_ file and generate straight sets that do NOT contain groups of pick 3 digits existent in the _LIE_ file.
    
    For more information, read:  
    
-   [_**Lottery <u>Strategy in Reverse or LIE Elimination</u>: Not-to-Win Leads to Not-to-Lose or WIN**_](https://saliu.com/reverse-strategy.html).
-   [_**Professors Play All Lotto Numbers and Win the Jackpot**_](https://saliu.com/all-lotto-numbers.html)

### _U_: Super Utilities

This piece of software bundles several utilities for pick 3 lottery games. Each sub-program in turn has several features of its own.

**S = Files (SIM-3, Count Lines)**

-   Simulate a SIM-3 file
-   Count lines in text files.

**D = Duplicates: Strip and Wheel**

-   The function retrieves the input data, finds duplicate lines, and eliminates them.

**F = Frequency Reporting by Digit**

-   The sub-program counts how many times each pick-3 digit came out in the specified range of past drawings. Then, it plots a _skip chart_ - number of drawings between hits. Also, the function saves to files the _most frequent pairings_ (_BEST3_) and the _least frequent pairings_ (_WORST3_).

**W = Check for Winners**

The function checks for groups of 3 winning pick-3 digits in two manners:-   1) an OUTPUT file against a data file with real draws
-   2) POOLS of digits against a data file with real pick-3 lottery drawings.  
    This function is the recommend method to check if your output files had any winners in past drawings, especially the drawings when you played the combinations.

**T = Sort or Add-up Data Files**

-   Nicely formats your 3-digit lottery files horizontally; plus, sorts in ascending order vertically, by multiple columns. The recommended method is the one I use. I referred to it at the beginning of this section: the **LotteryData** utility.
-   Add-up the digits in each lottery straight set in a data file to _sum-totals_. The recommended method is performed by the standalone application **Sums** presented in the next section (menu #2).
-   **G = Generate Combinations, Favorites, Least-Number-Groups**
-   The function generates pick-3 straight sets lexicographically. You can play favorite digits: 1, 2, 3; or NO favorites. You can also eliminate (least) singles and pairings. This function does not require a data file of past drawings.

**M = Make/Break/Position (D3, Positional Ranges, Break Long Lines to 3-Digit Straight Sets)**

-   Make D3 without LEAST3
-   Make D3 with LEAST3
-   Make D3 with LEAST & BEST3
-   _**Break 3+ straight sets to 3-digit lines**_
-   _**Generate pick-3 lottery straight sets by positional ranges (positional limits)**_.

**1 = Singles Rundown**

-   Calculate the frequency of every _single_ in a 3-digit lottery game;
-   sort the singles by frequency in descending order;
-   create a _least single_ file (singles that have not come out in the specified range of lottery drawings).

**2 = Pairs Rundown**

-   Calculate the frequency of every _pair_ (_pairing_) in a pick 3 lottery game;
-   sort the pairs by frequency in descending order;
-   create a _least pairing_ file (pairs that have not come out in the specified range of lottery drawings).

This important piece of _Pick 3 Lottery_ software is amply presented on its dedicated page:

-   [_**Lottery Software for Singles, Pairs, Triples, Quadruples, Quintuples, Sextuples**_](https://saliu.com/gambling-lottery-lotto/lotto-software.htm).

### The next three functions — _D, N, M_ — represent valuable new software, highly requested by members of the _Super Forums_.

### _D_: Dedicated LIE Elimination (_LieID_)

-   This program applies the **_LIE elimination_** feature introduced in the BRIGHT3 lottery software, specifically the combination generators. The main function generates the reports showing the levels of the ID filters for both the **_pairings_** and **_number frequencies_**.
    
    For ample information, you definitely want to read:
    
-   [_**Lottery Strategy in Reverse for Pairs, Number Frequency**_](https://saliu.com/lie-lottery-strategies-pairs.html).

### _N_: LIE StriNgs (_LieStrings3_)

-   The program shows the pick-3 draws as strings of skips, high/low, odd/even, decades, last digits — to be used in the _**LIE elimination**_ strategies. The string report files are to be used as INPUT to combination generating (**G**), with the filters being fed automatically. The _output_ files are then used as _LIE_ files in the combination generators (e.g. **Lexico3**).
    
    For ample information, you definitely want to read:
    
-   [_**Reversed Lottery Strategy for Lotto Decades, Last Digits, Odd Even, Low High, Skips**_](https://saliu.com/lie-lotto-strategies-decades.html).

### _M_: _Markov Chains_, Pairs, Followers

-   This complex piece of software deals with the famous _Markov chains_, random pairing, trendy digit frequencies (.e.g. _hot_, _cold_ lotto digits).
-   1) The software creates first reports for: Followers, Pairings, Number Frequencies.
-   2) The program generates lists of: Followers, Pairings, Frequencies — sorted in _descending order_, from _Hot_ to _Cold_ based on frequency.
-   3) The application generates pick 3 straight sets based on several methods related to this topic.
    
    For ample information, you definitely want to read:
    
-   [_**Theory of Markov Chains in Lottery, Lotto, Followers, Pairs, Software**_](https://saliu.com/markov-chains-lottery.html)
-   [_**Markov Chains: Lottery, Lotto, Software, Algorithms, Programming**_](https://saliu.com/Markov_Chains.html).

## <u>II. Menu #2: Skips, Delta, Lottery Decades, Last Digits, Wheels</u>

![Special additions to the best ever pick 3 lottery software include last digits and 2 deltas.](https://saliu.com/images/ultimate-lottery-software-31.gif)

### _D_: Deltas

This is another important addition to the grand collection _**Ultimate Pick-3 Lottery Software**_. The delta application shows the pick 3 drawings as strings of **deltas** or _differences_ between adjacent digits. The program creates first the delta report for a lottery data file (results). The program also generates 3-digit straight sets based on user-input deltas.

For ample information, you definitely want to read:

-   [_**Theory, Analysis of <u>Deltas</u> in Lottery Software, Strategy, Systems**_](https://saliu.com/delta-lotto-software.html)

### _S_: Skips, Decades, Last Digits, Frequencies

The program shows the pick-3 drawings as strings of skips, high/low, odd/even, increase/decrease from previous draw. The program also generates reports for _lottery-3 decades_, _last digits_, and a report of _frequencies_ from 3 groups: _hot_, _mild_, _cold_. You can use the skips, high/low, odd/even, decades, last digits, frequencies as _filters_ in the pick 3 set generators or the **purge** function. The lottery software can check if or when a strategy hit in the past. The _Strategy Hits_ function reports how many sets a particular pick-3 lottery strategy would have generated in winning situations.

For amplified information, you definitely want to read:

-   [**Reversed** _**Lottery Strategy for Lotto Decades, Last Digits, Odd Even, Low High, Skips**_](https://saliu.com/lie-lotto-strategies-decades.html)
-   [_**Lottery Decades: Software, Reports, Analysis, Strategies**_](https://saliu.com/decades.html)
-   [_**Lottery Strategy, Systems Based on Number Frequency**_](https://saliu.com/frequency-lottery.html)

### _Q_: Skips & Frequency Groups-Only

The program shows the pick-3 drawings as strings of **skips** and **frequencies**. Both parameters are divided into 3 groups of numbers: _hot_, _warm_, _cold_. You can use the _skips_ and _frequency groups_ as filters, or generate trifectas for the _**LIE Elimination**_ strategy.

This application is similar to function _S = Skips, Decades, Last Digits, Frequencies_. The _skips_, however, are divided in 3 groups, rather than dealing with them digit by digit. Please read the pages referred to above.

### _H_: Wheels from Files

This program takes an input file of pick-3 straight sets and converts the sets to _k of 3_ lottery wheel format - from _1 of 3_ to _3 of 3_; also, a number of combinations can be input in a random manner, without any wheeling.

The function is useful if you want to reduce the number of pick-3 sets in an output file previously generated. For example, you generated hundreds of combinations in **Combine3** (function _R = Random_ on main menu) with light filtering; you want to play only a handful of lottery pick-3 combinations that have no more than k digits in common (lotto wheeling); evidently, you settle for a lower-tier lottery prize.

Read more details:  

-   [_**Lottery Wheels for Lotto Games Drawing 5, 6, or 7 Numbers: Balanced and Randomized**_](https://saliu.com/lotto_wheels.html)
-   [_**Lottery Wheeling Software, Winning Report Generator**_](https://saliu.com/bbs/messages/wheel.html).

### _F_: Rank Lottery Digits by Frequency

This program generates frequency reports two ways: 1.- _Regardless of position_; 2.- _**Position by position**_. The pick-3 digits are ranked by frequency in descending order, from hot, to mild, to cold.

This multi-game application is amply presented on its dedicated page and in other specialty materials:

-   [_**Lottery Strategy, Systems Based on Number Frequency**_](https://saliu.com/frequency-lottery.html).

### _K_: Create Lotto, Lottery, and Gambling _Skip_ Systems

This program creates lottery and gambling systems based on two or three consecutive skips; the most recent skips make it into a particular system.

This multi-game application is amply presented on its dedicated page and in other specialty materials:

-   [**<u>Lottery Skip Systems</u>**](https://saliu.com/skip-strategy.html): _**Lotto, Powerball, Mega Millions, Euromillions**_.

### _M_: Sum-up Lottery Data Files and Games

The program calculates the number of lottery combinations that add-up to a _sum-total_. It also calculates the sums of each draw in lottery files, plus _root sum_, and _standard deviation_. You can generate such lottery combinations (straight sets) and save them. The program creates summary reports for the game: Every sum-total and its amount of sets, plus percentages... and much more.

See:

-   [_**Basics of a Lottery, Lotto Strategy Based on: Sums (Sum-Totals); Odd or Even; Low or High Numbers**_](https://saliu.com/strategy.html)
-   [_**Lottery, Lotto Sums, Sum-Totals**_](https://saliu.com/forum/lottery-sums.html)
-   [_**Lotto, Lottery Software to Calculate Sum-Totals, Odd-Even, Low-High Patterns**_](https://saliu.com/bbs/messages/626.html).

### _V_: Verify Data Files

This program _parses_ the lottery data files to make sure they comply with the format required by LotWon lottery software. It is highly recommended to check your data files periodically to make sure they are error-free — because _errare humanum est_.

For more information, read:

-   [_**Software to Correct Errors in Lottery, Lotto Results Files**_](https://saliu.com/bbs/messages/2.html).

### _C_: Check Winners

Check for winning digits in output files against real drawing lottery files. The combinations to play were saved first to output text files by the combination generators.

This task can be also performed specifically for 3-digit lotto data files in _**Super Utilities**_ (main menu), option _W = Check for Winners_ = the best method.

### _T_: Cross-Checking Lottery Strategies

The application writes to disk the lines of specified indexes in a file, usually a strategy file created by the function _C = Check Strategies_ (main menu). You created the _W\*.\*_ files in the Command Prompt LotWon lottery software. You also generated the statistical reports in **MDIEditor And Lotto WE**. You then created the strategy file for the _Stats_ function in **MDIEditor Lotto**. You want to see the same line numbers in _WS\*.\*_ files for a more comprehensive pick-3 strategy; i.e. possibilities to reduce even more the output to play.

Read more:

-   [_**Cross-Reference Lottery, Lotto Strategy Files**_](https://saliu.com/cross-lines.html).

### _U_: Text File Reverser

-   The program reverses the order in text files: the bottom becomes the top. Useful in arranging the lottery data files in the order required by LotWon lottery software.
-   Uncooperative lottery sites publish histories (drawings, results) in an unnatural order: The most recent drawing goes to the bottom, instead of the TOP. LotWon lottery software requires starting with the most recent draw, and go all the way down to the oldest drawing in that lottery game (bottom of file).
    
    For more detailed information, do read:
    
-   [_**Software to Reverse Order in Lottery, Lotto Results, Drawings Files**_](https://saliu.com/bbs/messages/539.html)
-   [_**The Definitive File Reverser, Shuffler, and Text Viewer Software**_](https://saliu.com/programming.html).

### _G_: User's Groups, O/E, L/H, Sums

This is a big pick-3 lottery program that works with groups of digits: _odd, even, low, high, frequency groups, sums_.

The application has a plethora of functions and its own Web page.

Please read the dedicated presentation:

-   [_**Lotto Software for Groups of Numbers/Digits: Odd, Even, Low, High, Sums, Frequency**_](https://saliu.com/lotto-groups.html)

## <u>III. Menu #3: Generate Sets, Pairings, Under/Over Strategies, Match Date (<i>911 NY</i>)</u>

![Ultimate lottery pick 3 has game specific programs to match drawing with its American date 911 NY.](https://saliu.com/images/ultimate-lottery-software-32.gif)

\* Axiomatic one, some of the functions below represent **standalone** programs. Other functions belong to older programs, but the names of the functions were less obvious to some software users. For example, several users asked me how to generate lottery sets (combinations) from groups or pools of digits. There are no programs with relevant names. Instead, these functions are well represented in large programs that provide a wide variety of functions. In this particular example, the combination generators from pools or groups of digits belong to the _**Super Utility**_ software.

### _N_: 3-Digit Sets from Pools of Numbers

Generate 3-digit lottery sets from pools or groups of digits.

Program name: **SoftwarePick3** (_**Super Utilities**_ on main menu), option _M: Make/Break/Position_.

The groups of lottery digits can be listed in files, in one line or multiple lines. For example, **SkipSystem** created for you a pool of pick-3 lottery digits. The file in text format consists of one line of 7 digits. You want to generate pick-3 straight sets from those 7 digits. Total of pick-3 lottery combinations of 7 digits: 7 ^ 3 = 343. In **SoftwarePick3**, you select option _M: Make/Break/Position_. Then, option _Break_, then option _2 = All 3 Digits Equally_.

The same function can also generate pick-3 straight sets from multiple lines of 3+ digits each. For example, you had 10 lines, for each digit of your pick-3 lottery game; each line has 6 other digits, as the best pairs of each of the 10 digits.

You can apply in this function a powerful filter: the _Least_ feature. Actually, you will eliminate all pairings in a special file _WORST3_ created by the same _**Super Utility**_ (option _F: Frequency_). It is recommended now, in strong terms, to use _WORST3_ instead of _LEAST3_.

For in-depth information, study:

-   [**<u>Lottery Skip Systems</u>**](https://saliu.com/skip-strategy.html): _**Lotto, Powerball, Mega Millions, Euromillions**_
-   [_**Analysis of Best Ranges for Lotto Number Frequency, Lottery Pairs, Pairings**_](https://saliu.com/lottery-lotto-pairs.html)
-   [_**Lottery**_ **Wonder Grid** _**Revisited: New Lotto Pairing Research, Software**_](https://saliu.com/bbs/messages/grid.html).

### _P_: 3-Digit Sets by Positions

Generate 3-digit lotto combinations from 3 lines of digits representing the 3 positions in a pick-3 combination.

Program name: **SoftwarePick3**, option _M: Make/Break/Position_.

You can generate pick-3 lottery combinations based on positions or _positional ranges_ (_positional limits_). If you run the statistical functions of my lottery software, you will see that the lotto digits are strongly biased regarding the position. You can read at SALIU.COM a lot about ranges or positional ranges in lottery. You will see living proof that the lotto digits follow the _**Fundamental Formula of Gambling (FFG)**_. Each position has lottery digits based on the _FFG median_.

You can apply in this function a powerful filter: the _Least_ feature. Actually, you will eliminate all pairings in a special file _WORST3_ created by the same **SoftwarePick3** (option _F: Frequency_).

For best information, read:

-   [_**Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies, Positional Ranges**_](https://saliu.com/Newsgroups.htm)
-   [**<u>Lottery Skip Systems</u>**](https://saliu.com/skip-strategy.html): _**Lotto, Powerball, Mega Millions, Euromillions**_
-   [_**Analysis of Best Ranges for Lotto Number Frequency, Lottery Pairs, Pairings**_](https://saliu.com/lottery-lotto-pairs.html).

### _F_: 3-digit Combinations from 3 Frequency Groups

Generate 3-digit pick 3 straight sets based on frequency: _hot_ digits, _mild_ digits, and _cold_ digits. Run first the _Frequency Reporting_ module, and then the combination generator (_lexicographic_ or _random_).

Application name: **SkipDecaFreq3** (function _D: Deltas_, menu #2), options: _L = Combinations, Lexicographic_; _R = Random Combinations_. Then, select the screen pertaining to the filters based on the 3 lottery frequency groups.

Read further:

-   [_**Lottery Strategy, Systems Based on Number Frequency**_](https://saliu.com/frequency-lottery.html).

### _D_: 3-Digit Sets by Decades

Generate 3-digit lottery sets decade by decade. I consider that a pick-3 lottery game has 3 "decades": _0-2_, _3-6_, _7-9_. This feature was meant to match the decade feature in the lotto programs.

Program name: **SkipDecaFreq3**, options: _L = Combinations_, _Lexico_; _R = Random Combinations_. Then, select the screen pertaining to the filters based on the pick-3 _decades_.

Please read much more:

-   [_**Lotto Decades: Software, Reports, Analysis, Strategies**_](https://saliu.com/decades.html).

### _G_: Pairing Reports, Custom Grids, Pick 3 Lottery Sets

This program generates a series of reports on the pairings of the pick-3 game; it also generates pairings in the manner of pick-3 combination generators in Bright-3.  
The _**Reports**_ option will show how many times each pairing came out; it also shows the winning pair reports in the manner of the W3/MD3 files created by **Report3**.

The app is especially useful in conjunction with the _**LIE**_ (_**reversed**_) strategy feature present in all lotto combination generators. The pairings do not lead to winning 3-digit lotto combinations the very next draw, far more often than not.

We can see that no lotto drawing has all top 2 pairings only. We can generate lotto combinations for the top 4 or 7 top pairings — and we will be wrong for sure. That output file qualifies as a _**LIE**_ file. We can apply, without possibility of error, the ID3 LIE filter. In a majority of cases, even ID2 will be a very safe filter.

Filter ID1 can never be applied. The grid files always contain all digits in the pick-3 game. If we create the _pure wonder-grid_ with the top-2 pairings, it will not hit the jackpot in several draws sometimes. The ID1 filter eliminates virtually all straight sets.

Also, you can create any pairing grid file in _**Super Utilities**_. You simply copy and paste any amount of digits from the file entitled _PAIRS3_. Or, you can create directly a file named _TOP3_. Copy and paste has the advantage of creating top files which are not necessarily the top pairs. For example, you copy and paste the digits corresponding to the pairs in the range 4 – 7.

Program name: **PairGrid3**.  
Replaces: _**SkipPair3**_ (still in the package — just type its name at the _**command prompt**_, after _e**X**iting_ the main menu).

For valuable information, read:

-   [_**Software News: Lotto, Lottery, Horse Racing, Pairs Programs, LIE Strategy**_](https://saliu.com/software-news.html).
-   [_**Lotto, Lottery Strategy in Reverse: Not-to-Win Leads to Not-to-Lose or WIN**_](https://saliu.com/reverse-strategy.html).
-   [_**Lottery Pairs and Repeat Probability**_](https://saliu.com/forum/lottery-pairs.html).

### _U_: Under/Over Strategies

-   A special pick-3 straight set generator based on the _FFG median_. Two FFG medians are considered:
-   **Under** = the latest one: from lottery drawing #1 to #693;  
    
-   **Over** = the next one: from draw 694 to draw 1387.  
    

This is a less potent lottery strategy; it works better at roulette and horse racing.

For more details, read:

-   [_**Winning Pattern for Under/Over Strategies**_](https://saliu.com/almighty_number.html)

### _M_: Match Date with Pick-3 Drawing

This app simulates random pick-3 combinations for each day of the year, in a continuous cycle. Then the program checks if a combination is equal to its corresponding date in the American format.

Inspired by the New York Lottery drawing _**9 1 1**_ on September 11, 2002 (date expressed as _9/11_ in the American date format).

For more detailed information, do read:

-   [_**Probability, Odds of New York Lottery Drawing 911 on September 11**_](https://saliu.com/bbs/messages/911.html).

### _S_: Add Spaces between Digits

This function inserts spaces between the digits of the pick 3, pick 4, Quinto (pick-5) drawings listed at some lottery Web sites. For example, _111_ becomes _1 1 1_; _9999_ becomes _9 9 9 9_.

### _B_: Concatenate Text Files, Make Big File

This function takes a number of text (ASCII) files and _concatenates_ (_combines_) them; i.e. it puts all the files into one. The function is useful when you need to combine multiple output lottery files (_OUT3_) or _LIE_ files and work with one file, instead of working with the files step by step (e.g. **_PURGING_** output pick-3 files).

## <u>IV. Menu #4: Specialty Programs for Probability, Odds, Combinatorics, Lexicographical Order, Wheeling</u>

![Many specialty programs enhance the best-ever software for 3-digit lottery daily games.](https://saliu.com/images/ultimate-lottery-software-33.gif)

### _S_: _Super Formula_, Definitive Probability, Statistics, and Gambling Software

_**Super Formula**_ is the definitive software for statistics, probability, odds, gambling mathematics... and much more. The functions are grouped in 12 categories. Each software category has its own detailed sub-categories. This unique application grew from the request by many people to create software to automate the calculations in the _**Fundamental Formula of Gambling (FFG)**_. _**FFG**_ discovered the most fundamental elements of theory of probability and also the Universe: The relation between the _**degree of certainty (DC)**_, _**probability p**_, and _**number of trials N**_.

There is ample information of the dedicated page:

-   [_**Formula Software for Statistics, Mathematics, Probability, Gambling**_](https://saliu.com/formula.html).

### _D_: _Birthday Paradox_ Strategies

I had written an original essay touching a few issues of creating winning systems from the _Birthday Paradox_, or the _probability of repetition_ (_duplication_). Such systems would apply to games of chance such as lotto, lottery, roulette... and more. There are lottery players and gamblers who now realize how important the probability of repetition is.

Can such mathematical knowledge be applied to gambling, especially lottery? I was skeptical when I first heard about it and was asked about it. The difficulty of achieving thorough understanding of this phenomenon was caused by a parameter I call _number of elements_. Indeed, the roulette numbers repeat. You can see them all the time, if the casinos turn on the electronic displays known as the marquees. But is there a rule, a mathematical formula that enables us to calculate the repeat probability?

I thought more deeply on this repetition fact. For example, I look at a sequence of 8 roulette numbers as an eight-element string. The degree of certainty is better than 50% that such string should contain one repetition (duplication). One of the eight numbers should be a repeat with a 50-50 chance. The same is true about lottery drawings. In this case, the element is the index of the combination (or set) drawn.

I studied some real data: Lottery drawings and roulette spins. I was somehow surprised to discover that repetition occurs close to that cutoff point of the 50-50 chance! I should also point out that the strength of the analysis and system creation is stronger at the beginning of the game. For lottery and lotto, the beginning is clear: A game starts with the first drawing of a game format.

Application names: **Collisions, BirthdayParadox**.

For specialty information, study:

-   [_**Applications of**_ **Birthday Paradox**: _**Lottery, Lotto, Roulette**_](https://saliu.com/birthday-paradox.html)
-   [**Birthday Paradox**: _**Combinatorics, Probability of Duplication, Coincidences, Collisions, Repetition**_](https://saliu.com/birthday.html).

### _P_: Generate All Possible Types of Sets

This piece of software generates ALL possible types of sets: _Exponents, permutations, arrangements, combinations_ - and _Powerball, Mega Millions, Euromillions combinations_. The software generates the sets in _lexicographical order_ or _randomly_. The sets can be _numerical_ or be composed of _words_ (_text_).

The _pick 3_ straight sets belong to the _exponents_ category (the order or position counts; the _exponents_ can have _duplicate_ elements).

For specialty information, visit:

-   [_**Combinatorics: Permutations, Combinations, Factorial, Exponents Generate**_](https://saliu.com/permutations.html)
-   [_**Comprehensive Generating: Exponents, Permutations, Arrangements, Combinations, Powerball, Mega Millions, Euromillions, Horse Racing**_](https://saliu.com/forum/numbers-words.html).

### _R_: Shuffle or Randomize Elements

_Shuffle_ lotto combinations files (text files); then go to the line equal to the _probability median_ (_FFG = 50%_). The program can also shuffle numbers in a highly randomized manner. There is a plethora of randomization functions in this program! The program can generate lotto combinations that mimic the official lottery drawings.

For specialty information, read:

-   [_**Random Numbers: Algorithms, Shuffle, Randomize, Software**_](https://saliu.com/random-numbers.html)
-   [_**Greatly Improved Shuffle, Randomize**_](https://forums.saliu.com/shuffle-randomize-software.html).

### _L_: Software for Lexicographical Order

The program finds the _lexicographical order_ (_index_, or _rank_) of a given set and conversely finds the set for a specified index (rank, or numeral, or lexicographic order). Applicable to these set types: _Exponents, permutations, arrangements, combinations, Powerball (5+1), Euromillions (5+2) combinations_.

For specialized information, read:

-   [_**Lexicographical Order: Index, Rank, Algorithms, Combinations, Permutations**_](https://saliu.com/lexicographic.html)
-   [_**Lexicographical Order: Lotto, Powerball, Mega Millions, Euromillions**_](https://saliu.com/forum/lexicographical.html).

### _B_: Generate Combinations inside _FFG Median Bell_

This multi-game software generates combinations in the _FFG median_ zone and inside the _bell (Gauss) curve_. The program can be used for: pick-3 4 lotteries, horse racing, lotto-5, -6, -7, Powerball, Mega Millions '5+1', Euromillions '5+2', roulette, sports betting, and soccer pools.

For specialized information, read:

-   [_**Median Bell Random Number, Combination Generator**_](https://saliu.com/median_bell.html)
-   [_**Winning Combinations Come Predominantly from Inside the FFG Median Bell**_](https://saliu.com/random-picks.html).

### _F_: Lexicographically-Index File

This program takes a lotto data file (drawings) and adds _indexes_ (_ranks_) to the corresponding combinations in the file. The indexes are calculated based on the combination lexicographical order or index for that lotto game.

For in-depth coverage, read:

-   [_**Combination Lexicographical Order, Index of Lotto, Lottery Drawings Files**_](https://saliu.com/combination.html)
-   [_**Combinations Generator: Any Lotto, Powerball, Mega Millions, Euromillions, Horseracing, Two-In-One Lotto Games**_](https://saliu.com/combinations.html).

### _O_: Probability, Odds Calculator

The probability software calculates all the _**odds**_ of any lotto game, including Powerball, Mega Millions and Euromillions games. For example, the odds of a pick 3 lottery game:

-   _straight_ pick: 1 in 1000
-   _boxed_: 1 in 220
-   _front/back pair_: 1 in 100.
    
    Read all the details:
    
-   [_**Calculate Odds, Probability to Win Lottery, Lotto, Powerball, Mega Millions, Euromillions**_](https://saliu.com/oddslotto.html)
-   [_**Probability, Odds, Formulae, Algorithm, Software Calculator**_](https://saliu.com/bbs/messages/266.html).

### _V_: Universal Lotto Combination Generator

Lotto software generates combinations for absolutely any type of lotto game, plus horse racing straight sets. Specifically to this program: the combinations can be generated in _steps_. That is, the user has the choice to generate lotto combinations with constant gaps or skips between them. For example, starting at the very top of a combination set (the lexicographical order #1), then step 90, the following combination generated will have lexicographic order #91,…, and so on, to the very last combination in the lotto set.

Read all the details:

-   [_**Combinations Generator: Lotto, Powerball, Mega Millions, Euromillions, Horse Racing, Two-In-One Lotto Games**_](https://saliu.com/combinations.html).

### _G_: _Wonder-Grid_ Checking for Lotto-3 Pairings

The lottery program checks the past performance of the _GRID3_ files. The program starts a number of draws back in the _DATA-3_ lotto file and creates 3 _GRID3_ files: for _10_, _20_, and _30_ drawingss. Each range of analysis N creates its own report file (_ChkGrid3.\*_).

It can be used well in conjunction with the _LIE_ (_reversed_ lottery strategy) feature in the combination generators. The _wonder grid_ skips more lotto drawings compared to the winning situations.

For more details, read:

-   [_**Lottery Wonder-Grid Revisited: New Lotto Pairing Research, Software**_](https://saliu.com/bbs/messages/grid.html)
-   [_**Lotto Wonder-grid: Lottery Reports and Lotto Software**_](https://saliu.com/bbs/messages/9.html).

### _K_: Wonder-Grid Pairs, Statistical Reports

This program creates all the statistical files for 3-drawing ranges - calculated by the _**FFG**_. The files have the extensions 1 to 3 in their names. The following files are created: _FREQ3_, _PAIRS3, GRID3, BEST3, WORST3, LEAST3_. _GRID3_ has each digit plus its 2 top pairs; _BEST3_ = the top 25% pairs; _WORST3_ has the worst 25% pairs; _LEAST3_ has the pairs with a frequency equal to 0. At the end, the program combines all _GRID3.\* BEST3.\*, WORST3.\*, LEAST3.\*_ to _GRID3, BEST3, WORST3, LEAST3_.

This program is useful in conjunction with the _LIE_ (_**reversed**_) strategy feature present in all lottery output generators. The pairings do not lead immediately to winning combinations more often than not.

For more details, read:

-   [_**Lottery Wonder-Grid Revisited: New Lotto Pairing Research, Software**_](https://saliu.com/bbs/messages/grid.html)
-   [_**Lotto, Lottery Software, Utilities**_](https://saliu.com/lottery-utility.html).

### _H_: Play-Last-N-Draws as a Lottery Wheel

The lottery utilities check for winning numbers in files of real drawings. A data file will be checked against itself as if playing the last N draws before current draw. For example, check the wins when I play the last 12 draws in a 5/43 lotto game. The odds of _2 of 5_ are _1 in 12_ as calculated by the **OddsCalc** super program. How many _2 of 5_ hits (and other hits) will be recorded if playing in 100 future 5/43 lottery drawings?

View:

-   [_**Wheeling All Lotto Numbers Formula: Play Last N Lottery Draws**_](https://saliu.com/wheel.html).

### _U_: Old Lotto-3 Utilities

This piece of lottery software was superseded by _**Super Utilities**_ for _pick three_ (option _U_ in the main menu). Just nostalgia, I guess! _O tempora! O mores!_

Program name: **Util332**.

You might want to read:

-   [_**Lotto, Lottery Software, Utilities**_](https://saliu.com/lottery-utility.html).

[

## <u>Resources in Lottery Software, Systems, Lotto Wheeling</u>

](https://saliu.com/content/lottery.html)

![Powerful programs for pick-3 lottery, Daily 3 Number help you tremendously to win the jackpot.](https://saliu.com/HLINE.gif)

**[Home](https://saliu.com/index.htm) | [Search](https://saliu.com/Search.htm) | [New Writings](https://saliu.com/bbs/index.html) | [Fundamental Formula](https://saliu.com/formula.htm) | [Odds, Generator](https://saliu.com/calculator_generator.html) | [Contents](https://saliu.com/content/index.html) | [Forums](https://forums.saliu.com/) | [Sitemap](https://saliu.com/sitemap/index.html)**

![Ultimate winning Pick 3 software was created by the Founder of lottery programming science.](https://saliu.com/HLINE.gif)
