using WonderGridLotterySystem

# Load lottery data
fm = FileManager()
data = read_data5_file(fm, "data/fan5.csv")
println("Loaded $(length(data)) lottery draws")

# Create Wonder Grid engine
engine = WonderGridEngine(data)

# Find favorable key numbers
key_numbers = select_key_numbers(engine)
println("Found $(length(key_numbers)) favorable key numbers")
println("First 10 key numbers: $(key_numbers[1:min(10, length(key_numbers))])")

# Generate combinations for first key number
if !isempty(key_numbers)
    key_number = key_numbers[1]
    result = execute_strategy(engine, key_number)
    
    println("\nWonder Grid Strategy Results for Key Number $key_number")
    println("=" ^ 50)
    println("Total combinations generated: $(length(result.combinations))")
    println("Generation time: $(round(result.generation_time, digits=3)) seconds")
    println("Estimated cost: \$$(result.estimated_cost)")
    
    println("\nFirst 10 combinations:")
    for i in 1:min(10, length(result.combinations))
        combo_str = join(result.combinations[i], "-")
        println("  $i: $combo_str")
    end
    
    # Show skip analysis for this key number
    skip_chart = generate_skip_chart(engine.skip_analyzer, key_number)
    println("\nSkip Analysis for Key Number $key_number:")
    println("Current skip: $(skip_chart.current_skip)")
    println("FFG median: $(round(skip_chart.ffg_median, digits=2))")
    println("Favorable timing: $(skip_chart.is_favorable)")
    
    # Show top pairings
    top_pairings = get_top_pairings(engine.pairing_engine, key_number, 0.25)
    println("\nTop 25% pairings for key number $key_number:")
    println("$(join(top_pairings, ", "))")
end