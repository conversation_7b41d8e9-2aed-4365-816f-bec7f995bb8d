include("src/WonderGridLotterySystem.jl")
using .WonderGridLotterySystem

println("Testing Statistics Engine")
println("=" ^ 30)

# Load lottery data
fm = FileManager()
data = read_data5_file(fm, "data/fan5.csv")
println("Loaded $(length(data)) lottery draws for analysis")

# Create statistics engine
engine = StatisticsEngine()

# Calculate frequency distribution
println("\nCalculating frequency distribution...")
freq_dist = calculate_frequency_distribution(engine, data)

# Compute statistical summary
summary = compute_statistical_summary(engine, freq_dist)

println("\nStatistical Summary:")
println("  Total draws analyzed: $(summary.total_draws)")
println("  Mean frequency per number: $(round(summary.mean_frequency, digits=2))")
println("  Median frequency per number: $(round(summary.median_frequency, digits=2))")
println("  Standard deviation: $(round(summary.standard_deviation, digits=2))")

# Show frequency distribution for all numbers
println("\nFrequency Distribution (Number: Frequency):")
for number in 1:39
    freq = freq_dist[number]
    percentage = round(100 * freq / (summary.total_draws * 5), digits=1)
    println("  $number: $freq times ($(percentage)%)")
end

# Find most and least frequent numbers
sorted_numbers = sort(collect(1:39), by = n -> freq_dist[n], rev = true)
println("\nMost frequent numbers:")
for i in 1:10
    number = sorted_numbers[i]
    freq = freq_dist[number]
    percentage = round(100 * freq / (summary.total_draws * 5), digits=1)
    println("  $i. Number $number: $freq times ($(percentage)%)")
end

println("\nLeast frequent numbers:")
for i in 1:10
    number = sorted_numbers[end-i+1]
    freq = freq_dist[number]
    percentage = round(100 * freq / (summary.total_draws * 5), digits=1)
    println("  $i. Number $number: $freq times ($(percentage)%)")
end

# Theoretical vs actual analysis
theoretical_freq = summary.total_draws * 5 / 39  # Expected frequency for uniform distribution
println("\nTheoretical vs Actual Analysis:")
println("  Theoretical frequency per number: $(round(theoretical_freq, digits=2))")
println("  Actual mean frequency: $(round(summary.mean_frequency, digits=2))")
println("  Difference: $(round(summary.mean_frequency - theoretical_freq, digits=2))")

# Calculate chi-square goodness of fit
function calculate_chi_square(freq_dist, theoretical_freq)
    chi_square = 0.0
    for number in 1:39
        observed = freq_dist[number]
        expected = theoretical_freq
        chi_square += (observed - expected)^2 / expected
    end
    return chi_square
end

chi_square_stat = calculate_chi_square(freq_dist, theoretical_freq)
println("  Chi-square statistic: $(round(chi_square_stat, digits=2))")

# Test enhanced statistical functions
println("\nTesting Enhanced Statistical Functions:")

# Test with raw data
raw_data = read_data5_raw(fm, "data/fan5.csv")
raw_freq_dist = calculate_frequency_distribution(engine, raw_data)
println("  Raw data frequency calculation: $(length(raw_freq_dist)) numbers processed")

# Test probability distribution
prob_dist = calculate_probability_distribution(engine, freq_dist)
println("  Probability distribution calculated")
println("  Sample probabilities: Number 1: $(round(prob_dist[1], digits=4)), Number 39: $(round(prob_dist[39], digits=4))")

# Test chi-square test
chi_square_result = chi_square_test(engine, freq_dist)
println("  Chi-square test:")
println("    Statistic: $(round(chi_square_result["chi_square"], digits=2))")
println("    Is random (95% confidence): $(chi_square_result["is_random_95"])")

# Test variance statistics
variance_stats = calculate_variance_stats(engine, freq_dist)
println("  Variance statistics:")
println("    Variance: $(round(variance_stats["variance"], digits=2))")
println("    Coefficient of variation: $(round(variance_stats["coefficient_of_variation"], digits=3))")

# Test percentiles
percentiles = calculate_percentiles(engine, freq_dist)
println("  Percentiles:")
println("    Min: $(percentiles["min"]), Q1: $(round(percentiles["q1"], digits=1)), Median: $(round(percentiles["median"], digits=1))")
println("    Q3: $(round(percentiles["q3"], digits=1)), Max: $(percentiles["max"])")

# Test outlier detection
outliers = identify_outliers(engine, freq_dist)
println("  Outlier analysis:")
println("    Outlier count: $(outliers["outlier_count"])")
if outliers["outlier_count"] > 0
    println("    Outlier numbers: $(outliers["outlier_numbers"])")
    println("    Outlier frequencies: $(outliers["outlier_frequencies"])")
end

# Test hot/cold number analysis
hot_cold = analyze_hot_cold_numbers(engine, freq_dist)
println("  Hot/Cold number analysis:")
println("    Hot numbers (top 20%): $(hot_cold["hot_numbers"])")
println("    Cold numbers (bottom 20%): $(hot_cold["cold_numbers"])")

# Test data quality assessment
quality_report = perform_data_quality_tests(engine, raw_data)
println("  Data quality assessment:")
println("    Quality score: $(round(quality_report["data_quality_score"], digits=1))/100")
println("    Is random distribution: $(quality_report["is_random_distribution"])")
println("    Coefficient of variation: $(round(quality_report["coefficient_of_variation"], digits=3))")

# Test moving averages (with smaller window for faster processing)
if length(raw_data) >= 200
    println("  Testing moving averages...")
    moving_avgs = calculate_moving_averages(engine, raw_data, 200)
    println("    Moving averages calculated for $(length(moving_avgs)) numbers")
    println("    Sample: Number 1 has $(length(moving_avgs[1])) moving average points")
end

# Performance test with large dataset
println("\nPerformance Test:")
large_data = generate_test_data(50000)
start_time = time()
large_freq_dist = calculate_frequency_distribution(engine, [draw.numbers for draw in large_data])
large_summary = compute_statistical_summary(engine, large_freq_dist)
elapsed_time = time() - start_time

println("  Processed $(length(large_data)) draws in $(round(elapsed_time, digits=3)) seconds")
println("  Processing rate: $(round(length(large_data) / elapsed_time, digits=0)) draws/second")

# Test correlation analysis
println("\nTesting correlation analysis...")
data1 = raw_data[1:500]
data2 = raw_data[501:1000]
correlation = calculate_correlation(engine, data1, data2)
println("  Correlation between two data sets: $(round(correlation, digits=3))")