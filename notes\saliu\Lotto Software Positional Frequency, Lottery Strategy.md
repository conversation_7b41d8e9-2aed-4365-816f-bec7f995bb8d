---
created: 2025-07-24T21:26:40 (UTC +08:00)
tags: [lotto,software,lottery,strategy,frequency,position,numbers,]
source: https://forums.saliu.com/lotto-software-position-frequency.html
author: 
---

# Lotto Software Positional Frequency, Lottery Strategy

> ## Excerpt
> Lotto software application FrequencyRank creates lottery strategies based on positional frequencies or ranks of numbers.

---
Indelibly-Axiomatic Colleagues of Mine:

There is one piece of grandiose lottery software well-known around the world; it is used a lot more illegally than legitimately. I got my instincts and also antennae! I am referring here to **FrequencyRank**.

The program generates frequency reports two ways:

1.- Regardless of position;  
2.- Position by position.

The numbers are ranked by frequency in _descending_ order, from _hot_ to _cold_.

Some of the most respected lotto software packages out there consider their strength to be the  **number frequency** as plotted by method #1 in **FrequencyRank**. Then, said software packages offer to the user the so-called _best numbers to play_: Usually, the top half of the lotto numbers based on frequency. I showed on several Web pages how weak that lottery strategy is.

It is infrequently that the winning lotto numbers come from the top-half of the  _hot_  numbers! 

How about a strategy based on positional frequency, as worked out by the function #2 in **FrequencyRank**? I believe it is a much more effective method of lottery number reduction. I did a test based on Pennsylvania 5-43 lottery game.

I ran **Frequency Rank** with the data file before the latest 5/43 lotto drawing I had downloaded: 10 15 17 3 40. I marked the winning numbers in the reports, and then I counted the respective frequency ranks.

Here is the situation for the  _regardless of position_ analysis:

-   9 33 19 28 1 7 23 34 25 30 40 43 15 3 27 2 17 32 6 20 39 11 12 5 26 8 13 4 31 38 41 16 37 22 42 35 24 29 18 21 10 14 36  
    
-   Ranks: 8, 11, 13, 17, 41

Here is the situation for the  _position by position_ analysis:

-   1 2 3 7 6 4 5 8 9 10 16 12 13 15 11 19 17 14 20 27 21 22 23 24 25 26 18 28 29 30 31 32 33 34 35 36 37 38 39 40 41 42 43
-   Rank for position 1: 10
-   11 12 9 15 13 7 17 5 4 14 18 19 8 3 23 24 21 16 6 25 26 28 22 20 27 10 30 33 29 1 31 32 2 34 35 36 37 38 39 40 41 42 43
-   Rank for position 2: 4
-   19 23 25 28 20 9 22 15 24 17 27 18 30 26 16 21 29 6 33 32 11 13 14 7 31 8 12 36 38 1 2 3 4 34 35 10 37 5 39 40 41 42 43
-   Rank for position 3: 10
-   34 32 31 33 27 30 25 26 23 28 35 20 37 41 38 29 19 21 22 13 16 17 36 24 8 39 12 15 4 5 18 6 7 1 9 10 11 2 3 40 14 42 43
-   Rank for position 4: 1
-   40 43 39 42 33 38 41 34 37 35 30 36 32 29 28 31 22 26 13 20 21 2 23 24 25 3 27 4 5 6 7 8 9 10 11 12 1 14 15 16 17 18 19
-   Rank for position 5: 1

The  _regardless of position_ analysis indicates a bad strategy as in the ordinary lotto software packages that ask you to pay with an arm and a leg. As I already pointed out many times now, it is best to reverse such lottery strategy and employ it as _**LIE elimination**_.

The  _position by position_ analysis indicates a better lotto strategy — it is not present in the ordinary lotto software packages. I copied the top 10 numbers in each position and created a simple text file with 5 lines. Such file should generate 10 ^ 5 = 100,000 lotto-5 combinations — at the maximum. Some numbers are common (e.g. 4, 5, 7, 9 for positions 1 and 2), therefore fewer combinations will be generated. In fact, I ran **Bingo 75** and the program generated 87,476 combinations.

-   I tested in _rec.gambling.lottery_ newsgroup the UK 6/59 lotto game. I generated the positional frequency report for some 100 draws. I left the set unchanged and checked it against lottery drawings as they occurred. The positional lottery strategy hit _6 of 6_ in 4 consecutive drawings, and in several others. The worst case-scenario was _5 of 6_.
-   Just _one-third_ of total possible lotto combinations provide the most wins _6 of 6_ or, worst, _5 of 6_. Yes, there are possible combinations starting at _50_ but they occur once in a blue moon! <u>The lotto positions are strongly biased.</u>

We can further reduce that amount of combosnations by applying my unique _**LIE elimination**_ method. We will apply the method to the output file with those 87,476 combinations.

Let's look at the top-10 in the  _regardless of position_ report. There is only one winning lotto number in that range. Therefore, we can apply the ID2 filter in the _**LIE elimination**_ function of **Bright5**. That ID2 alone could reduce all 87,476 combinations! Of course, several combinations overlap, so we will still be left with more _**LIE elimination**_ work to do.

All 5 winning numbers are **not** in the top 40 numbers of the lotto game. Therefore, we can generate combinations from 40 numbers saved to a one-line text file. We can use  _Make / Break_ in **Super Utilities**, or **BreakdownNumbers**. We got something easier to calculate now. Total combinations C(40, 5) = 658,008. That's how many combinations the Lie ID5 filter would eliminate at a maximum. Of course, many combinations overlap! But the final result would be a handful of combinations to play — if any! Because there will be cases when this lottery strategy will not hit — thus we save money.

The software is not ready to automate the task to higher degrees. The lottery software to generate the combinations is available. But the **reporting** software is not there. We need to see what  _frequency ranks_ the lottery drawings consisted of. The ranks separated by  _regardless of position_ and  _position by position_. Then automatically create the input files the same way as in **SkipSystem**… and then some… The shrewd reader with programming inclinations can't wait to go home and try my new lotto strategy…

### New Features in Lotto Software

Thanks Brian for bringing to the forefront the workings of the generating methods in my lotto software. I put an effort to take inventory of them all the other day. Overall, there are two methods:

1) Regardless of position; e.g. from ONE string consisting of lotto numbers;  
2) Position by position (from several ranges of numbers).

Method #2, as implemented in **BreakdownNumbers** and  _Make / Break_  functions in _**Super Utilities**_, is NOT true  _position by position_ generating. I knew something was missing in **Bright**. I discovered recently, when I posted in the Bingo thread. In fact, I had at my disposal **Range-5** that I released in 1999. I had upgraded the program to generate lotto combinations from NON-CONTIGUOUS ranges, as I presented in this thread. I thought the program was part of **Bright / Ultimate Software** – but it wasn't.

The combinations generated by **BreakdownNumbers** IGNORE the positions of the lotto numbers. You can have 100 lines of lotto numbers. The program will generate the combinations **regardless of position**.

For now, I will email you this interim **Range-5**. Also, an interim B5 to run the **Range-5** without typing the program name (menu #2).

I have always acknowledged how important your input was in creating the **LieID** software. Only you and I work with such lotto software. There are suckers out there who want me to believe that you collaborate with them in this new type of **LieID** software. I don't believe them – they only try to suck me up.

On the other hand, I feel like I have the duty to release the new and superior **Bright** packages. I discovered a few more quirks — they are extremely rare in my software in the past 2-3 years. The  _Any\_1_ filter is calculated totally accurately now by adding more drawings at the end of the draw-analysis range.

-   _**Updating the tutorials is the hardest part for me now.**_

I missed at least 6 situations in pick-3 while reworking important parts of my lottery software. I had started strategies based on very large TOT filters (over 4000). I just saw that TOT in all layers hit at least once recently. I'll miss a few more while making the duly necessary updates to the **Bright** packages. I know, you are good sport and will not get peed-off because I'll include in the upgrades the **LieID** software as well.

**However, in all fairness to you, Brian, the new **Bright** will not be available to other members. We are talking here NEW SOFTWARE, not upgrades. Therefore, a new type of membership should be made available only to current members.**

### Range-5 New Version: 3.01

The quirks in **Range-5** were not real. All screens showed only this option:  _Counting-Only_. In fact, the generating processes were the correct ones; e.g.  _\* NON-Contiguous Lexicographic Generation \*_. Now, you have it: version 3.01. See two screenshots below.

The function C = Contiguous Ranges (e.g.1 15,16 31) was already in **Lexico5**, part of Bright5. The function is:  _L = Lexicographic Combinations_ (main menu). The function generated accurately all lotto-5 combosnations from contiguous ranges of numbers. That was the original algorithm that I introduced in  _rec.gambling.lottery_ back in 1999, then developed extensively on this page:

-   [_**Excel Spreadsheets Lotto Lottery Software Systems Strategies Programming**_](https://saliu.com/Newsgroups.htm).

The function  _N = Non-contiguous Ranges (9 2 13)_  does what users expected from **BreakdownNumbers** or  _Make/Break_  functions in _**Super Utilities**_. Those lotto programs / functions did NOT represent **true**  _position by position_  generating. The numbers in the various input lines (e.g. pairings) appeared in position based on their size. That is, the smallest number appeared in position #1, while the largest number was placed in 5th position of the lotto combonation.

The function  _N = Non-contiguous Ranges (9 2 13)_  generates **strictly** _position-by-position_  lotto combinations from an input file. From the input file that I talked about in the first post (the top 10 lotto numbers in each position), the program will not generate a “combination” like:  _10 9 15 34 40_ . Mathematically, that is not a combination, but an arrangement. That is valid in horse racing trifectas, where the position counts; in lotto combination, the numbers are always sorted in ascending order.

-   1 2 3 7 6 4 5 8 9 10
-   11 12 9 15 13 7 17 5 4 14
-   19 23 25 28 20 9 22 15 24 17
-   34 32 31 33 27 30 25 26 23 28
-   40 43 39 42 33 38 41 34 37 35

Mathematically, an input file such this one should generate 10 \* 10 \* 10 \* 10 \* 10 = 100,000 5-number lotto combinations. That's the maximum and it is true only when there are no common numbers between the ranges. The input file above has overlapping numbers; total combinations generated: 61,774.

The program does not expect duplicate numbers in the same range. That is the normal situation, like in the case of the most frequent lotto numbers  _position-by-position_ . If the user has ranges with duplicate numbers in the same range, the software will generate duplicate combinations. The program does not strip duplicates, like **Breakdown Numbers** or  _Make / Break_  functions in _**Super Utilities**_. The reason is to preserve a very good speed. Besides, duplicate numbers should never appear on the same line (positional range).

![Original software generates lotto combinations position by position.](https://saliu.com/images/lotto-ranges.gif)

![Lotto software generates from ranges of numbers in 5 or 6 positions.](https://saliu.com/images/lotto-positions.gif)

### Range-5: New Version 3.1

The  _strip-duplicates_  function was added. The lines in the output files are sorted in **ascending order perfectly**. The incorrect situation as in the previous post shows now the lines sorted also in ascending order **vertically**:

-   1 12 24 38 41
-   1 12 35 36 41
-   1 12 35 38 41
-   1 15 21 23 36
-   1 15 21 23 41
-   1 15 21 31 36

The **random** combinations are also stripped off duplicates and the lines are sorted in ascending order **vertically**.

The  _Count-only_  functions could not be changed because they do not create output files. Sometimes, the  _Count-only_  functions will show more combinations (for the unusual situations with duplicate lotto numbers in the same line or range). These functions are meant for speedy calculations, not for real work.

I believe the latest version of **Range-5** does exactly what is supposed to do. Meanwhile, I realized that the great theory in **Skip System** did not have the proper combinations generators. I referred the users to the _**Super Utilities**_ but positional ranges did not generate combinations **strictly by positions**. Now, I want to add generating routines to all modules in **SkipSystem**. That includes the types of lotto games with scarcer software in my legacy: lotto-7, Powerball, Mega Millions, Euromillions. 

### Update for SkipSystem - Finalized Version

In the interim, this is an update to **Skips System**. The update was worked only on the 5- and 6-number lotto modules. Screenshots below.

I thought it might be useful to Brian and others who have a strong interest in lotto-5. This time, the program generates combinations from the system files. Also importantly, the combinations from the positional systems are **strictly position-by-position**. This is a real positional system for lotto-5 based on skips:

-   1 5 7 10
-   12 16 24 33
-   18 19
-   27 34 37 41
-   37 38 39 40 42 43

The combinations generated are totally correct from a positional point of view:

-   1 12 18 27 37
-   1 12 18 27 38
-   1 12 18 27 39
-   1 12 18 27 40
-   1 12 18 27 42
-   1 12 18 27 43
-   1 12 18 34 37
-   … 
-   10 16 19 34 43
-   10 16 19 37 38
-   10 16 19 37 39
-   10 16 19 37 40
-   10 16 19 37 42
-   10 16 19 37 43
-   10 16 19 41 42
-   10 16 19 41 43

Total combinations generated from the system: 304. The maximum amount of combinations would be 768 (4 \* 4 \* 2 \* 4 \* 6).

Meanwhile, **BreakdownNumbers** generates only 6 combinations, as the positions are ignored:

-   37 38 39 40 42
-   37 38 39 40 43
-   37 38 39 42 43
-   37 38 40 42 43
-   37 39 40 42 43
-   38 39 40 42 43

Again, be sure to use only **SkipSystem** for strict positional generating of combinations.

![Lotto combinations, Powerball, Mega Millions, Euromillions generate from groups of numbers by lines.](https://saliu.com/images/lotto-positions.gif)

![The lotto software also creates gambling systems from skips of numbers.](https://saliu.com/images/skip-lotto5.gif)

![This Powerball, Mega Millions skip system hit the jackpot several times.](https://saliu.com/images/combinations-lotto.5.gif)

-   [_**Lottery Strategy and Software Based on Number Frequency, Statistics**_](https://saliu.com/frequency-lottery.html).
-   [_**Skip Systems, Software: Lotto, Lottery, Powerball, Mega Millions, Euromillions**_](https://saliu.com/skip-strategy.html).
-   [_**Software to Generate Lotto Combinations with Favorite Lottery Numbers in Fixed Positions**_](https://saliu.com/favorite-lottery-numbers-positions.html).
-   [_**Powerball Strategy, Systems: Numbers, Drawings, Skips, Software**_](https://saliu.com/powerball-systems.html).

![Download your lottery software to create lotto systems based on positional frequency.](https://forums.saliu.com/HLINE.gif)

 **[![Forums for gambling software, strategy, systems, winning in real life casinos like in Atlantic City.](https://forums.saliu.com/go-back.gif) Back to Forums Index](https://forums.saliu.com/index.html)        [Socrates Home](https://saliu.com/index.htm)  [Search](https://saliu.com/Search.htm)**

![Exit the best site of software, systems, strategies, mathematics of lotto skips.](https://forums.saliu.com/HLINE.gif)
