#!/usr/bin/env julia

"""
Demonstration script for Wonder Grid Result Display and Export System
Shows how to use the display and export functionality
"""

include("src/result_display.jl")

"""
Demonstrate result display and export features
"""
function demo_result_display_system()
    println("🎯 Wonder Grid Result Display and Export Demo")
    println("=" ^ 70)
    
    # Create sample combinations for demonstration
    sample_combinations = [
        [1, 7, 15, 23, 39],
        [3, 11, 19, 27, 35],
        [2, 9, 16, 24, 38],
        [5, 12, 20, 28, 33],
        [4, 8, 17, 25, 36],
        [6, 13, 21, 29, 37],
        [1, 10, 18, 26, 34],
        [7, 14, 22, 30, 39],
        [3, 9, 15, 31, 38],
        [2, 11, 17, 25, 35]
    ]
    
    # Demo 1: Basic combination display
    println("\n📊 Demo 1: Combination Display Formats")
    println("-" ^ 50)
    
    println("Standard Display Format:")
    display_combinations(sample_combinations[1:5], "Sample Wonder Grid Combinations")
    
    println("\nGrid Display Format:")
    display_combinations_grid(sample_combinations, "Combinations in Grid Layout", 3)
    
    # Demo 2: Progress indicators
    println("\n⏱️  Demo 2: Progress Indicators")
    println("-" ^ 50)
    
    println("Simulating combination generation with progress tracking:")
    
    progress = ProgressIndicator(50, "Generating Combinations")
    
    for i in 1:50
        # Simulate work
        sleep(0.02)
        update_progress!(progress, i)
    end
    
    println("Progress indicator completed!")
    
    # Demo 3: Analysis results display
    println("\n📈 Demo 3: Analysis Results Display")
    println("-" ^ 50)
    
    sample_analysis = Dict{String, Any}(
        "key_number" => 13,
        "total_combinations" => length(sample_combinations),
        "generation_time" => 1.25,
        "analysis_date" => string(Dates.now()),
        "statistics" => Dict{String, Any}(
            "average_sum" => 95.2,
            "sum_range" => "70-125",
            "odd_even_ratio" => 0.58,
            "high_low_ratio" => 0.62,
            "consecutive_pairs" => 3,
            "number_distribution" => "well_balanced"
        ),
        "performance" => Dict{String, Any}(
            "combinations_per_second" => 8.0,
            "memory_usage_mb" => 12.5,
            "efficiency_score" => 8.7,
            "cache_hit_rate" => 0.85
        ),
        "recommendations" => [
            "Strategy shows excellent balance across number ranges",
            "Good distribution of odd/even numbers",
            "Consider testing with extended historical data",
            "Monitor performance with larger combination sets"
        ]
    )
    
    display_analysis_results(sample_analysis, "Wonder Grid Analysis Results")
    
    # Demo 4: Export functionality
    println("\n💾 Demo 4: Export Functionality")
    println("-" ^ 50)
    
    export_metadata = Dict{String, Any}(
        "key_number" => 13,
        "generation_method" => "Wonder Grid Strategy",
        "analysis_date" => string(Dates.now()),
        "total_combinations" => length(sample_combinations),
        "strategy_version" => "1.0"
    )
    
    println("Exporting combinations in multiple formats...")
    
    # Single format exports
    println("\n📄 Individual Format Exports:")
    
    export_combinations_csv(sample_combinations, "demo_combinations.csv", export_metadata)
    export_combinations_txt(sample_combinations, "demo_combinations.txt", export_metadata)
    export_combinations_json(sample_combinations, "demo_combinations.json", export_metadata)
    
    # Batch export
    println("\n📦 Batch Export:")
    
    exported_files = batch_export_combinations(
        sample_combinations,
        "demo_batch",
        ["csv", "txt", "json"],
        export_metadata
    )
    
    # Display export summary
    display_export_summary(exported_files, length(sample_combinations))
    
    # Export analysis report
    println("\n📋 Analysis Report Export:")
    export_analysis_report(sample_analysis, "demo_analysis_report.txt")
    
    # Demo 5: Result display manager
    println("\n📋 Demo 5: Result Display Manager")
    println("-" ^ 50)
    
    # Create different manager configurations
    managers = [
        ("Default", ResultDisplayManager()),
        ("JSON Output", ResultDisplayManager(true, "json", "json_results")),
        ("Silent Mode", ResultDisplayManager(false, "csv", "silent_results"))
    ]
    
    println("Result Display Manager Configurations:")
    for (name, manager) in managers
        println("\n$name Manager:")
        println("  Show Progress: $(manager.show_progress)")
        println("  Export Format: $(manager.export_format)")
        println("  Output Directory: $(manager.output_directory)")
        println("  Directory Exists: $(isdir(manager.output_directory))")
    end
    
    # Demo 6: Time formatting
    println("\n⏰ Demo 6: Time Formatting")
    println("-" ^ 50)
    
    time_examples = [5.2, 65.8, 125.3, 3665.7, 7325.1]
    
    println("Time Formatting Examples:")
    for time_val in time_examples
        formatted = format_time(time_val)
        println("  $(time_val) seconds = $formatted")
    end
    
    # Demo 7: File size analysis
    println("\n📊 Demo 7: Export File Analysis")
    println("-" ^ 50)
    
    exported_files_all = [
        "demo_combinations.csv",
        "demo_combinations.txt", 
        "demo_combinations.json",
        "demo_batch.csv",
        "demo_batch.txt",
        "demo_batch.json",
        "demo_analysis_report.txt"
    ]
    
    println("Exported File Analysis:")
    total_size = 0
    
    for filename in exported_files_all
        if isfile(filename)
            file_size = filesize(filename)
            total_size += file_size
            
            size_str = if file_size < 1024
                "$(file_size) bytes"
            elseif file_size < 1024^2
                "$(round(file_size / 1024, digits=1)) KB"
            else
                "$(round(file_size / 1024^2, digits=1)) MB"
            end
            
            println("  📄 $filename: $size_str")
        end
    end
    
    total_size_str = if total_size < 1024
        "$(total_size) bytes"
    elseif total_size < 1024^2
        "$(round(total_size / 1024, digits=1)) KB"
    else
        "$(round(total_size / 1024^2, digits=1)) MB"
    end
    
    println("\n📊 Total exported data: $total_size_str")
    
    # Cleanup demo
    cleanup_demo_files()
    
    println("\n✅ Result Display and Export Demo Complete!")
    println("=" ^ 70)
end

"""
Interactive demo for combination viewing
"""
function interactive_demo()
    println("\n🎮 Interactive Features Demo")
    println("=" ^ 50)
    
    sample_combinations = [
        [1, 7, 15, 23, 39],
        [3, 11, 19, 27, 35],
        [2, 9, 16, 24, 38],
        [5, 12, 20, 28, 33],
        [4, 8, 17, 25, 36]
    ]
    
    println("This demo shows interactive combination viewing features.")
    println("Available commands in interactive viewer:")
    println("  'n' - Next page")
    println("  'p' - Previous page") 
    println("  'g <number>' - Go to specific combination")
    println("  's <number>' - Search for combinations containing number")
    println("  'q' - Quit viewer")
    
    print("\nWould you like to try the interactive combination viewer? (y/n): ")
    response = strip(readline())
    
    if lowercase(response) == "y"
        println("\n🚀 Starting interactive combination viewer...")
        println("(Type 'q' to exit when you're done exploring)")
        
        interactive_combination_viewer(sample_combinations)
        
        println("👋 Interactive demo completed!")
    else
        println("👋 Skipping interactive demo")
    end
end

"""
Clean up demo files
"""
function cleanup_demo_files()
    demo_files = [
        "demo_combinations.csv",
        "demo_combinations.txt",
        "demo_combinations.json",
        "demo_batch.csv",
        "demo_batch.txt", 
        "demo_batch.json",
        "demo_analysis_report.txt"
    ]
    
    demo_directories = [
        "json_results",
        "silent_results"
    ]
    
    println("\n🧹 Cleaning up demo files...")
    
    # Remove files
    for filename in demo_files
        if isfile(filename)
            rm(filename)
            println("  🗑️  Removed: $filename")
        end
    end
    
    # Remove directories
    for dirname in demo_directories
        if isdir(dirname)
            rm(dirname, recursive=true)
            println("  🗑️  Removed directory: $dirname")
        end
    end
    
    println("✅ Cleanup completed!")
end

"""
Performance demonstration
"""
function performance_demo()
    println("\n⚡ Performance Demo")
    println("=" ^ 40)
    
    # Generate larger dataset for performance testing
    println("Generating larger combination set for performance testing...")
    
    large_combinations = Vector{Vector{Int}}()
    
    # Generate 1000 random combinations
    progress = ProgressIndicator(1000, "Generating Large Dataset")
    
    for i in 1:1000
        # Generate random combination
        combo = sort(rand(1:39, 5))
        while length(unique(combo)) != 5
            combo = sort(rand(1:39, 5))
        end
        
        push!(large_combinations, combo)
        update_progress!(progress, i)
    end
    
    println("\n📊 Performance Test Results:")
    println("  Generated combinations: $(length(large_combinations))")
    
    # Test export performance
    println("\nTesting export performance...")
    
    start_time = time()
    export_combinations_csv(large_combinations, "performance_test.csv")
    csv_time = time() - start_time
    
    start_time = time()
    export_combinations_txt(large_combinations, "performance_test.txt")
    txt_time = time() - start_time
    
    start_time = time()
    export_combinations_json(large_combinations, "performance_test.json")
    json_time = time() - start_time
    
    println("Export Performance:")
    println("  CSV export: $(round(csv_time, digits=3)) seconds")
    println("  TXT export: $(round(txt_time, digits=3)) seconds")
    println("  JSON export: $(round(json_time, digits=3)) seconds")
    
    # File size comparison
    csv_size = filesize("performance_test.csv")
    txt_size = filesize("performance_test.txt")
    json_size = filesize("performance_test.json")
    
    println("\nFile Size Comparison:")
    println("  CSV: $(round(csv_size / 1024, digits=1)) KB")
    println("  TXT: $(round(txt_size / 1024, digits=1)) KB")
    println("  JSON: $(round(json_size / 1024, digits=1)) KB")
    
    # Cleanup performance test files
    for filename in ["performance_test.csv", "performance_test.txt", "performance_test.json"]
        if isfile(filename)
            rm(filename)
        end
    end
    
    println("\n🧹 Performance test files cleaned up")
end

# Run the demo
if abspath(PROGRAM_FILE) == @__FILE__
    demo_result_display_system()
    interactive_demo()
    performance_demo()
end