MDIEditor Lotto WE 是一款功能全面的彩票分析軟體，其設計宗旨是為彩票分析師和玩家提供強大的數學工具，以分析歷史數據、生成策略並優化投注組合。它涵蓋了多種彩票遊戲，包括 Pick-3、Pick-4、5/6/7 號樂透、強力球、超級百萬、歐洲百萬、基諾和賽馬。以下是 MDIEditor Lotto WE 的主要功能與邏輯，以及它們如何應用於彩票策略：

### MDIEditor Lotto WE 的主要功能與邏輯

1. **統計分析與報告 (Statistical Analyzing and Reporting)**:
    
    - **頻率報告 (Frequency Reports)**：顯示每個號碼在指定歷史開獎範圍內的出現次數，並能區分熱號、中號和冷號。
        - **應用**：用於識別**最常出現的號碼 (熱號)** 和**最不常出現的號碼 (冷號)**。這些數據可以指導號碼選擇，例如選擇熱號作為「最喜歡的號碼」或「銀行家號碼」。頻率報告也能與 `LIE` 消除策略結合，排除頻率較低的號碼組合。
    - **跳躍報告 (Skip Reports)**：**「跳躍 (skip)」** 定義為特定彩票號碼兩次中獎之間的開獎次數。報告顯示每個號碼的最近兩次跳躍值，並包含每個號碼的**跳躍中位數 (Skip Median)**，這表示該號碼在多少次跳躍或更少次數內中獎的機率為 50%。
        - **應用**：跳躍報告是制定投注時機的關鍵工具。`SkipSystem` 軟體根據跳躍模式和 FFG 中位數生成策略。可以通過觀察跳躍趨勢來制定策略，例如當前跳躍值小於或等於中位數時才進行投注。跳躍報告也是 `LIE` 消除的良好候選數據。
    - **過濾器報告 (Filter Reports)**：這些報告顯示各種**過濾器的歷史值、中位數、平均值和標準差**。過濾器值會根據歷史開獎數據計算其效率。報告還會顯示每個過濾器值旁邊的 `+` 或 `-` 符號，表示該值相對於前一次開獎是增加還是減少。
        - **應用**：這些報告是策略制定的核心，用於設定過濾器的最小值和最大值。特別是用於識別**「古怪」過濾器值** (超出統計正常範圍的值)，這些值可能帶來豐厚利潤。過濾器可以大大減少要投注的組合數量。例如，`Ion_5` 過濾器若數據文件不足夠大，可能會顯示重複值，影響策略的準確性。軟體提供 `SortFilterReports` 工具按列排序報告，以便更容易發現策略和「古怪」值。
    - **Wonder Grid 報告 (`GRID*`)**：該軟體創建 **"wonder-grid" 文件**，顯示遊戲中每個數字及其**最常見的配對 (pairings)**。
        - **應用**：基於配對頻率來選擇最有潛力的號碼組合。例如，創建包含最常見的 5 對數字的 `wonder-grid`，雖然不會在每次開獎中都中獎，但可以作為 `LIE` 消除策略的基礎。
2. **優化組合生成 (Optimized Combination Generating)**:
    
    - MDIEditor Lotto WE 能夠根據用戶設定的過濾器值生成**優化後的彩票組合**。軟體內建的過濾器會在優化生成過程中自動運作。
    - **應用**：這是將分析結果轉化為實際投注的步驟。通過嚴格設定過濾器，可以**大幅減少需要投注的組合數量**，從而降低成本並提高中獎效率。
3. **策略設定與檢查 (Strategy Setting and Checking)**:
    
    - **策略設定**：策略是一組過濾器的設定，旨在從生成過程中排除組合。MDIEditor Lotto WE 允許用戶設定過濾器的最小值和最大值，通常會參考過濾器報告中的中位數。軟體會根據數據檔案的大小給出建議，例如 Pick-3 和賽馬 3 遊戲建議至少 10,000 行數據，樂透遊戲建議至少 200,000 行。
    - **策略檢查 (Check Strategies)**：軟體提供此功能來評估特定過濾器設定組合在過去開獎中的表現 (命中次數、跳躍模式等)。
        - **應用**：這對於驗證策略的有效性至關重要。通過回測，用戶可以了解他們的策略在歷史數據中的表現，從而調整過濾器設定以提高未來中獎的機率。
4. **清除輸出檔案 (`Purge` Previous Output Files)**:
    
    - 此功能允許用戶對先前生成的彩票組合文件進行**二次過濾**，以進一步減少組合數量。它可以處理 MDIEditor Lotto WE 或 `LotWon` 軟體生成的任何文本文件，甚至是彩票輪盤。
        - **應用**：`Purge` 是減少投注組合、控制成本的重要工具。例如，可以將 `SkipSystem` 生成的號碼池輸入 `Purge`，並應用 `MDIEditor Lotto` 中的過濾器。
5. **`LIE` 消除策略 (`LIE Elimination Strategy` 或 `Reversed Lottery Strategy`)**:
    
    - MDIEditor Lotto WE 和其他 `Bright/Ultimate` 套件中的軟體都實施了 `LIE` 消除功能。這是一種「逆向策略」，旨在故意設定預計不會中獎的過濾器，然後生成 `LIE` 檔案 (Output file will **NOT** have winners)。
    - `LIE` 檔案中的組合隨後會被「消除」(`purge`)，從而大大減少要投注的票數。
        - **應用**：適用於消除低概率組合，例如所有「5 個全中」組、低頻率模式、特定的跳躍模式、不活躍的數字組或「古怪」過濾器值產生的組合。例如，可以生成「熱號組合」作為 `LIE` 文件，因為它們很少能命中所有中獎號碼。
6. **數據檔案管理 (Data File Management)**:
    
    - MDIEditor Lotto WE 要求資料檔案只包含中獎號碼，不含日期、獎金等其他資訊。號碼之間需用逗號或空格分隔，且每行不應有空行。組合中的號碼必須按**升序排列** (例如 `1, 2, 3, 4, 5, 6`)。
    - **最新開獎結果必須位於檔案的最頂端 (第一行)**，而最舊的開獎結果則在底部。軟體提供 `UpDown` 等工具來反轉檔案順序以符合此要求。
    - 為了確保分析準確性，軟體要求使用**非常大的歷史數據檔案**（例如，樂透遊戲建議至少 200,000 行，6 位數樂透的某些過濾器甚至需要 1,200 萬次開獎結果）。這通常需要合併真實開獎數據 (`DATA*`) 和**大量的隨機模擬數據檔案 (`SIM*`)** 來創建大型的 `D*` 檔案。
    - `PARSEL` 工具用於檢查資料檔案的正確性，能夠找出檔案中的錯誤並指出問題所在的行。
        - **應用**：正確且足夠大的數據檔案是所有統計報告和策略分析的**基礎**。
7. **組合生成功能**：
    
    - **隨機組合生成 (Random Combination Generating)**：生成完全隨機的組合，模擬彩票銷售點提供的「快速選號」(Quick Pick)。
    - **序列 (字典) 組合生成 (Sequential/Lexicographic Combination Generating)**：按字典順序生成所有可能的組合。
    - **喜歡的號碼 (Favorite Numbers)**：允許用戶在組合中包含他們喜歡的號碼，可以選擇「不論位置」或「固定位置」。
        - **應用**：這對於那些有特定偏好數字的玩家非常有用，並且通過將熱門號碼或特定位置上表現最佳的號碼設為「固定號碼」，可以進一步優化組合。
8. **可擴展性與互操作性 (Expandability and Interoperability)**:
    
    - MDIEditor Lotto WE 可以與作者的其他軟體 (`LotWon`、`Bright/Ultimate` 套件) 結合使用。`FileLines` 等工具用於**交叉引用和合併**不同軟體創建的策略檔案，從而建立更全面的策略。
        - **應用**：這種互操作性允許用戶結合不同軟體提供的獨特分析和過濾器，以實現更精細和強大的策略。

總而言之，MDIEditor Lotto WE 透過其全面的數據管理、多樣化的統計報告（頻率、跳躍、過濾器、Wonder Grid）、靈活的策略設定、強大的組合生成和消除功能（包括 `Purge` 和 `LIE` 消除），以及與其他軟體的協同作用，為彩票策略的制定和優化提供了**堅實的數學和統計基礎**。