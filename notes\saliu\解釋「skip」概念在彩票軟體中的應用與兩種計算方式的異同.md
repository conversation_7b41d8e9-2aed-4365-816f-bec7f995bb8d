這份文件深入探討了「Skips Systems」軟體，一套專為樂透、彩票及其他賭博遊戲設計的數學策略工具。其核心概念在於「**跳號 (skip)**」，意指特定號碼兩次出現之間間隔的開獎次數，而這項策略的有效性主要依賴於「**賭博基本公式 (Fundamental Formula of Gambling, FFG)**」所預測的數字重複模式。該軟體能生成不同類型的跳號系統，包括不考慮位置或依位置排列的組合，並透過「**FFG 中位數 (FFG median)**」來判斷哪些號碼組合最有潛力。文件也強調，為了提升中獎機率並減少無效組合，必須結合其他強大的篩選工具，例如軟體內建的「**清除 (Purge)**」和「**LIE 排除 (LIE Elimination)**」功能，以優化彩票系統。

在彩票軟體中，「**跳躍 (skip)**」是一個核心概念，指的是**某個號碼或模式在兩次中獎（或出現）之間，所錯過（未出現）的開獎次數**。這個概念對於制定彩票策略，尤其是識別最佳投注時機和最佳化投注組合至關重要。

以下是「跳躍」概念在彩票軟體中的應用以及兩種主要計算方式的異同：

### 「跳躍」概念的應用

1. **識別投注時機**：透過分析號碼的跳躍模式，彩票玩家可以識別出那些**當前跳躍次數小於或等於賭博基本公式 (FFG) 中位數的號碼**，這些號碼被認為更有可能在近期內再次出現。
2. **過濾器設定**：
    - 跳躍過濾器允許用戶設定最大或最小的跳躍次數，以**排除在特定期數內未出現或過於頻繁出現的號碼組合**。
    - 例如，在 6/49 樂透中，選擇跳躍次數在 FFG 中位數或更少抽獎次數後重複出現的號碼，能顯著提高中獎機率，**甚至在 6/49 彩票中提高七倍中獎機率**。
3. **「謊言消除」(LIE Elimination) 策略**：跳躍過濾器也是「謊言消除」策略的重要組成部分。該策略旨在**有意排除預計不會中獎的組合**，例如在下一期開獎中極少出現的特定跳躍模式。
4. **動態分析**：FFG 強調彩票是一個動態過程，因此過濾器應動態調整。透過觀察跳躍過濾器數值的趨勢（例如連續三次增加後常會出現反轉），可以更精準地設定過濾器等級，從而**嚴格消除大量低機率組合**。
5. **生成最佳組合**：FFG 理論認為中獎組合主要來自 FFG 中位數附近的「鐘形曲線」區域。將組合生成範圍限定在此區域內，可以提高中獎率。

### 「跳躍」的兩種計算方式及異同

在 Ion Saliu 的彩票軟體中，「跳躍」有兩種主要的計算方式，這兩種方式在不同的軟體和語境下使用：

1. **MDIEditor Lotto WE 和 LotWon 彩票軟體（Command Prompt 軟體）的計算方式**：
    
    - **計算方法**：這種方式將**跳躍次數定義為兩次命中之間完全錯過的抽獎次數**。
    - **範例**：如果一個號碼在最近一次開獎中命中，而其前一次命中是在第 3 次抽獎中，那麼跳躍值被計算為 `1`。換句話說，如果號碼在「第 1 次」和「第 3 次」開獎中命中，則「第 2 次」開獎被「跳過」了，所以跳躍值為 1。因此，「跳躍值為 7」意味著號碼已經有 8 次開獎未中，因為從 0 到 7 共計 8 次。
    - **用途**：這種跳躍值在 LotWon 中主要作為**彩票過濾器**使用。
2. **SkipSystem 軟體的計算方式**：
    
    - **計算方法**：這種方式將**跳躍次數定義為兩次命中之間「經過」的抽獎總數，包括最後一次命中**。
    - **範例**：承上例，如果一個號碼在第 3 次抽獎中命中，然後在最近一次（第 1 次）抽獎中再次命中，那麼跳躍值會被計算為 `2` (3 - 1 = 2)。作者進一步解釋，如果一個號碼在 4 次抽獎前命中，那麼跳躍值就是 4 (即 4、3、2、1)。
    - **用途**：在 SkipSystem 中，跳躍代表的是**由 FFG 計算的機率參數**。這使得它成為直接應用 FFG 理論來建立策略的基礎。

### 異同與影響

- **差異根源**：兩種計算方式的差異在於**是否包含「最後一次命中」的抽獎作為計數的一部分**。LotWon 的方法是計算「錯過」的次數，而 SkipSystem 則計算「經歷」的次數。
- **策略影響**：
    - **FFG 中位數**：FFG 在計算確定性程度 (DC) 和試驗次數 (N) 時，FFG 計算的 N 值（即 FFG 中位數）是**不扣除 1 的**。因此，SkipSystem 的計算方式與 FFG 的數學基礎更為一致，即 FFG 計算出的 N 值直接代表了事件再次發生所需的試驗次數。
    - **過濾器設定**：對於 LotWon 和 MDIEditor Lotto WE 軟體中的過濾器設定，通常需要**減去 1** 以與 FFG 的 N 值保持一致。例如，如果 FFG 中位數為 25，那麼在 LotWon 中設定跳躍過濾器時，可能會設定為 24（因為「跳過」了 24 次開獎）。
- **數據報告**：理解這些差異對於正確解讀軟體生成的統計報告至關重要。例如，報告會顯示「跳躍中位數 (Skip Median)」，這表示在 50% 的情況下，號碼會在該跳躍值或更少的跳躍後命中。

總之，「跳躍」是 Ion Saliu 彩票數學理論中的一個動態且關鍵的參數，它透過不同軟體的計算方式，為彩票分析和策略生成提供了靈活而強大的工具。