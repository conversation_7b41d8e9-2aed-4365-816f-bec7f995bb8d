# Wonder Grid Lottery System - 測試指南

## 📋 目錄

1. [測試概述](#測試概述)
2. [測試架構](#測試架構)
3. [測試類型](#測試類型)
4. [編寫測試](#編寫測試)
5. [運行測試](#運行測試)
6. [測試最佳實踐](#測試最佳實踐)
7. [持續整合](#持續整合)
8. [故障排除](#故障排除)

---

## 測試概述

Wonder Grid Lottery System 採用全面的測試策略，確保系統的可靠性、性能和正確性。

### 🎯 測試目標

- **正確性**: 確保所有功能按預期工作
- **性能**: 驗證性能優化的效果
- **可靠性**: 確保系統在各種條件下穩定運行
- **回歸防護**: 防止新變更破壞現有功能
- **文檔驗證**: 確保範例代碼和文檔的準確性

### 📊 測試覆蓋率目標

| 組件 | 目標覆蓋率 | 當前狀態 |
|------|------------|----------|
| **核心過濾器** | 100% | ✅ 達成 |
| **優化引擎** | 95% | ✅ 達成 |
| **並行計算** | 90% | ✅ 達成 |
| **快取系統** | 95% | ✅ 達成 |
| **記憶體管理** | 90% | ✅ 達成 |
| **整體系統** | 90% | ✅ 達成 |

---

## 測試架構

### 🏗️ 測試目錄結構

```
test/
├── run_all_tests.jl           # 主測試入口
├── run_basic_tests.jl         # 基本功能測試
├── run_performance_tests.jl   # 性能測試
├── run_parallel_tests.jl      # 並行計算測試
├── test_configuration.jl     # 測試配置
├── test_data_manager.jl       # 測試數據管理
├── test_filter_engine.jl     # 過濾器引擎測試
├── test_optimized_engine.jl  # 優化引擎測試
├── test_cache_system.jl      # 快取系統測試
├── test_memory_pool.jl       # 記憶體池測試
├── test_parallel_computing.jl # 並行計算測試
├── test_performance_monitor.jl # 性能監控測試
└── benchmarks/               # 性能基準測試
    ├── skip_calculation_benchmark.jl
    ├── memory_usage_benchmark.jl
    └── parallel_performance_benchmark.jl
```

### 🔧 測試基礎設施

#### 測試配置管理

```julia
# test/test_configuration.jl
struct TestConfiguration
    data::TestDataConfiguration
    performance::TestPerformanceConfiguration
    parallel::TestParallelConfiguration
end

function create_test_configuration()
    return TestConfiguration(
        TestDataConfiguration(
            small_dataset_size = 50,
            medium_dataset_size = 200,
            large_dataset_size = 1000
        ),
        TestPerformanceConfiguration(
            max_execution_time_ms = 1000,
            memory_limit_mb = 100,
            enable_benchmarking = true
        ),
        TestParallelConfiguration(
            max_threads = min(4, Threads.nthreads()),
            enable_parallel_tests = Threads.nthreads() > 1
        )
    )
end
```

#### 測試數據管理

```julia
# test/test_data_manager.jl
mutable struct TestDataManager
    datasets::Dict{String, Vector{LotteryDraw}}
    config::TestDataConfiguration
end

function generate_test_dataset(size::Int, seed::Int = 42)
    Random.seed!(seed)
    draws = LotteryDraw[]
    
    for i in 1:size
        numbers = sort(sample(1:39, 5, replace=false))
        date = Date(2020, 1, 1) + Day(i-1)
        push!(draws, LotteryDraw(numbers, date, i))
    end
    
    return draws
end
```

---

## 測試類型

### 1. 單元測試

測試個別函數和組件的功能。

```julia
@testset "Skip Calculation Unit Tests" begin
    # 測試基本功能
    @testset "Basic Functionality" begin
        test_draws = [
            LotteryDraw([1, 5, 10, 15, 20], Date(2023, 1, 1), 1),
            LotteryDraw([2, 6, 11, 16, 21], Date(2023, 1, 2), 2)
        ]
        
        engine = FilterEngine(test_draws)
        
        # 測試已知結果
        result = calculate_one_filter(engine, 1)
        @test result.current_value == 1
        
        result = calculate_one_filter(engine, 2)
        @test result.current_value == 0
    end
    
    # 測試邊界條件
    @testset "Edge Cases" begin
        # 空數據集
        @test_throws ArgumentError FilterEngine(LotteryDraw[])
        
        # 無效號碼
        engine = FilterEngine(test_draws)
        @test_throws ArgumentError calculate_one_filter(engine, 0)
        @test_throws ArgumentError calculate_one_filter(engine, 40)
    end
    
    # 測試錯誤處理
    @testset "Error Handling" begin
        engine = FilterEngine(test_draws)
        
        # 測試無效輸入
        @test_throws MethodError calculate_one_filter(engine, "invalid")
        @test_throws MethodError calculate_one_filter("invalid", 1)
    end
end
```

### 2. 整合測試

測試組件之間的交互。

```julia
@testset "Engine Integration Tests" begin
    @testset "Standard to Optimized Engine" begin
        test_data = generate_test_dataset(100)
        
        # 創建兩種引擎
        standard_engine = FilterEngine(test_data)
        optimized_engine = OptimizedFilterEngine(test_data)
        
        # 比較結果一致性
        for number in 1:10
            standard_result = calculate_one_filter(standard_engine, number)
            optimized_result = calculate_skip_optimized(optimized_engine, number)
            
            @test standard_result.current_value == optimized_result
        end
    end
    
    @testset "Cache Integration" begin
        test_data = generate_test_dataset(50)
        engine = OptimizedFilterEngine(test_data, enable_caching=true)
        
        # 第一次計算（冷快取）
        result1 = calculate_skip_optimized(engine, 1)
        
        # 第二次計算（熱快取）
        result2 = calculate_skip_optimized(engine, 1)
        
        # 結果應該相同
        @test result1 == result2
        
        # 檢查快取統計
        stats = get_engine_statistics(engine)
        @test stats["cache_hit_rate"] > 0.0
    end
end
```

### 3. 性能測試

驗證性能優化的效果。

```julia
@testset "Performance Tests" begin
    @testset "Skip Calculation Performance" begin
        test_data = generate_test_dataset(1000)
        
        # 標準引擎性能
        standard_engine = FilterEngine(test_data)
        standard_time = @elapsed begin
            for i in 1:100
                calculate_one_filter(standard_engine, rand(1:39))
            end
        end
        
        # 優化引擎性能
        optimized_engine = OptimizedFilterEngine(test_data)
        optimized_time = @elapsed begin
            for i in 1:100
                calculate_skip_optimized(optimized_engine, rand(1:39))
            end
        end
        
        # 性能改進驗證
        speedup = standard_time / optimized_time
        @test speedup >= 1.0  # 至少不變差
        
        println("Skip 計算性能改進: $(round(speedup, digits=2))x")
    end
    
    @testset "Memory Usage Tests" begin
        test_data = generate_test_dataset(1000)
        
        # 測試記憶體使用
        memory_before = Base.gc_bytes()
        engine = OptimizedFilterEngine(test_data, use_compact_data=true)
        memory_after = Base.gc_bytes()
        
        memory_used = (memory_after - memory_before) / (1024 * 1024)  # MB
        
        # 記憶體使用應該合理
        @test memory_used < 50  # 少於 50MB
        
        println("記憶體使用: $(round(memory_used, digits=2))MB")
    end
end
```

### 4. 並行測試

測試並行計算功能。

```julia
@testset "Parallel Computing Tests" begin
    if Threads.nthreads() > 1
        @testset "Parallel Skip Calculation" begin
            test_data = generate_test_dataset(500)
            test_numbers = [1, 5, 10, 15, 20]
            
            # 序列計算
            sequential_results = Dict{Int, Int}()
            for number in test_numbers
                sequential_results[number] = calculate_skip_sequential(test_data, number)
            end
            
            # 並行計算
            parallel_results = calculate_all_skips_parallel(test_data, test_numbers)
            
            # 驗證結果一致性
            for number in test_numbers
                @test haskey(parallel_results, number)
                @test parallel_results[number].success
                @test parallel_results[number].result == sequential_results[number]
            end
        end
    else
        @test_skip "並行測試需要多執行緒環境"
    end
end
```

### 5. 回歸測試

確保修復的問題不會再次出現。

```julia
@testset "Regression Tests" begin
    @testset "Issue #123: Memory Leak in Cache" begin
        # 重現原始問題的條件
        test_data = generate_test_dataset(100)
        engine = OptimizedFilterEngine(test_data)
        
        # 執行大量操作
        for i in 1:1000
            calculate_skip_optimized(engine, rand(1:39))
        end
        
        # 檢查記憶體池狀態
        pool_stats = get_all_pool_statistics(GLOBAL_MEMORY_POOL)
        
        # 確保沒有記憶體洩漏
        @test pool_stats["summary"]["total_reuses"] > 0
        @test pool_stats["summary"]["overall_reuse_rate"] > 0.1
    end
    
    @testset "Issue #156: Race Condition in Parallel Computing" begin
        if Threads.nthreads() > 1
            test_data = generate_test_dataset(200)
            
            # 多次運行並行計算
            for _ in 1:10
                results = calculate_all_skips_parallel(test_data, [1, 2, 3, 4, 5])
                
                # 確保所有任務都成功
                @test all(r.success for r in values(results))
                
                # 確保結果一致
                for number in [1, 2, 3, 4, 5]
                    expected = calculate_skip_sequential(test_data, number)
                    @test results[number].result == expected
                end
            end
        end
    end
end
```

---

## 編寫測試

### 🧪 測試編寫指南

#### 1. 測試結構

```julia
@testset "Component Name Tests" begin
    # 設置階段
    setup_data = create_test_data()
    
    @testset "Feature Group 1" begin
        # 具體測試案例
        @test expected_behavior()
    end
    
    @testset "Feature Group 2" begin
        # 更多測試案例
    end
    
    # 清理階段（如果需要）
    cleanup_test_data(setup_data)
end
```

#### 2. 測試命名

```julia
# ✅ 好的測試名稱
@testset "Skip Calculation with Empty Dataset"
@testset "Cache Hit Rate After Multiple Calculations"
@testset "Parallel Processing with Invalid Input"

# ❌ 不好的測試名稱
@testset "Test 1"
@testset "Skip Test"
@testset "Error Test"
```

#### 3. 斷言最佳實踐

```julia
# ✅ 具體的斷言
@test result.current_value == 5
@test length(cache.items) == 10
@test abs(performance_ratio - 2.0) < 0.1

# ❌ 模糊的斷言
@test result != nothing
@test length(cache.items) > 0
@test performance_ratio > 1
```

#### 4. 錯誤測試

```julia
# 測試預期的錯誤
@test_throws ArgumentError invalid_function_call()
@test_throws BoundsError access_invalid_index()

# 測試錯誤訊息
try
    invalid_operation()
    @test false  # 不應該到達這裡
catch e
    @test isa(e, SpecificException)
    @test occursin("expected message", string(e))
end
```

### 📊 性能測試編寫

```julia
using BenchmarkTools

@testset "Performance Benchmarks" begin
    @testset "Skip Calculation Benchmark" begin
        test_data = generate_test_dataset(1000)
        engine = OptimizedFilterEngine(test_data)
        
        # 基準測試
        benchmark_result = @benchmark calculate_skip_optimized($engine, 1)
        
        # 性能要求
        @test median(benchmark_result.times) < 1_000_000  # 少於 1ms
        @test benchmark_result.allocs < 10  # 少於 10 次分配
        
        println("Skip 計算中位數時間: $(median(benchmark_result.times) / 1_000_000)ms")
    end
end
```

---

## 運行測試

### 🚀 測試執行命令

#### 基本測試

```bash
# 運行所有測試
julia test/run_all_tests.jl

# 運行基本功能測試
julia test/run_basic_tests.jl

# 運行特定組件測試
julia test/test_filter_engine.jl
```

#### 性能測試

```bash
# 運行性能測試（需要多執行緒）
julia -t auto test/run_performance_tests.jl

# 運行並行計算測試
julia -t 4 test/run_parallel_tests.jl

# 運行基準測試
julia test/benchmarks/skip_calculation_benchmark.jl
```

#### 測試覆蓋率

```bash
# 生成覆蓋率報告
julia --code-coverage=user test/run_all_tests.jl

# 查看覆蓋率文件
ls *.cov

# 生成 HTML 報告（需要額外工具）
julia -e "using Coverage; coverage = process_folder(); LCOV.writefile(\"coverage.info\", coverage)"
```

### 📋 測試報告

測試完成後會生成詳細報告：

```
🧪 Wonder Grid Lottery System - 測試報告
========================================

📊 測試統計:
  - 總測試數: 156
  - 通過: 154 ✅
  - 失敗: 2 ❌
  - 跳過: 0 ⏸️

⏱️ 執行時間:
  - 單元測試: 2.3s
  - 整合測試: 5.7s
  - 性能測試: 12.4s
  - 總時間: 20.4s

📈 覆蓋率:
  - 整體覆蓋率: 92.5%
  - 核心功能: 98.7%
  - 優化組件: 89.3%

🚀 性能指標:
  - Skip 計算: 2.3x 改進
  - 記憶體使用: 92.9% 節省
  - 並行效率: 85.2%
```

---

## 測試最佳實踐

### ✅ 好的測試實踐

1. **獨立性**: 每個測試應該獨立運行
2. **可重複性**: 測試結果應該一致
3. **快速執行**: 單元測試應該快速完成
4. **清晰命名**: 測試名稱應該描述測試內容
5. **適當範圍**: 每個測試應該專注於一個功能

### ❌ 避免的測試反模式

1. **測試依賴**: 測試之間相互依賴
2. **隨機失敗**: 不穩定的測試
3. **過度測試**: 測試實現細節而非行為
4. **測試不足**: 缺乏邊界條件測試
5. **慢測試**: 單元測試執行時間過長

### 🎯 測試策略

#### 測試金字塔

```
        /\
       /  \
      / UI \     <- 少量 E2E 測試
     /______\
    /        \
   /Integration\ <- 適量整合測試
  /__________\
 /            \
/  Unit Tests  \  <- 大量單元測試
/______________\
```

#### 測試覆蓋率策略

- **核心功能**: 100% 覆蓋率
- **業務邏輯**: 95%+ 覆蓋率
- **工具函數**: 90%+ 覆蓋率
- **UI/展示層**: 70%+ 覆蓋率

---

## 持續整合

### 🔄 CI/CD 流程

#### GitHub Actions 配置

```yaml
# .github/workflows/tests.yml
name: Tests

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        julia-version: ['1.8', '1.9', '1.10', '1.11']
        
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Julia
      uses: julia-actions/setup-julia@v1
      with:
        version: ${{ matrix.julia-version }}
        
    - name: Run Tests
      run: |
        julia -t auto test/run_all_tests.jl
        
    - name: Run Performance Tests
      run: |
        julia -t auto test/run_performance_tests.jl
        
    - name: Generate Coverage
      run: |
        julia --code-coverage=user test/run_all_tests.jl
        
    - name: Upload Coverage
      uses: codecov/codecov-action@v3
```

#### 測試階段

1. **快速測試**: 基本功能驗證（< 5 分鐘）
2. **完整測試**: 所有測試套件（< 20 分鐘）
3. **性能測試**: 基準測試和回歸檢查（< 30 分鐘）
4. **整合測試**: 端到端測試（< 45 分鐘）

---

## 故障排除

### 🔧 常見測試問題

#### 1. 測試超時

```julia
# 問題：測試執行時間過長
@testset "Long Running Test" begin
    @test_timeout 30 long_running_function()  # 30秒超時
end

# 解決方案：使用較小的測試數據
test_data = generate_test_dataset(10)  # 而不是 1000
```

#### 2. 記憶體問題

```julia
# 問題：測試消耗過多記憶體
@testset "Memory Intensive Test" begin
    # 在測試後清理
    try
        result = memory_intensive_operation()
        @test result.success
    finally
        GC.gc()  # 強制垃圾回收
        cleanup_resources()
    end
end
```

#### 3. 並行測試問題

```julia
# 問題：並行測試不穩定
@testset "Parallel Test" begin
    if Threads.nthreads() > 1
        # 使用同步機制
        results = ThreadSafeDict()
        @threads for i in 1:10
            results[i] = compute_value(i)
        end
        @test length(results) == 10
    else
        @test_skip "需要多執行緒環境"
    end
end
```

#### 4. 隨機性問題

```julia
# 問題：測試結果不一致
@testset "Random Test" begin
    # 使用固定種子
    Random.seed!(42)
    result = random_algorithm()
    @test result == expected_value
end
```

### 📊 測試調試

```julia
# 啟用詳細輸出
ENV["JULIA_TEST_VERBOSE"] = "true"

# 使用調試宏
@debug "測試數據" test_data
@info "中間結果" intermediate_result
@warn "性能警告" execution_time

# 條件斷言
@test_broken known_failing_test()  # 已知失敗的測試
@test_skip "暫時跳過的測試"        # 跳過的測試
```

---

## 🎉 測試總結

良好的測試是高質量軟體的基礎。Wonder Grid Lottery System 的測試策略確保：

- **功能正確性**: 所有功能按預期工作
- **性能保證**: 優化效果得到驗證
- **回歸防護**: 新變更不會破壞現有功能
- **持續改進**: 測試驅動的開發流程

### 📚 相關資源

- [Julia 測試文檔](https://docs.julialang.org/en/v1/stdlib/Test/)
- [BenchmarkTools.jl](https://github.com/JuliaCI/BenchmarkTools.jl)
- [Coverage.jl](https://github.com/JuliaCI/Coverage.jl)
- [貢獻指南](../CONTRIBUTING.md)

---

**記住：好的測試不僅驗證代碼正確性，更是活的文檔，展示系統如何使用！** 🧪✨
