# FFG (Fundamental Formula of Gambling) calculator

"""
FFGCalculator for median calculations and probability analysis
"""
struct FFGCalculator
    degree_of_certainty::Float64
    game_type::Lotto5_39
    ffg_cache::Dict{Int, Float64}  # Cache for FFG medians
    
    function FFGCalculator(dc::Float64 = 0.5)
        if !(0.0 < dc < 1.0)
            throw(ArgumentError("Degree of certainty must be between 0 and 1"))
        end
        new(dc, Lotto5_39(), Dict{Int, Float64}())
    end
end

"""
Calculate theoretical FFG median for Lotto 5/39 game with given degree of certainty
"""
function calculate_theoretical_ffg_median(calc::FFGCalculator)::Float64
    # For Lotto 5/39: probability of a number appearing = 5/39
    p = 5.0 / 39.0

    # FFG formula: N = log(1-DC) / log(1-p)
    # where DC is the degree of certainty and p is the probability
    # Both log(1-DC) and log(1-p) are negative, so the result is positive
    theoretical_median = log(1 - calc.degree_of_certainty) / log(1 - p)

    return max(1.0, theoretical_median)  # Ensure at least 1 (minimum skip)
end

"""
Calculate FFG median value for a specific number with degree of certainty adjustment
"""
function calculate_ffg_median(calc::FFGCalculator, number::Int, historical_data::Vector{LotteryDraw})::Float64
    # Check cache first
    if haskey(calc.ffg_cache, number)
        return calc.ffg_cache[number]
    end
    
    # Extract skip sequence for the number using SkipSystem method
    # (consistent with FFG theory)
    skips = Int[]
    last_occurrence = 0

    for (i, draw) in enumerate(historical_data)
        if number in draw.numbers
            if last_occurrence > 0
                # SkipSystem method: include the last hit in the count
                skip = i - last_occurrence
                push!(skips, skip)
            end
            last_occurrence = i
        end
    end
    
    ffg_median = if isempty(skips)
        # If number never appeared, use theoretical median
        calculate_theoretical_ffg_median(calc)
    else
        # Calculate empirical median of observed skips
        empirical_median = median(skips)
        
        # Apply degree of certainty adjustment
        theoretical_median = calculate_theoretical_ffg_median(calc)
        
        # Blend empirical and theoretical based on data confidence
        data_confidence = min(1.0, length(skips) / 20.0)  # More data = higher confidence
        
        # Weight the medians: more data = rely more on empirical
        weighted_median = data_confidence * empirical_median + (1 - data_confidence) * theoretical_median
        
        # Apply degree of certainty adjustment
        if calc.degree_of_certainty != 0.5
            # Adjust median based on degree of certainty
            # Higher DC = more conservative (higher median)
            # Lower DC = more aggressive (lower median)
            dc_adjustment = (calc.degree_of_certainty - 0.5) * 2.0  # Scale to [-1, 1]
            adjusted_median = weighted_median * (1.0 + dc_adjustment * 0.2)  # ±20% adjustment
            max(0.0, adjusted_median)
        else
            weighted_median
        end
    end
    
    # Cache the result
    calc.ffg_cache[number] = ffg_median
    return ffg_median
end

"""
Compute skip probability based on FFG and current skip position
"""
function compute_skip_probability(calc::FFGCalculator, current_skip::Int, median_skip::Float64)::Float64
    if median_skip <= 0
        return 0.5  # Default probability if median is invalid
    end
    
    # Calculate probability based on position relative to median
    skip_ratio = current_skip / median_skip
    
    if current_skip <= median_skip
        # Favorable timing - probability increases as we approach median
        # Use exponential decay: higher probability for lower skips
        base_prob = calc.degree_of_certainty
        position_factor = exp(-skip_ratio * 0.5)  # Exponential decay
        return min(0.95, base_prob * (1.0 + position_factor))
    else
        # Unfavorable timing - probability decreases as we exceed median
        excess_ratio = (current_skip - median_skip) / median_skip
        decay_factor = exp(-excess_ratio * 0.3)  # Slower decay for excess
        return max(0.05, (1.0 - calc.degree_of_certainty) * decay_factor)
    end
end

"""
Check if timing is favorable for a number
"""
function is_favorable_timing(calc::FFGCalculator, number::Int, current_skip::Int, historical_data::Vector{LotteryDraw})::Bool
    median_skip = calculate_ffg_median(calc, number, historical_data)
    return current_skip <= median_skip
end

"""
Calculate FFG median with custom degree of certainty
"""
function calculate_ffg_median_with_dc(number::Int, historical_data::Vector{LotteryDraw}, dc::Float64)::Float64
    custom_calc = FFGCalculator(dc)
    return calculate_ffg_median(custom_calc, number, historical_data)
end

"""
Update FFG calculations with new historical data (dynamic recalculation)
"""
function update_ffg_calculations!(calc::FFGCalculator, new_data::Vector{LotteryDraw})
    # Clear cache to force recalculation with new data
    empty!(calc.ffg_cache)
    
    # Pre-calculate FFG medians for all numbers with new data
    for number in 1:39
        calculate_ffg_median(calc, number, new_data)
    end
end

"""
Update FFG calculations with single new draw (dynamic recalculation)
"""
function update_ffg_calculations!(calc::FFGCalculator, historical_data::Vector{LotteryDraw}, new_draw::LotteryDraw)
    # Add new draw to historical data
    updated_data = vcat([new_draw], historical_data)
    
    # Update calculations with new data
    update_ffg_calculations!(calc, updated_data)
end

"""
Get FFG analysis for all numbers
"""
function get_ffg_analysis(calc::FFGCalculator, historical_data::Vector{LotteryDraw})::Dict{Int, Dict{String, Float64}}
    analysis = Dict{Int, Dict{String, Float64}}()
    
    for number in 1:39
        ffg_median = calculate_ffg_median(calc, number, historical_data)
        
        # Get current skip for probability calculation
        current_skip = 0
        for (i, draw) in enumerate(historical_data)
            if number in draw.numbers
                current_skip = i - 1
                break
            end
        end
        if current_skip == 0 && !(number in historical_data[1].numbers)
            current_skip = length(historical_data)
        end
        
        skip_probability = compute_skip_probability(calc, current_skip, ffg_median)
        is_favorable = is_favorable_timing(calc, number, current_skip, historical_data)
        
        analysis[number] = Dict{String, Float64}(
            "ffg_median" => ffg_median,
            "current_skip" => Float64(current_skip),
            "skip_probability" => skip_probability,
            "is_favorable" => is_favorable ? 1.0 : 0.0,
            "favorability_score" => is_favorable ? current_skip / ffg_median : ffg_median / current_skip
        )
    end
    
    return analysis
end

"""
Find optimal key numbers based on FFG analysis
"""
function find_optimal_key_numbers(calc::FFGCalculator, historical_data::Vector{LotteryDraw}, max_numbers::Int = 10)::Vector{Tuple{Int, Float64}}
    candidates = Tuple{Int, Float64}[]
    
    for number in 1:39
        ffg_median = calculate_ffg_median(calc, number, historical_data)
        
        # Get current skip
        current_skip = 0
        for (i, draw) in enumerate(historical_data)
            if number in draw.numbers
                current_skip = i - 1
                break
            end
        end
        if current_skip == 0 && !(number in historical_data[1].numbers)
            current_skip = length(historical_data)
        end
        
        # Only consider favorable numbers
        if current_skip <= ffg_median
            # Score based on how close current skip is to optimal (median/2)
            optimal_skip = ffg_median / 2.0
            score = abs(current_skip - optimal_skip) / ffg_median
            push!(candidates, (number, score))
        end
    end
    
    # Sort by score (lower is better)
    sort!(candidates, by = x -> x[2])
    
    return candidates[1:min(max_numbers, length(candidates))]
end

"""
Calculate FFG confidence interval for a number
"""
function calculate_ffg_confidence_interval(calc::FFGCalculator, number::Int, historical_data::Vector{LotteryDraw}, confidence_level::Float64 = 0.95)::Tuple{Float64, Float64}
    # Extract skip sequence
    skips = Int[]
    last_occurrence = 0
    
    for (i, draw) in enumerate(historical_data)
        if number in draw.numbers
            if last_occurrence > 0
                skip = i - last_occurrence - 1
                push!(skips, skip)
            end
            last_occurrence = i
        end
    end
    
    if length(skips) < 3
        # Insufficient data for confidence interval
        ffg_median = calculate_ffg_median(calc, number, historical_data)
        margin = ffg_median * 0.5  # 50% margin for insufficient data
        return (max(0.0, ffg_median - margin), ffg_median + margin)
    end
    
    # Calculate confidence interval using bootstrap method
    n_bootstrap = 1000
    bootstrap_medians = Float64[]
    
    for _ in 1:n_bootstrap
        bootstrap_sample = sample(skips, length(skips), replace=true)
        push!(bootstrap_medians, median(bootstrap_sample))
    end
    
    # Calculate percentiles for confidence interval
    alpha = 1.0 - confidence_level
    lower_percentile = alpha / 2.0
    upper_percentile = 1.0 - alpha / 2.0
    
    lower_bound = quantile(bootstrap_medians, lower_percentile)
    upper_bound = quantile(bootstrap_medians, upper_percentile)
    
    return (lower_bound, upper_bound)
end

"""
Validate FFG calculations against theoretical expectations
"""
function validate_ffg_calculations(calc::FFGCalculator, historical_data::Vector{LotteryDraw})::Dict{String, Any}
    theoretical_median = calculate_theoretical_ffg_median(calc)
    empirical_medians = Float64[]
    
    for number in 1:39
        empirical_median = calculate_ffg_median(calc, number, historical_data)
        push!(empirical_medians, empirical_median)
    end
    
    mean_empirical = mean(empirical_medians)
    std_empirical = std(empirical_medians)
    
    # Statistical tests
    deviation_ratio = abs(mean_empirical - theoretical_median) / theoretical_median
    is_reasonable = deviation_ratio < 0.5  # Within 50% of theoretical
    
    return Dict{String, Any}(
        "theoretical_median" => theoretical_median,
        "mean_empirical_median" => mean_empirical,
        "std_empirical_median" => std_empirical,
        "deviation_ratio" => deviation_ratio,
        "is_reasonable" => is_reasonable,
        "empirical_range" => (minimum(empirical_medians), maximum(empirical_medians)),
        "numbers_analyzed" => 39,
        "validation_passed" => is_reasonable
    )
end