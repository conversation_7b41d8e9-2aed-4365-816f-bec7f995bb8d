---
created: 2025-07-24T21:26:40 (UTC +08:00)
tags: [skip,gap,miss,hit,software,strategy,system,lotto,pick,lottery,Powerball,Mega Millions,Euromillions,roulette,sports,football,horse racing,numbers,]
source: https://saliu.com/skip-strategy.html
author: 
---

# Skips Systems Software: Lottery, Lotto, Powerball, Gambling

> ## Excerpt
> Special software creates lottery systems, strategies based on mathematics of skips for lotto, digit lotteries, Powerball, Mega Millions, Euromillions, horse racing, roulette.

---
First capture by the _WayBack Machine_ (_web.archive.org_) October 16, 2006.

The <big><u>skip</u></big> is at the heart of this system theory, more precisely <u>the last one/two/three <b>skips</b></u> for each lotto / lottery game, roulette, horse race, NFL week. _The **skip** is simply the number of drawings between two hits of a particular lottery number (digit)._ The skip is calculated here as _number of drawings back_. If, for example, a lotto number hit in the very last drawing and previously in drawing #3, then the skip is equal to 2: 3 - 1 = 2. We can say that the lotto number hit last time 2 draws back. You can also think the **skip** as the _gap between two hits_, or as _hit-and-miss count_.

This method of calculating the skip is slightly different from the method applied in **MDIEditor Lotto WE** and in the **LotWon lottery software** (the _**Command Prompt**_ software; e.g. **Bright6**). In _**LotWon**_, the skip is calculated as the absolute number of draws skipped between hits. In the example above, the skip is 1. The _LotWon skip_ is always less 1 than the skip calculated by **SkipsSystem**.

The difference is well thought out. The skip in LotWon acts as a lottery filter. In **Skip System**, the **skip** represents a probability parameter calculated by the _**Fundamental Formula of Gambling (FFG)**_.

Once upon a time, I created a special [_**lotto system for Powerball**_](https://saliu.com/powerball-systems.html), by running software to generate skips and automatically generate systems based on skips below the FFG median. I wrote on that Web page:

_****Skip System** handles the following lotto and lottery games: 3-, 4-, 5-digit (Quinto) pick lotteries; 4-, 5-, 6-, 7-number lotto jackpot games; Powerball, Mega Millions (_5+1_), and Euromillions (_5&2_). It also works with trifectas in horse racing; also roulette, and NFL football parlays.**_

[_**Home of Notepad++**_](https://notepad-plus-plus.org/) - free download.

I made sure Notepad++ runs under both 32-bit and 64-bit Windows. These are the program locations (where it should be installed):  
**C:\\Program Files\\Notepad++\\notepad++** (32-bit);  
**C:\\Program Files (x86)\\Notepad++\\notepad++** (64-bit).

![The main software function creates positional skip systems for lotto, lottery, Mega Millions.](https://saliu.com/HLINE.gif)

## <u>Function S = Create the <i>Skip Systems</i></u>

This is the original procedure in **SkipSystem**. It takes a lottery data (results) files and creates text files named **FFG\*.SYS**. It is recommended to use a D\* type data file: A real lottery draw file on top of a SIMulated file of combinations. The reason is the size of the file. If your file with real lottery draws is small (under 100 lotto combinations), the skip analysis may not be as precise as desired. If your real results file has in the neighborhood of 200 drawings, you can get an accurate skip report.

The software creates two types of skip systems:_**-   1) One-line strings or systems regardless of position (two-lines for Powerball, Mega Millions, Euromillions);**_-   _**2) Multi-line strings or positional systems = one line for each number position (e.g. 5-lines for a 5-number lotto game).**_

![The SKIP systems are just a part of the best lottery software in the world - ever.](https://saliu.com/ScreenImgs/skip-systems-powerball.gif)

These are the first two values of the skip report that starts every lotto, lottery numbers. For example:

Number: 4  
Any Position: 3 5 17 3 7 7 10 5 3 11 1 6 6 = the skips

The first two entries are 3 and 5. Those are the most recent skips for lotto number 4.

The main [**_lottery strategy_**](https://saliu.com/LottoWin.htm) page starts with the simplest of lotto systems based on skips and pools (groups) of numbers. There are also other systems out there (very expensive, though, such as Gail Howard's) based on the first skip or the two most recent skips of each lotto number. For example, you might have read recommendations of this nature. _“Select lotto numbers that hit within the last five or so drawings and also hit in the last 10 draws or so.”_ In this case, you would select lotto numbers with the first two skips in the form: 3, 5; or 4, 5; or 2, 1; or 4, 3; or 5, 5.

Well, such a lotto strategy is not standing on strong mathematical grounds. It doesn't explain why the magical numbers 5 and 10?

That's where the _**Fundamental Formula of Gambling (FFG)**_ steps in. The _**FFG**_ has discovered that each and every lotto number repeats, in 50%+ of all its hit situations, after a number of draws equal to the _FFG median_.

I went one step further. It is one of my discoveries in casino gambling. I look at the first two skips (the two most recent skips). Instead of selecting lottery numbers that show each of the two most recent skips less than or equal to the FFG median, I _SUM UP the skips_. In the previous example, lotto #4 started with the skips 3 and 5. Each skip is under FFG median. One possible lottery system will select #4 as being playable. The lotto #4 is also playable because 3+5=8; the result is less than the double amount of the FFG median. Double FFG median is equal to 12 for the lotto 6/49 game.

The lottery system based on the **double FFG median** is clearly more frequent than a system based on each of the two skips being under the FFG median. In the example above:

Lotto Number: 4  
Any Position: 3 5 17 3 7 7 10 5 3 11 1 6 6  
\* Automatic System = Double FFG Median of 12: 5 times in 13 hits (38%)  
\* Your System Hits <= 6 6: 3 times in 13 hits (23%)

<= 6 6: describes a system where the user sets the first skip to less than or equal to 6 (FFG median), and the second skip at the same level.

Lotto Number: 9  
Any Position: 4 7 3 3 4 6 3 2 21 2 2 5 7 1 11 15  
\* Automatic System = Double FFG Median of 12: 12 times in 16 hits (75%)  
\* Your System Hits = 6 6: 7 times in 16 hits (44%)

The _Automatic System_ is exactly what it says. The program selects the lottery numbers for which the first two skips sum up to Double FFG Median (12 in this real 6/9 lotto case).

The so-called _Your System_ will be your own creation. You create your own lottery system based on the first two skips (the two most recent).

The _Automatic System_ recorded 12 hits (winning situations) or 75% of cases for lotto #9. The _Your System_ (mine, in this case) registered 7 hits (winning situations) or 44% of cases for lotto #9.

Hint: Numbers where the Automatic System registers 50% or better hits represent the golden eggs!

You can set Your System to any values you want to. The two skip values do not need to be equal. If you select values above the FFG median, your lottery system will record more hits. For example, values such as 8 and 7, instead of 6 and 6.

The lottery software defaults to the FFG median. You don't have to fire up **SuperFormula** and calculate the _FFG median_ or the _degree of certainty_.

![Nay-Sayers try to detract the viability of lottery systems software for skips, misses of N drawings.](https://saliu.com/HLINE.gif)

## <u>Function G = <i>Generate Combinations</i> from Skip Systems</u>

This function was introduced in **version 8** (September 2013) of the program. It answered a special request: _How to generate lotto combinations from the skips, especially from the positional skip systems?_ There are several combination-generating functions and procedures in my lottery software, spread across several programs. But specifically to combinations from skip systems: Only the non-positional systems were served. There is still _**BreakDownNumbers**_, which generates lotto combinations, but ignoring the number positions. The _Make/Break/Position_ functions in **Super Utilities** do the same thing. The procedure _3 = Positional Ranges_ does the same thing as the positional generating functions in **Skip System**.

The perfect generating procedures are now components of **Skip System**. The software generates now correct and unique combinations, including from skip positional systems. The latter are strictly positional combinations. That is, a number in line #4 of the system will never appear also in line #2 (as an example). The positional combinations are also sorted in ascending order vertically (i.e. sorting by column).

![The lottery software generates combinations from the skip systems for many lotto, lottery games.](https://saliu.com/ScreenImgs/skip-systems-euromillions.gif)

Axiomatic one, here is a real-life example in the 6-49 lotto game in Pennsylvania. The following is the _positional skip system_, with exactly 6 lines, one for each number position; no line is empty (otherwise the strategy would not generate a single combination):

2 3 5 7 9  
3 5 11 13 16 17 31  
4 6 7 15 18 39  
6 7 25 26 30 33 40 41  
26 27 33 36 38 45 46  
26 28 37 38 44 47 48 49  

You can notice some numbers are common. Also, numbers in higher ranked lines are lower than numbers in previous lines; e.g. 7 in line #4 is lower than 4 in line #3. The generating function in this software will take good care of such occurrences. All combinations generated will be unique and in the correct combination format (the 6 numbers sorted in ascending order, as the combination format requires).

Total combinations generated from the positional skip system: 5461. If the numbers in the system are all-unique and in according order from line to line, the system would generate 5 \* 7 \* 6 \* 8 \* 7 \* 8 = 94,080 combinations. The lotto strategy based on positional skips has a higher efficiency. The unique combinations go from the lowest indexes (lexicographic order) to the highest:

2 3 4 6 26 28  
2 3 4 6 26 37  
2 3 4 6 26 38  
2 3 4 6 26 44  
2 3 4 6 26 47  
...  
9 31 39 40 46 48  
9 31 39 40 46 49  
9 31 39 41 45 47  
9 31 39 41 45 48  
9 31 39 41 45 49  
...

![There are important facts to consider in using lottery software for skip strategies and systems.](https://saliu.com/HLINE.gif)

## <u>Other Considerations in <i>Skip Strategies</i></u>

It is very important to understand that the winning probability is very, very slim to play a system the very next lottery drawing. That's so because the multiple skips need a _cycle of fruition_. That is, it takes several lotto drawings before we see a draw when all 6 numbers have the first two skips as established in a system. I checked a Pennsylvania lotto 6/49 file of 269 drawings. I saw jackpot hits, but they were separated by over 120 drawings. Therefore, the skip strategy can make a good candidate for the _**LIE elimination**_ feature in the Bright / Ultimate software packages.

I have not checked what happens from drawing to drawing. I have not written the lottery software to automate the process of checking for winners. If you are the patient type - you might want to try it manually! Go back 100 draws in your lottery data file and check the next drawings one at a time.

![The lottery software generates combinations from the skip systems for many lotto, lottery games.](https://saliu.com/ScreenImgs/lotto-skips-win.gif)

The results were poor at the beginning of the lotto game. It takes a while for a group of lotto numbers to take over for a range of drawings. At a different point in the lottery drawings file, other numbers will take over. The jackpot situations reflect lotto numbers with good recent frequency and higher percentages of the hit situations for the lottery system.

Version 1.0 of _**SkipSystem**_ calculated the probability of pick-3 lottery for any position as 3/10. The precise method is _p = 271/1000_. The necessary corrections were made also to the pick-4 lottery and horse racing. The double FFG medians for pick-4 and horse racing were also increased by 1 for more accurate results.

-   **Skips Systems** works in conjunction with the _**Super Utilities**_ (on the main menu of Bright software). The lotto, lottery utility software provides for winner checking in pools (groups) of lotto numbers directly (_Check for Winners W_, then option 2). Also importantly, the lotto, lottery utility software can generate combinations from pools of numbers (_Make/Break/Position M_).
-   **Winners** is a standalone program that checks for winning numbers in past lottery drawings. The program is also integrated in the **Bright / Ultimate** software collections. **Winners** is more limited in scope: It only checks for winning combinations. By contrast, _**Super Utilities**_ can check: combination by combination, and pools of numbers, including positional. That's the way to go!

![The combinations generated by a lottery skip system must be purged to reduce tickets to play.](https://saliu.com/HLINE.gif)

There are many lottery combinations to play by using the skip systems created here. That's mathematics. Some lottery players might get disappointed with the truth.

The raw output file generated by a skip lottery system contains many <u>unnecessary combinations</u>. For example, it is <u>very unlikely</u> that all skips will be equal to _1_; or all equal to _2_; or the skip string will be _1 1 1 2 2 2_; or the skips will be _1 2 3 4 5 6_ or _6 5 4 3 2 1_; etc. You get the point. The _combosnations_ (a favorite _lotto-speak_ term of mine!) generated by such skip configurations represent wasted tickets.

Be mindful that the lottery is based on gigantic odds. Many more filters are necessary to reduce the amount of lotto combinations to a playable size. I created my lotto, lottery software with that goal in mind. I came up with powerful reduction tools such the _lottery filters_. Use other lottery apps in my software packages in conjunction with **SkipSystems**.

The _Your System_ option allows you to create your own lottery skip systems; e.g. systems with a higher frequency. Instead of skips under _FFG median_, try skips for _**degree of certainty DC**_ equal to 75%. It amounts to _N = 11_ (or _10_ in **SkipDecaFreq**). You accomplish the task in the _Your System_ option. Only _SKIP #1_ matters here. Set it to 10. For _SKIP #2_, type a very large number; e.g. 1000. I don't think a lotto number will ever skip 1000 lottery drawings... not even close! That way, we will disregard the second skip of every lotto number.

Next, run the _Check Strategy_ option in **SkipDecaFreq**. Lotto strategy: the MAXimum level of all 6 skips equal to or under 10 (all skips up to 10; i.e. in single digits).

The particular 6/49 lotto strategy has a very good frequency: 135 – 180 hits in 1000 drawings (13 – 18 times in 100 draws, or one year). The median of the strategy is 5. That is, in over 50% of the hits, the strategy comes to life after 0 drawings (consecutive hits) or after up to 5 draws. If you wait from 0 to 7 draws, the frequency is significantly higher. Meanwhile, none of 1000 lottery drawings analyzed had all skips in double digits. See the real-life statistical report: [<u><b>Report: Lottery Skips Systems, Best Chance at Lotto Jackpot</b></u>](https://saliu.com/freeware/skips-lotto.html).

It is very rare that 4 of the 6 numbers in any skip system have the same skip value. Therefore, we can apply the filters in the _W6.1_ reports to the _L = Lexicographic Combinations_ option of **Bright / Ultimate Software** (main menu). The _Four_ filter is over 1000 in 50 of the 1000 drawings analyzed. It eliminates all 4-number groups that appeared in the last 1000 lottery draws. The big _Del6_ filter is also very potent. It reaches over 1000000 (one million) numerous times. It is slow, however, as the _Deltas-6_ filter is very processor-intensive.

The procedure above is known as _**Purge**_ in my lottery software. In addition, there is the other powerful feature: _**LIE Elimination strategy**_. Here is one example of a good candidate for the _**LIE Elimination**_ function: _Top half / Bottom half_ of the lotto numbers sorted by <u>frequency</u>. The _Top 25_ or _Hot 25_ lottery numbers rarely will register 5 winners if applied from draw to draw. Therefore we can safely apply the _ID5_ filter in the _**LIE Elimination**_ function. The _Bottom 25_ or _Cold 25_ numbers rarely will register 4 winners if applied from draw to draw. Therefore we can safely apply the _ID4_ filter in the _**LIE Elimination**_ function. Each ID filter will safely eliminate a very large amount of combinations from the respective skip system (_FFG\*.SYS_). Good riddance!

All these procedures in synergy can thin a skip system to very few combinations to play (translated into tickets).

In conclusion: We need to keep in mind that every lottery skip system consists largely of unwanted combinations. We need to discard of them by applying the _**Purge**_ and _**LIE Elimination**_ functions proprietary to the **Parpaluck software** (Ion Saliu's royalty name; every human has a right to a royalty-name and should boast one in all places and times in life).

-   Read a fruitful idea on how to greatly improve the efficiency of the skip strategy, especially for lotto games. A significantly large number of combinations can be eliminated.
-   [_**<u>Optimizing the Skip Systems</u> in Lottery, Lotto, Gambling, Horse Racing**_](https://saliu.com/lotto-skips.html).

![Lotto skips strategies are efficient tools in conjunction with lottery filters in Ion Saliu apps.](https://saliu.com/HLINE.gif)

Power comes at a price: Complexity. You might think other lottery system developers give you easier choices. You falsely believe that they make your life easier, compared to my software and theories. The other guys only get you drunk with illusions. They make you think you can avoid mathematics all together! It's an illusion. They also give you the illusion of winning with 10-20 lotto combinations! They simply give you 10-20 random lotto combinations, without any real data processing. They only lie to you!

The main [_**lottery, lotto strategy software**_](https://saliu.com/LottoWin.htm#Methods) page makes a truthful assessment of my lotto, lottery theories and software, and all the other theories and programs out there. It is as honest as it can be: It's mathematical!

-   Two special lotto programs were released in the year of grace 2011: **UserGroups5/6**. That type of lotto software can generate combinations from any kind of number groups, such as odd or even, low or high, plus any groups the user chooses.
    -   Be forewarned that some skip systems will NOT generate any lottery _combosnations_ (a favorite term of my lotto-speak!) sometimes. A minimum amount of numbers is required depending on the lottery game type. Let's take the case of the 6-number lotto games. The first type of skip systems is called **ANY or regardless of position**. The system names range from _FFG-1L6.SYS_ to _FFG-5L6.SYS_. There must be AT LEAST 6 lotto numbers on the line of each skip system generated.
    -   The **POSitional** skip lotto-6 system is named _FFG-L6P.SYS_. That skip system must have AT LEAST 1 lotto number on each of the 6 lines (6 positions).
    -   If the above requirements are not met, no lottery _combonation_ (another favorite term of mine!) can be generated. Obviously, you cannot play such a lottery skip system. The good news is you save money. You need wait for the next lottery real drawing and create new skip systems.

The skip systems are ideal for roulette or other casino games where the skips can be tracked. In turn, these games have their own drawback: The players are not allowed to use computers inside casinos. The skip gambling systems for roulette or craps can be plotted manually, on paper only. For example, play only the numbers (outcomes) that show 2 (or even 3) consecutive skips _under median_.

Here is data from the _Hamburg Casino (Spielbank)_, Germany, showing clearly a better performance by the _**under median**_ roulette strategies.

FFG-1: 2 skips SUM-UP TO 52 = 2823 hits in 7000 (40%) = 16 numbers (to play)  
FFG-2: 2 skips UNDER/EQUAL 26 = <u>1806</u> hits in 7000 (26%) = <u>12</u> numbers  
FFG-3: 2 skips ABOVE 26 = 1681 hits in 7000 (24%) = 11 numbers  
FFG-4: 3 skips UNDER/EQUAL 26 = <u>927</u> hits in 7000 (13%) = <u>7</u> numbers  
FFG-5: 3 skips ABOVE 26 = 859 hits in 7000 (12%) = 8 numbers  
FFG-6: MEDIAN & SUM <= 52 = 2309 hits in 7000 (33%) = 11 numbers  
FFG-7: 1st Skip <= 26 = <u>3505</u> hits in 7000 (50%) = <u>17</u> numbers  
FFG-8: 1st Skip > 26 = 3384 hits in 7000 (48%) = 20 numbers  
SYS-: Your System <= 20 20 = <u>1244</u> hits in 7000 (18%) = <u>7</u> numbers

Read a beginning of my theory of skips in gambling and lottery here (a repost from my closed forums):

-   [_**First Lottery Systems, Gambling Systems on Skips, Software**_](https://forums.saliu.com/lottery-gambling-skips-systems.html).

![Run software to create lottery systems based on the mathematics of skips: Gaps between lotto wins.](https://saliu.com/HLINE.gif)

[

## <u>Resources in Lottery Software, Systems, Strategies, Lotto Wheels</u>

](https://saliu.com/content/lottery.html)Lists the main pages on the subject of lottery, software, skip strategy, lots of systems, programs, apps.

-   The Main [_**Lotto, Lottery, Software, Strategy**_](https://saliu.com/LottoWin.htm) Page.  
    Presenting software to create free winning lotto, lottery strategies, systems based on mathematics. Get your lotto systems or wheels, the best lottery, lotto software, combinations, winning numbers.
-   [_**Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies**_](https://saliu.com/Newsgroups.htm).  
    Read a genuine analysis of Excel spreadsheets applied to lottery and lotto developing of software, systems, and strategies. Combining Excel analysis with powerful lottery and lotto software programmed by this author, _Parpaluck_.
-   [_**<u>MDIEditor Lotto WE</u>: Tutorial, Software User Guide, Manual, Book, ebook**_](https://saliu.com/MDI-lotto-guide.html).  
    ~ Also applicable to my entire lottery software; plus Powerball, Mega Millions, Euromillions.
-   [_**Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps, Programs**_](https://saliu.com/forum/lotto-book.html).
-   [_**Lottery Mathematics, Lotto Mathematics**_](https://saliu.com/gambling-lottery-lotto/lottery-math.htm)_**, Probabilities, Appearance, Repeat, Number Affiliation, Wheels, Systems, Strategies**_.
-   Practical [_**Lottery and Lotto Filtering in Software**_](https://saliu.com/filters.html).
-   [_**Lotto, Lottery <u>Strategy in Reverse</u>: Not-to-Win Leads to Not-to-Lose or WIN**_](https://saliu.com/reverse-strategy.html).
-   [_**Lotto, Lottery Strategy on Sums, Root Sums, Skips, Odd Even, Low High, Deltas**_](https://saliu.com/strategy.html).
-   [_**Lottery Utility Software**_](https://saliu.com/lottery-utility.html)_**: Pick-3, 4 Lottery, Lotto-5, 6, Powerball, Mega Millions, Euromillions**_.
-   [_**Lottery Systems on Skips Improve Lotto Odds Sevenfold**_](https://saliu.com/bbs/messages/923.html).
-   [_**Gail Howard's <u>Skip</u> Lottery Systems, Lotto <u>Wheels</u>**_](https://saliu.com/bbs/messages/278.html) _**<u>never</u> hit in the next lottery drawing**_.
-   [**Theory, Analysis of _Deltas_ in Lotto, Lottery Software, Strategy, Systems**](https://saliu.com/delta-lotto-software.html).
-   [_**Lotto Decades, Last Digits, Systems, Strategies, Software**_](https://saliu.com/decades.html).
-   [_**Markov Chains, Followers, Pairs, Lottery, Lotto, Software**_](https://saliu.com/markov-chains-lottery.html).
-   [_**LIE Elimination: Lottery, Lotto Strategy in Reverse for Pairs, Number Frequency**_](https://saliu.com/lie-lottery-strategies-pairs.html).
-   [_**Reversed Lottery Strategy for Lotto Decades, Last Digits, Odd Even, Low High, Skips**_](https://saliu.com/lie-lotto-strategies-decades.html).
-   [_**The Best Ever Lottery Strategy, Lotto Strategies**_](https://software.saliu.com/lottery-strategy-lotto-strategies.html).
-   [_**Lotto Software for Groups of Numbers: Odd, Even, Low, High, Sums, Frequencies, User's Groups**_](https://saliu.com/lotto-groups.html).
-   [_**The Best Strategy for Lottery, Gambling, Sports Betting, Horse Racing, Blackjack, Roulette**_](https://saliu.com/strategy-gambling-lottery.html).
-   _"The Start Is the Hardest Part"_ in [_**Lottery Strategies**_](https://forums.saliu.com/lottery-strategies-start.html).
-   [_**Strategy Lotto Software on Positional Frequency**_](https://forums.saliu.com/lotto-software-position-frequency.html).
-   [_**Artificial Intelligence AI, Axiomatic Intelligence AxI, Neural Networks in Lottery: Strategies, Systems, Software**_](https://saliu.com/neural-networking-lottery.html).
-   Download [**Lottery Software, Lotto Applications**](https://saliu.com/infodown.html).

![Lottery developer Ion Saliu created the best software to create lotto systems based on number skips.](https://saliu.com/HLINE.gif)

**[Home](https://saliu.com/index.htm) | [Search](https://saliu.com/Search.htm) | [New Writings](https://saliu.com/bbs/index.html) | [Fundamental Formula](https://saliu.com/formula.htm) | [Odds, Generator](https://saliu.com/calculator_generator.html) | [Contents](https://saliu.com/content/index.html) | [Forums](https://forums.saliu.com/) | [Sitemap](https://saliu.com/sitemap/index.html)**

![Thanks for reading software for systems, lottery strategies based on skips after lotto jackpot hits.](https://saliu.com/HLINE.gif)
