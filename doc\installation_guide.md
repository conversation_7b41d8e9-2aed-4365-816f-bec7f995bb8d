# Wonder Grid Lottery System - 安裝指南

## 📋 目錄

1. [系統需求](#系統需求)
2. [<PERSON> 安裝](#julia-安裝)
3. [系統下載](#系統下載)
4. [環境配置](#環境配置)
5. [驗證安裝](#驗證安裝)
6. [故障排除](#故障排除)
7. [升級指南](#升級指南)

---

## 系統需求

### 硬體需求

| 組件 | 最低需求 | 推薦配置 | 高性能配置 |
|------|----------|----------|------------|
| **CPU** | 雙核心 2.0GHz | 四核心 3.0GHz | 八核心 3.5GHz+ |
| **記憶體** | 4GB RAM | 8GB RAM | 16GB+ RAM |
| **儲存空間** | 1GB 可用空間 | 2GB 可用空間 | 5GB+ 可用空間 |
| **網路** | 可選（下載用） | 寬頻連接 | 寬頻連接 |

### 軟體需求

| 軟體 | 最低版本 | 推薦版本 | 說明 |
|------|----------|----------|------|
| **Julia** | 1.8.0 | 1.11.0+ | 核心運行環境 |
| **Git** | 2.0+ | 最新版本 | 下載源碼（可選） |
| **文字編輯器** | 任意 | VS Code + Julia 擴展 | 開發和配置 |

### 作業系統支援

- ✅ **Windows 10/11** (x64)
- ✅ **macOS 10.15+** (Intel/Apple Silicon)
- ✅ **Linux** (Ubuntu 18.04+, CentOS 7+, Debian 10+)
- ✅ **FreeBSD** (實驗性支援)

---

## Julia 安裝

### Windows 安裝

#### 方法 1：官方安裝程式（推薦）

1. **下載 Julia**
   ```
   訪問：https://julialang.org/downloads/
   下載：Windows x86_64 版本
   ```

2. **執行安裝程式**
   - 雙擊下載的 `.exe` 文件
   - 選擇安裝路徑（建議使用默認路徑）
   - 勾選 "Add Julia to PATH"
   - 點擊 "Install"

3. **驗證安裝**
   ```powershell
   # 開啟 PowerShell 或命令提示字元
   julia --version
   ```

#### 方法 2：Chocolatey

```powershell
# 安裝 Chocolatey（如果尚未安裝）
Set-ExecutionPolicy Bypass -Scope Process -Force
iex ((New-Object System.Net.WebClient).DownloadString('https://chocolatey.org/install.ps1'))

# 安裝 Julia
choco install julia
```

#### 方法 3：Winget

```powershell
# 使用 Windows Package Manager
winget install julia -s msstore
```

### macOS 安裝

#### 方法 1：官方安裝程式

1. **下載 Julia**
   ```
   訪問：https://julialang.org/downloads/
   下載：macOS x86_64 或 Apple Silicon 版本
   ```

2. **安裝**
   - 開啟下載的 `.dmg` 文件
   - 將 Julia 拖拽到 Applications 資料夾
   - 開啟終端機，創建符號連結：
   ```bash
   sudo ln -s /Applications/Julia-1.11.app/Contents/Resources/julia/bin/julia /usr/local/bin/julia
   ```

#### 方法 2：Homebrew（推薦）

```bash
# 安裝 Homebrew（如果尚未安裝）
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# 安裝 Julia
brew install julia
```

### Linux 安裝

#### Ubuntu/Debian

```bash
# 方法 1：官方 PPA（推薦）
sudo apt update
sudo apt install software-properties-common
sudo add-apt-repository ppa:staticfloat/juliareleases
sudo apt update
sudo apt install julia

# 方法 2：Snap
sudo snap install julia --classic

# 方法 3：下載二進制文件
wget https://julialang-s3.juliacomputing.com/bin/linux/x64/1.11/julia-1.11.0-linux-x86_64.tar.gz
tar -xzf julia-1.11.0-linux-x86_64.tar.gz
sudo mv julia-1.11.0 /opt/julia
sudo ln -s /opt/julia/bin/julia /usr/local/bin/julia
```

#### CentOS/RHEL/Fedora

```bash
# CentOS/RHEL
sudo yum install epel-release
sudo yum install julia

# Fedora
sudo dnf install julia

# 或下載二進制文件（同 Ubuntu 方法 3）
```

#### Arch Linux

```bash
# 使用 pacman
sudo pacman -S julia

# 或使用 AUR
yay -S julia-bin
```

---

## 系統下載

### 方法 1：Git Clone（推薦）

```bash
# 克隆倉庫
git clone https://github.com/your-repo/wonder-grid-lottery-system.git

# 進入目錄
cd wonder-grid-lottery-system

# 檢查版本
git tag --list
git checkout v1.0.0  # 切換到穩定版本
```

### 方法 2：下載 ZIP

1. 訪問 GitHub 倉庫頁面
2. 點擊 "Code" → "Download ZIP"
3. 解壓縮到目標目錄

### 方法 3：發布版本

```bash
# 下載最新發布版本
curl -L https://github.com/your-repo/wonder-grid-lottery-system/releases/latest/download/wonder-grid-system.tar.gz -o wonder-grid-system.tar.gz

# 解壓縮
tar -xzf wonder-grid-system.tar.gz
cd wonder-grid-lottery-system
```

---

## 環境配置

### 基本配置

#### 1. 設置執行緒數

```bash
# 方法 1：環境變量（永久）
echo 'export JULIA_NUM_THREADS=auto' >> ~/.bashrc
source ~/.bashrc

# 方法 2：啟動時指定（臨時）
julia -t auto

# 方法 3：手動指定執行緒數
julia -t 4
```

#### 2. 記憶體配置

```bash
# 設置最大記憶體使用量
export JULIA_MAX_MEMORY=8G

# 設置垃圾回收參數
export JULIA_GC_ALLOC_POOL=1
export JULIA_GC_ALLOC_OTHER=1
```

#### 3. 性能優化

```bash
# 啟用編譯快取
export JULIA_DEPOT_PATH="$HOME/.julia"

# 設置編譯優化等級
export JULIA_OPTLEVEL=3

# 啟用性能監控
export WONDER_GRID_MONITORING=true
```

### 進階配置

#### 1. 創建配置文件

創建 `config/system_config.jl`：

```julia
# Wonder Grid System 配置文件

# 性能配置
const PERFORMANCE_CONFIG = Dict(
    "enable_multithreading" => true,
    "enable_caching" => true,
    "enable_compact_data" => true,
    "auto_cleanup" => true
)

# 快取配置
const CACHE_CONFIG = Dict(
    "l1_max_items" => 200,
    "l2_max_items" => 2000,
    "l3_max_items" => 20000,
    "cleanup_interval_minutes" => 15
)

# 記憶體池配置
const MEMORY_POOL_CONFIG = Dict(
    "max_size" => 1000,
    "cleanup_threshold" => 100,
    "cleanup_interval_minutes" => 30
)

# 監控配置
const MONITORING_CONFIG = Dict(
    "enable_performance_monitoring" => true,
    "enable_auto_tuning" => true,
    "tuning_interval_minutes" => 60,
    "learning_rate" => 0.1
)
```

#### 2. 環境檢測腳本

創建 `scripts/check_environment.jl`：

```julia
#!/usr/bin/env julia

println("🔍 Wonder Grid System 環境檢測")
println("=" ^ 50)

# 檢查 Julia 版本
println("Julia 版本: $(VERSION)")
if VERSION >= v"1.8"
    println("✅ Julia 版本符合需求")
else
    println("❌ Julia 版本過低，需要 1.8+")
end

# 檢查執行緒數
thread_count = Threads.nthreads()
println("可用執行緒數: $thread_count")
if thread_count > 1
    println("✅ 多執行緒已啟用")
else
    println("⚠️ 建議啟用多執行緒：julia -t auto")
end

# 檢查記憶體
total_memory = Sys.total_memory() / (1024^3)  # GB
println("系統記憶體: $(round(total_memory, digits=1))GB")
if total_memory >= 4
    println("✅ 記憶體充足")
else
    println("⚠️ 記憶體可能不足，建議 4GB+")
end

# 檢查 CPU
cpu_count = Sys.CPU_THREADS
println("CPU 執行緒數: $cpu_count")

# 檢查作業系統
os_name = Sys.KERNEL
println("作業系統: $os_name")

println("\n🎯 建議配置:")
println("  啟動命令: julia -t auto")
println("  記憶體設置: export JULIA_MAX_MEMORY=8G")
println("  性能監控: export WONDER_GRID_MONITORING=true")
```

---

## 驗證安裝

### 基本驗證

#### 1. 啟動系統

```bash
# 進入系統目錄
cd wonder-grid-lottery-system

# 啟動 Julia（啟用多執行緒）
julia -t auto

# 在 Julia REPL 中載入系統
julia> include("src/wonder_grid_system.jl")
```

#### 2. 運行測試

```julia
# 運行基本測試
julia> include("test/run_basic_tests.jl")

# 運行完整測試套件
julia> include("test/run_all_tests.jl")
```

#### 3. 快速功能測試

```julia
# 創建測試數據
test_draws = [
    LotteryDraw([1, 5, 10, 15, 20], Date(2023, 1, 1), 1),
    LotteryDraw([2, 6, 11, 16, 21], Date(2023, 1, 2), 2)
]

# 創建引擎
engine = OptimizedFilterEngine(test_draws)

# 測試基本功能
result = calculate_skip_optimized(engine, 1)
println("✅ Skip 計算測試: $result")

# 測試性能監控
summary = get_global_performance_summary()
println("✅ 性能監控測試: $(summary["performance_grade"])")

println("🎉 系統安裝驗證完成！")
```

### 性能驗證

#### 1. 性能基準測試

```julia
# 運行性能基準測試
julia> include("test/run_performance_benchmark.jl")
```

#### 2. 並行計算測試

```julia
# 測試並行計算能力
capabilities = get_parallel_capabilities()
println("並行計算能力: $capabilities")

# 運行並行測試
julia> include("test/run_parallel_tests.jl")
```

---

## 故障排除

### 常見安裝問題

#### 1. Julia 安裝失敗

**問題**: 無法下載或安裝 Julia

**解決方案**:
```bash
# 檢查網路連接
ping julialang.org

# 使用鏡像站點
# 中國用戶可使用清華大學鏡像
wget https://mirrors.tuna.tsinghua.edu.cn/julia-releases/bin/linux/x64/1.11/julia-1.11.0-linux-x86_64.tar.gz
```

#### 2. 權限問題

**問題**: 安裝時權限不足

**解決方案**:
```bash
# Linux/macOS
sudo chown -R $USER:$USER ~/.julia
chmod -R 755 ~/.julia

# Windows（以管理員身份運行 PowerShell）
icacls "%USERPROFILE%\.julia" /grant "%USERNAME%":F /T
```

#### 3. 路徑問題

**問題**: 找不到 julia 命令

**解決方案**:
```bash
# Linux/macOS
echo 'export PATH="/opt/julia/bin:$PATH"' >> ~/.bashrc
source ~/.bashrc

# Windows（添加到系統 PATH）
# 控制台 → 系統 → 高級系統設置 → 環境變量
# 在 PATH 中添加 Julia 安裝目錄
```

### 系統載入問題

#### 1. 模組載入失敗

**問題**: include 時出現錯誤

**解決方案**:
```julia
# 檢查當前目錄
pwd()

# 確保在正確的目錄中
cd("path/to/wonder-grid-lottery-system")

# 檢查文件是否存在
isfile("src/wonder_grid_system.jl")

# 逐步載入模組
include("src/types.jl")
include("src/filter_engine.jl")
# ...
```

#### 2. 依賴問題

**問題**: 缺少必要的 Julia 包

**解決方案**:
```julia
# 進入包管理模式
julia> ]

# 安裝必要的包
pkg> add Dates
pkg> add Statistics
pkg> add Test

# 退出包管理模式
pkg> <Backspace>
```

### 性能問題

#### 1. 執行緒未啟用

**問題**: 系統運行緩慢

**解決方案**:
```julia
# 檢查執行緒數
println("執行緒數: $(Threads.nthreads())")

# 如果只有 1 個執行緒，重新啟動
# julia -t auto
```

#### 2. 記憶體不足

**問題**: 系統崩潰或記憶體錯誤

**解決方案**:
```bash
# 增加虛擬記憶體（Linux）
sudo fallocate -l 4G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile

# 或減少數據集大小
# 使用較小的測試數據集
```

---

## 升級指南

### 版本升級

#### 1. 檢查當前版本

```julia
# 在系統中檢查版本
julia> include("src/version.jl")
julia> println(WONDER_GRID_VERSION)
```

#### 2. 升級到新版本

```bash
# 備份當前配置
cp -r config config_backup

# 拉取最新代碼
git fetch origin
git checkout main
git pull origin main

# 或下載新版本
wget https://github.com/your-repo/wonder-grid-lottery-system/releases/latest/download/wonder-grid-system.tar.gz
```

#### 3. 遷移配置

```julia
# 運行遷移腳本
julia> include("scripts/migrate_config.jl")
```

### Julia 升級

#### 1. 升級 Julia

```bash
# Ubuntu
sudo apt update
sudo apt upgrade julia

# macOS
brew upgrade julia

# Windows
# 下載新版本安裝程式並重新安裝
```

#### 2. 重新編譯

```julia
# 清除編譯快取
julia> using Pkg
julia> Pkg.precompile()
```

---

## 自動化安裝腳本

### Linux/macOS 一鍵安裝

創建 `install.sh`：

```bash
#!/bin/bash

echo "🚀 Wonder Grid Lottery System 自動安裝"
echo "======================================"

# 檢查 Julia
if ! command -v julia &> /dev/null; then
    echo "📦 安裝 Julia..."
    
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        # Linux
        sudo apt update
        sudo apt install -y julia
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        if command -v brew &> /dev/null; then
            brew install julia
        else
            echo "❌ 請先安裝 Homebrew"
            exit 1
        fi
    fi
fi

# 檢查 Git
if ! command -v git &> /dev/null; then
    echo "📦 安裝 Git..."
    sudo apt install -y git  # Linux
    # macOS 通常已預裝 Git
fi

# 下載系統
echo "📥 下載 Wonder Grid System..."
git clone https://github.com/your-repo/wonder-grid-lottery-system.git
cd wonder-grid-lottery-system

# 設置環境
echo "⚙️ 配置環境..."
echo 'export JULIA_NUM_THREADS=auto' >> ~/.bashrc
echo 'export WONDER_GRID_MONITORING=true' >> ~/.bashrc

# 運行環境檢測
echo "🔍 檢測環境..."
julia scripts/check_environment.jl

# 運行測試
echo "🧪 運行測試..."
julia -t auto test/run_basic_tests.jl

echo "✅ 安裝完成！"
echo "🚀 啟動命令: julia -t auto"
echo "📚 文檔位置: doc/"
```

### Windows PowerShell 安裝

創建 `install.ps1`：

```powershell
Write-Host "🚀 Wonder Grid Lottery System 自動安裝" -ForegroundColor Green
Write-Host "======================================" -ForegroundColor Green

# 檢查 Julia
if (-not (Get-Command julia -ErrorAction SilentlyContinue)) {
    Write-Host "📦 安裝 Julia..." -ForegroundColor Yellow
    
    if (Get-Command winget -ErrorAction SilentlyContinue) {
        winget install julia -s msstore
    } else {
        Write-Host "❌ 請手動安裝 Julia 或 winget" -ForegroundColor Red
        exit 1
    }
}

# 檢查 Git
if (-not (Get-Command git -ErrorAction SilentlyContinue)) {
    Write-Host "📦 安裝 Git..." -ForegroundColor Yellow
    winget install Git.Git
}

# 下載系統
Write-Host "📥 下載 Wonder Grid System..." -ForegroundColor Yellow
git clone https://github.com/your-repo/wonder-grid-lottery-system.git
Set-Location wonder-grid-lottery-system

# 設置環境變量
Write-Host "⚙️ 配置環境..." -ForegroundColor Yellow
[Environment]::SetEnvironmentVariable("JULIA_NUM_THREADS", "auto", "User")
[Environment]::SetEnvironmentVariable("WONDER_GRID_MONITORING", "true", "User")

# 運行環境檢測
Write-Host "🔍 檢測環境..." -ForegroundColor Yellow
julia scripts/check_environment.jl

# 運行測試
Write-Host "🧪 運行測試..." -ForegroundColor Yellow
julia -t auto test/run_basic_tests.jl

Write-Host "✅ 安裝完成！" -ForegroundColor Green
Write-Host "🚀 啟動命令: julia -t auto" -ForegroundColor Cyan
Write-Host "📚 文檔位置: doc/" -ForegroundColor Cyan
```

---

**🎉 安裝完成！**

現在您可以開始使用 Wonder Grid Lottery System 了。建議接下來：

1. 閱讀 [快速入門指南](quick_start.md)
2. 查看 [用戶手冊](user_manual.md)
3. 探索 [API 參考文檔](api_reference.md)
4. 運行範例項目

如遇到問題，請參考 [故障排除指南](troubleshooting.md) 或提交 Issue。
