---
created: 2025-07-24T06:53:50 (UTC +08:00)
tags: [lotto,lottery,filters,filtering,odds,probability,median,jackpot,winning,strategy,strategies,software,]
source: https://saliu.com/bbs/messages/923.html
author: 
---

# Filters in Lottery, Lotto Jackpot, <PERSON><PERSON>, High Skips

> ## Excerpt
> Study filters, filtering, median skip in lotto, lottery, gambling winning. Lotto jackpots are influenced by low skips, FFG median calculated by formulas.

---
## <u>FFG Median, <i>Skips</i>, Lottery, Lotto Filtering, Probability, Odds, Jackpot</u>

## Playing Combinations around the FFG Median Can Improve the Odds of a Lotto Jackpot

## By <PERSON>, _★ Founder of Lottery Mathematics_

![Apply filters, filtering, median, pairs in lotto, lottery, gambling, software.](https://saliu.com/bbs/messages/HLINE.gif)

Published on February 17, 2002; later updates.

I come back to the idea presented in a previous article: [_**Dynamic versus Static in Lotto, Lottery Analysis**_](https://saliu.com/bbs/messages/919.html). I ran my lottery software **Combinations** to generate ranges of combinations around the field's _midpoint_ or around the _probability median_ for a 6/69 lotto game.

I applied the test to PA 6/69 lotto game (a very tough game, indeed; the jackpot odds are _1 in 119,877,472_). I generated first 1000 combinations around the _mid-point_ index: _59,938,736_. That is, the lotto program generated combinations between the counts of _59,938,236_ and _59,939,236_.

Next, I checked for winners in the output file against real draws in PA 6/69 lotto (350 real lottery draws at my discretion). The _mid-point output_ achieved 3 _5 of 6_ hits and 140 _4 of 6_ hits. The game odds are _1 in 317,000_ for _5 of 6_ and _1 in 4092_ for _4 of 6_. The _5 of 6_ wins were recorded in 350 x 1000 = 350,000 combinations. Therefore, the _mid-point_ strategy was about 3 times better than random play in the _5 of 6_ situation. For the _4 of 6_ case, _1 in 4092_ applied to 350,000 lotto combinations translates into 85. The _mid-point_ strategy hit 140 times, or about one and a half times better.

The next test was applied against the _FFG median_, instead of the _mid-point_. The _FFG median_ is calculated in _**FFG**_ for a _**degree of certainty DC**_ equal to **50%**. The _FFG median_ is 83,092,731 for a 6/69 lotto game. A number of 1000 combinations were generated in the 83,092,231 – 83,093,231 range. The results were undoubtedly better. The strategy yielded 6 _5 of 6_ hits and 238 _4 of 6_ hits. Compared to purely random play, the _FFG median_ strategy was 6 times better for _5 of 6_ and about 3 times better in the _4 of 6_ case.

I saw in the newsgroups similar tests applied to UK 6/49 lotto game, with lower odds than Pennsylvania's 6/69 monster lotto. This time, the results were inverted: the _mid-point_ strategy was favored! It became clear to me that this type of lottery strategy favors the _probability (FFG) median_ in high odds games. The higher the odds, the better the _FFG median_ strategy fares.

I did the test for 10000 lotto combinations against the same 350-draw data file (PA 6/69 lotto). I ran the test for both the _midpoint_ and _FFG median_: 5000 below and 5000 above (actually, there are 10,001 lotto combinations, including the pivots). The results were dramatic!

1) The _midpoint_ strategy performed worse than in the 1000 test; even worse than random play! It recorded 6 _5 of 6_ hits and 442 _4 of 6_ wins. That's about half of what lotto random play yields!

2) The _probability median_ strategy was far better than the _1000-combination test_ and far-far better than random play.  
\- 1 _6 of 6_ (jackpot) win (34 times better than random play);  
\- 75 _5 of 6_ hits;  
\- 2,232 _4 of 6_ wins!  
Again, a random play for an equivalent amount of combinations (350 x 10001 = 3,500,350) should yield:  
\- 0.029 _6 of 6_ hits;  
\- 11 _5 of 6_ hits;  
\- 855 _4 of 6_ wins.

I compared these results with the results in a 6/49 game (UK, 642 draws).  
The 10,001 runs did not yield jackpot wins. The lotto jackpot hits occurred at 50,001-combination runs!  
1) _Midpoint_ strategy:  
\- 27,439 hits   (1 _6 of 6_ jackpot hit)  
2) _FFG median_ strategy:  
\- 34,616 hits (2 _6 of 6_ wins).  
Now, the _FFG median_ strategy was favored over the _midpoint_ strategy!

It is very clear that this type of play favors the high odds games lotto by far. The lottery strategy is insignificant for lower odds games, such as pick-3 or pick-4 lotteries. I don't have a formula for it. I don't think this type of strategy has an under-pinning formula. The explanation relies on the type of combinations in various areas of the lotto field. The combinations in the _FFG median_ area are largely part of the bell distribution. On the other hand, the purely random field includes many _hardly-to-come-out-in-a-lifetime_ combinations. Combinations like _1-2-3-4-5-6_ and the like, or _44-45-46-47-48-49_ and the like.

Yes, some dudes will jump off: _"They have an equal probability to come out!"_ But they won't come out equally (statistically). One can bet the entire budget surplus of the U.S. treasury in 2000 on lotto combinations such as the above. The results would be the red figures of the year 2002. I can give you here a summary explanation, other than the _Markov Chains_. The lotto balls would need to be mixed for millions of hours before any drawing. Then, _weird_ combinations would have a reasonable chance to come out. It is all about _**number of trials N**_.

Probability is viewed by many as a static phenomenon. _"The individual probability in the pick-3 lottery is 1/1000. Therefore every lottery combination has an equal chance of appearance."_ That's correct, insofar as no dynamic processes are involved. But the lottery is a dynamic process. There is more to the picture than meets the eye. The formula of individual probability becomes just a part of inclusive systems (formulae).

I have found out that many random processes are ruled by the _**Fundamental Formula of Gambling (FFG)**_. Equal individual probabilities do not lead to equal frequencies. The pick-3 combinations do not appear equally. There are 1000 possibilities in the pick-3 game. In 1000 tries, each and every combination should come out, right? NOT! _**FFG**_ shows that only around **63** percent of the pick-3 combinations come out in 1000 draws, any 1000 consecutive draws. Some **37%** of the pick-3 combination need more than 1000 draws to come out: 1500 draws, 3000 draws, even 6000 lottery drawings!

[![Lottery Software: Jackpot Lotto, Pick Lotteries, Powerball, Mega Millions, Euromillions, Keno.](https://saliu.com/ScreenImgs/lotto-decades-skips.gif)](https://saliu.com/free-lotto-lottery.html)

-   Let's answer a very interesting question. What is the probability that ALL pick-3 combinations will come out in 1000 lottery drawings (tries)? This point is closely related to the [_**Classical Occupancy Problem**_](https://saliu.com/monty-paradox.html#Paradoxes) and _Probability of Collisions or Duplication_ (e.g. [_**Birthday Paradox**_](https://saliu.com/birthday.html)).
-   Let's start with the simplest gamble: _coin tossing_. The probability is 1/2. Then, what is the probability that _heads_ and _tails_ will come out in 2 tosses? We apply the _**Binomial Distribution Formula**_. You can use my **Super Formula** to do the calculations. The probability for either _heads_ or _tails_ to come out EXACTLY once in 2 tries is: 1/2 or 50% (no surprise there). We must have EXACTLY 1 _heads_ and 1 _tails_ in 2 tosses. That is, ½ x ½ = ¼ (25%).
    
    The chance to get EXACTLY 1 _heads_ AND 1 _tails_ in 2 tosses is _1 in 4_. The event will be _heads-tails_ or _tails-heads_. It means that if we toss the coin 2 times, and do such a thing 4 times, we can expect to get a result such as _heads-tails_ or _tails-heads_. Sometimes, we may get it in the first try of 2 tosses; other times, we may have to wait 10 or more 2-toss tries.
    
-   Let's take a higher odds game: _rolling the dice_. A cube has 6 point faces. What is the probability to get ALL 6 point faces in 6 rolls? The individual probability is 1/6. We apply again the _**Binomial Distribution Formula**_. The probability to get EXACTLY 1 face point in 6 tries is 0.4 (40%). That chance is equal for each point face. The final result for ALL 6 point faces in 6 rolls is 0.4 to the power of 6: _0.4 ^ 6 = 0.004_, or _1 in 250_. We need to repeat the 6-roll event 250 times to get _ALL 6 point faces in 6 rolls_. Sometimes it can happen in 10 or 100 tries; other times, it may take 500 or 1000 or more 6-roll tries.
-   The _pick-3 lottery_ results enter the astronomical realm. We can't even say those numbers. The individual probability is 1/1000. We apply again the _Binomial Distribution Formula_. The probability to get EXACTLY 1 pick-3 combonation (a favorite of mine!) in 1000 lottery drawings is 0.368 (36.8%). It is a pretty high probability. That chance is equal for each pick-3 combination. The final result for ALL 1000 pick-3 lottery combinations in 1000 drawings is 0.368 to the power of 1000: _0.368 ^ 1000_ = _my-calculator-can't-compute-that-operation number_! Raising only to the power of 100 gives 0._44 zeros_3844! Forget about it! There is a far, far, far better chance for the Earth colliding with an asteroid that would put an end to all life.
-   Undoubtedly, the lotto combinations have different frequencies as a matter of truth. Some repeat, some repeat more often, some do not appear at all in various ranges of trials. The process is not blind. It can be analyzed scientifically. As far as I am concerned, the _**Fundamental Formula of Gambling (FFG)**_ has provided the best analytical tools. Its fundamental discovery: In 50% of the cases, each combination repeats after a number of trials less than/equal to the probability median of the game. That's a law. It is validated in any game of chance, lottery included. I analyzed the facts in other essays; e.g. [_**Comparison of lotto software programs**_](https://saliu.com/bbs/messages/278.html).
-   I showed that in a lotto 6/48 (or 6/49) game, every number will repeat within 6 drawings in over 50% of the situations. Let's dig deeper into this statement. So, the probability of a lotto 6/49 number to repeat is 1/2 times 1/6 (the number will repeat after 1, or 2, or 3, or 4, or 5, or 6 drawings). The combined probability is 1/2 x 1/6 = 1/12. Actually, it is 0.54 \* 1/6 = 0.09. But the lottery draws 6 numbers, therefore the probability to get all 6 numbers repeating after 1 to 6 drawings is: _0.9 ^ 6 = 0.000000531_ (raising to the power of 6). The result can be expressed also as _5.31 times in 10 million_, or _1,881,676 to 1_. Well, the odds are better than playing 6 random numbers (_1 in 13,983,816_): 7 and half times better. That's a law.
-   You can have my peerless lotto software (**LotWon** or **MDIEditor Lotto**) create the well-known _**skip charts**_. Simply selecting 6 lotto numbers that show as current _skips_ figures between _0 and 5_ improves your lotto 6/49 odds _sevenfold_! There is no doubt or mystery here. It is a formula-backed fact. No matter when you play such a lotto strategy, your odds will always be seven times better than purely random play.
-   Look at this real-life case (6/49 lotto in Pennsylvania Lottery). You can see that all skips are _5 or under_ in 14 drawings out of 1000 draws analyzed. You can improve the odds further if considering skips of _0 to 6_ (20 additional winning situations). Meanwhile, I haven't seen one single draw where all 6 skips are in double digits.
-   But look how much further we can improve the strategy by making sure that at least one skip is 0, while the rest of the skips are _less than or equal to the median_. It occurs 13 times (just one drawing shorter). A skip of _0_ here means _hits in consecutive draws_. In the **SkipSystem** application, a _hit in consecutive drawings_ is represented by a skip of **1** (in order to avoid few inaccuracies in roulette).
-   It gets even better: At least 2 skips are 0, while the rest are _less than or equal to the median_. The amount of numbers in the pool to play is reduced dramatically. This strategy hits 12 times in 1000 drawings - not a big drop in strategy hits from 14 without the new restriction.
-   The flipside: There is NO software to apply this strategy _exactly_. We can only get a string (pool) of numbers with skips under FFG median; e.g. 25 numbers in 6/49 lotto.
-   Only the lexicographic generator in **SkipDecaFreq6** can generate combinations for such situations: _Screen\_1_, the _Any_ and _Pos_ filters. The maximum levels of all _**Any**_ filters are set to 6.
-   Save the combinations (e.g. _C(25, 6) = 177100_) to an output file.
-   If 2 of the maximum _<u>Any</u>_ filters are set to 1, we would deal with only 2000–20000 combinations. They can certainly be _**LIE eliminated**_ further safely.
-   _**Purge**_ the output file (of 177100 combos) in **Lexico6** (function _L_ in main menu) by setting the maximum level of the _Two_ filter to 1.
-   This is only an approximation at this time and there is no alternative.

-   You might have to wait a lifetime or two to see all skips larger than 20! Well, then, why play lotto numbers that all have large skips?! Random selection can include many lotto numbers with very large skips. And that is the reason why random play fares clearly worse than playing lottery numbers with skips _less than or equal to the probability (FFG) **median**_. None of 1000 lottery drawings analyzed in Pennsylvania Lottery had all skips in double digits. See the real-life statistical report: [<u><b>Report: Lottery Skips Systems, Best Chance at Lotto Jackpot</b></u>](https://saliu.com/freeware/skips-lotto.html).
-   The cynics who ragingly cry that the odds are always the same are right once and only once. At the very beginning of the lottery game, all skips are "equal": **0** (zero). The skip-charting lottery software is **Skip Decades Frequency**, menu II of the **Bright / Ultimate Lottery Software** packages, function _S = Skips, Decades, Frequencies_. The report is sorted in ascending order by the _POT_ column (representing the **largest skip** in a 6-number drawing).
    
    ![Playing lottery skips below median increases tremendously the chance to win the lotto jackpot.](https://saliu.com/ScreenImgs/lotto-skips.gif)
    
    -   Inquisitively axiomatic reader and student of randomness, a point is in tall order: _House edge_ or _house advantage (HA)_. We all know that lottery, unlike casino gambling, enjoys (_imposes_, in fact) a huge, _humungous HA_. We calculate the house advantage with another formula based on _units paid UP_ over _total possibilities TP_:
        
        **HA = 1 – (UP / TP)**  
        (Always expressed as a percentage.)
        
    -   If, _par exemple_, the 6/49 lotto game has a 2-million units (e.g. dollars) in payout for jackpot, and total combinations reach about 14 million (to simplify):  
        HA = 1 – (2/14) = _85.7%_.
    -   Granted, the jackpot has rollovers. Let's consider a nice case-scenario and set the average payout to 4 million dollars. The lottery house advantage is still big: _71.4%_. By contrast, the house edge in the American roulette is _5.26%_, or 13 times better!
    -   The player can have an advantage, however. If the payout exceeds the odds (total possible outcomes), that is a _disadvantage_ for the house (the lottery commission). For example, if the payout (jackpot) is over 14 million, the player has an advantage. Qualification: If the player chooses to play **only** when the lotto jackpot is over 14 million dollars (units). Of course, it would take many, many drawings for that _player's advantage_ to come to fruition (biblical lifetimes!)
    -   Things get better (shorter lifespans) if we apply the discovery above: Play only lotto numbers with _skips under the FFG median_. Total possible outcomes go down to 1,881,676... let's make it 1.9 million. If the lottery player only plays when the jackpot is equal to or above 2 million pay-units. The _minimum player advantage_ turns out to be... _5.26%_. The best roulette out there as far as players are concerned!
    -   Evidently, the lotteries would die if players choose to play **only** when they have an advantage. There wouldn't be enough players to push the jackpot amount over the advantage limit. Actually, there wouldn't be a jackpot at all! In casino gambling, the player must play every bet. However, the mathematical gambler can bet bigger when the degree of certainty is higher. Such [_**gambling strategy offsets losses in minimum-bet situations**_](https://saliu.com/blackjack-strategy-system-win.html).
    
    As for the _midpoint_ and _FFG median_ strategies presented at the beginning of this post: I do not endorse them. I have no formula to lay a foundation. They might as well be expensive to play. They certainly need further lotto filtering, using LotWon filters. You should never — ever — apply ordinary _cigarette-butt filters_ to smoking down the lottery odds!
    
    ![Lotto software creates the best lottery strategies to win the jackpot multiple times.](https://saliu.com/ScreenImgs/mdi-lotto-software.gif)
    
    -   [_**Dynamic or Static Filters in Lottery, Lotto Analysis, Mathematics, Software**_](https://saliu.com/bbs/messages/919.html).
    -   [_**Internet, State Lotteries: Lotto Drawings, Lottery Results, Winning Numbers**_](https://saliu.com/bbs/messages/920.html).
    
    [
    
    ## <u>Resources in Lottery Software, Strategies, Lotto Systems</u>
    
    ](https://saliu.com/content/lottery.html)
    
    -   The Main [_**Lotto, Lottery, Software, Strategy**_](https://saliu.com/LottoWin.htm) page  
        Presenting software to create free winning lotto, lottery strategies, systems based on mathematics. Get your lotto systems or wheels, the best lottery, lotto software, combinations, winning numbers.
    -   [_**Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies**_](https://saliu.com/Newsgroups.htm).  
        Read a genuine analysis of Excel spreadsheets applied to lottery and lotto software development, systems, and strategies. Combining Excel analysis with powerful lottery and lotto software programmed by this author, _Parpaluck_.
    -   [_**<u>MDIEditor Lotto WE</u>: Lottery Software Manual, Book, ebook, Help**_](https://saliu.com/MDI-lotto-guide.html).  
        ~ Also applicable to LotWon lottery, lotto software; plus Powerball/Mega Millions, Euromillions.
    -   [_**Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps, Programs**_](https://saliu.com/forum/lotto-book.html).
    -   [_**<u>Skip System</u> Software**_](https://saliu.com/skip-strategy.html): _**Lotto, Lottery, Powerball, Mega Millions, Euromillions**_.
    -   [_**<u>Optimizing the Skip Systems</u> in Lottery, Lotto, Gambling, Horse Racing**_](https://saliu.com/lotto-skips.html).
    -   [_**Lottery Utility Software**_](https://saliu.com/lottery-utility.html): Pick-3, 4 Lottery, Lotto-5, 6, Powerball, Mega Millions, Thunderball, Euromillions.
    -   [_**Lotto Filters, Reduction Strategies in Lottery Software**_](https://saliu.com/filters.html).
    -   _"The Start Is the Hardest Part"_: [_**Play a Lotto Strategy, Lottery Strategies**_](https://forums.saliu.com/lottery-strategies-start.html); Work with Data Files.
    -   [_**Artificial Intelligence AI, Axiomatic Intelligence AxI, Neural Networks in Lottery: Strategies, Systems, Software**_](https://saliu.com/neural-networking-lottery.html).
    -   [_**A Practical Essay on Artificial Intelligence, AI Chatbots Regarding Ion Saliu**_](https://saliu.com/ai-chatbots-ion-saliu.html).
    -   [_**Lotto wheels**_](https://saliu.com/lotto_wheels.html) for lotto games drawing 5, 6, or 7 numbers.  
        The most advanced theory of lotto wheels or reduced lottery systems. Get also original lotto wheels for lotto games drawing 5, 6, or 7 numbers: Balanced, randomized, and free.
    -   _**Download lottery software, lotto**_ [**software**](https://saliu.com/infodown.html).
    
    ![Improve lotto odds my a great margin using skip, frequency, pairing strategies.](https://saliu.com/bbs/messages/HLINE.gif)
    
    **[Home](https://saliu.com/index.htm) | [Search](https://saliu.com/Search.htm) | [New Writings](https://saliu.com/bbs/index.html) | [Software](https://saliu.com/infodown.html) | [Odds, Generator](https://saliu.com/calculator_generator.html) | [Contents](https://saliu.com/content/index.html) | [Forums](https://forums.saliu.com/) | [Sitemap](https://saliu.com/sitemap/index.html)**
    
    ![A study on filtering, median, pairs in lotto, lottery software, systems, strategies.](https://saliu.com/bbs/messages/HLINE.gif)
