---
created: 2025-07-24T22:09:20 (UTC +08:00)
tags: [source code,software,programs,Basic,language,PowerBasic,compiler,permanent membership]
source: https://saliu.com/software-code.html
author: <PERSON>
---

# <PERSON> 創建的軟體和程式的源代碼 --- Source Code for Software, Programs Created by <PERSON>

> ## Excerpt
> <PERSON> makes available the source code of some of his software programs. Programming language: BASIC, ready to run in PowerBasic.

---
<big>•</big> 請閱讀[軟體下載](https://saliu.com/infodown.html)主頁上的所有相關資訊。建議在存取「免費彩票、樂透、賭博、科學軟體：FTP 下載」之前不要造訪此頁面。如果出現問題，您可能需要返回該頁面並仔細閱讀說明。

<big>•</big> 不過，您下載的軟體原始碼可以無限期免費運作。它沒有任何功能缺陷，也沒有任何附加條件。它不是共享軟體：它是完全免費的軟體。請閱讀條款和條件： [下載優秀的免費軟體：需要付費會員資格](https://saliu.com/membership.html) 。

<big>•</big> 您也可以在此下載跨司法管轄區彩券遊戲的真實開獎結果，例如美國強力球 (Powerball)、美國超級百萬 (Mega Millions) 和歐盟國家歐洲百萬 (Euromillions)。這些開獎結果以文字檔案格式保存，可供 LotWon 彩票軟體、 MDIEditor 和 Lotto WE 使用。

[BJODDS](https://saliu.com/BjOdds/BJODDS) — 包含原始碼。  
BjOdds — 全新二十一點軟體能夠精確產生所有有利情況（爆牌）和所有可能情況（二十一點行動總數）。因此，首次實現了對二十一點賠率和賭場優勢的精確數學計算。歷史演算法現已發布，供所有人查看、閱讀、理解和全面驗證。先前由 John Scarne 計算的爆牌賠率存在較大的近似值。下載並解壓縮軟體包後，請務必盡快閱讀 ReadMe.txt 檔案。

[BjOdds：二十一點軟體，可精確計算爆牌機率、莊家優勢、莊家優勢](https://saliu.com/blackjackodds-software.html) 。  
BJODDS 是一個壓縮的自解壓縮檔。  
版本 21，2010 年 8 月，32 位元 DOS 軟體（即_**指令提示符**_ ，包含 64 位元 Vista/Windows 7）。

[BreakDownNumbers](https://saliu.com/code/BreakDownNumbers) 樂透工具軟體。  
BreakDownNumbers 採用一行或多行數字，並將每行分解為更小的數字組：從 1 個數字組到每個組合（行）7 個數字的組。  
原始行中的整數必須是文字格式，數字之間以空格或逗號分隔；1、2、3、... 或 1 2 3...  
每個數字組將按升序排序（每行）；重複的組將被刪除；即，這些組將是唯一的。  
在留言板上閱讀更多內容： _策略錯誤：雙數、三數、四數、五數_ 。  
在 32 位元 DOS 7+（Win 95/98/Me/NT4/2000/XP/Vista/Windows 7 下的命令提示字元）下執行的 32 位元軟體。  
版本 1.0，2010 年 7 月 23 日〜包含原始碼：  
[細分數字.BAS](https://saliu.com/code/BreakDownNumbers.BAS) 。  
程式語言：Basic；可在 32 位元 PowerBasic 控制台編譯器中運作。

[LexicographicAlgorithms.bas](https://saliu.com/code/LexicographicAlgorithms.bas) 包含兩種演算法，用於計算組合的字典順序（或排名或索引）；反過來，產生給定字典順序或排名的組合。也稱為樂透組合序號 (CSN) 問題。第一種演算法由 BP Buckles 和 M. Lybanon 提出；第二種演算法由 Ion Saliu 提出。  
[LexicographicAlgorithms](https://saliu.com/code/LexicographicAlgorithms) 是編譯的程式： [演算法，用於計算組合字典順序、排名的軟體](https://saliu.com/bbs/messages/348.html) ；和： [字典順序：索引、排名、演算法、組合、排列](https://saliu.com/lexicographic.html) 。  
版本 1.0，2009 年 10 月，在 DOS 7+ 下運行的 32 位元軟體（Win 95/98/Me/NT4/2000/XP/Vista 中的命令提示字元）。  
程式語言：Basic；可在 32 位元 PowerBasic 控制台編譯器中運作。

[Fibonacci.bas](https://saliu.com/code/Fibonacci.bas) ，產生斐波那契數併計算斐波那契數列兩個連續項之間的黃金分割率的數學軟體。  
斐波那契是編譯的程序： [圓周率日、圓周率、神聖比例、黃金比例、黃金數、斐波那契數列](https://saliu.com/bbs/messages/958.html) ；以及： [斐波那契數列：數學、賭博、軟體、黃金數](https://saliu.com/Fibonacci.html) 。  
程式語言：Basic；可在 32 位元 PowerBasic 控制台編譯器中運作。

[FileLines.BAS](https://saliu.com/code/FileLines.BAS) 是一款彩票軟體，它將指定索引的行寫入磁碟，該檔案通常由 STRAT\* 建立策略檔案。這代表了使用 LotWon 以及 MDIEditor & Lotto 時的一種情況。例如：您在 DOS（命令提示字元）LotWon 中建立了 WS 檔案。您也在 MDIEditor & Lotto 中產生了統計報告。然後，您在 MDIEditor & Lotto 中建立了用於統計的策略檔案。您不能在兩個平台上使用同一個策略文件。您希望 WS 文件中的行號相同，以便制定更全面的策略。  
FileLines 是編譯後的程式： [“LotWon、MDIEditor 和 Lotto WE 所建立的交叉引用策略檔”](https://saliu.com/cross-lines.html) 。另可下載範例輸入檔： [INP-FILE.TXT](https://saliu.com/pub/INP-FILE.TXT) 。  
程式語言：Basic；可在 32 位元 PowerBasic 控制台編譯器中運作。

[PARSEL.BAS](https://saliu.com/code/PARSEL.BAS) . 解析彩票資料檔以確保其符合 LotWon 軟體要求的格式的軟體。  
PARSEL 是編譯程式： [用於修正彩票、樂透資料檔案中大多數錯誤的軟體](https://saliu.com/bbs/messages/2.html) 。  
程式語言：Basic；可在 32 位元 PowerBasic 控制台編譯器中運作。

[UpDown.bas](https://saliu.com/code/UPDOWN.BAS) 是一款彩票軟體，可以反轉 ASCII 檔案中的順序：檔案底部會成為新檔案的頂部。該程式有助於按照 LotWon 軟體所需的順序排列彩票資料檔案。一些不良彩票網站會以不自然的順序發布彩票歷史記錄：最新開獎的彩票會放在文件底部，而不是頂部。作為 Lotwonista，您總是會從最近一次開獎開始，一直到最早的彩票開獎（文件底部）。  
UpDown 是編譯程式： [用於反轉彩票、樂透結果、圖面檔案中的順序的軟體](https://saliu.com/bbs/messages/539.html) 。  
程式語言：Basic；可在 32 位元 PowerBasic 控制台編譯器中運作。

[Writer.bas](https://saliu.com/code/Writer.bas) 根據拉丁字母表中的 26 個字母（包括 W 和 Y）加上一個空格，產生隨機單字和句子。生成過程完全隨機，但有一個限制：每個單字必須至少包含一個語音基因──也就是元音。它也可以用作強密碼產生器——您只需在單字中添加一兩個數字。  
Writer，是編譯程式：寫隨機單字、句子、密碼和…書籍的電腦程式！  
程式語言：Basic；可在 32 位元 PowerBasic 控制台編譯器中運作。

[SuperRoulette.BAS](https://saliu.com/code/SuperRoulette.BAS) 可能是輪盤賭軟體程式設計中最先進、最有前途的行為。  
經過編譯的 SuperRoulette 程序，附帶令人難以置信的超級輪盤賭策略，以及有史以來最佳的輪盤賭系統，現已發布 = 現已免費！仔細閱讀，因為您可能會中大獎： [超級輪盤賭策略](https://saliu.com/best-roulette-systems.html) 。  
程式語言：Basic；可在 32 位元 PowerBasic 控制台編譯器中運作。

[SPINS.BAS](https://saliu.com/code/SPINS.BAS) ，輪盤統計軟體。  
SPINS 是一款經過編譯的程序，它是一款出色的輪盤旋轉生成應用程序，同時也是一款輪盤旋轉的統計分析器。該程式也是 [_**「輪盤系統理論、數學、策略、軟體」**_](https://saliu.com/Roulette.htm) 中介紹的免費輪盤系統 #1 的絕佳補充。根據該系統，建議在「結果 +/-」欄中連續出現 2 個「-」後進行遊戲。  
程式語言：Basic；可在 32 位元 PowerBasic 控制台編譯器中運作。

[Streaks.bas](https://saliu.com/code/Streaks.bas) ，機率論軟體。  
編譯後的程式 Streaks.exe 可以計算多次試驗中出現相同結果的次數。例如：在 1000 次拋硬幣中，有多少次出現了剛好連續 5 次正面朝上的現象？  
閱讀更多： [賭博數學：有關線上賭博、網路賭場的反應和立法](https://saliu.com/gambling-mathematics.html) 。  
程式語言：Basic；可在 32 位元 PowerBasic 控制台編譯器中運作。

[SORTING.BAS](https://saliu.com/code/SORTING.BAS) ，彩券、樂透、賽馬軟體。  
編譯後的程式 SORTING.exe 會依照升序對實際彩券開獎或組合進行排序。它可以處理樂透 4、樂透 5、樂透 6、樂透 7、強力球“5+1”和歐洲百萬彩票“5+2”以及五位數彩票。改進後的版本還可以格式化選 3、選 4 和賽馬（三連勝）資料檔。  
程式語言：Basic；可在 32 位元 PowerBasic 控制台編譯器中運作。

[RandomNumbers.bas](https://saliu.com/code/RandomNumbers.bas) ，用於產生真正隨機且唯一數字的 BASIC 語言程式的原始程式碼。  
閱讀更多： [真隨機數產生器：BASIC 程式原始碼、演算法](https://saliu.com/random-numbers.html) 。  
程式語言：Basic；可在 32 位元 PowerBasic 控制台編譯器中運作。

![ The software source code you download, however, is free to use for an unlimited period of time.](https://saliu.com/HLINE.gif)

![Download your software, source code, gambling systems, strategy for casino roulette, baccarat, and blackjack.](https://saliu.com/HLINE.gif)

**[首頁](https://saliu.com/index.htm) | [搜尋](https://saliu.com/Search.htm) | [新文章](https://saliu.com/bbs/index.html) | [基本公式](https://saliu.com/formula.htm) | [賠率產生器](https://saliu.com/calculator_generator.html) | [目錄](https://saliu.com/content/index.html) | [論壇](https://forums.saliu.com/) | [網站地圖](https://saliu.com/sitemap/index.html)**

![Exit the best site of software downloads for lottery, gambling, science, and chance!](https://saliu.com/HLINE.gif)
