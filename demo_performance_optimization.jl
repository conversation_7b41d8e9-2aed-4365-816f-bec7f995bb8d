#!/usr/bin/env julia

"""
Demonstration script for Wonder Grid Performance Optimization System
Shows performance improvements, memory efficiency, and caching benefits
"""

using Dates

# Include required modules
include("src/wonder_grid_engine.jl")
include("src/backtesting.jl")
include("src/performance_optimization.jl")

"""
Demonstrate performance optimization features
"""
function demo_performance_optimization()
    println("🚀 Wonder Grid Performance Optimization Demo")
    println("=" ^ 70)
    
    # Demo 1: Data structure efficiency
    println("\n🏗️  Demo 1: Optimized Data Structures")
    println("-" ^ 50)
    
    demo_data_structure_efficiency()
    
    # Demo 2: Caching benefits
    println("\n💾 Demo 2: Caching System Benefits")
    println("-" ^ 50)
    
    demo_caching_benefits()
    
    # Demo 3: Memory management
    println("\n🧠 Demo 3: Memory Management")
    println("-" ^ 50)
    
    demo_memory_management()
    
    # Demo 4: Performance monitoring
    println("\n📊 Demo 4: Performance Monitoring")
    println("-" ^ 50)
    
    demo_performance_monitoring()
    
    # Demo 5: Benchmark comparison
    println("\n🏁 Demo 5: Performance Benchmark")
    println("-" ^ 50)
    
    demo_benchmark_comparison()
    
    # Demo 6: Memory profiling
    println("\n🔍 Demo 6: Memory Profiling")
    println("-" ^ 50)
    
    demo_memory_profiling()
    
    println("\n✅ Performance Optimization Demo Complete!")
    println("=" ^ 70)
end

"""
Demonstrate data structure efficiency
"""
function demo_data_structure_efficiency()
    println("Comparing standard vs optimized data structures...")
    
    # Standard combination storage
    standard_combinations = [
        [1, 5, 12, 23, 39],
        [3, 7, 15, 28, 35],
        [2, 11, 19, 31, 37],
        [4, 9, 16, 25, 33],
        [6, 13, 20, 29, 38]
    ]
    
    # Optimized combination storage
    optimized_combinations = [
        OptimizedCombination([1, 5, 12, 23, 39]),
        OptimizedCombination([3, 7, 15, 28, 35]),
        OptimizedCombination([2, 11, 19, 31, 37]),
        OptimizedCombination([4, 9, 16, 25, 33]),
        OptimizedCombination([6, 13, 20, 29, 38])
    ]
    
    # Calculate memory usage
    standard_size = sizeof(standard_combinations) + sum(sizeof(combo) for combo in standard_combinations)
    optimized_size = sizeof(optimized_combinations) + sum(sizeof(opt_combo.numbers) for opt_combo in optimized_combinations)
    
    memory_savings = (standard_size - optimized_size) / standard_size * 100
    
    println("Memory Usage Comparison:")
    println("  Standard combinations: $standard_size bytes")
    println("  Optimized combinations: $optimized_size bytes")
    println("  Memory savings: $(round(memory_savings, digits=1))%")
    
    # Test conversion performance
    println("\nConversion Performance:")
    
    # Time standard access
    start_time = time()
    for _ in 1:10000
        for combo in standard_combinations
            sum(combo)  # Simple operation
        end
    end
    standard_time = time() - start_time
    
    # Time optimized access
    start_time = time()
    for _ in 1:10000
        for opt_combo in optimized_combinations
            combo_vec = convert(Vector{Int}, opt_combo)
            sum(combo_vec)  # Same operation
        end
    end
    optimized_time = time() - start_time
    
    println("  Standard access: $(round(standard_time * 1000, digits=2)) ms")
    println("  Optimized access: $(round(optimized_time * 1000, digits=2)) ms")
    
    if optimized_time < standard_time
        speedup = standard_time / optimized_time
        println("  ✅ Optimized is $(round(speedup, digits=2))x faster")
    else
        println("  ⚠️  Optimized has conversion overhead for this operation")
    end
end

"""
Demonstrate caching benefits
"""
function demo_caching_benefits()
    println("Demonstrating caching system benefits...")
    
    # Clear caches for clean demo
    clear_all_caches!()
    
    key_numbers = [7, 13, 21, 7, 13, 21]  # Repeated numbers to show cache benefits
    
    println("\nFFG Calculation Performance:")
    println("Key | First Call | Cached Call | Speedup")
    println("-" ^ 45)
    
    for key_number in unique(key_numbers)
        # First call (cache miss)
        start_time = time()
        ffg1 = calculate_ffg_optimized(key_number)
        first_call_time = time() - start_time
        
        # Second call (cache hit)
        start_time = time()
        ffg2 = calculate_ffg_optimized(key_number)
        cached_call_time = time() - start_time
        
        speedup = first_call_time / max(cached_call_time, 1e-9)  # Avoid division by zero
        
        println("$(lpad(key_number, 3)) | $(rpad(round(first_call_time * 1000000, digits=1), 10)) μs | $(rpad(round(cached_call_time * 1000000, digits=1), 11)) μs | $(round(speedup, digits=0))x")
    end
    
    # Show cache statistics
    cache_stats = get_cache_stats()
    println("\nCache Statistics:")
    println("  FFG Cache Size: $(cache_stats["ffg_cache_size"]) entries")
    println("  Pairing Frequency Cache: $(cache_stats["pairing_frequency_cache_size"]) entries")
    println("  Total Cached Items: $(cache_stats["total_cached_items"])")
    
    # Demonstrate pairing frequency caching
    println("\nPairing Frequency Caching:")
    
    calc = OptimizedPairingCalculator()
    add_combination!(calc, [1, 5, 12, 23, 39])
    add_combination!(calc, [3, 7, 15, 28, 35])
    
    # Multiple lookups of same pair
    test_pairs = [(1, 5), (3, 7), (1, 5), (3, 7), (1, 5)]
    
    for (num1, num2) in test_pairs
        freq = get_pairing_frequency(calc, num1, num2)
    end
    
    println("  Cache Hits: $(calc.cache_hits)")
    println("  Cache Misses: $(calc.cache_misses)")
    println("  Hit Rate: $(round(calc.cache_hits / (calc.cache_hits + calc.cache_misses) * 100, digits=1))%")
end

"""
Demonstrate memory management
"""
function demo_memory_management()
    println("Demonstrating memory management features...")
    
    # Memory pool demonstration
    println("\nMemory Pool Efficiency:")
    
    pool = MemoryPool{Vector{Int}}(() -> Vector{Int}(), empty!)
    
    # Without pool (creating new objects each time)
    start_time = time()
    GC.gc()
    memory_before = Base.gc_bytes()
    
    temp_objects = []
    for i in 1:1000
        obj = Vector{Int}()
        push!(obj, i, i+1, i+2)
        push!(temp_objects, obj)
    end
    
    GC.gc()
    memory_after_no_pool = Base.gc_bytes()
    time_no_pool = time() - start_time
    
    # With pool (reusing objects)
    start_time = time()
    GC.gc()
    memory_before = Base.gc_bytes()
    
    temp_objects_pool = []
    for i in 1:1000
        obj = acquire!(pool)
        push!(obj, i, i+1, i+2)
        push!(temp_objects_pool, obj)
    end
    
    # Return objects to pool
    for obj in temp_objects_pool
        release!(pool, obj)
    end
    
    GC.gc()
    memory_after_pool = Base.gc_bytes()
    time_pool = time() - start_time
    
    memory_saved = (memory_after_no_pool - memory_after_pool) / memory_after_no_pool * 100
    time_saved = (time_no_pool - time_pool) / time_no_pool * 100
    
    println("  Without Pool: $(round(time_no_pool * 1000, digits=2)) ms")
    println("  With Pool: $(round(time_pool * 1000, digits=2)) ms")
    println("  Time Savings: $(round(time_saved, digits=1))%")
    println("  Memory Efficiency: $(round(memory_saved, digits=1))% less allocation")
    
    # Batch processing demonstration
    println("\nBatch Processing Benefits:")
    
    generator = OptimizedCombinationGenerator(7, 50)  # Small batches for demo
    
    batch_count = 0
    total_combinations = 0
    processing_times = Float64[]
    
    for batch in generate_combinations_batch(generator)
        start_time = time()
        
        # Simulate processing each combination in batch
        for opt_combo in batch
            combo_vec = convert(Vector{Int}, opt_combo)
            # Simulate some work
            sum(combo_vec)
        end
        
        processing_time = time() - start_time
        push!(processing_times, processing_time)
        
        batch_count += 1
        total_combinations += length(batch)
        
        # Limit demo to first few batches
        if batch_count >= 3
            break
        end
    end
    
    avg_processing_time = sum(processing_times) / length(processing_times)
    
    println("  Processed $batch_count batches")
    println("  Total combinations: $total_combinations")
    println("  Average batch processing: $(round(avg_processing_time * 1000, digits=2)) ms")
    println("  Combinations per second: $(round(total_combinations / sum(processing_times), digits=0))")
end

"""
Demonstrate performance monitoring
"""
function demo_performance_monitoring()
    println("Demonstrating performance monitoring...")
    
    # Create optimized engine with monitoring
    opt_engine = OptimizedWonderGridEngine()
    
    # Perform various operations
    key_numbers = [7, 13, 21]
    
    println("\nPerforming monitored operations...")
    
    for key_number in key_numbers
        println("  Processing key number $key_number...")
        
        # Generate combinations (monitored)
        combinations = generate_combinations_optimized(opt_engine, key_number)
        
        # Create test data for backtesting
        test_draws = [
            LotteryDraw(Date("2023-01-01"), [1, 5, 12, 23, 39]),
            LotteryDraw(Date("2023-01-02"), [3, 7, 15, 28, 35])
        ]
        
        # Run backtest (monitored)
        backtest_result = run_backtest_optimized(opt_engine, combinations[1:min(5, length(combinations))], test_draws)
    end
    
    # Display performance statistics
    perf_stats = get_performance_stats(opt_engine.performance_monitor)
    
    println("\nPerformance Statistics:")
    println("  Total Runtime: $(round(perf_stats["total_runtime"], digits=3)) seconds")
    println("  Memory Used: $(round(perf_stats["memory_used_mb"], digits=2)) MB")
    
    println("\nOperation Breakdown:")
    for (operation, stats) in perf_stats["operations"]
        println("  $operation:")
        println("    Count: $(stats["count"])")
        println("    Total Time: $(round(stats["total_time"], digits=3)) seconds")
        println("    Average Time: $(round(stats["average_time"] * 1000, digits=2)) ms")
        println("    Min/Max Time: $(round(stats["min_time"] * 1000, digits=2))/$(round(stats["max_time"] * 1000, digits=2)) ms")
    end
end

"""
Demonstrate benchmark comparison
"""
function demo_benchmark_comparison()
    println("Running performance benchmark comparison...")
    
    key_number = 7
    iterations = 10  # Reasonable number for demo
    
    println("  Benchmarking with key number $key_number over $iterations iterations...")
    
    benchmark_results = benchmark_performance(key_number, iterations)
    
    println("\nBenchmark Results:")
    println("=" ^ 40)
    
    # Standard engine results
    standard = benchmark_results["standard_engine"]
    println("Standard Engine:")
    println("  Average Time: $(round(standard["average_time"] * 1000, digits=2)) ms")
    println("  Min Time: $(round(standard["min_time"] * 1000, digits=2)) ms")
    println("  Max Time: $(round(standard["max_time"] * 1000, digits=2)) ms")
    println("  Total Time: $(round(standard["total_time"], digits=3)) seconds")
    
    # Optimized engine results
    optimized = benchmark_results["optimized_engine"]
    println("\nOptimized Engine:")
    println("  Average Time: $(round(optimized["average_time"] * 1000, digits=2)) ms")
    println("  Min Time: $(round(optimized["min_time"] * 1000, digits=2)) ms")
    println("  Max Time: $(round(optimized["max_time"] * 1000, digits=2)) ms")
    println("  Total Time: $(round(optimized["total_time"], digits=3)) seconds")
    
    # Performance improvement
    println("\nPerformance Improvement:")
    println("  Speedup Factor: $(round(benchmark_results["speedup_factor"], digits=2))x")
    println("  Improvement: $(round(benchmark_results["improvement_percentage"], digits=1))%")
    
    # Visual comparison
    println("\nVisual Performance Comparison:")
    standard_bar_length = 50
    optimized_bar_length = round(Int, standard_bar_length / benchmark_results["speedup_factor"])
    
    println("  Standard:  $("█" ^ standard_bar_length)")
    println("  Optimized: $("█" ^ optimized_bar_length)")
    
    # Cache statistics
    cache_stats = benchmark_results["cache_stats"]
    println("\nCache Utilization:")
    println("  Total Cached Items: $(cache_stats["total_cached_items"])")
    println("  FFG Cache: $(cache_stats["ffg_cache_size"]) entries")
    println("  Pairing Cache: $(cache_stats["pairing_frequency_cache_size"]) entries")
end

"""
Demonstrate memory profiling
"""
function demo_memory_profiling()
    println("Demonstrating memory profiling capabilities...")
    
    # Profile standard Wonder Grid operation
    println("\nProfiling Standard Wonder Grid Operation:")
    
    standard_operation = function(key_num::Int)
        engine = WonderGridEngine()
        return generate_combinations(engine, key_num)
    end
    
    standard_profile = profile_memory_usage(standard_operation, 7)
    
    println("  Memory Used: $(round(standard_profile["memory_used_mb"], digits=2)) MB")
    println("  Execution Time: $(round(standard_profile["execution_time"] * 1000, digits=2)) ms")
    println("  Combinations Generated: $(standard_profile["result_size"])")
    println("  Memory per Combination: $(round(standard_profile["memory_per_item"], digits=0)) bytes")
    
    # Profile optimized Wonder Grid operation
    println("\nProfiling Optimized Wonder Grid Operation:")
    
    optimized_operation = function(key_num::Int)
        engine = OptimizedWonderGridEngine()
        return generate_combinations_optimized(engine, key_num)
    end
    
    optimized_profile = profile_memory_usage(optimized_operation, 7)
    
    println("  Memory Used: $(round(optimized_profile["memory_used_mb"], digits=2)) MB")
    println("  Execution Time: $(round(optimized_profile["execution_time"] * 1000, digits=2)) ms")
    println("  Combinations Generated: $(optimized_profile["result_size"])")
    println("  Memory per Combination: $(round(optimized_profile["memory_per_item"], digits=0)) bytes")
    
    # Calculate improvements
    memory_improvement = (standard_profile["memory_used_mb"] - optimized_profile["memory_used_mb"]) / standard_profile["memory_used_mb"] * 100
    time_improvement = (standard_profile["execution_time"] - optimized_profile["execution_time"]) / standard_profile["execution_time"] * 100
    
    println("\nOptimization Benefits:")
    println("  Memory Reduction: $(round(memory_improvement, digits=1))%")
    println("  Time Reduction: $(round(time_improvement, digits=1))%")
    
    # Profile different data sizes
    println("\nScalability Analysis:")
    println("Key | Combinations | Memory (MB) | Time (ms) | MB/1000 combos")
    println("-" ^ 65)
    
    test_keys = [3, 7, 11, 13]
    
    for key_num in test_keys
        profile = profile_memory_usage(optimized_operation, key_num)
        
        memory_mb = profile["memory_used_mb"]
        time_ms = profile["execution_time"] * 1000
        combo_count = profile["result_size"]
        memory_per_1000 = combo_count > 0 ? memory_mb / combo_count * 1000 : 0
        
        println("$(lpad(key_num, 3)) | $(lpad(combo_count, 12)) | $(lpad(round(memory_mb, digits=2), 11)) | $(lpad(round(time_ms, digits=1), 9)) | $(lpad(round(memory_per_1000, digits=2), 15))")
    end
end

"""
Interactive performance demo
"""
function interactive_performance_demo()
    println("\n🎮 Interactive Performance Demo")
    println("=" ^ 50)
    
    println("This demo allows you to test performance with custom parameters.")
    
    print("Enter key number to test (1-39) [7]: ")
    key_input = strip(readline())
    key_number = isempty(key_input) ? 7 : parse(Int, key_input)
    
    if !(1 <= key_number <= 39)
        println("Invalid key number, using default: 7")
        key_number = 7
    end
    
    print("Enter number of benchmark iterations [5]: ")
    iter_input = strip(readline())
    iterations = isempty(iter_input) ? 5 : parse(Int, iter_input)
    
    if iterations < 1
        println("Invalid iterations, using default: 5")
        iterations = 5
    end
    
    println("\n🚀 Running custom benchmark...")
    println("  Key Number: $key_number")
    println("  Iterations: $iterations")
    
    # Run custom benchmark
    results = benchmark_performance(key_number, iterations)
    
    println("\n📊 Custom Benchmark Results:")
    println("  Speedup: $(round(results["speedup_factor"], digits=2))x")
    println("  Improvement: $(round(results["improvement_percentage"], digits=1))%")
    println("  Standard Average: $(round(results["standard_engine"]["average_time"] * 1000, digits=2)) ms")
    println("  Optimized Average: $(round(results["optimized_engine"]["average_time"] * 1000, digits=2)) ms")
    
    # Memory profiling
    println("\n🧠 Memory Profile:")
    profile = profile_memory_usage(key_num -> generate_combinations_optimized(OptimizedWonderGridEngine(), key_num), key_number)
    println("  Memory Used: $(round(profile["memory_used_mb"], digits=2)) MB")
    println("  Combinations: $(profile["result_size"])")
    println("  Memory Efficiency: $(round(profile["memory_per_item"], digits=0)) bytes/combination")
end

# Run the demo
if abspath(PROGRAM_FILE) == @__FILE__
    demo_performance_optimization()
    interactive_performance_demo()
end