---
created: 2025-01-03T19:52:42 (UTC +08:00)
tags: [software,utilities,program,programs,Powerball,Mega Millions,Euromillions,lotto,loto,lottery,horse racing,loterie,loteria,]
source: https://saliu.com/software-lotto-lottery.html
author: 
---

# Software Lotto, Lottery, Gambling, Loto Loteria Loterie

> ## Excerpt
> Lottery software, utilities for lotto, gambling, loterie, loteria, Powerball, Mega Millions, Euromillions make every user a lotto winner, including the jackpot.

---
The final version written by <PERSON> on July 15, 2004 (4 WE).

## <u>1. Software Upgrades</u>

<big>Util532, Util632</big> version 11.0, May 21, 2004 (4 WE) - Lotto Freeware.  
Major bug fixes and improvements in performance in version 11.0.  
There are many new features in the 32-bit lotto software utilities. Convert lotto drawings to regular sums and single-digit (root) sums. The Ion\_3 filter in **MDIEditor Lotto WE** is based on the _root sum_ (_Fadic addition_). Create the _wonder grid_ (pairings file) for any number of top pairs. Generate combinations based on the most frequent numbers position by position.

Also available the 32-bit lottery utility applications for pick-3, pick 4, and horse racing: <big>Util332, Util432, Util-H32.</big>

Available for the first time as freeware: **UtilPB,** the utility for Powerball, Mega Millions, CA SuperLotto. The utility calculates more precisely the winning combinations. The function follows more closely the procedure of calculating the lotto odds for Powerball or Mega Millions. Seven prizes are checked: 0 regular plus Powerball; then from 3 regular numbers and NO Powerball to 5 regular plus the _power ball_ (or _mega ball_.

I made the same correction to the Powerball function in **WINNERS** (version 3.1 2004; also better user error handling).

The statistical function does calculations for the 6 positions of the Powerball, Mega Millions, CA SuperLotto. The player can select the most frequent numbers for each position. See the sample file _RANGEPB_ consisting of the most frequent positional numbers in Powerball 5/53/42 for 184 draws. _RANGEPB_ can serve as an input file to the _POSITIONAL LIMITS_ function in **UtilPB**. It represents a very powerful way to put together only Powerball numbers of high frequency. If the numbers in each position represent 50% of total frequency, the final jackpot probability is 0.5<sup>6</sup> = 1 in 64. If the numbers in each position represent 60% of total frequency, the final jackpot probability improves to 0.6<sup>6</sup> = 1 in 22. The result file can be diminished further by using the PURGE routines in **MDIEditor and Lotto** or Command Prompt **LotWon**.

Older _INI_ files might create some errors. It is recommended to delete the _Pick\*.INI_ files before running the utilities for the first time, or IF errors occur. Incorrect formats of the data files create also errors. Run **PARSEL** from time to time.

-   Ahoe! These software utilities were greatly superseded by **Super Utilities - SoftwareLotto** (main menu of **Bright / <u>Ultimate</u>** software packages). The lottery, lotto utility software is no longer offered as standalone.
-   The programs are organically integrated in the incredibly powerful and comprehensive software packages named **Bright3, Bright4, BrightH, Bright5, Bright6**.
-   Of course, such power houses are no longer freeware! But if you decide to pay, rest assured that it is but a small fraction of what other developers/vendors ask for their lottery software programs (that virtually do nothing by comparison!)

[![Lottery Software: Jackpot Lotto, Pick Lotteries, Powerball, Mega Millions, Euromillions, Keno.](https://saliu.com/ScreenImgs/best-lotto-utilities.gif)](https://saliu.com/free-lotto-lottery.html)

## <u>2. Complaints, Questions, Answers Regarding Ion Saliu's Freeware</u>

_“Someone who is familiar with Ion Saliu's Util-6 can explain me why do I have following discrepancy? He does not answer to technical questions to his software, he does not support it, that's why. - miami24”

“Well miami24,  
If you cannot use a software without problems and the developer offers no support, there are only 2 solutions as seen from a blond point of view:  
1- Dump that lotto software and use something else.  
2- Make your own lottery software. – Babarlish”

_

_"I have no idea how to write software, I'm not a programmer.  
I have tried many free programs (and have bought one of them) and they are not as good as Saliu's program should be. I'm interested in his theories as well.  
I've asked in this lottery forum because someone may have a useful tip. Maybe someone tried his software and have an idea what the problem could be. Maybe even Saliu himself may have compassion if he reads it. Don't be offended, but I expect real help here (if someone can help, of course) and I don't think other people who haven't used his software can be of help."_

My young friend, Critser Thornc, the Roman Portuguese, responded:

_"Just to show you how friendly we are here, I used the <u>Canada-6/49</u> lotto history and fed it into both the programs you mentioned! These are the first lines of FREQ6 for Can649.txt:_

_They are equal! Notice, however, that they are from different files because **MDIEditor and Lotto** requires files with more than 10000 lines in it! (Ion's note: More than 200,000 lines is even better!)_

_What I did was to get the history from BCLC, copy-paste it to c649.txt file, run my **CoolRevGui** on it to reverse it (yes, I'm the author of **CoolRevGui**!) and fed it to Util-6. Then I used util-6 to simulate a 10000-line of lotto6, appended it to the end of c649.txt and named the new one c639-10k.txt fed this to **MDIEditor and Lotto** and got the results!_

_My first guess at your problem is that you either didn't reverse the files; append the simulated lines to wrong end; or a combination of both!_

_Everything Depends; Nothing is Always; Everything is Sometimes -- Unix fortune output!"_  
(_Ion Saliu's note: Great adagio, Critser, super crocodilule axiomatic! Would you, please put it in Latin, then in Portuguese? It sounds kind of Greek to me!_)  

I noticed lots of links to my Web site from this thread at the lotto 6/49 message board in Canada. As the original poster noticed, I don't respond to most messages I receive directly. There are already too many to read. More than one million new visitors a year hit my Web site. The vast majority of the questions I receive are _bona fide_. But a few of the inquirers are hard-core crooks who only want to disrupt me, if possible. It ain't possible, though! Nobody and nothing can disrupt me, if I may paraphrase Critser. Unfortunately, I can no longer answer the _bona fide_ messages, either. There are way too many in my inboxes!

I have added 32-bit replacements to the **UTIL** software at my download site. Actually, not real replacements, because the oldies are still there. Follow the link in the _Resources_ section, or click the banner at the top of this page.

**• UTIL-5; UTIL-6**. These are statistical analysis utilities for the lotto-5 and lotto-6 games. Keep those programs handy, but use the 32-bit versions: **UTIL532, UTIL632**. The 32-bit lotto programs work with gigantic data files, long file names, and have more potent features. Pay attention to the last function under _Make/Break/Position_. Take one or two loto numbers with lower median skip(s) and a good probability of hitting in the next drawing. Add to the numbers their own top 8 or 10 pairings; or add the top 8 or 10 pairs of another frequent number that is not expected to hit the next draw. _Break_ the 9/11-number strings into 6-number combinations.

The results are astonishing, more often than not! If budget doesn't allow playing all the lotto combinations, then shuffle the combination file and play combinations from the middle (approximately the _FFG median_). No matter what combinations you play, the following is a constant rule. The combinations from inside the _FFG median bell_ always show a better frequency—around 25% better frequency. Read these convincing articles (conviction founded on mathematics and undeniable formulas):

-   [_**<u>FFG Median</u>, Gauss, Bell, Curve, Random Number, Combination Generator**_](https://saliu.com/median_bell.html)
-   [_**Playing Combinations around <u>FFG Median</u> Improve Lotto <u>Jackpot</u> Odds Sevenfold**_](https://saliu.com/bbs/messages/923.html).

Pain is sometimes beneficial. I lifted tons, I mean tons of books, last month. Even a toughest guy will fall in bed thereafter. But the rest gave me the chance to read more _goingons_ at my site, or related to my website. NO, I can't respond personally, individually, no matter who or what. But keep in mind that there is nothing out there with a more solid foundation AND a more honest foundation than the lottery software known as _**LotWon**_ or _**Ion Saliu's software for command prompt**_. No false modesty — only the truth.

You can pay for other lottery software, of course. Paying is always an option, in every culture and historical period. In the particular case of lottery/gambling, however, you can pay as much as you want — but get nothing real in exchange. The only exception is **LotWon**. The exception is not in price (free or much lower prices), but in the workings also. Yes, your phone calls may be answered nicely, usually by nice female voices — at other sites. Even your email messages may be answered nicely, but without any substance. Gambling/lottery are nothing but gut feeling anywhere you look at — except for my website.

Having more personal time, I asked myself: _“Self, what is the worst thing about Ion Saliu's lotto software? Why don't I, the most knowledgeable user of Ion Saliu's software win BIG?”_ I tell you firsthand what the worst thing about Ion Saliu's software is. It is the jungle of options the software offers. A jungle of options requires a jungle of time. I repeat what I said many times at my site. I was a lot more successful using my lottery software when the programs were less powerful than they are now. The explanation is: I played much more before. There are many, many more options. Yet, only a few options are all that's needed to crush the odds. The key strategy is to focus on one, _just one lottery filter_.

Get on a filter value way outside the median range — something like 4 times or more the median/the median divided by 4 or more times. That filter value alone reduces the odds tremendously. Not only the random expectation is beaten, but also even the lottery player's disadvantage is overcome.

The _cost of winning_ (the holy _COW_) is still expensive, however. We need use several other filters—and plenty of them are available. The lottery strategy checking utilities show very clearly how other filters would have fared. We don't need to enable every filter in the lottery strategy checking utility. Only enabling a minority of other filters leads to a drastic reduction in the playing cost. One Balkan correspondent told me how he used the _Ver6_ filter. He used other filters with very tight settings. The player missed the lotto jackpot, although he hit a dozen _4 of 6_, or so.

![Many pathetically jealous authors vendors of lotto software or systems hate and attack Ion Saliu, creator of the best lottery software in the world.](https://saliu.com/HLINE.gif)

I tell you what, Kokostirk. Complaining to the search engines that I “fool” them to send me visitors won't cut it. Every normal person realizes that my website is absolutely and honestly the best of its kind. The other thing you trying, Kulai Parakelsus, Kotskarr _et al._, won't do it, either. That is, trying lots of hits to my Web site just to slow it down, or even shut it down. Those who crave for bad things can always read this verse at bedtime. Their dreams are easy to predict, almost easier than the loterie:

-   _“He shall feed their hunger with the sands of the shore and quench their thirst in the depths of the sea.”_

Perhaps the complaining and hacking work, unfortunately. Look what Dracgoogla and other search engines throw at you lately. Just search on _lottery lotto software_. You get a lot of garbage on the top result pages… The likes of _lotto-logix.com, ldir.com, lotwin.com, smartluck.com, lottomania2000.ne, windowslotto.com, timersoft.com  Lotto Logic Pro, magayo.com, versabet.com, use4.com, lottostrategies.lottoguy, justlottery.com, alllottoresults.com, lottogenius.com_… and on, and on…

They all claim that their lottery software is **THE BEST**. Some of those lottery software packages are **very expensive**. I guarantee you this, even in a court of law: Those software packages **do virtually nothing**. For the most part, said software packages collect for free lottery results (drawings) from the websites of the lottery commissions. Then, they absolutely cheat when they call every results file a lotto program! Now, that's big-time deception! [_**Every lottery player can easily copy and paste for free every lottery result (lotto draw) at Web sites of lottery commissions or agents**_](https://saliu.com/bbs/messages/920.html).

The “best” of that lottery software garbage packages add lotto wheels to the menu. But keep also in mind that just about all lotto wheels are free on the Internet! And I am the only one who offers free lotto wheeling software to convert the theoretical wheels to playing tickets with the player's picks (actual numbers to play): **LottoWheeler**! It's the only lottery wheeling program that works with Powerball, Mega Millions, Euromillions. As a _Thank You_ note, my lottery, lotto software is nowhere to be found in searches nowadays! _O tempora! O mores!_

Keep in mind that most lotto / lottery / gambling authors / developers / vendors always hide their identity in public places. They never write about their lottery software/systems and sign with their authentic names. They write under assumed Internet names. They pose as _bona fide_ users of said lotto software or systems. They are not real users. They're trolling for their “creations”. It's a simple advertising scheme.

I do not do that. This thread that made me write this article came as a full surprise to me. I just saw another thread at the “Lottery Post” message board (started in May of 2004). I did not suggest the idea of writing about me. In fact, somebody I suspect to be from the Kokostirk & Kotkoduck (_BobP_ at _lotterypost_.com) & Kotskarr & Kulai Parakelsus (un)law firm. His “idea” is that one would spend a lot of time at my Web site, only to lose one's sanity. Tell you what. If one has sanity before getting to my site, one would not lose an iota of sanity. But if one is kind of stupidiot, one has all the chances to lose some idiotism. That result can be painful. Now, I am aware also that I increase the insanity level of those who are already plenty insane!

So, I have settled for just a few filters of my own. The lottery software works with data files of over 200000 (two hundred thousand) combinations for lotto-5 and 2500000 (two million five hundred thousand) for loto 6. The layers are, respectively, 25000 and 250000 combinations apart. Even **MDIEditor Lotto WE** works with four layers. I noticed, for example, that _FivR(S)_ = 200,000+ for lotto 5 can achieve a ratio of one million in cost / to 1.8 million in winning. Using more filters reduces the winning, but also makes the cost much more manageable. Or just shuffling the combination file and selecting combinations from the _middle_ of the output _dossier_ — still offers me far better odds than playing anything else (lotto wheels, or quick-picks, or random selection).

There is a convincing fact in the optimized simulated lotto draws as implemented in **Util532 / Util632**. Accept the default for amount of draws to simulate (10200). Next, purge the resulting SIM file, using the _Strip Duplicates_ modules in the two applications. I repeated the procedure numerous times. In the case of the lotto 6/49 game, the _SIM-6_ file has an average of 2-3 duplicate combinations (all 6 numbers are repeats). In the case of the lotto 5/39 game, the _SIM-5_ file has an average of 100 duplicate lotto combinations (all 5 numbers are repeats).

The optimized combinations as generated by **Util532, Util632** have a hugely superior jackpot-hit rate. I noticed in my own data files. I also remember two users of **LotWon** who complained that my programs missed the jackpot-winning combinations, despite the correct setting of the filters. The problem is not obvious and is determined by the existence of the winning combination in the SIM file! By default, **LotWon** eliminates from the output ALL 6-number groups that exist in the _DATA-6_ file. (Or, ALL 5-number groups that exist in the _DATA-5_ file; the lotto-5 game offer an even more drastic repeat.) An output of 10000 combinations offers a jackpot chance equal to millions of purely random lottery combinations. One good strategy is to keep handy a 10000-combination optimized simulated lotto file that has not yet hit the jackpot in your lotto game...

Now, to bring closure to this little chapter. Take your time when using my lottery software. Be focused. You can take a look at every filter that **Command Prompt LotWon lottery software** employs. But after you've seen them all, settle for just a few filters and create lottery strategies pivoted on them filters. Be diligent - The lottery strategies do hit. Procrastination is the worst enemy of lottery strategizing. Read one of my personal happenings:

-   _"The Start Is Hardest Part"_ in [_**Lottery Strategies**_](https://forums.saliu.com/lottery-strategies-start.html).

Can you believe it, axiomatic colleagues of mine? Many people complained when my lottery software was **totally free**! Now, lottery players [_**pay a nominal fee to download all my software**_](https://saliu.com/membership.html). The complaints subsided dramatically! The morale: <u>Don't infatuate yourself when others are very generous with you!</u>

![Run the best-ever lotto software for jackpot and lottery software for pick daily games.](https://saliu.com/ScreenImgs/lotto-b60.gif)

![Ion Saliu's Theory of Probability Book founded on undeniable mathematics, also applied to lottery software and systems.](https://saliu.com/probability-book-Saliu.jpg) [Read Ion Saliu's first book in print: **_Probability Theory, Live!_**](https://saliu.com/probability-book.html)  
~ Founded on valuable mathematical discoveries with a wide range of scientific applications, including probability theory applied to lottery, software, systems.

![Software: Utilities for lotto, lottery, gambling, loterie, loteria.](https://saliu.com/HLINE.gif)

[

## <u>Resources in Lottery Software, Strategies, Lotto Systems</u>

](https://saliu.com/content/lottery.html)

-   The Main [_**<u>Lotto, Lottery, Software, Strategy</u>**_](https://saliu.com/LottoWin.htm) Page.  
    Presenting software to create free winning lotto, lottery strategies, systems based on mathematics. Get your lotto systems or wheels, the best lottery, lotto software, combinations, winning numbers.
-   [_**Lotto, Lottery Software, <u>Excel Spreadsheets</u>: Programming, Strategies**_](https://saliu.com/Newsgroups.htm).  
    Read a genuine analysis of Excel spreadsheets applied to lottery and lotto developing of software, systems, and strategies. Combining Excel analysis with powerful lottery and lotto software programmed by this author, _Parpaluck_.
-   A User's Guide to [_**<u>MDIEditor Lotto WE</u>: Lottery Software Manual, Book, ebook, Help**_](https://saliu.com/MDI-lotto-guide.html).  
    ~ Also applicable to my entire lottery software; plus Powerball, Mega Millions, Euromillions.
-   [_**Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps, Programs**_](https://saliu.com/forum/lotto-book.html).
-   [_**<u>Lottery Mathematics, Lotto Mathematics</u>**_](https://saliu.com/gambling-lottery-lotto/lottery-math.htm)_**, Probabilities, Appearance, Repeat, Number Affiliation, Wheels, Systems, Strategies**_.
-   [_**<u>Lotto Decades</u>, Last Digits, Systems, Strategies, Software**_](https://saliu.com/decades.html).
-   Practical [_**Lottery and Lotto Filtering in Software**_](https://saliu.com/filters.html).
-   [**Skips Lottery, Lotto, Gambling, Systems, Strategy**](https://saliu.com/skip-strategy.html)
-   [_**Lotto, Lottery <u>Strategy in Reverse</u>: Not-to-Win Leads to Not-to-Lose or WIN**_](https://saliu.com/reverse-strategy.html).
-   [_**Lotto, Lottery Strategy on Sums, Root Sums, Skips, Odd Even, Low High, Deltas**_](https://saliu.com/strategy.html).
-   [_**Lottery Utility Software**_](https://saliu.com/lottery-utility.html)_**: Pick-3, 4 Lottery, Lotto-5, 6, Powerball, Mega Millions, Euromillions**_.
-   [_**Lottery Strategy, Systems, Software Based on <u>Lotto Number Frequency</u>**_](https://saliu.com/frequency-lottery.html).
-   [**Theory, Analysis of _<u>Deltas</u>_ in Lotto, Lottery Software, Strategy, Systems**](https://saliu.com/delta-lotto-software.html).
-   [_**<u>Markov Chains</u>, Followers, Pairs, Lottery, Lotto, Software**_](https://saliu.com/markov-chains-lottery.html).
-   [_**LIE Elimination: Lottery, Lotto Strategy in Reverse for Pairs, Number Frequency**_](https://saliu.com/lie-lottery-strategies-pairs.html).
-   [_**<u>Reversed Lottery Strategy</u> for Lotto Decades, Last Digits, Odd Even, Low High, Skips**_](https://saliu.com/lie-lotto-strategies-decades.html).
-   [_**<u>Lotto Software for Groups of Numbers</u>: Odd, Even, Low, High, Sums, Frequencies, User's Groups**_](https://saliu.com/lotto-groups.html).
-   [_**<u>The Best Strategy for Lottery, Gambling</u>, Sports Betting, Horse Racing, Blackjack, Roulette**_](https://saliu.com/strategy-gambling-lottery.html).
-   [_**Strategy Lotto Software on Positional Frequency**_](https://forums.saliu.com/lotto-software-position-frequency.html).
-   Download [**<u>Lottery Software, Lotto Programs</u>**](https://saliu.com/infodown.html).

![Software: Utilities for lottery, gambling, loterie, loteria, Powerball, Mega Millions.](https://saliu.com/HLINE.gif)

![Wide ranging lottery software to improve chances in most lotto games, including Powerball, Mega Millions, Euromillions.](https://saliu.com/HLINE.gif)

**| [Home](https://saliu.com/index.htm) | [Search](https://saliu.com/Search.htm) | [New Writings](https://saliu.com/bbs/index.html) | [Odds, Generator](https://saliu.com/calculator_generator.html) | [Contents](https://saliu.com/content/index.html) | [Forums](https://forums.saliu.com/) | [Sitemap](https://saliu.com/sitemap/index.html) |**

![SALIU.COM: Site of lottery, lotto, software, systems.](https://saliu.com/images/HLINE.gif)
