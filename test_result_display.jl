#!/usr/bin/env julia

"""
Comprehensive test suite for result display and export functionality
Tests combination display, progress indicators, export formats, and interactive features
"""

using Dates

# Include required modules
include("src/result_display.jl")

"""
Test the result display and export system
"""
function test_result_display_system()
    println("🧪 Testing Result Display and Export System")
    println("=" ^ 70)
    
    # Test 1: Basic combination display
    println("\n📊 Test 1: Basic Combination Display")
    test_combination_display()
    
    # Test 2: Progress indicators
    println("\n⏱️  Test 2: Progress Indicators")
    test_progress_indicators()
    
    # Test 3: Export functionality
    println("\n💾 Test 3: Export Functionality")
    test_export_functionality()
    
    # Test 4: Analysis result display
    println("\n📈 Test 4: Analysis Result Display")
    test_analysis_result_display()
    
    # Test 5: Batch export operations
    println("\n📦 Test 5: Batch Export Operations")
    test_batch_export()
    
    # Test 6: Interactive features
    println("\n🎮 Test 6: Interactive Features")
    test_interactive_features()
    
    # Test 7: Result display manager
    println("\n📋 Test 7: Result Display Manager")
    test_result_display_manager()
    
    println("\n✅ Result Display and Export Tests Complete!")
    println("=" ^ 70)
end

"""
Test combination display functionality
"""
function test_combination_display()
    try
        # Create test combinations
        test_combinations = [
            [1, 5, 12, 23, 39],
            [3, 7, 15, 28, 35],
            [2, 11, 19, 31, 37],
            [4, 9, 16, 25, 33],
            [6, 13, 20, 29, 38]
        ]
        
        println("  Testing standard combination display...")
        
        # Capture output to avoid cluttering test results
        original_stdout = stdout
        redirect_stdout(devnull)
        
        display_combinations(test_combinations, "Test Combinations")
        
        redirect_stdout(original_stdout)
        
        println("  ✅ Standard combination display working")
        
        # Test grid display
        println("  Testing grid combination display...")
        
        redirect_stdout(devnull)
        display_combinations_grid(test_combinations, "Test Grid", 3)
        redirect_stdout(original_stdout)
        
        println("  ✅ Grid combination display working")
        
        # Test empty combinations
        println("  Testing empty combination handling...")
        
        redirect_stdout(devnull)
        display_combinations(Vector{Vector{Int}}(), "Empty Test")
        redirect_stdout(original_stdout)
        
        println("  ✅ Empty combination handling working")
        
    catch e
        println("  ❌ Error in combination display: $e")
        rethrow(e)
    end
end

"""
Test progress indicator functionality
"""
function test_progress_indicators()
    try
        println("  Testing progress indicator creation...")
        
        progress = ProgressIndicator(100, "Test Operation")
        @assert progress.total == 100
        @assert progress.current == 0
        @assert progress.description == "Test Operation"
        
        println("  ✅ Progress indicator created successfully")
        
        println("  Testing progress updates...")
        
        # Capture output to avoid cluttering test results
        original_stdout = stdout
        redirect_stdout(devnull)
        
        # Test progress updates
        for i in 1:10:100
            update_progress!(progress, i)
            sleep(0.01)  # Small delay to simulate work
        end
        
        # Complete the progress
        update_progress!(progress, 100)
        
        redirect_stdout(original_stdout)
        
        @assert progress.current == 100
        println("  ✅ Progress updates working correctly")
        
        # Test time formatting
        println("  Testing time formatting...")
        
        @assert format_time(30.5) == "30.5s"
        @assert format_time(90.0) == "1m 30s"
        @assert format_time(3665.0) == "1h 1m"
        
        println("  ✅ Time formatting working correctly")
        
    catch e
        println("  ❌ Error in progress indicators: $e")
        rethrow(e)
    end
end

"""
Test export functionality
"""
function test_export_functionality()
    try
        # Create test combinations
        test_combinations = [
            [1, 5, 12, 23, 39],
            [3, 7, 15, 28, 35],
            [2, 11, 19, 31, 37]
        ]
        
        test_metadata = Dict{String, Any}(
            "key_number" => 13,
            "generation_method" => "Wonder Grid",
            "analysis_date" => string(Date("2023-01-01"))
        )
        
        # Test CSV export
        println("  Testing CSV export...")
        csv_filename = "test_combinations.csv"
        export_combinations_csv(test_combinations, csv_filename, test_metadata)
        
        @assert isfile(csv_filename)
        
        # Verify CSV content
        csv_content = read(csv_filename, String)
        @assert contains(csv_content, "Combination_ID")
        @assert contains(csv_content, "1,5,12,23,39")
        @assert contains(csv_content, "key_number: 13")
        
        println("  ✅ CSV export working correctly")
        
        # Test TXT export
        println("  Testing TXT export...")
        txt_filename = "test_combinations.txt"
        export_combinations_txt(test_combinations, txt_filename, test_metadata)
        
        @assert isfile(txt_filename)
        
        # Verify TXT content
        txt_content = read(txt_filename, String)
        @assert contains(txt_content, "WONDER GRID LOTTERY SYSTEM")
        @assert contains(txt_content, "[ 1,  5, 12, 23, 39]")
        
        println("  ✅ TXT export working correctly")
        
        # Test JSON export
        println("  Testing JSON export...")
        json_filename = "test_combinations.json"
        export_combinations_json(test_combinations, json_filename, test_metadata)
        
        @assert isfile(json_filename)
        
        # Verify JSON content
        json_content = read(json_filename, String)
        @assert contains(json_content, "\"metadata\"")
        @assert contains(json_content, "\"combinations\"")
        @assert contains(json_content, "\"numbers\": [1, 5, 12, 23, 39]")
        
        println("  ✅ JSON export working correctly")
        
        # Clean up test files
        for filename in [csv_filename, txt_filename, json_filename]
            if isfile(filename)
                rm(filename)
            end
        end
        
        println("  🧹 Test files cleaned up")
        
    catch e
        println("  ❌ Error in export functionality: $e")
        rethrow(e)
    end
end

"""
Test analysis result display
"""
function test_analysis_result_display()
    try
        # Create test analysis results
        test_results = Dict{String, Any}(
            "key_number" => 13,
            "total_combinations" => 150,
            "generation_time" => 2.5,
            "analysis_date" => string(Dates.now()),
            "statistics" => Dict{String, Any}(
                "average_sum" => 97.5,
                "sum_range" => "65-130",
                "odd_even_ratio" => 0.6,
                "number_distribution" => "balanced"
            ),
            "performance" => Dict{String, Any}(
                "generation_speed" => 60.0,
                "memory_usage" => 45.2,
                "efficiency_score" => 8.7
            ),
            "recommendations" => [
                "Strategy shows good balance",
                "Consider testing with larger dataset",
                "Monitor performance over time"
            ]
        )
        
        println("  Testing analysis result display...")
        
        # Capture output to avoid cluttering test results
        original_stdout = stdout
        redirect_stdout(devnull)
        
        display_analysis_results(test_results, "Test Analysis Results")
        
        redirect_stdout(original_stdout)
        
        println("  ✅ Analysis result display working")
        
        # Test analysis report export
        println("  Testing analysis report export...")
        
        report_filename = "test_analysis_report.txt"
        export_analysis_report(test_results, report_filename)
        
        @assert isfile(report_filename)
        
        # Verify report content
        report_content = read(report_filename, String)
        @assert contains(report_content, "WONDER GRID LOTTERY SYSTEM")
        @assert contains(report_content, "key_number: 13")
        @assert contains(report_content, "STATISTICS:")
        
        println("  ✅ Analysis report export working")
        
        # Clean up
        rm(report_filename)
        println("  🧹 Test file cleaned up")
        
    catch e
        println("  ❌ Error in analysis result display: $e")
        rethrow(e)
    end
end

"""
Test batch export operations
"""
function test_batch_export()
    try
        # Create test combinations
        test_combinations = [
            [1, 5, 12, 23, 39],
            [3, 7, 15, 28, 35],
            [2, 11, 19, 31, 37],
            [4, 9, 16, 25, 33]
        ]
        
        test_metadata = Dict{String, Any}(
            "key_number" => 7,
            "method" => "batch_test"
        )
        
        println("  Testing batch export...")
        
        # Capture output to avoid cluttering test results
        original_stdout = stdout
        redirect_stdout(devnull)
        
        exported_files = batch_export_combinations(
            test_combinations, 
            "batch_test", 
            ["csv", "txt", "json"], 
            test_metadata
        )
        
        redirect_stdout(original_stdout)
        
        # Verify all files were created
        expected_files = ["batch_test.csv", "batch_test.txt", "batch_test.json"]
        @assert length(exported_files) == 3
        
        for filename in expected_files
            @assert isfile(filename)
            @assert filename in exported_files
        end
        
        println("  ✅ Batch export working correctly")
        
        # Test export summary display
        println("  Testing export summary display...")
        
        redirect_stdout(devnull)
        display_export_summary(exported_files, length(test_combinations))
        redirect_stdout(original_stdout)
        
        println("  ✅ Export summary display working")
        
        # Clean up test files
        for filename in exported_files
            if isfile(filename)
                rm(filename)
            end
        end
        
        println("  🧹 Test files cleaned up")
        
    catch e
        println("  ❌ Error in batch export: $e")
        rethrow(e)
    end
end

"""
Test interactive features
"""
function test_interactive_features()
    try
        # Test interactive combination viewer (simulated)
        println("  Testing interactive combination viewer setup...")
        
        test_combinations = [
            [1, 5, 12, 23, 39],
            [3, 7, 15, 28, 35],
            [2, 11, 19, 31, 37]
        ]
        
        # We can't test the actual interactive functionality without user input,
        # but we can test that the function exists and handles empty combinations
        
        # Capture output to avoid cluttering test results
        original_stdout = stdout
        redirect_stdout(devnull)
        
        # Test with empty combinations
        interactive_combination_viewer(Vector{Vector{Int}}())
        
        redirect_stdout(original_stdout)
        
        println("  ✅ Interactive viewer handles empty combinations")
        
        # Test that the function exists for non-empty combinations
        # (We can't test the full interactive functionality in automated tests)
        @assert isa(interactive_combination_viewer, Function)
        
        println("  ✅ Interactive combination viewer function available")
        
    catch e
        println("  ❌ Error in interactive features: $e")
        rethrow(e)
    end
end

"""
Test result display manager
"""
function test_result_display_manager()
    try
        println("  Testing result display manager creation...")
        
        # Test default manager
        manager = ResultDisplayManager()
        @assert manager.show_progress == true
        @assert manager.export_format == "csv"
        @assert manager.output_directory == "results"
        
        println("  ✅ Default result display manager created")
        
        # Test custom manager
        custom_manager = ResultDisplayManager(false, "json", "custom_output")
        @assert custom_manager.show_progress == false
        @assert custom_manager.export_format == "json"
        @assert custom_manager.output_directory == "custom_output"
        
        # Verify directory was created
        @assert isdir("custom_output")
        
        println("  ✅ Custom result display manager created")
        
        # Clean up test directory
        if isdir("custom_output")
            rm("custom_output", recursive=true)
        end
        
        println("  🧹 Test directory cleaned up")
        
    catch e
        println("  ❌ Error in result display manager: $e")
        rethrow(e)
    end
end

"""
Test file format validation
"""
function test_file_format_validation()
    println("\n📄 Test: File Format Validation")
    
    try
        # Create test combinations
        test_combinations = [
            [1, 5, 12, 23, 39],
            [3, 7, 15, 28, 35]
        ]
        
        # Test CSV format validation
        csv_filename = "format_test.csv"
        export_combinations_csv(test_combinations, csv_filename)
        
        csv_content = read(csv_filename, String)
        lines = split(csv_content, '\n')
        
        # Check header
        @assert contains(lines[1], "# Wonder Grid Lottery System")
        
        # Check CSV header line
        header_line = ""
        for line in lines
            if startswith(line, "Combination_ID")
                header_line = line
                break
            end
        end
        @assert !isempty(header_line)
        @assert contains(header_line, "Number_1,Number_2,Number_3,Number_4,Number_5")
        
        println("  ✅ CSV format validation passed")
        
        # Test TXT format validation
        txt_filename = "format_test.txt"
        export_combinations_txt(test_combinations, txt_filename)
        
        txt_content = read(txt_filename, String)
        @assert contains(txt_content, "WONDER GRID LOTTERY SYSTEM")
        @assert contains(txt_content, "Total Combinations: 2")
        @assert contains(txt_content, "[ 1,  5, 12, 23, 39]")
        
        println("  ✅ TXT format validation passed")
        
        # Test JSON format validation
        json_filename = "format_test.json"
        export_combinations_json(test_combinations, json_filename)
        
        json_content = read(json_filename, String)
        @assert contains(json_content, "\"metadata\":")
        @assert contains(json_content, "\"combinations\":")
        @assert contains(json_content, "\"total_combinations\": 2")
        
        println("  ✅ JSON format validation passed")
        
        # Clean up
        for filename in [csv_filename, txt_filename, json_filename]
            if isfile(filename)
                rm(filename)
            end
        end
        
        println("  🧹 Test files cleaned up")
        
    catch e
        println("  ❌ Error in file format validation: $e")
        rethrow(e)
    end
end

# Run the tests
if abspath(PROGRAM_FILE) == @__FILE__
    test_result_display_system()
    test_file_format_validation()
end