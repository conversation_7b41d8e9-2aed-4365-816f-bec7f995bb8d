# FOUR Filter Implementation
# FOUR 過濾器實現 - 分析四號組合的統計特性

using Dates
using Statistics

# 引入必要的模組
include("../types.jl")
include("../filter_engine.jl")

"""
生成號碼組合的所有四號組合
從給定的號碼列表中生成所有可能的四號組合
"""
function generate_quadruplets(numbers::Vector{Int})::Vector{Tuple{Int, Int, Int, Int}}
    if length(numbers) < 4
        return Tuple{Int, Int, Int, Int}[]
    end
    
    quadruplets = Tuple{Int, Int, Int, Int}[]
    n = length(numbers)
    
    for i in 1:n-3
        for j in i+1:n-2
            for k in j+1:n-1
                for l in k+1:n
                    # 確保號碼按升序排列
                    quadruplet = (numbers[i], numbers[j], numbers[k], numbers[l])
                    push!(quadruplets, quadruplet)
                end
            end
        end
    end
    
    return quadruplets
end

"""
計算特定四號組合在歷史數據中的出現頻率
"""
function calculate_quadruplet_frequency(engine::FilterEngine, quadruplet::Tuple{Int, Int, Int, Int})::Int
    if isempty(engine.historical_data)
        return 0
    end
    
    count = 0
    for draw in engine.historical_data
        # 檢查四個號碼是否都在該次開獎中
        if quadruplet[1] in draw.numbers && quadruplet[2] in draw.numbers && 
           quadruplet[3] in draw.numbers && quadruplet[4] in draw.numbers
            count += 1
        end
    end
    
    return count
end

"""
計算四號組合的 Skip 值
四號組合 Skip 是指該組合上次出現到現在的間隔
"""
function calculate_quadruplet_skip(engine::FilterEngine, quadruplet::Tuple{Int, Int, Int, Int})::Int
    if isempty(engine.historical_data)
        return 0
    end
    
    # 尋找四號組合最後出現的位置（數據是按最新到最舊排序的）
    last_occurrence = 0
    
    for (i, draw) in enumerate(engine.historical_data)
        if quadruplet[1] in draw.numbers && quadruplet[2] in draw.numbers && 
           quadruplet[3] in draw.numbers && quadruplet[4] in draw.numbers
            last_occurrence = i
            break  # 找到最近的出現位置就停止（第一個找到的就是最新的）
        end
    end
    
    if last_occurrence == 0
        # 組合從未出現過，返回總數據長度
        return length(engine.historical_data)
    else
        # 返回從最後出現到現在的間隔
        return last_occurrence - 1
    end
end

"""
計算四號組合統計分析
分析四號組合的出現模式和統計特性
"""
function calculate_four_combination_statistics(engine::FilterEngine, numbers::Vector{Int})::Dict{String, Any}
    if length(numbers) < 4
        return Dict("error" => "至少需要 4 個號碼進行四號組合分析")
    end
    
    quadruplets = generate_quadruplets(numbers)
    
    # 計算每個四號組合的統計
    frequencies = Int[]
    skips = Int[]
    
    for quad in quadruplets
        freq = calculate_quadruplet_frequency(engine, quad)
        skip = calculate_quadruplet_skip(engine, quad)
        push!(frequencies, freq)
        push!(skips, skip)
    end
    
    # 計算統計指標
    total_quadruplets = length(quadruplets)
    appeared_quadruplets = count(f -> f > 0, frequencies)
    never_appeared = total_quadruplets - appeared_quadruplets
    
    return Dict(
        "total_quadruplets" => total_quadruplets,
        "appeared_quadruplets" => appeared_quadruplets,
        "never_appeared" => never_appeared,
        "appearance_rate" => appeared_quadruplets / total_quadruplets,
        "frequencies" => frequencies,
        "skips" => skips,
        "average_frequency" => isempty(frequencies) ? 0.0 : mean(frequencies),
        "max_frequency" => isempty(frequencies) ? 0 : maximum(frequencies),
        "average_skip" => isempty(skips) ? 0.0 : mean(skips),
        "min_skip" => isempty(skips) ? 0 : minimum(skips)
    )
end

"""
計算機率計算邏輯
基於理論機率和歷史數據計算四號組合的出現機率
"""
function calculate_four_combination_probability(engine::FilterEngine, numbers::Vector{Int})::Dict{String, Float64}
    if length(numbers) < 4
        return Dict("error" => "至少需要 4 個號碼")
    end
    
    # 對於 Lotto 5/39，任意四個號碼同時出現的理論機率
    # P(四個特定號碼都被選中) = C(35,1) / C(39,5)
    total_combinations = binomial(39, 5)
    favorable_combinations = binomial(35, 1)  # 剩餘 1 個位置從其他 35 個號碼中選
    theoretical_probability = favorable_combinations / total_combinations
    
    # 計算期望頻率
    data_size = length(engine.historical_data)
    expected_frequency = theoretical_probability * data_size
    
    # 計算實際統計
    stats = calculate_four_combination_statistics(engine, numbers)
    actual_frequency = get(stats, "average_frequency", 0.0)
    
    # 計算機率指標
    probability_ratio = expected_frequency > 0 ? actual_frequency / expected_frequency : 0.0
    
    return Dict(
        "theoretical_probability" => theoretical_probability,
        "expected_frequency" => expected_frequency,
        "actual_frequency" => actual_frequency,
        "probability_ratio" => probability_ratio,
        "is_above_expected" => actual_frequency > expected_frequency,
        "deviation_score" => abs(actual_frequency - expected_frequency) / max(expected_frequency, 0.1)
    )
end

"""
計算四號組合信心水準
基於統計分析和機率計算
"""
function calculate_four_combination_confidence(engine::FilterEngine, numbers::Vector{Int})::Float64
    if length(numbers) < 4 || isempty(engine.historical_data)
        return 0.0
    end
    
    # 獲取統計和機率分析
    stats = calculate_four_combination_statistics(engine, numbers)
    prob_analysis = calculate_four_combination_probability(engine, numbers)
    
    # 基於出現率的信心
    appearance_rate = get(stats, "appearance_rate", 0.0)
    appearance_confidence = min(1.0, appearance_rate * 2.0)  # 出現率越高，信心越高
    
    # 基於機率偏差的信心
    deviation_score = get(prob_analysis, "deviation_score", 1.0)
    deviation_confidence = exp(-deviation_score / 2.0)  # 偏差越小，信心越高
    
    # 基於樣本大小的信心
    sample_confidence = min(1.0, length(engine.historical_data) / 100.0)
    
    # 綜合信心水準
    final_confidence = (appearance_confidence + deviation_confidence) / 2.0 * sample_confidence
    
    return clamp(final_confidence, 0.0, 1.0)
end

"""
判斷四號組合是否有利
基於統計分析判斷當前組合是否處於有利狀態
"""
function is_four_combination_favorable(engine::FilterEngine, numbers::Vector{Int})::Bool
    if length(numbers) < 4
        return false
    end
    
    stats = calculate_four_combination_statistics(engine, numbers)
    prob_analysis = calculate_four_combination_probability(engine, numbers)
    
    # 有利條件：
    # 1. 出現率適中（不要太高也不要太低）
    appearance_rate = get(stats, "appearance_rate", 0.0)
    moderate_appearance = 0.1 <= appearance_rate <= 0.7
    
    # 2. 平均 skip 不要太高
    average_skip = get(stats, "average_skip", 0.0)
    reasonable_skip = average_skip <= length(engine.historical_data) * 0.3
    
    # 3. 機率比率接近或略低於期望
    probability_ratio = get(prob_analysis, "probability_ratio", 0.0)
    good_probability = 0.5 <= probability_ratio <= 1.2
    
    return moderate_appearance && reasonable_skip && good_probability
end

"""
計算 FOUR 過濾器結果
分析給定號碼組合的四號組合統計特性
"""
function calculate_four_filter(engine::FilterEngine, numbers::Vector{Int})::FilterResult
    start_time = time()
    
    if length(numbers) < 4
        throw(ArgumentError("FOUR 過濾器至少需要 4 個號碼，得到: $(length(numbers))"))
    end
    
    if isempty(engine.historical_data)
        throw(ArgumentError("歷史數據不能為空"))
    end
    
    # 驗證號碼範圍
    if !all(1 <= n <= 39 for n in numbers)
        throw(ArgumentError("所有號碼必須在 1-39 範圍內"))
    end
    
    # 檢查快取
    cache_key = "four_filter_$(sort(numbers))"
    if engine.cache_enabled && haskey(engine.filter_cache, cache_key)
        cached_result = engine.filter_cache[cache_key]
        @info "使用快取結果: FOUR 過濾器，號碼 $(numbers)"
        return cached_result
    end
    
    try
        # 計算四號組合統計
        stats = calculate_four_combination_statistics(engine, numbers)
        prob_analysis = calculate_four_combination_probability(engine, numbers)
        
        # 計算當前四號組合數量
        current_quadruplet_count = get(stats, "total_quadruplets", 0)
        
        # 計算期望值（基於理論機率）
        expected_value = get(prob_analysis, "expected_frequency", 0.0)
        
        # 判斷是否有利
        is_favorable = is_four_combination_favorable(engine, numbers)
        
        # 計算信心水準
        confidence = calculate_four_combination_confidence(engine, numbers)
        
        # 獲取歷史頻率作為歷史值
        historical_frequencies = get(stats, "frequencies", Int[])
        
        # 計算執行時間
        calculation_time = time() - start_time
        
        # 創建結果
        result = FilterResult(
            "FOUR_FILTER_$(length(numbers))_numbers",
            FOUR_FILTER,
            current_quadruplet_count,
            expected_value,
            is_favorable,
            confidence,
            historical_frequencies,
            calculation_time
        )
        
        # 儲存到快取
        if engine.cache_enabled
            engine.filter_cache[cache_key] = result
            manage_cache_size!(engine)
        end
        
        return result
        
    catch e
        @error "計算 FOUR 過濾器時發生錯誤" numbers=numbers error=e
        rethrow(e)
    end
end

"""
獲取最有潛力的四號組合
基於統計分析找出最有潛力的四號組合
"""
function get_most_promising_quadruplets(engine::FilterEngine, numbers::Vector{Int}, top_n::Int = 5)::Vector{Dict{String, Any}}
    if length(numbers) < 4
        return Dict{String, Any}[]
    end
    
    quadruplets = generate_quadruplets(numbers)
    analyses = Dict{String, Any}[]
    
    for quad in quadruplets
        frequency = calculate_quadruplet_frequency(engine, quad)
        skip = calculate_quadruplet_skip(engine, quad)
        
        # 計算潛力分數（低頻率 + 適中 skip = 高潛力）
        potential_score = if frequency == 0
            1.0 + (1.0 / max(skip, 1))  # 從未出現但 skip 不太高
        else
            (1.0 / frequency) * (1.0 / max(skip, 1))  # 低頻率且低 skip
        end
        
        push!(analyses, Dict(
            "quadruplet" => quad,
            "frequency" => frequency,
            "skip" => skip,
            "potential_score" => potential_score,
            "is_overdue" => skip > length(engine.historical_data) * 0.2
        ))
    end
    
    # 按潛力分數排序
    sort!(analyses, by = x -> x["potential_score"], rev = true)
    
    return analyses[1:min(top_n, length(analyses))]
end

"""
計算 FOUR 過濾器的統計摘要
"""
function get_four_filter_summary(results::Vector{FilterResult})::Dict{String, Any}
    if isempty(results)
        return Dict("error" => "無結果數據")
    end
    
    # 篩選 FOUR 過濾器結果
    four_filter_results = filter(r -> r.filter_type == FOUR_FILTER, results)
    
    if isempty(four_filter_results)
        return Dict("error" => "無 FOUR 過濾器結果")
    end
    
    favorable_count = count(r -> r.is_favorable, four_filter_results)
    confidence_levels = [r.confidence_level for r in four_filter_results]
    quadruplet_counts = [r.current_value for r in four_filter_results]
    expected_values = [r.expected_value for r in four_filter_results]
    
    return Dict(
        "total_combinations" => length(four_filter_results),
        "favorable_combinations" => favorable_count,
        "average_confidence" => mean(confidence_levels),
        "average_quadruplet_count" => mean(quadruplet_counts),
        "average_expected_frequency" => mean(expected_values),
        "quadruplet_count_distribution" => Dict(
            "min" => minimum(quadruplet_counts),
            "max" => maximum(quadruplet_counts),
            "median" => median(quadruplet_counts)
        )
    )
end

# 導出函數
export calculate_four_filter, generate_quadruplets, calculate_quadruplet_frequency
export calculate_quadruplet_skip, calculate_four_combination_statistics
export calculate_four_combination_probability, get_most_promising_quadruplets
export get_four_filter_summary
