---
created: 2025-07-24T06:53:50 (UTC +08:00)
tags: [lottery,loto,draw,range,analysis,wonder grid,wonder-grid,lotto,software,lottery,system,statistics,drawings,draws,analyze,analysis]
source: https://saliu.com/bbs/messages/663.html
author: 
---

# Optimal Lotto Drawings Range, Lottery Wonder Grid System

> ## Excerpt
> For lotto  the statistical range should represent three times the biggest lotto number in the game or cover the most recent 150 lottery drawings.

---
Posted by <PERSON> on April 07, 2001.

In Reply to: [_**Wonder Grid Lottery Strategy Plays Pairs of Lotto Numbers**_](https://saliu.com/bbs/messages/638.html) posted by <PERSON> on March 24, 2001.

El Loco asked this meaningful question:

"Maybe I should stick to analyzing the last 100 drawings in the future as well (instead of 200)."

I believe there is an optimal range, correlated to the Fundamental Formula of Gambling (FFG). Simplifying, I recommend the following values:  
1) For lotto: the range should represent three times the biggest lotto number in the game. For example, in a lotto-49 game, the analysis should cover the most recent 150 drawings (49 x 3 = 147; I always use round figures). An intuitive explanation will consider a number of 3 unique pairs that make up a lotto combination. Lotto-5 and lotto-7 are close to the same situation.  
2) For digit lotteries: use the same intuitive method. In the pick-3 game, there are C(3,2) = 3 pairs. Each pair has two possibilities, since the digit position counts. The digits 1 and 2 have two pairings: 1-2 and 2-1. as a result, there are almost 6 pairs, if we break down a pick-3 combination. The range of analysis should be 60 (6 pairs x 10 digits). I also analyze 100 drawings in the pick-3 lottery.

I read Scrooge’s strategy. It is interesting; it has a mathematical foundation. Karl M, of course, has a solid approach, using some complex statistical science. My approach does not follow that path. But he is also in the stock market analysis, hence his preference. I wonder what path would Wall Street professionals take. Soon after I posted my strategy, at least one major brokerage firm literally had a grip on my website. I checked the access-log file; the firm occupied a long segment in the server file! Maybe they rushed to the lotteries and took advantage of the incredible return: over a million for 19,000! (Of course, it also depends on how many winners my strategy creates in the same lottery! Fear not: at this time, my website only gets 7,000+ hits a week!)

Using the pairings represents, indeed, a very powerful lottery tool. I wrote in previous posts how I used the method without a computer. I used it successfully in Romania and also in America. The computer is of super help, nonetheless. The lotto pairing strategy can be honed to a great extent. I won’t be too specific right now. You can think of selecting a group of numbers and their best pairings. One can also use two favorite lotto numbers and their winning pairings. One can also eliminate many more of the worst pairings. I tried, for example to eliminate the worst 75% or 50% pairs. The programs haven’t come up with any combination! But there has to be one combination (or a few) from time to time! Of course, there is the multitude of the _LotWon filters_ we can use, in combination with the pairing or other lottery strategies!

There are still a group of users who just face difficulties following my strategies and/or software. The easiest way for them to get a better winning chance is to use my freeware generating combinations around the median. They should seriously consider: BELLBET, BELLOTTO, BETUS. They are much easier to use, since they don’t require filters. And the programs certainly increase the winning probability, while decreasing the losing probability!

I ran BETUS during the NCAA basketball tournament. I applied it to the 16 games of the second round. Astonishingly, the program generated two times the right results. All 16 winners! Of course, I ran the program many times. Nobody should expect the program win every time it is used. But, beyond a doubt, the program increases the chances to win. The odds in a 16-game case are 65536 to 1. In a 50% house edge scenario, you can win 32768 for a 1-unit bet! To be sure, using BELLBET, or BELLOTTO, or BETUS is like playing hundreds, or thousands, even millions of tries FOR FREE!

I am asked many, many times about selling my lottery software or my gambling strategies. I am a philosopher. That represents a major obstacle in doing business. Instead of thinking in terms of profit-only, I visualize the income discrepancies in this world of ours. What price should I charge? Ten dollars buys a meal in a cheap restaurant in America. Meanwhile, ten dollars can be a matter of life and death in a poor country. It is very hard for me to even think of marketing my lottery software and strategies. At the same time, I am facing the impossibility of offering everything for free. I am also a nice person, but I am also a sane person. Let alone that some are asking me to go even beyond freeware. I was asked to send my stuff AND a few hundred dollars for the software user to play the lottery!

I am asking myself sometimes: "Self, why did Socrates have to have nothing materially?"

![Ion Saliu's Theory of Probability Book founded on valuable mathematics, including statistical analysis of lottery games.](https://saliu.com/bbs/messages/probability-book-Saliu.jpg) [Read Ion Saliu's first book in print: **_Probability Theory, Live!_**](https://saliu.com/probability-book.html)  
~ Founded on valuable mathematics, including statistical analyses to win any lotto game.

![The range (span) of analysis in lotto, lottery software.](https://saliu.com/bbs/messages/HLINE.gif)

## [Resources in Lottery Software, Systems, Lotto Wheeling](https://saliu.com/content/lottery.html)

-   Download [**lottery software, lotto software**](https://saliu.com/infodown.html).
    
    <u>Follow Ups</u>  
    
-   [_**Wonder Grid Lottery Strategy Plays Pairs of Lotto Numbers**_](https://saliu.com/bbs/messages/638.html) **Ion Saliu** _3/24/2001._
-   [Optimal lottery drawings range to analyze - Illinois Lotto](https://saliu.com/bbs/messages/664.html) **Bill Smith** _4/09/2001._
-   [BELLOTTO, BELLBET, BETUS: Software to generate random combinations for lotto, lottery, gambling](https://saliu.com/bbs/messages/665.html) **Ion Saliu** _4/09/2001._
-   [Wonder-grid lottery strategy hit the lotto jackpot in Belgium Lottery](https://saliu.com/bbs/messages/647.html) **El Loco** _3/29/2001._
-   [_**Lottery Pairs System, Lotto Pair Strategy**_](https://saliu.com/bbs/messages/645.html) **lottoscorp** _3/28/2001._
-   [Likely winning lotto numbers to wheel by best lotto wheels software](https://saliu.com/bbs/messages/644.html) **KM** _3/27/2001._
-   [Lottery system: Lotto numbers, skips of lotto numbers, sum of gaps](https://saliu.com/bbs/messages/646.html) **BigJer** _3/28/2001._
-   [Lotto deltas in powerful, winning lottery software](https://saliu.com/bbs/messages/648.html) **KM** _3/29/2001._
-   [Lottery Analysis in the Spirit of Ramanujan – The Great Indian Mathematician](https://saliu.com/bbs/messages/641.html) **Ramanujan** _3/25/2001._

![Optimal lotto loto draw range analysis in lottery software.](https://saliu.com/bbs/messages/HLINE.gif)

Comments:  

![For lotto:  the statistical range should represent three times the biggest lotto number in the game. In lotto 6/49, the lottery system analysis should cover the most recent 150 lottery drawings.](https://saliu.com/bbs/messages/HLINE.gif)

**[Home](https://saliu.com/index.htm) | [Search](https://saliu.com/Search.htm) | [New Writings](https://saliu.com/bbs/index.html) | [Software](https://saliu.com/infodown.html) | [Odds, Generator](https://saliu.com/calculator_generator.html) | [Contents](https://saliu.com/content/index.html) | [Forums](https://forums.saliu.com/) | [Sitemap](https://saliu.com/sitemap/index.html)**

![Ion Saliu: Software, Programs, Apps, Systems, Strategies.](https://saliu.com/bbs/messages/HLINE.gif)
