---
created: 2025-07-24T21:26:40 (UTC +08:00)
tags: [lottery,lotto,sum,sums,sum-totals,software,lottery drawings,pick digit lotteries,lotto-5 6,Powerball,Mega Millions,Euromillions,chart,]
source: https://saliu.com/forum/lottery-sums.html
author: 
---

# Lottery Sums, Lotto Root-Sum, Charts, Software

> ## Excerpt
> Special lottery software charts show the sums or sum-totals of many lotto, lottery games: pick digit lotteries, lotto, Powerball, Mega Millions, Euromillions.

---
First captured by the _WayBack Machine_ (_web.archive.org_) on October 23, 2007.

A lot of lottery players have a keen interest in a topic such as the title of this document: **sums of lottery games**. Furthermore, if you land in lottery communities (forums, newsgroups, blogs), you will come across quite a few requests to show **charts of sum-totals or sums** of various lotto and lottery games.

The surprise for you and me has to do with this scarcity: _**There is only one piece of software that accurately creates such statistical charts! Just one lottery program, fittingly named <big>Sums</big>.**_ Yes, it's me who wrote it! But I am not writing this to brag about that piece of incredulously great lotto software – or am I? NOT!

**Sums** has always plotted charts for the sums in just about any kind of lottery and lotto games (since 1990s). And, for sure, no other program has ever done it. They lie when they claim they created the lottery some charts in Excel spreadsheets. A spreadsheet cannot handle millions and millions of lotto combinations, especially games like Powerball and Mega Millions. I've seen code by others to only generate lotto combinations for one sum-total at a time, not for an entire game in one run.

This piece of special software does a whole lot more mathematically than just plotting sum tables. For starters, the application generates all the combinations for a given _sum-total_. You can't do that in an Excel spreadsheet. Moreover, the **Sums** lottery software performs comprehensive statistical analyses of lottery data (results) files. The statistics of a lotto data file show the _sums_, and then sum... I mean some... _mean averages, medians, standard deviation, average deviations, deltas_...

This lottery program came to life by request from... a school student! A computer science teacher gave the class a programming assignment:

-   _"Determine exactly how many lotto combinations can add up to a certain **sum-total**."_

-   [_**Software to Calculate Lottery Sums, Lotto Sum-Totals, Pick Digit Lotteries, Powerball Mega Millions**_](https://saliu.com/bbs/messages/626.html) — (almost) every lottery game in the world.
-   A special Web page was then created for strategies based on **sums** and other **static lottery filters**:
-   [_**Lotto Strategy Based on: Sums (Sum-Totals); Odd or Even; Low or High Numbers**_](https://saliu.com/strategy.html).

![Lotto sums software has four functions for most type of lottery games, from pick 2 to Powerball, Mega Millions, Euromillions.](https://saliu.com/ScreenImgs/lottery-sums-combinations.gif)

_**Deltas**_, anyone? Well, that sounds like a fancy word. It is for the alphabet letter _**D**_ of the Classical Greek language (the one that educated humans in creating the Modern Civilization). **_D_** is the mnemonic for _**difference**_. [**Lottery deltas**](https://saliu.com/delta-lotto-software.html) are _**differences** between adjacent lottery numbers._ For example, _delta\_1_ represents the difference between the number one and the 2nd number in a lotto combination. The delta is expressed as an absolute number, not a signed number. Lotto number\_1 = 1; lotto number\_2 = 7; delta = 7 – 1 = 6 (it is never minus 6).

Let us show here for everybody to see and use — for their personal usage only — never for commercial use! (Other sites will never tell you what lottery software they are using to plot the charts of any possible lotto game... for such software has been mine-only in the 20th and 21st centuries!)

Following are links to **sum-total, root sums charts** for a wide variety of lottery and lotto games all over the world. Each game has its own Web page for the best possible viewing. The <u>sum-total/root sums charts</u> show the percentages as well. The percentages are directly related to the theoretical lottery probabilities.

-   [_**Pick 2 Lottery Sums, Root Sum Chart**_](https://saliu.com/forum/pick-2-lottery-sums-chart.html)
-   [_**Pick 3 Lottery Sum-Total, Root Sums Chart**_](https://saliu.com/forum/pick3-sums.html)
-   [_**Pick 4 Lottery Sum-Total, Root Sum Charts**_](https://saliu.com/forum/pick4-sums.html)
-   [_**Pick 5 (formerly _Quinto_ in Pennsylvania) Lottery Root, Sums Chart**_](https://saliu.com/forum/quinto-sums.html)
-   [_**Lotto 5/39 Sums, Root Sum Chart**_](https://saliu.com/forum/lotto539-sums.html)
-   [_**Lotto 5/43 Sums, Root Sum Chart**_](https://saliu.com/forum/lotto543-sums.html)
-   [_**Lotto 6/49 Sums, Root Sum Chart**_](https://saliu.com/forum/lotto649-sums.html)
-   [_**Lotto 6/51 Sums, Root Sum Chart**_](https://saliu.com/forum/lotto651-sums.html)
-   [_**Lotto 6/54 Sums, Root Sum Chart**_](https://saliu.com/forum/lotto654-sums.html)
-   [_**Lotto 6/59 Sums, Root Sum Chart**_](https://saliu.com/forum/lotto659-sums.html)
-   [_**Euromillions Sums, Root Sum Chart**_](https://saliu.com/forum/euromillions-sums.html); start of current game format: September 27, 2016
-   [_**Euromillions 5/50 & 2/11 Sum-Totals**_](https://saliu.com/forum/euromillions-sums50211.html)
-   [_**Mega Millions Sums, Root Sum Chart**_](https://saliu.com/forum/megamillions-sums.html); start of current game format: October 31, 2017
-   [_**Sums, Sum Chart: <u>5/75 &amp; 1/15</u> Sum-Totals**_](https://saliu.com/forum/megamillions-sums-75-15.html)
-   [_**Powerball Sums, Root Sum Chart**_](https://saliu.com/forum/powerball-sums.html); start of current game format: October 7, 2015
-   [_**Powerball 5/59 & 1/35 Sum-Totals**_](https://saliu.com/forum/powerball-sums-59-35.html)
-   [_**SuperLotto-Plus Sums, Root Sum Chart**_](https://saliu.com/forum/superlotto-sums.html); current game format: _5 of 47_ AND _1 of 27_
-   [_**Thunderball Sums, Root Sum Chart**_](https://saliu.com/forum/thunderball-sums.html); current game format: _5 of 39_ AND _1 of 14_

The screenshot below shows an analysis of a _**data (drawings) file**_ for the Powerball, Mega Millions lotto game.

![Analyze a Powerball or Megamillion results file to show sums, average, standard deviation, delta.](https://saliu.com/ScreenImgs/sum-lottery-draws.gif)

-   Function _S = Sum-up lottery-drawings files_. Here is the most sophisticated statistical analysis of lottery drawings — _sum-totals, root sums, average, standard deviation, deltas, medians_. You won't find it anywhere else; unless they use my software **Sums** legitimately. (I doubt it... I've never given anybody permission to make public the results of my software... because nobody ever asked me!)

![Read or print the statistics of sums for Powerball, Mega Millions, Euromillions lottery games.](https://saliu.com/ScreenImgs/sum-stats.gif)

Axiomatic one, the statistical parameters of the bottom line (_Medians_) are virtually identical to what mathematics calculates for the respective parameters. By the way - _StdDev_ means _standard deviation_, while _Del_ refers to [_**lotto deltas**_](https://saliu.com/bbs/messages/648.html) - or the _absolute difference_ between two neighboring numbers. The statistical parameters for the most common lotto game (6/49) are mathematically equivalent (the real data is for the Pennsylvania Lottery _Match 6_, 864 drawings analyzed):

![Read or print the statistics of sums for regular lotto games like 6 from 49.](https://saliu.com/ScreenImgs/lotto-sums-stats.gif)

-   How about that controversial lotto-6 combination that a million lottery players worldwide play every week... [_**1 2 3 4 5 6 Lotto Combination Probability, Odds**_](https://saliu.com/bbs/messages/961.html)?
    
    Well, indelibly axiomatic colleague of mine, look no further than the bottom line. The (in)famous combonation above has a sum of _21_; mathematically and practically, that sum-total should be close to _150_ (anyway, not that far away as sum 21 is). The average delta of _1 2 3 4 5 6_ is a mere _1_; mathematically and practically, that delta average should be close to _7.40_. The average is a very low _3.5_. The standard deviation is the most convincing tell-tale fact: _1.71_. It is _7.5_ times lower than the norm of _12.90_!
    
    As we can see now, a combination like _1 2 3 4 5 6_ is statistically very far away from the mathematical norms.
    
    ![Lottery sums software calculates sum-totals in any lotto game and generates the combinations.](https://saliu.com/forum/HLINE.gif)
    
    -   [_**Back to index: Forum Lotto, Lottery, Sums, Wheeling, Software**_](https://saliu.com/forum/index.html).
    
    ## [<u>Resources in Lottery Software, Lotto Wheeling, Lottery Sums</u>](https://saliu.com/content/lottery.html)
    
    -   Introduction to [**Lottery Mathematics**](https://saliu.com/gambling-lottery-lotto/lottery-math.htm): _**Probabilities, Appearance, Repeat, Affinity or Number Affiliation, Wheels, Systems, Strategies**_.
    -   The Starting Strategy Page: [_**Lotto Lottery Software Systems**_](https://saliu.com/LottoWin.htm).  
        Presenting software to create free winning lotto, lottery strategies, systems based on mathematics. Get your lotto systems or wheels, the best lottery, lotto software, combinations, winning numbers.
    -   [_**Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies**_](https://saliu.com/Newsgroups.htm).  
        Read a genuine analysis of Excel spreadsheets applied to lottery and lotto developing of software, systems, and strategies. Combining Excel analysis with powerful lottery and lotto software programmed by this author, _Parpaluck_.
    -   [_Users' Guide to_ _**MDIEditor And Lotto WE**_](https://saliu.com/MDI-lotto-guide.html).  
        ~ Also applicable to LotWon lottery, lotto software; plus Powerball, Mega Millions, Euromillions.
    -   [_**Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps, Programs**_](https://saliu.com/forum/lotto-book.html).
    -   [**Standard Deviation**](https://saliu.com/deviation.html): _**Basics, Mathematics, Statistics, Formula, Software, Algorithm, Equation**_.
    -   [_**Basics of Lottery, Lotto Strategy Based on: Sums (Sum-Totals); Odd/Even; Low/High Numbers**_](https://saliu.com/strategy.html).
    -   [_**Software to Calculate Lottery Sums, Odd-Even, Low-High Patterns**_](https://saliu.com/bbs/messages/626.html).
    -   [**<u>Lottery Skip Systems</u>**](https://saliu.com/skip-strategy.html): _**Lotto, Powerball, Mega Millions, Euromillions, Horse Racing, Roulette, Football**_.
    -   [**Lottery Utility Software**](https://saliu.com/lottery-utility.html): _**Pick-3, 4 Lottery, Lotto-5, 6, Powerball, Mega Millions, Euromillions**_.
    -   [_**Lotto Software for Groups of Numbers: Odd, Even, Low, High, Sums, Frequencies, User's Groups**_](https://saliu.com/lotto-groups.html).
    -   [_**Lottery: Mathematics, Social Purpose, History, Software, Systems**_](https://saliu.com/lottery.html).
        -   Download [**the best lottery software**](https://saliu.com/infodown.html).
    
    ![Download all root sums for pick 2 3 4 5 lotteries, lotto, Powerball, Mega Millions, Euromillions.](https://saliu.com/forum/HLINE.gif)
    
    **[Home](https://saliu.com/index.htm) | [Search](https://saliu.com/Search.htm) | [Help](https://saliu.com/Help.htm) | [New Writings](https://saliu.com/bbs/index.html) | [Software](https://saliu.com/infodown.html) | [Odds, Generator](https://saliu.com/calculator_generator.html) | [Contents](https://saliu.com/content/index.html) | [Forums](https://forums.saliu.com/) | [Sitemap](https://saliu.com/sitemap/index.html)**
    
    ![View root sums, sum-totals in lottery lotto: Powerball, Mega Millions, Euromillions, Pick-5 Quinto.](https://saliu.com/forum/HLINE.gif)
