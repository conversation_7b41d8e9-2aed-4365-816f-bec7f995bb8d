# Comprehensive FFG Calculation Tests
# FFG 計算綜合測試 - 全面測試 FFG 計算的準確性和性能

using Test
using Dates
using Statistics

# 引入必要的模組
include("../src/types.jl")
include("../src/ffg_calculator.jl")
include("../src/filter_engine.jl")
include("test_data_manager.jl")
include("test_configuration.jl")

"""
FFG 理論計算準確性測試
測試 FFG 理論中位數計算的數學準確性
"""
function test_ffg_theoretical_accuracy(test_data::Vector{LotteryDraw})::Dict{String, Any}
    @testset "FFG Theoretical Accuracy" begin
        ffg_calculator = FFGCalculator()
        results = Dict{String, Any}()
        accuracy_scores = Float64[]
        
        # 測試不同 DC 值的理論中位數計算
        test_dc_values = [0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9]
        
        for dc in test_dc_values
            try
                # 創建具有指定 DC 的 FFG 計算器
                dc_calculator = FFGCalculator(dc)
                theoretical_median = calculate_theoretical_ffg_median(dc_calculator)

                # 驗證理論中位數的合理性
                @test theoretical_median > 0  # 中位數應該為正數
                @test theoretical_median < 1000  # 中位數應該在合理範圍內
                @test !isnan(theoretical_median)  # 不應該是 NaN
                @test !isinf(theoretical_median)  # 不應該是無窮大

                # 驗證 DC 與中位數的關係（DC 越大，中位數應該越大）
                if dc > 0.1
                    prev_calculator = FFGCalculator(dc - 0.1)
                    prev_median = calculate_theoretical_ffg_median(prev_calculator)
                    @test theoretical_median >= prev_median  # 單調遞增關係
                end

                push!(accuracy_scores, 1.0)

            catch e
                @warn "DC $dc 的理論中位數計算失敗: $e"
                push!(accuracy_scores, 0.0)
            end
        end
        
        # 計算整體準確性
        overall_accuracy = mean(accuracy_scores)
        results["overall_accuracy"] = overall_accuracy
        results["tested_dc_values"] = length(test_dc_values)
        results["successful_calculations"] = count(score -> score == 1.0, accuracy_scores)
        
        @test overall_accuracy >= 0.95  # 要求 95% 以上的準確性
        
        println("  ✅ FFG 理論計算準確性: $(round(overall_accuracy * 100, digits=1))%")
        
        return results
    end
end

"""
FFG 經驗計算測試
測試基於歷史數據的 FFG 經驗中位數計算
"""
function test_ffg_empirical_calculation(test_data::Vector{LotteryDraw})::Dict{String, Any}
    @testset "FFG Empirical Calculation" begin
        ffg_calculator = FFGCalculator()
        results = Dict{String, Any}()
        
        empirical_results = []
        
        # 測試前 10 個號碼的經驗 FFG 計算
        for number in 1:10
            try
                empirical_median = calculate_ffg_median(ffg_calculator, number, test_data)
                
                # 驗證經驗中位數的合理性
                @test empirical_median >= 0  # 中位數應該非負
                @test empirical_median < length(test_data)  # 不應該超過數據長度
                @test !isnan(empirical_median)  # 不應該是 NaN
                @test !isinf(empirical_median)  # 不應該是無窮大
                
                # 記錄結果
                push!(empirical_results, Dict(
                    "number" => number,
                    "empirical_median" => empirical_median,
                    "is_valid" => true
                ))
                
            catch e
                @warn "號碼 $number 的經驗 FFG 計算失敗: $e"
                push!(empirical_results, Dict(
                    "number" => number,
                    "empirical_median" => NaN,
                    "is_valid" => false
                ))
            end
        end
        
        # 計算統計
        valid_results = filter(r -> r["is_valid"], empirical_results)
        success_rate = length(valid_results) / length(empirical_results)
        
        results["empirical_results"] = empirical_results
        results["success_rate"] = success_rate
        results["tested_numbers"] = length(empirical_results)
        results["valid_calculations"] = length(valid_results)
        
        @test success_rate >= 0.8  # 要求 80% 以上的成功率
        
        println("  ✅ FFG 經驗計算成功率: $(round(success_rate * 100, digits=1))%")
        
        return results
    end
end

"""
FFG 理論與經驗比較測試
比較理論 FFG 和經驗 FFG 的差異
"""
function test_ffg_theoretical_vs_empirical(test_data::Vector{LotteryDraw})::Dict{String, Any}
    @testset "FFG Theoretical vs Empirical" begin
        ffg_calculator = FFGCalculator()
        results = Dict{String, Any}()
        
        comparison_results = []
        
        # 測試前 5 個號碼
        for number in 1:5
            try
                # 計算經驗 FFG
                empirical_median = calculate_ffg_median(ffg_calculator, number, test_data)
                
                # 估算對應的 DC 值（簡化計算）
                estimated_dc = 5.0 / 39.0  # Lotto 5/39 的理論 DC

                # 計算理論 FFG
                dc_calculator = FFGCalculator(estimated_dc)
                theoretical_median = calculate_theoretical_ffg_median(dc_calculator)
                
                # 計算差異
                absolute_diff = abs(empirical_median - theoretical_median)
                relative_diff = absolute_diff / max(theoretical_median, 1.0)
                
                comparison = Dict(
                    "number" => number,
                    "empirical_median" => empirical_median,
                    "theoretical_median" => theoretical_median,
                    "absolute_difference" => absolute_diff,
                    "relative_difference" => relative_diff,
                    "is_reasonable" => relative_diff < 2.0  # 允許較大差異
                )
                
                push!(comparison_results, comparison)
                
                # 驗證差異在合理範圍內
                @test relative_diff < 5.0  # 相對差異不超過 500%
                
            catch e
                @warn "號碼 $number 的 FFG 比較失敗: $e"
            end
        end
        
        # 計算統計
        reasonable_count = count(comp -> comp["is_reasonable"], comparison_results)
        reasonableness_rate = reasonable_count / length(comparison_results)
        
        results["comparison_results"] = comparison_results
        results["reasonableness_rate"] = reasonableness_rate
        results["tested_numbers"] = length(comparison_results)
        
        @test reasonableness_rate >= 0.6  # 要求 60% 以上的合理性
        
        println("  ✅ FFG 理論與經驗合理性: $(round(reasonableness_rate * 100, digits=1))%")
        
        return results
    end
end

"""
FFG 邊界條件測試
測試各種邊界條件下的 FFG 計算
"""
function test_ffg_boundary_conditions()::Dict{String, Any}
    @testset "FFG Boundary Conditions" begin
        ffg_calculator = FFGCalculator()
        results = Dict{String, Any}()
        boundary_tests = []
        
        # 測試 1：極小 DC 值
        try
            small_dc_calculator = FFGCalculator(0.001)
            small_dc_median = calculate_theoretical_ffg_median(small_dc_calculator)
            @test small_dc_median > 0
            @test !isinf(small_dc_median)
            push!(boundary_tests, Dict("test" => "small_dc", "status" => "passed"))
        catch e
            @warn "極小 DC 值測試失敗: $e"
            push!(boundary_tests, Dict("test" => "small_dc", "status" => "failed"))
        end

        # 測試 2：極大 DC 值
        try
            large_dc_calculator = FFGCalculator(0.999)
            large_dc_median = calculate_theoretical_ffg_median(large_dc_calculator)
            @test large_dc_median > 0
            @test !isinf(large_dc_median)
            push!(boundary_tests, Dict("test" => "large_dc", "status" => "passed"))
        catch e
            @warn "極大 DC 值測試失敗: $e"
            push!(boundary_tests, Dict("test" => "large_dc", "status" => "failed"))
        end
        
        # 測試 3：空數據
        empty_data = LotteryDraw[]
        try
            empty_median = calculate_ffg_median(ffg_calculator, 1, empty_data)
            # 空數據應該返回合理的默認值或拋出異常
            @test empty_median >= 0 || isnan(empty_median)
            push!(boundary_tests, Dict("test" => "empty_data", "status" => "passed"))
        catch e
            # 拋出異常也是可接受的
            push!(boundary_tests, Dict("test" => "empty_data", "status" => "passed"))
        end
        
        # 測試 4：單筆數據
        single_data = [LotteryDraw([1, 2, 3, 4, 5], Date(2022, 1, 1), 1)]
        try
            single_median = calculate_ffg_median(ffg_calculator, 1, single_data)
            @test single_median >= 0
            push!(boundary_tests, Dict("test" => "single_data", "status" => "passed"))
        catch e
            @warn "單筆數據測試失敗: $e"
            push!(boundary_tests, Dict("test" => "single_data", "status" => "failed"))
        end
        
        results["boundary_tests"] = boundary_tests
        results["total_boundary_tests"] = length(boundary_tests)
        results["passed_boundary_tests"] = count(test -> test["status"] == "passed", boundary_tests)
        
        println("  ✅ 邊界條件測試: $(results["passed_boundary_tests"])/$(results["total_boundary_tests"]) 通過")
        
        return results
    end
end

"""
FFG 性能基準測試
測試 FFG 計算的性能表現
"""
function test_ffg_calculation_performance(test_data::Vector{LotteryDraw}, iterations::Int = 50)::Dict{String, Any}
    @testset "FFG Calculation Performance" begin
        ffg_calculator = FFGCalculator()
        results = Dict{String, Any}()
        
        # 預熱
        for _ in 1:5
            calculate_ffg_median(ffg_calculator, 1, test_data)
        end
        
        # 測試經驗 FFG 計算性能
        empirical_times = Float64[]
        for _ in 1:iterations
            start_time = time()
            calculate_ffg_median(ffg_calculator, rand(1:39), test_data)
            execution_time = (time() - start_time) * 1000  # 轉換為毫秒
            push!(empirical_times, execution_time)
        end
        
        # 測試理論 FFG 計算性能
        theoretical_times = Float64[]
        for _ in 1:iterations
            dc = rand() * 0.8 + 0.1
            dc_calculator = FFGCalculator(dc)
            start_time = time()
            calculate_theoretical_ffg_median(dc_calculator)
            execution_time = (time() - start_time) * 1000
            push!(theoretical_times, execution_time)
        end
        
        # 計算統計
        results["empirical_ffg"] = Dict(
            "mean_ms" => mean(empirical_times),
            "median_ms" => median(empirical_times),
            "max_ms" => maximum(empirical_times),
            "std_ms" => std(empirical_times)
        )
        
        results["theoretical_ffg"] = Dict(
            "mean_ms" => mean(theoretical_times),
            "median_ms" => median(theoretical_times),
            "max_ms" => maximum(theoretical_times)
        )
        
        # 性能要求檢查
        @test mean(empirical_times) < 50.0   # 經驗 FFG 計算應該小於 50ms
        @test mean(theoretical_times) < 5.0  # 理論 FFG 計算應該小於 5ms
        
        println("  ✅ FFG 計算性能:")
        println("    - 經驗 FFG: $(round(mean(empirical_times), digits=1))ms")
        println("    - 理論 FFG: $(round(mean(theoretical_times), digits=3))ms")
        
        return results
    end
end

"""
執行完整的 FFG 計算綜合測試
"""
function run_comprehensive_ffg_tests(data_manager::TestDataManager)::Dict{String, Any}
    println("🧪 開始執行 FFG 計算綜合測試...")
    
    comprehensive_results = Dict{String, Any}()
    
    # 使用中等大小的測試數據
    test_data = get_test_data(data_manager, "medium")
    
    try
        # 執行各項測試
        comprehensive_results["theoretical_accuracy"] = test_ffg_theoretical_accuracy(test_data)
        comprehensive_results["empirical_calculation"] = test_ffg_empirical_calculation(test_data)
        comprehensive_results["theoretical_vs_empirical"] = test_ffg_theoretical_vs_empirical(test_data)
        comprehensive_results["boundary_conditions"] = test_ffg_boundary_conditions()
        comprehensive_results["performance"] = test_ffg_calculation_performance(test_data)
        
        # 計算整體評分
        theoretical_score = comprehensive_results["theoretical_accuracy"]["overall_accuracy"]
        empirical_score = comprehensive_results["empirical_calculation"]["success_rate"]
        comparison_score = comprehensive_results["theoretical_vs_empirical"]["reasonableness_rate"]
        boundary_score = comprehensive_results["boundary_conditions"]["passed_boundary_tests"] / 
                        comprehensive_results["boundary_conditions"]["total_boundary_tests"]
        
        overall_score = (theoretical_score + empirical_score + comparison_score + boundary_score) / 4
        comprehensive_results["overall_score"] = overall_score
        
        println("\n📊 FFG 計算綜合測試結果:")
        println("  - 理論計算評分: $(round(theoretical_score * 100, digits=1))%")
        println("  - 經驗計算評分: $(round(empirical_score * 100, digits=1))%")
        println("  - 比較合理性評分: $(round(comparison_score * 100, digits=1))%")
        println("  - 邊界測試評分: $(round(boundary_score * 100, digits=1))%")
        println("  - 整體評分: $(round(overall_score * 100, digits=1))%")
        
        if overall_score >= 0.85
            println("🎉 FFG 計算測試：優秀")
        elseif overall_score >= 0.75
            println("✅ FFG 計算測試：良好")
        elseif overall_score >= 0.65
            println("⚠️ FFG 計算測試：需要改進")
        else
            println("❌ FFG 計算測試：需要重大修復")
        end
        
        return comprehensive_results
        
    catch e
        @error "FFG 計算綜合測試失敗: $e"
        comprehensive_results["error"] = string(e)
        comprehensive_results["overall_score"] = 0.0  # 設置默認評分
        return comprehensive_results
    end
end

# 導出主要函數
export test_ffg_theoretical_accuracy, test_ffg_empirical_calculation
export test_ffg_theoretical_vs_empirical, test_ffg_boundary_conditions
export test_ffg_calculation_performance, run_comprehensive_ffg_tests
