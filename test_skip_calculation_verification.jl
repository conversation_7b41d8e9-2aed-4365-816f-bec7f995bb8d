# Skip Calculation Verification Test
# Tests both MDIEditor Lotto WE and SkipSystem calculation methods

using Test
using Dates

# Include only the necessary types and functions
include("src/types.jl")
include("src/skip_analyzer.jl")

"""
Test skip calculation methods against documented examples
"""
function test_skip_calculation_methods()
    println("=== Skip Calculation Method Verification ===")
    
    # Create test data based on documented example
    # Number hits in draws: 1, 3, 6, 10
    # Expected skips (SkipSystem method): [current_skip, 4, 3, 2]
    # Expected skips (MDIEditor method): [current_skip, 3, 2, 1]
    
    test_draws = [
        [1, 5, 10, 15, 20],  # Draw 1: number 1 hits
        [2, 6, 11, 16, 21],  # Draw 2: number 1 misses
        [1, 7, 12, 17, 22],  # Draw 3: number 1 hits
        [3, 8, 13, 18, 23],  # Draw 4: number 1 misses
        [4, 9, 14, 19, 24],  # Draw 5: number 1 misses
        [1, 10, 15, 20, 25], # Draw 6: number 1 hits
        [5, 11, 16, 21, 26], # Draw 7: number 1 misses
        [6, 12, 17, 22, 27], # Draw 8: number 1 misses
        [7, 13, 18, 23, 28], # Draw 9: number 1 misses
        [1, 14, 19, 24, 29], # Draw 10: number 1 hits
        [8, 15, 20, 25, 30], # Draw 11: number 1 misses (current)
    ]
    
    # Create analyzer
    analyzer = SkipAnalyzer(test_draws)
    
    # Test SkipSystem method (current implementation)
    skips_skip_system = calculate_skips(analyzer, 1)
    println("SkipSystem method skips for number 1: $skips_skip_system")
    
    # Test MDIEditor method
    skips_mdi = calculate_skips_mdi_method(analyzer, 1)
    println("MDIEditor method skips for number 1: $skips_mdi")
    
    # Verify SkipSystem method
    # Number 1 hits in positions: 1, 3, 6, 10
    # Current skip: 11 - 10 = 1 (from last hit to current position)
    # Skip between 10 and 6: 10 - 6 = 4
    # Skip between 6 and 3: 6 - 3 = 3  
    # Skip between 3 and 1: 3 - 1 = 2
    expected_skips_skip_system = [1, 4, 3, 2]
    
    @test skips_skip_system == expected_skips_skip_system
    println("✓ SkipSystem method verification passed")
    
    # Verify MDIEditor method
    # Current skip: 11 - 10 = 1 (same as SkipSystem for current skip)
    # Skip between 10 and 6: 10 - 6 - 1 = 3
    # Skip between 6 and 3: 6 - 3 - 1 = 2
    # Skip between 3 and 1: 3 - 1 - 1 = 1
    expected_skips_mdi = [1, 3, 2, 1]
    
    @test skips_mdi == expected_skips_mdi
    println("✓ MDIEditor method verification passed")
    
    # Verify the difference is exactly 1 for historical skips
    for i in 2:length(skips_skip_system)
        @test skips_skip_system[i] == skips_mdi[i] + 1
    end
    println("✓ Difference verification passed: SkipSystem = MDIEditor + 1")
    
    return true
end

"""
Test FFG consistency with SkipSystem method
"""
function test_ffg_skip_consistency()
    println("\n=== FFG-Skip Consistency Verification ===")
    
    # Create test data
    test_draws = [
        [1, 5, 10, 15, 20],
        [2, 6, 11, 16, 21],
        [1, 7, 12, 17, 22],
        [3, 8, 13, 18, 23],
        [1, 9, 14, 19, 24],
    ]
    
    analyzer = SkipAnalyzer(test_draws)
    ffg_calc = FFGCalculator()
    
    # Test number 1
    current_skip = get_current_skip(analyzer, 1)
    ffg_median = calculate_ffg_median(ffg_calc, 1, analyzer.historical_data)
    
    println("Number 1 - Current skip: $current_skip, FFG median: $ffg_median")
    
    # Test favorable timing logic
    is_favorable = current_skip <= ffg_median
    println("Is favorable timing: $is_favorable")
    
    # Verify skip chart generation
    skip_chart = generate_skip_chart(analyzer, 1)
    @test skip_chart.current_skip == current_skip
    @test skip_chart.ffg_median == ffg_median
    @test skip_chart.is_favorable == is_favorable
    
    println("✓ FFG-Skip consistency verification passed")
    
    return true
end

"""
Test edge cases for skip calculation
"""
function test_skip_edge_cases()
    println("\n=== Skip Calculation Edge Cases ===")
    
    # Test case 1: Number never appears
    test_draws_no_hit = [
        [2, 5, 10, 15, 20],
        [3, 6, 11, 16, 21],
        [4, 7, 12, 17, 22],
    ]
    
    analyzer_no_hit = SkipAnalyzer(test_draws_no_hit)
    skips_no_hit = calculate_skips(analyzer_no_hit, 1)
    current_skip_no_hit = get_current_skip(analyzer_no_hit, 1)
    
    @test isempty(skips_no_hit)
    @test current_skip_no_hit == length(test_draws_no_hit)
    println("✓ No hits case handled correctly")
    
    # Test case 2: Number appears only once
    test_draws_one_hit = [
        [1, 5, 10, 15, 20],
        [2, 6, 11, 16, 21],
        [3, 7, 12, 17, 22],
    ]
    
    analyzer_one_hit = SkipAnalyzer(test_draws_one_hit)
    skips_one_hit = calculate_skips(analyzer_one_hit, 1)
    current_skip_one_hit = get_current_skip(analyzer_one_hit, 1)
    
    @test length(skips_one_hit) == 1
    @test current_skip_one_hit == 2  # 3 - 1 = 2
    println("✓ Single hit case handled correctly")
    
    # Test case 3: Number appears in consecutive draws
    test_draws_consecutive = [
        [1, 5, 10, 15, 20],
        [1, 6, 11, 16, 21],
        [1, 7, 12, 17, 22],
        [2, 8, 13, 18, 23],
    ]
    
    analyzer_consecutive = SkipAnalyzer(test_draws_consecutive)
    skips_consecutive = calculate_skips(analyzer_consecutive, 1)
    
    # Expected: [1, 1, 1] (current skip = 1, then skips of 1 between consecutive hits)
    @test skips_consecutive == [1, 1, 1]
    println("✓ Consecutive hits case handled correctly")
    
    return true
end

"""
Run all skip calculation verification tests
"""
function run_skip_verification_tests()
    println("Starting Skip Calculation Verification Tests...")
    
    try
        test_skip_calculation_methods()
        test_ffg_skip_consistency()
        test_skip_edge_cases()
        
        println("\n🎉 All skip calculation verification tests passed!")
        return true
    catch e
        println("\n❌ Skip calculation verification tests failed: $e")
        return false
    end
end

# Run tests if this file is executed directly
if abspath(PROGRAM_FILE) == @__FILE__
    run_skip_verification_tests()
end
