---
created: 2025-07-24T21:26:40 (UTC +08:00)
tags: [strategy,systems,lotto,wheels,software,lottery,jackpot,computer,generate,random,play,win,lost lotto jackpot,]
source: https://saliu.com/lotto-jackpot-lost.html
author: 
---

# Jackpot Lottery Strategy Software: 6-12 Number Lotto Wheels

> ## Excerpt
> Lotto strategy by the best lottery player ever hit the jackpot. It generates 12-number random combinations converted to efficient 6-number lotto wheels.

---
**_<PERSON><PERSON><PERSON><PERSON><PERSON>, Da <PERSON> Lotto Programmer, <PERSON>, Ana<PERSON><PERSON>, Strategist_**

[![<PERSON> has won the lottery multiple times since 1980s in Romania and United States.](https://saliu.com/AxiomIon.jpg)](https://saliu.com/free-lotto-lottery.html)  

## <u><i>Jackpot</i> Lottery Strategy: 12-Number Combinations, Lotto-6 Wheels, Pioneer Software</u>

## By <PERSON>, _★ The Best Lottery Player in History_

![The lotto strategy that hit the Pennsylvania Lottery jackpot in 1986.](https://saliu.com/HLINE.gif)

Authored by <PERSON> on June 3, 2010.

## <u>I. Software Author, Lottery Winner</u>

There have been questions in my forums (a message board hosted independently from **SALIU.COM**). Also, I've received questions from readers of my book. Why don't I present clearly and in detail that lotto strategy that hit a jackpot in 1986 (although was not played)?

Part of that lottery strategy was already presented in my lotto forums, responding to a question from an active member (whose also a _skeptic_!)

First, I had intended to offer specific software, exactly like my original software of 1986: Generate 12-number combinations, in random manner. I got the programs started. The task is huge, however. I can implement now full filters to eliminate groups of 1, 2, 3, 4, 5, and 6 numbers from past drawings.

Eliminate groups of 3 numbers: C(12, 3) = 220 possible groups;  
Eliminate groups of 4 numbers: C(12, 4) = 495 possible groups;  
Eliminate groups of 5 numbers: C(12, 5) = 792 possible groups;  
Eliminate groups of 6 numbers: C(12, 6) = 924 possible groups.

Over 2300 eliminating procedures to deal with!

That software project will be on the backburner for a while. I will travel to Romania, especially to see my old and frail parents again. It was 1993 when I saw them last time. They want to see me again — and I want to see them again. They fear they might never see me again. I promised them for years, and years, and years that I would visit them… one year after another… It's so hard to get from here to there. It's the distance … and it's about money too. And, again, something unwanted comes to aggravate my situation. This year, temporary but physically hard work almost destroyed my fingers and hands. Pretty much recovered now.

Pages 127-130 of my book, **_Probability Theory Live!_** present my first lotto strategies. They were so-called _**paper-and-pencil**_ methods. That is, I created the strategies manually, without computer software.

The very first method was based on number frequency and pairing. I would check the lotto numbers for the past 20 drawings or so. I would select numbers with good frequencies and also well paired with one another. I would group the numbers 3 at a time, being careful not to group 3 numbers that also came together in one of the past 20 drawings.

I usually came up with 12 lotto numbers to play. I grouped them 3 at a time based on pairing. Then, I _wheeled_ the 12-number group applying a **special lotto wheel**. I had noticed a special leverage in that 12-number wheel applied to lotto-6 games. There is a clear presentation here, including the mathematics of lotto wheeling:

-   [_**Make Lotto Wheels, Systems: Manually, or in Lotto Wheeling Software**_](https://saliu.com/lottowheel.html).

That lotto strategy led to a significant prize in the Romanian lottery in the early 1980s. The two economists I played with promised that I wouldn't have to pay for the tickets, as I was the one who did the whole strategy work. Later on, the two guys split the profit between them. They didn't give me a penny… I mean, nothing. They claimed that I hadn't paid them my share of the tickets cost!

And thus I arrived as a refugee to the United States in 1985. One day, one of my farm fellow workers gave me lottery materials (a list of past drawings, tickets for various games, etc.) He invited me to play with his group. The Porto Rican later told me that he had figured out I was a very smart guy, therefore I might be able to figure out how to win the lottery! I am not kidding! We won the very first time I played (the third prize). Each one of them played my lotto combinations as well. Everybody was happy. We played again next drawing — and we won again!

I applied the same lotto strategy based on number frequency and pairing. As I was _“just a poor boy, stranger in this town”_ — per _Pink Floyd_ — I settled for 9 lotto numbers instead of 12. This time around, I applied twice a 9-number lotto wheel. That lotto wheel had also a very good leverage, as you can see on the page mentioned above. The requirement was to play 2 tickets for $1. So, each member of our lotto group played 6 tickets for $3 apiece. My lotto strategy offered 5 and 4 winning numbers, respectively. Given the good leverage of the wheel, we hit the third prize (_4 winners of 6_) in two consecutive drawings.

![Play a powerful lotto strategy with 12-number combinations in lotto-6 games worldwide.](https://saliu.com/HLINE.gif)

## <u>II. Lotto Jackpot <i>Hit</i>, <i>NOT</i> Played</u>

And here we reach now the moment when I devised a new lotto strategy. It was now computer-run and based on filtering.

By the end of 1985, I had the lotto combination-generating algorithm working perfectly! I stepped up and bought a new computer. It was a bargain alright (a discontinued _Atari 800 XL_). It was a powerhouse, by my standards! It had **64 KB** or RAM (compared to the previous 4 KB of Sinclair). I was able to develop my lottery algorithms to new heights. My algorithms were now real lottery programs. I could also do statistical analyses of a few recent lotto drawings. It was not easy, though. My **_Atari 800 XL_** did not have a disk drive and I couldn't find one (the home computer was a discontinued model). It very much looks nightmarish now, but I had to type the source code in BASIC every time I wanted to run a program! Believe it or not, I enjoyed the process. One must enjoy the process if one wants to perfect the process and thusly perfect himself/herself.

I played a few more times with the same Porto Rican fellow and another one (who was also a great singer). We did win a few times, but only the third tier prize. We felt good and our hopes grew higher.

_Ion Saliu sings on **YouTube** music by Don Gabito of Puerto Rico:_

<iframe src="https://www.youtube.com/embed/pg10ONophXY" frameborder="0" allowfullscreen=""></iframe>

The big event did happen: The lotto jackpot occurrence. I know exactly it was a Friday in February. We were pruning apple trees in an orchard. The year could be 1986 (most likely) or 1987. I did write about it at my website (the old message board). A couple of messages are still there. It was about random numbers generated by computers. I was amazed how the old home computers generated random numbers that resembled more closely manual drawings in lotteries. The random seed for those computers was hardware-based. By contrast, the **_IBM PC_** relied on software-generated random seeds (_TIMER_). There are several pages at SALIU.COM about those issues:

-   [_**Randomness, Degree of Randomness, Degree of Certainty, True Random Numbers Generators**_](https://saliu.com/bbs/messages/683.html)
-   [_**BASIC source code software to generate true random & unique numbers**_](https://saliu.com/random-numbers.html).

### Now, the big _lotto strategy_ that did hit the _jackpot_.

I missed the jackpot, as I wrote the aforementioned post in my forums. That Porto Rican fellow had such a negative impact on my life! I do not mean he was intentionally malicious. It just happened, although his personality did play a role. You already read in my forum about some of his acts...

My lotto strategy was based on non-repeating groups of numbers from previous drawings. I was limited by my home computer. First, its speed was very low for a demanding task. Second, I had to retype my programs every time I ran them!

My lotto programs would generate 12-number combinations. That **12-number lotto wheel** was the main reason why I wanted only 12-number combinations. I tried eliminating 2-number groups from the previous lottery drawings. I also tried eliminating 3-number groups, but the program couldn't generate a single combination, more often than not. I think I made a good decision when I settled for eliminating 4-number groups from the most recent 20 drawings, if possible. If not, I was happy with 15 or 18 drawings. Time was of the essence.

I wanted at least 20 12-number lotto combinations. My computer would run for days and nights. My TV set played also the role of a computer monitor! I was able to only listen to the radio (a news buff I am)…

The three of us agreed to play $6 each every week. Accordingly, I selected the last three 12-number combinations generated by my AtariXL. I then applied twice the 12-number lotto wheel to each of the 3 12-number combinations generated. The total was 36 6-number lotto combinations, for a cost of $18.

The 12 numbers in each combination were random. I did not order the numbers in lexicographic order. First, it was due to convenience: I would have had to retype a whole lot more when running my programs. But in the end it proved to be a wise decision based on mathematics. _Randomness improves the chance!_ No, it is not a paradox.

First, I applied the lotto wheel to the exact random sequence as the computer generated. Then, I shuffled the sequence. I had run a short program to generate just random numbers, 12 of them. I let the program run a long time, then I selected the last 12-number random sequence. I wrote it down, and I used it to shuffle each 12-number combination to play before wheeling.

We also decided to change the 12-number lotto combination after each lottery drawing. I couldn't follow that rule precisely for no fault of my own. The apartment I lived in (formerly a doctor's office) belonged to the farm I worked for. There was a prankster in the family who owned the farm. Donald was in his late 70s and enjoyed playing practical jokes. One of his favorites was to cause power outages, so that my computer would stop working. My apartment was above his garage. Also the breakers controlling the power in my apartment were located in his garage. He would laugh like crazy when he saw my face… in front of my idle computer! The two Porto Ricans called him _viejo pendejo_.

When Donald made sure I wouldn't get fresh lotto combinations, we would replay the tickets from the previous week. The last time we didn't play fresh combinations was when we hit the jackpot… _in absentia_! We didn't play, as you read in my forum.

![The best-ever lotto player created a strategy that hit the lotto jackpot in Pennsylvania Lottery.](https://saliu.com/HLINE.gif)

## <u>III. Lottery Software to Generate More Than 6 Lotto Numbers Per Combination</u>

As I said, I do not have complete lotto software to generate 12-number combinations. So, for now, I'll show how to use my 6-number lotto software to apply that strategy.

The choice is Combine6: We must generate random combinations. The option is _Randomized Combinations_, the first screen in Bright Lotto Software App. The only lottery filters that work in this strategy are the _**minimum**_ levels of: _**ONE, TWO, THREE, FOUR, DEL\_4, DEL\_5,**_ and _**DEL\_6.**_ I strongly recommend to use as _**SIM-6**_ the shuffled file with ALL combinations in your lotto game. You'll notice very high values, especially DEL\_6 (over 5,000,000 several times). We have to run the winning report generator so see the filters in your game.

I would disregard now the _ONE_ and _TWO_ filters. Use high values for _DEL\_5_, and _DEL\_6_. Add also _THREE = 100_ or _FOUR = 1000_.

Let the program run for a while. Press _F10_ to stop it, then _Run Again, same data file_. Enter the same filters. Run it again for a while, the stop it again… repeat the procedure several times. This method does increase the degree of certainty. It's mathematical: **The degree of certainty increases with the increase in the number of trials**. Chances are good you wouldn't have won anything in the first tries. So, you saved money and also get a better winning chance.

Keep also in mind that the current PCs are much, much faster than the electronic toys I used in the 1980s. The randomness was also better with the old home computers, not only because they were much slower. They also used a hardware-based randomization procedure. The IBM PC, on other hand, introduced a software-based randomization scheme based on the _TIMER_ ('_number of seconds since midnight_'). My lottery software improved the randomization scheme of the PC by using a special _Randomize_ function, independent of the _Timer_.

So, you repeated the 6-number random combinations several times. You should wait for at least 25,000 – 30000 combinations. You need lots of them to prepare them for a conversion to 12-number lotto combinations. You saved the combinations to an output file.

Next important piece of software is **WheelIn6**. The option in **Bright6** is _Wheels from files_. Run **WheelIn6** for the output file you just generated. What we need to do here is to get 6-number combinations that are absolutely unique. They don't have one single number in common. We need to concatenate them two at a time and thus obtain 12-number combinations with unique numbers only. In this case, we apply the _1 in 6_ restriction in **WheelIn6**.

Next, just combine two neighboring 6-number _combosnations_ (a favorite of mine)! The result will be 12 unique numbers per line. If you have an odd amount of 6-number combinations, simply add the last lotto combo to any previous 6-number line.

The concatenated 12-number combinations should be randomized (shuffled) twice. The best tool is my scientific software, of course: **Shuffle**.

Finally, apply twice the 12-number lotto wheel as you read on the webpage mentioned at the beginning of this material. The 9-number and 12-number lotto wheels are the only ones I've ever trusted. They are the only lotto wheels based on solid mathematics. The two wheels offer a tremendous leverage. Personally, I will never play any other lotto wheel. Actually, I settled on the 12-number wheel only.

This extraordinary phenomenon in lotto wheeling is known as the **_Parpaluck effect_**. It only occurs in lotto games that draw an even amount of winning numbers: Lotto-4, lotto-6, lotto-8 (if ever implemented), etc. For example, lotto-4 draws 4 winning numbers. The optimal wheel has 8 numbers (the double of the winning numbers). Divide the 8 numbers in groups of 2; a total of 4 groups results; wheel the 4 groups two at a time; the final result is a 6-line lotto wheel, with 4 numbers per combo. The distribution of the winning numbers in the 4 groups can be: _0-0-2-2_ (first prize); _1-1-1-1_ (the worst distribution = third prize); etc.

As always, my recommendation is to start some 100 drawings back in your game. Check for winners in the next 100 draws, as if it were the future. You can try also two ways. One, play the same set of lotto combinations for several drawings _“in the future”_. Two, apply the strategy after each drawing in the lotto game; check for winners only for the _“next draw in the future”_.

### <u>Update October 2, 2010</u>

-   There is now software that works directly with the lottery strategy presented on this page:
-   [_**Lotto Strategy, Software: 12-Numbers Combinations in 6-Number Lotto Games**_](https://saliu.com/12-number-lotto-combinations.html).
-   There is also software that applies this lottery strategy to pick-5 lotto:
-   [_**Lotto Software, Wheels: 10-Number Combinations in Lotto 5 Games**_](https://saliu.com/lotto-10-5-combinations.html).
-   _"The Start Is the Hardest Part"_ in [_**Lotto Strategies**_](https://forums.saliu.com/lottery-strategies-start.html).
-   [_**A Brief History of Parpaluck's Lottery, Lotto Experience with Software, Systems, Strategies**_](https://saliu.com/bbs/messages/532.html).
-   [_**Lottery, Gambling Experiences: Robbed of Prize Money!**_](https://saliu.com/bbs/messages/535.html).
-   These lottery strategies keep working across time and space; e.g., year of grace 2021:
-   [_**Winning Lottery Strategy with Frequent Triples and Lotto Wheels**_](https://saliu.com/lotto-triples.html).

Best of luck from yours truly, Parpaluck!

_Ion Saliu sings **Federal Lottery** on YouTube:_

<iframe src="//www.youtube.com/embed/Qbk5ZgXBZm0" frameborder="0" allowfullscreen=""></iframe>

![Ion Saliu's Probability Book on mathematics of lotto strategy.](https://saliu.com/probability-book-Saliu.jpg) Read Ion Saliu's first book in print: [**_Probability Theory, Live!_**](https://saliu.com/probability-book.html)  
~ Discover profound scientific implications of the **_Fundamental Formula of Gambling (FFG)_**, including mathematics of lotto; two chapter dedicated to devising lotto strategies.

![The best lotto wheels are also created by the best lottery player in history, Ion Saliu.](https://saliu.com/HLINE.gif)

**[Home](https://saliu.com/index.htm) | [Search](https://saliu.com/Search.htm) | [New Writings](https://saliu.com/bbs/index.html) | [Fundamental Formula](https://saliu.com/formula.htm) | [Odds, Generator](https://saliu.com/calculator_generator.html) | [Contents](https://saliu.com/content/index.html) | [Forums](https://forums.saliu.com/) | [Sitemap](https://saliu.com/sitemap/index.html)**

![Lotto strategy by the best lottery player ever hit the jackpot generating 12-number combinations.](https://saliu.com/HLINE.gif)
