# Wonder Grid Lottery System - 清潔使用範例
# 解決命名衝突並展示正確的使用方式

println("🎯 Wonder Grid Lottery System - 清潔使用範例")
println("=" ^ 60)

# ==========================================
# 方法 1: 使用模組前綴避免衝突
# ==========================================

println("\n📦 方法 1: 使用模組前綴")
println("-" ^ 40)

# 載入模組但不導入符號
import WonderGridLotterySystem as WGLS

# 使用模組前綴訪問功能
println("✅ 模組載入成功，使用前綴 WGLS")

# 創建測試數據
test_data = [
    WGLS.LotteryDraw([1, 8, 15, 22, 29], Date(2023, 1, 1), 1),
    WGLS.LotteryDraw([3, 12, 18, 25, 31], Date(2023, 1, 2), 2),
    WGLS.LotteryDraw([5, 9, 16, 23, 35], Date(2023, 1, 3), 3),
    WGLS.LotteryDraw([2, 11, 19, 27, 33], Date(2023, 1, 4), 4),
    WGLS.LotteryDraw([7, 14, 21, 28, 36], Date(2023, 1, 5), 5),
]

println("📊 創建了 $(length(test_data)) 筆測試數據")

# 創建分析組件
skip_analyzer = WGLS.SkipAnalyzer(test_data)
ffg_calculator = WGLS.FFGCalculator(0.5)

println("🔧 分析組件初始化完成")

# 進行 Skip 分析
println("\n📈 Skip 分析結果:")
for number in [1, 8, 15, 22, 29]
    current_skip = WGLS.get_current_skip(skip_analyzer, number)
    ffg_median = WGLS.calculate_ffg_median(ffg_calculator, number, test_data)
    
    println("  號碼 $number: Skip=$current_skip, FFG=$(round(ffg_median, digits=2))")
end

# ==========================================
# 方法 2: 選擇性導入
# ==========================================

println("\n📦 方法 2: 選擇性導入")
println("-" ^ 40)

# 只導入需要的函數，避免類型衝突
using WonderGridLotterySystem: calculate_skips, get_current_skip, calculate_ffg_median

println("✅ 選擇性導入成功")

# 直接使用導入的函數
println("\n📊 使用導入的函數:")
skips = calculate_skips(skip_analyzer, 1)
println("  號碼 1 的歷史 Skip: $(join(skips[1:min(3, length(skips))], ", "))...")

# ==========================================
# 方法 3: 清潔環境重新開始
# ==========================================

println("\n📦 方法 3: 建議的清潔使用方式")
println("-" ^ 40)

println("""
🧹 清潔環境步驟:
1. 重新啟動 Julia REPL
2. 直接使用: using WonderGridLotterySystem
3. 或者使用新的 Julia 會話

💡 推薦做法:
# 在新的 Julia 會話中
julia> using WonderGridLotterySystem

# 創建測試數據
julia> test_draws = [
    LotteryDraw([1, 8, 15, 22, 29], Date(2023, 1, 1), 1),
    LotteryDraw([3, 12, 18, 25, 31], Date(2023, 1, 2), 2),
    # ... 更多數據
]

# 創建分析器
julia> analyzer = SkipAnalyzer(test_draws)
julia> calculator = FFGCalculator(0.5)

# 進行分析
julia> skip = get_current_skip(analyzer, 1)
julia> ffg = calculate_ffg_median(calculator, 1, test_draws)
""")

# ==========================================
# 方法 4: 實用的工作流程
# ==========================================

println("\n🔧 方法 4: 實用工作流程")
println("-" ^ 40)

# 定義便利函數
function quick_analysis(data, numbers)
    analyzer = WGLS.SkipAnalyzer(data)
    calculator = WGLS.FFGCalculator(0.5)
    
    results = []
    for number in numbers
        skip = WGLS.get_current_skip(analyzer, number)
        ffg = WGLS.calculate_ffg_median(calculator, number, data)
        is_favorable = skip <= ffg
        
        push!(results, (
            number = number,
            skip = skip,
            ffg = round(ffg, digits=2),
            favorable = is_favorable
        ))
    end
    
    return results
end

# 使用便利函數
println("\n📊 快速分析結果:")
results = quick_analysis(test_data, [1, 8, 15, 22, 29])

for result in results
    status = result.favorable ? "✅" : "❌"
    println("  號碼 $(result.number): Skip=$(result.skip), FFG=$(result.ffg) $status")
end

# ==========================================
# 解決方案總結
# ==========================================

println("\n💡 解決方案總結")
println("-" ^ 40)

println("""
🎯 推薦解決方案:

1. 🆕 新會話 (最佳):
   - 重新啟動 Julia
   - 直接 using WonderGridLotterySystem

2. 🏷️ 模組前綴:
   - import WonderGridLotterySystem as WGLS
   - 使用 WGLS.LotteryDraw 等

3. 🎯 選擇性導入:
   - using WonderGridLotterySystem: 特定函數名
   - 避免導入衝突的類型

4. 🔧 便利函數:
   - 封裝常用操作
   - 簡化使用流程

⚠️ 警告說明:
- 警告不會影響功能
- 只是提醒有命名衝突
- 系統仍然正常工作
""")

println("\n🎉 清潔使用範例完成！")
println("💡 建議在新的 Julia 會話中重新開始以獲得最佳體驗")
