---
created: 2025-07-24T21:26:40 (UTC +08:00)
tags: [positional lotto wheels,reduced,lotto,systems,lottery wheel,software,position-by-position,in-position,real winnings,analyses,]
source: https://saliu.com/positional-lotto-wheels.html
author: <PERSON>
---

# Positional Lotto Wheels, Wheeling

> ## Excerpt
> The positional lotto wheels are the best, most efficient reduced lottery systems, winning far more frequently than the non-positional lotto wheels.

---
Published in January 2018.

Axiomatic one, you might be among the visitors who asked me about **positional lotto wheels**, especially starting the year of grace 2017. The interest grew by an order of magnitude after I posted analyses of positional lottery wheels in the venerable _rec.gambling.lottery (RGL)_ newsgroup. My **in-position lotto wheels** showed a tremendous advantage over any similar systems. My thread in the Google groups is still live:  

-   [_**Positional Lotto Wheels from Groups of Numbers**_](https://groups.google.com/forum/?hl=en#!topic/rec.gambling.lottery/jsjtCP6Pszc).

The 274-line _position-by-position_ lotto wheel hit one _4 of 6_ winner and 8 _3 of 6_ winners. It happened on January 24, 2018.

My lotto software can also check for winners in positional groups of numbers without expanding them to combinations first. The positional frequency system hit two jackpots in one year worth of lottery drawings. The un-wheeled system recorded also 15 _match-5_ and 23 _4 of 6_ winners. I didn't check for _match-3_ — there would have been plenty of those! The positional systems are the way to go in lotto. They hit more often (more efficiently) because the lotto numbers have an inherent tendency to hit in certain positions. The positional lotto systems (based on both frequency and skips) respect that natural tendency.

I haven't written, however, **dedicated** software to generate lotto wheels from ranges of numbers. That is, given, say, 6 strings of numbers for the lotto-6 games, let's generate lotto wheels where the numbers are strictly in-position. If a lotto number is only in the string in position 1, that entity must always appear only in the 1st position of the combinations. No 2 or more numbers in the same position will appear in the same combination. In the real system you see below, the lotto numbers _1_ and _4_ in the first-position string will not show together (in the same combination).

Fear not! My software is more than capable of handling a task of such magnitude — and it is the only software of this kind. It only takes two steps to generate positional lottery wheels from output files. Until I decide to write specialized, in-one-step positional-wheeling software, you can simply follow the easy procedure clearly described on this Web page. Not to mention that the personal computers are very fast nowadays and they can fulfill this task with flying colors.

### <u>STEP 1: Generate combinations from strings of numbers arranged by positions</u>

The 6-number lotto game has 6 positions; therefore you'll have 6 strings of numbers. Example: the positional frequency system we have analyzed in _RGL_. The system consists of the _top-12_ UK 6/59 lotto numbers in each position (based on frequency).

```
<span size="5" face="Courier New" color="#c5b358">  2   1  10  11   4   8   5   7  13   3   9  12
 14  17  12   3  13  11   8  22   7  15  16   2
 30  31  26  18  25  16  20  29  21  22  35  27 
 32  34  37  46  38  39  42  28  40  41  19  24 
 40  51  44  41  53  48  49  43  52  47  54  37 
 58  57  59  54  52  55  56  50  51  49  42  46 
</span>
```

In this case, each string has an equal length (same amount of numbers). But the strings can also be uneven, as it is mostly the case with the positional skips systems.

Software to generate lotto combinations strictly position-by-position: **SkipSystem**, menu II of the **Bright / Ultimate** grandiose lottery applications. Functions: _S = Lotto 6_, then _G = Generate Combinations from Systems_.

![The lottery skip software generates combinations from positional systems.](https://saliu.com/ScreenImgs/skip-systems-lotto.gif)

-   **Additional operation (not mandatory, but strongly recommended)**: Apply the _**LIE Elimination**_ in a combination generator in my software. For example, you might want to eliminate combinations generated by non-positional systems as they hit far more rarely. You might want to take a look at my first post in the aforementioned _RGL_ thread.
-   First, I generated combinations from the non-positional system consisting of the _top-24_ UK 6/59 lotto numbers (based on frequency). As you know by now, that is, after reading my theory, the non-positional lottery systems fare significantly worse than their positional counterparts. Therefore, they represent ideal candidates for _**LIE Elimination**_.
-   Next, we generate combinations from the positional systems. They hit far more frequently, albeit with many more combinations. But that's why we employ the _**LIE Elimination**_ strategy!
-   Finally, we _**LIE Eliminate**_ the combinations generated by the non-positional string of numbers _from_ the combinations generated by the positional frequency lottery system. In my example, I applied the _LieID filter = 4_. Translation: My level of confidence is high that the non-positional system will not fare better than _3 of 6_. That is, more often than not, the non-positional number will not hit 4, or 5, or 6 winners. In the case above, the situation was way, way better. The non-positional frequency system has one and only one hit. Thus, I could have applied the _LieID filter = 2_ and very few combinations are generated. The next step — wheeling — would not be necessary. We would hit as many winners as the positional system hit unexpanded.
-   There are many more reduction strategies one can apply with high degrees of certainty. Both frequency and skip-based non-positional systems can be fed to _**LIE Elimination**_, one file at a time. The _LieID filters_ might be different, however.
-   If the _LieID filters_ are equal for various files, you can concatenate (combine them in one file) and perform the reduction in one step.
-   The _**LIE Elimination**_ function is present in all combination generators of my lottery software.
-   You can learn a lot more by reading the specialized page in the _Resources_ section. In fact, every entry in _Resources_ is really valuable.

### <u>STEP 2: Wheel the output file generated at Step 1</u>

-   Additional operation (not mandatory, but strongly recommended): Shuffle or randomize the final output after the _**LIE Elimination**_.
-   Pertinent software: **Shuffle.exe**, menu IV of **Bright / Ultimate** software, function _R = Randomize, Shuffle Elements_. Then, select the function _F = Shuffle Files (Numbers & Text)_ in the program.
-   Wheeling a randomized file does bring about better results: Fewer lines in the wheel while maintaining the minimum guarantee.

You have three choices here. The first one was applied in the aforementioned thread.

**<u>A) I pointed you to one of the functions (programs)</u>** in my lottery software capable of such much-sought-after task: **Super Utilities** (**SoftwareLotto6.exe**). It is listed as **Super Utilities** on the main menu of the **Bright / Ultimate** grand lottery packages. We'll use for this task the function _D = Duplicates: Strip and Wheel_. I chose the _3 of 6_ minimum guarantee to get the minimal amount of combinations to be published in the Google group.

![Lottery utility software has a function to wheel in combinations that satisfy minimum guarantees.](https://saliu.com/ScreenImgs/positional-wheel-utility.gif)

**<u>B) Function <i>H = Wheels from Files</i></u>** (**WheelIn6.exe**), menu #2 of the **Bright / Ultimate** great applications. If I choose the _3 of 6_ minimum guarantee again, I'll get the same amount of lotto combinations as in **Option A**.

_H = Wheels from Files_ is very similar to _D = Duplicates: Strip and Wheel_.

![Special lottery wheeling software creates efficient lotto wheels from input files.](https://saliu.com/ScreenImgs/positional-wheel-program.gif)

**<u>C) The best wheeling choice: <i>W = Wheel On-the-Fly</i></u>** (**Wheel6.exe**), also on menu #2. In **Wheel6**, select the minimal guarantee, and then the function _P = Purge-Wheel an Output File_. This time, however, if I apply the _3 of 6_ minimum guarantee, I'll get fewer combinations than in **Option A** or **Option B**. Therefore, the lottery wheel is more efficient cost-wise.

This is the most efficient option. It generates random combinations that never start at _1, 2, 3, 4, 5, 6_ or _44, 45, 46, 47, 48, 49_. Also importantly, the amount of combinations generated is lower than in **A** or **B** — without affecting the minimum guarantee. There is a higher chance at hitting higher prizes. You can even apply safe filters to reduce the total of lines generated and still preserving the minimal guarantee.

There is one drawback, however: Speed. You need be more patient compared to options **A** and **B**. If you wait more than half an hour without a single combination having been generated, you can stop the program. It probably found a complete randomized lotto wheel that satisfies the minimum guarantee.

### <u>World-Record Lotto Wheel (<i>54, 3 of 6</i>) = <i>274</i> Lines</u>

The special reduced lottery system was created in the process unintentionally. The previous world record in lottery wheeling was for <u>49 unique numbers in 163 lines</u>. Not to mention that the particular cover or design had a severe flaw: It was a _SPLIT_ wheel. The lotto numbers were divided into two groups; the numbers in one group were never combined with numbers from the other group. No wonder such lotto wheel fares very poorly when targeting higher-tier prizes.

My cover or lotto design treats the numbers equally. <u>The key feature is the natural bias of the lotto numbers toward one or two positions in the combination.</u> Evidently, nobody has ever seen a 6/59 combination to start with number 58; and we'll never see lotto number 3 in the 6th position!

How can I state I created the world-record lotto wheel? I can answer such a question and set it on a solid foundation. I can compare the sizes of the two designs and how many combinations they cover.

The previous world-record lotto wheel: 163 lines; my design: 274 lines. Ratio: 274 / 163 = 1.68. The previous design covered _6 of 49_ numbers or 13983816 combinations. My lotto wheel deals with _6 of 54_ unique numbers that generate 25827165 combinations. Ratio: 25827165 / 13983816 = 1.85.

<u>Advantage of my lotto wheel: 1.85 – 1.68 = 0.17 (17%).</u> Therefore, my lotto design is the champion of the world by a margin of 17 percentage points. For <u>parity</u>, the _(49, 3 of 6)_ lotto wheel should have a maximum of <u>136</u> lines, not 163.

_“\[Lotto\] wheel in the sky keeps on turnin'  
I don't know where I'll be tomorrow  
Wheel in the sky keeps on turnin'.”_  
Journey, _“Wheel In The Sky”_

<iframe src="https://www.youtube.com/embed/Ez-lRQAklV4" frameborder="0" allow="autoplay; encrypted-media" allowfullscreen=""></iframe>

![The best lotto wheels are created on-the-fly by purging files generated from positional systems.](https://saliu.com/ScreenImgs/on-the-fly-wheeling.gif)

## [<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)

It lists the main pages on the subject of lottery, lotto, software, wheels and systems.

-   _**The Main**_ [_**Lottery, Software, Strategies**_](https://saliu.com/LottoWin.htm), _**Lotto Wheeling Page**_.  
    Presenting software to create free winning lotto, lottery strategies, systems based on mathematics. Get your lotto systems or wheels, the best lottery, lotto software, combinations, winning numbers.
-   [**<u>Lotto Wheels</u>**](https://saliu.com/lotto_wheels.html) for _**Lottery Games Drawing 5, 6, 7 Numbers**_.  
    The most advanced theory of lotto wheels or reduced lottery systems. Get also original lotto wheels for lotto games drawing 5, 6, or 7 numbers: Balanced, randomized, and free.
-   [_**Create, Make Lotto Wheels in Lottery Wheeling Software or Manually**_](https://saliu.com/lottowheel.html).
-   [_**Myth of Lotto Wheels, Abbreviated, Reduced Lottery Systems**_](https://saliu.com/bbs/messages/11.html).  
    Statistical analysis of real lottery data proves the negative effect of playing static (unbalanced or split) lotto wheels.
-   **Free** [_**Lottery Wheeling Software for Players of Lotto Wheels**_](https://saliu.com/bbs/messages/857.html).  
    Fill out lotto wheels with player's picks (numbers to play); presenting _FillWheel_, **LottoWheeler** lottery wheeling software.
-   _**<u>WHEEL-632</u> available as the**_ [_**Best <u>On-The-Fly Wheeling</u> Software**_](https://saliu.com/bbs/messages/wheel.html) — applies real lottery filtering.
-   [_**Software to Verify Lotto Wheels**_](https://saliu.com/check-wheels.html) _for missing combinations and generate lotto 5, 6 abbreviated (reduced) systems._
-   [_**Copyright Lottery Numbers, Combinations, Lotto Wheels, Mathematical Relations, Formulas, Equations**_](https://saliu.com/copyright.html).
-   [_**Balanced Lotto Wheels, Lexicographical Order Lottery Wheeling**_](https://saliu.com/bbs/messages/772.html).
-   [_**Check WHEEL System, Lotto Wheels Winners**_](https://saliu.com/bbs/messages/90.html).
-   [<u><b>Skip Systems, Strategy Lotto, Lottery Skips</b></u>](https://saliu.com/skip-strategy.html), _**Powerball, Mega Millions, Euromillions**_.
-   [_**Lotto, Lottery <u>Strategy in Reverse</u>**_](https://saliu.com/reverse-strategy.html) ~ The Fundamentals of the _**LIE Elimination**_ strategy.
-   [**Lottery Utility Software**](https://saliu.com/lottery-utility.html): _**Pick-3, 4 Lottery, Lotto-5, 6, Powerball, Mega Millions, Euromillions**_.
-   [_**Software to Generate Lotto Combinations with Favorite Lottery Numbers in Fixed Positions**_](https://saliu.com/favorite-lottery-numbers-positions.html).
-   [_**The Best Ever Lottery Strategy, Lotto Strategies**_](https://software.saliu.com/lottery-strategy-lotto-strategies.html)

![Lottery wheeling master teaches how to create best positional lotto wheel from in-position systems.](https://saliu.com/HLINE.gif)

**[Home](https://saliu.com/index.htm) | [Search](https://saliu.com/Search.htm) | [New Writings](https://saliu.com/bbs/index.html) | [Fundamental Formula](https://saliu.com/formula.htm) | [Odds, Generator](https://saliu.com/calculator_generator.html) | [Contents](https://saliu.com/content/index.html) | [Forums](https://forums.saliu.com/) | [Sitemap](https://saliu.com/sitemap/index.html)**

![You need a little patience if you want to generate positional lottery wheels with Ion Saliu software.](https://saliu.com/HLINE.gif)
