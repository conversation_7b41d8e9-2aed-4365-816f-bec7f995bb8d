---
created: 2025-07-24T21:26:40 (UTC +08:00)
tags: [software,system,systems,lotto,lottery,numbers,sets,frequency,analysis,straight,pick-3,pick 4,patterns,odd,even,low,high,]
source: https://saliu.com/frequency-lottery.html
author: 
---

# Lottery Strategy Software Number Frequency Statistics

> ## Excerpt
> Run special lottery software to create powerful lottery systems based on number frequency, lotto decades, skips, low, high, odd, even.

---
First capture by the _WayBack Machine_ (_web.archive.org_) January 8, 2007.

<big><u>FrequencyRank</u></big>, version 4.1

-   lottery and gambling software to generate frequency reports two ways:  
    \- regardless of position;  
    \- position by position.

<big><u>SkipSystem</u></big>, version 8.0

-   lottery and gambling software to create lottery/gambling systems based on two or three consecutive skips;  
    \- the program also generates the unique combinations from each system, including by positions.

Greatly **upgraded** software for strategies, systems based on _**skips, decades, last digits, frequency groups**_ ~ version **5.0**, May 2016:

-   **SkipDecaFreq5** - software for 5-number lotto games;
-   **SkipDecaFreq6** - software for 6-number lottos;
-   **SkipDecaFreq3** - software for 3-digit (pick 3) lottery;
-   **SkipDecaFreq4** - software for 4-digit (pick 4) lotteries;
-   **SkipDecaFreqH3** - software for horse racing trifectas.
-   Please be advised that some of these programs, released in 2015, require a special form of _membership to download software_: _**Ultimate Lottery Software**_. Click on the top banner to learn the terms, conditions, and availability. All my software is always announced in the **Forums**, **New Writings**, **Software Downloads**. The presentation pages are always published first. Such Web resources are free to read and help the reader make a decision regarding the licensing of software.
-   The _Decades, Last Digits_ program runs from the second menu of the respective _**Ultimate Software**_ package, function _S = Skips, Decades, Frequencies, Last Digits_.

[![Lottery Software: Jackpot Lotto, Pick Lotteries, Powerball, Mega Millions, Euromillions, Keno.](https://saliu.com/ScreenImgs/lotto-decades-skips.gif)](https://saliu.com/free-lotto-lottery.html)

### This new strategy is about lottery numbers divided into three categories based on frequency.

Let's create here a lotto strategy that is going to be pirated big-time! It happened to those eBay pick-3 lottery systems that were blunt examples of _plagiarism of my theories_. To their credit, eBay fixed the problem after I filed a copyright infringement complaint. Read how users of my software came across the piracy acts while bidding on eBay:

-   [_**Piracy of Fundamental Formula of Gambling on eBay**_](https://forums.saliu.com/gambling-formula-pirated-ebay.html)
-   [_**Steve Players Piracy of Lottery Wonder Grid Systems**_](https://forums.saliu.com/steve-player-lottery-piracy.html).

I take as an example a lotto 6/49 game. We divide the lotto numbers in three frequency categories as follows:

1) Group **1**, the most frequent: **1/8** (**12.5%**) of all numbers = 6 lotto numbers;  
2) Group **2**: **37.5%** of all numbers = 18 lotto numbers;  
3) Group **3**, the least frequent: **50%** of all numbers = 25 lotto numbers.  

The division has a good foundation: Fewer numbers with higher frequency lead to better playing efficiency. Here is an example that shows _favoritism_ towards the groups with the most frequent numbers. The game is pick-4 played in Pennsylvania Lottery. The figures in the _Drawings_ column refer to the actual drawing — the _pick-4 number drawn_ by the lottery agency (_evening_ only in my reports).

![Pick-4 frequency statistical report divides the digits in 3 groups: hot, mild, cold.](https://saliu.com/images/pick-4-lottery-strategy.gif)

We have two distinct frequency groups (_1-3-0_ and _2-2-0_), both generating the same amount of pick-4 straight sets: _216_. But the group with more digits in the most frequent group (_**2**\-2-0_) registered 24 hits. The group with only one digit in the primary frequency group (_1-3-0_) recorded only 16 hits. The _**2**\-2-0_ configuration shows a 50% advantage.

-   That is a wicked lottery strategy that mocked me several times. Last time, it happened in less than 2 months — April – May 2016 — or about 10,000 dollars. Latest it happened, May 27, 2016, it was due to an unexpected stormy afternoon that lasted the lottery drawing. The 216 raw pick-4 straight sets were reduced to _under 10_ by the safest of _**LIE elimination**_ settings. The strategy had a _skip median_ equal to 27 prior to the latest hit (currently 28). The plan was to play the strategy up until 40 drawings (to cover 2/3 of total hits).
-   It was not the end of it! If players in Pennsylvania Lottery paid attention — and played the strategy! — they made $5000 in a couple of days. This pick-4 strategy had 3 $5000 hits in 42 days in the April – June 2016 period.
    -   The end was still far away, diligently axiomatic Pennsylvanian fellow of mine! From June to September of the year of grace 2016, the exact same strategy hit 8 more times:
    -   16 | **15 1 29 12 4 7 30 10** | 28
    -   The hits are between the two | bars. _28_ represents the last hit (see the _Skips_ below). The first number in the string (_16_) is not completed (still running).
    -   Amount of drawings played: 108 + 8 = 116. At a maximum price of $20 per play, total cost amounted to $2,320.
    -   Total winnings: $5,000 \* 8 = $40,000. _ROI_ (return on investment): 1724%. <u>This must be the most efficient lottery strategy in history!</u>
    -   All that time, I wrote more software, I was very busy fixing SEO issues with my website, busy and angry solving the hacking and spoofing of my phone... (Not to mention the _Sword of Damocles_ hanging above my head forever it seems like... threats of withholdings due to abusive application of law. All my winnings paid by check are seized this year of grace 2016!)

<u>A few graphics (easily replicated by data in Pennsylvania Lottery)</u>

![The pick 4 lottery strategy based on frequency, stats has a good winning rate.](https://saliu.com/images/lottery-strategy-stats.gif)

\* The _pivot_ of the strategy:

![Every lottery strategy has a pivot: Main filter or restriction or condition.](https://saliu.com/images/lottery-strategy-pivot.gif)

\* LIE elimination rule obeyed: The settings from the very previous draw were not repeated. Just a few examples:

![A winning lottery strategy always applies the reverse as in the case of lotto decades.](https://saliu.com/images/lottery-strategy-decades.gif)

![The winning lottery strategy always applies the reverse as in the case of number skips.](https://saliu.com/images/lottery-strategy-skips.gif)

![This winning lottery strategy also applies the odd, even, low, high lottery numbers.](https://saliu.com/images/lottery-strategy-odd-even.gif)

Data with reports from other _<u>LotWon</u>_ applications is even more strongly _misaligned_ — the _bread-and-butter_ of _**LIE elimination**_ lottery strategies.

![Combining more lottery strategies and playing the aggregated result increase the profits.](https://saliu.com/HLINE.gif)

-   <u>The best way to apply this <b>super lottery strategy</b>:</u> Instead of playing the strategy one at a time (layer by layer), play the strategies for all 6 layers <u>at the same time</u>. I named the 6 strategies as _STR-FR4-1-220_ to _STR-FR4-6-220_. I added the _OUT_ extension to the strategy filename for the respective output file. <u>This <b>combined lottery strategy</b> hits around <i>1 in 6</i> drawings.</u>
-   Combine/concatenate the 6 output files in one (e.g. _BIG.FR4_). Do NOT purge the duplicates, as the strategies hit <u>simultaneously</u> sometimes (about 15 times). We slightly increase the cost, but we increase the winnings by thousands of dollars.
-   The combined lottery strategy hits around 170 times in 1000 real lottery draws, with 12-15 hits being common to 2 or more strategies.
-   Generate... generously _**LIE**_ files to further reduce the amount of tickets to play. Some things are almost automatic _**LIE Elimination**_ candidates. The _top half_ of lotto numbers / lottery digits do not hit the very next drawing. The _bottom half_ based on frequency fares even worse. The same about the skip systems: The _FFG-SYS skips_ system files do NOT hit the very next lottery drawing; the _FFG-SYS skips_ systems based on _over median_ fare even worse in the very next draw.
-   If you look at the _WS_ files, you notice that no groups of 5 filters hit exactly in the next drawing. The combinations generated by those filters ought to be added to the final _**LIE**_ file. All filter reports should be analyzed to find groups of filters that will not hit, combined, in the very next lottery drawing. How _**LIE methodology**_ beats the odds badly: [_**<u>LIE Elimination, Reversed Lottery Strategy</u> for Lotto Decades, Last Digits, Odd Even, Low High, Skips**_](https://saliu.com/lie-lotto-strategies-decades.html).
-   As I have always stated, especially on this website, there is <u>NO absolute certainty in the entire Universe</u>. That's an undeniable mathematical law (_**FFG**_). Therefore, we can experience a wrong _**LIE Elimination**_ result sometimes. Personally, I haven't been wrong more than 1% of the time. The _**degree of certainty DC**_ that a losing _**LIE**_ situation hits at the same time with a hit of the **combined lottery strategy** is around _1/6 \* 1/100 = 1/600_. It is adequately <u>safe</u> in my book.
-   The above information in this indented paragraph was based on data created on April 4, 2017. The aggregated pick-4 lottery strategy missed 6 drawings, then hit 2 times in the next 4 draws. I saved the output file of that day (_BIG.FR4_, always with 1296 straight sets in this strategy).
-   I waited 10 days and then checked for winners. The top 10 drawings in the data file acted as _"future"_ drawings.
-   The _BIG.FR4_ output file recorded 3 _straight_ winners (I disregard the _boxed_ wins as they have a worse _house edge_).
-   The aggregate strategy created 10 days earlier made a profit. Granted, it is out of question paying 1296 monetary units a day. It is necessary to always apply other filters, and especially the _**LIE Elimination**_ tactic. Even if wins are eliminated in the process, the much lower cost increases the profit.

Axiomatic one, you might want to read real-life examples of LIE-strategy files and strategies. The text files were created for the Pennsylvania 5/43 lotto game. You can also download them freely (right-click, then _Save link_).

-   [LieStrategies.txt](https://saliu.com/freeware/LieStrategies.txt)
-   [ST5-Any1-1-MX-1](https://saliu.com/freeware/ST5-Any1-1-MX-1)
-   [ST5-Any2-1-MX-1](https://saliu.com/freeware/ST5-Any2-1-MX-1)

![Pick 4 lottery strategies hit in the same drawings, multiplying total winnings by a lot of money.](https://saliu.com/HLINE.gif)

-   All [_**lotto strategies are based on filters**_](https://saliu.com/filters.html). A good lottery strategy based on number/digit frequency starts with the group _**2**\-2-0_ as the _pivot_. Apply other filters (that show _safe_ values for the next draw) at runtime. In other words, we **purge** the 216 combinations by the specific function in all Parpaluck's lottery software applications.

The 6 winning numbers in a lotto draw will have a distribution such as: _2-2-2_: 2 numbers from the top 6, 2 numbers from the middle frequency zone, 2 numbers from the least frequent group.

I created a table for a few examples of distribution. How many numbers in each of the three frequency groups; number of winners in each group; then, in the brackets, how many _CombosNations_ from that group; lastly, how many total lotto combinations by the distribution?

![Lottery strategies based on number frequency can be very effective only with Ion lotto software.](https://saliu.com/ScreenImgs/lottery-frequency.gif)

-   You start with the least frequent lottery group: 50%. Round up to the next largest integer: 20. Then, select the most frequent group: 1/8; round off to the next lowest integer. The rest of the numbers go to group 2.
-   Use the combination formula to calculate the number of combinations in each group.
-   group **1**: C(5, n1), where n1 is number of winners from the group. Eg C(5, 1) = 5; C(5, 2) = 10; C(5, 0) = 1.
-   group **2**: C(14, n2), where n2 is number of winners from the group. Eg C(14, 1) = 14; C(14, 2) = 91; C(14, 0) = 1.
-   group **3**: C(20, n3), where n3 is number of winners from the group. Eg C(20, 1) = 20; C(20, 2) = 190; C(20, 0) = 1.
-   _Total combinations is the product of number of combinations in each group._ For example, the case 1-2-2 gives 5 \* 91 \* 190 = 86,450 total combinations. The 0-0-5 case generates C(20, 5) = 15,504 lotto combos.
-   You can **choose randomly** from all those combinations, or **purge** the output file.

Another point is the _parpaluck_ (_range of analysis_ or _how many lottery draws to analyze_). You chose N \* 2 (78 past draws). The degree of certainty for N = 78 is DC = 99.99%. That means that 99.99% of the lotto 5/39 numbers will come out in 78 drawings. I would try lower parpalucks, like N (39 drawings) or N/2 (20). The parpaluck is higher for pairs, because the probability is lower.

Calculating the number of combinations should be the easiest part. I said previously _"to apply the combination formula"_. It is hard to apply a formula manually. There are hand-held calculators with a function that calculates the combinations _C(N, M)_; e.g. C(49, 6). My calculator (Sharp EL-531D) has a button labelled _nCr_. You type 49, then press _nCr_, then 6, and finally _nCr_.

My lottery software also calculates the number of combinations with ease. The easiest one is **OddsCalc**. It can be downloaded from my downloads site, software category _**5.6 Scientific software: Mathematics, statistics, probability, combinatorics, odds, algorithms**_. **Odds Calc** calculates all the odds in the game; e.g. from 0 of 5 to 5 of 5. The _5 of 5_ is the only one needed in the case of this strategy.

Also, **MDIEditor Lotto WE** calculates the number of combinations as C(39, 5). Steps in menu: _Lotto, Lotto Odds_. Type, for example 18, then 5; or 20, 3. The program will display total number of combinations for those two parameters. It will also generate lottery combinations for that format. Just type 1 for _number of combinations to generate_.

By the way, I think the best distribution for pick3 or pick-4 should be:

-   **2-3-5**
    
    The best distribution for single-zero roulette should be:
    
-   **4-14-19**
    
    The best distribution for double-zero roulette should be:
    
-   **4-15-19**
    
    I calculated total combinations for various frequency cases. I compiled the calculations in tables: [_**Frequency groups pick-3, pick-4 lotteries, lotto 5/39, 649 lotto**_](https://saliu.com/frequency-tables.html).
    
    You can see real-life reports for Pennsylvania Lottery Cash 5 game (lotto _5 of 39_). The reports show the influence of the _parpaluck_ in the appearance of the lotto numbers based on frequency. Read: [_**Statistical Frequency Reports for Pennsylvania 5/39 Lotto**_](https://saliu.com/frequency-reports.html).
    
    ![Rank lottery numbers by frequency from hot to cold for Powerball, Mega Millions, Euromillions.](https://saliu.com/ScreenImgs/frequency-lottery.gif)
    
    **FrequencyRank** has a special feature. The program lists the lottery numbers by frequency: From the most frequent number (_hot_) to the least drawn (_cold_). This is the very traditional method of utilizing _lotto strategies_. The numbers are divided in two or three categories: _Hot, mild, cold_. Most lottery "experts" strongly recommend to play mainly hot numbers; or mostly hot numbers with a few _milders_ mixed in.
    
    The traditional lottery strategy based on number frequency is not nearly as potent as our new strategy created above.
    
    Still, my lottery software **Frequency Rank** has a pretty good following. Not to mention that its workings are used illegitimately by several Webmasters. I also offer paid-for Web pages with statistical reports created by **FrequencyRank** for Powerball, Mega Millions, and Euromillions.
    
    In truth, **Frequency Rank** offers the best options to create text files to represent lottery systems based on the traditional frequency. The user can copy and paste strings of numbers generated by the program and pasted in a text editor (_Notepad_ will do). The lottery frequency systems can be: _regardless of position_ and _position by position_.
    
    _Non-positional_ frequency systems consist of 1 line (regular lotto), or 2 lines (Powerball, Mega Millions, Euromillions):
    
    4 48 13 39 42 28 33 19 10 16 35 2 49 43 46 7 37 41 5 36 32 21 24 26 27 18 31 17 25 8 6 20 29 11 38 9 15 44 40 1 3 12 14 45 22 34 23 30 47 (_6/49_ lotto)
    
    In this case, the 6 _most frequent_ (_hottest_) lottery numbers, _regardless of position_, were: _4 48 13 39 42 28_. The 6 _coldest_ numbers were: _47 30 23 34 22 45_.
    
    _Positional_ frequency systems consist of multiple lines, one for each number position in the game (e.g. 6 in 6-number lottos or Powerball; 7 in 7-number lotto or Euromillions).
    
    All you have to do, l'axiomatique, is copy as many lotto numbers as you want (10, 12, 18, etc.) and paste them to _Notepad_. Save to a text file with a mnemonic filename. Be sure not to leave any empty lines in the file!
    
    ![Lottery software generates combinations from systems, strategies based on skips of numbers.](https://saliu.com/ScreenImgs/lottery-systems-skips.gif)
    
    So, you have ready a system file based on lottery frequencies. The next step is to generate lotto combinations from all those numbers you saved to disk. There is no better software than the very program you see above: **Skip System**. First of all, the comprehensive program creates even better systems than those based on traditional frequency methods. But that's not all. **Skip System** has special functions that generate the most precise lotto and lottery combinations, including from _positional_ systems (multi-line files). Read: [_**Skip Systems, Software for Lotto, Lottery, Powerball, Mega Millions, Euromillions**_](https://saliu.com/skip-strategy.html).
    
    ![Lottery players use statistics such as frequency to reduce the amount of lotto combinations to play.](https://saliu.com/HLINE.gif)
    
    [
    
    ## <u>Resources in Lottery Software, Strategies, Lotto Systems</u>
    
    ](https://saliu.com/content/lottery.html)
    
    -   [_**<u>MDIEditor Lotto WE</u>: Tutorial, Software User Guide, Manual, Book, ebook**_](https://saliu.com/MDI-lotto-guide.html).  
        ~ Also applicable to my entire lottery software; plus Powerball, Mega Millions, Euromillions.
    -   [_**Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps, Programs**_](https://saliu.com/forum/lotto-book.html).
    -   [_**<u>Lottery Mathematics, Lotto Mathematics</u>**_](https://saliu.com/gambling-lottery-lotto/lottery-math.htm)_**, Probabilities, Appearance, Repeat, Number Affiliation, Wheels, Systems, Strategies**_.
    -   [_**Lotto, Lottery <u>Strategy in Reverse</u>: Not-to-Win Leads to Not-to-Lose or WIN**_](https://saliu.com/reverse-strategy.html).
    -   [_**<u>LIE Elimination</u>: Lottery, Lotto Strategy in Reverse for <u>Pairs, Number Frequency</u>**_](https://saliu.com/lie-lottery-strategies-pairs.html).
    -   [_**<u>LIE Elimination, Reversed Lottery Strategy</u> for Lotto Decades, Last Digits, Odd Even, Low High, Skips**_](https://saliu.com/lie-lotto-strategies-decades.html).
    -   [_**Lottery Utility Software**_](https://saliu.com/lottery-utility.html)_**: Pick-3, 4 Lottery, Lotto-5, 6, Powerball, Mega Millions, Euromillions**_.
    -   [_**Software to Generate Lotto Combinations with Favorite Lottery Numbers in Fixed Positions**_](https://saliu.com/favorite-lottery-numbers-positions.html).
    -   [<u><b>Skip Systems, Strategy Lotto, Lottery Skips</b></u>](https://saliu.com/skip-strategy.html), _**Powerball, Mega Millions, Euromillions**_. Systems.
    -   [_**Cross-Reference, Combine Lottery Strategy Files Created by Various Types of Lottery Software**_](https://saliu.com/cross-lines.html).
    -   _"The Start Is the Hardest Part"_ in [_**<u>Lottery Strategies</u>**_](https://forums.saliu.com/lottery-strategies-start.html).
    -   [_**Strategy Lotto Software on Positional Frequency**_](https://forums.saliu.com/lotto-software-position-frequency.html).
    -   Download [**<u>Lottery Software, Lotto Programs</u>**](https://saliu.com/infodown.html).
    
    ![Frequency Rank is unique lottery software to analyze the drawings in many lotto games in the world.](https://saliu.com/HLINE.gif)
    
    **[Home](https://saliu.com/index.htm) | [Search](https://saliu.com/Search.htm) | [New Writings](https://saliu.com/bbs/index.html) | [Fundamental Formula](https://saliu.com/formula.htm) | [Odds, Generator](https://saliu.com/calculator_generator.html) | [Contents](https://saliu.com/content/index.html) | [Forums](https://forums.saliu.com/) | [Sitemap](https://saliu.com/sitemap/index.html)**
    
    ![Download software for lottery strategy, systems based on lotto number frequency.](https://saliu.com/HLINE.gif)
