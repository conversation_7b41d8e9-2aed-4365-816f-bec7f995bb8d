---
created: 2025-07-24T21:26:41 (UTC +08:00)
tags: [<PERSON><PERSON><PERSON>,Mini<PERSON>Baccarat,Super Baccarat,<PERSON>,<PERSON>er,Tie,Push,cards,win,winning,play,winner,gambling,new casino game,systems,mathematics,probability,odds,software,streaks]
source: https://saliu.com/winning_bell.html
author: 
---

# Baccarat, Mini-Baccarat, Gambling Systems, Strategy Software

> ## Excerpt
> Software, systems, mathematics, probability, odds of the casino games Baccarat, Mini-Baccarat; proposition for a new casino game of Baccarat named Super Baccarat.

---
![<PERSON> created a new form of casino baccarat: Super baccarat; it resembles blackjack.](https://saliu.com/HLINE.gif)

### I. [Sing Along a Winning (Non-Devotional) Song!](https://saliu.com/winning_bell.html#Song)  
II. [Introduction to Casino Baccarat, Mini-Baccarat](https://saliu.com/winning_bell.html#Licensed)  
III. [Proposition for a New Casino Game: _Super Baccarat_](https://saliu.com/winning_bell.html#Game)  
IV. [<PERSON>'s Gambling Theory, Software Compared to Other Baccarat Gambling Methods](https://saliu.com/winning_bell.html#Gambling)  
V. [Baccarat Links and Resources](https://saliu.com/winning_bell.html#Baccarat)  
• Including color-coded charts for best learning the rules

![Study an introduction to Baccarat, Mini-Baccarat, Super Baccarat.](https://saliu.com/HLINE.gif)

## <u>I. Sing Along!</u>

First capture by the _WayBack Machine_ (_web.archive.org_) March 26, 2004.

_"I can't kneel, I can't pray  
The only thing about me is my winning play."  
(Ion Saliu, "Winning Play", "The Winner Who Wins the Winning" album; credits to the "Genesis" great rock band)_

This is a motivational get-ready-and-just-do-it mantra. I largely neglected playing the lottery and gambling. I was too involved in developing ideas, theories, and systems. I must be more involved in the practical side as well. Otherwise, creativity might not be sustainable for long.

There is an inspiring story to many gamblers. They have referred me to a book I've already recommended several times at this web site: _Scarne's New Complete Guide to Gambling_ by John Scarne. The point in case is the game of **baccarat**. Says John Scarne: _”I saw a well-groomed young Chinese woman run a $200 bet into a $250,000 profit in a week's play in a number of Las Vegas Strip casinos.”_ ("Chemin de Fer and Baccarat", page 460).

Pure luck can't turn $200 into $250,000, playing several days in several venues! Prayer ain't gonna do it, either. I received emails from Asians, either gamblers from Asia or American Asians. Their common trait in gambling is **pattern recognition**. Some asked me to calculate the probabilities of various patterns, like _PBBBPP_. It was one of the reasons that led to my writing a great piece of probability software: **Streaks**:

-   [_**Software, systems for gambling mathematics of streaks**_](https://saliu.com/occult-science-gambling.html#HonestGambling).
-   More on theory of patterns: [_**Probability of**_ **Multiple Successes in a Number of Trials**, _**Streaks, Gambling Patterns**_](https://saliu.com/bbs/messages/269.html).

![The Winner Who Wins to Win the Winning Baccarat, Mini-Baccarat.](https://saliu.com/HLINE.gif)

## <u>II. Introduction to Baccarat, Mini-Baccarat</u>

The following is a brief but sound presentation of the mathematics of the game of baccarat. The game of baccarat is very close to a fair game, as far as the house edge (percentage advantage) is concerned. It may be the fairest game in the casino, alongside poker. The casino may not care who the winner is. The casinos make a profit by charging the winning side a commission. In baccarat, the winning side is, statically mathematically, the **Banker**. If the gambler bets on **Banker** and wins, the casino gets a 5% commission.

You may want to print the following tables for your reference. They say the gambler never needs to learn the rules of baccarat, because the dealers know the game very well and apply the rules very accurately. But, just in case, you may want to apply the Reagan - Gorbachev approach to conflict and understanding: "Trust but verify!"

• Tens and figure cards are counted as 10. All totals above 10 or multiples of 10 are adjusted by deducting 10 or multiples of 10. For example, if the first two cards are Jack and 10, the total = 10 + 10 = 20 = 0. If the first two cards are King and 8, the total = 10 + 8 = 18 = 8 (the small natural; a show stopper). Mnemonically, only the last digit is significant. 17 is 7, 23 is 3, 29 is 9. The maximum possible is 30 (three 10-valued cards). The Italian origin of the game of baccarat describes it well: _Baccara_ means the _little berry_, hence a metaphorical expression of _zero_. Baccarat is not French in origin, although the French were the first to make it a casino game (_chemin de fer_ = railroad).

### <u>Player</u> always plays first

[![Casino Gambling Software: Blackjack, Roulette, Baccarat, Craps, Systems.](https://saliu.com/ScreenImgs/baccarat-player.gif)](https://saliu.com/free-casino-software.html)

### <u>Banker</u>

![Banker must follow strict and complex baccarat rules; baccarat player has simpler plays.](https://saliu.com/ScreenImgs/baccarat-banker.gif)

### <u>Baccarat Probability, Odds, House Edge</u>

![Baccarat probability, odds, house edge represent complicated calculations.](https://saliu.com/ScreenImgs/baccarat-odds.gif)

The degree of certainty is **99%** that either the **Player** or **Banker** will strike a win within **8** hands. The 99% degree of certainty for **Tie** is assured within **46** hands!

<big>•</big> I released in May 2005 a great baccarat software analyzer: **BAQKARAT**, followed by **Baccarat**. The probability programs are available from the software download site, software category 5.5. This is accurate probability and statistical analyzing software for thousands of baccarat hands from the perspective of a strategy player. The programs represent a great complement to the free casino strategy presented on the page: [_**Gambling Streaks, Martingale Betting, Blackjack, Baccarat**_](https://saliu.com/occult-science-gambling.html#GamblingSystem).

![Founder of gambling mathematics the exiting casino game of Super Baccarat.](https://saliu.com/HLINE.gif)

## <u>III. Proposition for a New Casino Game: <i>Super Baccarat</i> or <i>Super Baqkarat</i></u>

The majority of gamblers consider the game of baccarat to be boring. The game is unattractive because it is robotic. The player has only one choice: place a bet. I played for a while baccarat. I didn't like it at all. The players wait and wait and delay the game until they see how others bet. Some players don't even play for several rounds in a row. Some players change their bets depending on how others bet. I did that too. I noticed also that others changed their betting based on how I bet!

The key problem is the uniformity. The game is not personal; it doesn't have a personality. I therefore propose a change to the game of baccarat. Let's personalize the game and name it **Super Baccarat** or **Super Baqkarat**, or simply BAQKARAT.

John Scarne, the noted author of gambling and creator of casino games already tried. He named the game Scarney Baccarat. Apparently, that baccarat game was adopted by some casinos in Nevada, England, Turkey, France, Italy, former Yugoslavia, North Africa. I am not sure if Scarney Baccarat is still played today. I haven't seen a word about it lately. Scarney Baccarat was too complicated. It combined rules from various casino games, such as Chemin de Fer, Baccarat, Bank Craps, and Black Jack. Looking at the rules of Scarney Baccarat, I think it was way too complicated (_Scarne's New Complete Guide to Gambling_, page 480).

I only take one rule from Scarney Baccarat: Personalize the game. Every player is dealt cards like at the blackjack table. I also take one rule from the new game of Double Attack Blackjack. The first card in the round is Dealer's face card. Based on that card, the players may decide to place an additional bet, up to the amount of the original bet. Other than that, all current mini-baccarat rules apply.

1) The mini- baccarat table allows betting to a number of Players, from one to seven.  
2) A player can bet on Banker or Player; additionally, the player can place a bet on Tie (Push).  
3) The Banker bet will be the Dealer's hand.  
4) The Player bet will be the hand dealt to the respective player.  
5) The first card in the round is Dealer's face card. At that moment, the Dealer pauses. After each Player sees the first card, they may decide to increase their original bet. The additional betting may be from the minimum limit up to the amount of the original bet.  
6) The Dealer deals the cards to the Players, from left to right, one card at a time.  
7) The Dealer deals his/her second card; it should be a face down (hole) card, simply for the reason of more excitement.  
8) The Dealer deals a second card to each Player.  
9) Starting with Dealer's leftmost Player, the Dealer makes the decision of Player standing or drawing a third and final card. The decision follows exactly the current mini-baccarat rules for the Player bet.  
10) After every Player was served, the Dealer reveals her/his hole card. The Dealer makes the decision of Dealer standing or drawing a third and final card. The decision follows exactly the current mini-baccarat rules for the Banker bet.

The new Super Baccarat game will be a whole lot more attractive. People love personalization. We love the idea of uniqueness. I also have the best software to calculate the probabilities and odds exactly. The software is based on the very precise (and unique!) **PermuteCombine**. It is one of the most popular titles of my software library. They calculate now the odds for casino games somehow approximately. Generating millions of random possibilities can only approximately lead to the probability. Only when we know a probability formula beforehand, the random generation can be accurate. In such cases, the random generation simply confirms the formulas. The only difficulty regarding _**Permute Combine**_ is the speed of today's computers. One deck has 52 cards. The number of permutations of 52 is a number starting with 8 and consisting of 67 digits! Arrangements of 52 taken 6 at a time amount to 14,658,134,400 (that's 14 billion something). Arrangements of 52 taken 8 at a time makes 30,342,338,208,000 (that's 30 trillion something).

![Gambling in casino at Baccarat, Mini Baccarat, Super Baccarat.](https://saliu.com/HLINE.gif)

## <u>4. Ion Saliu's Gambling Theory, Software Compared to Other Baccarat Gambling Systems</u>

My gambling theory, including Baccarat, is founded on mathematics, specifically the Fundamental Formula of Gambling (FFG). Also, everything I set out in my software must be validated mathematically to the best of my knowledge.

In all honesty, I don't see anything out there that offers better ways to winning at gambling or baccarat. I wish there was something easier and more efficient than my approach. There is nothing, honestly. IF my casino gambling methodology doesn't win the — there is absolutely nothing to accomplish such daunting tasks. Never will be, if my research has been futile. Never mind that they will continue to put huge efforts in playing the stock market or predicting the weather. The latter two phenomena are equally random to baccarat gambling or casino gambling. They have some advantages, but casino gambling has its advantages as well. In the end, it's all about streaks and skips, no matter what the phenomenon is. The skips (misses) are shorter and the streaks are longer if the probability is higher; and vice versa.

The rest of the baccarat gambling theory is nothing more than **_counting cards_**. Card counting in baccarat has virtually NO mathematical validity. Such a waste of time!

So, right now, I cannot see any casino gambling approach better than mine. That's the only way, like my gambling theory and software do: Track the winning and the losing streaks and their corresponding skips (misses). There are some no playing moments (or play the minimum bet); other situations demand a higher bet. It is hard work, but nothing else works better.

In fact, the science of gambling is the science of the streaks. Theory of probability, in general, is the science of the streaks and skips. A statement such as "This event will always have the probability equal to zero point zero zero three four etc." is virtually meaningless.

The probability represents the ratio of the favorable cases over total possible cases. So, it works with integers. In real-life we deal with integers (discrete values) such as numbers of elements and numbers of trials. The events will hit, or miss, in streaks pretty clearly predicted by rules and formulas of probability theory. If you play longer sessions at the baccarat table, for example, you will face a higher probability of some very long losing streaks. But play shorter sessions and there is a far better chance that you'll escape with shorter losing streaks. It is recommended to play baccarat more aggressively at the beginning of short sessions.

For we must remember that there is NO such a thing as ABSOLUTE CERTAINTY in the entire Universe. That's way humans invented the gods and myths; humans badly need the comfort of absolute certainty.

The hardest thing for me now is fighting piracy. Looks like everything I create and publish is pirated or plagiarized soon thereafter. I put considerable effort, make the fruit of my effort available for free mostly — and a bunch of pirates harvest the profits for themselves! Read more: [_**Free Winning Software, Systems: True Gifts on eBay, NOT Piracy**_](https://download.saliu.com/eBay-scams.html). They take risky chances. The law never sleeps. A number of eBay pirates were punished already...

![Baccarat, casinos mini-baccarat: The best in mathematics, winning Martingale.](https://saliu.com/HLINE.gif)

![Ion Saliu's Theory of Probability Book founded on mathematical discoveries, including for baccarat.](https://saliu.com/probability-book-Saliu.jpg) [Read Ion Saliu's first book in print: **_Probability Theory, Live!_**](https://saliu.com/probability-book.html)  
~ Founded on valuable mathematical discoveries with a wide range of scientific applications, including probability theory applied to baccarat, gambling, software, real winning systems.

![Baccarat, Mini-Baccarat: Basic Strategy, Probability, Odds, Rules, Tables, Win.](https://saliu.com/HLINE.gif)

[

## <u>Blackjack, Baccarat: Software, Content, Resources, Systems, Basic Strategy</u>

](https://saliu.com/content/blackjack.html)See a comprehensive directory of the pages and materials on the subject of blackjack, baccarat, software, systems, basic strategy.

-   [_**The Best Strategy for Lottery, Gambling, Sports Betting, Horse Racing, Blackjack, Roulette, Baccarat, Craps**_](https://saliu.com/strategy-gambling-lottery.html).
-   [_**Demise of Gambler's Fallacy, Reversed Gambler's Fallacy, Streaks**_](https://saliu.com/gamblers-fallacy.html), including roulette.
-   [_**Winning at sports betting, soccer, football, parlay, bet systems, software**_](https://saliu.com/betting.html).
-   [_**Blackjack, Basic Strategy, Card Counting**_](https://saliu.com/blackjack.html); Double Down, Hit, Stand, Split Pairs; Charts & Tables; Probability, Odds, Rules.
-   [_**Roulette basics, betting, systems**_](https://saliu.com/roulette.html), methods, gambling strategies.
-   [_**Win at horse racing**_](https://saliu.com/horses.html) as pick-3, pick 4, digit lottery with the best free winning systems and software.
-   [_**Blackjack Basic Strategy: The Best Cards, Charts, Learning**_](https://saliu.com/bbs/messages/399.html).
-   [_**The Best Blackjack Strategy, System Tested with Unbiased, Fair Online Software App**_](https://saliu.com/blackjack-strategy-system-win.html).
    
    Of interest:
    
-   [_**Mathematics of Fundamental Formula of Gambling**_](https://saliu.com/formula.htm):  
    ~ The mathematical proof of the inexistence of God or the absurdity uvda God concept. Don't pray, just play... mathematically!  
    
-   _**Download Baccarat Gambling**_ [**Software**](https://saliu.com/infodown.html).

![Winner who wins at baccarat or baccara is Italian for berry or zero.](https://saliu.com/HLINE.gif)

**[Home](https://saliu.com/index.htm) | [Search](https://saliu.com/Search.htm) | [New Writings](https://saliu.com/bbs/index.html) | [Fundamental Formula](https://saliu.com/formula.htm) | [Odds, Generator](https://saliu.com/calculator_generator.html) | [Contents](https://saliu.com/content/index.html) | [Forums](https://forums.saliu.com/) | [Sitemap](https://saliu.com/sitemap/index.html)**

![Exit site of gambling, baccarat, software, systems, mathematics.](https://saliu.com/images/HLINE.gif)
