# File management functions

using CSV
using DataFrames
using Dates

"""
FileManager for handling lottery data files
"""
struct FileManager
    data_directory::String
    max_file_size::Int
    
    FileManager(data_dir::String = "data") = new(data_dir, 100_000_000)  # 100MB default
end

"""
Read DATA5 file and return lottery draws
Supports both date-prefixed and numbers-only formats
"""
function read_data5_file(fm::FileManager, filepath::String)::Vector{LotteryDraw}
    draws = LotteryDraw[]
    
    try
        # Check file size
        file_size = filesize(filepath)
        if file_size > fm.max_file_size
            @warn "File size ($file_size bytes) exceeds maximum allowed size ($(fm.max_file_size) bytes)"
        end
        
        df = CSV.read(filepath, DataFrame, header=false)
        
        for (i, row) in enumerate(eachrow(df))
            try
                # Determine if first column is date or number
                first_col = string(row[1])
                
                if occursin("-", first_col) && length(split(first_col, "-")) == 3
                    # Date format: date,n1,n2,n3,n4,n5
                    draw_date = Date(first_col)
                    numbers = [Int(row[j]) for j in 2:6]
                else
                    # Numbers only format: n1,n2,n3,n4,n5
                    draw_date = Date(2022, 1, 1) + Day(i-1)  # Default date sequence
                    numbers = [Int(row[j]) for j in 1:5]
                end
                
                # Validate numbers
                if length(numbers) != 5 || any(n -> n < 1 || n > 39, numbers) || length(unique(numbers)) != 5
                    @warn "Invalid numbers at line $i: $numbers"
                    continue
                end
                
                # Create lottery draw
                draw = LotteryDraw(numbers, draw_date, i)
                push!(draws, draw)
                
            catch e
                @warn "Error parsing line $i: $e"
                continue
            end
        end
        
        # Sort by date (newest first for chronological order)
        sort!(draws, by = d -> d.draw_date, rev = true)
        
        return draws
    catch e
        error("Failed to read DATA5 file $filepath: $e")
    end
end

"""
Write lottery draws to DATA5 file
"""
function write_data5_file(fm::FileManager, filepath::String, draws::Vector{LotteryDraw})
    try
        open(filepath, "w") do file
            for draw in draws
                date_str = Dates.format(draw.draw_date, "yyyy-mm-dd")
                numbers_str = join(draw.numbers, ",")
                println(file, "$date_str,$numbers_str")
            end
        end
    catch e
        error("Failed to write DATA5 file $filepath: $e")
    end
end

"""
Merge real and simulated data files
"""
function merge_data_files(fm::FileManager, real_file::String, sim_file::String)::String
    real_draws = read_data5_file(fm, real_file)
    sim_draws = read_data5_file(fm, sim_file)
    
    # Combine and sort
    all_draws = vcat(real_draws, sim_draws)
    sort!(all_draws, by = d -> d.draw_date, rev = true)
    
    # Create merged filename
    merged_file = joinpath(fm.data_directory, "merged_d5.csv")
    write_data5_file(fm, merged_file, all_draws)
    
    return merged_file
end

"""
Read raw lottery data (Vector{Vector{Int}}) from DATA5 file
For use with analysis functions that don't need date information
"""
function read_data5_raw(fm::FileManager, filepath::String)::Vector{Vector{Int}}
    draws_with_dates = read_data5_file(fm, filepath)
    return [draw.numbers for draw in draws_with_dates]
end

"""
Write raw lottery data to DATA5 file
"""
function write_data5_file(fm::FileManager, filepath::String, draws::Vector{Vector{Int}})
    try
        # Ensure directory exists
        dir = dirname(filepath)
        if !isempty(dir) && !isdir(dir)
            mkpath(dir)
        end
        
        open(filepath, "w") do file
            for (i, draw) in enumerate(draws)
                # Generate sequential dates for raw data
                date_str = Dates.format(Date(2022, 1, 1) + Day(i-1), "yyyy-mm-dd")
                numbers_str = join(draw, ",")
                println(file, "$date_str,$numbers_str")
            end
        end
    catch e
        error("Failed to write DATA5 file $filepath: $e")
    end
end

"""
Read SIM-5 file (simulated data format)
"""
function read_sim5_file(fm::FileManager, filepath::String)::Vector{Vector{Int}}
    return read_data5_raw(fm, filepath)
end

"""
Write SIM-5 file (simulated data format)
"""
function write_sim5_file(fm::FileManager, filepath::String, draws::Vector{Vector{Int}})
    write_data5_file(fm, filepath, draws)
end

"""
Read D5 file (combined real and simulated data)
"""
function read_d5_file(fm::FileManager, filepath::String)::Vector{Vector{Int}}
    return read_data5_raw(fm, filepath)
end

"""
Write D5 file (combined real and simulated data)
"""
function write_d5_file(fm::FileManager, filepath::String, draws::Vector{Vector{Int}})
    write_data5_file(fm, filepath, draws)
end

"""
Check if file exists and is readable
"""
function check_file_accessibility(fm::FileManager, filepath::String)::ValidationResult
    if !isfile(filepath)
        return ValidationResult(false, "File does not exist: $filepath")
    end
    
    if !isreadable(filepath)
        return ValidationResult(false, "File is not readable: $filepath")
    end
    
    file_size = filesize(filepath)
    if file_size == 0
        return ValidationResult(false, "File is empty: $filepath")
    end
    
    if file_size > fm.max_file_size
        return ValidationResult(false, "File size ($file_size bytes) exceeds maximum allowed size ($(fm.max_file_size) bytes)")
    end
    
    return ValidationResult(true, "File is accessible and valid size ($file_size bytes)")
end

"""
Get file statistics and information
"""
function get_file_info(fm::FileManager, filepath::String)::Dict{String, Any}
    info = Dict{String, Any}()
    
    if !isfile(filepath)
        info["exists"] = false
        return info
    end
    
    info["exists"] = true
    info["size"] = filesize(filepath)
    info["readable"] = isreadable(filepath)
    info["writable"] = iswritable(filepath)
    
    try
        # Try to count lines
        line_count = 0
        open(filepath, "r") do file
            for _ in eachline(file)
                line_count += 1
            end
        end
        info["line_count"] = line_count
        
        # Try to read first few lines to detect format
        lines = String[]
        open(filepath, "r") do file
            for (i, line) in enumerate(eachline(file))
                push!(lines, line)
                if i >= 3  # Read first 3 lines
                    break
                end
            end
        end
        info["sample_lines"] = lines
        
        # Detect format
        if !isempty(lines)
            first_line = lines[1]
            parts = split(first_line, ",")
            
            if length(parts) >= 6 && occursin("-", parts[1])
                info["format"] = "DATA5_with_dates"
                info["columns"] = length(parts)
            elseif length(parts) == 5
                info["format"] = "DATA5_numbers_only"
                info["columns"] = length(parts)
            else
                info["format"] = "unknown"
                info["columns"] = length(parts)
            end
        end
        
    catch e
        info["error"] = string(e)
    end
    
    return info
end

"""
Backup file with timestamp
"""
function backup_file(fm::FileManager, filepath::String)::String
    if !isfile(filepath)
        error("Cannot backup non-existent file: $filepath")
    end
    
    # Create backup filename with timestamp
    dir = dirname(filepath)
    filename = basename(filepath)
    name, ext = splitext(filename)
    
    # Generate unique timestamp-based filename
    counter = 0
    while true
        timestamp = Dates.format(now(), "yyyymmdd_HHMMSS")
        if counter > 0
            timestamp *= "_$counter"
        end
        backup_filename = "$(name)_backup_$(timestamp)$(ext)"
        backup_path = joinpath(dir, backup_filename)
        
        if !isfile(backup_path)
            # Copy file
            cp(filepath, backup_path)
            return backup_path
        end
        
        counter += 1
        if counter > 100
            error("Could not create unique backup filename after 100 attempts")
        end
        
        # Small delay to ensure different timestamp
        sleep(0.01)
    end
end

"""
Clean up old backup files (keep only N most recent)
"""
function cleanup_backups(fm::FileManager, filepath::String, keep_count::Int = 5)
    dir = dirname(filepath)
    if isempty(dir)
        dir = "."
    end
    
    if !isdir(dir)
        return 0
    end
    
    filename = basename(filepath)
    name, ext = splitext(filename)
    
    # Find all backup files
    backup_pattern = Regex("$(name)_backup_\\d{8}_\\d{6}.*$(ext)")
    backup_files = String[]
    
    try
        for file in readdir(dir)
            if occursin(backup_pattern, file)
                push!(backup_files, joinpath(dir, file))
            end
        end
    catch e
        @warn "Error reading directory $dir: $e"
        return 0
    end
    
    # Sort by modification time (newest first)
    sort!(backup_files, by = f -> stat(f).mtime, rev = true)
    
    # Remove old backups
    removed_count = 0
    for i in (keep_count + 1):length(backup_files)
        try
            rm(backup_files[i])
            removed_count += 1
        catch e
            @warn "Could not remove backup file $(backup_files[i]): $e"
        end
    end
    
    return removed_count
end

