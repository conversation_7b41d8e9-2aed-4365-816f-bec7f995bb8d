# THREE Filter Tests
# THREE 過濾器測試

using Test
using Dates

# 引入必要的模組
include("../src/types.jl")
include("../src/filter_engine.jl")
include("../src/filters/three_filter.jl")

"""
創建測試用的彩票數據
"""
function create_test_lottery_data_for_triplets()::Vector{LotteryDraw}
    test_draws = [
        LotteryDraw([1, 2, 3, 15, 20], Date(2022, 1, 10), 10),  # 最新，包含三號組合 (1,2,3)
        LotteryDraw([4, 6, 11, 16, 21], Date(2022, 1, 9), 9),
        LotteryDraw([1, 2, 12, 17, 22], Date(2022, 1, 8), 8),   # 包含配對 (1,2)
        LotteryDraw([5, 8, 13, 18, 23], Date(2022, 1, 7), 7),
        LotteryDraw([1, 9, 14, 19, 24], Date(2022, 1, 6), 6),
        LotteryDraw([1, 2, 3, 20, 25], Date(2022, 1, 5), 5),   # 包含三號組合 (1,2,3)
        LotteryDraw([7, 10, 16, 21, 26], Date(2022, 1, 4), 4),
        LotteryDraw([6, 12, 17, 22, 27], Date(2022, 1, 3), 3),
        LotteryDraw([1, 4, 5, 23, 28], Date(2022, 1, 2), 2),   # 包含三號組合 (1,4,5)
        LotteryDraw([8, 14, 19, 24, 29], Date(2022, 1, 1), 1),  # 最舊
    ]
    return test_draws
end

"""
測試三號組合生成功能
"""
function test_triplet_generation()
    @testset "Triplet Generation" begin
        # 測試正常情況
        numbers = [1, 2, 3, 4]
        triplets = generate_triplets(numbers)
        expected_count = binomial(4, 3)  # C(4,3) = 4
        @test length(triplets) == expected_count
        
        # 驗證具體的三號組合
        expected_triplets = [(1, 2, 3), (1, 2, 4), (1, 3, 4), (2, 3, 4)]
        @test all(triplet in expected_triplets for triplet in triplets)
        
        # 測試邊界條件
        @test isempty(generate_triplets([1, 2]))  # 只有兩個號碼
        @test isempty(generate_triplets([1]))     # 只有一個號碼
        @test isempty(generate_triplets(Int[]))   # 空數組
        
        # 測試三個號碼
        three_numbers = [5, 2, 8]
        three_triplets = generate_triplets(three_numbers)
        @test length(three_triplets) == 1
        @test three_triplets[1] == (5, 2, 8)  # 應該保持原順序
        
        println("✓ 三號組合生成測試通過")
    end
end

"""
測試三號組合頻率計算
"""
function test_triplet_frequency_calculation()
    @testset "Triplet Frequency Calculation" begin
        test_data = create_test_lottery_data_for_triplets()
        engine = FilterEngine(test_data)
        
        # 測試三號組合 (1,2,3) 的頻率 - 應該出現 2 次
        freq_1_2_3 = calculate_triplet_frequency(engine, (1, 2, 3))
        @test freq_1_2_3 == 2
        
        # 測試三號組合 (1,4,5) 的頻率 - 應該出現 1 次
        freq_1_4_5 = calculate_triplet_frequency(engine, (1, 4, 5))
        @test freq_1_4_5 == 1
        
        # 測試不存在的三號組合
        freq_never = calculate_triplet_frequency(engine, (30, 35, 39))
        @test freq_never == 0
        
        println("✓ 三號組合頻率計算測試通過")
    end
end

"""
測試三號組合 Skip 計算
"""
function test_triplet_skip_calculation()
    @testset "Triplet Skip Calculation" begin
        test_data = create_test_lottery_data_for_triplets()
        engine = FilterEngine(test_data)
        
        # 測試三號組合 (1,2,3) 的 Skip - 最後在索引 1 出現（最新）
        skip_1_2_3 = calculate_triplet_skip(engine, (1, 2, 3))
        @test skip_1_2_3 == 0  # 最新開獎就有出現
        
        # 測試三號組合 (1,4,5) 的 Skip - 最後在索引 9 出現
        skip_1_4_5 = calculate_triplet_skip(engine, (1, 4, 5))
        @test skip_1_4_5 == 8  # 索引 9 - 1 = 8
        
        # 測試從未出現的三號組合
        skip_never = calculate_triplet_skip(engine, (30, 35, 39))
        @test skip_never == length(test_data)
        
        println("✓ 三號組合 Skip 計算測試通過")
    end
end

"""
測試歷史三號組合分佈
"""
function test_historical_triplet_distribution()
    @testset "Historical Triplet Distribution" begin
        test_data = create_test_lottery_data_for_triplets()
        engine = FilterEngine(test_data)
        
        # 獲取歷史分佈
        distribution = calculate_historical_triplet_distribution(engine)
        
        # 驗證分佈長度
        @test length(distribution) == length(test_data)
        
        # 每次開獎應該有 C(5,3) = 10 個三號組合
        expected_count = binomial(5, 3)
        @test all(count == expected_count for count in distribution)
        
        println("✓ 歷史三號組合分佈測試通過")
    end
end

"""
測試期望三號組合數量計算
"""
function test_expected_triplet_count()
    @testset "Expected Triplet Count" begin
        test_data = create_test_lottery_data_for_triplets()
        engine = FilterEngine(test_data)
        
        # 測試不同數量號碼的期望三號組合數
        @test calculate_expected_triplet_count(engine, 2) == 0.0  # 2個號碼無三號組合
        @test calculate_expected_triplet_count(engine, 1) == 0.0  # 1個號碼無三號組合
        
        expected_3 = calculate_expected_triplet_count(engine, 3)
        @test expected_3 > 0.0  # 3個號碼有期望三號組合
        
        expected_5 = calculate_expected_triplet_count(engine, 5)
        @test expected_5 > expected_3  # 更多號碼有更多期望三號組合
        
        println("✓ 期望三號組合數量計算測試通過")
    end
end

"""
測試 THREE 過濾器計算
"""
function test_three_filter_calculation()
    @testset "THREE Filter Calculation" begin
        test_data = create_test_lottery_data_for_triplets()
        engine = FilterEngine(test_data)
        
        # 測試包含多個三號組合的號碼組合
        test_numbers = [1, 2, 3, 4]
        result = calculate_three_filter(engine, test_numbers)
        
        # 驗證結果結構
        @test result.filter_type == THREE_FILTER
        @test occursin("THREE_FILTER", result.filter_name)
        @test isa(result.current_value, Int)
        @test isa(result.expected_value, Float64)
        @test isa(result.is_favorable, Bool)
        @test 0.0 <= result.confidence_level <= 1.0
        @test isa(result.historical_values, Vector{Int})
        @test result.calculation_time >= 0.0
        
        # 驗證三號組合數量 - C(4,3) = 4
        @test result.current_value == 4
        
        println("✓ THREE 過濾器計算測試通過")
        println("  - 當前三號組合數: $(result.current_value)")
        println("  - 期望三號組合數: $(round(result.expected_value, digits=4))")
        println("  - 是否有利: $(result.is_favorable)")
        println("  - 信心水準: $(round(result.confidence_level, digits=2))")
    end
end

"""
測試特定三號組合分析
"""
function test_specific_triplet_analysis()
    @testset "Specific Triplet Analysis" begin
        test_data = create_test_lottery_data_for_triplets()
        engine = FilterEngine(test_data)
        
        # 分析三號組合 (1,2,3)
        analysis = analyze_specific_triplet(engine, (1, 2, 3))
        
        # 驗證分析結果結構
        @test haskey(analysis, "triplet")
        @test haskey(analysis, "frequency")
        @test haskey(analysis, "skip")
        @test haskey(analysis, "expected_frequency")
        @test haskey(analysis, "is_above_average")
        @test haskey(analysis, "rarity_score")
        
        @test analysis["triplet"] == (1, 2, 3)
        @test analysis["frequency"] == 2
        @test analysis["skip"] == 0
        
        println("✓ 特定三號組合分析測試通過")
    end
end

"""
測試最稀有三號組合識別
"""
function test_rarest_triplets()
    @testset "Rarest Triplets" begin
        test_data = create_test_lottery_data_for_triplets()
        engine = FilterEngine(test_data)
        
        # 獲取號碼 [1,2,3,4] 中最稀有的三號組合
        test_numbers = [1, 2, 3, 4]
        rarest = get_rarest_triplets(engine, test_numbers, 3)
        
        @test length(rarest) == 3  # 應該有 3 個結果（要求前3個）
        @test all(haskey(triplet, "rarity_score") for triplet in rarest)
        
        # 驗證排序（稀有度分數應該遞減）
        for i in 2:length(rarest)
            @test rarest[i-1]["rarity_score"] >= rarest[i]["rarity_score"]
        end
        
        println("✓ 最稀有三號組合識別測試通過")
    end
end

"""
測試 THREE 過濾器統計摘要
"""
function test_three_filter_summary()
    @testset "THREE Filter Summary" begin
        test_data = create_test_lottery_data_for_triplets()
        engine = FilterEngine(test_data)
        
        # 計算多個號碼組合的 THREE 過濾器結果
        test_combinations = [
            [1, 2, 3],
            [1, 2, 3, 4],
            [1, 2, 3, 4, 5],
            [10, 11, 12]
        ]
        
        results = FilterResult[]
        for combo in test_combinations
            result = calculate_three_filter(engine, combo)
            push!(results, result)
        end
        
        # 獲取統計摘要
        summary = get_three_filter_summary(results)
        
        # 驗證摘要結構
        @test haskey(summary, "total_combinations")
        @test haskey(summary, "favorable_combinations")
        @test haskey(summary, "average_confidence")
        @test haskey(summary, "average_triplet_count")
        @test haskey(summary, "triplet_count_distribution")
        
        @test summary["total_combinations"] == length(test_combinations)
        
        println("✓ THREE 過濾器統計摘要測試通過")
        println("  - 總組合數: $(summary["total_combinations"])")
        println("  - 有利組合數: $(summary["favorable_combinations"])")
        println("  - 平均信心水準: $(round(summary["average_confidence"], digits=2))")
    end
end

"""
測試三號組合歷史表現檢查
"""
function test_triplet_historical_performance()
    @testset "Triplet Historical Performance" begin
        test_data = create_test_lottery_data_for_triplets()
        engine = FilterEngine(test_data)
        
        # 檢查三號組合 (1,2,3) 的歷史表現
        performance = check_triplet_historical_performance(engine, (1, 2, 3))
        
        # 驗證結果結構
        @test haskey(performance, "triplet")
        @test haskey(performance, "total_frequency")
        @test haskey(performance, "current_skip")
        @test haskey(performance, "occurrence_intervals")
        @test haskey(performance, "average_interval")
        @test haskey(performance, "is_overdue")
        
        @test performance["triplet"] == (1, 2, 3)
        @test performance["total_frequency"] == 2
        @test performance["current_skip"] == 0
        
        println("✓ 三號組合歷史表現檢查測試通過")
    end
end

"""
測試快取功能
"""
function test_three_filter_caching()
    @testset "THREE Filter Caching" begin
        test_data = create_test_lottery_data_for_triplets()
        engine = FilterEngine(test_data, cache_enabled=true)
        
        test_numbers = [1, 2, 3, 4]
        
        # 第一次計算
        result1 = calculate_three_filter(engine, test_numbers)
        cache_size_after_first = length(engine.filter_cache)
        
        # 第二次計算（應該使用快取）
        result2 = calculate_three_filter(engine, test_numbers)
        cache_size_after_second = length(engine.filter_cache)
        
        # 驗證快取工作
        @test cache_size_after_first >= 1
        @test cache_size_after_second == cache_size_after_first  # 快取大小不變
        @test result1.current_value == result2.current_value
        @test result1.expected_value == result2.expected_value
        
        println("✓ THREE 過濾器快取功能測試通過")
    end
end

"""
測試錯誤處理
"""
function test_three_filter_error_handling()
    @testset "THREE Filter Error Handling" begin
        test_data = create_test_lottery_data_for_triplets()
        engine = FilterEngine(test_data)
        
        # 測試號碼數量不足
        @test_throws ArgumentError calculate_three_filter(engine, [1, 2])
        @test_throws ArgumentError calculate_three_filter(engine, [1])
        @test_throws ArgumentError calculate_three_filter(engine, Int[])
        
        # 測試無效號碼範圍
        @test_throws ArgumentError calculate_three_filter(engine, [0, 1, 2])
        @test_throws ArgumentError calculate_three_filter(engine, [1, 2, 40])
        
        # 測試空數據
        empty_engine = FilterEngine(LotteryDraw[])
        @test_throws ArgumentError calculate_three_filter(empty_engine, [1, 2, 3])
        
        println("✓ THREE 過濾器錯誤處理測試通過")
    end
end

"""
執行所有 THREE 過濾器測試
"""
function run_three_filter_tests()
    println("🧪 開始執行 THREE 過濾器測試...")
    
    try
        test_triplet_generation()
        test_triplet_frequency_calculation()
        test_triplet_skip_calculation()
        test_historical_triplet_distribution()
        test_expected_triplet_count()
        test_three_filter_calculation()
        test_specific_triplet_analysis()
        test_rarest_triplets()
        test_three_filter_summary()
        test_triplet_historical_performance()
        test_three_filter_caching()
        test_three_filter_error_handling()
        
        println("\n🎉 所有 THREE 過濾器測試通過！")
        return true
    catch e
        println("\n❌ THREE 過濾器測試失敗: $e")
        return false
    end
end

# 如果直接執行此文件，運行測試
if abspath(PROGRAM_FILE) == @__FILE__
    run_three_filter_tests()
end
