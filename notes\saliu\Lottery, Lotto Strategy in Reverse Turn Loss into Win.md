---
created: 2025-07-24T21:26:40 (UTC +08:00)
tags: [strategy,reverse strategy,strategies,lottery,lotto,hit,miss,win,lose,drawing,file,software,LIE files,]
source: https://saliu.com/reverse-strategy.html
author: 
---

# Lottery, Lotto Strategy in Reverse: Turn Loss into Win

> ## Excerpt
> Lottery missing is more frequent than hitting in lotto. We reverse the strategy: Intentionally set lotto or lottery filters that will not win next drawing, thus making a profit from losing.

---
![Reversed lottery systems, strategies aka LIE elimination are the creation of <PERSON> in the 1990s.](https://saliu.com/images/lottery.gif)

### I. [A Brief History of Lottery Strategies in _Reverse_](https://saliu.com/reverse-strategy.html#history)  
II. [Reversed Lotto Strategy _Redivivus_](https://saliu.com/reverse-strategy.html#strategy)  
III. [Reversed Lotto Strategy Example: Eliminate Pairings, Triples, Quads](https://saliu.com/reverse-strategy.html#reverse)  
IV. [Reversed Lottery Strategy Example: Skips, Decades, Frequencies](https://saliu.com/reverse-strategy.html#skips)  
V. [A Lottery Strategy is _Trinity_: _Straight_, _Purge_, _Reversed_](https://saliu.com/reverse-strategy.html#trinity)  
VI. [Resources in Lottery Lotto Software, Strategies, Systems](https://saliu.com/reverse-strategy.html#links)

![In lie or NOT elimination lottery strategy we rely on having non-winners in the first output.](https://saliu.com/images/lotto.gif)

## <u>I. A Brief History of Lottery Strategies in <i>Reverse</i></u>

First captured by the _WayBack Machine_ (_web.archive.org_) on June 26, 2004.

I stumbled upon some old papers. The documents described old pick-3 lottery strategies. I remember I worked them out together with a good lottery collaborator in California. Funny, the output files were named LIE.3, LIE.4… Then, reminders to me on pieces of paper: “The filter TOT always purges LIE.3 pick lottery files. The lotto filter ANY always purges LIE files generated by ANY and SUM.”

The strategy was, in fact, a lottery strategy in reverse! I wanted to select lottery filters making sure I set them wrong! For example, I would set VR\_1 = 5 and VR\_2 = 5. The pick-3 strategy worked if VR\_1 was less than 5 and VR\_2 was less than 5 the very next draw.

The output generated by the two filters (a file I named LIE) would be added to the D3 file (on top). I would eliminate all the lotto combinations in that LIE file, because I _knew_ they wouldn't hit! I would select some 25-30 filters that way. On many occasions, I would be _wrong_ in the case of just one filter! That is, out of 30 pick-3 lottery filters I wanted to be wrong, I would set one of them correctly! Therefore, the whole lottery strategy would fail! Make sense?

![New lotto strategy in reverse, lottery 2010, 2011.](https://saliu.com/HLINE.gif)

## <u>II. Reversed Lotto Strategy Redivivus</u>

I came back to that _**reversed lottery strategy**_ in the year of grace 2010. Basically, a loyal user of my lottery software and member of my Forums (known as BrianPA from Pennsylvania) wanted to completely eliminate “unwanted” lotto combinations from a file:

-   [_**Removing Lotto Combinations**_](https://saliu.com/lie-lottery-strategies-pairs.html).

It is a longer story that I wrote about in my previous lotto forum and also in my 2010 lottery Super Forums. Several members of the forums participated in the discussion. That discussion contributed also to the most significant upgrade of my lotto-5 software: **Bright5**.

I will repost here the most important parts of the analyses of the new strategy (also known as _**LIE**_) that is also a feature (function) of my latest lotto software.

The LIE name for the lottery strategy and the files sounds curious. The better one I had was _**NOT**_ files. As in _that output file will NOT have winners the next lottery drawing_. Problem was _NOT_ is a widely used keyword in computer programming. That's why I chose _LIE_ — still a short word with similar significance. As in _if that output file will hit in the next lottery drawing, that will be a LIE_.

It is one of the fundamental laws of logic: _**The negation of negation is affirmation.**_ And that's how my reversed lottery strategy took off.

You noticed that, more often than not, your lotto output files do NOT have winning numbers: Not 6 winners, not 5, not 4, not 3n ot even 2 at times. You can open the LIE file and generate lotto combinations that do NOT contain groups of numbers in the LIE file. For example, you don't want any '5 of 5' groups; i.e. every 5-number combination will be eliminated. You can apply tougher settings, such as '4 of 5' or '3 of 5', even '2 of 5'. You can apply only ONE filter at a time for this elimination function. The following filters are applicable: ONE, PAIR, TRIP, QUAD, QUINTET (6-number lotto).

The _**LIE**_ option (_**reversed lottery strategy**_) is implemented in the following lotto software packages: Bright5.exe and Bright6. Also in the pick lottery software collections Bright3 and Bright4.exe; and in the horse racing software BrightH3. By default, the _**LIE filter**_ is NOT enabled — the user has to press _**Y/y**_ (for Yes) to enable it.

![Code name of this reversed lottery strategy is LIE lotto strategy.](https://saliu.com/ScreenImgs/reverse-strategy.gif)

## <u>III. Reversed Lotto Strategy Example: Eliminate Pairings, Triples, Quads, etc.</u>

I repeat an example from my new lottery forum (starting 2010). I ran _**Super Utilities**_, created a TOP5 file with the best 10 pairings for each number. Then I used TOP5 as the input to Make/Break/Position, option 4 = Break 5+/Positional ranges, then option 1 = 1 Number + 4 Pairs. I named the output file LIE5.2, as I wanted to use the filter ID 2.

I ran **Combine5-10** with the LIE option against LIE5.2. The filter ID=2 generated 12 combinations. One of the combos had 3 winners. I had expected _5 of 5_. I looked up carefully TOP5. One of the numbers that was not drawn was top-10-paired with 3 of the numbers in the drawing:

5 3 16 10 13 1 19 33 34 40 42

That was a real bad situation! In my logic, I did not expect more than one of the lotto numbers in the drawing be top-10 paired with any other number in the game! For example, just 5 and 3 being top-10 pairings would have been OK.

I repeated the procedure for the other situation:  
Drawing #203: 12, 24, 25, 37, 38

The filter ID = 2 generated just one combination, which also had 3 winners. I checked TOP5 again:  
15 38 32 24 30 31 1 36 22 39 41 20 30 42 27 5 32 37 24 13 14 15

I tried ID = 3 and it generated the jackpot combination, but in over 45,000 combinations! On the other hand, 45,000 represent just fewer than 5% of total 5-43 lotto combinations. In other words, the _LIE_ filter eliminated 95% of total combinations, with just one _LIE_ file, and with a good frequency.

We are not limited to pairings only. We run the combination generators in _**Super Utilities**_ (normally without favorite numbers). We can enable the _Least Triples_ with _Least53_ as input. We know now for sure that the output will NOT have the jackpot combination the very next lottery drawing. It is very rare to get just 3 winning numbers in the output. That output file also is a very good candidate for the LIE function. The same goes for Quadruples and Least54 as input.

![This is a powerful lottery strategy: reverse the loss to win.](https://saliu.com/HLINE.gif)

## <u>IV. Reversed Lottery Strategy Example: Skips, Decades, Frequencies</u>

The _skips_, _decades_, _last digits_, and the _3-group frequencies_ are also good candidates for the _**LIE**_ option (_**reversed lottery strategy**_). Look at the reports created by **SkipDecaFreq**. You will notice that the strings in a particular lottery drawing are no repeats from several previous draws. Well, then, generate lotto combinations using skips, decades, and the 3-group frequencies as filters. You know it is very rare for the output file to register 5, even 4, winning lotto numbers in the very next drawing.

The main focus was pick 3 lottery, clarifying intentional misunderstanding by a member (and also sore competitor).

I plotted 3 reports for pick-3. You can see that some 3-parameter strings (1-0-2, etc.) do not repeat for a few drawings; other strings do repeat more often than others. You can see that the 1-1-1 type of strings show better frequencies.

LOWER frequency: Good candidate for _**LIE**_;  
HIGHER frequency: Good candidate for _**PURGE**_ (as in playing **straight**, not **in reverse**).

![This is a LIE strategy for pick-3 digit lottery.](https://saliu.com/ScreenImgs/loss-win-pick3.gif)

I generate combinations to one output file (LIE3.1 or just OUT3.1) for the decade string 1-0-2 (the 1st line in the report). Straight sets generated: 81.

I generate combinations to one output file (LIE3.2 or just OUT3.2) for the frequency string 0-2-1 (the 1st line in the report). Straight sets generated: 135.

I generate combinations to one output file (LIE3.3 or just OUT3.3) for the low/high string 2-2-1 (the 1st line in the report). Straight sets generated: 125.

I generate combinations to one output file (LIE3.4 or just OUT3.4) for the odd/even string 1-2-1 (the 1st line in the report). Straight sets generated: 100.

I can concatenate to a LIE3 file of 441 combosnations (there are some duplicates). There is a good chance I will be successful quite often. Of course, the LIE feature is an addition to other filters, part of a pick-3 strategy.

![A lotto strategy is many reductions in one: Straight software, purge, reversed.](https://saliu.com/HLINE.gif)

## <u>V. A Lottery Strategy is <i>Trinity</i>: Straight, Purge, Reversed</u>

~ 1: _**Straight**_ represents the traditional type of lottery strategies. You select a strategy based on the analyses of the W, MD, SK, DE, FR reports. You select filters that have a higher degree of certainty (DC) to hit soon. You can also check how that particular lottery strategy fared in the past. You can also see how many lotto combinations that strategy generated for the hit situations (the _check strategy hits_ function).

You could see in the frequency report two occurrences of an obvious pick-3 strategy. They are marked by \* (lines #6 and #16). The frequency string 2-1-0 always generates 36 combinations (different from draw to draw, usually). If played in the last 20 draws, the cost would be 720. An online lottery would pay 1800. The strategy occurs 10-12 times in 200 drawings. The cost of 7200 would yield 9000.

The strategy has also skips — thus, a number of drawings will NOT be played. There are also other filters to apply. The LIE strategy can be also applied at the same time.

An even tighter strategy occurs 2-4 times in 200 drawings: 3-0-0 (frequency groups). It generates 8 combinations. That strategy can hardly be reduced to fewer combinations. But 50 drawings can be skipped between hits. The cost would be 8 \* 100 = 800. The online lotteries would pay 1800.

~ 2: _**Purge output files**_  
In this real-life case, we generate the 36 pick 3 sets to a traditional output file (e.g. OUT3). We select _**Purge**_ in the Lexico.exe programs. We enter the normal lottery data file (D\*). Then, we enter OUT\* as file to purge. Finally, we can apply one or more LIE\* files.

If we create LIE files that work with different filter IDs, we can use the power of the _PURGE_ function available in the lexicographical generators: Lexico5.exe and **Combine5-10**. For example, we start with LIE5.1 and generate (in normal lexicographic mode) an output file OUT5.L1. Next, we apply the LIE5.2 and run PURGE against OUT5.L1 ... and so on.

~ 3: _**Reversed strategy**_  
There are lots of possibilities for the new LIE function. We have also plenty — I mean, lots — of filters to work with! Not to mention we have the strategy checking utilities. I guess we need some extra patience and diligence too.

Additionally, more than one LIE file can be created to work with the same filter ID. And, of course, there are many other lotto filters to work with the traditional lottery strategy manner.

It is important, however, the filter ID to enable when applying the LIE function. Also, we can concatenate several LIE files where we can apply the same filter ID. Use the special _**concatenation**_ function in the menu facility of Bright lottery packages.

Preserve your lottery output files. You might want to concatenate some of them. _**Checking for winners**_ is another useful tool in _**Super Utilities**_. We can run also **Winners**, the standalone program that checks for winning numbers against real lottery drawings. We can notice that a large output file doesn't hit even 2 winning lotto numbers at times. Make it a LIE file — and apply the specific filter ID!

![We reverse the strategy: Intentionally set lotto or lottery filters that will not win next drawing.](https://saliu.com/HLINE.gif)

-   I have achieved significant advances in the theory and software programming of lotto software dedicated to _reversed strategies_. A loyal user of my 5-number lotto software made a major contribution. In all fairness, releasing the new software to the general public would be inappropriate at this time.
-   It would be considered **new** software, **not** _upgraded_ software. The new _**LIE elimination**_ strategy software (2 categories), plus extended _**Markov Chains lottery**_, plus _**last digits**_ and _**deltas**_ lotto programs would be added to new **Bright** software packages. If the new software is released, the decision will be made public via the New Writings and Software Download pages. Latest:
-   [_**LIE Elimination: Lottery, Lotto Strategy in Reverse for Pairs, Number Frequency**_](https://saliu.com/lie-lottery-strategies-pairs.html).
-   [_**Reversed Lottery Strategy for Lotto Decades, Last Digits, Odd Even, Low High, Skips**_](https://saliu.com/lie-lotto-strategies-decades.html).

_"Tell me lies...  
Tell me sweet little lies..."_

<iframe src="https://www.youtube.com/embed/BVUOuwEeVX0" frameborder="0" allowfullscreen=""></iframe>

![This program applies the LIE ELIMINATION reversed lottery strategy feature introduced in Bright lotto software, in combination generators.](https://saliu.com/ScreenImgs/LieIDLotto.gif)

[

## <u>6. Resources in Lottery Lotto Software, Strategies, Systems</u>

](https://saliu.com/content/lottery.html)

-   The Main [_**Lotto, Lottery, Software, Strategy**_](https://saliu.com/LottoWin.htm) Page.  
    Presenting software to create free winning lotto, lottery strategies, systems based on mathematics. Get your lotto systems or wheels, the best lottery, lotto software, combinations, winning numbers.
-   [_**Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies**_](https://saliu.com/Newsgroups.htm).  
    Read a genuine analysis of Excel spreadsheets applied to lottery and lotto developing of software, systems, and strategies. Combining Excel analysis with powerful lottery and lotto software programmed by this author, _Parpaluck_.
-   [_**<u>MDIEditor Lotto WE</u>: Lottery Software Manual, Book, ebook, Help**_](https://saliu.com/MDI-lotto-guide.html).  
    ~ Also applicable to my entire lottery software; plus Powerball, Mega Millions, Euromillions.
-   [_**Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps, Programs**_](https://saliu.com/forum/lotto-book.html).
-   [**Lottery Mathematics, Lotto Mathematics, Math, Maths**](https://saliu.com/gambling-lottery-lotto/lottery-math.htm): Probability, Appearance, Repeat, Affinity, Number Affiliation, Lotto Wheels, Lottery Systems, Strategies.
-   Practical [_**Lottery and Lotto Filtering in Software**_](https://saliu.com/filters.html).
-   [**Skips Lottery, Lotto, Gambling, Systems, Strategy**](https://saliu.com/skip-strategy.html); software included.
-   [_**Lotto, Lottery Strategy on Sums, Root Sums, Skips, Odd Even, Low High, Deltas**_](https://saliu.com/strategy.html).
-   [_**Lottery Utility Software**_](https://saliu.com/lottery-utility.html)_**: Pick-3, 4 Lottery, Lotto-5, 6, Powerball, Mega Millions, Euromillions**_.
-   [_**Lottery Strategy, Systems Based on Number, Digit Frequency**_](https://saliu.com/frequency-lottery.html)
-   [_**<u>LIE Elimination</u>: Lottery, Lotto Strategy in Reverse for <u>Pairs, Number Frequency</u>**_](https://saliu.com/lie-lottery-strategies-pairs.html).
-   [_**<u>LIE Elimination, Reversed Lottery Strategy</u> for Lotto Decades, Last Digits, Odd Even, Low High, Skips**_](https://saliu.com/lie-lotto-strategies-decades.html).
-   [_**Software to Generate Lotto Combinations with Favorite Lottery Numbers in Fixed Positions**_](https://saliu.com/favorite-lottery-numbers-positions.html).
-   [**Theory, Analysis of _Deltas_ in Lotto, Lottery Software, Strategy, Systems**](https://saliu.com/delta-lotto-software.html).
-   [_**Lotto Decades, Last Digits, Systems, Strategies, Software**_](https://saliu.com/decades.html).
-   [_**Markov Chains, Followers, Pairs, Lottery, Lotto, Software**_](https://saliu.com/markov-chains-lottery.html).
-   [_**Lotto Software for Groups of Numbers: Odd, Even, Low, High, Sums, Frequencies, User's Groups**_](https://saliu.com/lotto-groups.html).
-   [_**The Best Strategy for Lottery, Gambling, Sports Betting, Horse Racing, Blackjack, Roulette**_](https://saliu.com/strategy-gambling-lottery.html).
-   _"The Start Is the Hardest Part"_ in [_**Lottery Strategies**_](https://forums.saliu.com/lottery-strategies-start.html).
-   [_**Strategy Lotto Software on Positional Frequency**_](https://forums.saliu.com/lotto-software-position-frequency.html).
-   Download [**Lottery Software, Lotto Software Applications**](https://saliu.com/infodown.html).

![Apply the lottery strategy in reverse: Not-lose to win.](https://saliu.com/HLINE.gif)

**[Home](https://saliu.com/index.htm) | [Search](https://saliu.com/Search.htm) | [New Writings](https://saliu.com/bbs/index.html) | [Fundamental Formula](https://saliu.com/formula.htm) | [Odds, Generator](https://saliu.com/calculator_generator.html) | [Contents](https://saliu.com/content/index.html) | [Forums](https://forums.saliu.com/) | [Sitemap](https://saliu.com/sitemap/index.html)**

![This impressive lottery software and strategy are based on the science of logic.](https://saliu.com/images/HLINE.gif)
