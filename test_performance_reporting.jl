using WonderGridLotterySystem
using Statistics
using Dates

println("Testing Performance Reporting System")
println("=" ^ 50)

# Load lottery data
fm = FileManager()
data = read_data5_file(fm, "data/fan5.csv")
println("Loaded $(length(data)) lottery draws for performance reporting")

# Create engines
engine = WonderGridEngine(data)
key_numbers = select_key_numbers(engine)

# Create test period
test_draws = data[1:min(500, length(data))]  # Use first 500 draws
start_date = minimum([d.draw_date for d in test_draws])
end_date = maximum([d.draw_date for d in test_draws])

backtesting_engine = BacktestingEngine(data, start_date, end_date)
lie_engine = LIEEliminationEngine(engine.pairing_engine)

# Create performance reporter
reporter = PerformanceReporter(engine, backtesting_engine, lie_engine)

println("Performance reporter created successfully")
println("Test period: $start_date to $end_date ($(length(test_draws)) draws)")

# Test single key number performance report
println("\n" * "=" ^ 50)
println("Single Key Number Performance Report")
println("=" ^ 50)

if !isempty(key_numbers)
    test_key = key_numbers[1]
    println("Generating performance report for key number $test_key...")
    
    start_time = time()
    report = generate_performance_report(reporter, test_key, test_draws)
    generation_time = time() - start_time
    
    println("Report generated in $(round(generation_time, digits=3)) seconds")
    
    # Display the report
    display_performance_report(report)
    
    # Test report export
    export_filename = "performance_report_key_$(test_key).txt"
    export_performance_report(report, export_filename)
    println("\nReport exported to: $export_filename")
end

# Test comparative performance reporting
println("\n" * "=" ^ 50)
println("Comparative Performance Analysis")
println("=" ^ 50)

# Test with first 5 key numbers for speed
test_keys = key_numbers[1:min(5, length(key_numbers))]
println("Generating comparative report for $(length(test_keys)) key numbers...")

start_time = time()
comparative_reports = generate_comparative_report(reporter, test_keys, test_draws)
comparative_time = time() - start_time

println("Comparative analysis completed in $(round(comparative_time, digits=3)) seconds")
println("Successfully generated $(length(comparative_reports)) reports")

# Display comparative analysis
display_comparative_analysis(comparative_reports)

# Test performance metrics analysis
println("\n" * "=" ^ 50)
println("Performance Metrics Analysis")
println("=" ^ 50)

if !isempty(comparative_reports)
    println("Analyzing performance metrics across $(length(comparative_reports)) reports:")
    
    # Processing time analysis
    generation_times = [r.generation_time for r in comparative_reports]
    backtest_times = [r.backtest_time for r in comparative_reports]
    total_times = [r.total_processing_time for r in comparative_reports]
    
    println("\nProcessing Time Analysis:")
    println("  Average generation time: $(round(mean(generation_times), digits=3)) seconds")
    println("  Average backtest time: $(round(mean(backtest_times), digits=3)) seconds")
    println("  Average total time: $(round(mean(total_times), digits=3)) seconds")
    println("  Total processing time: $(round(sum(total_times), digits=3)) seconds")
    
    # Hit rate analysis
    hit_rates_3_5 = [r.hit_rates.three_of_five for r in comparative_reports]
    hit_rates_4_5 = [r.hit_rates.four_of_five for r in comparative_reports]
    hit_rates_5_5 = [r.hit_rates.five_of_five for r in comparative_reports]
    
    println("\nHit Rate Statistics:")
    println("  3/5 matches - Mean: $(round(mean(hit_rates_3_5) * 100, digits=3))%, Std: $(round(std(hit_rates_3_5) * 100, digits=3))%")
    println("  4/5 matches - Mean: $(round(mean(hit_rates_4_5) * 100, digits=3))%, Std: $(round(std(hit_rates_4_5) * 100, digits=3))%")
    println("  5/5 matches - Mean: $(round(mean(hit_rates_5_5) * 100, digits=3))%, Std: $(round(std(hit_rates_5_5) * 100, digits=3))%")
    
    # Efficiency analysis
    efficiency_ratios = [r.insights["best_efficiency_ratio"] for r in comparative_reports]
    
    println("\nEfficiency Analysis:")
    println("  Mean efficiency ratio: $(round(mean(efficiency_ratios), digits=2))x")
    println("  Best efficiency ratio: $(round(maximum(efficiency_ratios), digits=2))x")
    println("  Worst efficiency ratio: $(round(minimum(efficiency_ratios), digits=2))x")
    println("  Standard deviation: $(round(std(efficiency_ratios), digits=2))x")
    
    # Quality metrics
    success_rates = [r.success_rate for r in comparative_reports]
    integrity_scores = [r.data_integrity_score for r in comparative_reports]
    
    println("\nQuality Metrics:")
    println("  Average success rate: $(round(mean(success_rates) * 100, digits=1))%")
    println("  Average data integrity: $(round(mean(integrity_scores) * 100, digits=1))%")
    println("  Reports with perfect integrity: $(count(s -> s == 1.0, integrity_scores))/$(length(integrity_scores))")
end

# Test insights generation
println("\n" * "=" ^ 50)
println("Insights Analysis")
println("=" ^ 50)

if !isempty(comparative_reports)
    println("Analyzing strategic insights:")
    
    # Strategy assessments
    assessments = [r.insights["strategy_assessment"] for r in comparative_reports]
    assessment_counts = Dict{String, Int}()
    for assessment in assessments
        assessment_counts[assessment] = get(assessment_counts, assessment, 0) + 1
    end
    
    println("\nStrategy Assessment Distribution:")
    for (assessment, count) in sort(collect(assessment_counts), by = x -> x[2], rev = true)
        percentage = round(100 * count / length(assessments), digits=1)
        println("  $assessment: $count ($(percentage)%)")
    end
    
    # Recommendations
    recommendations = [r.insights["recommendation"] for r in comparative_reports]
    recommendation_counts = Dict{String, Int}()
    for rec in recommendations
        recommendation_counts[rec] = get(recommendation_counts, rec, 0) + 1
    end
    
    println("\nRecommendation Distribution:")
    for (rec, count) in sort(collect(recommendation_counts), by = x -> x[2], rev = true)
        percentage = round(100 * count / length(recommendations), digits=1)
        println("  $rec: $count ($(percentage)%)")
    end
    
    # Best performing tiers
    best_tiers = [r.insights["best_performing_tier"] for r in comparative_reports]
    tier_counts = Dict{String, Int}()
    for tier in best_tiers
        tier_counts[tier] = get(tier_counts, tier, 0) + 1
    end
    
    println("\nBest Performing Tier Distribution:")
    for (tier, count) in sort(collect(tier_counts), by = x -> x[2], rev = true)
        percentage = round(100 * count / length(best_tiers), digits=1)
        println("  $tier: $count ($(percentage)%)")
    end
    
    # Jackpot analysis
    jackpot_achievers = count(r -> r.insights["jackpot_achieved"], comparative_reports)
    println("\nJackpot Analysis:")
    println("  Keys achieving jackpot: $jackpot_achievers/$(length(comparative_reports))")
    println("  Jackpot achievement rate: $(round(100 * jackpot_achievers / length(comparative_reports), digits=1))%")
end

# Test report export functionality
println("\n" * "=" ^ 50)
println("Report Export Testing")
println("=" ^ 50)

if !isempty(comparative_reports)
    println("Testing report export functionality:")
    
    # Export individual reports
    for (i, report) in enumerate(comparative_reports[1:min(3, length(comparative_reports))])
        filename = "performance_report_key_$(report.key_number)_detailed.txt"
        export_performance_report(report, filename)
        println("  Exported report for key $(report.key_number) to: $filename")
    end
    
    # Create summary export
    summary_filename = "performance_summary.csv"
    open(summary_filename, "w") do file
        println(file, "Key,3/5_Rate,4/5_Rate,5/5_Rate,Best_Ratio,Assessment,Recommendation")
        for report in comparative_reports
            println(file, "$(report.key_number),$(report.hit_rates.three_of_five),$(report.hit_rates.four_of_five),$(report.hit_rates.five_of_five),$(report.insights["best_efficiency_ratio"]),$(report.insights["strategy_assessment"]),$(report.insights["recommendation"])")
        end
    end
    println("  Exported summary to: $summary_filename")
end

# Performance benchmarking
println("\n" * "=" ^ 50)
println("Performance Benchmarking")
println("=" ^ 50)

if !isempty(key_numbers)
    println("Benchmarking performance reporting system:")
    
    # Test with different numbers of key numbers
    benchmark_sizes = [1, 3, 5, min(10, length(key_numbers))]
    
    for size in benchmark_sizes
        test_keys_bench = key_numbers[1:size]
        
        start_time = time()
        bench_reports = generate_comparative_report(reporter, test_keys_bench, test_draws[1:min(100, length(test_draws))])
        bench_time = time() - start_time
        
        reports_per_second = round(length(bench_reports) / bench_time, digits=1)
        time_per_report = round(bench_time / length(bench_reports), digits=3)
        
        println("  $size keys: $(round(bench_time, digits=3))s total, $(time_per_report)s per report, $(reports_per_second) reports/sec")
    end
end

# Memory usage analysis
println("\n" * "=" ^ 50)
println("Memory Usage Analysis")
println("=" ^ 50)

if !isempty(comparative_reports)
    println("Analyzing memory usage:")
    
    # Estimate memory usage per report
    estimated_memory_per_report = sizeof(PerformanceReport) + 
                                 sizeof(Dict{String, Float64}) * 2 +  # hit rates and efficiency ratios
                                 sizeof(Dict{String, Any}) +          # insights
                                 sizeof(HitRates) + 
                                 sizeof(CostAnalysis) +
                                 sizeof(EfficiencyComparison)
    
    total_estimated_memory = length(comparative_reports) * estimated_memory_per_report
    
    println("  Estimated memory per report: $(round(estimated_memory_per_report / 1024, digits=2)) KB")
    println("  Total estimated memory: $(round(total_estimated_memory / 1024, digits=2)) KB")
    println("  Memory efficiency: $(round(total_estimated_memory / (length(comparative_reports) * 1024), digits=2)) KB per report")
end

# Quality assurance
println("\n" * "=" ^ 50)
println("Quality Assurance Summary")
println("=" ^ 50)

if !isempty(comparative_reports)
    # Check report completeness
    complete_reports = count(r -> 
        haskey(r.insights, "best_efficiency_ratio") &&
        haskey(r.insights, "strategy_assessment") &&
        haskey(r.insights, "recommendation") &&
        r.data_integrity_score > 0.8, comparative_reports)
    
    # Check data consistency
    consistent_data = all(r -> 
        r.hit_rates.five_of_five <= r.hit_rates.four_of_five + 0.01 &&  # Allow small tolerance
        r.hit_rates.four_of_five <= r.hit_rates.three_of_five + 0.01, comparative_reports)
    
    # Check performance metrics
    reasonable_performance = all(r -> 
        r.generation_time >= 0 &&
        r.backtest_time >= 0 &&
        r.total_processing_time >= r.generation_time + r.backtest_time - 0.01, comparative_reports)  # Small tolerance
    
    println("Performance Reporting Quality Assessment:")
    println("  Complete reports: $complete_reports/$(length(comparative_reports))")
    println("  Data consistency: $(consistent_data ? "PASS" : "FAIL")")
    println("  Performance metrics validity: $(reasonable_performance ? "PASS" : "FAIL")")
    println("  Average data integrity: $(round(mean([r.data_integrity_score for r in comparative_reports]) * 100, digits=1))%")
    
    overall_quality = if complete_reports == length(comparative_reports) && consistent_data && reasonable_performance
        "EXCELLENT"
    elseif complete_reports >= length(comparative_reports) * 0.8
        "GOOD"
    else
        "NEEDS IMPROVEMENT"
    end
    
    println("  Overall quality rating: $overall_quality")
end

println("\nPerformance Reporting System testing completed successfully!")