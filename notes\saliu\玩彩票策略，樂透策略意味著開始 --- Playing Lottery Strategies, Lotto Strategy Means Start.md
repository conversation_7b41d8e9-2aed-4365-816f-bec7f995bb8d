---
created: 2025-07-24T22:40:23 (UTC +08:00)
tags: [lottery,strategy,lotto strategies,start play,procrastination,win,Pennsylvania Lottery,]
source: https://forums.saliu.com/lottery-strategies-start.html
author: 
---

# 玩彩票策略，樂透策略意味著開始 --- Playing Lottery Strategies, Lotto Strategy Means Start

> ## Excerpt
> The founder of lottery mathematics presents real lotto strategies, lottery strategy to win. Procrastination is an obstacle in winning the lottery.

---
**_ParpalAxio，大超級彩券程式設計師、惠勒、分析師、策略師_**

[![<PERSON> teaches you how to create the best lotto wheeling strategies.](https://saliu.com/AxiomIon.jpg)](https://saliu.com/membership.html)  

## <u><i>彩票策略</i>中最難的是<i>開始</i></u> ★★★★★★

## 作者：Ion Saliu， _彩票數學、彩票程式科學創始人_

![Start playing lottery strategies, lotto strategies win real money, in actual play.](https://forums.saliu.com/HLINE.gif)

### 一、 [彩票策略領先](https://forums.saliu.com/lottery-strategies-start.html#strategy) II. [樂透策略報告製作軟體](https://forums.saliu.com/lottery-strategies-start.html#reports) 三、 [拖延症：彩券策略的敵人](https://forums.saliu.com/lottery-strategies-start.html#procrastinate) 四、 [彩票資料檔管理軟體](https://forums.saliu.com/lottery-strategies-start.html#software) 五、 [我的彩券策略經驗（2013）](https://forums.saliu.com/lottery-strategies-start.html#experience)

![It is important to start working with lottery software to discover good playing strategies.](https://forums.saliu.com/HLINE.gif)

## <u><span lang="zh-TW"><span data-immersive-translate-translation-element-mark="1"><span data-immersive-translate-translation-element-mark="1">1. 彩券策略的先行者</span></span></span></u>

有時，我會透過在新聞群組或其他虛擬世界的試驗場中比武來磨練我的劍術。我就是這樣開始在網路世界中活動的，在 rec.gambling.lottery 新聞群組裡，有 Kotkoducs、Kokostirks、Kotskarrs，當然還有 Psychosamas……O tempora! O mores!

我們珍惜現在，回報過去，卻又總是堅定地面向未來。就像那句名言所說的：

-   _「誰控制了過去，誰就控制了未來；誰控制了現在，誰就控制了過去」_ （喬治‧歐威爾， _《一九八四》_ ）。

彩票的動態性建立在不斷變化的現實之上。是_變異_創造了人類。正如赫拉克利特所說： _「變化是宇宙中唯一不變的。」_ 我將透過分析賓州彩券的 6-49 樂透開獎結果文件來舉例說明。使用的分析軟體： MDIEditor 和 Lotto WE 。

我這裡只發布報告的開頭部分。完整的文字檔案可以免費下載（連結位於本文末尾）。

![The report shows the filters for the pick 3 lottery game.](https://forums.saliu.com/lotto-filter.gif)

_Any\_1_ 表示從過去開獎的某個區塊中重複出現一個號碼，無論其位置為何。例如， _Any\_1 = 2_ 表示至少有一個號碼與過去兩次開獎的區塊（即 12 個號碼，不一定唯一）不重複。然而，該號碼與最近三次開獎的區塊（即 18 個號碼，不一定唯一）重複出現。

中_位數_是一個非常重要的參數。 Any\_1 的中位數是 **0。** 這意味著至少在 50% 的情況下，會有一個數字與上次抽獎的結果重複。

Any\_2 指 2 個重複的數字；Any\_3 指 3 個重複的數字，如此循環。中位數分別為 2、4、6、9、18。這意味著在至少 50% 的情況下：最近 3 次抽獎中重複出現兩個數字；最近 5 次抽獎中重複出現三個數字；最近 7 次抽獎中重複出現四個數字；最近 10 次抽獎中重複出現五個數字；最近 19 次抽獎中重複出現六個數字。

在 MDIEditor 和 Lotto WE 中可以立即得出樂透策略：

**MAX\_Any\_1 = 1** （即，在產生的每個組合中至少會出現上次抽獎中的一個數字）；

_Any\_2_ 至 _Any\_6_ 的最小值分別為：2、4、6、9、18。這是一個非常強大的彩票策略！在某些情況下，該策略不會產生任何彩票組合（沒有任何組合滿足限制）。在像 #38 這樣的開獎（賠率線）中，可以發現中獎的情況：

38 225+ 9- 3- 1 13+ 0- 2- 5+ 7+ 12+ 19- 2- 5- 5- 7- 12- 62-

在那種情況下真正的彩票開獎號碼是： _3 10 19 21 38 39_ 。

當然，還可以實作其他幾個過濾器（限制）；例如 Min\_Ver\_6 = 25。

然而，並非所有彩票策略都生來平等。有一種策略甚至更強大，中獎頻率也更高。看看 Ver\_6 就知道了。中位數是 62。當同時實作最小值和最大值篩選值時，每種彩券策略的效力都會大大增強。因此，一個非常嚴謹但有效的 Lotto 649 策略應該是：

**Min\_Ver\_6 = 62 且 MAX\_Ver\_6 = 63**

可以安全地啟用更多過濾器。看看那個「過去開獎」濾鏡。如果您有一個大型 D6 檔案（ Bright6 要求），您可以安全地將「過去開獎」過濾器設定為 100000……甚至 1000000（一百萬）！等等，還有更多！您可以在 Bright6 中清除由 MDIEditor 和 Lotto WE 產生的輸出檔。甚至更多：您可以透過在 Bright 軟體中使用 _**「消除誤碼」**_ 功能來減少更多彩票組合…

樂透輸出檔案中的絕大多數組合都有 4 個甚至 5 個共同的數字。一個快速策略：在 **_Super Utilities_** （ Bright6 主選單）中， _使用 WHEEL-FROM-FILE_ 輸出檔。查看策略產生的樂透組合中所有共同的數字…

```
<span size="5" face="Courier New" color="#ff8040"> 1  3  32  34  35  37 
 1  3  32  34  35  38 
 1  3  32  34  35  39 
 1  3  32  34  35  40 
 1  3  32  34  35  41 
 1  3  32  34  35  42 
 1  3  32  34  35  43 
 1  3  32  34  35  49 
...
 23  29  32  44  45  47 
 23  29  32  44  45  48 
 23  29  32  44  45  49 
 23  29  32  44  46  47 
 23  29  32  45  46  48 
 23  29  32  45  48  49
</span>
```

-   [_**<u>MDIEditor Lotto WE</u> ：彩票軟體手冊、書籍、電子書、幫助**_](https://saliu.com/MDI-lotto-guide.html) 。

_**命令提示字元**_樂透軟體 KOs _GUI_ ！

毫無疑問， MDIEditor 和 Lotto WE 看起來很棒。正如一位用戶所說： _「它簡直就是一件藝術品。」_ 這款超級應用程式運作良好，尤其是在快速統計分析方面。它還可以為較小的資料檔案產生彩票組合。

然而，圖形介面存在嚴重的限制。就性能而言，這是一個障礙。我嘗試對在 Bright6 中創建的 D6 資料檔案進行相同的分析。結果毫無懸念。我等 D6（近 1400 萬行）打開時已經失去了耐心！所以我把 MDIEditor 和 Lotto WE 中的過濾器加入到 Bright6 。這樣我就可以處理 4+4 層中的數百萬種組合。毫無疑問，我希望速度更快。目前，我的 Lotto-6 軟體已經將當今電腦的技術極限推向了極限。

此外， Bright 軟體能夠在更短的時間內進行更大範圍的分析。因此，可以發現更多彩票策略。我對 500 張真實的彩票抽獎進行了過濾分析。我注意到 _Ver6\_1 的值非常大：超過 1000。_ 我運行了**策略檢查**程式（主選單）。我還注意到其他幾個過濾器的有效值顯示出一定的一致性。我在這篇文章中發布了第 1 層的策略報告。但首先，依 _Ver6\_1_ 降序排列的 _MD6.1_ 片段。

![The checking of the lotto strategy is a neat report.](https://forums.saliu.com/strategy-1.gif)

![The next report shows the filters in MDIEditor and Lotto.](https://forums.saliu.com/strategy-2.gif)

![ The reports are valuable tools to find new lotto strategies to win the lottery.](https://forums.saliu.com/strategy-3.gif)

![The reports are similar for every layer of the lotto data file of drawings.](https://forums.saliu.com/strategy-4.gif)

強大的樂透策略的**關鍵過濾器**是 **Ver6\_1 = 1000** 。

我可以加 _Ion5\_1 = 1000、MAX\_One\_1 = 1、MAX\_Any\_1 = 1。_ 此外，所有 4 個層級都支援 _Del6_ 。在大多數情況下，我可以將 _Del6_ 設定為 _100000_ ；在某些層級中，甚至可以設定為 _100,000_ （100 萬），這取決於該策略最近的中獎次數。 Del6 是一個強大且安全的過濾器。它可以消除所有過去的開獎結果和 Delta 格式的模擬組合。例如，過去的樂透開獎結果或模擬組合之一是 7、12、15、23、38、47；該組合將被消除（0-delta），加上 6、11、14、22、37、46（-1 delta），加上 8、13、16、24、37、48（+1 delta）等。此過濾器的要求也很高，會減慢組合的產生速度。

擺脫不需要的樂透組合還有第二部分： _**LIE 消除**_功能。

產生同步報告非常重要。在這種情況下，W6、MD6 和配對報告 ( _Pair6.REP_ ) 應該針對同一個資料檔案產生。然後，您可以使用 FileLines 交叉檢查不同平台的彩票策略。位置：選單 #2， _T = 交叉核對策略_ 。

![The lotto pairs are essential in creating lottery strategies.](https://forums.saliu.com/lotto-pairs.gif)

有時候，這個策略連一個組合都抽不到。顯然，它不會在下一期彩票開獎時中獎。不過，這確實省錢！但這個策略確實會中獎——平均每年大概一次。即使兩年內中一次彩券大獎，也已經是很不錯了，不是嗎？這就是為什麼耐心是如此珍貴的美德！我可不是那種值得效法的榜樣（雖然我的確有編寫軟體、開發理論和系統的藉口）！完整策略報告（免費）連結：

-   [排序 MD6 獲勝報告](https://saliu.com/freeware/MD6.1-SOR)
-   [樂透 649 策略 Ver6\_1=1000](https://saliu.com/freeware/st6.rep) 。

## <u><span lang="zh-TW"><span data-immersive-translate-translation-element-mark="1"><span data-immersive-translate-translation-element-mark="1">2. 產生樂透策略報告</span></span></span></u>

正確的彩票策略如下圖所示。僅應用了一個關鍵過濾器：Ver6\_1 = 1000（來自文件：MD6.1）。但在 8 份報告中，還有許多其他過濾器可供選擇。很容易發現 One\_1 = 0 和 Any1\_1 = 0。這意味著過濾器的最大值將被啟用。這兩個過濾器並不完全相同，就像在舊版本的 LotWon 軟體中一樣。您還會注意到，大多數 One 過濾器的值都為 0 或小於 1。 Del6（基於_增量_ ）過濾_器_的值通常較高。

-   策略跳過可以非常有效地減少要玩的組合數量。這裡有一個簡單的策略。你注意到最低跳過次數是10。你先在10次抽獎後立即使用該策略。如果該策略沒有中獎，則等待一輪抽獎，例如25次。第二低跳過次數是27。如果仍然沒有中獎，則在接下來的40到55次抽獎中繼續玩。如果仍然沒有中獎（這種情況非常罕見），則停止使用該彩票策略。等到再次中獎。同時，你至少還有另一種彩票策略可用。
-   也要注意，中獎彩券抽獎通過了 MDIEditor 和 Lotto WE 中 INNER FILTERS 的限制。

![We must check any lottery strategies against past drawings.](https://forums.saliu.com/check-strategy-1.gif)

![Back-testing a lottery strategy is done layer by layer in the draw file.](https://forums.saliu.com/check-strategy-2.gif)

![The reports show different data from layer to layer, which offers more choices.](https://forums.saliu.com/check-strategy-3.gif)

...

![ Procrastination is the main obstacle in winning the lottery with Ion lotto software programs.](https://forums.saliu.com/check-strategy-4.gif)

...

## <u>3. Procrastination: Enemy of Lottery Strategies</u>

最初，我想把這篇文章命名為 _“別像伊翁·薩利烏那樣拖延！”_ ，但這對我個人來說不太公平。我得說，我最近真的很忙。首先，我更新了 _「跳過系統」_ 軟體，以及位置範圍程式。

From there, I upgraded the powerful LIE elimination software (which still remains reserved for now). It was during that time I mentioned I was chasing a strategy for the pick-3 lottery.

This very forum was viciously attacked, like many other forums run by the _vBulletin_ software. I believe I fixed the security issues. Unfortunately, the _vBulletin_ update introduced new issues. I decided not to update my forum software until they fix all the bugs and quirks.

Moreover, there were some issues with my website as warned in my webmaster tools accounts. I hope I won't do such changes to my webpages any more. I've had enough!

Finally, I got a break. I found the time and nerves to apply a lottery strategy to the pick-3 game in Pennsylvania. It happened yesterday, November 4, 2013. I bought also food and red wine. I cooked, had wine and watched a Netflix movie. It was a great film that triggered special memories. It was about life in Prague during the Soviet invasion: _"The Unbearable Lightness of Being"_. I was tipsy at the end of the movie. I saw the end of the TV night news when they announced the winning lottery numbers. I was convinced I won.

The winning daily number was **8-6-6**. In fact, one of the 3 numbers I played was _2-6-6_! I noticed it this morning. I was still tipsy and then I drank some more wine to lift my spirits... BRRRRRRRRAHAHAHAHA!!!!!!!

Point is I didn't play the lottery today. I never drink and drive... not even after a night of drinking. Today would have been an even better day to play. The lottery strategy is based on just one key filter: TOT\_1(no straight repeats in 4000 drawings). Yesterday, my strategy (I added a second safe filter) generated just 3 combosnations. Today, it generated 7 straight sets - a frequent amount when the lottery strategy hits. The minimum amount of combinations in the hit situations is 5. So, yesterday was not the right moment to play - but I was decided to win over procrastination!

I create a text file based on a strategy and name it meaningfully. In this case the name is _STR3-Tot14000.txt_. Here is a fragment:

![This is a real-life pick lottery strategy that hit the first prize.](https://forums.saliu.com/play-1.gif)

![The winning pick lottery strategy had a few tickets to play.](https://forums.saliu.com/play-2.gif)

When I started mentioning that strategy here, it was between PA Lottery drawings #137 and #104. The strategy hit in 10 drawings or so for the first time. Then it hit again after 38 drawings. The strategy should not be played immediately after a hit. This time, I looked at the situation as if I had saved 42 drawings! My consolation for today is that maybe the strategy will hit after a skip of 50 drawings.

But, I will play again tomorrow (no more drinking for a long while!) For today, the strategy generated 7 combinations. It is 5 PM when I'm writing this post. The Pennsylvania Lottery drawings are conducted at 8 PM. I will publish the pick-3 straight sets here rationalizing that no Pennsylvanian will read this post before 8 PM!

```
<span size="5" face="Courier New" color="#ff8040"> 1  9  9
 2  4  4 *
 2  6  6 *
 2  8  4 *
 5  7  2
 6  1  9
 7  6  4
</span>
```

The numbers marked by the asterisk are repeats from yesterday's strategy.

I just created a lottery software package that makes users' lives easier. The package updates the lottery data files in one common folder. Then, the data files are updated in their corresponding Bright folders. I present the new package - _LotteryData_ - in the next section.

## <u>4. Software to Manage Lottery Data Files</u>

軟體包名稱： **LotteryData.zip** 。

There is nothing really new here as far as the programs are concerned. Only the concept is new: The users can now update the lottery data files in one common folder. Then, the data files are updated in their corresponding _**Bright**_ folders. A screenshot of the main menu:

![Lottery Data Files is a collection of software to work with past winning lotto numbers.](https://forums.saliu.com/LotteryData.gif)

I strongly recommend creating a shortcut on your _Desktop_. You first create a folder in _Windows (File) Explorer_. Right-click **C:**, then _New_, then _Folder_, then type _LotteryData_ as folder name. You download the ZIP file to that folder and unzip it in the same directory. It will be easier for you to keep that folder name, as an important batch file uses that name. Of course, you can easily edit that file in Notepad++.

After you unzipped the package, you open it in Windows Explorer. Right-click on **LotteryData**, then _Send to_, then _Desktop (Create shortcut)_. Here is the shortcut on my desktop:

![The lottery software works best at the command prompt run by a desktop shortcut.](https://forums.saliu.com/LotteryDataShortcut.gif)

You configure the shortcut following the easy steps I presented on this page:

-   [_**Run Lottery Software at Command Prompt in Windows XP, Vista, Windows 7, 8, 10**_](https://saliu.com/gambling-lottery-lotto/command-prompt.htm).

This lottery software package relies on the best text editor out there: **Notepad++**. It is free to download and use for an unlimited time with no strings attached.

-   [_**Home of Notepad++**_](https://notepad-plus-plus.org/).
    
    If you are not a computer programmer, you should install the great application in one of the two locations:
    
    **C:\\Program Files (x86)\\Notepad++\\notepad++** - for 64-bit Windows, or -  
    **C:\\Program Files\\Notepad++\\notepad++** - for 32-bit Windows.
    
    Of course, if you are a programmer and have **PowerBasic Console Compiler**, you can edit the source code file I included in the package: **LotteryData.bas**.
    
    One useful feature of **Notepad++** is column selection. You can select a rectangular bloc of text and delete it easily. For example, delete the dates or other information you find on the lottery websites (e.g. _Payouts_). You hold down the _Alt_ key, then click at the beginning of the block of text, then move the mouse pointer to the end of the rectangular bloc of text. Here is how I do it with Pennsylvania Lottery data files I copy and paste from their Web site:
    
    ![My lottery software is enhanced nicely by the best text editor: Notepad plus plus.](https://forums.saliu.com/NotepadLottery.gif)
    
    Axiomatic one, I work with this lottery utility this way:
    
    1) Click the desktop shortcut  
    2) Press _6_ (or _3_) to open the editor  
    3) Open all data files in the editor (one after another)  
    4) Open a browser and open the bookmarked lottery website  
    5) Go to the results for each game, select the range of drawings that I am missing, then copy the results  
    6) Paste the results in the respective lottery data file  
    7) Edit data, if necessary (i.e. delete all unnecessary information and preserve only the lottery numbers;  
    make sure the numbers are separated by blank spaces or commas)  
    9) Repeat for every data file  
    10) Exit the editor and return to the main program \*  
    11) Press _S_ to sort the data files as required by my lottery software  
    12) I can do other operations (e.g. generate the _frequency reports_ for my lottery games)  
    13) I make sure I never leave the program without backing up (updating) all my files; always press _B_.
    
    \* I pinned the _Notepad++_ editor to the taskbar. I can start and work with the editor independently from the _LotteryData_ application. That way, I don't have to close the editor — it can stay open at all times. The open editor helps me also with managing other files, especially when they have long names.
    
    Again, you can edit the batch file that updates your files: **BkUpLotto.bat**. You open it in the editor and make changes to fit your PC configuration. My lottery data files are named **PA**\*; e.g. **PA-3** for the _pick 3 results in Pennsylvania Lottery_. In my case, I also backup to an external drive (named **J:** in my PC configuration). This are the contents of the backup batch file included in this software package:
    
    ECHO OFF  
    CLS  
    rem Be sure to surround by " " all directories with blank spaces in names; e.g. "C:\\My Web Pages\\\*.\*"  
    rem You can disable a command (line) by adding REM or rem in front of the line  
    REM You can add your own commands by editing the lines in this BATch file; or -  
    rem by writing your own lines based on your particular folders/files;  
    rem the only unchanged text is represented by:  
    REM XCOPY and the switches: /S /D /I /Y
    
    ECHO ON  
    XCOPY PA-3 C:\\Pick3 /S /D /I /Y  
    XCOPY PA-4 C:\\Pick4 /S /D /I /Y  
    XCOPY PA-5 C:\\Lotto5 /S /D /I /Y  
    XCOPY PA-6 C:\\Lotto6 /S /D /I /Y  
    REM XCOPY PA-Q C:\\Quinto5 /S /D /I /Y  
    REM XCOPY PA59-35 C:\\PBall /S /D /I /Y  
    REM XCOPY EURO.DAT C:\\EURO /S /D /I /Y  
    REM XCOPY MEGA.DAT C:\\MEGA /S /D /I /Y  
    XCOPY C:\\LotteryData _J:\\LotteryData_ /S /D /I /Y  
    PAUSE
    
    -   **WARNING!** The **Edge** browser that comes as the default in _Windows 10_ can be a serious hazard! I used it once to download the Pennsylvania Lottery results. The drawings are nicely presented in _tables_. As usually, I copied-and-pasted data into **Notepad++**. The data files turned into horrible content after _Sorting_, with lots and lots of zeros. I have absolutely no clue how the **Edge** browser handles copy-and-paste. Fortunately, I always have fresh backups of my lottery data files. You should try for yourself and see how the **Edge** browser works in your particular situation. Do it for one file only -- after you made sure you have an up-to-the date backup.
    
    Registered users can download the lottery data file software application directly from here:
    
    -   [_**Download LotteryData.zip**_](https://saliu.com/pub/LotteryData.zip).
-   I highly recommend also you visit this Web page that helps a lot with the backup of a computer up to its entirety. The backup software is the best and fastest there is, and it is totally FREE:
