彩票過濾器透過**設定限制**來減少彩票組合，旨在**提高中獎機率並最佳化投注成本**。這些過濾器基於 Ion Saliu 的數學理論，特別是賭博基本公式 (FFG) 及其動態特性。

以下是彩票過濾器如何減少組合的詳述：

- **過濾器的定義與目的**：
    
    - 彩票過濾器是**消除** LotWon 彩票軟體生成過程中組合的**參數**。它們本質上是**限制** (restrictions)。
    - 其主要目標是**大量減少**需要投注的彩票組合數量。
- **透過「最低」和「最高」等級進行限制**：
    
    - 過濾器通常有**兩個層級**：**最低** (minimum) 和**最高** (maximum)。
    - **最低級別**：只允許**高於**該級別的彩票組合，從而**消除**所有在該範圍**內**的組合。
    - **最高級別**：只允許**低於**該級別的彩票組合，從而**消除**所有**不在**該組合範圍內的內容。
    - 這兩種設定方式在效率上是相同的，效率指的是過濾器所消除的彩票組合數量。
- **過濾器的效率與驗證**：
    
    - **效率**：指過濾器所消除的彩票組合的數量。
    - 要確定特定過濾器的效率，可以將其最低和最高等級都設定為 1，並在字典生成模式下運行軟體來計算被消除的組合數量。
    - 例如，對於 6/49 樂透遊戲，如果將 `Two`（配對）過濾器的最低等級設定為 1，它將消除 1,851,150 個組合。
- **常見的過濾器類型及其減少組合的方式**：
    
    - **個體數字組過濾器 (ONE, TWO, THREE, FOUR, FIVE, SIX)**：這些過濾器針對單個號碼、配對、三元組、四元組、五元組和六元組的重複情況進行篩選。例如，`ONE` 過濾器若設為最低等級 1，將消除上次開獎中出現的每個單一樂透號碼，從而減少用於生成新組合的號碼池。
    - **位置性過濾器 (Any/Ver filters)**：`Any` 過濾器適用於任何位置的開獎號碼，而 `Ver` 過濾器則針對特定位置的號碼。例如，`Any 1` 值為零表示該期開獎中有至少一個號碼與上一期重複，這代表無需「跳過」任何開獎即可找到重複號碼。
    - **跳躍過濾器 (Skip filters)**：基於號碼在兩次開獎之間**未出現的次數**（跳躍次數）來設定。結合 FFG 中位數（50% 確定性所需的跳躍次數），玩家可以將投注範圍限制在那些當前跳躍次數**小於或等於** FFG 中位數的號碼上。歷史數據分析顯示，這種策略能顯著提高中獎機率（例如在 6/49 樂透中提高七倍）。
    - **增量過濾器 (Deltas filters)**：分析相鄰樂透號碼之間的差值。通過設定增量值（例如，排除任何超過 20 或 30 的增量值），可以大幅減少組合。
    - **頻率過濾器**：基於號碼、配對或其他模式的歷史出現頻率來篩選組合。
    - **「謊言消除」(LIE Elimination) — 反向策略**：這是一種**反向彩票策略**，旨在**有意設定預計不會中獎的過濾器**，從而將極不可能中獎的組合**從投注池中剔除**。例如，將在下一次開獎中極少出現的特定模式（如某些配對、十年組或跳躍組）納入「謊言檔案」（LIE file），並透過「清除」（Purge）功能從總組合中剔除。Ion Saliu 的「謊言消除」策略的錯誤率據稱不超過 1%。
- **FFG 中位數與動態過濾**：
    
    - **FFG 中位數**：這是衡量過濾器**「正常範圍」**的關鍵參數，代表事件在 50% 確定性下至少發生一次所需的試驗次數。最新的彩票軟體可以直接為用戶計算出中位數。
    - **動態調整**：FFG 強調彩票是一個**動態過程**，因此過濾器應**動態調整**。透過觀察過濾器數值的趨勢（例如連續三次增加後常會出現反轉），並結合 FFG 的確定性程度概念（例如 90% 確定性會發生趨勢反轉），可以更精準地設定過濾器等級，從而**嚴格消除**大量低機率組合。這與靜態機率論（所有組合機率均等）形成對比，FFG 證明了在動態過程中，組合的出現頻率並不均等。
    - **鐘形曲線**：FFG 理論表明，中獎組合主要來自 FFG 中位數附近的「鐘形曲線」區域。將組合生成範圍限定在此區域內，可以提高中獎率.
- **軟體支援**：
    
    - Ion Saliu 的彩票軟體套件，如 `LotWon`、`MDIEditor Lotto WE`、`Bright Software` 和 `Ultimate Software`，以及特定的工具如 `SuperFormula`、`SkipSystem`、`Deltas` 和 `FileLines`，都內建了這些強大的過濾與組合減少功能。這些軟體能生成詳細的報告（如 `W*`, `MD*`, `SK*` 報告），幫助用戶分析數據並制定策略。