# Debug FFG Calculation

using Dates

# Include necessary types and functions
include("src/types.jl")
include("src/ffg_calculator.jl")

println("=== FFG Calculation Debug ===")

# Test different DC values
dc_values = [0.25, 0.5, 0.75, 0.9]
p = 5.0 / 39.0

println("Probability p = $p")
println()

for dc in dc_values
    println("DC = $dc ($(Int(dc*100))%)")
    
    # Manual calculation
    manual_result = log(1 - dc) / log(1 - p)
    println("  Manual: log(1-$dc) / log(1-$p) = $manual_result")
    
    # Using FFGCalculator
    calc = FFGCalculator(dc)
    calc_result = calculate_theoretical_ffg_median(calc)
    println("  Calculator: $calc_result")
    
    # Check intermediate values
    log_1_minus_dc = log(1 - dc)
    log_1_minus_p = log(1 - p)
    println("  log(1-DC) = $log_1_minus_dc")
    println("  log(1-p) = $log_1_minus_p")
    println("  -log(1-DC) = $(-log_1_minus_dc)")
    println("  Ratio = $(-log_1_minus_dc / log_1_minus_p)")
    println("  max(1.0, ratio) = $(max(1.0, -log_1_minus_dc / log_1_minus_p))")
    println()
end

# Test the specific case that should work (DC=50%)
println("=== Special case: DC=50% ===")
calc_50 = FFGCalculator(0.5)
result_50 = calculate_theoretical_ffg_median(calc_50)
manual_50 = log(0.5) / log(1 - p)

println("DC=50% manual: $manual_50")
println("DC=50% calculator: $result_50")
println("Should be approximately 5.05")
