# FIVE Filter Implementation
# FIVE 過濾器實現 - 分析完整五號組合的重複性

using Dates
using Statistics

# 引入必要的模組
include("../types.jl")
include("../filter_engine.jl")

"""
生成號碼組合的所有五號組合
從給定的號碼列表中生成所有可能的五號組合
"""
function generate_quintuplets(numbers::Vector{Int})::Vector{Tuple{Int, Int, Int, Int, Int}}
    if length(numbers) < 5
        return Tuple{Int, Int, Int, Int, Int}[]
    end
    
    quintuplets = Tuple{Int, Int, Int, Int, Int}[]
    n = length(numbers)
    
    for i in 1:n-4
        for j in i+1:n-3
            for k in j+1:n-2
                for l in k+1:n-1
                    for m in l+1:n
                        # 確保號碼按升序排列
                        quintuplet = (numbers[i], numbers[j], numbers[k], numbers[l], numbers[m])
                        push!(quintuplets, quintuplet)
                    end
                end
            end
        end
    end
    
    return quintuplets
end

"""
檢查組合唯一性
檢查給定的五號組合是否在歷史數據中出現過
"""
function check_combination_uniqueness(engine::FilterEngine, combination::Vector{Int})::Dict{String, Any}
    if length(combination) != 5
        throw(ArgumentError("組合必須包含正好 5 個號碼"))
    end
    
    # 排序組合以便比較
    sorted_combo = sort(combination)
    
    # 檢查歷史數據中是否有完全相同的組合
    exact_matches = 0
    similar_matches = 0  # 4個號碼相同的組合
    last_occurrence = 0
    
    for (i, draw) in enumerate(engine.historical_data)
        sorted_draw = sort(draw.numbers)
        
        # 檢查完全匹配
        if sorted_combo == sorted_draw
            exact_matches += 1
            if last_occurrence == 0
                last_occurrence = i
            end
        end
        
        # 檢查相似匹配（4個號碼相同）
        common_numbers = length(intersect(sorted_combo, sorted_draw))
        if common_numbers == 4
            similar_matches += 1
        end
    end
    
    return Dict(
        "combination" => sorted_combo,
        "is_unique" => exact_matches == 0,
        "exact_matches" => exact_matches,
        "similar_matches" => similar_matches,
        "last_occurrence_index" => last_occurrence,
        "uniqueness_score" => exact_matches == 0 ? 1.0 : 1.0 / exact_matches
    )
end

"""
分析重複組合模式
分析歷史數據中的重複組合模式
"""
function analyze_duplicate_combinations(engine::FilterEngine)::Dict{String, Any}
    if isempty(engine.historical_data)
        return Dict("error" => "無歷史數據")
    end
    
    # 統計重複組合
    combination_counts = Dict{Vector{Int}, Int}()
    
    for draw in engine.historical_data
        sorted_numbers = sort(draw.numbers)
        combination_counts[sorted_numbers] = get(combination_counts, sorted_numbers, 0) + 1
    end
    
    # 分析重複模式
    unique_combinations = count(count -> count == 1, values(combination_counts))
    duplicate_combinations = count(count -> count > 1, values(combination_counts))
    max_repetitions = maximum(values(combination_counts))
    
    # 找出最常出現的組合
    most_frequent = []
    for (combo, count) in combination_counts
        if count == max_repetitions && count > 1
            push!(most_frequent, combo)
        end
    end
    
    return Dict(
        "total_combinations" => length(combination_counts),
        "unique_combinations" => unique_combinations,
        "duplicate_combinations" => duplicate_combinations,
        "uniqueness_rate" => unique_combinations / length(combination_counts),
        "max_repetitions" => max_repetitions,
        "most_frequent_combinations" => most_frequent,
        "average_repetition" => mean(values(combination_counts))
    )
end

"""
計算組合重複機率
基於歷史數據計算組合重複的機率
"""
function calculate_repetition_probability(engine::FilterEngine)::Float64
    if isempty(engine.historical_data)
        return 0.0
    end
    
    duplicate_analysis = analyze_duplicate_combinations(engine)
    
    # 計算重複機率
    duplicate_rate = get(duplicate_analysis, "uniqueness_rate", 1.0)
    repetition_probability = 1.0 - duplicate_rate
    
    return repetition_probability
end

"""
計算五號組合信心水準
基於唯一性和重複分析
"""
function calculate_five_combination_confidence(engine::FilterEngine, combination::Vector{Int})::Float64
    if length(combination) != 5 || isempty(engine.historical_data)
        return 0.0
    end
    
    # 檢查組合唯一性
    uniqueness = check_combination_uniqueness(engine, combination)
    
    # 基於唯一性的信心
    uniqueness_confidence = get(uniqueness, "uniqueness_score", 0.0)
    
    # 基於相似組合數量的信心調整
    similar_matches = get(uniqueness, "similar_matches", 0)
    similarity_penalty = min(0.5, similar_matches * 0.1)  # 相似組合越多，信心越低
    
    # 基於樣本大小的信心
    sample_confidence = min(1.0, length(engine.historical_data) / 200.0)
    
    # 綜合信心水準
    final_confidence = (uniqueness_confidence - similarity_penalty) * sample_confidence
    
    return clamp(final_confidence, 0.0, 1.0)
end

"""
判斷五號組合是否有利
基於唯一性和重複分析判斷
"""
function is_five_combination_favorable(engine::FilterEngine, combination::Vector{Int})::Bool
    if length(combination) != 5
        return false
    end
    
    uniqueness = check_combination_uniqueness(engine, combination)
    
    # 有利條件：
    # 1. 組合是唯一的（從未出現過）
    is_unique = get(uniqueness, "is_unique", false)
    
    # 2. 相似組合不要太多
    similar_matches = get(uniqueness, "similar_matches", 0)
    reasonable_similarity = similar_matches <= 3
    
    # 3. 整體重複機率不要太高
    repetition_prob = calculate_repetition_probability(engine)
    low_repetition_risk = repetition_prob <= 0.1
    
    return is_unique && reasonable_similarity && low_repetition_risk
end

"""
計算 FIVE 過濾器結果
分析給定五號組合的唯一性和重複性
"""
function calculate_five_filter(engine::FilterEngine, combination::Vector{Int})::FilterResult
    start_time = time()
    
    if length(combination) != 5
        throw(ArgumentError("FIVE 過濾器需要正好 5 個號碼，得到: $(length(combination))"))
    end
    
    if isempty(engine.historical_data)
        throw(ArgumentError("歷史數據不能為空"))
    end
    
    # 驗證號碼範圍
    if !all(1 <= n <= 39 for n in combination)
        throw(ArgumentError("所有號碼必須在 1-39 範圍內"))
    end
    
    # 檢查快取
    cache_key = "five_filter_$(sort(combination))"
    if engine.cache_enabled && haskey(engine.filter_cache, cache_key)
        cached_result = engine.filter_cache[cache_key]
        @info "使用快取結果: FIVE 過濾器，號碼 $(combination)"
        return cached_result
    end
    
    try
        # 檢查組合唯一性
        uniqueness = check_combination_uniqueness(engine, combination)
        
        # 計算當前值（唯一性分數）
        current_value = Int(round(get(uniqueness, "uniqueness_score", 0.0) * 100))
        
        # 計算期望值（基於重複機率）
        repetition_prob = calculate_repetition_probability(engine)
        expected_value = (1.0 - repetition_prob) * 100  # 期望的唯一性分數
        
        # 判斷是否有利
        is_favorable = is_five_combination_favorable(engine, combination)
        
        # 計算信心水準
        confidence = calculate_five_combination_confidence(engine, combination)
        
        # 獲取歷史重複數據
        duplicate_analysis = analyze_duplicate_combinations(engine)
        repetition_counts = [get(duplicate_analysis, "max_repetitions", 1)]
        
        # 計算執行時間
        calculation_time = time() - start_time
        
        # 創建結果
        result = FilterResult(
            "FIVE_FILTER_uniqueness",
            FIVE_FILTER,
            current_value,
            expected_value,
            is_favorable,
            confidence,
            repetition_counts,
            calculation_time
        )
        
        # 儲存到快取
        if engine.cache_enabled
            engine.filter_cache[cache_key] = result
            manage_cache_size!(engine)
        end
        
        return result
        
    catch e
        @error "計算 FIVE 過濾器時發生錯誤" combination=combination error=e
        rethrow(e)
    end
end

"""
獲取最佳唯一組合建議
基於唯一性分析提供組合建議
"""
function get_best_unique_combinations(engine::FilterEngine, candidate_numbers::Vector{Int}, top_n::Int = 5)::Vector{Dict{String, Any}}
    if length(candidate_numbers) < 5
        return Dict{String, Any}[]
    end
    
    # 生成所有可能的五號組合
    quintuplets = generate_quintuplets(candidate_numbers)
    
    if isempty(quintuplets)
        return Dict{String, Any}[]
    end
    
    analyses = Dict{String, Any}[]
    
    for quintuplet in quintuplets
        combination = [quintuplet[1], quintuplet[2], quintuplet[3], quintuplet[4], quintuplet[5]]
        uniqueness = check_combination_uniqueness(engine, combination)
        confidence = calculate_five_combination_confidence(engine, combination)
        
        push!(analyses, Dict(
            "combination" => combination,
            "is_unique" => get(uniqueness, "is_unique", false),
            "uniqueness_score" => get(uniqueness, "uniqueness_score", 0.0),
            "similar_matches" => get(uniqueness, "similar_matches", 0),
            "confidence" => confidence,
            "recommendation_score" => get(uniqueness, "uniqueness_score", 0.0) * confidence
        ))
    end
    
    # 按推薦分數排序
    sort!(analyses, by = x -> x["recommendation_score"], rev = true)
    
    return analyses[1:min(top_n, length(analyses))]
end

"""
計算 FIVE 過濾器的統計摘要
"""
function get_five_filter_summary(results::Vector{FilterResult})::Dict{String, Any}
    if isempty(results)
        return Dict("error" => "無結果數據")
    end
    
    # 篩選 FIVE 過濾器結果
    five_filter_results = filter(r -> r.filter_type == FIVE_FILTER, results)
    
    if isempty(five_filter_results)
        return Dict("error" => "無 FIVE 過濾器結果")
    end
    
    favorable_count = count(r -> r.is_favorable, five_filter_results)
    confidence_levels = [r.confidence_level for r in five_filter_results]
    uniqueness_scores = [r.current_value for r in five_filter_results]
    expected_values = [r.expected_value for r in five_filter_results]
    
    return Dict(
        "total_combinations" => length(five_filter_results),
        "favorable_combinations" => favorable_count,
        "unique_combinations" => count(r -> r.current_value >= 90, five_filter_results),
        "average_confidence" => mean(confidence_levels),
        "average_uniqueness_score" => mean(uniqueness_scores),
        "average_expected_uniqueness" => mean(expected_values),
        "uniqueness_distribution" => Dict(
            "min" => minimum(uniqueness_scores),
            "max" => maximum(uniqueness_scores),
            "median" => median(uniqueness_scores)
        )
    )
end

"""
檢查組合的歷史相似性
檢查給定組合與歷史組合的相似程度
"""
function check_historical_similarity(engine::FilterEngine, combination::Vector{Int})::Dict{String, Any}
    if length(combination) != 5
        return Dict("error" => "組合必須包含 5 個號碼")
    end
    
    sorted_combo = sort(combination)
    similarity_analysis = Dict{Int, Int}()  # 相同號碼數量 -> 出現次數
    
    for draw in engine.historical_data
        sorted_draw = sort(draw.numbers)
        common_count = length(intersect(sorted_combo, sorted_draw))
        similarity_analysis[common_count] = get(similarity_analysis, common_count, 0) + 1
    end
    
    return Dict(
        "combination" => sorted_combo,
        "similarity_distribution" => similarity_analysis,
        "exact_matches" => get(similarity_analysis, 5, 0),
        "four_matches" => get(similarity_analysis, 4, 0),
        "three_matches" => get(similarity_analysis, 3, 0),
        "total_analyzed" => length(engine.historical_data)
    )
end

# 導出函數
export calculate_five_filter, generate_quintuplets, check_combination_uniqueness
export analyze_duplicate_combinations, calculate_repetition_probability
export get_best_unique_combinations, get_five_filter_summary
export check_historical_similarity
