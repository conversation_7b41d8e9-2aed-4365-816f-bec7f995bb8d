using WonderGridLotterySystem
using Statistics
using Dates

println("Enhanced FFG Calculator Testing")
println("=" ^ 50)

# Load lottery data
fm = FileManager()
data = read_data5_file(fm, "data/fan5.csv")
println("Loaded $(length(data)) lottery draws for enhanced FFG analysis")

# Test different degree of certainty values
println("\n1. Testing Different Degree of Certainty Values")
println("=" ^ 50)

test_number = 14
dc_values = [0.25, 0.5, 0.75, 0.9]

println("Testing number $test_number with different DC values:")
for dc in dc_values
    calc = FFGCalculator(dc)
    ffg_median = calculate_ffg_median(calc, test_number, data)
    
    # Get current skip
    analyzer = SkipAnalyzer(data)
    current_skip = get_current_skip(analyzer, test_number)
    
    skip_prob = compute_skip_probability(calc, current_skip, ffg_median)
    is_favorable = is_favorable_timing(calc, test_number, current_skip, data)
    
    println("  DC $(Int(dc*100))%: FFG median $(round(ffg_median, digits=2)), skip prob $(round(skip_prob * 100, digits=1))%, favorable: $(is_favorable)")
end

# Test theoretical vs empirical comparison
println("\n2. Theoretical vs Empirical FFG Analysis")
println("=" ^ 50)

calc = FFGCalculator()
theoretical_median = calculate_theoretical_ffg_median(calc)
println("Theoretical FFG median for Lotto 5/39: $(round(theoretical_median, digits=2))")

# Calculate empirical medians for all numbers
empirical_medians = Float64[]
for number in 1:39
    empirical_median = calculate_ffg_median(calc, number, data)
    push!(empirical_medians, empirical_median)
end

mean_empirical = mean(empirical_medians)
println("Average empirical FFG median: $(round(mean_empirical, digits=2))")
println("Difference: $(round(mean_empirical - theoretical_median, digits=2))")
println("Ratio (empirical/theoretical): $(round(mean_empirical / theoretical_median, digits=2))")

# Test dynamic recalculation
println("\n3. Dynamic Recalculation Testing")
println("=" ^ 50)

# Create a subset of data for testing
test_data = data[1:500]  # Use first 500 draws
calc_dynamic = FFGCalculator()

# Calculate initial FFG median
initial_median = calculate_ffg_median(calc_dynamic, test_number, test_data)
println("Initial FFG median for number $test_number: $(round(initial_median, digits=2))")

# Add more data and recalculate
extended_data = data[1:800]  # Add 300 more draws
update_ffg_calculations!(calc_dynamic, extended_data)
updated_median = calculate_ffg_median(calc_dynamic, test_number, extended_data)
println("Updated FFG median after adding data: $(round(updated_median, digits=2))")
println("Change: $(round(updated_median - initial_median, digits=2))")

# Test FFG analysis for all numbers
println("\n4. Complete FFG Analysis")
println("=" ^ 50)

analysis = get_ffg_analysis(calc, data)

println("Top 10 most favorable numbers:")
favorable_numbers = [(num, info["favorability_score"]) for (num, info) in analysis if info["is_favorable"] == 1.0]
sort!(favorable_numbers, by = x -> x[2])

for i in 1:min(10, length(favorable_numbers))
    num, score = favorable_numbers[i]
    info = analysis[num]
    println("  $i. Number $num: skip $(Int(info["current_skip"])) vs median $(round(info["ffg_median"], digits=1)), prob $(round(info["skip_probability"]*100, digits=1))%")
end

println("\nTop 5 least favorable numbers:")
unfavorable_numbers = [(num, info["favorability_score"]) for (num, info) in analysis if info["is_favorable"] == 0.0]
sort!(unfavorable_numbers, by = x -> x[2])

for i in 1:min(5, length(unfavorable_numbers))
    num, score = unfavorable_numbers[i]
    info = analysis[num]
    println("  $i. Number $num: skip $(Int(info["current_skip"])) vs median $(round(info["ffg_median"], digits=1)), prob $(round(info["skip_probability"]*100, digits=1))%")
end

println("\nSummary: $(length(favorable_numbers)) favorable, $(length(unfavorable_numbers)) unfavorable numbers")

# Test optimal key number selection
println("\n5. Optimal Key Number Selection")
println("=" ^ 50)

optimal_numbers = find_optimal_key_numbers(calc, data, 10)
println("Top 10 optimal key number candidates:")

for i in 1:length(optimal_numbers)
    num, score = optimal_numbers[i]
    info = analysis[num]
    println("  $i. Number $num: score $(round(score, digits=3)), skip $(Int(info["current_skip"])), median $(round(info["ffg_median"], digits=1))")
end

# Test confidence intervals
println("\n6. FFG Confidence Intervals")
println("=" ^ 50)

test_numbers = [1, 14, 27, 39]
println("95% confidence intervals for selected numbers:")

for number in test_numbers
    lower, upper = calculate_ffg_confidence_interval(calc, number, data, 0.95)
    median_val = calculate_ffg_median(calc, number, data)
    println("  Number $number: $(round(lower, digits=2)) - $(round(upper, digits=2)) (median: $(round(median_val, digits=2)))")
end

# Test validation
println("\n7. FFG Calculation Validation")
println("=" ^ 50)

validation = validate_ffg_calculations(calc, data)
println("Validation Results:")
println("  Theoretical median: $(round(validation["theoretical_median"], digits=2))")
println("  Mean empirical median: $(round(validation["mean_empirical_median"], digits=2))")
println("  Standard deviation: $(round(validation["std_empirical_median"], digits=2))")
println("  Deviation ratio: $(round(validation["deviation_ratio"], digits=3))")
println("  Validation passed: $(validation["validation_passed"])")
println("  Empirical range: $(round(validation["empirical_range"][1], digits=2)) - $(round(validation["empirical_range"][2], digits=2))")

# Performance test with new functions
println("\n8. Performance Testing")
println("=" ^ 50)

println("Testing performance of new FFG functions...")

# Test FFG analysis performance
start_time = time()
analysis_result = get_ffg_analysis(calc, data)
analysis_time = time() - start_time
println("  Complete FFG analysis: $(round(analysis_time, digits=3)) seconds")

# Test optimal key number selection performance
start_time = time()
optimal_result = find_optimal_key_numbers(calc, data, 15)
optimal_time = time() - start_time
println("  Optimal key number selection: $(round(optimal_time, digits=3)) seconds")

# Test confidence interval calculation performance
start_time = time()
for number in 1:10
    calculate_ffg_confidence_interval(calc, number, data)
end
ci_time = time() - start_time
println("  Confidence intervals (10 numbers): $(round(ci_time, digits=3)) seconds")

# Test validation performance
start_time = time()
validation_result = validate_ffg_calculations(calc, data)
validation_time = time() - start_time
println("  FFG validation: $(round(validation_time, digits=3)) seconds")

println("\nEnhanced FFG Calculator testing completed successfully!")