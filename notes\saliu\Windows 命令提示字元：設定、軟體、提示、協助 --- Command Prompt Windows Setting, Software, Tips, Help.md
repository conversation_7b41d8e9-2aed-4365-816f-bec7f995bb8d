---
created: 2025-07-24T22:42:35 (UTC +08:00)
tags: [command prompt,Windows,XP,Vista,Windows 7,Windows 8,Windows 10,32-bit,64-bit,lottery,programs,lotto,software,computer,files,]
source: https://saliu.com/gambling-lottery-lotto/command-prompt.htm
author: 
---

# Windows 命令提示字元：設定、軟體、提示、協助 --- Command Prompt Windows: Setting, Software, Tips, Help

> ## Excerpt
> Set Command Prompt environment in all 32-bit and 64-bit versions of Windows to assure the best error-free performance, ease of use, nice screens.

---
這個_命令提示符號_有什麼好大驚小怪的，竟然會嚇到我們這代人？最重要的是，從我的角度來看，命令提示字元嚇壞了我彩票軟體的很大一部分用戶。就連老舊的 DOS 系統都沒那麼嚇人…

_**命令提示字元**_是微軟使用的委婉說法。 32 位元 Windows 95/98 擁有明確定義的 DOS 提示字元。使用者可以直接啟動到 DOS，從而繞過美觀但運行緩慢的圖形介面。另一方面，微軟進行了頑強抗爭，包括在反壟斷訴訟中，聲稱 Windows 是一個全新的作業系統，與被稱為 DOS 的字元模式作業系統完全獨立，等等。

我個人（以及全世界的眾多信徒）認為，每家電腦製造商（硬體製造商）都必須為電腦買家提供開箱即用的啟動管理器。使用者應該可以選擇啟動任何電腦作業系統。作業系統的擁有者必須與任何其他類型軟體的創建/所有權分開。如果作業系統的創建者/所有者創建並擁有其他類型的軟體，則此類軟體必須免費（完全免費）包含在作業系統中。例如，如果作業系統的創建者/所有者創建了一套優秀的軟體應用程序，例如 Office 2007，那麼 Office 2007 必須免費包含在作業系統中。這項法律規定將立即打破壟斷……無需支付任何法律費用或任何延誤！

否則，作業系統只不過是推銷其他軟體的宣傳手段，通常對電腦買家來說成本高。人們創建/更新作業系統版本只是為了銷售不屬於作業系統的新版本軟體包。你賣 DOS 沒賺到多少錢吧？ ！那麼，低價出售 Windows……但要迫使 PC 買家……也買 Office！因為除了你之外，沒有誰能為最新的酷炫 Windows 撰寫應用軟體！

事實上，所有 Windows 版本都是具有圖形介面 (GUI) 的 DOS。但是，如果沒有那些隱藏在幕後強大的功能——也就是 DOS，GUI 幾乎無法發揮作用。即使在今天，64 位元 Vista / Windows 7、8、10 的許多任務也只能在字元模式下執行。試試檢查你的磁碟－Windows 7 會關閉炫目的燈光，然後啟動 _CHKDSK_ …嗯，是在_字元模式_下！

我一直很喜歡 _**DOS**_ …不好意思… _**命令提示字元**_ …因為它功能強大。它比 _**GUI**_ 強太多了，尤其是在我的彩票軟體這種情況下。 GUI _**版本**_經常更改其物件和元件（例如_對話方塊_ ），從而導致不相容。另一方面，我的 32 位元_**命令提示字元**_彩票軟體與所有 32 位元和 64 位元版本的 Windows 系統（從 _Windows 95_ 到 _Win 10）_ 都能完美相容。幹得好， _Parpaluck_ ，不言而喻！你值得 **10 分** ，就像 _2015 年的 Windows 10_ 一樣……從此以後，一切安好！

![Notes to using the Command Prompt in Windows XP, Vista, Windows 7 or Win8.](https://saliu.com/gambling-lottery-lotto/HLINE.gif)

首先，那個神秘的命令提示字元在哪裡？ _命令提示字元_本身隱藏得很好！您可以使用 Windows 資源管理器找到它。開啟 Windows 資料夾（在您的主硬碟上，可能是 **C:** ），然後開啟 _System32_ 資料夾。向下捲動到名為 _**cmd**_ 的行。如果您可以看到_桌面_ ，則可以右鍵單擊 _**cmd**_ ，按住滑鼠並將 _**cmd**_ 拖曳到桌面。放開滑鼠按鈕，然後選擇_在此處建立捷徑_ 。或者，右鍵單擊 _cmd_ ， _發送到_ ，然後_選擇 桌面（建立快捷方式）_ 。

你也可以點選 _「開始」_ 、 _「程式」_ 、 _「附件」_ 來找到 _**「命令提示字元」**_ 。右鍵點選 _「命令提示字元」_ ，然後選擇 _「傳送到」_ ，再選擇 _「桌面（建立捷徑）」_ 。

-   微軟把 Windows 8 打造成了一個糟糕的作業系統…乍看之下。它引入了 _Metro 介面_ ，這種介面適合小孩子，而不是真正的電腦用戶。 Metro 繞過了 Windows 7 中一直存在的、功能強大的_桌面_ 。許多 Windows 8 使用者必須使用各種實用程式來恢復 Windows _7_ 的 _「開始」_ 按鈕，並直接啟動到 _「桌面」_ 。我個人使用的是免費的 _「開始選單 8」_ 。微軟意識到了這個大錯誤，並發布了帶有 _「開始_ 」按鈕的 Windows 8.1（但功能不如 Windows 7）。
-   對於購買了 Windows 8 電腦且缺乏相關知識的使用者來說，使用一些重要的軟體會非常困難。使用者必須在_桌面_上安裝和執行重要的軟體。 _桌面_上沒有快捷方式（這在 Windows 7 中非常有用），甚至連必不可少的 _Windows 資源管理器_都沒有。使用者必須找到 _C:\\WINDOWS 目錄_ ，然後_找到 explorer_ ；右鍵點選 _explorer_ ，然後_傳送到__桌面（建立捷徑）_ 。必不可少的_控制面板_位於 _C:\\WINDOWS\\SYSTEM32 目錄_中，名稱為 _control_ 。

您現在位於_桌面_ （關閉 _Windows 資源管理器_後）。將捷徑重新命名為 _“命令提示字元”，_ 只需 **“命令提示字元”** 。右鍵點選 **“命令提示字元”** 。選擇 _“屬性”_ 。以下是我自訂 _**「命令提示字元」的**_方法。

-   顏色：螢幕文字： _黃色_ ；背景：先_藍_後_綠_ 。
-   快捷方式選項卡： _從 C:\\ 開始運行：最大化_ 。
-   字型選項卡：12 x 16 光柵字型。

### 最佳命令提示字元技巧

-   在桌面上建立 %SystemRoot%\\system32\\cmd 的捷徑；  
    （ **CMD.exe** 是真正的命令提示字元）；  
    
-   右鍵單擊快捷方式，然後選擇_屬性_ ；  
    
-   在_選項_中選擇_全螢幕_ （同時按住\[Alt\]和\[Enter\]也可以實現全螢幕）；  
    
-   在_佈局_中， _視窗大小_選擇寬度=80，高度=25；  
    
-   在_顏色_中選擇螢幕文字=黃色和螢幕背景=藍色；  
    
-   儲存並選擇將設定應用於所有應用程式。

注意：Windows XP 允許以全螢幕模式執行_命令提示字元_ 。這是我最喜歡的設定。但是我們在 Vista 或 Windows 7 中能做什麼呢？當你擁有了這種能力，你就可以決定讓某些操作對許多電腦使用者來說有多難，甚至有多不可能！

讓我們來看看我自己的命令提示字元（64 位元 Windows 7 Ultimate）：

![Software at the Command Prompt in Windows XP, Vista, Windows 7, 8, 32-bit, 64-bit.](https://saliu.com/ScreenImgs/command-prompt-1.gif)

![All information needed to under-the-hood operate the operating system – black-belt Windows.](https://saliu.com/ScreenImgs/command-prompt-2.gif)

![Command Prompt in Windows is the indispensable DOS with its powerful commands.](https://saliu.com/ScreenImgs/command-prompt-3.gif)

![Lottery, lotto software runs much faster at the Command Prompt in Windows XP, Win7, 8.](https://saliu.com/gambling-lottery-lotto/HLINE.gif)

我只能告訴你這麼多。圖形介面確實有優勢，尤其是在處理文件時（但不是像 DOS 的 **XCOPY** 或 **MOVE** 命令那樣複雜的任務）。我們可以利用 Windows 資源管理器建立資料夾（以前稱為目錄）。我假設你的主驅動器是 **C:** 。雙擊 C: 碟。它的內容將填滿資源管理器的右側。你移動到該窗格，然後一直移動到底部（向下滾動或同時按下 _Ctrl_ 和 _End_ 鍵）。

在空白處按一下滑鼠右鍵，然後從隨後出現的選單中選擇 _「新建」_ （或按 _W_ ）。接下來，選擇 _“資料夾”_ （或按 _F_ ）。您將看到一個新條目： _新資料夾_ （突出顯示）。覆蓋新資料夾的名稱。假設您建立了一個通用的下載資料夾。因此，輸入 _“下載”_ 。接下來，建立一個名為 _BAT_ 的新資料夾 - 用於存放一些有用的批次檔。然後，您可以為每個 _**LotWon**_ 樂透和彩票軟體建立一個新資料夾。例如： _Bright3_ （用於選 3 彩券）、 _Bright6_ （用於 6 號彩券）等。我建議您從我的網站將特定的彩票軟體下載到您電腦上的相應資料夾中。

我們在圖形使用者介面（GUI）中停留了一會兒。我們需要建立一些有用的批次文件，以使命令提示字元環境更有效率。只要打開那個默默無聞的記事本即可。批次檔必須是文字格式（電腦檔案的純文字格式）。我有一個名為 _MA.BAT_ 的批次檔（ _MA_ 表示_巨集_ ）。您可以為_命令提示_字元中的每個命令建立巨集。只需輸入以下命令即可了解更多：

_**C:\\>HELP|MORE**_ （| 位於鍵盤上 \\ 鍵上方）。按任意鍵可一次瀏覽一螢幕幫助資訊（ **MORE 的**作用就是此）。

但現在，您無需鍵入，而是可以複製並貼上以下行：

```
<span size="5" face="Courier New" color="#c5b358"><span lang="zh-TW"><span data-immersive-translate-translation-element-mark="1"><span data-immersive-translate-translation-element-mark="1">@ECHO 關閉
路徑 C:\;C:\BAT\;C:\BAS\;C:\LOTTERY\;%PATH%
doskey co=複製 $1 $2
doskey d=dir /O:N /P
REM doskey da=dir A: /O:N /P
doskey dc=dir C: /O:N /P
doskey dh=dir H: /O:N /P
doskey de=dir E: /O:N /P
REM 以下 re 是替換實用程式
doskey re=xcopy $1 $2 /D /U /Y
doskey bk=attrib +A $1$Txcopy $1 $2 /M /-Y
doskey xc=xcopy $1 $2 /M /-Y
doskey x=退出
REM doskey bcg=bellcurvegenerator
REM doskey per=cd\$Tcd\bas$TPermuteCombine
REM doskey lex=cd\$Tcd\bas$TLexicographicSets
REM doskey sf=cd\$Tcd\bas$TSuperFormula
REM doskey fl=C:\bas\filelines
REM doskey bp=C:\bas\BirthdayParadox
REM doskey col=C:\bas\Collisions
REM doskey osp=C:\bas\OccupancySaliuParadox
</span></span></span></span>
```

**PATH C:\\;C:\\BAT\\;C:\\BAS\\;C:\\LOTTERY\\;%PATH%** 是批次檔中最重要的一行。在 Windows 2000 之前的版本中，PATH 是透過 _AUTOEXEC.BAT_ 設定的。現在，電腦使用者不再可以使用 _AUTOEXEC.BAT_ 。相反，Windows 會自動設定 **PATH** ；其值為 **%PATH%** 。我們的批次檔只是在 Windows 自動設定的 **%PATH%** 中新增了更多條目。您可以在批次檔 _MA.BAT_ 的主行中新增您自己的資料夾。例如： _C:\\BRIGHT3\\;C:\\BRIGHT6\\;_ 等等。但請記住 \\ 和分隔符號 ;。

每次啟動命令提示字元會話時，您都可以在 **C:** 提示字元下輸入 _MA_ （或 _ma_ ）。批次檔也為 _DOSKEY_ 公用程式（由 Windows 自動載入）建立了一些實用的捷徑。例如，功能強大的實用程式 _**PermuteCombine**_ （用於產生排列、組合等）位於 BAS 資料夾中。我只需輸入 _per_ 即可啟動該應用程式。

將檔案儲存在記事本中（有時可能需要用「 」括起名稱，以確保它儲存的是帶有 BAT 副檔名的文字檔案）：  
名稱：「 _MA.BAT_ 」位於磁碟機 **C:（** 根目錄）中；  
保存類型：所有文件；  
編碼：ANSI。

在記事本中將此檔案儲存為 _L3.BAT_ ，但這次儲存在 C:\\BAT\\ 資料夾中。我們在 **CALL** 3.BAT 指令前面新增了 **REM** ，因為 CALL 指令在 64 位元 Vista 系統下無法正常運作（它會造成嚴重破壞！）。 _REM_ 指令只有一個作用：停用其後的 DOS 指令；也就是說，該行的其餘部分將無法運作。

您可以為 _Bright6_ ( _L6_ ) 或任何其他 LotWon 軟體包建立類似的批次檔。啟動命令提示字元會話後，輸入 ma （並按 _Enter_ 鍵）；然後輸入 l3 （並按 Enter 鍵），您將自動導航至 _C:\\BRIGHT3\\_ 資料夾。

您可以使用 CD 命令（ _更改目錄_ ）在命令提示字元中導航到任意位置。例如，您目前位於 **C:** 提示字元下。若要導覽至 _C:\\BRIGHT3\\_ ，請鍵入：

**cd\\bright3**

如果要從 **C:\\BRIGHT3\\** 回到根目錄 **C:，** 請輸入：

**光碟\\**

![Download lottery, lotto software to special folders created in Windows Explorer.](https://saliu.com/gambling-lottery-lotto/HLINE.gif)

有幾點觀察比較棘手。微軟在 _Windows Vista_ 中引入了臭名昭著的 _UAC（用戶帳戶控制）_ 。它是_控制面板_中的一個小程序，曾被_蘋果 Mac_ 電視廣告利用，讓 _Vista_ 淪為笑柄。要讓 Vista 正常運作，唯一的辦法就是停用 UAC。

_Windows 7、8、10_ 中新的 UAC 更加靈活。我將其設定為倒數第二個。它會詢問我是否允許某些程式對我的計算機進行更改：

_您是否允許以下程式對此電腦進行變更？_

我知道我在電腦上啟用的_**命令提示字元**_是安全的。因此，我右鍵單擊桌面上的_命令提示字元_捷徑。它打開了_快捷方式_選項卡。我點選 _“高級”_ 。然後，我取消選取 _「以管理員身分執行」_ 。

![Command Prompt of Windows has a plethora of useful functions and commands.](https://saliu.com/ScreenImgs/Windows-console.gif)

![Do not run Command prompt as Administrator of the system or PC.](https://saliu.com/ScreenImgs/Windows-DOS.gif)

否則，如果您_以管理員身分執行，_ _命令提示字元_始終會在 Windows 的 _System32_ 資料夾中開啟。我需要轉到根目錄（通常是 C:），然後輸入以下命令：

_**光碟\\**_

在 _**C:\\>**_ 提示字元下，我輸入以下內容執行正常操作：

_**C:\\>MA**_ 啟動巨集批次檔。然後，我輸入啟動 Bright 軟體包的批次檔；例如：

_**C:\\>B6**_ 為 lotto-6。

生活很簡單——只需稍加努力……就無所畏懼！

![Availability of Command Prompt in Windows XP, Windows 7, or Windows 8 is cumbersome by Microsoft.](https://saliu.com/gambling-lottery-lotto/HLINE.gif)

好了，現在您可以運行著名的 Bright 軟體了。首先，您需要下載軟體；例如 Bright3 。您可以從 SALIU.COM（如果您是註冊會員）下載 Bright3 到您的 C:\\BRIGHT3 資料夾，而不是通用的 C:\\DOWNLOAD 資料夾。

您可以在 Windows 資源管理器中解壓縮 BRIGHT3。導航至 C: 盤，然後找到 BRIGHT3 資料夾。雙擊該資料夾。移至右側窗格，然後雙擊 BRIGHT3。自動解壓縮將自動啟動。選擇相同的資料夾作為解壓縮檔案的位置。這將是您 pick-3 彩票軟體的永久工作空間。

您可以在_命令提示_字元下執行相同的操作。在 C:\\BRIGHT3 提示符號下，鍵入：

Bright3 並按 Enter。

解壓縮會自動開始，您可以選擇與目標相同的資料夾（目前目錄）。

此過程無需安裝程式（明亮的套件附帶 INSTALL.BAT 基本實用程式）。

_命令提示字元_隨時可用。雙擊桌面上的捷徑（命令提示字元）即可進入 C:\\ 提示字元。您需要導航到 BRIGHT3 資料夾。輸入：

cd \\bright3

或運行 L3.Bat（l3 並按 Enter）。

如果您使用的是 32 位元 Windows XP，則有兩種選擇來啟動 _**LotWon Bright**_ 軟體：-   批次檔選單的舊選擇；只需按 _**3**_ 然後_按 Enter_ 。  
    
-   新的和更好的選擇：輸入 _**B3**_ （或 b3）然後按 _Enter_ 。  
    _**_64 位元 Vista 或 Windows 7、8、10_ 用戶只能使用新方法：按 _b3_ 然後_按 Enter_ 。舊版 Bright 軟體包中包含一些 16 位元軟體。同樣，16 位元軟體無法在 64 位元作業系統下運作。**_

如果對雙重啟動（ _64 位元 Vista 或 Windows 7_ 與 _32 位元 Windows XP_ ）感興趣，請閱讀以下內容：

-   [_**64 位元 Windows Vista、Windows 7：與 32 位元 Windows XP 雙重啟動**_](https://saliu.com/gambling-lottery-lotto/lotwon-software-vista.htm) ；
-   [_**Microsoft Windows 7：正確完成的電腦作業系統**_](https://saliu.com/gambling-lottery-lotto/windows-7.htm) 。

微軟向 Windows 7 專業版、旗艦版和企業版用戶慷慨解囊。用戶可以免費下載舊版 Windows XP 軟體包。這允許用戶快速切換到 32 位元 Windows XP，從而運行 16 位元軟體。

-   強烈建議：輸入 _B3_ 來啟動 Bright3 pick-3 彩票包。
    
    我還包括了啟動程序的源代碼：B3.BAS。  
    這是一個許多程式設計師（尤其是 BASIC 程式設計師）都會喜歡的基本程式！
    
    之後，一切都清晰明了。你的選擇清晰明確。按下功能鍵或鍵盤上的普通按鍵，即可執行 Bright3 軟體包中的各種應用程式。
    
    ![Get help for the Command Prompt and lotto software for Windows XP, Vista, Win 7, 8.](https://saliu.com/ScreenImgs/pick31.gif)
    
    我知道，大多數用戶在談論這些細節時都會感到惱火。但是，請大家也考慮到新手的感受！仍然會有人抱怨他們不知道如何下載我的軟體、建立資料夾、導航到資料夾、使用命令提示字元、啟動批次檔等等。我們該怎麼辦？
    
    ![The best lottery software at command prompt needs data files of drawings, results in text format.](https://saliu.com/gambling-lottery-lotto/HLINE.gif)
    
-   強烈建議：將軟體包的所有檔案保存在同一個目錄（資料夾）中。這包括您的資料檔案（DATA\*、SIM\*、D\* 等）。輸入檔案名稱時無需輸入外部資料夾的路徑，這樣會更加方便。 \*
    
    新手們記住！沒有數據文件，Bright 或任何 LotWon 彩票軟體都無法運作！這類文件裡藏著寶藏：彩券和彩券遊戲中的開獎紀錄、開獎結果或往期中獎號碼！你只需要一個彩票開獎的純號碼。也就是說，只需要中獎號碼，不需要日期、獎金、獎金號碼（某些彩券有獎金號碼）……什麼都不需要！只需要你選擇的彩券遊戲中往期開獎中獎的號碼。
    
    讓我們用最簡單的彩票數字遊戲來舉例：Pick-3（抽出 3 位數字，每一位從 0 到 9）。
    
    首先建立一個 pick-3 資料檔。這是最簡單的步驟。開啟文字編輯器，包括記事本。每行輸入 3 位數字，剛好是 3 位。用逗號或空格（至少一個空格）分隔數字。在行尾按 Enter 鍵。繼續輸入接下來的三位數字，這三位數字實際上代表了上一次 pick-3 彩票開獎。確保文件中沒有空白行 — 任何空白行都沒有。完成後，請務必以文字格式儲存檔案（不進行任何格式設置，只有純數字）。最好將資料檔案保存在 Bright 程式和實用程式所在的目錄（資料夾）中（例如 C:\\BRIGHT3\\）。您可以為您的 pick-3 資料檔案命名，例如 DATA-3。實際上，任何名字都可以；只要在需要打開文件時記住它即可！再次強調，我的彩票軟體要求 pick-3 彩票遊戲的數據（結果）檔案每行正好有 3 位數字；彩票號碼之間用空格或逗號分隔。新手，明白了嗎？前往幫助頁面了解更多。
    
    更新彩票資料檔案時，請記住以下規則：最新（最近）的開獎記錄始終位於檔案頂部，成為行號 1。最早的開獎記錄位於文件底部。如果您的資料檔案並非按此順序排列，則需要使用內建程式 UPDOWN。它會反轉文字檔案中的順序：文件底部成為文件頂部。
    
    提示：Bright 軟體包附帶另一個實用工具：PARSEL。該工具可以檢查彩票資料檔案的正確性。它可以發現資料檔案中的大量錯誤，並指出出錯的行。您應該不時運行 PARSEL，以確保彩票資料檔案沒有錯誤。
    
    您可以在最新的 Bright 選單中找到這兩個實用程式；或者，您可以單獨下載並使用它們。
    
    BRIGHT3 中的程式需要至少 100,000（十萬）行的最終 pick3 資料檔。當然，在三位數樂透遊戲中，實際開獎結果不會那麼多！因此，您需要建立一個包含隨機產生的樂透 3 組合的附加檔案。該附加檔案通常名為 SIM-3，由 _SuperUtilities Utilities_ 函數（應用程式名稱：SoftwarePick3）在幾秒鐘內建立。 「Utilities」函數也會建立 D3 檔案：報告產生器和 pick-3 彩票組合產生器使用的最終檔案。全新、功能更強大的 Super Utilities（可透過 B3 取得）也會建立 SIM\* 和 D\* 檔案。
    
    ![Lotto software strategies are available for Windows XP, Vista, Windows 7, 8.1 command prompt.](https://saliu.com/gambling-lottery-lotto/HLINE.gif)
    
    接下來，您需要建立報告：W3 + MD3 檔案。功能名稱： _產生 W3 報告_ （按功能鍵 F5）。您需要分析 W3 報告，以便為未來的開獎設定篩選條件。這對我來說是最令人頭痛的問題。人們想要了解關於如何選擇篩選條件（創建策略）的大量細節。我得活一百次才能滿足我的彩票軟體的一位用戶！我只能建議：閱讀，閱讀，再閱讀！這個網站上有很多資訊。一個很好的起點：  
    
    -   [_**彩票過濾器、彩票軟體中的減少策略**_](https://saliu.com/filters.html) 。
    
    當您想出一個策略（一組篩選設定）後，您可以檢查該策略過去的表現。也就是說，您可以查看該策略在先前的彩票抽獎中中獎的次數。如果您為 1000 場選三抽獎產生 W3 報告，您的策略檢查會更加成功。按 F3 功能鍵執行_策略檢查_功能。
    
    ![Generate winning combinations with lottery, lotto software at PC command prompt or DOS of Windows.](https://saliu.com/gambling-lottery-lotto/HLINE.gif)
    
    您可以使用策略產生三選一組合。即使不先檢查策略，您也可以產生組合。只需在螢幕提示字元下輸入篩選值即可。功能鍵 F6 和 F7 可啟動組合產生器。第一個生成器依字典順序產生組合。 F7 鍵依最佳化的隨機順序產生三選一彩券組合。
    
    組合生成器具有多種功能：  
    
    -   從輸出檔案中清除彩票組合（先前產生的彩票組合）；  
        
    -   根據最喜歡的數字產生三選一組合；  
        
    -   使用者可以啟用或停用新型內部過濾器。
    
    ![Lottery, lotto software at Command Prompt in Windows XP, Vista, Win 7, 8.1 runs fast.](https://saliu.com/gambling-lottery-lotto/HLINE.gif)
    
    _檢查策略中獎次數_ (F4) 是一款很棒的應用程式。它會告訴你，在以往的中獎案例中，某個彩券策略能產生多少種組合。
    
    以下是檢查策略和策略命中的正確順序。
    
    1) 首先產生 W3 和 MD3 報告（功能鍵 F5）；  
    2) 接下來，檢查策略（功能鍵 F3）；它會建立非常重要的文件（ST3.000、ST3.HIT 和 ST3.REP）；  
    3) 然後，對步驟 2 中建立的檔案執行_策略命中_ （功能鍵 F4）。該過程還會更新 ST3.REP（打開它並在報告底部尋找資訊）。
    
    為了確保準確性，您必須使用相同的 D3 文件、相同的 W3 和 MD3 報告、相同的策略文件、相同的策略報告以及相同的 ST3.HIT 文件。因此，每當 D3 檔案發生變更時，都必須針對更新的 D3 彩票資料檔案重新產生報告。
    
    ![Use a great input data utility for the command prompt software.](https://saliu.com/gambling-lottery-lotto/HLINE.gif)
    
    ### 滑鼠、選擇、複製、貼上在命令提示字元中
    
    -   我的命令提示字元軟體採用了非常好的輸入實用程式。您將看到黃色反白的行，要求您輸入（鍵入）要開啟的資料檔案的名稱或要使用的行數（圖形）等。您可以使用_箭頭_鍵或 _Home_ 、 _End_ 、 _Insert_ 、 _Delete_ 輸入適當的資料。如果按_向上_或_向下_箭頭鍵，您將瀏覽預設值（例如，建議使用的檔案名稱）。如果預設值不符合您的需要，您可以編輯它們（例如，按 _Insert_ ，鍵入要開啟的檔案的名稱，如果檔案位於不同的資料夾中，則包含路徑）。您也可以按下 _/向上_箭頭鍵，直到輸入方塊中出現一個空白行；然後，您可以鍵入要開啟的檔案的名稱，如果檔案位於不同的資料夾中，則包含路徑。
    -   如果您的檔案位於同一目錄中，則無需輸入路徑（即目錄或資料夾名稱）。如果您的資料檔案名稱為 _data3_ （包含真實的 pick-3 彩票開獎結果），位於 _\\Bright3_ 資料夾中，並且您在 _\\Bright3_ 資料夾中工作，則只需輸入 _data3_ （不區分大小寫）即可開啟該檔案。但是，如果您的資料檔案名稱為 _data-3_ （包含真實的 pick-3 彩票開獎結果），位於 _D:\\LotteryData_ 資料夾中，並且您在 _C:\\Bright3_ 資料夾中工作，則只需輸入 _D:\\Lotterydata\\data3_ （不區分大小寫）即可開啟該檔案。
    -   在大多數情況下， _**LotWon**_ 命令提示字元軟體會記住您使用的檔案。下次執行程式時，輸入實用程式會將您上次使用的檔案名稱作為預設值。只需按 _Enter 鍵_即可套用預設值。
    -   1) 將 **Notepad++** 固定到 Windows 工作列；始終在 **LotWon** **命令提示字元**會話之前啟動編輯器。
    -   2) 您可以將檔案名稱複製到 **Notepad++** 中的剪貼簿，然後將其傳遞到 **LotWon** 的輸入行（具有黃色背景的輸入行）。
    -   3) 在 **Notepad++** 中 _「開啟檔案」_ 或 _「將檔案另存為」_ ；反白顯示檔案名稱後按 _Ctrl+C_ 進行複製；或右鍵單擊，然後 _「複製」_ 。
    -   4) **LotWon** 中的輸入行要求您輸入檔案名稱；找到一個空白的黃色突出顯示的輸入行，右鍵單擊，然後 _「貼上」_ 。
    -   5）或者，按一下左上角的命令提示字元圖示；選擇 _「編輯」_ ，然後選擇 _「貼上」_ （或在需要時_選擇「複製」_ ）。
    -   6) 您隨時可以點擊左上角的命令提示字元圖示進行 _「編輯」_ ，並在 **LotWon** 程式之間或程式內部交換資料。 _點選「編輯」_ ，然後_點選「標記」_ ，用滑鼠反白（即選擇）文字；然後點選 _「複製」_ 或 _「貼上」_ 。
    -   請將此網頁列印成紙本版，以便參考。
    
    ![COMMAND PROMPT is the most powerful component of Microsoft Windows, a child of the all-powerful DOS.](https://saliu.com/gambling-lottery-lotto/HLINE.gif)
    
    相關資料：
    
    -   <u><span lang="zh-TW"><span data-immersive-translate-translation-element-mark="1"><span data-immersive-translate-translation-element-mark="1"><span color="#ff0000"><b>光明</b></span>軟體</span></span></span></u>
    -   [**賽馬軟體**](https://saliu.com/horseracing-software.html)
    -   [**3位數彩券軟體**](https://saliu.com/lottery3-software.html)
    -   [**適用於四位數彩票遊戲的彩票軟體**](https://saliu.com/lottery4-software.html)
    -   [**5號彩券軟體**](https://saliu.com/lotto5-software.html)
    -   [**6號樂透彩票軟體**](https://saliu.com/lotto6-software.html)
    -   <u><span lang="zh-TW"><span data-immersive-translate-translation-element-mark="1"><span data-immersive-translate-translation-element-mark="1"><span color="#ff0000"><b>終極</b></span>軟體</span></span></span></u>
    -   [**賽馬三連勝、OTW 投注的終極軟體**](https://saliu.com/ultimate-horse-software.html)
    -   [**3 位數選 3 彩券遊戲的終極軟體（從 _000_ 到 _999_ ）**](https://saliu.com/ultimate-pick3-software.html)
    -   [**4 位數選 4 彩券遊戲的終極軟體（從 _0000_ 到 _9999_ ）**](https://saliu.com/ultimate-pick4-software.html)
    -   [**適用於所有 5 個號碼樂透遊戲的終極軟體（例如 _39 中的 5 個_或 _43 個中的 5 個_ ）**](https://saliu.com/ultimate-lotto5-software.html)
    -   [**適用於所有 6 號樂透遊戲的終極軟體（例如 _49 中的 6_ 或 _59 中的 6_ ）**](https://saliu.com/ultimate-lotto6-software.html)
    -   [_**XCOPY 指令：Windows 中最佳備份程式、方法、軟體**_](https://saliu.com/best-backup.html) 。
    
    [
    
    ## <u><span lang="zh-TW"><span data-immersive-translate-translation-element-mark="1"><span data-immersive-translate-translation-element-mark="1">彩票軟體、系統、策略、彩票轉盤資源</span></span></span></u>
    
    ](https://saliu.com/content/lottery.html)查看有關彩票、軟體、系統、策略、彩票輪盤等主題的頁面和資料的綜合目錄。
    
    -   主要的[_**樂透、樂透、軟體、策略系統**_](https://saliu.com/LottoWin.htm)頁面。  
        提供創建免費中獎彩券、彩券策略和基於數學系統的軟體。取得您的彩券系統或輪盤、最佳彩券、彩券軟體、組合和中獎號碼。
    -   [_**樂透、彩票軟體、Excel 電子表格：程式設計、策略**_](https://saliu.com/Newsgroups.htm) 。  
        閱讀一篇關於 Excel 電子表格在彩票和樂透軟體開發、系統和策略應用的真實分析。本文將 Excel 分析與作者 _Parpaluck_ （Ion Saliu 的暱稱）編寫的強大彩票和樂透軟體結合。
    -   [_****MDIEditor Lotto** 使用者指南**_](https://saliu.com/MDI-lotto-guide.html) 。  
        ~ 也適用於 LotWon 彩票、樂透軟體；以及 Powerball/Mega Millions、Euromillions。
    -   [_**視覺化教學、書籍、手冊：彩票軟體、樂透應用程式、程式**_](https://saliu.com/forum/lotto-book.html) 。
    -   _「我的王國有一個好的樂透教學！」_ [樂透，彩票策略教學](https://saliu.com/bbs/messages/818.html) 。
    -   [**彩票軟體中的<u>彩票過濾器、過濾 </u> 、減少策略** 。](https://saliu.com/filters.html)
    -   [_**<u>跳過系統</u>軟體**_](https://saliu.com/skip-strategy.html) ： _**樂透、彩票、強力球、超級百萬、歐洲百萬、賽馬、輪盤、足球**_ 。
    -   [**<u>彩票實用軟體 </u>**](https://saliu.com/lottery-utility.html) ： _**Pick-3、4 彩票、Lotto-5、6、強力球、超級百萬、雷霆球、歐洲百萬彩票**_ 。
    -   [_**文件、幫助：MDIEditor Lotto WE、彩票軟體、策略教學**_](https://saliu.com/mdi_lotto.html) 。
    -   [_**州彩券、樂透抽獎、結果、資料檔、網路**_](https://saliu.com/bbs/messages/920.html) 。
    -   [_**幫助頁面：建立、編輯樂透、彩票資料檔**_](https://saliu.com/Help.htm) 。
    -   下載[**最好的彩票軟體**](https://saliu.com/infodown.html) 。
    
    ![Run the latest lottery software for Windows command prompt known as DOS.](https://saliu.com/gambling-lottery-lotto/HLINE.gif)
    
    **[首頁](https://saliu.com/index.htm) | [搜尋](https://saliu.com/Search.htm) | [幫助](https://saliu.com/Help.htm) | [新文章](https://saliu.com/bbs/index.html) | [軟體](https://saliu.com/infodown.html) | [賠率產生器](https://saliu.com/calculator_generator.html) | [內容](https://saliu.com/content/index.html) | [論壇](https://forums.saliu.com/) | [網站地圖](https://saliu.com/sitemap/index.html)**
    
    ![Do NOT fear Command Prompt - it makes GUI Windows do powerful operations FAST.](https://saliu.com/gambling-lottery-lotto/HLINE.gif)
