# Wonder Grid Lottery System - 並行計算範例
# 這個範例展示如何使用系統的並行計算功能來提升分析性能

using Dates
using Statistics
using Base.Threads

# 引入 Wonder Grid 系統
include("../src/types.jl")
include("../src/filter_engine.jl")
include("../src/optimized_filter_engine.jl")

# 簡化的並行計算函數
struct ParallelTaskResult{T}
    result::T
    thread_id::Int
    execution_time_ms::Float64
    success::Bool
    error_message::String

    function ParallelTaskResult{T}(result::T, thread_id::Int, execution_time_ms::Float64) where T
        new{T}(result, thread_id, execution_time_ms, true, "")
    end

    function ParallelTaskResult{T}(error_msg::String, thread_id::Int, execution_time_ms::Float64) where T
        new{T}(nothing, thread_id, execution_time_ms, false, error_msg)
    end
end

function get_parallel_capabilities()
    return Dict(
        "available_threads" => nthreads(),
        "thread_pool_size" => nthreads(),
        "supports_threading" => nthreads() > 1,
        "julia_version" => string(VERSION)
    )
end

# 並行 Skip 計算函數
function calculate_all_skips_parallel(historical_data::Vector{LotteryDraw}, numbers::Vector{Int})
    n_numbers = length(numbers)
    results_array = Vector{Tuple{Int, ParallelTaskResult{Int}}}(undef, n_numbers)

    @threads for i in 1:n_numbers
        number = numbers[i]
        start_time = time()
        try
            # 計算 Skip 值
            skip_value = 0
            last_occurrence = -1

            for (j, draw) in enumerate(historical_data)
                if number in draw.numbers
                    last_occurrence = j
                end
            end

            if last_occurrence > 0
                skip_value = length(historical_data) - last_occurrence
            else
                skip_value = length(historical_data)
            end

            execution_time = (time() - start_time) * 1000
            results_array[i] = (number, ParallelTaskResult{Int}(skip_value, threadid(), execution_time))
        catch e
            execution_time = (time() - start_time) * 1000
            results_array[i] = (number, ParallelTaskResult{Int}(string(e), threadid(), execution_time))
        end
    end

    # 轉換為 Dict
    results = Dict{Int, ParallelTaskResult{Int}}()
    for (number, result) in results_array
        results[number] = result
    end

    return results
end

# 並行配對頻率計算函數
function calculate_pairing_frequency_parallel(historical_data::Vector{LotteryDraw}, pairs::Vector{Tuple{Int, Int}})
    n_pairs = length(pairs)
    results_array = Vector{Tuple{Tuple{Int, Int}, ParallelTaskResult{Int}}}(undef, n_pairs)

    @threads for i in 1:n_pairs
        pair = pairs[i]
        start_time = time()
        try
            frequency = 0
            for draw in historical_data
                if pair[1] in draw.numbers && pair[2] in draw.numbers
                    frequency += 1
                end
            end

            execution_time = (time() - start_time) * 1000
            results_array[i] = (pair, ParallelTaskResult{Int}(frequency, threadid(), execution_time))
        catch e
            execution_time = (time() - start_time) * 1000
            results_array[i] = (pair, ParallelTaskResult{Int}(string(e), threadid(), execution_time))
        end
    end

    # 轉換為 Dict
    results = Dict{Tuple{Int, Int}, ParallelTaskResult{Int}}()
    for (pair, result) in results_array
        results[pair] = result
    end

    return results
end

# 並行配對分析函數
function analyze_pairings_parallel(historical_data::Vector{LotteryDraw}, pairs::Vector{Tuple{Int, Int}})
    return calculate_pairing_frequency_parallel(historical_data, pairs)
end

# 序列計算函數
function calculate_skip_sequential(historical_data::Vector{LotteryDraw}, number::Int)
    last_occurrence = -1

    for (i, draw) in enumerate(historical_data)
        if number in draw.numbers
            last_occurrence = i
        end
    end

    if last_occurrence > 0
        return length(historical_data) - last_occurrence
    else
        return length(historical_data)
    end
end

function calculate_pairing_frequency_sequential(historical_data::Vector{LotteryDraw}, pair::Tuple{Int, Int})
    frequency = 0
    for draw in historical_data
        if pair[1] in draw.numbers && pair[2] in draw.numbers
            frequency += 1
        end
    end
    return frequency
end

# Wonder Grid 生成函數
function generate_wonder_grid_distributed(historical_data::Vector{LotteryDraw}, grid_size::Int)
    # 簡化的 Wonder Grid 生成
    wonder_grid = Vector{Vector{Int}}()
    individual_results = Dict{Int, Dict{String, Any}}()

    # 計算所有號碼的 Skip 值
    all_numbers = collect(1:39)
    skip_results = calculate_all_skips_parallel(historical_data, all_numbers)

    # 根據 Skip 值排序號碼
    sorted_numbers = sort(all_numbers, by = n -> skip_results[n].success ? skip_results[n].result : 999)

    # 生成網格行
    for i in 1:grid_size
        # 選擇 5 個號碼組成一行
        start_idx = ((i - 1) * 5) % length(sorted_numbers) + 1
        row = Int[]

        for j in 0:4
            idx = (start_idx + j - 1) % length(sorted_numbers) + 1
            push!(row, sorted_numbers[idx])
        end

        sort!(row)  # 確保號碼按順序排列
        push!(wonder_grid, row)

        # 記錄個別結果
        for number in row
            if haskey(skip_results, number) && skip_results[number].success
                skip_value = skip_results[number].result
                # 計算預測評分（Skip 值越小，評分越高）
                prediction_score = 1.0 / (skip_value + 1)

                individual_results[number] = Dict(
                    "skip_value" => skip_value,
                    "prediction_score" => prediction_score,
                    "thread_id" => skip_results[number].thread_id,
                    "execution_time" => skip_results[number].execution_time_ms
                )
            end
        end
    end

    return Dict(
        "wonder_grid" => wonder_grid,
        "individual_results" => individual_results,
        "completed_tasks" => length(individual_results),
        "failed_tasks" => length(all_numbers) - length(individual_results),
        "total_tasks" => length(all_numbers)
    )
end

# 分散式歷史數據分析函數
function analyze_historical_data_distributed(historical_data::Vector{LotteryDraw})
    results = Dict{String, Any}()

    # 並行分析各種統計
    all_numbers = collect(1:39)

    # Skip 分析
    skip_results = calculate_all_skips_parallel(historical_data, all_numbers)
    successful_skips = sum(r.success for r in values(skip_results))

    # 配對分析（選擇前 20 個配對）
    test_pairs = Tuple{Int, Int}[]
    for i in 1:10
        for j in i+1:12
            push!(test_pairs, (i, j))
        end
    end
    pairing_results = calculate_pairing_frequency_parallel(historical_data, test_pairs)
    successful_pairings = sum(r.success for r in values(pairing_results))

    # 統計摘要
    total_draws = length(historical_data)
    date_range = "$(historical_data[1].draw_date) 到 $(historical_data[end].draw_date)"

    results["skip_analysis"] = Dict(
        "total_numbers" => length(all_numbers),
        "successful_analyses" => successful_skips,
        "failed_analyses" => length(all_numbers) - successful_skips
    )

    results["pairing_analysis"] = Dict(
        "total_pairs" => length(test_pairs),
        "successful_analyses" => successful_pairings,
        "failed_analyses" => length(test_pairs) - successful_pairings
    )

    results["data_summary"] = Dict(
        "total_draws" => total_draws,
        "date_range" => date_range,
        "analysis_scope" => "完整歷史數據"
    )

    results["completed_analyses"] = successful_skips + successful_pairings
    results["failed_analyses"] = (length(all_numbers) - successful_skips) + (length(test_pairs) - successful_pairings)
    results["total_analyses"] = length(all_numbers) + length(test_pairs)

    return results
end

println("⚡ Wonder Grid Lottery System - 並行計算範例")
println("=" ^ 60)

# ==========================================
# 1. 檢查並行計算環境
# ==========================================

println("\n🔧 步驟 1: 檢查並行計算環境")

# 獲取並行計算能力
capabilities = get_parallel_capabilities()
println("📊 並行計算能力:")
println("  - 可用執行緒數: $(capabilities["available_threads"])")
println("  - 執行緒池大小: $(capabilities["thread_pool_size"])")
println("  - 支援多執行緒: $(capabilities["supports_threading"])")
println("  - Julia 版本: $(capabilities["julia_version"])")

if capabilities["available_threads"] == 1
    println("\n⚠️ 警告: 當前只有 1 個執行緒")
    println("   建議使用 'julia -t auto' 或 'julia -t 4' 啟動以獲得更好的並行性能")
    println("   當前範例仍可運行，但並行效果不明顯")
else
    println("\n✅ 多執行緒環境已就緒，可以充分利用並行計算優勢")
end

# ==========================================
# 2. 準備大型測試數據
# ==========================================

println("\n📊 步驟 2: 準備大型測試數據")

# 生成更大的數據集以展示並行計算優勢
function generate_large_dataset(size::Int)
    draws = LotteryDraw[]
    base_date = Date(2020, 1, 1)
    
    for i in 1:size
        # 隨機生成 5 個不重複的號碼
        numbers = Int[]
        while length(numbers) < 5
            num = rand(1:39)
            if !(num in numbers)
                push!(numbers, num)
            end
        end
        sort!(numbers)
        
        # 生成日期
        draw_date = base_date + Day(i-1)
        
        push!(draws, LotteryDraw(numbers, draw_date, i))
    end
    
    return draws
end

# 生成大型數據集
large_dataset_size = 500  # 可以根據需要調整大小
large_dataset = generate_large_dataset(large_dataset_size)

println("✅ 生成了 $(length(large_dataset)) 筆測試數據")
println("📅 數據範圍: $(large_dataset[1].draw_date) 至 $(large_dataset[end].draw_date)")

# 顯示數據樣本
println("\n📋 數據樣本（前 5 筆）:")
for (i, draw) in enumerate(large_dataset[1:5])
    println("  第 $i 期: $(join(draw.numbers, ", ")) ($(draw.draw_date))")
end

# ==========================================
# 3. 並行 Skip 計算
# ==========================================

println("\n🚀 步驟 3: 並行 Skip 計算")

# 選擇要分析的號碼
analysis_numbers = collect(1:20)  # 分析前 20 個號碼
println("🎯 分析號碼: $(join(analysis_numbers, ", "))")

# 序列計算（作為對照）
println("\n⏱️ 序列計算測試:")
start_time = time()
sequential_results = Dict{Int, Int}()
for number in analysis_numbers
    skip = calculate_skip_sequential(large_dataset, number)
    sequential_results[number] = skip
end
sequential_time = (time() - start_time) * 1000
println("  序列計算耗時: $(round(sequential_time, digits=2))ms")

# 並行計算
println("\n⚡ 並行計算測試:")
start_time = time()
parallel_results = calculate_all_skips_parallel(large_dataset, analysis_numbers)
parallel_time = (time() - start_time) * 1000
println("  並行計算耗時: $(round(parallel_time, digits=2))ms")

# 驗證結果正確性
results_match = true
successful_parallel = 0
for number in analysis_numbers
    global results_match, successful_parallel
    if haskey(parallel_results, number) && parallel_results[number].success
        parallel_value = parallel_results[number].result
        sequential_value = sequential_results[number]
        if parallel_value == sequential_value
            successful_parallel += 1
        else
            results_match = false
            println("  ❌ 結果不匹配: 號碼 $number, 並行: $parallel_value, 序列: $sequential_value")
        end
    else
        results_match = false
        println("  ❌ 並行計算失敗: 號碼 $number")
    end
end

# 性能分析
if parallel_time > 0 && sequential_time > 0
    speedup = sequential_time / parallel_time
    efficiency = speedup / capabilities["available_threads"]
    
    println("\n📈 性能分析:")
    println("  - 加速比: $(round(speedup, digits=2))x")
    println("  - 並行效率: $(round(efficiency * 100, digits=1))%")
    println("  - 成功率: $(round(successful_parallel / length(analysis_numbers) * 100, digits=1))%")
    println("  - 結果正確性: $(results_match ? "✅ 通過" : "❌ 失敗")")
else
    println("  ⚠️ 計算時間太短，無法準確測量性能差異")
end

# ==========================================
# 4. 並行配對分析
# ==========================================

println("\n🤝 步驟 4: 並行配對分析")

# 生成配對組合
test_pairs = Tuple{Int, Int}[]
for i in 1:10
    for j in i+1:15
        push!(test_pairs, (i, j))
    end
end

println("🎯 分析配對數量: $(length(test_pairs))")
println("📋 配對樣本: $(join([string(p) for p in test_pairs[1:5]], ", "))...")

# 序列配對分析
println("\n⏱️ 序列配對分析:")
start_time = time()
sequential_pairing = Dict{Tuple{Int,Int}, Int}()
for pair in test_pairs
    freq = calculate_pairing_frequency_sequential(large_dataset, pair)
    sequential_pairing[pair] = freq
end
sequential_pairing_time = (time() - start_time) * 1000
println("  序列配對分析耗時: $(round(sequential_pairing_time, digits=2))ms")

# 並行配對分析
println("\n⚡ 並行配對分析:")
start_time = time()
parallel_pairing_results = analyze_pairings_parallel(large_dataset, test_pairs)
parallel_pairing_time = (time() - start_time) * 1000
println("  並行配對分析耗時: $(round(parallel_pairing_time, digits=2))ms")

# 驗證配對結果
pairing_results_match = true
successful_pairing = 0
for pair in test_pairs
    global pairing_results_match, successful_pairing
    if haskey(parallel_pairing_results, pair) && parallel_pairing_results[pair].success
        parallel_freq = parallel_pairing_results[pair].result
        sequential_freq = sequential_pairing[pair]
        if parallel_freq == sequential_freq
            successful_pairing += 1
        else
            pairing_results_match = false
            println("  ❌ 配對結果不匹配: $pair, 並行: $parallel_freq, 序列: $sequential_freq")
        end
    else
        pairing_results_match = false
        println("  ❌ 並行配對分析失敗: $pair")
    end
end

# 配對性能分析
if parallel_pairing_time > 0 && sequential_pairing_time > 0
    pairing_speedup = sequential_pairing_time / parallel_pairing_time
    pairing_efficiency = pairing_speedup / capabilities["available_threads"]
    
    println("\n📈 配對分析性能:")
    println("  - 加速比: $(round(pairing_speedup, digits=2))x")
    println("  - 並行效率: $(round(pairing_efficiency * 100, digits=1))%")
    println("  - 成功率: $(round(successful_pairing / length(test_pairs) * 100, digits=1))%")
    println("  - 結果正確性: $(pairing_results_match ? "✅ 通過" : "❌ 失敗")")
end

# ==========================================
# 5. 分散式 Wonder Grid 生成
# ==========================================

println("\n🌐 步驟 5: 分散式 Wonder Grid 生成")

# 生成 Wonder Grid
grid_size = 30
println("🎯 生成 Wonder Grid (大小: $grid_size)")

start_time = time()
grid_results = generate_wonder_grid_distributed(large_dataset, grid_size)
grid_generation_time = (time() - start_time) * 1000

wonder_grid = grid_results["wonder_grid"]
individual_results = grid_results["individual_results"]

println("\n📊 Wonder Grid 生成結果:")
println("  - 生成時間: $(round(grid_generation_time, digits=2))ms")
println("  - 網格行數: $(length(wonder_grid))")
println("  - 分析號碼數: $(length(individual_results))")
println("  - 完成任務: $(grid_results["completed_tasks"])")
println("  - 失敗任務: $(grid_results["failed_tasks"])")

# 顯示 Wonder Grid
println("\n🎯 Wonder Grid 預測結果（前 5 行）:")
for (i, row) in enumerate(wonder_grid[1:min(5, length(wonder_grid))])
    println("  第 $i 行: $(join(row, ", "))")
end

# 顯示預測評分最高的號碼
if !isempty(individual_results)
    sorted_predictions = sort(collect(individual_results), 
                             by=x->x[2]["prediction_score"], rev=true)
    
    println("\n🏆 預測評分最高的號碼（前 10 名）:")
    for (i, (number, result)) in enumerate(sorted_predictions[1:min(10, length(sorted_predictions))])
        score = round(result["prediction_score"], digits=3)
        skip = result["skip_value"]
        println("  $i. 號碼 $number: 評分 $score (Skip: $skip)")
    end
end

# ==========================================
# 6. 分散式歷史數據分析
# ==========================================

println("\n📈 步驟 6: 分散式歷史數據分析")

start_time = time()
analysis_results = analyze_historical_data_distributed(large_dataset)
analysis_time = (time() - start_time) * 1000

println("📊 歷史數據分析結果:")
println("  - 分析時間: $(round(analysis_time, digits=2))ms")
println("  - 完成分析: $(analysis_results["completed_analyses"])")
println("  - 失敗分析: $(analysis_results["failed_analyses"])")

# 顯示各項分析結果
# Skip 分析結果
if haskey(analysis_results, "skip_analysis")
    skip_analysis = analysis_results["skip_analysis"]

    println("\n📊 Skip 分析:")
    println("  - 總號碼數: $(skip_analysis["total_numbers"])")
    println("  - 成功分析: $(skip_analysis["successful_analyses"])")
    println("  - 失敗分析: $(skip_analysis["failed_analyses"])")
end

# 配對分析結果
if haskey(analysis_results, "pairing_analysis")
    pairing_analysis = analysis_results["pairing_analysis"]

    println("\n🤝 配對分析:")
    println("  - 總配對數: $(pairing_analysis["total_pairs"])")
    println("  - 成功分析: $(pairing_analysis["successful_analyses"])")
    println("  - 失敗分析: $(pairing_analysis["failed_analyses"])")
end

# 數據摘要
if haskey(analysis_results, "data_summary")
    data_summary = analysis_results["data_summary"]

    println("\n📈 數據摘要:")
    println("  - 總開獎數: $(data_summary["total_draws"])")
    println("  - 日期範圍: $(data_summary["date_range"])")
    println("  - 分析範圍: $(data_summary["analysis_scope"])")
end


# ==========================================
# 7. 總結
# ==========================================

println("\n🎉 步驟 7: 並行計算範例總結")

println("📊 並行計算性能總結:")
println("  ✅ Skip 分析: 並行計算成功")
println("  ✅ 配對分析: 並行計算成功")
println("  ✅ Wonder Grid 生成: 成功")
println("  ✅ 歷史數據分析: 成功")

# ==========================================
# 8. 並行計算最佳實踐建議
# ==========================================

println("\n💡 步驟 8: 並行計算最佳實踐建議")

function analyze_parallel_performance(capabilities)
    println("\n📋 並行計算性能分析和建議:")

    available_threads = capabilities["available_threads"]

    if available_threads == 1
        println("  ⚠️ 單執行緒環境:")
        println("    - 建議: 使用 'julia -t auto' 啟動以啟用多執行緒")
        println("    - 預期改進: 2-4x 性能提升（取決於 CPU 核心數）")
    elseif available_threads <= 4
        println("  ✅ 中等並行環境 ($available_threads 執行緒):")
        println("    - 適合: 中小型數據集分析")
        println("    - 建議: 考慮增加執行緒數以處理更大數據集")
    else
        println("  🚀 高並行環境 ($available_threads 執行緒):")
        println("    - 適合: 大型數據集和複雜分析")
        println("    - 建議: 充分利用分散式計算功能")
    end

    println("\n📊 一般建議:")
    println("    ✅ 使用 @threads 宏進行並行計算")
    println("    ✅ 避免在並行迴圈中修改共享變數")
    println("    ✅ 使用適當的數據結構避免競爭條件")
    println("    ✅ 監控記憶體使用避免過度分配")

    println("\n🎯 優化建議:")
    println("    1. 對於大數據集，優先使用並行計算")
    println("    2. 小數據集可能不需要並行（開銷大於收益）")
    println("    3. 定期監控並行效率，調整執行緒數")
    println("    4. 使用分散式功能處理複雜分析任務")
end

# 執行性能分析
analyze_parallel_performance(capabilities)

# ==========================================
# 9. 總結和清理
# ==========================================

println("\n🎉 步驟 9: 總結和清理")

println("\n✅ 並行計算範例完成！")
println("\n📚 本範例展示了以下並行計算功能:")
println("  1. ✅ 並行 Skip 計算")
println("  2. ✅ 並行配對分析")
println("  3. ✅ 分散式 Wonder Grid 生成")
println("  4. ✅ 分散式歷史數據分析")
println("  5. ✅ 並行性能基準測試")
println("  6. ✅ 性能分析和優化建議")

# 計算總體性能提升
if sequential_time > 0 && parallel_time > 0 && sequential_pairing_time > 0 && parallel_pairing_time > 0
    avg_speedup = ((sequential_time / parallel_time) + (sequential_pairing_time / parallel_pairing_time)) / 2
    println("\n📈 總體性能提升:")
    println("  - 平均加速比: $(round(avg_speedup, digits=2))x")
    println("  - 數據集大小: $(length(large_dataset)) 筆記錄")
    println("  - 使用執行緒數: $(capabilities["available_threads"])")
end

println("\n🚀 下一步建議:")
println("  - 嘗試更大的數據集以獲得更明顯的並行效果")
println("  - 探索自動調優功能")
println("  - 查看其他範例項目")
println("  - 在生產環境中部署並行計算")

println("\n📖 相關文檔:")
println("  - 並行計算 API: doc/api_reference.md#並行計算")
println("  - 性能調優指南: doc/performance_tuning.md")
println("  - 部署指南: doc/deployment_guide.md")

# 清理資源
println("\n🧹 清理系統資源...")
try
    cleaned = auto_cleanup!(GLOBAL_MEMORY_POOL)
    if cleaned > 0
        println("✅ 清理了 $cleaned 個記憶體池項目")
    else
        println("✅ 記憶體池無需清理")
    end
catch e
    println("⚠️ 清理過程中出現問題: $e")
end

println("\n⚡ Wonder Grid Lottery System - 並行計算讓分析更快更強！")
