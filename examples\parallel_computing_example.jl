# Wonder Grid Lottery System - 並行計算範例
# 這個範例展示如何使用系統的並行計算功能來提升分析性能

using Dates
using Statistics

# 引入 Wonder Grid 系統
include("../src/types.jl")
include("../src/parallel/parallel_computing.jl")

println("⚡ Wonder Grid Lottery System - 並行計算範例")
println("=" ^ 60)

# ==========================================
# 1. 檢查並行計算環境
# ==========================================

println("\n🔧 步驟 1: 檢查並行計算環境")

# 獲取並行計算能力
capabilities = get_parallel_capabilities()
println("📊 並行計算能力:")
println("  - 可用執行緒數: $(capabilities["available_threads"])")
println("  - 執行緒池大小: $(capabilities["thread_pool_size"])")
println("  - 支援多執行緒: $(capabilities["supports_threading"])")
println("  - Julia 版本: $(capabilities["julia_version"])")

if capabilities["available_threads"] == 1
    println("\n⚠️ 警告: 當前只有 1 個執行緒")
    println("   建議使用 'julia -t auto' 或 'julia -t 4' 啟動以獲得更好的並行性能")
    println("   當前範例仍可運行，但並行效果不明顯")
else
    println("\n✅ 多執行緒環境已就緒，可以充分利用並行計算優勢")
end

# ==========================================
# 2. 準備大型測試數據
# ==========================================

println("\n📊 步驟 2: 準備大型測試數據")

# 生成更大的數據集以展示並行計算優勢
function generate_large_dataset(size::Int)
    draws = LotteryDraw[]
    base_date = Date(2020, 1, 1)
    
    for i in 1:size
        # 隨機生成 5 個不重複的號碼
        numbers = Int[]
        while length(numbers) < 5
            num = rand(1:39)
            if !(num in numbers)
                push!(numbers, num)
            end
        end
        sort!(numbers)
        
        # 生成日期
        draw_date = base_date + Day(i-1)
        
        push!(draws, LotteryDraw(numbers, draw_date, i))
    end
    
    return draws
end

# 生成大型數據集
large_dataset_size = 500  # 可以根據需要調整大小
large_dataset = generate_large_dataset(large_dataset_size)

println("✅ 生成了 $(length(large_dataset)) 筆測試數據")
println("📅 數據範圍: $(large_dataset[1].draw_date) 至 $(large_dataset[end].draw_date)")

# 顯示數據樣本
println("\n📋 數據樣本（前 5 筆）:")
for (i, draw) in enumerate(large_dataset[1:5])
    println("  第 $i 期: $(join(draw.numbers, ", ")) ($(draw.draw_date))")
end

# ==========================================
# 3. 並行 Skip 計算
# ==========================================

println("\n🚀 步驟 3: 並行 Skip 計算")

# 選擇要分析的號碼
analysis_numbers = collect(1:20)  # 分析前 20 個號碼
println("🎯 分析號碼: $(join(analysis_numbers, ", "))")

# 序列計算（作為對照）
println("\n⏱️ 序列計算測試:")
start_time = time()
sequential_results = Dict{Int, Int}()
for number in analysis_numbers
    skip = calculate_skip_sequential(large_dataset, number)
    sequential_results[number] = skip
end
sequential_time = (time() - start_time) * 1000
println("  序列計算耗時: $(round(sequential_time, digits=2))ms")

# 並行計算
println("\n⚡ 並行計算測試:")
start_time = time()
parallel_results = calculate_all_skips_parallel(large_dataset, analysis_numbers)
parallel_time = (time() - start_time) * 1000
println("  並行計算耗時: $(round(parallel_time, digits=2))ms")

# 驗證結果正確性
results_match = true
successful_parallel = 0
for number in analysis_numbers
    if haskey(parallel_results, number) && parallel_results[number].success
        parallel_value = parallel_results[number].result
        sequential_value = sequential_results[number]
        if parallel_value == sequential_value
            successful_parallel += 1
        else
            results_match = false
            println("  ❌ 結果不匹配: 號碼 $number, 並行: $parallel_value, 序列: $sequential_value")
        end
    else
        results_match = false
        println("  ❌ 並行計算失敗: 號碼 $number")
    end
end

# 性能分析
if parallel_time > 0 && sequential_time > 0
    speedup = sequential_time / parallel_time
    efficiency = speedup / capabilities["available_threads"]
    
    println("\n📈 性能分析:")
    println("  - 加速比: $(round(speedup, digits=2))x")
    println("  - 並行效率: $(round(efficiency * 100, digits=1))%")
    println("  - 成功率: $(round(successful_parallel / length(analysis_numbers) * 100, digits=1))%")
    println("  - 結果正確性: $(results_match ? "✅ 通過" : "❌ 失敗")")
else
    println("  ⚠️ 計算時間太短，無法準確測量性能差異")
end

# ==========================================
# 4. 並行配對分析
# ==========================================

println("\n🤝 步驟 4: 並行配對分析")

# 生成配對組合
test_pairs = []
for i in 1:10
    for j in i+1:15
        push!(test_pairs, (i, j))
    end
end

println("🎯 分析配對數量: $(length(test_pairs))")
println("📋 配對樣本: $(join([string(p) for p in test_pairs[1:5]], ", "))...")

# 序列配對分析
println("\n⏱️ 序列配對分析:")
start_time = time()
sequential_pairing = Dict{Tuple{Int,Int}, Int}()
for (num1, num2) in test_pairs
    freq = calculate_pairing_frequency_sequential(large_dataset, num1, num2)
    sequential_pairing[(num1, num2)] = freq
end
sequential_pairing_time = (time() - start_time) * 1000
println("  序列配對分析耗時: $(round(sequential_pairing_time, digits=2))ms")

# 並行配對分析
println("\n⚡ 並行配對分析:")
start_time = time()
parallel_pairing_results = analyze_pairings_parallel(large_dataset, test_pairs)
parallel_pairing_time = (time() - start_time) * 1000
println("  並行配對分析耗時: $(round(parallel_pairing_time, digits=2))ms")

# 驗證配對結果
pairing_results_match = true
successful_pairing = 0
for pair in test_pairs
    if haskey(parallel_pairing_results, pair) && parallel_pairing_results[pair].success
        parallel_freq = parallel_pairing_results[pair].result
        sequential_freq = sequential_pairing[pair]
        if parallel_freq == sequential_freq
            successful_pairing += 1
        else
            pairing_results_match = false
            println("  ❌ 配對結果不匹配: $pair, 並行: $parallel_freq, 序列: $sequential_freq")
        end
    else
        pairing_results_match = false
        println("  ❌ 並行配對分析失敗: $pair")
    end
end

# 配對性能分析
if parallel_pairing_time > 0 && sequential_pairing_time > 0
    pairing_speedup = sequential_pairing_time / parallel_pairing_time
    pairing_efficiency = pairing_speedup / capabilities["available_threads"]
    
    println("\n📈 配對分析性能:")
    println("  - 加速比: $(round(pairing_speedup, digits=2))x")
    println("  - 並行效率: $(round(pairing_efficiency * 100, digits=1))%")
    println("  - 成功率: $(round(successful_pairing / length(test_pairs) * 100, digits=1))%")
    println("  - 結果正確性: $(pairing_results_match ? "✅ 通過" : "❌ 失敗")")
end

# ==========================================
# 5. 分散式 Wonder Grid 生成
# ==========================================

println("\n🌐 步驟 5: 分散式 Wonder Grid 生成")

# 生成 Wonder Grid
grid_size = 30
println("🎯 生成 Wonder Grid (大小: $grid_size)")

start_time = time()
grid_results = generate_wonder_grid_distributed(large_dataset, grid_size)
grid_generation_time = (time() - start_time) * 1000

wonder_grid = grid_results["wonder_grid"]
individual_results = grid_results["individual_results"]

println("\n📊 Wonder Grid 生成結果:")
println("  - 生成時間: $(round(grid_generation_time, digits=2))ms")
println("  - 網格行數: $(length(wonder_grid))")
println("  - 分析號碼數: $(length(individual_results))")
println("  - 完成任務: $(grid_results["completed_tasks"])")
println("  - 失敗任務: $(grid_results["failed_tasks"])")

# 顯示 Wonder Grid
println("\n🎯 Wonder Grid 預測結果（前 5 行）:")
for (i, row) in enumerate(wonder_grid[1:min(5, length(wonder_grid))])
    println("  第 $i 行: $(join(row, ", "))")
end

# 顯示預測評分最高的號碼
if !isempty(individual_results)
    sorted_predictions = sort(collect(individual_results), 
                             by=x->x[2]["prediction_score"], rev=true)
    
    println("\n🏆 預測評分最高的號碼（前 10 名）:")
    for (i, (number, result)) in enumerate(sorted_predictions[1:min(10, length(sorted_predictions))])
        score = round(result["prediction_score"], digits=3)
        skip = result["skip_value"]
        println("  $i. 號碼 $number: 評分 $score (Skip: $skip)")
    end
end

# ==========================================
# 6. 分散式歷史數據分析
# ==========================================

println("\n📈 步驟 6: 分散式歷史數據分析")

start_time = time()
analysis_results = analyze_historical_data_distributed(large_dataset)
analysis_time = (time() - start_time) * 1000

println("📊 歷史數據分析結果:")
println("  - 分析時間: $(round(analysis_time, digits=2))ms")
println("  - 完成分析: $(analysis_results["completed_analyses"])")
println("  - 失敗分析: $(analysis_results["failed_analyses"])")

# 顯示各項分析結果
analysis_data = analysis_results["analysis_results"]

# 頻率分析結果
if haskey(analysis_data, "frequency_analysis")
    freq_analysis = analysis_data["frequency_analysis"]
    hot_numbers = freq_analysis["hot_numbers"][1:min(5, length(freq_analysis["hot_numbers"]))]
    cold_numbers = freq_analysis["cold_numbers"][1:min(5, length(freq_analysis["cold_numbers"]))]
    
    println("\n🔥 頻率分析:")
    println("  - 熱門號碼: $(join(hot_numbers, ", "))")
    println("  - 冷門號碼: $(join(cold_numbers, ", "))")
    println("  - 總開獎數: $(freq_analysis["total_draws_analyzed"])")
end

# Skip 分析結果
if haskey(analysis_data, "skip_analysis")
    skip_analysis = analysis_data["skip_analysis"]
    
    println("\n📊 Skip 分析:")
    println("  - 平均 Skip: $(round(skip_analysis["average_skip"], digits=2))")
    println("  - 最大 Skip: $(skip_analysis["max_skip"])")
    println("  - 最小 Skip: $(skip_analysis["min_skip"])")
    println("  - 分析號碼數: $(skip_analysis["numbers_analyzed"])")
end

# 趨勢分析結果
if haskey(analysis_data, "trend_analysis")
    trend_analysis = analysis_data["trend_analysis"]
    
    if haskey(trend_analysis, "recent_numbers")
        recent_numbers = trend_analysis["recent_numbers"]
        println("\n📈 趨勢分析:")
        println("  - 最近熱門: $(join(recent_numbers[1:min(10, length(recent_numbers))], ", "))")
        println("  - 分析期間: $(trend_analysis["analysis_period"]) 期")
    end
end

# ==========================================
# 7. 並行性能基準測試
# ==========================================

println("\n🏃 步驟 7: 並行性能基準測試")

# 執行完整的並行性能基準測試
benchmark_results = parallel_performance_benchmark(large_dataset)

println("📊 並行性能基準測試結果:")

# Skip 計算基準
if haskey(benchmark_results, "skip_calculation")
    skip_bench = benchmark_results["skip_calculation"]
    println("\n⚡ Skip 計算基準:")
    println("  - 總耗時: $(round(skip_bench["total_time_ms"], digits=2))ms")
    println("  - 完成任務: $(skip_bench["tasks_completed"])")
    println("  - 失敗任務: $(skip_bench["tasks_failed"])")
    println("  - 使用執行緒: $(skip_bench["threads_used"])")
    println("  - 平均任務時間: $(round(skip_bench["avg_task_time_ms"], digits=2))ms")
end

# 配對分析基準
if haskey(benchmark_results, "pairing_analysis")
    pairing_bench = benchmark_results["pairing_analysis"]
    println("\n🤝 配對分析基準:")
    println("  - 總耗時: $(round(pairing_bench["total_time_ms"], digits=2))ms")
    println("  - 完成任務: $(pairing_bench["tasks_completed"])")
    println("  - 失敗任務: $(pairing_bench["tasks_failed"])")
    println("  - 使用執行緒: $(pairing_bench["threads_used"])")
    println("  - 平均任務時間: $(round(pairing_bench["avg_task_time_ms"], digits=2))ms")
end

# 整體統計
if haskey(benchmark_results, "overall")
    overall = benchmark_results["overall"]
    println("\n📈 整體性能統計:")
    println("  - 總任務數: $(overall["total_tasks"])")
    println("  - 成功任務: $(overall["successful_tasks"])")
    println("  - 成功率: $(round(overall["success_rate"] * 100, digits=1))%")
    println("  - 總耗時: $(round(overall["total_time_ms"], digits=2))ms")
    println("  - 並行效率: $(round(overall["parallel_efficiency"], digits=2))")
end

# ==========================================
# 8. 並行計算最佳實踐建議
# ==========================================

println("\n💡 步驟 8: 並行計算最佳實踐建議")

function analyze_parallel_performance(capabilities, benchmark_results)
    println("\n📋 並行計算性能分析和建議:")
    
    available_threads = capabilities["available_threads"]
    
    if available_threads == 1
        println("  ⚠️ 單執行緒環境:")
        println("    - 建議: 使用 'julia -t auto' 啟動以啟用多執行緒")
        println("    - 預期改進: 2-4x 性能提升（取決於 CPU 核心數）")
    elseif available_threads <= 4
        println("  ✅ 中等並行環境 ($available_threads 執行緒):")
        println("    - 適合: 中小型數據集分析")
        println("    - 建議: 考慮增加執行緒數以處理更大數據集")
    else
        println("  🚀 高並行環境 ($available_threads 執行緒):")
        println("    - 適合: 大型數據集和複雜分析")
        println("    - 建議: 充分利用分散式計算功能")
    end
    
    # 基於基準測試結果的建議
    if haskey(benchmark_results, "overall")
        overall = benchmark_results["overall"]
        success_rate = overall["success_rate"]
        parallel_efficiency = overall["parallel_efficiency"]
        
        println("\n📊 基於測試結果的建議:")
        
        if success_rate >= 0.95
            println("    ✅ 成功率優秀 ($(round(success_rate * 100, digits=1))%)")
        elseif success_rate >= 0.8
            println("    ⚠️ 成功率良好 ($(round(success_rate * 100, digits=1))%)，可能需要調優")
        else
            println("    ❌ 成功率較低 ($(round(success_rate * 100, digits=1))%)，需要檢查系統配置")
        end
        
        if parallel_efficiency >= 0.7
            println("    ✅ 並行效率優秀 ($(round(parallel_efficiency, digits=2)))")
        elseif parallel_efficiency >= 0.5
            println("    ⚠️ 並行效率中等 ($(round(parallel_efficiency, digits=2)))，考慮優化任務分割")
        else
            println("    ❌ 並行效率較低 ($(round(parallel_efficiency, digits=2)))，可能存在瓶頸")
        end
    end
    
    println("\n🎯 優化建議:")
    println("    1. 對於大數據集，優先使用並行計算")
    println("    2. 小數據集可能不需要並行（開銷大於收益）")
    println("    3. 定期監控並行效率，調整執行緒數")
    println("    4. 使用分散式功能處理複雜分析任務")
end

# 執行性能分析
analyze_parallel_performance(capabilities, benchmark_results)

# ==========================================
# 9. 總結和清理
# ==========================================

println("\n🎉 步驟 9: 總結和清理")

println("\n✅ 並行計算範例完成！")
println("\n📚 本範例展示了以下並行計算功能:")
println("  1. ✅ 並行 Skip 計算")
println("  2. ✅ 並行配對分析")
println("  3. ✅ 分散式 Wonder Grid 生成")
println("  4. ✅ 分散式歷史數據分析")
println("  5. ✅ 並行性能基準測試")
println("  6. ✅ 性能分析和優化建議")

# 計算總體性能提升
if sequential_time > 0 && parallel_time > 0 && sequential_pairing_time > 0 && parallel_pairing_time > 0
    avg_speedup = ((sequential_time / parallel_time) + (sequential_pairing_time / parallel_pairing_time)) / 2
    println("\n📈 總體性能提升:")
    println("  - 平均加速比: $(round(avg_speedup, digits=2))x")
    println("  - 數據集大小: $(length(large_dataset)) 筆記錄")
    println("  - 使用執行緒數: $(capabilities["available_threads"])")
end

println("\n🚀 下一步建議:")
println("  - 嘗試更大的數據集以獲得更明顯的並行效果")
println("  - 探索自動調優功能")
println("  - 查看其他範例項目")
println("  - 在生產環境中部署並行計算")

println("\n📖 相關文檔:")
println("  - 並行計算 API: doc/api_reference.md#並行計算")
println("  - 性能調優指南: doc/performance_tuning.md")
println("  - 部署指南: doc/deployment_guide.md")

# 清理資源
println("\n🧹 清理系統資源...")
try
    cleaned = auto_cleanup!(GLOBAL_MEMORY_POOL)
    if cleaned > 0
        println("✅ 清理了 $cleaned 個記憶體池項目")
    else
        println("✅ 記憶體池無需清理")
    end
catch e
    println("⚠️ 清理過程中出現問題: $e")
end

println("\n⚡ Wonder Grid Lottery System - 並行計算讓分析更快更強！")
