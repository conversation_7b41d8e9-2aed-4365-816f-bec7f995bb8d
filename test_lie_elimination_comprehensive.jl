#!/usr/bin/env julia

# Comprehensive test of LIE elimination functionality

using Pkg
Pkg.activate(".")

include("src/WonderGridLotterySystem.jl")
using .WonderGridLotterySystem
using StatsBase

# Generate test data with more realistic patterns
function generate_realistic_test_data(num_draws::Int = 2000)
    draws = Vector{Vector{Int}}()
    
    # Create some bias towards certain numbers to make pairing patterns more realistic
    biased_numbers = [5, 12, 18, 23, 31, 37]  # These will appear more frequently
    rare_numbers = [2, 9, 16, 25, 34]  # These will appear less frequently
    
    for i in 1:num_draws
        if rand() < 0.3  # 30% chance of biased draw
            # Include at least 2 biased numbers
            selected = sample(biased_numbers, 2, replace=false)
            remaining = sample(setdiff(1:39, selected), 3, replace=false)
            draw = sort([selected; remaining])
        elseif rand() < 0.1  # 10% chance of rare number draw
            # Include at least 1 rare number
            selected = sample(rare_numbers, 1, replace=false)
            remaining = sample(setdiff(1:39, selected), 4, replace=false)
            draw = sort([selected; remaining])
        else
            # Normal random draw
            draw = sort(sample(1:39, 5, replace=false))
        end
        push!(draws, draw)
    end
    return draws
end

# Test LIE elimination with artificially created overlapping combinations
function test_lie_with_overlapping_combinations(engine, key_number)
    println("\n=== Testing LIE with Artificially Overlapping Combinations ===")
    
    # Generate Wonder Grid combinations
    wg_combinations = generate_combinations(engine, key_number)
    
    # Generate LIE combinations
    lie_engine = LIEEliminationEngine(engine.pairing_engine)
    lie_combinations = generate_lie_combinations(lie_engine, key_number)
    
    # Create a mixed set that includes both Wonder Grid and some LIE combinations
    mixed_combinations = copy(wg_combinations)
    
    # Add some LIE combinations to the mix to simulate overlap
    num_to_add = min(20, length(lie_combinations))
    for i in 1:num_to_add
        push!(mixed_combinations, lie_combinations[i])
    end
    
    println("Created mixed combination set:")
    println("  Wonder Grid combinations: $(length(wg_combinations))")
    println("  Added LIE combinations: $num_to_add")
    println("  Total mixed combinations: $(length(mixed_combinations))")
    
    # Apply LIE elimination to the mixed set
    filtered_combinations = apply_elimination_filter(lie_engine, mixed_combinations)
    eliminated_count = length(mixed_combinations) - length(filtered_combinations)
    
    println("\nLIE elimination results:")
    println("  Original mixed set: $(length(mixed_combinations))")
    println("  After LIE elimination: $(length(filtered_combinations))")
    println("  Eliminated: $eliminated_count")
    println("  Elimination rate: $(round(eliminated_count / length(mixed_combinations) * 100, digits=1))%")
    
    return eliminated_count > 0
end

println("=== Comprehensive LIE Elimination Testing ===")

# Create realistic test data
println("\n1. Creating realistic test data...")
test_data = generate_realistic_test_data(2000)
println("Generated $(length(test_data)) test draws with realistic patterns")

# Initialize Wonder Grid engine
println("\n2. Initializing Wonder Grid engine...")
engine = WonderGridEngine(test_data)
println("Engine initialized successfully")

# Select a key number
println("\n3. Selecting key numbers...")
key_numbers = select_key_numbers(engine)
println("Found $(length(key_numbers)) favorable key numbers: $(key_numbers[1:min(10, length(key_numbers))])")

if !isempty(key_numbers)
    key_number = key_numbers[1]
    println("Using key number: $key_number")
    
    # Analyze pairing distribution for this key number
    println("\n4. Analyzing pairing distribution...")
    all_pairings = calculate_all_pairings(engine.pairing_engine)
    key_pairings = [(pair, freq) for (pair, freq) in all_pairings if pair[1] == key_number || pair[2] == key_number]
    sort!(key_pairings, by = x -> x[2], rev=true)  # Sort by frequency, highest first
    
    println("Pairing analysis for key number $key_number:")
    println("  Total pairings: $(length(key_pairings))")
    if length(key_pairings) >= 10
        println("  Top 5 pairings:")
        for i in 1:5
            pair, freq = key_pairings[i]
            other_num = pair[1] == key_number ? pair[2] : pair[1]
            println("    $key_number-$other_num: $freq occurrences")
        end
        
        println("  Bottom 5 pairings:")
        for i in (length(key_pairings)-4):length(key_pairings)
            pair, freq = key_pairings[i]
            other_num = pair[1] == key_number ? pair[2] : pair[1]
            println("    $key_number-$other_num: $freq occurrences")
        end
    end
    
    # Test standard LIE elimination
    println("\n5. Testing standard LIE elimination...")
    lie_analysis = generate_combinations_with_lie_analysis(engine, key_number)
    
    println("Standard LIE Analysis:")
    println("  Original combinations: $(lie_analysis["original_count"])")
    println("  LIE combinations generated: $(length(lie_analysis["lie_combinations"]))")
    println("  Filtered combinations: $(lie_analysis["filtered_count"])")
    println("  Natural elimination: $(lie_analysis["eliminated_count"])")
    println("  Cost savings: $(round(lie_analysis["cost_savings_percentage"], digits=2))%")
    
    # Test with overlapping combinations
    elimination_occurred = test_lie_with_overlapping_combinations(engine, key_number)
    
    if elimination_occurred
        println("\n✓ LIE elimination is working correctly when overlaps exist")
    else
        println("\n⚠ No elimination occurred even with artificial overlaps")
    end
    
    # Test different elimination thresholds
    println("\n6. Testing different elimination thresholds...")
    thresholds = [0.05, 0.1, 0.2, 0.3, 0.5]
    
    for threshold in thresholds
        lie_engine = LIEEliminationEngine(engine.pairing_engine)
        lie_engine.elimination_threshold = threshold
        
        lie_combinations = generate_lie_combinations(lie_engine, key_number)
        println("  Threshold $(threshold): $(length(lie_combinations)) LIE combinations generated")
    end
    
    # Test the concept: show what LIE elimination is designed to do
    println("\n7. Demonstrating LIE elimination concept...")
    println("LIE elimination is designed to:")
    println("  - Generate combinations from LEAST frequent pairings")
    println("  - These represent combinations UNLIKELY to win")
    println("  - Wonder Grid uses MOST frequent pairings (top 25%)")
    println("  - LIE combinations should be DIFFERENT from Wonder Grid combinations")
    println("  - This separation is CORRECT behavior, not a bug")
    
    wg_combinations = generate_combinations(engine, key_number)
    lie_engine = LIEEliminationEngine(engine.pairing_engine)
    lie_combinations = generate_lie_combinations(lie_engine, key_number)
    
    println("\nActual results:")
    println("  Wonder Grid combinations (top pairings): $(length(wg_combinations))")
    println("  LIE combinations (bottom pairings): $(length(lie_combinations))")
    
    # Check if any Wonder Grid combinations accidentally use bottom pairings
    wg_set = Set(wg_combinations)
    lie_set = Set(lie_combinations)
    overlap = intersect(wg_set, lie_set)
    
    println("  Natural overlap: $(length(overlap)) combinations")
    println("  This low overlap confirms the strategies are working as designed")
    
else
    println("No favorable key numbers found")
end

println("\n=== Comprehensive LIE Testing Complete ===")