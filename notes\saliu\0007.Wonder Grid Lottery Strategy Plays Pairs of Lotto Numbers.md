---
created: 2024-06-21T08:45:12 (UTC +08:00)
tags: [strategy,strategies,wonder,grid,wonder-grid,lotto,pairs,pairing,odds,software,lottery,lotto,system,frequency,statistics,drawings,draws,numbers,combinations,]
source: https://saliu.com/bbs/messages/638.html
author: 
---

# Wonder Grid Lottery Strategy Plays Pairs of Lotto Numbers

> ## Excerpt
> An efficient lotto, lottery strategy, system is Lotto Wonder Grid, founded on lotto number pairs, pairing, frequency: Play the most frequent pairs.

---
Written on March 24, 2001 - and later...

The **_Wonder Grid_ lottery strategy** is based on two elements:

-   1) Playing a **favorite** (key) number based on the _**Fundamental Formula of Gambling (FFG)**_;
-   2) Playing only the **most frequent pairings** of the favorite number.

• Selecting a key number follows the procedures presented on the _**Winning Lottery Strategy**_ page (_LottoWin.htm_ – see link in the Resources section). There is no denial at this step. Each and every lotto number repeats after a number of drawings less than its median in at least 50% of the cases. _**FFG**_ explains in mathematical terms such behavior of lottery numbers. The formula is also validated by real lottery drawings. I haven't heard of any report to the contrary, in any lottery. I am convinced now _**FFG**_ will not be contradicted in this regard, ever.

Let's take the case of the lotto 6/49 game, the most common worldwide. The median is 6 (use my probability software **SuperFormula** to do the calculations for a **50%** degree of certainty **DC**). We need software to plot the skips for each lotto number. A skip shows how many drawings a number waits between hits. My comprehensive lottery software **MDIEditor Lotto WE** and **Super Utilities** (main menu of **Bright / Ultimate Software**) do the best job at charting the lotto **skips**. The **skips** will look like this:

```
<span size="5" face="Courier New"><b>Number: 8
Skips:  4  11  20  1  22  6  4  7  3  8  4  3  2  6  0  12  13  0  6  1  3  1...
Median Skip:  4

Number: 9
Skips:  5  5  2  0  35  1  1  4  0  23  1  11  16  5  0  17  21  11  4  3  8
Median Skip:  5
</b></span>
```

If we add up the skips _0, 1, 2, 3, 4, 5_ — for any number — the sum will represent at least 50% of all cases. (The case above shows actual data in a 6/69 lotto case, where the median is 8). Calculating the probability to selecting a lotto number with a skip below the median involves two steps. There is a 50% chance (or 1/2) that a lotto number will repeat after an amount of drawings lower than the median. But the median takes 6 values, from 0 to 5. The simultaneous probability of the two events is 1/2 x 1/6 = 1/12 (8.3%). We should be successful every 12 tries (drawings) in picking a favorite lotto 6/49 number. Read all mathematical foundation: [**FFG Median, Skips**, _**Lottery, Lotto Filtering, Probability, Odds, Jackpot**_](https://saliu.com/bbs/messages/923.html).

So far, this is the strongest part of the potential _killer lotto strategy_, probabilistically speaking.

•• The second part of the strategy is newer in my research. It is based on the pairings of the lotto numbers. Given a range of drawings, each lotto number shows a clear bias towards being drawn with the rest of the lotto numbers. The newest version of my **Super Utilities** calculates the frequencies of all lotto pairings for every lotto number. It is a major component of **Bright6** (2011), definitely the most powerful collection of lotto software applications. The following pairing report was done by function _F = Frequency Reports by Lotto Number_.

[![Pay Membership, Download Software: Lottery, Gambling, Roulette, Sports, Powerball, Mega Millions.](https://saliu.com/ScreenImgs/lotto-pairings.gif)](https://saliu.com/membership.html)

The report looks like this:

```
<span size="5" face="Courier New"><b>Number:  1     Hits:   19   ( 9.5 %)
With #:  8 16 30 22 25 14 31 32 42 27 29 17 21 15 33 35 36 ...
Hits:    4  4  4  3  3  3  3  3  3  2  2  2  2  2  2  2  2 ...
Pairs total: 95

Number:  2     Hits:   23   ( 11.5 %)
With #: 36 40  6 48 68 24 25  7  9 42 11 50 61 6...
Hits:    6  6  4  4  4  3  3  3  3  3  3  3  3 3 ...
Pairs total: 115
...
Number:  69    Hits:   18   ( 9 %)
With #: 19 51 63  3 22 26 36 40  6  9 64 29 34...
Hits:    4  4  4  3  3  3  3  3  3  3  3  2  2  2  2...
Pairs total: 90
</b></span>
```

I know the pair frequencies are based on _**FFG**_, although I haven't done an analysis as thorough as in Step 1.

The **top 10%** of the pairings represents **25%** of total frequencies for each number. The _top_ is defined as the most frequent part in a pairing string (starting from left to right). In the case above (still a lotto 6/69 game) the _top 10%_ means the first 7 pairs (10% of 69 is approximately 7). The first 10% pairs for lotto number 1 add-up to 24 (25.3% of all 95 cases for that lotto number). _4+4+4+3+ 3+ 3+ 3 = 24_.

Conversely, the **bottom 10%** pairs sum-up to 0 (zero).

The **top 25%** of the pairings represents **50%** of total frequencies for each number. That is, the 25% segment of the most frequent pairs account for 50% of the entire pairing frequency for any given lotto number.

The **top 50%** of the pairings represents **75%** of total frequencies for each number. That is, the first half of the pairs (from left to right) accounts for 75% of the entire pairing frequency for any given lotto number.

The other type of pairing reporting in **Super Utilities** can be performed by function _2 = Lotto Pairs Rundown_.

![Bright6 integrated lotto 6 software generates useful lottery pairs reports.](https://saliu.com/ScreenImgs/lotto-pairs.gif)

Again, I repeat the very important point here. My analysis of the lotto pairings is not as thorough as other domains of my gambling mathematics. But this is an open debate and we should be able to find the fault, if it exists. So, I assume that the pairing percentages above hold true for any lotto game, at any given point in a lotto history. Based on the premise, I decide on the following _lottery **wonder grid** strategy_:

-   1) to play a **favorite** lotto number; AND
-   2) play only its **top 25%** pairs.

In a hypothetical case, I will play number 1, since it shows as the last skip a value between 0 and 5. For a lotto 6/49 game, I will also play the most frequent 12 pairs (25% of the remaining 48 lotto numbers). Lotto number 1 comes out with its top 25% pairs in 50% of its appearances. The chance is 1/2 that the lotto number will come out with such pairings – WHEN it hits!

Every combination I play will contain the number 1 (the favorite). The remaining 5 numbers will consist of combinations of the 12 lotto numbers in the most frequent 25% pairs. Total combinations of 12 taken 5 at a time: C(12,5) = 792. The combined probability to hit the jackpot is 1/12 x 1/2 = 1/24.

It sounds extraordinary: the chance is 1 in 24 to win the lotto jackpot with 792 combinations. 792 x 24 = 19008. For a total cost of $19,008 it is possible to win the one million dollar jackpot! Actually, I saw such lotto combinations – containing a favorite number plus 5 numbers from its top 25% pairs. Checking for winning lotto numbers this way is a little trickier. First, the software should break down the top pairs in groups of 5, and then add the favorite number to each combination. Then, it should check the results against real drawings.

Curiously, this strategy does NOT work with static lotto wheels. Instead of playing all possible combinations of 5, we may think of wheeling the 12 most frequent pairs. Let's say the best _3 of 5_ 12-number static wheel amounts to 6 combinations. 24 x 6 = 144 combinations, while the 3rd prize is lower than $144. Even playing a 4-combination _3 of 5_ wheel makes no sense at all. 24 x 4 = 96, probably still below the prize! One more reason why playing static lotto wheels is a no-no.

This is an open debate. I hope there will be several worthy contributions. Somebody made a contribution already, before I posted this lotto strategy. See the post _“Eliminate more than one of the worst lotto pairings! – Ramanujan Mathematician”_.

There is another category. There are those who don't want to make their _"killer"_ lottery strategies public. I have received several requests to take a look at or try to improve lottery or gambling systems. The requests were private and their authors did not want to make public their ideas. They have a point, but it's not a strong point. I know how humans function. Humans have progressed, in great part, because they make public their efforts. Effort here has a generalized meaning. That's not pure generosity. We make our efforts public because we expect positive consequences that serve us.

The _**Fear-Survival system**_ is selfish, to be sure, even when it shows signs of unselfishness. Quoting from a Beatles song: _”You take what you make"_. Speaking from my personal experience. Don't more than a few computing beasts consider me nuts because I make public my gambling and lottery systems?! So, what? I am not at a loss. On the contrary, I have gained more knowledge and improved my mathematical systems to higher degrees. I feel a force inside that pushes out many of my ideas. The force has come up with something new and worthier. I just want more free space in my mind. Believe me, you'll come up with even better ideas after you make public some of your private thoughts.

![The role of Pairs, pairings, pairing frequency in winning lottery software, lotto software.](https://saliu.com/bbs/messages/HLINE.gif)

[

## <u>Resources in Lottery Software, Strategies, Systems Lotto Wheeling</u>

](https://saliu.com/content/lottery.html)See a comprehensive directory of the pages and materials on the subject of lottery, lotto, software, systems, and wheels. If the page content is of interest to you, copy-and-paste to that lottery page of yours: _SaliuLottery.txt_ or something similar. By editing that page in your own words will result in a very effective lottery manual tailor-made for you.

-   The Main [_**Lotto, Lottery, Software, Strategy**_](https://saliu.com/LottoWin.htm) page  
    Presenting software to create free winning lotto, lottery strategies, systems based on mathematics. Get your lotto systems or wheels, the best lottery, lotto software, combinations, winning numbers.
-   [_**Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies**_](https://saliu.com/Newsgroups.htm).  
    Read a genuine analysis of Excel spreadsheets applied to lottery and lotto software development, systems, and strategies. Combining Excel analysis with powerful lottery and lotto software programmed by this author, _Parpaluck_.
    
    [_**<u>MDIEditor Lotto WE</u>: Lottery Software Manual, Book, ebook, Help**_](https://saliu.com/MDI-lotto-guide.html).  
    ~ Also applicable to LotWon lottery, lotto software; plus Powerball/Mega Millions, Euromillions.
    
-   [_**Visual Tutorial to the ultimate lotto, lottery software**_](https://saliu.com/forum/lotto-book.html).
-   [**Skips Lottery, Lotto, Gambling, Systems, Strategy**](https://saliu.com/skip-strategy.html).
-   [_**Lottery Utility Software**_](https://saliu.com/lottery-utility.html): Pick-3, 4 Lottery, Lotto-5, 6, Powerball, Mega Millions, Thunderball, Euromillions.
-   [_**Lotto Filters, Reduction Strategies in Lottery Software**_](https://saliu.com/filters.html).
-   _"The Start Is the Hardest Part"_: [_**Play a Lotto Strategy, Lottery Strategies**_](https://forums.saliu.com/lottery-strategies-start.html); Work with Data Files.
-   [_**Lotto wheels**_](https://saliu.com/lotto_wheels.html) for lotto games drawing 5, 6, or 7 numbers.  
    The most advanced theory of lotto wheels or reduced lottery systems. Get also original lotto wheels for lotto games drawing 5, 6, or 7 numbers: Balanced, randomized, and free.
-   Download [**_Software: Lottery, Lotto, Powerball, Mega Millions, Euromillions_**](https://saliu.com/free-lotto-lottery.html).

<u>Follow Ups</u>  

-   [_**Optimal lotto drawings range to analyze for lottery wonder grid system**_](https://saliu.com/bbs/messages/663.html) **Ion Saliu** _4/07/2001._
-   [_**Optimal lottery drawings range to analyze: Illinois Lotto**_](https://saliu.com/bbs/messages/664.html) **Bill Smith** _4/09/2001._
-   [**BELLOTTO, BELLBET, BETUS**: _**Software to generate random combinations for lotto, lottery, gambling**_](https://saliu.com/bbs/messages/665.html) **Ion Saliu** _4/09/2001._
-   [_**Wonder-grid lottery strategy hit lotto jackpot in Belgium Lottery**_](https://saliu.com/bbs/messages/647.html) **El Loco** _3/29/2001._
-   [_**Lottery Pairs System, Lotto Pair Strategy**_](https://saliu.com/bbs/messages/645.html) **lottoscorp** _3/28/2001._
-   [_**Likely winning lotto numbers to wheel by best lottery wheeling software**_](https://saliu.com/bbs/messages/644.html) **KM** _3/27/2001._
-   [_**Lottery system: Lotto numbers, skips of lotto numbers, sum of gaps**_](https://saliu.com/bbs/messages/646.html) **BigJer** _3/28/2001._
-   [_**Lotto deltas in powerful, winning lottery software**_](https://saliu.com/bbs/messages/648.html) **KM** _3/29/2001._
-   [_**Lottery Analysis in the Spirit of Ramanujan, Greatest Indian Mathematician**_](https://saliu.com/bbs/messages/641.html) **Ramanujan** _3/25/2001._

![Pairs, pairings, pairing frequency: BEST Winning lotto lottery strategy system wonder grid.](https://saliu.com/bbs/messages/HLINE.gif)

**[Home](https://saliu.com/index.htm) | [Search](https://saliu.com/Search.htm) | [New Writings](https://saliu.com/bbs/index.html) | [Software](https://saliu.com/infodown.html) | [Odds, Generator](https://saliu.com/calculator_generator.html) | [Contents](https://saliu.com/content/index.html) | [Forums](https://forums.saliu.com/) | [Sitemap](https://saliu.com/sitemap/index.html)**

![Powerful lotto, lottery strategy, system is Wonder-Grid of number pairs.](https://saliu.com/bbs/messages/HLINE.gif)
