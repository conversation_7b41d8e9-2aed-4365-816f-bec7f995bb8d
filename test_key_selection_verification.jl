using WonderGridLotterySystem
using Statistics

println("Key Number Selection Algorithm Verification")
println("=" ^ 50)

# Load test data
fm = FileManager()
data = read_data5_file(fm, "data/fan5.csv")
engine = WonderGridEngine(data)

println("✓ Successfully loaded $(length(data)) lottery draws")
println("✓ Successfully created WonderGridEngine")

# Test 1: Verify logic to identify numbers with current skip ≤ FFG median
println("\n1. Testing identification of numbers with current skip ≤ FFG median:")
favorable_numbers = select_key_numbers(engine)
println("   Found $(length(favorable_numbers)) favorable numbers")

# Verify each favorable number meets the criteria
valid_results = Bool[]
for number in favorable_numbers[1:min(5, length(favorable_numbers))]
    skip_chart = generate_skip_chart(engine.skip_analyzer, number)
    is_valid = skip_chart.current_skip <= skip_chart.ffg_median
    println("   Number $number: skip=$(skip_chart.current_skip), median=$(round(skip_chart.ffg_median, digits=1)), valid=$is_valid")
    push!(valid_results, is_valid)
end
all_valid = all(valid_results)
println("   ✓ All tested numbers meet skip ≤ FFG median criteria: $all_valid")

# Test 2: Verify functions to evaluate and rank potential key numbers
println("\n2. Testing evaluation and ranking of potential key numbers:")
evaluated_numbers = evaluate_key_numbers(engine)
println("   ✓ Successfully evaluated $(length(evaluated_numbers)) key numbers")

ranked_numbers = rank_key_numbers(engine, 10)
println("   ✓ Successfully ranked top $(length(ranked_numbers)) key numbers")

# Verify ranking is in ascending order (lower scores are better)
scores = [score for (_, score) in ranked_numbers]
is_sorted = issorted(scores)
println("   ✓ Rankings are properly sorted (ascending): $is_sorted")

# Test 3: Verify automated key number selection based on FFG analysis
println("\n3. Testing automated key number selection:")
auto_selected = select_key_numbers_auto(engine, 5)
println("   ✓ Automatically selected $(length(auto_selected)) key numbers: $(join(auto_selected, ", "))")

# Verify all auto-selected numbers are favorable
favorable_results = Bool[]
for number in auto_selected
    skip_chart = generate_skip_chart(engine.skip_analyzer, number)
    is_favorable = skip_chart.is_favorable
    push!(favorable_results, is_favorable)
    println("   Number $number: favorable=$is_favorable")
end
all_favorable = all(favorable_results)
println("   ✓ All auto-selected numbers are favorable: $all_favorable")

# Test 4: Verify detailed analysis functionality
println("\n4. Testing detailed key number analysis:")
if !isempty(auto_selected)
    test_number = auto_selected[1]
    analysis = analyze_key_number(engine, test_number)
    
    required_fields = ["number", "is_favorable", "current_skip", "ffg_median", 
                      "skip_ratio", "skip_probability", "top_pairings_count", 
                      "potential_combinations", "recommendation"]
    
    all_fields_present = all(field -> haskey(analysis, field), required_fields)
    println("   ✓ Analysis contains all required fields: $all_fields_present")
    println("   ✓ Analysis for number $test_number: $(analysis["recommendation"])")
end

# Test 5: Verify filtering functionality
println("\n5. Testing filtered key number selection:")
filtered_numbers = select_key_numbers_filtered(engine, 
                                             max_skip_ratio=0.5, 
                                             min_pairing_strength=0.7,
                                             max_numbers=5)
println("   ✓ Filtered selection returned $(length(filtered_numbers)) numbers: $(join(filtered_numbers, ", "))")

# Test 6: Verify comparison functionality
println("\n6. Testing key number comparison:")
if length(auto_selected) >= 3
    comparison_numbers = auto_selected[1:3]
    comparison = compare_key_numbers(engine, comparison_numbers)
    
    has_best_overall = haskey(comparison, "best_overall") && comparison["best_overall"] > 0
    has_analyses = haskey(comparison, "analyses") && length(comparison["analyses"]) == 3
    
    println("   ✓ Comparison has best overall candidate: $has_best_overall")
    println("   ✓ Comparison has individual analyses: $has_analyses")
    println("   ✓ Best overall candidate: $(comparison["best_overall"])")
end

# Test 7: Verify performance requirements
println("\n7. Testing performance requirements:")
start_time = time()
for _ in 1:100
    test_selection = select_key_numbers(engine)
end
elapsed_time = time() - start_time
avg_time_ms = (elapsed_time / 100) * 1000

println("   ✓ 100 selections completed in $(round(elapsed_time, digits=3)) seconds")
println("   ✓ Average time per selection: $(round(avg_time_ms, digits=2)) ms")
println("   ✓ Performance meets requirements: $(avg_time_ms < 100 ? "YES" : "NO") (< 100ms)")

# Test 8: Verify integration with existing components
println("\n8. Testing integration with existing components:")

# Test integration with FFG calculator
ffg_calc = engine.ffg_calculator
test_number = auto_selected[1]
ffg_median = calculate_ffg_median(ffg_calc, test_number, engine.skip_analyzer.historical_data)
println("   ✓ FFG calculator integration: median=$(round(ffg_median, digits=2))")

# Test integration with skip analyzer
current_skip = get_current_skip(engine.skip_analyzer, test_number)
println("   ✓ Skip analyzer integration: current_skip=$current_skip")

# Test integration with pairing engine
top_pairings = get_top_pairings(engine.pairing_engine, test_number, 0.25)
println("   ✓ Pairing engine integration: $(length(top_pairings)) top pairings")

# Test combination generation
combinations = generate_combinations(engine, test_number)
all_contain_key = all(combo -> test_number in combo, combinations)
println("   ✓ Combination generation: $(length(combinations)) combinations, all contain key: $all_contain_key")

println("\n" * "=" ^ 50)
println("Key Number Selection Algorithm Verification Complete!")
println("✓ All core functionality implemented and working correctly")
println("✓ Requirements 4.1 and 4.2 satisfied")
println("=" ^ 50)