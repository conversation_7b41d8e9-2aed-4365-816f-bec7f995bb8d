---
created: 2025-07-24T21:26:40 (UTC +08:00)
tags: [delta lotto,lottery,deltas,systems,strategy,software,statistics,drawings,draws,difference,analysis,adjacent,neighboring numbers,]
source: https://saliu.com/delta-lotto-software.html
author: 
---

# Delta Lotto, Lottery Software, Systems, Strategies

> ## Excerpt
> Deltas or differences between adjacent lotto numbers make good lottery strategies, systems, software programs to win even the jackpot more frequently.

---
The ultimate software for analyzing lottery **deltas**, creating lotto **delta** systems, generating combinations:

-   **DeltasPick3** ~ for pick-3 daily games;
-   **DeltasPick4** ~ for pick-4 lotteries;
-   **DeltasLotto5** ~ for 5-number lotto games;
-   **DeltasLotto6** ~ for 6-number lottos;
-   **DeltasHorses3** ~ for horse racing trifectas.
-   Version **2.0** January 2016 added a very important function: _**Strategy Checking**_
-   Version **3.0** February 2016 added _**layers of data analysis**_ as in the mainstream **LotWon** software.
-   Version **3.1** April 2016 added the _**inner LIE strategy filtering**_ as in the mainstream **LotWon** software; improvements in functionality.
-   Please be advised that these programs, released in 2015, require a special form of _membership to download software_: _**Ultimate Lottery Software**_. Click on the top banner to learn the terms, conditions, and availability. All my software is always announced in the **Forums**, **New Writings**, **Software Downloads**. The presentation pages are always published first. Such Web resources are free to read and help the reader make a decision regarding the licensing of software.
-   The _Delta Lotto_ program runs from the second menu of the respective _**Ultimate Software**_ package, function _D = Deltas (DeltasLotto6)_.

![Delta Lotto Numbers run from menu two of the Ultimate Lottery Software.](https://saliu.com/images/ultimate-lotto-software-61.gif)

Two screenshots — one for the 6-number lotto and one for pick-3 lotteries —

![The best unique delta lottery software calculates deltas, generates combinations for lotto-6.](https://saliu.com/images/delta-lotto.gif)

![There is ultimate delta lotto software for all pick-3, pick 4 daily lottery games.](https://saliu.com/images/delta-pick-software.gif)

-   Axiomatic one, my website hosted lottery _deltas_ since the year of grace 2001: [_**Lottery Deltas Can Build Effective Lotto Strategies, Systems, Software**_](https://saliu.com/bbs/messages/648.html). The differential (delta) parameter has been an important component of my lottery software since the early stages. MDIEditor Lotto has always eliminated unnecessary combinations based on delta restrictions (reduction filters).
-   The same is true about the greatest random number/combination generator for lottery ever — _**IonSaliuGenerator**_. It can be accessed from any page at this site by clicking the link in the footer - _**Odds, Generator**_. The combinations won't show many lotto numbers on the same line or on the same column — much like the real-life lottery drawings.
-   Also, the highly-prized **Sums** software performs thorough statistical analyses, deltas included. Function _S = Sum-up lottery-drawings files_ creates reports on [_sum-totals, root sums, average, standard deviation, **deltas**, medians_](https://saliu.com/forum/lottery-sums.html).

_Delta_ might sound like a curious word in lottery science. It is the name of the letter _D_ in Classical Greek. It has nothing to do with philosophy or _"It sounds Greek to me"_. _D_ or _delta_ stands for _difference_ in mathematics, statistics especially. If 7 and 13 are adjacent (neighboring) numbers in a lotto drawing, the delta is 13 – 7 = _6_. The deltas are positive numbers only; e.g. even if we deduct 7 – 13 is not –6, but always 6. The pick (or daily or digit) lottery games can have deltas equal to 0, as the pick games can have repeat elements (e.g. _599_).

This 6-number lotto drawing in Pennsylvania Lottery _11 19 33 44 46 47_ has the following 5 deltas: _8 14 11 2 1_. Shouldn't be 6 deltas for 6 lotto numbers — for example deducting the first number from the sixth number? Why, no. It is superfluous. That virtual 6th delta simply represents the sum of the 5 _real_ deltas. If generating combinations from 6 deltas, the result is exactly the same as generating lotto combinations from the 5 real deltas!

The same is true about the digit lottery games. This pick-4 drawing in Pennsylvania Lottery _4 3 3 8_ has the following 3 deltas: _1 0 5_. In this case, we could calculate a 4th delta, a meaningful one (8 – 4 = 4). My lottery software, however, does not apply such a rule in order to keep _similarity_ across lottery games.

## <u>II. <i>Report</i> Functions: Calculate Deltas for Past Lottery Drawings</u>

The user always starts the program with the _Report_ function (press _R_ on the keyboard).

![Generate delta lottery reports to create winning lotto strategies, systems.](https://saliu.com/images/delta-lotto-calculate.gif)

The delta lottery software is fast, including the 6-number lotto games — as far as the reporting is concerned. A 5-year old desktop app analyzes 1000 real lottery drawings against 5 million lines in a _D6_ file in around 5 minutes. A _D6_ data file consists of at least 12 million lines (real and simulated drawings) as required by the **Bright / Ultimate** integrated lottoery software. For **DeltasLotto6**, however, just 4 – 5 million _draws to analyze_ is a sufficient range. Read the following screen in the reporting function:

![The delta lotto number reports require a minimum amount of combinations to analyze.](https://saliu.com/images/delta-analyze-draws.gif)

Here is a fragment of the report for a 6/49 lotto game —

![Deltas in lottery can reduce millions of combinations and still win the jackpot.](https://saliu.com/ScreenImgs/lottery-delta-filters.gif)

_Del#1_ represents the difference between the first lotto number in the drawing (e.g. _9_) and the second number (e.g. _14_). _Del#5_ represents the difference between the 5th number in the lotto drawing (e.g. _39_) and the 6th number (e.g. _41_). Most deltas are low: between 1 and the median 6. The fewest deltas are in the 20s; it is extremely rare to see deltas in the 30s. I am referring here to the _6 of 49_ lotto.

The last area of the report shows the _skips_ of all groups of deltas; i.e. how many drawings _elapsed_ between two hits of a particular group. We see in line 1 that _ONE Delta_ is 0. That means, the group hit in two consecutive draws: _Del#1_ was 5 in lines (drawings) #1 and #2. In line 2, _ONE Delta_ is 1. That means, the group skipped one draw: _Del#1_ was 1 in lines (drawings) #2 and #4; the group didn't hit in line 3.

_TWO Deltas_ group in line #3 shows 9. We count to 9 beginning immediately after line 3 and reach line #13 (13 – 3 – 1 = 9). We can notice that _Del#2_ and _Del#4_ repeated.

The _THREE_, _FOUR_, and _FIVE_ delta groups represent exactly their labels. _BOXED Five_ represents the permutations of all 5 _Del_s in the first section of the report. There are 120 permutations of 5 elements. The report looks for all 120 possibilities and reports the first occurrence it found. _FIVE Deltas_ filter only shows the _12345_ permutation; _BOXED Five_ looks for _12345_, and _12354_, and _12543_ .... all the way to _54321_.

The _FIVE Deltas_ group can reach very high levels, as in the cases marked by the asterisk. Actually, those values were over 4 million, even over 5 million! The median is 797908. Translated, it means that in more than half of total drawings, the delta _no-repeat_ is over _700,000_. _BOXED Five_ itself reaches its own huge values, as in the cases marked by the asterisks. It is not usual for all five deltas, straight or boxed, to repeat. Tell that, _in your face_, to them _mystics_ who scream that a lotto 6/49 draw can **repeat** the very next drawing... and again... and again... It hasn't happened anywhere in the world in our lifetime! But what's the big deal IF it happens in one or two lotto games once in a lifetime or two? There are hundreds of lottery games worldwide!

I discovered also another mathematical relationship I already coined: [_**Reversed Birthday Paradox**_](https://forums.saliu.com/wikipedia-piracy.html). (Some sore losers with emotional troubles curse me for achieving discoveries AND taking full credit!) The _**Reversed Birthday Paradox**_ calculates that some 4400 are necessary for a 6/49 lotto drawing to repeat — not necessarily the last combination drawn. And the _degree of certainty DC_ for a repeat to happen is only 50%... I know and I heard that there are _cashbecks_ out there who always play the last combination drawn in their lotto game!

-   Evidently, the _deltas_ can be calculated **vertically** as well — **position by position**, that is. The same is true about the **Markov Chains followers** (see link in _Resources_). I am doing more research; if the results warrant, I should write new software. Currently, a lotto delta program has already a very large report.
-   A _positional_ report will have a _number of deltas_ equal to the _numbers drawn_ in the game. The report would look like this:
    
    ```
    <span size="5" face="Courier New" color="#c5b358"> Line      Drawings          DelP DelP DelP DelP DelP DelP 
      no                          #1   #2   #3   #4   #5   #6   
    
        1   9 14 35 37 39 41       8+   8-   5-   5-   4-   3-  
        2   1  6 30 42 43 44       2   16    6   16   15    5
        3   3 22 24 26 28 49                              
    </span>
    ```
    

## <u>III. Lottery Strategies, Systems Based on <i>Delta</i> Reports</u>

As all genuine users of my lottery software know, _a strategy represents a collection of filters selected from specific reports; the values selected are then fed to specific combination generators_. Selecting filter values, however, requires diligence, patience and confidence. The best way to observe applicable filter values is _sorting the reports by column_. My lottery software provides auxiliary functions to sort files by column and by line. In the case of this type of software, the pertinent function is _T = Sort Reports by Column_. Here is what the report looks like after sorting by the _BOXED Five_ column (filter) —

![Lottery deltas and restrictions in the past 1000 lotto drawings.](https://saliu.com/ScreenImgs/delta-lottery-draws.gif)

**A.**

The top half of the sorted report shows some huge values for _BOXED Five_ (over 200,000) and also _FIVE Deltas_ (over 3 million, actually over 4000000, even 5000000+)! Those values are to be used with the _minimum_ levels of the respective filters, as you well know by now. They would certainly smash the odds! I got some bad news, however. Working with such huge filters takes considerable time to generate combinations for a 6-49 lotto game. I did my best to implement the fastest algorithms — yet, there are way too many calculations for the processor! The 5/43 lotto game I tested is a lot more manageable. The pick lottery software is fantastically faster: It takes just seconds sometimes generating combinations.

I kept the _FIVE Deltas_ and _BOXED Five_ thinking they can be handled with patience. Not to mention that the computers are getting faster quite rapidly. You can generate combinations from huge _minimum_ filters and save them to an output file. You can reuse that file for many drawings (of course, feeding it to the _Purge_ functions in my lotto software).

The bottom half of the sorted report shows _BOXED Five_ filter values to be used mainly with the _maximum_ levels of the filter. Or, even both levels; e.g. _minimum = 10_ and _maximum = 200_. As you notice, you can add high values for _FIVE Deltas_ _minimum_ and also the other delta groups.

**B.**

Another type of lotto strategy is based on the first area of the report: From _Del#1_ to _Del#5_. One looks at the columns one by one and tries to predict the next _Del_ value. One can see that lower _Del_ values repeat more often. I see in column #1 that value 3 skipped too many (in my estimation) drawings, meanwhile the same value shows up more often in other _Del_ columns. The same is true about the value 4 in column #2; value 3 in column #3; value 5 in column #4; value 7 in column #7. One can look also for 10-15 values that haven't occurred in a while. Do not choose the same value for all _Del_s. Do not choose _Del_ values in lexicographic order (e.g. 1, 2, 3, 4, 5 or _vice versa_ 5, 4, 3, 2, 1). Most likely they won't occur in a lifetime or two.

You can use all individual deltas, or only one or two of them. Thus, you can disable some filters. Disabling a filter is accomplished by leaving the field blank at the screen prompt; just press _Enter_.

The + or - sign can help too. The + sign indicates an increase from the previous drawing, while the – sign means a decrease from the previous draw.

**C.**

Apply the 5 individual deltas (_Del_) to the _**LIE elimination**_ feature of **LotWon** lottery software. As per the above paragraph, you can enter all 5 _Del_s in lexicographic order (e.g. 1, 2, 3, 4, 5 or _vice versa_ 5, 4, 3, 2, 1). They are very good candidates for the _**LIE elimination**_ functions. If you plot real lottery drawings on the grid cards, it is very rare to see more than 3 lotto numbers on the same line or in the same column.

We can notice that the majority of the drawings do not have any _Del_ 20 or larger. In the 6-49 game, typing _20_ for one _Del_ generates 118,755 combosnations. That means a total of 593,775 for all 5 _Del_s. Place all those unwanted combinations in a _LIE_ file and use it often.

I haven't seen a _6 of 49_ lotto case with any _Del_ being 30 or larger. One _Del_ set at 30 generates 11,628 combinations for a total of 58,140 for all 5 _individual deltas_. One _Del_ set at 31 generates 8,568 combinations for a total of 42,840 for all 5 _Del_s. Put them all combosnations in a permanent _LIE_ file.

By the way: There is a limit to an individual delta. The maximum a _Del_ can reach is equal to _N – K + 1_; for _49/6_ is _49 – 6 + 1 = **44**_. It always generates 1 combo: _1, 45, 46, 47, 48, 49_. The limit of deltas is _**9**_ for pick games.

## <u>IV. <i>Check Delta</i> Lotto Strategies in Past Drawings</u>

![Check if delta lottery strategies hit in past drawings and see the skips.](https://saliu.com/images/delta-lottery-strategy.gif)

**Version 2** of the software implements also **strategy checking**. You selected a **lottery strategy**; i.e. set the value of a **key filter** to very high or very low levels (you might have also set more _moderate_ values to a few _auxiliary filters_). You want to know now if the respective strategy hit more than once before. If so, next you want to know in what lottery drawings the strategy hit. Finally, you want to see how many draws the lottery strategy **skipped** between **hits** and what the _skip median_ was.

You might want to know that _not all filters are equal_. I discussed the issue several times at this very site and in public forums. I divide the [_**filters in static and dynamic**_](https://saliu.com/bbs/messages/919.html). I do take full credit for the discovery of the _dynamic lotto filters_ in the 1980s. Most of the filters in my lottery software are dynamic.

I held the _static filters_ in low regards until early 2000s. After I coined the term _lottery filters_, other software developers started to incorporate filters in their programs. But they haven't been able to figure out the dynamic filters, so they only employed _static restrictions_ (or _reduction parameters_, or _eliminating conditions_, or... _filters_). And these are such static filters: _odd/even_, _high/low_. A few developers have tried also _lotto decades_ and/or _last digits_.

There are serious problems related to the static filters: They are very _streaky_ and _inefficient_. The static filters always eliminate the same amount of lottery combinations. For example, playing _3-odds & 3-even_ in a 6/49 lotto game always leaves 4,655,200 combinations — always the same combinations. Besides, how efficient is to "play" 4,655,200 lotto combinations?! I found a better method to utilize static lottery filters. I coined another term: _**LIE elimination strategy**_. Instead of generating combinations _"to play"_, I apply static filters to generate combinations _**"NOT to play"**_. You might have already read here several dedicated pages dedicated to the _**LIE elimination strategy**_ or [_**REVERSED lottery strategy**_](https://saliu.com/reverse-strategy.html).

So, not all lottery filters were "born" equal. Here is a test I did while creating this **strategy checking** function in this lottery delta software. _Del\_#1_ and _Del\_#2_ are _static_ filters. They always generate the same amount of combinations (pick-3 straight sets, in this case). I took a random case: _Del\_#1_ = 7 and _Del\_#2_ = 7. The strategy occurs just 4 times in 1000 lottery drawings and it always generates 6 straight sets. There is a better pick-3 strategy, while still utilizing on static filter; e.g. _Del\_#1_ = 7 and _Del\_#2_ not set (can take any value). This time, I also employ a couple of dynamic filters: _BOXED Two_ = 100 and _TWO Deltas_ = 200. This _static AND dynamic_ strategy generates the same amount of combinations: 6. This filter setting, however, occurs around 17 times in 1000 draws! A fourfold increase in efficiency!

You already know that my lottery strategies can also enable just a few more [_**lottery filters at run-time**_](https://saliu.com/filters.html) (very safe values). Here is a pick-3 lottery strategy purely based on just one dynamic filter: _BOXED Two = 450_. The strategy hit 14 times in 1000 drawings. It generates 2 straight sets (_0 9 0_ and _9 0 9_ at this time of writing). You can still employ a few more filters, at very safe levels, at run-time. An interesting phenomenon happens now and then: NO combination will be generated. More often than not, it happens not because the few additional filters were wrongly set — but because the strategy (the pivot filter) simply was not “scheduled” to hit. You just saved a couple of bucks that drawing!

-   There are multiple strategy checking functions, specific to each type of software in the **Bright/Ultimate** packages. The user can combine all possible strategies in one report. I created special software to [_**cross-reference lottery strategies**_](https://saliu.com/cross-lines.html) in a program named **FileLines**. It can be accessed in menu #2, function _T = Cross-Checking STrategies_.
-   Indubitably axiomatic one, you will probably create numerous lottery strategies. I name each strategy meaningfully; e.g. _STR3-Fr-021_. I start a name with _STR_, then a digit indicating the type of lottery game (_3_ stands for _pick-3_ in this case). It refers to a strategy in the **SkipDecaFreq3** application using the frequency groups _0-2-1_.
-   I name the strategy report to match the strategy file: _STR3-Fr-021.REP_. It hit 127 times in 1000 drawings, with a median skip of 5. I add at the end of the main report the files created by **FileLines**.
-   I look then at all reports in _STR3-Fr-021.REP_ to find filters with very safe values. The viewing tool of choice is **Notepad++**.
-   You only need to set a few more filters (additional eliminating parameters) in other applications of the **Bright/Ultimate** bundles. Simply right them down in a dedicated notebook. You can always look back at the parameters you set for the respective strategy. Write the strategy name at the top of the page and the date. Write down also the number of combinations generated by your lotto strategy for the corresponding date. Of course, you can also do that in a **Notepad++** file.
-   You then run the combination generator in the application of the strategy file (**SkipDecaFreq3** in this example). We will have a _primary output_ file, named accordingly _STR3-Fr-021.OUT_. We'll _**Purge**_ the _primary output_ file in the applications of the additional parameters we set in the lottery strategy.

## <u>V. Generate Combinations from <i>Delta</i> Lottery Strategies, Systems</u>

There are two types of combination generators in this lottery _Delta_ software. The screens are self-explanatory:

![The two lotto delta combination generators are easy to understand and run.](https://saliu.com/images/delta-lotto-generator.gif)

**1.** Generator _U = Generate User-Input Deltas_  
You type numbers at the screen prompts; e.g. Del#1 = 5; Del#2 = 1; Del#3 = 11, etc. You don't have to set every Del filter: User can disable some filters. Disabling a filter is accomplished by leaving the field blank at the screen prompt; just press _Enter_. Usually in **LotWon** lotto software disabling a filter is equivalent to typing _0_. Problem is, _0_ is a **valid delta** in the pick lotteries. Therefore, the best practice is to press _Enter_ for the filter you want to disable. If you want a _Del_ equal to **0**, you must type **0** instead of pressing _Enter_.

The parameters as set in the screen above (_Del#1_ = 0 and _Del#2_ disabled will always generate 100 pick 3 straight sets. The first 2 digits are equal and followed by one of the 10 digits; e.g. _880_, _881_, _882_...)

**2.** Generator _E = Eliminate Past Deltas_  
This is exactly how you do it in the traditional **LotWon** lottery software. You previously chose a strategy and you type the filter values at the corresponding screen prompts. Every filter has a _minimum_ level and a _MAXimum_ level. You can set all filters, or, much more often, you only select one or 2-3 filters, while disabling the rest. For example, set only _BOXED Five_; _minimum = 10_ and _MAXIMUM = 200_;

![The computer processor is tested with delta filters in 6-number lotto games.](https://saliu.com/images/delta-lottery-filters.gif)

## <u>VI. <i>Purge</i> Combinations from Output Files Applying Lotto <i>Delta</i> Filters</u>

The _**Purge**_ feature is present in just about every piece of software I've created. The function takes a text file of previously generated combinations and **purges** it; i.e. it removes combinations based on filter settings.

The output file can be:
