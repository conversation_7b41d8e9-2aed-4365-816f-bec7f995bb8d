---
created: 2025-07-23T18:11:45 (UTC +08:00)
tags: [roulette,systems,strategy,gambling,system,software,spins,free,odds,win,winning,mathematics,theory,Birthday Paradox roulette,wheel half,marquee,]
source: https://saliu.com/Roulette.htm
author: 
---

# Mathematics of Roulette Systems, Strategies, Software

> ## Excerpt
> The roulette strategies, roulette systems are founded on mathematics, probability theory, analyses of real, actual casino spins, and running the best roulette software.

---
![Roulette mathematics, statistics, systems, software to win in casinos.](https://saliu.com/images/roulette-spin.gif)

### I. [Mathematics of Winning Roulette Strategy, Systems](https://saliu.com/Roulette.htm#Math)  
II. [Glamour and Attraction of Roulette on Gamblers](https://saliu.com/Roulette.htm#Gamble)  
III. [Statistical Analysis of Roulette Spins (Double- Zero)](https://saliu.com/Roulette.htm#Stats)  
IV. [Free Winning _Roulette Systems #1_](https://saliu.com/Roulette.htm#Free1)  
• _<PERSON>'s Paradox_ and the game of roulette  
• Relation of _Birthday Paradox_ to roulette strategies  
V. [Free Winning _Roulette Systems #2_](https://saliu.com/Roulette.htm#Free2)  
VI. [Ion Saliu's Roulette, Gambling Theory, Software Compared to Other Roulette Systems, Strategies](https://saliu.com/Roulette.htm#Best)  
VII. [Roulette Links, Resources, Software, Systems](https://saliu.com/Roulette.htm#Roulette)

![Discover at this Web site The Super Roulette Strategy: The best winning roulette systems ever.](https://saliu.com/HLINE.gif)

## <u>1. The Mathematical Foundation of Winning Roulette Strategy and Systems</u>

First captured by the _WayBack Machine_ (_web.archive.org_) on March 11, 2000.

The true book of **roulette** is founded on mathematics, probability theory and statistical analysis of casino roulette spins and random roulette spins. The winning roulette systems, strategies are totally free at this website; the software is free to run with a reasonable membership fee to download.

[![Casino Gambling Software: Blackjack, Roulette, Baccarat, Craps, Systems.](https://saliu.com/ScreenImgs/roulette-software.gif)](https://saliu.com/free-casino-software.html)

The common belief in the gambling world is related to the **roulette wheel** bias. Many _kokodrilos_ (big–time gamblers) swear by the wheel in the sky that they make millions by exploiting the roulette wheel bias. There are plenty of legends. The wheels must have been terribly worn out.

I do have some problems and some remarks regarding the bias of the roulette wheel. I have done the most thorough research in this field. My tool of choice is computer software. My **roulette software** is not only unique, but founded on solid mathematics. I have analyzed both real–life casino roulette spins and random–generated spins. The reports are very much the same.

Nobody will ever be able to tell which one is the real spin report and which one is the report on computer–generated roulette spins. The number frequencies are the same. I apply a key parameter in my analysis: the SKIP. The skip represents the number of spins between two hits of the same roulette numbers. Again, the skips are the same for both real–life spins and randomly generated roulette spins. The statistical parameters are in accordance to theory of probability. You will see a statistical report on this page, and another one on a different page.

Wheel bias might occur sometimes. It's hard and costly to check very often every roulette wheel in a casino. Later on this page you will see an event of possible wheel bias I witnessed in a real casino. The data sample was not very large, however.

• The second [_**roulette pairing statistical report**_](https://saliu.com/roulette-pairs.html) I told you about is very interesting. If there is roulette wheel bias, then each roulette number must be followed preponderantly by certain numbers. I call this feature number pairing. It is a common feature that occurs in lottery as well. The pairing is also a feature in computer-generated **roulette spins**. The roulette pairing is not the result of mechanical bias. It is the result of probability theory, IF the results are compatible with the normal probability rule.

I analyzed 1968 real roulette spins recorded at the Hamburg, Germany casino, February 1 – 6, 2000. File name: _HAMB00.DAT_, component of the **BrightR** integrated roulette software, systems, utilities.

The results are categorized by the following parameters, in order:  
_Table or Wheel (Tisch) – Day (Tag) – Month (Monat) – Year (Jahr)_.

I downloaded the spins at one roulette table or wheel (#1) for the entire month of January 2006. File name: _HAMB0106.WH1_; 7990 lines (draws or spins) in text format; the last draw of 31 January 2006 is at the very top of the file (line #1). The file contains roulette numbers (spins) only, one number per line. The casino results files have also other data, such as frequency and various statistics. The files also have dashed lines (----) which probably represent dealer changes at that particular roulette table.

_HAMB0106.WH1_ represents the best recording format for roulette: _table by table_. Do not mix the spins from different roulette wheels in the same file.

I remarked that same-number repeats are far less frequent than different-number pairing. One roulette number is followed by itself slightly less than once in 1000 spins (on average). But the same number is immediately followed by a different number (_best pairing_) 3 or 4 times in 1000 spins.

![The book of roulette mathematics, systems, strategy is founded on theory of probability.](https://saliu.com/HLINE.gif)

## <u>2. The Glamour and Attraction of Roulette on Humans Named Gamblers</u>

This **roulette theory** is absolutely unique. Nobody has ever come up with similar ideas. It is founded on the Fundamental Formula of Gambling and the _WL_ streaks (_winning/losing_). Nobody, other than myself has ever put together a mathematical relation between **probability** and the W/L streaks. I am the first one to state that theory of probability is the science of the winning/losing streaks.

I know firsthand how casinos have reacted to my roulette and gambling strategies. They are highly anxious because they can't figure out my systems. Some figured out that I might count losing streaks, or winning streaks, or dealer bust streaks, etc. Of course, I do my best not to let the casinos gain knowledge of my strategies. That's the reason why I do not sell the roulette system unless the customer signs that he/she is not affiliated with a casino whatsoever. It would be illegal for a casino to get their hands on my systems.

Then, I am posed, mockingly, this puzzling fact: _Roulette is unbeatable_. Great thinkers, such as D'Alembert and Einstein, designed roulette systems that actually fail. I believe Einstein was joking when he presented his infamous roulette system: Always double-up after a loss! What a huge mistake! Like any scientist, Einstein was fascinated with numbers and the unknown, as the roulette game provides. He was, however, very busy with his theory of relativity. He just did not allocate more time to the roulette phenomenon. Otherwise, Einstein's mental programs would have come up with a winning gambling (and roulette) system based, indeed, on the winning/losing streaks. Also, be mindful that Einstein was a religious man. He might as well be the most intelligent mystic who ever lived.

One issue concerns the reaction of casinos to this **roulette system and strategy**. You may be asked not to use pencil and paper at the **roulette table**. I stress here that the casinos have NO right to prohibit the use of pencil and paper on the premises. Such an act would represent a form of discrimination. Don't let yourself be intimidated. We all have rights under the law. The courts of law are wide open, including to class action suits.

Finally, I advise **_against_** the use of these **roulette systems** in a cyber casino (online). Internet gambling is prone to very serious fraud. Read the [_**Gambling Odds, House Edge, Integrity, Fraud**_](https://saliu.com/more.htm) page for details based on observation and logic. Among other types of fraud: The higher your bet, the higher the probability you will lose it!

![This gambling roulette strategy in casinos works with winning Martingale betting.](https://saliu.com/HLINE.gif)

## <u>3. Statistical Analysis of Roulette Spins (Double-Zero)</u>

Following is a fragment of the report generated by the **roulette software** **Spins**. It does not show all data, obviously. The program simulates a roulette game with **0** and **00**. Number 37 represents _double-zero (00)_.

![Roulette frequency report shows the hit and miss skip chart for each number, spins.](https://saliu.com/ScreenImgs/roulette-number-frequency.gif)

The roulette numbers can hit-and-miss in a wild manner. Some numbers can hit frequently in a session (_hot_ numbers). At the other end of the spectrum, some numbers can miss (_skip_) many spins (roulette _sleepers_). It is not unusual for a number or two to miss well over 100 consecutive roulette spins!

The _FFG median_ of all skips, however, abides by undeniable mathematical rules (probability formulas). The skip median for _single-zero_ roulette is _25_; the skip median for _zero-zero_ roulette is _26_.

Many gamblers play roulette strategies based on _hot_ numbers and/or _sleeping_ numbers. I tend to disregard the _sleepers_ by favoring the _repeat_ numbers. You can get acquainted with my roulette strategy founded on the _**Birthday Paradox**_ in the next section. Axiomatic gambling colleague of mine, you might want to keep a keen eye on that marquee (the casinos should turn it on at every table!)

![Roulette theory, roulette systems, software, roulette strategy leads to beat casino!](https://saliu.com/images/roulette-spin.gif)

## <u>4. The Free Winning <i>Roulette System #1</i></u>

Did you jump directly here from the top? I recommend you go back and start with the beginning. You need some information that I do not repeat here. The free roulette system I will present now is not included in the licensed roulette strategy package. As I explained many times, I give away freebies when I am certain I have far better systems, strategies, or software. On the other hand, I received several requests to give more details on how my systems work and what makes them tick. The predominant opinion is that nobody can play roulette for a profit consistently. Here is just one proof to the contrary. This is a real thing: a **winning roulette system**. There is also real life data to back it up. The skips presented here are collected from the Hamburg, Germany casino roulette.

-   A diligent, disciplined player can make a weekly profit by COVERING ALL THE NUMBERS BUT FOUR PLAYING NO MORE THAN 10 SPINS A DAY. The four numbers discarded of are the last four spins. You wait patiently, not playing, until the following event occurs. When one of the last four numbers hits again, you get ready to play. You cover all the numbers (0 and 00 including) but the numbers in the last FIVE spins (four numbers to play, because one is a repeat). You place 34 2-unit straight-up bets. Chances are you will win most of the time.
-   Next roulette spin, you make the same bet, using now three units per number (you are using some casino money). The first time you won 38-36=2 units; 2 units times 2 = 4 bets profit. The second time: you won 2 units x 3 = 6 units. Total profit per two plays: 10 units. Things are not always like this. Handy Brandy could lose sometimes the first bet. It happens very rarely, but one of the last four roulette numbers will hit again in consecutive sequence.

In most cases, Handy Brandy wins two times in a row without any of the last four roulette numbers repeating. He goes immediately to another table. He knows that the _long run_ "kills" the **casino roulette player** more than anything else.

After winning at the second table too, Handy Brandy usually cashes out. He runs out of the casino. Sometimes he might try doing the same things at the third table, but never more than winning at three roulette tables. The player will do the same thing the next day at a different casino and again at another casino... Here is illustrative data from the Hamburg casino. The following figures do NOT represent **roulette numbers**, but _SKIPS_. The skips, as in the frequency reports above, represent the number of roulette spins between hits.

9 5 57 17 30 18 0 3 13 25 6 0 4 17 2 24 0 4 3 15 13 8 21 2 23 2 6 7 7 10 3 3 17 10 15 11 1 15 3 1 24 11 10 2 3 13 6 12 3 3 24 6 10 21 4 8 9 8 0 20 7 21 7 18 6 0 17 4 13 5 2 6 16 0 2 4 0 7 0 7 1 8

A figure such as _57_ represents the skip of a particular roulette number. That particular roulette number (the 3rd most recent in our database) hit after 57 spins. That is, if we look at index 57+3 in our database, we'll find the same number again. Zero in the string above does not represent the 'green 0' (roulette number 0). It means that a particular number hit two spins in row, or hit in two consecutive spins. It couldn't be clearer now.

This page has been written by _addition_, rather than _from the ground up_. I added instead of editing and rewriting the material from top to bottom. The page also shows my growing in my linguistic English shoes! It is also clear that a force inside fights me from being too clear! No gambler has ever been more generous with his/her secrets than me!

The casino **roulette software** program **Spins** (also **SuperRoulette**) offers the best graphical representation of this free system. Page-down to the end of the frequency report. The system of interest is named _**Play all roulette numbers except for the last 5 spins**_. The report also recommends playing immediately following two consecutive misses (indicated by the – sign).

You only play at the points marked 0 (zero). That's when the last number is a repeat from the previous 5 roulette spins. You will play as above the next 3 points where the skip is zero. In many cases, it's after the third win in a row when you can encounter a string like 0 0. When that happens, you will lose 34 \* 2 = 68 units. You improve your chances further when you leave after you won at three roulette tables. You should expect to lose once a week.

### <u>Ion Saliu's Paradox and Roulette</u>

_**Ion Saliu's Paradox of N Trials**_ is presented in minute mathematical detail at SALIU.COM, especially the probability theory page and the mathematics of gambling formula. If p = 1 / N, we can discover an interesting relation between the degree of certainty DC and the number of trials N. The degree of certainty has a limit, when N tends to infinity. That limit is **1 — 1/e**, or approximately **0.632...**

-   If you play 1 **roulette** number for the next 38 roulette spins, common belief was that you expected to win once. NOT! Only if you play 38 numbers in 1 spin, your chance to hit the winning number is 100%. Here is an interesting table, which includes also the _**Free Roulette System #1**_ in the last row.

![Ion Saliu's Paradox is applied to roulette betting and also Birthday Paradox.](https://saliu.com/ScreenImgs/roulette-paradox.gif)

The maximum gain comes when playing 38 roulette numbers in one spin: 36.3%. Obviously, it makes no sense to play that way because of the house advantage. On the other hand, a so-called wise gambler is more than happy to play one number at a time. What he does is simply losing **_slowly!_** Not only that, but losing slowly is accompanied by losing more. That cautious type of gambling is like a placebo. A roulette system such as the _**Free Roulette System #1**_ scares most roulette gamblers. _"Play 34 or 33 numbers in one shot? I'll have a heart attack!"_ In reality, the _**Free Roulette System #1**_ offers a 28.8% advantage over playing singular numbers in long sessions. In fact, the gain is higher since the bets are placed at optimal moments. That's mathematics, and there is no heart to worry about.

-   You can also use **SuperFormula** to calculate all kinds of probabilities and advantage percentages, including roulette bets. The option _L — At least M successes in N trials_ is a very useful instrument. If you play 19 numbers in one spin, the probability to win is 50%. If you play 19 numbers in 2 **consecutive roulette spins**, the probability to win _at least once_ is 75%.

<big><u>Relation of the <i><b>Birthday Paradox</b></i> to roulette</u></big>  
The connection between the _**Birthday Paradox**_ and the game of roulette is extraordinarily appealing to some gamblers. Unfortunately, the appeal has a thin mathematical foundation. The **roulette game** draws one and only one number at a time. The birthday paradox requires at least two elements. _At least two persons in a room..._, etc.

If the roulette would draw 10 numbers at a time, the probability would be 72.7% that at least two of the numbers would be equal to one another (i.e. duplicates). But even if the roulette game would consist of 10 spins at a time, the **birthday paradox** would have nothing to do with predicting the numbers. Some gamblers make the following illogical connection. If I consider 10 roulette spins at a time, in 72.7% of the cases, at least two of the roulette numbers will be repeaters.

So, if I play the last 10 numbers, the chance is very good (almost 3 out of 4 cases) that one of the numbers will repeat next! Wow! That would bankrupt every casino on the planet in a few days! If the _roulette strategy_ would hold true, the probability would rise to 99.8% that the next spin will repeat a number from the last 20 roulette spins! Virtually, play the last 20 numbers and win every time. The cost is 20 units, the payout is 36 units, and therefore that roulette player would make a profit of 16 units in every play!

The cold truth is that the famous and appealing _Birthday Paradox_ merely shows the percentage of sets with duplicate elements in the total elements of an exponential set. That's all. So, unsuspecting roulette enthusiasts do NOT rely on the birthday paradox when playing roulette with real money. If you do, don't ask me for a refund later! Mathematically, it is correct to expect that one of the numbers in the last 26 spins will repeat next with a better than 50-50 chance. It is the median skip calculated by the _**Fundamental Formula of Gambling**_. Real life roulette spins and randomly generated roulette numbers validate this law — always.

-   I did check, however, several roulette tables in Atlantic City, 2004. I did not find one, not one, **roulette marquee** showing unique numbers only. Out of 15 numbers, some were repeats — from 3 to 7 repeat numbers. Problem is, the skips between _**Birthday Paradox**_ situations reached 8+ spins sometimes!
-   If I had checked 100 roulette marquees (in Atlantic City), I could have seen four tables showing all-unique roulette numbers! The probability of 15-number roulette strings with repeaters is 96%. Probably some players wait for 5 or 6 or so skips and then apply the _**Birthday Paradox**_. The average amount of unique **roulette** numbers to play is 12. We must win in two spins to make a profit.
-   Eventually, I devised a [_**roulette system based on**_ **Birthday Paradox formulas**, **marquee** numbers](https://saliu.com/AdSense-referrals.html). Apply it intelligently by checking first several tables. Select marquees that show 8+ unique numbers at the top; if not available, start at a roulette table showing the longest string of unique numbers.

The **Super Roulette** gambling software adds kind of a new system to the _**Free Roulette System #1**_. The program adds two more columns to the report. Instead of tracking only the last 5 spins, Roulette tracks also the last 15 spins as displayed on the roulette marquee.

The relation to the _**Free Roulette System #1**_. What is the probability that two randomly drawn **roulette numbers (spins)** will be the same in a group of 5 roulette numbers? It is a _Birthday Paradox_ with 38 _birthdays_. Instead of 5 persons, we deal with 5 randomly drawn roulette numbers. The probability of at least two _collisions_ (_coincidences_, or _repeats_) is: 24%. It's scary for the players of the _**Free Roulette System #1**_.

Fortunately, the roulette wheel does not draw 5 numbers at a time. The reports of **SPINS** show you a more optimistic story. The reports are validated by real-life roulette spins, in a real-life casino. Actually, the analyses of roulette repeats must go much deeper. The roulette probability here refers to chunks of N numbers; e.g. 5 unique roulette numbers versus 5-number groups with at least one repeater...

Months after I published the **mathematics of roulette related to the _Birthday Paradox_**, I received negative reports from **roulette players** around the world. Quite a few casinos decided to turn off the electronic displays (marquees) at the roulette tables. **Gamblers** say that the decision of casino executives depends on how roulette players stare at the marquees and if the players take notes of the roulette numbers... and especially if they bet big and win big!

I also discovered wide piracy of my mathematical analysis of the _Birthday Paradox_ as a potential roulette system. Even weeks after I published my **roulette analysis**, there were absolutely no other references on the Internet. Now, there is thousands of roulette systems derived from the _Birthday Paradox_ as I presented it! Beware of other acts of piracy! You can find further information in the roulette resources section of this page.

![Advantages of Ion Saliu's Paradox in the game of roulette, as a roulette strategy, roulette system.](https://saliu.com/HLINE.gif)

## <u>5. The Free Winning <i>Roulette System #2</i></u>

An alleged casino executive made me kick him out and also _fix_ the ill-fated James Bond roulette system. He posted on my message board with the intent to intimidate my guests. He made me angry as my response to him indicates! Plenty hot! The Bondish system is based on the first two **douzaines**: Play together 1-12 & 13-24. The scheme can be extended to any **2-to-1 bets**. I offer the power of the standard deviation to improve James bond fictitious system. You can start with my free software **SuperFormula**, option _S = Standard Deviation_. You can also use as a training tool my other roulette programs, like **Spins** and especially **Super-Roulette** .

The _**Free Roulette System #2**_ is presented on Web page: [_**James Bond Roulette System**_](https://saliu.com/bbs/messages/588.html) in the _Taliban Desert_.

By the way, if your bankroll is smaller, you can apply this system in 50-spin sessions.

One more tip. It is possible for a two-dozen (two-column) group to hit more than seven times in a row; or, more than seven out of 10 times. If that happens, you rode a very favorable standard deviation tide. Cash out immediately and move to another table!

My article and the system were provoked by an alleged casino top executive.  
_"I am currently the_ [_**Chairman of the MGM Grand Casino**_](https://saliu.com/bbs/messages/579.html), _and I was advised of this site by way of a memo sent to me."_

In a way, the alleged executive's message is an endorsement of my **gambling systems**. If the systems were not valid, why bother to risk being rear-end kicked by an understandably angered author? I want to stress again that the casinos have no legal grounds for banning record keeping by the players. Record keeping is also a requirement of tax laws. The rules of private entities do not take precedence over the national laws.

That's how the _**Free Roulette System #1**_ came to life as well. I am aware of such tendency now and I believe I have it under control. It will give the player and edge, without a doubt. I have checked for thousands of spins, broken down in 100-spin sessions. There are clearly two situations.

One, the roulette player wins _**65 or FEWER spins**_; it is a _**losing**_ session. The player counts 100 spins. The player started with 100 chip-units. At the end of the roulette table session, the player counts 61 units. The result was under the standard deviation. Remember to increase the bet the next session.

Two, the player wins _**66 or MORE roulette spins**_; it is a _**winning**_ session. The player counts 100 spins. The player started with 100 chip-units. At the end of the session, the player counts 69 units. The result was above the standard deviation (calculated for roulette probabilities). Remember to decrease the bet the next session.

The fluctuations in the roulette systems are _**mathematical**_, considering the game is totally fair. In an online casino, for instance, the fluctuations go mostly in the favor of the house. The house has to win above the standard deviation — always! Or especially when player's bet is high! That's anything but fairness! This roulette system does not even require strict record keeping. The player can just put aside a chip for each session played, and for each 10-spin roulette groups.

![Roulette strategy, systems, software, probability, mathematics, odds began with this page.](https://saliu.com/HLINE.gif)

## <u>6. <i>Ion Saliu's Roulette, Gambling Theory, Software</i> Compared to Other Roulette Systems</u>

My gambling theory, including roulette, is founded on **mathematics**, specifically the Fundamental Formula of Gambling (FFG). Also, everything I set out in my software must be validated mathematically to the best of my knowledge.

In all honesty, I don't see anything out there that offers better ways to winning at gambling or roulette. I wish there was something easier and more efficient than my approach. **Nothing**, honestly. IF my **casino gambling** methodology doesn't win the — there is absolutely nothing to accomplish such daunting tasks. Never will be, if my research has been futile. In the end, it's all about streaks and skips, no matter what the phenomenon is. The skips (misses) are shorter and the streaks are longer if the probability is higher; and vice versa.

The rest of the roulette gambling theory is nothing more than static progression betting and/or clocking the speed of the roulette wheel.

Static progression betting has virtually NO mathematical validity. The number of roulette skips (misses between wins) can be staggering. The winning/losing streaks are dynamic; they flow. It is random, but _**FFG**_ discerns with various degrees of certainty DC how the streaks move.

Clocking the speed of the roulette wheel is an illusion the myths are made of. NASA sophisticated instruments may be able to measure precisely the speed of the roulette wheel. But NO instrument can predict the landing position of the spinning ball on the roulette wheel. The wheel deflectors randomize the movement regardless of the speed. Of course, it is absolutely impossible for any roulette player to clock the roulette wheel speed precisely, even if the casinos allowed the use of any instruments or computers on the premises.

Not to mention the thorough tests conducted by the manufacturers of **roulette wheels**. They certainly perform tests regarding the wheel speed and the landing of the spinning roulette ball. They probably run the roulette wheel at exactly the _speed v_ (rotations per minute) for a number of _runs R_. They note that the roulette ball is landing randomly; i.e. in various positions on the wheel. They change the wheel speed and run another batch of tests. They notice the randomness of the ball landing. It is a strong fact regarding the roulette wheel. The same speed of the roulette wheel leads to highly random landing positions (roulette numbers).

I did notice bias, however. It was due to the wear of the roulette wheel. Probably the deflectors of the roulette wheel were worn out. The wheel was malfunctioning, anyhow, I think. We can divide the roulette wheel in two sections based on the last number drawn. There are 38 numbers in double-zero roulette. The last number drawn, plus 9 numbers to its left on the roulette wheel layout, plus 9 numbers to the right on the layout = make up the inside hemisphere of the roulette wheel. The other 19 numbers make-up the outer hemisphere.

The next number should be FFG-equally distributed between the two wheel hemispheres. That is, the **_roulette insiders_** and **_roulette outsiders_** should be FFG-equally distributed. I tracked the spins at a roulette table in Atlantic City. To my (belated) dismay, the ratio was 80% to 20% biased towards the outer hemisphere! Actually, two roulette dealers — a woman and a man — shifted at the table during that continuous run! So, it was not a so-called roulette dealer's signature! It was a malfunctioning of the roulette wheel.

It is likely that the casinos have no knowledge of such phenomenon. But tracking the landing in the roulette inner hemisphere and the outer hemisphere is pretty hard to accomplish when actually playing roulette in a casino.

Later on, I decided to release special roulette software that works with the **roulette wheel sectors** and the _Birthday Paradox_. Program name: ****RouletteHemis****; software category 5.5.

Right now, I cannot see any casino roulette gambling system better than mine. That's the only way, like my gambling theory and software do: Track the winning and the losing streaks and their corresponding skips (misses). Apply the _**Fundamental Formula of Gambling (FFG)**_. There will be some no playing moments (or play the minimum bet); other situations demand a higher **roulette bet**. It is hard work, but nothing else works better. It might not be attractive to the faint at heart or the illusion-drinker.

![The roulette systems and much gambling software are free at this site.](https://saliu.com/HLINE.gif)

![Ion Saliu's Theory of Probability Book founded on solid mathematics applied to roulette.](https://saliu.com/probability-book-Saliu.jpg) Read Ion Saliu's first book in print: [**_Probability Theory, Live!_**](https://saliu.com/probability-book.html)  
~ Founded on valuable mathematical discoveries with a wide range of scientific applications, including probability theory applied to roulette gambling, software, real winning systems.

![Study the book of roulette strategy systems, software, theory, spins analysis.](https://saliu.com/HLINE.gif)

[](https://saliu.com/content/roulette.html)See a comprehensive directory of the pages and materials on the subject of **roulette**, software, systems, and the Super Strategy.

-   [_**<u>BrightR</u>: High-Powered Integrated Roulette Software**_](https://saliu.com/roulette-software.html).  
    It bundles in a convenient system the most important pieces of roulette software, accompanied by a visual tutorial.
-   [_**The Best-Ever Roulette Strategy, Systems**_](https://saliu.com/best-roulette-systems.html) based on mathematics of progressions and free-for-all.
-   [_**The Best Strategy for Lottery, Gambling, Sports Betting, Horse Racing, Blackjack, Roulette**_](https://saliu.com/strategy-gambling-lottery.html).
-   _**An inventory of free and outrageously priced**_ [_**roulette systems from many gambling developers**_](https://download.saliu.com/roulette-systems.html).
-   [_**Roulette Software, Systems: Wheel Positioning, Slots, Sectors, Birthday Paradox**_](https://saliu.com/RouletteWheel.html).  
    Of course, my theory, system, and software were pirated already! An Australian group behind the website named _genuinewinner.com_ already sells a $2,500 roulette system. Their advertising logo reads in a picture): _**We consider which HALF of the wheel the ball landed in.**_ - [_**"Roulette System that Won Millions!"**_](https://saliu.com/roulette-millions.html)
-   [_**Roulette System: Wheel Halves, Sectors, Layout, Bias Betting**_](https://saliu.com/RouletteHalves.html)  
    ~ The original roulette system that was pirated and distributed at exorbitant prices.
-   [**Probability, Odds to Win Roulette**](https://saliu.com/roulette2.html) _**in Various Number of Spins: To Be Ahead and Quit**_.
-   The [_**<u>James Bond</u> Roulette System**_](https://saliu.com/bbs/messages/588.html) in the _Taliban Desert_.
-   [_**"Roulette Systems", Threats from Casino Chairman**_](https://saliu.com/bbs/messages/579.html).
-   [_**Anti-gambler Advice: John Patrick, Casino Mole, Conspirator**_](https://saliu.com/bbs/messages/587.html).
-   [_**Wizard of Odds Gambling Fallacy, Ion Saliu Gambling Theory**_](https://saliu.com/bbs/messages/204.html).
-   [_**Casinos pay troubled individuals to intimidate intelligent gambling system authors**_](https://saliu.com/bbs/messages/255.html).
-   [_**Demise of Gambler's Fallacy, Reversed Gambler's Fallacy, Streaks**_](https://saliu.com/gamblers-fallacy.html), including roulette.
-   [_**Gamblers' Fallacy, Doctrine of Maturity of Chances, Roulette, Gambling Systems**_](https://saliu.com/bbs/messages/199.html).
-   Download [_**Software for Casino Gambling, Roulette, Blackjack**_](https://saliu.com/free-casino-software.html).

![Find winning roulette strategy, strategies, systems, software, including free.](https://saliu.com/HLINE.gif)

**[Home](https://saliu.com/index.htm) | [Search](https://saliu.com/Search.htm) | [New Writings](https://saliu.com/bbs/index.html) | [Fundamental Formula](https://saliu.com/formula.htm) | [Odds, Generator](https://saliu.com/calculator_generator.html) | [Contents](https://saliu.com/content/index.html) | [Forums](https://forums.saliu.com/) | [Sitemap](https://saliu.com/sitemap/index.html)**

![Roulette, systems, strategy, gambling, system, software, actual casino spins.](https://saliu.com/images/HLINE.gif)
