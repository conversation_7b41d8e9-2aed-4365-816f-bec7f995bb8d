# Wonder Grid Lottery System - 系統整合測試
# 測試所有組件的整合和交互

using Test
using Dates

# 載入系統
include("../src/wonder_grid_system.jl")

println("🧪 Wonder Grid System - 系統整合測試")
println("=" ^ 60)

@testset "系統整合測試" begin
    # 準備測試數據
    test_data = [
        LotteryDraw([1, 5, 10, 15, 20], Date(2023, 1, 1), 1),
        LotteryDraw([2, 6, 11, 16, 21], Date(2023, 1, 2), 2),
        LotteryDraw([3, 7, 12, 17, 22], Date(2023, 1, 3), 3),
        LotteryDraw([4, 8, 13, 18, 23], Date(2023, 1, 4), 4),
        LotteryDraw([5, 9, 14, 19, 24], Date(2023, 1, 5), 5),
        LotteryDraw([1, 6, 12, 18, 25], Date(2023, 1, 6), 6),
        LotteryDraw([2, 7, 13, 19, 26], Date(2023, 1, 7), 7),
        LotteryDraw([3, 8, 14, 20, 27], Date(2023, 1, 8), 8),
        LotteryDraw([4, 9, 15, 21, 28], Date(2023, 1, 9), 9),
        LotteryDraw([5, 10, 16, 22, 29], Date(2023, 1, 10), 10)
    ]
    
    @testset "系統初始化測試" begin
        println("\n📊 測試系統初始化...")
        
        # 測試基本初始化
        system = WonderGridSystem(test_data)
        
        @test system.initialized == true
        @test length(system.historical_data) == length(test_data)
        @test system.standard_engine !== nothing
        @test system.optimized_engine !== nothing
        @test system.initialization_time > 0
        
        println("✅ 系統初始化測試通過")
    end
    
    @testset "Skip 計算整合測試" begin
        println("\n⚡ 測試 Skip 計算整合...")
        
        system = WonderGridSystem(test_data)
        
        # 測試基本 Skip 計算
        skip_1 = calculate_skip(system, 1)
        skip_2 = calculate_skip(system, 2)
        
        @test isa(skip_1, Int)
        @test isa(skip_2, Int)
        @test skip_1 >= 0
        @test skip_2 >= 0
        
        # 測試與標準引擎的一致性
        standard_result = calculate_one_filter(system.standard_engine, 1)
        @test skip_1 == standard_result.current_value
        
        println("✅ Skip 計算整合測試通過")
    end
    
    @testset "配對分析整合測試" begin
        println("\n🤝 測試配對分析整合...")
        
        system = WonderGridSystem(test_data)
        
        # 測試配對頻率計算
        freq_1_2 = calculate_pairing_frequency(system, 1, 2)
        freq_5_10 = calculate_pairing_frequency(system, 5, 10)
        
        @test isa(freq_1_2, Int)
        @test isa(freq_5_10, Int)
        @test freq_1_2 >= 0
        @test freq_5_10 >= 0
        
        # 驗證對稱性
        freq_2_1 = calculate_pairing_frequency(system, 2, 1)
        @test freq_1_2 == freq_2_1
        
        println("✅ 配對分析整合測試通過")
    end
    
    @testset "Wonder Grid 生成整合測試" begin
        println("\n🎯 測試 Wonder Grid 生成整合...")
        
        system = WonderGridSystem(test_data)
        
        # 測試小型 Wonder Grid
        grid = generate_wonder_grid(system, 5)
        
        @test isa(grid, Vector{Vector{Int}})
        @test length(grid) == 5
        
        # 檢查每行都有 5 個號碼
        for row in grid
            @test length(row) == 5
            @test all(1 ≤ num ≤ 39 for num in row)
            @test length(unique(row)) == 5  # 無重複
        end
        
        println("✅ Wonder Grid 生成整合測試通過")
    end
    
    @testset "性能監控整合測試" begin
        println("\n📈 測試性能監控整合...")
        
        system = WonderGridSystem(test_data)
        
        # 執行一些操作以生成性能數據
        for i in 1:5
            calculate_skip(system, i)
            calculate_pairing_frequency(system, i, i+1)
        end
        
        # 獲取性能報告
        report = get_performance_report(system)
        
        @test isa(report, Dict)
        @test haskey(report, "system_info")
        @test haskey(report, "performance")
        
        # 檢查系統信息
        system_info = report["system_info"]
        @test system_info["initialized"] == true
        @test system_info["data_points"] == length(test_data)
        @test system_info["optimized_engine_enabled"] == true
        
        println("✅ 性能監控整合測試通過")
    end
    
    @testset "健康檢查整合測試" begin
        println("\n🏥 測試健康檢查整合...")
        
        system = WonderGridSystem(test_data)
        
        # 執行健康檢查
        health_status = system_health_check(system)
        
        @test isa(health_status, Bool)
        @test health_status == true  # 應該是健康的
        
        println("✅ 健康檢查整合測試通過")
    end
    
    @testset "錯誤處理整合測試" begin
        println("\n❌ 測試錯誤處理整合...")
        
        # 測試空數據初始化
        @test_throws ArgumentError WonderGridSystem(LotteryDraw[])
        
        # 測試未初始化系統的操作
        uninitialized_system = WonderGridSystem()
        @test_throws ErrorException calculate_skip(uninitialized_system, 1)
        @test_throws ErrorException calculate_pairing_frequency(uninitialized_system, 1, 2)
        @test_throws ErrorException generate_wonder_grid(uninitialized_system, 5)
        @test_throws ErrorException get_performance_report(uninitialized_system)
        
        # 測試無效參數
        system = WonderGridSystem(test_data)
        @test_throws ArgumentError calculate_skip(system, 0)
        @test_throws ArgumentError calculate_skip(system, 40)
        
        println("✅ 錯誤處理整合測試通過")
    end
    
    @testset "並行計算整合測試" begin
        println("\n⚡ 測試並行計算整合...")
        
        if Threads.nthreads() > 1
            system = WonderGridSystem(test_data, enable_parallel=true)
            
            # 測試並行 Skip 計算
            test_numbers = [1, 2, 3, 4, 5]
            results = calculate_all_skips_parallel(test_data, test_numbers)
            
            @test isa(results, Dict)
            @test length(results) == length(test_numbers)
            
            # 驗證結果正確性
            for number in test_numbers
                @test haskey(results, number)
                @test results[number].success == true
                
                # 與系統計算結果比較
                system_result = calculate_skip(system, number)
                @test results[number].result == system_result
            end
            
            println("✅ 並行計算整合測試通過")
        else
            println("⚠️ 跳過並行計算測試（需要多執行緒環境）")
        end
    end
    
    @testset "記憶體管理整合測試" begin
        println("\n💾 測試記憶體管理整合...")
        
        system = WonderGridSystem(test_data)
        
        # 執行大量操作以觸發記憶體管理
        for i in 1:20
            calculate_skip(system, rand(1:10))
            calculate_pairing_frequency(system, rand(1:10), rand(1:10))
        end
        
        # 檢查記憶體池統計
        memory_stats = get_all_pool_statistics(GLOBAL_MEMORY_POOL)
        @test isa(memory_stats, Dict)
        @test haskey(memory_stats, "summary")
        
        # 執行清理
        cleanup_system!(system)
        
        # 檢查清理後的狀態
        post_cleanup_stats = get_all_pool_statistics(GLOBAL_MEMORY_POOL)
        @test isa(post_cleanup_stats, Dict)
        
        println("✅ 記憶體管理整合測試通過")
    end
    
    @testset "配置管理整合測試" begin
        println("\n⚙️ 測試配置管理整合...")
        
        # 測試自定義配置
        custom_config = Dict{String, Any}(
            "performance" => Dict(
                "enable_caching" => false,
                "enable_compact_data" => false
            )
        )
        
        system = WonderGridSystem(test_data, config=custom_config)
        
        @test system.config["performance"]["enable_caching"] == false
        @test system.config["performance"]["enable_compact_data"] == false
        
        # 測試默認配置合併
        @test haskey(system.config, "monitoring")
        @test haskey(system.config, "parallel")
        
        println("✅ 配置管理整合測試通過")
    end
    
    @testset "端到端工作流程測試" begin
        println("\n🔄 測試端到端工作流程...")
        
        # 創建系統
        system = WonderGridSystem(test_data)
        
        # 執行完整的分析工作流程
        println("  執行 Skip 分析...")
        skip_results = Dict{Int, Int}()
        for number in 1:10
            skip_results[number] = calculate_skip(system, number)
        end
        
        println("  執行配對分析...")
        pairing_results = Dict{Tuple{Int,Int}, Int}()
        for i in 1:5
            for j in i+1:5
                pairing_results[(i, j)] = calculate_pairing_frequency(system, i, j)
            end
        end
        
        println("  生成 Wonder Grid...")
        wonder_grid = generate_wonder_grid(system, 10)
        
        println("  生成性能報告...")
        performance_report = get_performance_report(system)
        
        println("  執行健康檢查...")
        health_status = system_health_check(system)
        
        println("  清理系統...")
        cleanup_system!(system)
        
        # 驗證所有結果
        @test length(skip_results) == 10
        @test length(pairing_results) == 10  # C(5,2) = 10
        @test length(wonder_grid) == 10
        @test isa(performance_report, Dict)
        @test health_status == true
        
        println("✅ 端到端工作流程測試通過")
    end
end

println("\n🎉 系統整合測試完成！")

# 執行最終的系統驗證
println("\n🔍 執行最終系統驗證...")

try
    # 創建測試系統
    final_system = WonderGridSystem(test_data)
    
    # 驗證核心功能
    skip_test = calculate_skip(final_system, 1)
    pairing_test = calculate_pairing_frequency(final_system, 1, 2)
    grid_test = generate_wonder_grid(final_system, 3)
    report_test = get_performance_report(final_system)
    health_test = system_health_check(final_system)
    
    println("✅ 所有核心功能驗證通過")
    println("   Skip 計算: ✅")
    println("   配對分析: ✅") 
    println("   Wonder Grid: ✅")
    println("   性能報告: ✅")
    println("   健康檢查: ✅")
    
    # 清理
    cleanup_system!(final_system)
    
    println("\n🎯 Wonder Grid Lottery System 整合驗證完成！")
    println("系統已準備好進行生產部署。")
    
catch e
    println("❌ 系統驗證失敗: $e")
    rethrow(e)
end
