#!/usr/bin/env julia

# Test LIE elimination integration with Wonder Grid strategy

using Pkg
Pkg.activate(".")

include("src/WonderGridLotterySystem.jl")
using .WonderGridLotterySystem
using StatsBase

# Generate test data
function generate_test_data(num_draws::Int = 1000)
    draws = Vector{Vector{Int}}()
    for i in 1:num_draws
        # Generate 5 unique numbers between 1 and 39
        draw = sort(sample(1:39, 5, replace=false))
        push!(draws, draw)
    end
    return draws
end

println("=== Testing LIE Integration with Wonder Grid Strategy ===")

# Create test data
println("\n1. Creating test data...")
test_data = generate_test_data(1000)
println("Generated $(length(test_data)) test draws")

# Initialize Wonder Grid engine
println("\n2. Initializing Wonder Grid engine...")
engine = WonderGridEngine(test_data)
println("Engine initialized successfully")

# Select a key number
println("\n3. Selecting key numbers...")
key_numbers = select_key_numbers(engine)
println("Found $(length(key_numbers)) favorable key numbers: $key_numbers")

if !isempty(key_numbers)
    key_number = key_numbers[1]
    println("Using key number: $key_number")
    
    # Test standard combination generation
    println("\n4. Testing standard combination generation...")
    try
        standard_combinations = generate_combinations(engine, key_number)
        println("Generated $(length(standard_combinations)) standard combinations")
        
        # Show first few combinations
        println("First 5 combinations:")
        for i in 1:min(5, length(standard_combinations))
            println("  $i: $(standard_combinations[i])")
        end
    catch e
        println("Error in standard generation: $e")
    end
    
    # Test LIE elimination
    println("\n5. Testing LIE elimination...")
    try
        lie_analysis = generate_combinations_with_lie_analysis(engine, key_number)
        
        println("LIE Analysis Results:")
        println("  Original combinations: $(lie_analysis["original_count"])")
        println("  Filtered combinations: $(lie_analysis["filtered_count"])")
        println("  Eliminated combinations: $(lie_analysis["eliminated_count"])")
        println("  Cost savings: $(round(lie_analysis["cost_savings_percentage"], digits=2))%")
        
        # Show first few filtered combinations
        filtered_combinations = lie_analysis["filtered_combinations"]
        println("\nFirst 5 filtered combinations:")
        for i in 1:min(5, length(filtered_combinations))
            println("  $i: $(filtered_combinations[i])")
        end
        
        # Show LIE combinations (what was eliminated)
        lie_combinations = lie_analysis["lie_combinations"]
        println("\nLIE combinations generated: $(length(lie_combinations))")
        if !isempty(lie_combinations)
            println("First 5 LIE combinations (to be eliminated):")
            for i in 1:min(5, length(lie_combinations))
                println("  $i: $(lie_combinations[i])")
            end
            
            # Check if any LIE combinations overlap with original combinations
            original_set = Set(lie_analysis["original_combinations"])
            lie_set = Set(lie_combinations)
            overlap = intersect(original_set, lie_set)
            println("Overlap between original and LIE combinations: $(length(overlap))")
            if !isempty(overlap)
                println("First few overlapping combinations:")
                for (i, combo) in enumerate(collect(overlap)[1:min(3, length(overlap))])
                    println("  $i: $combo")
                end
            end
        else
            println("No LIE combinations generated - this explains why no elimination occurred")
        end
        
    catch e
        println("Error in LIE analysis: $e")
        println("Stack trace:")
        for (exc, bt) in Base.catch_stack()
            showerror(stdout, exc, bt)
            println()
        end
    end
    
    # Test strategy comparison
    println("\n6. Testing strategy comparison...")
    try
        comparison = compare_wonder_grid_strategies(engine, key_number)
        
        println("Strategy Comparison:")
        println("  Standard Wonder Grid: $(comparison["standard_strategy"]["combination_count"]) combinations")
        println("  Wonder Grid with LIE: $(comparison["lie_strategy"]["combination_count"]) combinations")
        println("  Cost reduction: $(round(comparison["comparison_metrics"]["cost_reduction_percentage"], digits=2))%")
        println("  Recommendation: $(comparison["comparison_metrics"]["strategy_recommendation"])")
        
    catch e
        println("Error in strategy comparison: $e")
    end
    
    # Test full Wonder Grid with LIE execution
    println("\n7. Testing full Wonder Grid with LIE execution...")
    try
        execution_result = execute_wonder_grid_with_lie(engine, key_number)
        
        println("Full Strategy Execution Results:")
        println("  Strategy: $(execution_result["strategy_name"])")
        println("  Key number: $(execution_result["key_number"])")
        println("  Key number favorable: $(execution_result["key_number_analysis"]["is_favorable"])")
        println("  Final play set size: $(length(execution_result["recommended_play_set"]))")
        println("  Execution time: $(round(execution_result["execution_time"], digits=3)) seconds")
        
        efficiency = execution_result["efficiency_metrics"]
        println("  Efficiency metrics:")
        println("    Skip ratio: $(round(efficiency["skip_ratio"], digits=3))")
        println("    Pairing coverage: $(round(efficiency["pairing_coverage"], digits=1))%")
        println("    Combination reduction: $(round(efficiency["combination_reduction_ratio"] * 100, digits=1))%")
        
    catch e
        println("Error in full execution: $e")
    end
    
    # Test threshold optimization
    println("\n8. Testing LIE threshold optimization...")
    try
        optimization = optimize_lie_threshold(engine, key_number)
        
        println("Threshold Optimization Results:")
        println("  Optimal threshold: $(optimization["optimal_threshold"])")
        println("  $(optimization["recommendation"])")
        
        println("\nThreshold analysis:")
        thresholds = sort(collect(keys(optimization["threshold_analyses"])))
        for threshold in thresholds
            analysis = optimization["threshold_analyses"][threshold]
            println("    $(threshold): $(analysis["filtered_count"]) combinations, $(round(analysis["cost_savings_percentage"], digits=1))% savings")
        end
        
    catch e
        println("Error in threshold optimization: $e")
    end
    
else
    println("No favorable key numbers found - cannot test LIE integration")
end

println("\n=== LIE Integration Testing Complete ===")