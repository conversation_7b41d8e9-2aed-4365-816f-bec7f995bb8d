---
created: 2024-12-29T12:40:39 (UTC +08:00)
tags: [filters,restrictions,lottery,lotto,formula,probability,software,mathematics,theory probability,system,statistics,drawings,filters,filtering,numbers,winning,combinations,]
source: https://saliu.com/filters.html
author: 
---

# 彩票軟體中的樂透過濾器、縮減策略 --- Lotto Filters, Reduction Strategies in Lottery Software

> ## Excerpt
> The first and only lottery software, lotto programs based on mathematics of dynamic filters, filtering, restriction, and elimination reduce huge amounts of lotto combinations from play.

---
[![Lottery Software: Jackpot Lotto, Pick Lotteries, Powerball, Mega Millions, Euromillions, Keno.](https://saliu.com/images/lottery-software-systems.gif)](https://saliu.com/free-lotto-lottery.html)  

## <u data-immersive-translate-walked="161216df-86f3-4a34-9418-613e1d939e12" data-immersive-translate-paragraph="1"><span data-immersive-translate-translation-element-mark="1" lang="zh-TW"><span data-immersive-translate-translation-element-mark="1"><span data-immersive-translate-translation-element-mark="1"><i data-immersive-translate-walked="161216df-86f3-4a34-9418-613e1d939e12">過濾器理論、過濾、樂透還原</i>、彩票軟體</span></span></span></u>

## 作者：Ion Saliu，_彩票過濾數學創始人_

![Set filters in lottery software, filtering for reduction of combinations in lottery software.](https://saliu.com/HLINE.gif)

![Run the best lottery software, lotto software to win the lottery or lotto, based on filtering.](https://saliu.com/images/lotto.gif)

### 一、[彩票過濾器簡介及軟體過濾](https://saliu.com/filters.html#Filters)  
二.[設定彩票過濾器：理論和軟體工具](https://saliu.com/filters.html#Software)  
三．[設定過濾器和彩票策略的更高級理論](https://saliu.com/filters.html#Theories)  
四．[設定過濾器的較早著作；彩票軟體使用者制定策略](https://saliu.com/filters.html#History)  
V.[樂透、樂透、軟體、過濾器、策略、系統中的資源](https://saliu.com/filters.html#Links)

![Implement filtering to reduce lotto playing tickets with lottery software.](https://saliu.com/images/lotto.gif)

## <u data-immersive-translate-walked="161216df-86f3-4a34-9418-613e1d939e12" data-immersive-translate-paragraph="1"><span data-immersive-translate-translation-element-mark="1" lang="zh-TW"><span data-immersive-translate-translation-element-mark="1"><span data-immersive-translate-translation-element-mark="1">1. 彩票過濾器簡介及專用軟體過濾</span></span></span></u>

## 也請研究本出版物的補充：[_**有史以來最好的彩票策略，樂透策略**_](https://software.saliu.com/lottery-strategy-lotto-strategies.html)。

  <big data-immersive-translate-walked="161216df-86f3-4a34-9418-613e1d939e12" data-immersive-translate-paragraph="1"><span data-immersive-translate-translation-element-mark="1" lang="zh-TW"><span data-immersive-translate-translation-element-mark="1">&nbsp;&nbsp;</span><span data-immersive-translate-translation-element-mark="1"><span data-immersive-translate-translation-element-mark="1">• 排序過濾器報告*</span></span></span></big>  
樂透軟體依列對_W\*、MD\*、GR\*、DE\*、FR\*、SK\*_報告進行排序。它可以幫助用戶更輕鬆地查看過濾器並更輕鬆地發現彩票策略。

![Column sorting software: program sorts by column lottery reports to discover lotto strategies.](https://saliu.com/ScreenImgs/sort-lottery-reports.gif)

彩票過濾器是消除 LotWon 彩票和彩票軟體生成過程中的組合的參數。換句話說，過濾器就是_限制_。例如，一個簡單的限制：_生成的樂透組合不會重複任何過去中過大獎的抽獎_。

諷刺的是，大多數彩票過濾器的最低級別被視為...過濾器！事實上，我的樂透軟體中的過濾器多年來一直只有最低水平。事情就是這樣開始的。我花了很多年才意識到沒有最大值就沒有最小值。原因：20世紀80年代的個人電腦並不先進。另一個原因是：我逐漸成為程式設計師。

-   **_最低_級別僅允許_高於_該級別的彩票組合。它消除了組合範圍_內的_所有內容。**
-   **_最高_級別只允許_低於_該級別的彩票組合。它消除了所有_不在組合範圍內的_東西。**

兩級過濾器的效率相同。沒有通用的公式來計算彩票過濾器的效率。_效率是指過濾器消除的彩票組合的數量。_

假設樂透遊戲每期有 6 個中獎號碼。我們可以透過公式準確計算過濾器_ONE_和過濾器_SIX_將消除多少種組合 - 但前提是我們將每個等級設為**1** 。也就是說，只有當我們將過去的**一張**繪圖視為濾鏡設定時才能執行計算。這是因為根據機率論，彩票號碼組確實會重複。

濾鏡的_最低等級__「一」_ （名稱很直觀）消除了過去樂透抽獎中的所有單數字群組。如果我們將_ONE_過濾器的最低等級設為**1** ，它將消除上一次抽獎中的每個樂透號碼。每個組合有 6 個數字。因此，我們將從比賽場地中消除 6 個號碼（例如遊戲中的 49 個號碼）。只有剩餘 49 – 6 = 43 個樂透號碼中的號碼才會成為產生組合的一部分。

因此，_最低等級__1_將允許的組合數量等於_組合公式 43 一次取 6 個_。 C(43, 6) = 6,096,454 種組合。樂透組合總數 C(49, 6) = 13,983,816。換句話說， _ONE_的_最低等級_將<u data-immersive-translate-walked="161216df-86f3-4a34-9418-613e1d939e12">消除</u>_7,887,362 個_組合。

_最高等級__ONE_則相反。如果_ONE_的_最大等級_設定為**1** ，則會消除 6,096,454 個組合。同樣，它<u data-immersive-translate-walked="161216df-86f3-4a34-9418-613e1d939e12">允許</u>_7,887,362 種_樂透 6/49 組合。

我們可以套用公式的另一個過濾器是一個樂透參數，我們可以稱之為_SIX_ 。它將僅套用完整的 6 位數組作為限制。我們將_SIX_的_最小等級_設為**1** （僅限上一張圖）。只有 1 個可能的 6 號碼組與過去的抽獎相符。_六_的_最低水準_將恰好消除一個樂透組合。相反：_最高等級__「六」_將只允許一種組合（即它消除了樂透遊戲中的其餘組合）。

沒有其他類型的限製或彩票過濾器的公式。確定彩票過濾器效率的唯一方法是在字典生成模式下運行軟體（ **Bright / Ultimate Software**主選單中的 L ）。將特定過濾器的最低等級設為 1。將特定過濾器的最大等級設為 1。也就是說，如果將同一過濾器的兩個層級產生的彩票組合的數量相加，結果應該等於彩票遊戲的總組數。

您可能希望將特定軟體包中每個過濾器的效率資料儲存到文件中。列印該文件作為一個很好的參考，以幫助您使用我的樂透和彩票軟體。

樂透過濾器（例如_Four、FivS、FivR）_具有特殊行為。它們是如此之高，以至於它們達到了超出小數據檔案的水平。如果您分析 10,000 次抽牌，第 1 層中的_FivS_將顯示 10,000。過濾器很可能比這個更高。可以達到兩萬以上，這是事實。在我的樂透 6/69 遊戲中， _FivS_和_FivR_都輕鬆達到了 100,000。這就是為什麼_FivS_和_FivR_過濾器沒有最大等級的原因！_四級_過濾器有最高級別，但需要小心。如果過濾器在您的_WS6.1_報告中顯示 10,000，則僅表示沒有足夠的抽籤可供分析。

## <u data-immersive-translate-walked="161216df-86f3-4a34-9418-613e1d939e12" data-immersive-translate-paragraph="1"><span data-immersive-translate-translation-element-mark="1" lang="zh-TW"><span data-immersive-translate-translation-element-mark="1"><span data-immersive-translate-translation-element-mark="1">2. 設定濾波器：理論與軟體工具</span></span></span></u>

作為快速開始，將一個或極少數樂透過濾器設定為超出正常範圍的水平。正常範圍由相應彩票軟體過濾器的中位數決定。最新的彩票軟體包（ **Bright** ，尤其是**Ultimate Software** ）使使用中位數變得非常容易。已經為使用者計算出中位數。您可以在每個過濾器的頂部看到它（WS 檔案中的一列）。對於其他套餐，您需要自行確定中位數。

您可以使用 WS 檔案和QEdit編輯器（它不適用於 64 位元 Windows）。 QEdit 有一些不錯的功能，我沒有費心在我的編輯器中實現。為什麼我每次都要重新發明…樂透輪盤？ ！

QEdit 的一項不錯的功能是_列區塊_或_列選擇_。閱讀手冊中的快捷方式，它可能是_Alt+k_ 。轉到_WS_檔案中的第一張圖形。將遊標移至過濾器（列）的第一位數字。如果樂透軟體濾鏡有四位數字，請確保遊標距離最右邊的數字有四個空格。列塊必須覆蓋過濾器中的所有數字。同時按_Alt+K_ ，然後按向右箭頭鍵，直到到達該列中的最後一位數字。按向下箭頭鍵，直到到達 WS 文件中的最後一行。

-   不言而喻，我們總是在進化，對此毫無疑問！請經常訪問該網站以獲取最新資訊。特別要檢查該網站每個網頁的頁尾。
-   強烈建議使用名為**Notepad++**的免費 32 位元編輯器，而不是 16 位元Qedit 。它效果很好，速度很快，而且更容易使用。它是用於[_**創建、更新過去中獎號碼的彩票資料檔案**_](https://forums.saliu.com/lottery-strategies-start.html)的建議工具。
-   我編寫了專門的軟體來自動執行對過濾器（或列或文字區塊）進行排序的過程。程式名稱是**SortFilterReports** 。樂透軟體按列對_W5、MD5、GR5、DE5、FE5、SK5_報告進行排序，幫助使用者更輕鬆地查看過濾器，例如_古怪_值的過濾器。排序是根據獲獎報告的類型進行的。樂透程式還為過濾器排序提供正確的選擇（正確的名稱）。現在也有類似的 6 號樂透遊戲、選 3、4 彩券和賽馬節目。
-   還有一個通用程式（**排序**）可以對任何 ASCII（文字格式）檔案中的數字列進行排序。**排序**是獨立提供的，也是**Bright**和**Ultimate Software**彩票應用程式的一部分。
-   最先進的樂透軟體應用程式具有最新的演示頁面：
-   [_**下載軟體、原始碼、樂透結果、強力球統計、超級百萬、歐洲百萬**_](https://saliu.com/bright-software-code.html)。
-   [_**下載樂透、Pick 3 4 樂透、賽馬的<u data-immersive-translate-walked="161216df-86f3-4a34-9418-613e1d939e12">終極</u>軟體**_](https://saliu.com/ultimate-software-code.html)。
-   這是尋找彩票策略的一個非常好的方法。我為特定遊戲建立了資料夾： **Pick3** 、 **Pick4** 、 **Lotto5** 、 **Lotto6**和**Horses** 。然後，在每個資料夾中我建立了一個名為**StratSorRep**的子資料夾。這就是我將排序後的獲勝報告_W\*.\*、MD\*.\*、LieId\*.\*_等移動到的位置。我將在未來很長一段時間內使用這些報告。我可能不需要再次對報告進行排序。
-   為 1000 張（甚至 2000 張）圖紙製作獲勝報告會產生真正的影響。您可能看不到很少出現的過濾級別，但它們確實出現並且可以為您賺錢。讓我們來看看我的 W4 中獎報告，其中 4 號秀是賓州彩券的真實數據。有一個名為_Tot_的過濾器 — 它是_N 次抽牌後的 Total Straight Sets_的縮寫。 N 可能非常高，甚至超過 50,000 次抽獎。但我們感興趣的是最低值，例如 0 或 1：

![The lotto filters can eliminate huge amounts of lottery combinations or tickets, cost of play.](https://saliu.com/images/lotto-filters.gif)

-   如果分析至少 1000 張圖紙，則 6 層中的 3 層將顯示_1_ 。其他三份 W4 報告不會有千分之一的抽獎，但將來會有，而_Tot = 1_的報告則不會。我們將處理未看到_Tot_過濾器值_1_的報告。
-   設定_Tot\_min = 1_且_Tot\_MAX = 2_將始終產生 1 順子 4 盤（組合進行比賽）。將有 3 個這樣的集合，因為我們有 3 個 W4 報告，其中缺少_Tot = 1_ 。絕對不需要額外的過濾器。
-   即使我們進行接下來的 1000 次抽獎，成本也是 3000，而支出是 5000。在我的現實生活報告中，最新的熱門是 40 次退場。底線：我們已經保存了 1000 張圖紙！
-   當然，這種類型的彩票需要很大的耐心（大約三年的等待）。重點是：當我們想要尋找策略時，如果我們只分析少數抽獎（100-200），我們可能會錯過賺錢的彩票策略。

這是資料夾組織的螢幕截圖：

![Create winning reports, sort them and use them for effective lottery strategies.](https://saliu.com/images/strategy-sorted-reports.gif)

有很多很多的報道！報告越多，提出大量有效策略的機會就越大。我花了幾個小時來產生獲勝報告，對所有過濾器上的所有報告進行排序，最後將它們移至每個**Ultimate 軟體**包的**StratSorRep**子資料夾中。我喝了幾杯酒，讓無聊不再那麼_「壓抑」_ ！事實上，排序後的報告將永遠有效。

排序後的報告在頂部顯示最低的篩選器數字，而最大的列值顯示在底部。該報告按Any\_5過濾器排序。我曾經獲得過很好的成功（當我認真玩彩票時！）我設定了_Any\_5\_minimum = 400_ 。有時，該策略不會產生單一組合。我混合併匹配了來自各個層面的_任何_策略，以防某個策略陷入“低迷”。這些策略有時會變得_「冷」_ ，有時會變得_「熱」_ 。但所有的彩券策略都_**跳過了數學上一致的中位數**_。

![Study advanced methods in setting lotto filters to create winning lottery strategies.](https://saliu.com/images/lottery-filter-strategies.gif)

因此，當我說_古怪的過濾器值_時，我指的是關鍵列的排序獲勝報告的頂部和底部。當然，我們總是應該在運行時設定其他過濾器值。我們可以在這裡看到，當_Any\_5\_minimum = 400_時，其他過濾器通常非零（ _Tot、Rev、Pr、Syn、Sum_ ）。我們可以激進地玩並設定_Tot = 1000_ ，因為它在_Any = 400_時出現了 14 次。當然，我們也可以組合來自不同層和不同程式的過濾器。

我們也可以使用排序獲勝來報告 WS 的_中位數_區域中的篩選器值。在第三種彩票策略中，我們可以設定_嚴格的_過濾器設定。在此特定範例中，我們可以在_報表_標題中看到中位數等於_59_ _。_此設定可能非常嚴格，有時無法產生組合。我們可以透過以下設定來放鬆它： _Any\_5\_minimum = 58_ AND _Any\_5\_MAXimum = 62_

![The lottery filters are founded on theory of probability, mathematical formulas.](https://saliu.com/images/filter-report.gif)

這就是我勤奮的公理同事。以下是基於一個**主元過濾器**等級的三種主要類型的樂透策略：-   **最低**值：我們套用過濾器的**最大**級別
-   **最高**值：我們套用過濾器的**最低**級別
-   **中**位數：我們套用過濾器的**最小**等級**和****最大****等級**。
    -   **最小值****和****最大值**可以應用於篩選器的任何等級（例如，_最小值_\= 1 AND_最大值_\= 2；我們只需要重複多次的值）。
-   每種類型的遊戲（選 3、選 4、樂透 5、樂透 6）在每個濾鏡上都有超過 300 份報告。如上所述，每份報告至少可以產生 3 種抽獎策略。事實上，每個遊戲可以輕鬆擁有2000種彩票策略。當然，沒有人能夠設計並運用 1000 種策略……甚至 500 種！但經過排序的報告使得基於單一過濾器製定彩票策略變得非常容易。
-   單過濾器策略需要在運作時設定更多過濾器。不需要許多過濾器，因為它們彼此重疊。例如，您在執行時設定_Filter\_M_和_Filter\_N_ 。然而，單獨使用任一彩票過濾器都會產生相同數量的組合。其中之一是多餘的。

## <u data-immersive-translate-walked="161216df-86f3-4a34-9418-613e1d939e12" data-immersive-translate-paragraph="1"><span data-immersive-translate-translation-element-mark="1" lang="zh-TW"><span data-immersive-translate-translation-element-mark="1"><span data-immersive-translate-translation-element-mark="1">3. 設定過濾器和彩票策略的更高級理論</span></span></span></u>

您現在已經有了每個彩票軟體過濾器的中位數。超出正常範圍的過濾器可能是：

-   **_中_位數乘以3或4，甚至5；**
-   **_中位數_除以 3 或 4，甚至 5。**

例如，如果中位數為 12，您可以將嚴格的彩票過濾器設定為 12 \* 4 = 48（或向上舍入到 50）作為過濾器的最小值。或者，您可以將同樣嚴格的過濾器設定為 12 / 4 = 3+1，以獲得對應過濾器的最小值。 （請記住，過濾器的最高等級必須至少為（最低等級+ 1）。如果過濾器設定為中位數的4 倍，則它會將總組合的四倍減半。在pick-3 彩票遊戲範例中：第一步1000個彩券組合減少到500個；500個減少到250個；最後125個減少到60+。

按列排序的 WS 檔案可以向您顯示更有價值的資訊。假設您按_Pairs-1_列對_W3.1_進行了排序。_中_位數為_32_ 。級別類型其他過濾器也可能顯示非常低的數字。其他樂透濾鏡可能會顯示更大的數字。您可以選擇_Max\_Pair\_1=8+1=9_作為遊戲策略，以及其他不太嚴格等級的過濾器。例如， _Max\_Vr\_1=4_ 、 _Max\_TV\_1=6_ 、 _Val\_1=5_ 。這只是一個例子。您可以在排序的 WS 檔案中找到類似的數字。

中位數乘以 4 _\=_ 128。您也可以查看其他濾鏡對於大於 128 的_Pairs-1_顯示什麼樣的等級。其他過濾器可能顯示較低的數字。您可以選擇_Min\_Pair\_1=130_作為玩彩票策略，以及其他等級不太嚴格的過濾器。例如， _Min\_Vr\_1=1、Min\_TV\_1=5、Min\_Syn\_1=50_ 。

對一個或極少數樂透過濾器使用如此嚴格的等級可以消除大量的樂透組合。這種水平很少出現。你不應該在每張圖中都使用它們。他們在點擊之間跳過了一些圖畫。最新的 pick-3 軟體讓您變得更輕鬆。該應用程式還有一個_**策略檢查**_軟體實用程式。它顯示了所有濾鏡的等級以及策略的跳躍圖。

另請閱讀[_**Pick-3 彩票策略、系統、方法、玩法、配對**_](https://saliu.com/STR30.htm)頁面。它說明了為什麼您應該僅在當前跳過（跳過圖表中的第一個數字）小於或等於中位數時才使用策略。例如，如果策略的中位數為5，則僅當跳過字串中的第一個數字為0、或1、或2、或3、或4、或5 時，才應播放該策略。目前跳過較大，不玩策略；省錢。由於您可以選擇非常非常多的彩票策略，因此請尋找其他策略。尋找顯示目前跳躍低於中位數的策略。

### 另一條路徑認為過濾等級發生的機率較高。

查看彩票過濾器，從第一行開始，回到之前的圖紙。很明顯，濾鏡會上下移動。這是一條法律。

同樣，pick-3 彩票軟體讓您更輕鬆。它更明顯地顯示了過濾器的運動。當濾鏡高於上圖時，濾鏡右側有+號。如果過濾器低於上一次抽籤的過濾器，則會附加一個 - 符號。這是更明顯的。您會注意到，在大多數情況下，濾鏡在兩到三張繪圖後就會從一個趨勢變成相反趨勢。也就是說，在2或3個+號之後，出現-號；反之亦然。基於此，我們可以查看 WS 檔案中的每個篩選器（列）。

關鍵位置是第 1 行。如果第 1 行中的符號為 -，第 2 行和第 3 行中的符號也為 -（3 連續減少），我們應該預期下一次抽獎會出現 +（增加）。如果第 1 行中的符號是 +，第 2 行和第 3 行中的符號也是 +（3 連續增加），我們應該預期下一次抽獎會出現 -（減少）。我們以pick-3彩券為例。第 1 行中的 Pair-1 是 12，它顯示 -，第三個連續的 -（減少）。我們應該期待在下一張圖中出現+。增加過濾器需要使用對應過濾器的最低等級。在此範例中，我將設定_Min\_Pair\_1=13_ 。例如，如果我想增加機率，我可以設定_Min\_Pair\_1=10_ 。當然，該程式會產生大量的接三直盤。

現在假設第 1 行中的_Pair-1_是 123，它顯示 +，即第三個連續的 +。我們應該期待在下一張圖中出現 - 。減少彩票過濾器需要使用相應過濾器的最大等級。在此範例中，我將設定_Max\_Pair\_1 = 124_ 。例如，如果我想增加機率，我可以設定_Max\_Pair\_1 = 130_ 。當然，程式會產生更大量的pick3彩票組。

您可以尋找較長的+或-條紋。只需轉到每個 WS 文件中的第 1 行即可。在某些情況下，目前的連勝可以是 4 長，或 5 長，在極少數情況下甚至更長。您可能需要先考慮較長的類似符號條紋。但請記住，在大多數情況下，繪製最多 3 次後，條紋會改變方向。實際上，1 或 2 個連續類似符號的條紋是最常見的。我不會朝這個方向走得更遠。

您可以將此路徑中選擇的篩選器與路徑 #1 中顯示的選擇類型組合起來。您可以設定一個嚴格的過濾器（4 倍中位數等）。然後按照路徑 #2 設定其他篩選器。例如， _Min\_Pair\_1=120_ （路徑#1）、 _Max\_Vr\_1=7_ （路徑#2）、 _Min\_TV\_1=10_ （路徑#1）、 _Min\_Syn\_1=100_ （路徑#1）、 _Max\_Bun\_2=6_ （路徑#2）、 _Max\_Tot\_3= 1500_ （路徑#2）， _Max\_Any\_5=300_ （路徑#2）。等等…

-   不言而喻，關於濾波器_連續增加/減少_的機率有一個有趣的方面。我們稱之為**獲勝餘額**。增加 (+) 或減少 (-) 的機率為 50%。根據_**賭博基本公式 (FFG)，**__趨勢反轉_的機率為**90%** 。
-   設定的過濾器的最佳數量（數量）是多少，並且仍然有機會獲得**獲勝平衡**？更具體地說，我查看了許多過濾器及其級別，並選擇那些顯示 3 個連續 +/- 的過濾器。我可以選擇多少個這樣的過濾器並且具有良好的置信度？
-   我們應用[_**機率的兩個基本規則**_：_非互斥_事件的_乘法_和_加法__：__**<u data-immersive-translate-walked="161216df-86f3-4a34-9418-613e1d939e12">貝葉斯定理</u>**_](https://saliu.com/bayes-theorem.html)。如果我選擇 5 個顯示 3 個連續 +/- 的過濾器，則**獲勝**機會（確定度）為**0.9 ^ 5 = 0.59 (59%)** 。**失敗**的幾率（僅遺失一個或多個過濾器）為**0.1 + 0.1 + 0.1 + 0.1 + 0.1 = 0.5 (50%)** 。這是我可以設定並獲得**獲勝餘額**的最大過濾器數量（在本例中為 9%）。對於 6 個過濾器，平衡轉向失敗位置（60% 對 53% 或 7% 的劣勢）。

![Combining more lottery strategies and playing the aggregate result increase the profits.](https://saliu.com/images/lotto.gif)

-   <u data-immersive-translate-walked="161216df-86f3-4a34-9418-613e1d939e12">應用<b data-immersive-translate-walked="161216df-86f3-4a34-9418-613e1d939e12">抽獎策略的</b>最佳方法：</u>不要一次玩一個策略（一層一層），而是<u data-immersive-translate-walked="161216df-86f3-4a34-9418-613e1d939e12">同時</u>玩所有 6（或 4）層的策略。我們[_**以基於數<u data-immersive-translate-walked="161216df-86f3-4a34-9418-613e1d939e12">位頻率</u>組的選4彩券超級策略**_](https://saliu.com/frequency-lottery.html)作為現實範例。
-   將 6 個輸出檔合併/連接為一個（例如_STR-FR4-BIG_ ）。不要清除重複項，因為這些策略有時會<u data-immersive-translate-walked="161216df-86f3-4a34-9418-613e1d939e12">同時</u>生效。我們稍微增加了成本，但我們增加了數千美元的獎金。
-   產生...慷慨的_**LIE**_文件，以進一步減少可玩的門票數量。有些東西幾乎是自動_**LIE 消除的**_候選人。樂透號碼/彩票數字的_上半部_不會出現在下一次抽獎中。基於頻率的_下半部分_票價甚至更糟。跳過系統也是如此： _FFG-SYS 跳過_系統檔案不會命中下一次抽獎； _FFG-SYS 在下一次抽獎中跳過_基於_中位數_票價的系統，情況甚至更糟。
-   如果您查看_WS_文件，您會發現沒有 5 個過濾器組完全符合下一個圖形。這些過濾器產生的組合應該會添加到最終的_**LIE**_檔案中。應分析所有過濾器報告，以找到在下一次抽獎中不會組合的過濾器組。
-   正如我一直所說的那樣，尤其是在這個網站上，<u data-immersive-translate-walked="161216df-86f3-4a34-9418-613e1d939e12">整個宇宙沒有絕對的確定性</u>。這是不可否認的數學定律（ _**FFG**_ ）。因此，我們有時會遇到錯誤的_**LIE 消除**_結果。就我個人而言，我的錯誤率不超過 1%。失敗的_**LIE**_情況與**組合彩券策略**同時命中_**的確定性 DC**_約為_1/6 \* 1/100 = 1/600_ 。在我看來，它是足夠<u data-immersive-translate-walked="161216df-86f3-4a34-9418-613e1d939e12">安全的</u>。
-   將一種彩票策略結合起來，或者同時玩幾種彩票策略，可以平滑裸奔。條紋更短，而且當應用於更大的組合時，_**謊言消除**_策略更有效。

![There is a balance of confidence in the number of lottery filters we set in software.](https://saliu.com/images/lotto.gif)

-   非常重要！如果彩票委員會更改遊戲格式，您必須創建新的彩票/彩票資料檔案！不要在一個資料檔中混合遊戲格式！例如，賓州從樂透 6/69 改為樂透 6/49。我丟棄了之前所有的樂透 6/69 抽獎。我重命名了舊的 6/69 文件並將其保留以用於存檔和研究目的。我從頭開始創建賓夕法尼亞州樂透 6/49 歷史文件。我還重新創建了模擬數據文件，專門用於 6/49 樂透遊戲。強力球遊戲將其格式從 49 個常規號碼更改為 53 個常規號碼。我按照上面的步驟進行。我將強力球資料檔更改為僅包含從**1****到****53**的常規樂透號碼。永遠不要在同一個資料檔案中混合使用各種彩票遊戲格式！

![Run lotto lottery data files, systems, strategies with filtering, filters in software.](https://saliu.com/HLINE.gif)

## <u data-immersive-translate-walked="161216df-86f3-4a34-9418-613e1d939e12" data-immersive-translate-paragraph="1"><span data-immersive-translate-translation-element-mark="1" lang="zh-TW"><span data-immersive-translate-translation-element-mark="1"><span data-immersive-translate-translation-element-mark="1">4. 設定過濾器的舊著作；彩票軟體使用者制定策略</span></span></span></u>

LotWon 的一名用戶創建並分析了大約 40 個_D6_檔案！令人難以置信的努力，但卻很有意義。他正確地判斷出 Ion _Parpaluck_ Saliu 創建 D\* 檔案和圖層是出於一兩個充分的理由。古怪是這裡的關鍵字。也就是說，過濾器可以記錄超出範圍的值。超出範圍是指過濾器的統計參數：平均值、標準差，尤其是中位數。真正古怪的過濾器值可能會被忽視，原因有兩個：

1）分析範圍太短（100張彩券左右）；  
2) 過去抽獎的次數非常少（遠低於 100,000）。

還有一個原因是層數不夠。層數只能透過增加 D\* 檔案的數量來增加。

在**MDIEditor 和 Lotto**以及 LotWon（命令提示字元）中都有備受關注的 ION5 篩選器。過濾器的構造方式完全相同。從字面上看，那個樂透濾鏡可以到達天空。如果 D\* 檔案的組合超過 100,000 個，它可能會變得巨大。對於 6/49 樂透遊戲，ION5 可以達到 TOTAL\_DRAWS/24。 （順便一提：24 = INT（49/2））。如果在 100,000 行 D6（MDIEditor 檔案）或 DOS Pick632 中的某個圖層中沒有 Ion\_5 高於 4,167，那麼您的資料檔案可能有足夠的大小。對於 5/39 的遊戲，ION5 可以達到 TOTAL\_DRAWS/20。如果 100,000 行 D5（MDIEditor 檔案）或**Bright5**中的圖層中沒有 Ion\_5 超過 5000，則您的 D\* 檔案可能有足夠的大小。

然而，_古怪的事情_可能超出了這些數字。我以我的樂透 5/39 遊戲為例。我透過執行那個很棒的組合軟體****PermuteCombine****產生了遊戲中的所有樂透組合 (575,757)。接下來，我透過運行那個很棒的應用程式**Shuffle**來打亂 all-5-39 組合檔案。我洗牌一次，從而創建了我的第一個_SIM-5_檔案。我又對 all-5-39 樂透檔案進行了幾次洗牌，每次都建立不同的_SIM-5_檔案。

我看過_Ion\_5_為 2500+、5000+、6000+、7000+…這些都是可怕的數字…可怕的不是你，而是賠率！我用 min\_Ion\_5 = 2500 做了一些測試。即使停用內部過濾器，也不會產生任何組合。

我檢查了_ION5_超過 2500 時的情況。產生的總組合：1（一），皆啟用並停用固有過濾器。你知道，我喜歡那樣！如果我五年前就明白這一點，我可能會中十幾次 5/39 樂透大獎。當然，我 5 年前的電腦是 kinduva snail (300 MZ PII)。

所以，現在我來看看_古怪的_樂透過濾器，像是著名的 Ion-5。如果某個層中的數量不超過 2500，請查看另一層...或查看另一個_D\*_彩票資料檔。_古怪_並不是一層或一張工程圖檔所獨有的。古怪的事情會重演，就像歷史重演一樣。

### <u data-immersive-translate-walked="161216df-86f3-4a34-9418-613e1d939e12" data-immersive-translate-paragraph="1"><span data-immersive-translate-translation-element-mark="1" lang="zh-TW"><span data-immersive-translate-translation-element-mark="1"><span data-immersive-translate-translation-element-mark="1">較舊的著作 - 但仍有效</span></span></span></u>

_由 Ion Saliu 於 2000 年 5 月 21 日發行。_

事實上，需要提供有關 LotWon 軟體中過濾器的更多事實。 SuperPower 彩票軟體中的文件不包括如何使用篩選器的最大值。

彩票過濾器的_**最小值**__至少_等於 。如果我們選擇_Two-1_ = 3，那麼如果_Two-1_至少為 3（如 W6 檔案所示），我們就是正確的。_這_意味著 3, 4, 5, ... 10, 11 等。

濾波器的_**最大值**_相當於_不大於但不等於_。如果_我們_選擇_Max\_Two-1_ = 3，如果_Two-1_不超過_2_ ，我們就是正確的。如果_Two-1_是 2、或 1、或 0（如_W6_檔案所示），我們就是正確的。如果_2-1_是3或更多，那就是失敗的局面。

如果_W6_檔案顯示_2-1_為 0，我們只能玩彩票過濾器的_最大值_。播放_最小值_不會產生任何效果（因為它等於 0）。在這種情況下， _Max-Two-1_的正確條目是 1 (0+1=1)。

濾鏡的效果取決於樂透遊戲的格式。我將舉例說明樂透 6/49 遊戲的一些濾鏡的效果。它是全球最受歡迎的，我收到了一份真實的_W6_樂透報告（來自名為 Guy 的用戶）。我不會再接受任何_W6_檔案來查看了。

-   **過濾器的_最小值_消除了一定數量的組合。  
    **
-   **濾鏡的_最大值_留下要播放的組合數量。**

1) 讓我們看看如果我們將過濾器_Two_的_最小值_設為 1 _，_它會消除多少個樂透組合。 15 個組合。剩下 49-6=43 個樂透號碼。剩下的 43 個號碼可以分解為_C43，每次取 4 個_\= 123410 個組合，每個組合有 4 個樂透號碼。 15 個 2 號樂透組合中的每一個都可以附加到 123410 個 4 號組合中的每一個上。結果是 15 x 123410 = 1851150 總共 6 號樂透組合。因此，過濾器_Two = 1_消除了 1,851,150 個組合。

讓我們讓_Two_ = 2。如果過去兩次抽獎沒有共同號碼，則過濾器_Two_ = 2 會消除 1,851,150 x 2 個組合。如果_Two_ = 3，則過濾器應消除 1,851,150 x 3 個組合。等等？不是！事實上，某些配對（兩個號碼的樂透組）比其他配對更頻繁。有些樂透配對即使在 200 次抽獎之內也不會出現。因此，在過去兩三次抽獎之後， _「二」_的效果就會減弱。我還建議使用_Least 6_檔案：樂透遊戲中配對次數最少的檔案。

如果濾鏡_「三」_設定為 1，則會從最近的樂透抽獎（前一期）中消除所有三個數字組。 _C（每次 6 個）_ = 20 種組合。剩下的 43 個數字可以分解為_C（43 個，一次取 3 個）_ = 12341 個每個 3 個數字的組合。 20 個_3 號_樂透組合中的每一個都可以附加到 12341 個 3 號組合中的每一個上。結果是 20 x 12341 = 246820 總共 6 號樂透組合。因此，過濾器_Three = 1_消除了 246,820 個組合。

讓我們讓_Three_ = 2。如果過去的兩張抽獎沒有共同數字，則過濾器_Three_ = 2 會消除 246,820 x 2 樂透組合。如果_Three_ = 3，則過濾器應消除 246,820 x 3 個組合。等等？不是！事實上，某些三數組合的出現頻率比其他組合高得多。經過大約 10 次抽獎後，_三個_過濾器將降低其消除能力。

2) 讓我們看看如果我們將其設為 1，_則_過濾器_「Two_ LEAVES TO BE PLAYED」的_最大值_有多少種組合。個組合。剩下 49-6=43 個號碼。剩餘的 43 個樂透號碼可以分解為_C（43 個，一次取 4 個）_ = 123410 個組合，每個組合有 4 個號碼。 15 個 2 號組合中的每一個都可以附加到 123410 個 4 號組合中的每一個上。結果是 15 x 123410 = 1851150 總共 6 個數字的組合。因此，過濾器_MAX\_Two_ = 1 留下 1,851,150 個要播放的組合。換句話說，樂透軟體將產生 1,851,150 種組合。

-   然而，這種情況並沒有發生，因為某些<u data-immersive-translate-walked="161216df-86f3-4a34-9418-613e1d939e12">數字組重疊</u>。例如，存在包含組_12、13_的組合以及其他包含組_13、12的_組合。<u data-immersive-translate-walked="161216df-86f3-4a34-9418-613e1d939e12">組合集始終按升序排序。</u>因此，只有_12、13_的組合才會通過過濾器；那些有_13、12的_將被丟棄。這就是與標準（公式）相比產生差異的原因。

讓我們讓_MAX\_Two_ = 2。假設最後兩張樂透抽獎沒有共同號碼。因此，最後兩張圖由 12 個唯一的數字組成。 _C（12 次取 2 個）_ = 66 種組合。剩下 49-12=37 個號碼。剩下的 37 個號碼可以分解為_C(37 個，一次取 4 個)_ = 66045 個 4 個號碼的組合。 66 個_2 號_組合中的每一個都可以附加到 66045_個 4 號_組合中的每一個上。結果是 66 x 66045 = 4358970 總共 6 號樂透組合。因此，過濾器_MAX\_Two_ = 2 留下 4,358,970 個要播放的組合。但上述免責聲明仍然適用，甚至擴大了差異。

如果濾鏡_「三」_設定為 1，則會保留最近一次樂透抽獎（前一期）中的所有三個數字組合。 _C（每次 6 個）_ = 20 種組合。剩下的 43 個數字可以分解為_C（43 個，一次取 3 個）_ = 12341 個每個 3 個數字的組合。 20 個_3 號_組合中的每一個都可以附加到 12341 個 3 號組合中的每一個上。結果是 20 x 12341 = 246820 總共 6 號樂透組合。因此，過濾器_Three_ = 1 LEAVES TO BE PLAYED 246,820 個組合。但要注意為_Two_的情況所描述的現象。

-   過濾值越大，與規範（公式）的差異越大。
-   只有過濾器_“一”_和_“六”_ （或_“五”_對於 5 號樂透）嚴格遵守公式 - 並且僅當設置等於**1 時**。

-   **_**最小值**_越高，過濾器的效率越高（它消除的組合越多）。**
-   **_**最大值**_越低，濾波器的效率越高（留下用於播放的組合越少）。**

查看 Guy 的_W6_文件，我看到 20 個或更多中的_兩個_。對於該類別中彩票軟體過濾器的最小值來說，這是一個很高的值（_兩個_或_2-#s_ ）。如此高的值對於其他過濾器（_三_或_3-#s_和_四_）具有相應的高值。對某些過濾器使用如此高的值將消除大量的樂透組合。

在同一份報告中，我看到了_三個_150 個或更多的過濾器。_最低__三位數_的如此高的價值也消除了大量的樂透組合。此外，2000 或更多（甚至 4000）中的_四和_400 或更多（甚至 700）之_和_確實消除了數百萬種組合。

您會注意到一個過濾器的高值與其他過濾器的高值相關。但這些高值偶爾會出現，但不是很頻繁。您將在過濾器“最低”級別的高值情況之間跳過一些樂透抽獎。正如 Guy 的報告中所述，在抽獎 #20 中，過濾器_Sum-1__為_828，然後在抽獎 #13 中為 469。跳過5 次樂透抽獎並玩再次_Sum-1_ = 400。

相反，樂透過濾器的有效_最大_等級是較低的。對於_兩個_過濾器，0 是最有效的值，並且出現的頻率非常高（Guy 分析的 100 張圖紙中出現超過 15 次）。在這種情況下，您設定_MAX\_Two-1_ _\=_ 1。或者更戲劇性的是，您可以將所有六個_兩個_過濾器設定為不高於 5 _。_ 6。

_對於_過濾器_Three_ ，_最大值__的__有效_等級為 5。

根據所有這些事實，您可以推斷 W6 檔案中不時出現異常高或異常低的值。因此，我們可以設定高效的過濾器，並期望時不時地以更少的樂透組合獲勝。

如果您想在每次運行 WHEEL-6 時產生中獎樂透組合，則需要為過濾器設定 SAFE 值。安全值的選擇不能100%保證，但也不是複雜的事。您不應期望每次執行程式時都能選擇正確的過濾器等級。

您可以在_W6_檔案中註意到，通常高值後面跟著較低值（反之亦然）。通常，連續三次增加後會減少（反之亦然：三次減少後會增加）。例如，在 Kulai 的報告中， Three _\-_ _1_是連續三期彩票中的 191、29、12，然後是 31 _。_於50）。

您設定這種類型的彩票過濾器_**安全**_等級的準確性將隨著使用而增加：您使用_W6_檔案的次數越多，您的過濾器設定的錯誤就越少。

-   好了，關於使用 Ion Saliu 彩票軟體的過濾器就講了很多。但是，等等……等等……還有更多，不言而喻的！當我創建這個網站時，我還參與了首要的彩票新聞群組： _rec.gambling.lottery_ 。 Google 群組幾乎存檔了該_Usenet_群組中發布的所有內容。您可以搜尋有關該作者的查詢： _Ion Saliu、Parpaluck、saliu systems、saliu software、ion saliustrategies等。_
-   我在這裡列出了我最近開始的一些主題 –
-   [_**英國 6-59 樂透：統計、彩券策略、最熱門 24 號碼**_](https://groups.google.com/forum/?hl=en#!topic/rec.gambling.lottery/_4vQk00mxzI)。
-   [_**結果抽獎#1，世界彩券錦標賽**_](https://groups.google.com/forum/?hl=en#!topic/rec.gambling.lottery/E-aM_zUsQFY)。
-   [_**過濾器、限制、減少樂透、彩票軟體**_](https://groups.google.com/forum/?hl=en#!topic/rec.gambling.lottery/f_I6udi8raQ)。
-   [_**彩券軟體、強力球策略、超級百萬、歐洲百萬**_](https://groups.google.com/forum/?hl=en#!topic/rec.gambling.lottery/0P7xKzncan8)。
-   大量複製並貼上到一個文件中，該文件將成為您自己的<u data-immersive-translate-walked="161216df-86f3-4a34-9418-613e1d939e12">手冊</u>或<u data-immersive-translate-walked="161216df-86f3-4a34-9418-613e1d939e12">教科書</u>，用於<u data-immersive-translate-walked="161216df-86f3-4a34-9418-613e1d939e12">設置過濾器和創建保證獲勝的彩票策略</u>（數學和統計）。

## [<u data-immersive-translate-walked="161216df-86f3-4a34-9418-613e1d939e12" data-immersive-translate-paragraph="1"><span data-immersive-translate-translation-element-mark="1" lang="zh-TW"><span data-immersive-translate-translation-element-mark="1"><span data-immersive-translate-translation-element-mark="1">彩券軟體、Lotto Wheeling、系統、策略資源</span></span></span></u>](https://saliu.com/content/lottery.html)

查看關於彩票、樂透、軟體、系統、樂透輪盤主題的頁面和材料的綜合目錄。

-   主[_**樂透、樂透、軟體、策略**_](https://saliu.com/LottoWin.htm)頁面。  
    展示創建免費中獎樂透、彩票策略、基於數學的系統的軟體。取得您的彩券系統或輪盤、最好的彩券、彩券軟體、組合、中獎號碼。
-   [_**樂透、彩票軟體、Excel 電子表格：程式設計、策略**_](https://saliu.com/Newsgroups.htm)。
-   [_**<u data-immersive-translate-walked="161216df-86f3-4a34-9418-613e1d939e12">MDIEditor Lotto WE</u> ：彩票軟體手冊、書籍、電子書、幫助**_](https://saliu.com/MDI-lotto-guide.html)。  
    ~ 也適用於LotWon彩券、樂透軟體；加上強力球、超級百萬、歐洲百萬。
-   [_**視覺教程、書籍、手冊：彩票軟體、彩票應用程式、程式**_](https://saliu.com/forum/lotto-book.html)。
    
    _**專門提供世界上最好的彩票程式和彩票軟體的幫助、說明、過濾器、策略的頁面：**_
    
-   [_**文件、幫助：MDIEditor Lotto WE、彩票軟體、策略教學**_](https://saliu.com/mdi_lotto.html)。
-   [_**MDI Editor Lotto**_是最好的樂透彩票軟體；你當法官](https://saliu.com/bbs/messages/623.html)。
-   [_**樂透、彩券軟體的過濾、限制、消除、減少**_](https://saliu.com/bbs/messages/42.html)。
-   [_**樂透、軟體中的彩票過濾器的逐步指南**_](https://saliu.com/bbs/messages/569.html)。
-   [_**樂透軟體基礎手冊、彩券軟體**_](https://saliu.com/bbs/messages/818.html)。
-   [_**彩票軟體中的**_**垂直或位置**過濾器](https://saliu.com/bbs/messages/838.html)。
-   [**LotWon**_**彩票軟體、Lotto 軟體**__**的初學者基本步驟**_](https://saliu.com/bbs/messages/896.html)。
-   [**動態**或**靜態**_**濾鏡：樂透軟體、樂透分析、數學**_](https://saliu.com/bbs/messages/919.html)。
-   [**跳過彩票、樂透、賭博、系統、策略**](https://saliu.com/skip-strategy.html)
-   [_**彩券實用軟體**_](https://saliu.com/lottery-utility.html)： _**Pick-3、4 Lottery、Lotto-5、6、Powerball/Mega Millions/Thunderball、Euromillions**_ 。
-   [_**彩票策略、基於數字的系統、數位頻率**_](https://saliu.com/frequency-lottery.html)
-   [**樂透十年**：_**軟體、報告、分析、策略**_](https://saliu.com/decades.html)
-   [_**樂透號碼頻率、彩券對、配對的最佳範圍分析**_](https://saliu.com/lottery-lotto-pairs.html)。
-   [_**用於_在固定位置產生最喜歡的彩票號碼_的樂透組合的軟體**_](https://saliu.com/favorite-lottery-numbers-positions.html)_。_
-   _[_**數位群組的樂透軟體：奇數、偶數、低數、高數、總和、頻率、使用者群組**_](https://saliu.com/lotto-groups.html)。_
-   _[_**理論、**_**樂透增量**分析_**、彩券軟體、策略、系統**_](https://saliu.com/delta-lotto-software.html)。_
-   _[_**彩票、賭博、運動博彩、賽馬、二十一點、輪盤賭的最佳策略**_](https://saliu.com/strategy-gambling-lottery.html)。_
-   _[_**Pick-3 彩票策略軟體、系統、方法、最後 100 次抽獎的配對**_](https://saliu.com/STR30.htm)。_
-   __「開始是最難的部分」_ ：[_**玩樂透策略，樂透策略**_](https://forums.saliu.com/lottery-strategies-start.html)。_
-   _[_**樂透，<u data-immersive-translate-walked="161216df-86f3-4a34-9418-613e1d939e12">相反的彩票策略</u>：不贏導致不輸或贏**_](https://saliu.com/reverse-strategy.html)。_
-   _[_**<u data-immersive-translate-walked="161216df-86f3-4a34-9418-613e1d939e12">LIE 消除</u>：彩票、配對反向<u data-immersive-translate-walked="161216df-86f3-4a34-9418-613e1d939e12">彩票策略、號碼頻率</u>**_](https://saliu.com/lie-lottery-strategies-pairs.html)。_
-   _[_**<u data-immersive-translate-walked="161216df-86f3-4a34-9418-613e1d939e12">LIE消除，樂透幾十年的反向彩票策略</u>，最後一位數字，奇偶，低高，跳過**_](https://saliu.com/lie-lotto-strategies-decades.html)。_
-   _**下載**[**彩券軟體、樂透程式**](https://saliu.com/infodown.html)_。

![Ion Saliu lottery software, lotto software is best to win with mathematics of filtering, filters.](https://saliu.com/HLINE.gif)

**[首頁](https://saliu.com/index.htm)|[搜尋](https://saliu.com/Search.htm)|[新作](https://saliu.com/bbs/index.html)|[賠率，發電機](https://saliu.com/calculator_generator.html)|[內容](https://saliu.com/content/index.html)|[論壇](https://forums.saliu.com/)|[網站地圖](https://saliu.com/sitemap/index.html)**

![Effective lottery software must apply filters or restrictions to the huge amounts of combinations.](https://saliu.com/HLINE.gif)
