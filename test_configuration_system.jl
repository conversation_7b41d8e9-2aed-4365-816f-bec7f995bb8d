#!/usr/bin/env julia

"""
Comprehensive test suite for configuration and parameter management system
Tests configuration creation, validation, saving/loading, and interactive features
"""

using Dates

# Include the configuration module
include("src/configuration.jl")

"""
Test the configuration and parameter management system
"""
function test_configuration_system()
    println("🧪 Testing Configuration and Parameter Management System")
    println("=" ^ 70)
    
    # Test 1: Basic configuration creation
    println("\n🔧 Test 1: Basic Configuration Creation")
    test_basic_configuration_creation()
    
    # Test 2: Configuration validation
    println("\n✅ Test 2: Configuration Validation")
    test_configuration_validation()
    
    # Test 3: Configuration file operations
    println("\n💾 Test 3: Configuration File Operations")
    test_configuration_file_operations()
    
    # Test 4: Configuration presets
    println("\n🎛️  Test 4: Configuration Presets")
    test_configuration_presets()
    
    # Test 5: Key number selection
    println("\n🔑 Test 5: Key Number Selection")
    test_key_number_selection()
    
    # Test 6: Configuration manager
    println("\n📋 Test 6: Configuration Manager")
    test_configuration_manager()
    
    # Test 7: Error handling
    println("\n❌ Test 7: Error Handling")
    test_error_handling()
    
    println("\n✅ Configuration System Tests Complete!")
    println("=" ^ 70)
end

"""
Test basic configuration creation
"""
function test_basic_configuration_creation()
    try
        # Create default configuration
        config = WonderGridConfig()
        
        # Verify default values
        @assert config.auto_key_selection == true
        @assert config.key_selection_method == "optimal"
        @assert config.analysis_depth == "standard"
        @assert config.include_lie_elimination == true
        @assert config.lie_threshold == 0.1
        @assert config.output_format == ["csv", "txt"]
        @assert config.show_progress == true
        
        println("  ✅ Default configuration created successfully")
        println("  📊 Key selection: $(config.auto_key_selection ? "Auto" : "Manual")")
        println("  🔬 Analysis depth: $(config.analysis_depth)")
        println("  📤 Output formats: $(join(config.output_format, ", "))")
        
    catch e
        println("  ❌ Error in basic configuration creation: $e")
        rethrow(e)
    end
end

"""
Test configuration validation
"""
function test_configuration_validation()
    try
        # Test valid configuration
        valid_config = WonderGridConfig()
        is_valid, errors = validate_configuration(valid_config)
        
        @assert is_valid == true
        @assert isempty(errors)
        println("  ✅ Valid configuration passed validation")
        
        # Test invalid configuration - bad key number
        invalid_config = WonderGridConfig()
        invalid_config.auto_key_selection = false
        invalid_config.key_number = 50  # Invalid (> 39)
        
        is_valid, errors = validate_configuration(invalid_config)
        @assert is_valid == false
        @assert !isempty(errors)
        println("  ✅ Invalid key number correctly detected")
        
        # Test invalid LIE threshold
        invalid_config2 = WonderGridConfig()
        invalid_config2.lie_threshold = 1.5  # Invalid (> 1.0)
        
        is_valid, errors = validate_configuration(invalid_config2)
        @assert is_valid == false
        @assert !isempty(errors)
        println("  ✅ Invalid LIE threshold correctly detected")
        
        # Test invalid date range
        invalid_config3 = WonderGridConfig()
        invalid_config3.historical_data_range = (Date("2023-01-01"), Date("2022-01-01"))  # End before start
        
        is_valid, errors = validate_configuration(invalid_config3)
        @assert is_valid == false
        @assert !isempty(errors)
        println("  ✅ Invalid date range correctly detected")
        
    catch e
        println("  ❌ Error in configuration validation: $e")
        rethrow(e)
    end
end

"""
Test configuration file operations
"""
function test_configuration_file_operations()
    try
        test_config_file = "test_config.txt"
        
        # Create and save configuration
        manager = ConfigurationManager(test_config_file)
        manager.config.key_number = 13
        manager.config.analysis_depth = "comprehensive"
        manager.config.output_format = ["json"]
        
        save_configuration!(manager)
        
        # Verify file was created
        @assert isfile(test_config_file)
        println("  ✅ Configuration file saved successfully")
        
        # Create new manager and load configuration
        manager2 = ConfigurationManager(test_config_file)
        load_success = load_configuration!(manager2)
        
        @assert load_success == true
        @assert manager2.config.key_number == 13
        @assert manager2.config.analysis_depth == "comprehensive"
        @assert manager2.config.output_format == ["json"]
        
        println("  ✅ Configuration file loaded successfully")
        println("  📊 Loaded key number: $(manager2.config.key_number)")
        println("  🔬 Loaded analysis depth: $(manager2.config.analysis_depth)")
        
        # Clean up test file
        rm(test_config_file)
        println("  🧹 Test file cleaned up")
        
    catch e
        println("  ❌ Error in file operations: $e")
        rethrow(e)
    end
end

"""
Test configuration presets
"""
function test_configuration_presets()
    try
        # Test beginner preset
        beginner_config = get_configuration_preset("beginner")
        @assert beginner_config.analysis_depth == "basic"
        @assert beginner_config.include_lie_elimination == false
        @assert beginner_config.output_format == ["txt"]
        println("  ✅ Beginner preset configured correctly")
        
        # Test advanced preset
        advanced_config = get_configuration_preset("advanced")
        @assert advanced_config.analysis_depth == "comprehensive"
        @assert advanced_config.include_lie_elimination == true
        @assert advanced_config.lie_threshold == 0.05
        @assert "json" in advanced_config.output_format
        println("  ✅ Advanced preset configured correctly")
        
        # Test performance preset
        performance_config = get_configuration_preset("performance")
        @assert performance_config.enable_parallel_processing == true
        @assert performance_config.cache_enabled == true
        @assert performance_config.show_progress == false
        println("  ✅ Performance preset configured correctly")
        
        # Test unknown preset
        unknown_config = get_configuration_preset("unknown")
        # Should return default configuration for unknown preset
        @assert unknown_config.analysis_depth == "standard"
        println("  ✅ Unknown preset handled correctly")
        
    catch e
        println("  ❌ Error in preset testing: $e")
        rethrow(e)
    end
end

"""
Test key number selection
"""
function test_key_number_selection()
    try
        # Test manual key selection
        manual_config = WonderGridConfig()
        manual_config.auto_key_selection = false
        manual_config.key_number = 25
        
        # Since interactive_key_selection requires user input for manual mode,
        # we'll test the logic for non-manual modes
        
        # Test random selection
        random_config = WonderGridConfig()
        random_config.key_selection_method = "random"
        
        # Capture output to avoid cluttering test results
        original_stdout = stdout
        redirect_stdout(devnull)
        
        key_num = interactive_key_selection(random_config)
        
        redirect_stdout(original_stdout)
        
        @assert 1 <= key_num <= 39
        println("  ✅ Random key selection working (selected: $key_num)")
        
        # Test optimal selection
        optimal_config = WonderGridConfig()
        optimal_config.key_selection_method = "optimal"
        
        redirect_stdout(devnull)
        key_num = interactive_key_selection(optimal_config)
        redirect_stdout(original_stdout)
        
        @assert 1 <= key_num <= 39
        println("  ✅ Optimal key selection working (selected: $key_num)")
        
        # Test sequential selection
        sequential_config = WonderGridConfig()
        sequential_config.key_selection_method = "sequential"
        
        redirect_stdout(devnull)
        key_num = interactive_key_selection(sequential_config)
        redirect_stdout(original_stdout)
        
        @assert key_num == 1  # Sequential starts with 1
        println("  ✅ Sequential key selection working (selected: $key_num)")
        
    catch e
        println("  ❌ Error in key selection testing: $e")
        rethrow(e)
    end
end

"""
Test configuration manager
"""
function test_configuration_manager()
    try
        test_config_file = "test_manager_config.txt"
        
        # Test manager creation with non-existent file
        manager1 = ConfigurationManager(test_config_file)
        @assert manager1.config.analysis_depth == "standard"  # Default value
        println("  ✅ Configuration manager created with defaults")
        
        # Modify and save configuration
        manager1.config.key_number = 7
        manager1.config.analysis_depth = "basic"
        save_configuration!(manager1)
        
        # Create new manager with existing file
        manager2 = ConfigurationManager(test_config_file)
        @assert manager2.config.key_number == 7
        @assert manager2.config.analysis_depth == "basic"
        println("  ✅ Configuration manager loaded existing configuration")
        
        # Test configuration display
        original_stdout = stdout
        redirect_stdout(devnull)
        display_configuration(manager2.config)
        redirect_stdout(original_stdout)
        println("  ✅ Configuration display working")
        
        # Clean up
        rm(test_config_file)
        println("  🧹 Test file cleaned up")
        
    catch e
        println("  ❌ Error in configuration manager testing: $e")
        rethrow(e)
    end
end

"""
Test error handling
"""
function test_error_handling()
    try
        # Test validation error display
        invalid_config = WonderGridConfig()
        invalid_config.key_number = -1  # Invalid
        invalid_config.lie_threshold = 2.0  # Invalid
        
        is_valid, errors = validate_configuration(invalid_config)
        @assert !is_valid
        @assert length(errors) >= 2
        
        # Test error display (capture output)
        original_stdout = stdout
        redirect_stdout(devnull)
        display_validation_errors(errors)
        redirect_stdout(original_stdout)
        
        println("  ✅ Validation error display working")
        
        # Test loading invalid configuration file
        invalid_config_file = "invalid_config.txt"
        open(invalid_config_file, "w") do file
            println(file, "[Invalid Section]")
            println(file, "invalid_key = invalid_value")
            println(file, "malformed line without equals")
        end
        
        manager = ConfigurationManager(invalid_config_file)
        load_success = load_configuration!(manager)
        
        # Should handle errors gracefully
        @assert load_success == false
        println("  ✅ Invalid configuration file handled gracefully")
        
        # Clean up
        rm(invalid_config_file)
        println("  🧹 Test file cleaned up")
        
    catch e
        println("  ❌ Error in error handling testing: $e")
        rethrow(e)
    end
end

"""
Test configuration parameter types and ranges
"""
function test_parameter_types_and_ranges()
    println("\n🔢 Test: Parameter Types and Ranges")
    
    try
        config = WonderGridConfig()
        
        # Test boolean parameters
        @assert isa(config.auto_key_selection, Bool)
        @assert isa(config.include_lie_elimination, Bool)
        @assert isa(config.enable_parallel_processing, Bool)
        println("  ✅ Boolean parameters correctly typed")
        
        # Test numeric parameters
        @assert isa(config.lie_threshold, Float64)
        @assert isa(config.max_threads, Int)
        @assert isa(config.minimum_data_points, Int)
        println("  ✅ Numeric parameters correctly typed")
        
        # Test string parameters
        @assert isa(config.key_selection_method, String)
        @assert isa(config.analysis_depth, String)
        @assert isa(config.error_handling_mode, String)
        println("  ✅ String parameters correctly typed")
        
        # Test array parameters
        @assert isa(config.output_format, Vector{String})
        println("  ✅ Array parameters correctly typed")
        
        # Test date parameters
        @assert isa(config.historical_data_range, Tuple{Date, Date})
        println("  ✅ Date parameters correctly typed")
        
    catch e
        println("  ❌ Error in parameter type testing: $e")
        rethrow(e)
    end
end

# Run the tests
if abspath(PROGRAM_FILE) == @__FILE__
    test_configuration_system()
    test_parameter_types_and_ranges()
end