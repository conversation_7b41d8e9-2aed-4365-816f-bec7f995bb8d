# ONE Filter Tests
# ONE 過濾器測試

using Test
using Dates

# 引入必要的模組
include("../src/types.jl")
include("../src/filter_engine.jl")
include("../src/filters/one_filter.jl")
include("../src/statistics/basic_stats.jl")

"""
創建測試用的彩票數據
"""
function create_test_lottery_data()::Vector{LotteryDraw}
    test_draws = [
        LotteryDraw([1, 5, 10, 15, 20], Date(2022, 1, 10), 10),  # 最新
        LotteryDraw([2, 6, 11, 16, 21], Date(2022, 1, 9), 9),
        LotteryDraw([1, 7, 12, 17, 22], Date(2022, 1, 8), 8),   # 號碼 1 出現
        LotteryDraw([3, 8, 13, 18, 23], Date(2022, 1, 7), 7),
        LotteryDraw([4, 9, 14, 19, 24], Date(2022, 1, 6), 6),
        LotteryDraw([1, 10, 15, 20, 25], Date(2022, 1, 5), 5),  # 號碼 1 出現
        LotteryDraw([5, 11, 16, 21, 26], Date(2022, 1, 4), 4),
        LotteryDraw([6, 12, 17, 22, 27], Date(2022, 1, 3), 3),
        LotteryDraw([7, 13, 18, 23, 28], Date(2022, 1, 2), 2),
        LotteryDraw([1, 14, 19, 24, 29], Date(2022, 1, 1), 1),  # 號碼 1 出現（最舊）
    ]
    return test_draws
end

"""
測試 ONE 過濾器的基本功能
"""
function test_one_filter_basic_functionality()
    @testset "ONE Filter Basic Functionality" begin
        test_data = create_test_lottery_data()
        engine = FilterEngine(test_data)
        
        # 測試號碼出現次數計算
        @test count_number_occurrences(engine, 1) == 4  # 號碼 1 出現 4 次
        @test count_number_occurrences(engine, 2) == 1  # 號碼 2 出現 1 次
        @test count_number_occurrences(engine, 39) == 0 # 號碼 39 未出現
        
        # 測試邊界條件
        @test_throws ArgumentError count_number_occurrences(engine, 0)   # 無效號碼
        @test_throws ArgumentError count_number_occurrences(engine, 40)  # 無效號碼
        
        println("✓ ONE 過濾器基本功能測試通過")
    end
end

"""
測試 ONE 過濾器計算
"""
function test_one_filter_calculation()
    @testset "ONE Filter Calculation" begin
        test_data = create_test_lottery_data()
        engine = FilterEngine(test_data)
        
        # 測試號碼 1 的 ONE 過濾器計算
        result = calculate_one_filter(engine, 1)
        
        # 驗證結果結構
        @test result.filter_type == ONE_FILTER
        @test result.filter_name == "ONE_FILTER_1"
        @test isa(result.current_value, Int)
        @test isa(result.expected_value, Float64)
        @test isa(result.is_favorable, Bool)
        @test 0.0 <= result.confidence_level <= 1.0
        @test isa(result.historical_values, Vector{Int})
        @test result.calculation_time >= 0.0
        
        # 測試當前 skip 值（號碼 1 最後在第 10 次開獎出現，也就是最新開獎）
        @test result.current_value == 0  # 最新開獎就有出現，所以 skip = 0
        
        # 測試歷史 skip 序列不為空
        @test !isempty(result.historical_values)
        
        println("✓ ONE 過濾器計算測試通過")
        println("  - 當前 skip: $(result.current_value)")
        println("  - 期望值: $(round(result.expected_value, digits=2))")
        println("  - 是否有利: $(result.is_favorable)")
        println("  - 信心水準: $(round(result.confidence_level, digits=2))")
    end
end

"""
測試批量計算功能
"""
function test_one_filter_batch_calculation()
    @testset "ONE Filter Batch Calculation" begin
        test_data = create_test_lottery_data()
        engine = FilterEngine(test_data)
        
        # 測試批量計算
        test_numbers = [1, 5, 10, 15, 20]
        results = calculate_one_filter_batch(engine, test_numbers)
        
        @test length(results) == length(test_numbers)
        
        # 驗證每個結果
        for (i, result) in enumerate(results)
            @test result.filter_type == ONE_FILTER
            @test occursin("ONE_FILTER_$(test_numbers[i])", result.filter_name)
        end
        
        println("✓ ONE 過濾器批量計算測試通過")
    end
end

"""
測試所有號碼的計算
"""
function test_all_one_filters()
    @testset "All ONE Filters Calculation" begin
        test_data = create_test_lottery_data()
        engine = FilterEngine(test_data)
        
        # 測試所有號碼的計算
        all_results = calculate_all_one_filters(engine)
        
        @test length(all_results) == 39  # 應該有 39 個結果
        
        # 驗證所有結果都是有效的
        for result in all_results
            @test result.filter_type == ONE_FILTER
            @test 0.0 <= result.confidence_level <= 1.0
            @test result.calculation_time >= 0.0
        end
        
        println("✓ 所有 ONE 過濾器計算測試通過")
    end
end

"""
測試有利號碼篩選
"""
function test_favorable_number_filtering()
    @testset "Favorable Number Filtering" begin
        test_data = create_test_lottery_data()
        engine = FilterEngine(test_data)
        
        # 計算前 10 個號碼的結果
        test_numbers = collect(1:10)
        results = calculate_one_filter_batch(engine, test_numbers)
        
        # 篩選有利號碼
        favorable_numbers = filter_favorable_numbers(results)
        
        @test isa(favorable_numbers, Vector{Int})
        @test all(1 <= n <= 39 for n in favorable_numbers)
        @test issorted(favorable_numbers)  # 應該是排序的
        
        println("✓ 有利號碼篩選測試通過")
        println("  - 有利號碼: $favorable_numbers")
    end
end

"""
測試統計摘要功能
"""
function test_one_filter_summary()
    @testset "ONE Filter Summary" begin
        test_data = create_test_lottery_data()
        engine = FilterEngine(test_data)
        
        # 計算前 10 個號碼的結果
        test_numbers = collect(1:10)
        results = calculate_one_filter_batch(engine, test_numbers)
        
        # 獲取統計摘要
        summary = get_one_filter_summary(results)
        
        # 驗證摘要結構
        @test haskey(summary, "total_numbers")
        @test haskey(summary, "favorable_numbers")
        @test haskey(summary, "high_confidence_numbers")
        @test haskey(summary, "average_confidence")
        @test haskey(summary, "average_current_skip")
        @test haskey(summary, "average_expected_value")
        @test haskey(summary, "confidence_distribution")
        
        @test summary["total_numbers"] == length(test_numbers)
        @test 0.0 <= summary["average_confidence"] <= 1.0
        
        println("✓ ONE 過濾器統計摘要測試通過")
        println("  - 總號碼數: $(summary["total_numbers"])")
        println("  - 有利號碼數: $(summary["favorable_numbers"])")
        println("  - 平均信心水準: $(round(summary["average_confidence"], digits=2))")
    end
end

"""
測試快取功能
"""
function test_one_filter_caching()
    @testset "ONE Filter Caching" begin
        test_data = create_test_lottery_data()
        engine = FilterEngine(test_data, cache_enabled=true)
        
        # 第一次計算
        result1 = calculate_one_filter(engine, 1)
        cache_size_after_first = length(engine.filter_cache)
        
        # 第二次計算（應該使用快取）
        result2 = calculate_one_filter(engine, 1)
        cache_size_after_second = length(engine.filter_cache)
        
        # 驗證快取工作
        @test cache_size_after_first == 1
        @test cache_size_after_second == 1  # 快取大小不變
        @test result1.current_value == result2.current_value
        @test result1.expected_value == result2.expected_value
        
        # 測試快取清除
        clear_cache!(engine)
        @test length(engine.filter_cache) == 0
        
        println("✓ ONE 過濾器快取功能測試通過")
    end
end

"""
測試錯誤處理
"""
function test_one_filter_error_handling()
    @testset "ONE Filter Error Handling" begin
        test_data = create_test_lottery_data()
        engine = FilterEngine(test_data)
        
        # 測試無效號碼
        @test_throws ArgumentError calculate_one_filter(engine, 0)
        @test_throws ArgumentError calculate_one_filter(engine, 40)
        
        # 測試空數據
        empty_engine = FilterEngine(LotteryDraw[])
        @test_throws ArgumentError calculate_one_filter(empty_engine, 1)
        
        println("✓ ONE 過濾器錯誤處理測試通過")
    end
end

"""
執行所有 ONE 過濾器測試
"""
function run_one_filter_tests()
    println("🧪 開始執行 ONE 過濾器測試...")
    
    try
        test_one_filter_basic_functionality()
        test_one_filter_calculation()
        test_one_filter_batch_calculation()
        test_all_one_filters()
        test_favorable_number_filtering()
        test_one_filter_summary()
        test_one_filter_caching()
        test_one_filter_error_handling()
        
        println("\n🎉 所有 ONE 過濾器測試通過！")
        return true
    catch e
        println("\n❌ ONE 過濾器測試失敗: $e")
        return false
    end
end

# 如果直接執行此文件，運行測試
if abspath(PROGRAM_FILE) == @__FILE__
    run_one_filter_tests()
end
