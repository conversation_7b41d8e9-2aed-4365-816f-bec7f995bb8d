---
created: 2025-07-24T22:09:56 (UTC +08:00)
tags: [lotto,lottery,software,odds,formula,probability,strategy,software,system,statistics,drawings,analysis,numbers,winning,combinations,randomness,lotto jackpot,random play,]
source: https://saliu.com/bbs/messages/919.html
author: 
---

# 動態、靜態彩票過濾器、軟體、分析 --- Dynamic, Static Lottery Filters, Software, Analysis

> ## Excerpt
> Study dynamic, static filtering in lotto, lottery analysis software. The odds of winning lotto jackpots improve dramatically with dynamic lottery filters created by the founder of lotto mathematics.

---
2002 年 2 月 14 日發布。

-   我主張我們必須動態地看待一切，因為宇宙中的一切都是動態的。我現在不想深入探討其基本哲學。我只想提醒讀者，我的賭博理論是建立在_動態的基礎_上，而拒絕_靜態_ 。我早期的彩票軟體就是在_消除條件_ （或_限制性模式_ ，或簡稱為_彩票限制）_ 的基礎上開發的。這些條件會_限製_或_消除_輸出中的某些彩票組合。

<u>1990 年代初的某一天，我的咖啡濾紙用完了！但</u>我還是需要早上喝咖啡，因為如果沒有咖啡，我連一行程式碼都寫不出來！我煮了咖啡，但那天的咖啡味道糟透了！我意識到濾紙的重要性。於是，我想出了_**彩票濾紙**_的概念。

彩票過濾器如今已非常普及。你可以在咖啡店、吸煙區或非吸煙區聽到它們。我在網路上也聽過。我的網站很多流量都源自於對_**彩票過濾器的**_搜尋。然而，這些過濾器卻被廣泛誤解。它們本應是動態的。可惜的是，大多數人卻把它們當成了靜態的！ _「玩奇偶數數量均衡的遊戲！兩個單數，四個雙數！最低點和最高點數量均等！最好也考慮一下彩票的中獎金額！”_

還有人會把所有的犁都變成劍，把那些相信彩券策略的「異教徒」斬首！後者就是「審判官」。他們高喊：  
_“每種組合的機率永遠都是 _**p**_ 。因此，任何試圖制定彩票遊戲策略的嘗試都必然會以失敗告終。”_

但他們不承認機率還有另一個基本要素： _**確定性程度（DC）**_ （參見_**賭博基本公式（FFG））**_ 。

「異端」們面臨著兩個非常嚴重的問題。靜態濾鏡會產生數量驚人的彩票組合供玩家選擇，這不切實際。此外，連續輸掉的彩票（跳過）也很難追蹤。組合數量和跳過的彩票使得靜態彩票過濾器毫無用處。這絕對是大忌。

另一方面，「調查員」對機率的理解卻很膚淺。讓他們去玩一場歷史悠久的三選一彩票遊戲。讓他們絞盡腦汁去研究_**賭博的基本公式（FFG）**_ 。

他們可以選擇任何彩票遊戲。我選了三選一，因為坦白說，人的一生實在太短暫了。三選一組合的個別機率是_千分之一_ 。然而，個別機率是靜態的。它不能描述動態現象，例如我們稱之為_彩票抽獎的_一系列時間事件（有些人使用 _“抽獎”_ 這個詞，因為他們年紀較大）。讓他們抽 1000 次。他們不會在其中找到所有的三選一組合。他們的 p 相等，為什麼頻率不相等呢？在任何 1000 次抽獎組中，只能找到大約 63% 的彩票組合。 （賭博的基本公式在這種情況下總是有效的。）

有些組合在 3000 次、4000 次甚至 5000 次連續的彩票抽獎中都找不到！這是怎麼回事？靜態上，這些組合的地位相同。動態上，這些組合是由不同規律引導的更高級過程的較小部分。在 _**FFG**_ 中， _p_ （個別機率）是系統的一部分。 P 不會被壓縮，而是被納入到一個更大的過程中。

我現在想在「異端」和「宗教裁判所」之間找到共同點。我會用我的方式，因為我更願意證明雙方都錯了！我想證明使用彩票過濾器明顯比隨機投注要好。同時，靜態過濾器毫無用處。它們在現實生活中根本用不上，因為它們太貴，而且會導致損失。

多年來，我一直提供免費軟體程式（包括原始程式碼和演算法），用於查找給定索引（或字典順序）的組合： NthIndex 。我還創建了更強大的軟體。這些程式分別是 LexicographicIndexes 、 SEQUENCE 、 NthIndex 和 DrawIndex 。

事實上， **DrawIndex** 可以獲得任何彩票結果（抽獎）檔案並顯示組合的字典索引。

![Software to calculate the combination lexicographical order of drawings in any lottery results file.](https://saliu.com/ScreenImgs/DrawIndex.gif)

I ran my software to generate multiple lotto combinations and save them to file. I applied the test to PA 6/69 lotto game (a very tough game, indeed; the lotto jackpot odds are _1 in 119,877,472_). I generated first 1000 combinations around the mid-point index: 59,938,736. That is, the lotto program generated combinations between the counts of 59,938,236 and 59,939,236. Next, I checked for winners in the output file against real draws in PA 6/69 lotto (350 real draws at my discretion). The _mid-point output_ achieved 3 _5 of 6_ hits and 140 _4 of 6 hits_. The game odds are _1 in 317,000_ for _5 of 6_ and _1 in 4092_ for _4 of 6_. The _5 of 6_ hits were recorded in 350 x 1000 = 350,000 combinations. Therefore, the _mid-point_ strategy was about 3 times better than random lotto play in the _5 of 6_ case. For the _4 of 6_ case, _1 in 4092_ applied to 350,000 combinations translates into 85. The mid-point strategy hit 140 times, or about one and a half times better.

The next test was applied against the _**FFG median**_, instead of _the mid-point_. The _FFG median_ is calculated in _**FFG**_ for a _**degree of certainty, DC**_, equal to **50%**. The _FFG median_ is 83,092,731 for a 6/69 lotto game. A number of 1000 combinations were generated in the 83,092,231 – 83,093,231 range. The results were undoubtedly better. The lotto strategy yielded 6 _5 of 6_ hits and 238 _4 of 6_ hits. Compared to purely random play, the _FFG median_ strategy was 6 times better for _5 of 6_ and about 3 times better for _4 of 6_.

The following is a report for a Powerball game that draws 5 regular numbers from 59 and one Power Ball from 39.
