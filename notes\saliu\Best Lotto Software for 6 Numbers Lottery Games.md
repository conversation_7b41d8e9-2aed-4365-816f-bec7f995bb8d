---
created: 2025-07-24T22:10:03 (UTC +08:00)
tags: [lotto,software,power,powerful,best,program,programs,6-number lotto games]
source: https://saliu.com/lotto6-software.html
author: 
---

# Best Lotto Software for 6 Numbers Lottery Games

> ## Excerpt
> The best lotto software for 6-number lottery games is BRIGHT6. It is a collection of powerful programs for pick-6 lotto, or lottery-6 games.

---
The final step is to sort the drawings (the lines in your lottery files) in ascending order. My lotto software requires files with the drawings sorted in ascending order. Again, my lotto software is the best for such a task: Sorting. Also, MDIEditor And Lotto WE has a lotto sorting function. Sorting creates a nice format of the lottery draw file!  
An example of the contents of DATA-6 for a 6/49 lotto game:

-   The top line in the real-drawings data file represents the latest (the most recent) draw in the game.
-   The bottom line in the real-drawings data file represents the oldest draw in the game.
-   If your lottery agent lists the drawings inversely (from oldest to newest), run the option _U = File Reverser (UpDown)_ in menu II.

The first menu item (E) opens my MDIEditor in 64-bit Windows (in "C:\\Program Files (x86)\\MDIEditorLottoWE\\MDIEditWE"). The other item (M) takes care of the 32-bit Windows: "C:\\Program Files\\MDIEditorLottoWE\\MDIEditWE". You might want to check your installation of MDIEditor And Lotto WE. Reinstall, if necessary, to match one of the above program locations.

Program name: Any text editor can be used for this task.

For more info, read:  

-   [_**Manual, Tutorial of <u>Lottery Software</u>, Lotto Apps**_](https://saliu.com/forum/lotto-book.html)
-   [_**<u>MDIEditor Lotto WE</u>: Lottery Software Manual, Book, ebook, Help**_](https://saliu.com/MDI-lotto-guide.html)
-   [Help on Software for Lottery, Lotto, Gambling: Download, Install, Run, Maintain Results Files](https://saliu.com/Help.htm)
-   [State Lottery, Lotto Drawings, Results, Past Winning Numbers: Online, over the Internet](https://saliu.com/bbs/messages/920.html)

Please read also a very basic tutorial (part of this lotto software package):  
Bright6.TUT (more info on lotto data files, including the very important operation named concatenation).

The concatenation is done in Super Utilities (option Make/Break/Position, then Make, then option 1 or 2: Make D6 from DATA-6 and SIM-6).  

This version of Bright6 requires a D6 data file of at least 12000000 (12 million) lotto combinations (lines).

Here is the best procedure of creating the SIM-6 and D6 files to meet the size requirement of 12000000 (12 million) combinations (lines). For example, the 6/49 lotto game has 13983816 total combinations. I generated all combinations in lexicographical order by running my software PermuteCombine or Combinations. I named the file ALL-6-49 (easy to remember).

Then, I shuffled that lexicographical file ALL-6-49 in Shuffle (option: F = Files, then V = Shuffle Vertically). The result was SIM-6, with all 6/49 combinations, 13983816 lines randomly arranged.

If your lotto game has fewer than 12 million combinations, generate the necessary balance in Super Utilities, Option S, then Generate SIM file. Add this second SIM6-2 file to the shuffled file of all lotto combinations in your game (concatenate SIM6-1+SIM6-2 to SIM-6). See also the info for Menu #2, option B = Concatenate files.

\*\* It is of the essence to SHUFFLE (randomize) all your SIM files. Do not use, under any circumstances, SIM files in lexicographical order! Otherwise, the winning reports (W, MD) will turn into big headaches! Some filters will be so unusually high that several columns will run into one another. The Check Strategy functions will NOT run any longer \*\*

![Run the best integrated lottery software to win pick-6 or 6-number lotto games.](https://saliu.com/HLINE.gif)

-   **<u>Please note: the lotto games tend to get larger and larger as far as the total number of combinations is concerned. It might be impossible to shuffle such files.</u>**
-   Instead of randomizing (shuffling) lexicographic files with all combinations, generate instead some 13000000 (13 million) random combinations. Run option _E = Randomized Combinations_.
-   You can try an even better method (one of my secrets). In menu II, run option _W = Wheel On-the-Fly (Wheel6.exe)_. Generate _5 of 6_ lotto wheels.
-   If it gets too slow after a while, save the first run as SIM-1. Then repeat until you get enough SIM-n files to reach some 13 million combinations. You need to concatenate all those SIM-n files to the big SIM6 file.

![Do not use huge lottery drawings files for gigantic lotto games.](https://saliu.com/HLINE.gif)

\* Option 'W = Winning Reports (W6 Files)'  
Create the winning (W6 & MD6) reports. Press F5 (or W in B6) to generate the reports. Type 100 (or more) for the length of the report. Type D6 for the name of the data file, and the default for total drawings to use. Type W6.1 to W6.4 and MD6.1 to MD6.4 for the report names.

The four report files will show a number of parameters or FILTERS. Based on the reports, you feed the combination generating program (L or R) or the wheel generating program (W in Menu #2) with the filters. The process of setting filter values is known as STRATEGY SELECTION.

Program name: Report6.

For more information, read:  

-   [_**Manual, Tutorial of <u>Lottery Software</u>, Lotto Apps**_](https://saliu.com/forum/lotto-book.html)
-   [_**<u>MDIEditor Lotto WE</u>: Lottery Software Manual, Book, ebook, Help**_](https://saliu.com/MDI-lotto-guide.html)
-   [Filtering, Filters in Lottery Software, Lotto Software](https://saliu.com/filters.html)
-   [Lotto, Lottery Software Tutorial - "My kingdom for a good tutorial!"](https://saliu.com/bbs/messages/818.html)
-   [_**The Best Ever Lottery Strategy, Lotto Strategies**_](https://software.saliu.com/lottery-strategy-lotto-strategies.html) — a must-read ebook on strategies.

\* Option 'O = Sort Filter Reports by Column'  
The program sorts the W6, MD6, GR6, DE6, FR6, SK6 reports by column, helping the user see more easily the filters -- e.g. filters of wacky values. The sorting is done by type of winning reports. The program also offers the correct choices (correct names) for filters to sort.

Program name: SortFilterReports6.

For more information, read:  

-   [Filtering, Filters in Lottery Software, Lotto Software](https://saliu.com/filters.html)
-   [Lotto, Lottery Software Tutorial](https://saliu.com/bbs/messages/818.html) - _"My kingdom for a good tutorial!"_
-   [Cross-Reference Lottery, Lotto Strategy Files](https://saliu.com/cross-lines.html).

\* Option 'C = Check Strategies (Filter Settings)'  
The function analyzes the 4 W6 + 4 MD6 reports to establish any type of strategy, between MINIMUM and MAXIMUM values. The strategy report will show how many times a particular lotto strategy hit in the past lottery drawings. In other words, we check how a collection of filter settings would have fared in the past.  
The program also creates the strategy files in the correct format (error-free); the strategy files are named ST6.000 (default). You need to remember the ST file names! It's a very good idea to create a simple text file where you record various lottery strategies: ST names and what the strategies are based on.

\*\* Potential errors when checking for lotto strategies.  
#1: Do NOT use SIM files in LEXICOGRAPHICAL ORDER. Always shuffle all your SIM files.  
#2: Do NOT mix different game formats in your lotto files; that includes the file with your REAL lottery drawings and your SIMulated files.  
#3: Sometimes some filters can get way, way out of range. The value can be wider than what I planned as the maximum length for that particular filter. I wanted the reports to be as readable as possible.

If #3 happens, the strategy checking functions will trigger errors. You can fix the error by opening your winning reports one by one in a text editor. You will notice that two neighboring columns are no longer separated by at least one blank space. The older 16-bit lotto software added the % character at the end of the column.

The culprit is the number in the column which is no longer aligned with numbers above and below. You need delete one (very rarely more than one) character at the end of that lengthy number. Make sure that there is one blank space between the numbers in the two columns and that they are properly aligned in the respective columns. Here is a visual example:

1234    23  
12345123 = strategy-checking error

Corrected W/MD file:  
1234   23  
1234 123

Repeat the procedure with all your lotto winning reports, as necessary. \*\*

Program name: Strategy6.

For more info, read:  

-   [_**<u>MDIEditor Lotto WE</u>: Lottery Software Manual, Book, ebook, Help**_](https://saliu.com/MDI-lotto-guide.html)
-   [Filtering, Filters in Lottery Software, Lotto Software](https://saliu.com/filters.html).

\* Option 'H = Strategy Hits in the Past'  
It generates lotto-6 combinations for situations when a particular strategy (as in the ST6\*.\* files) hit in the past. The program needs an input file created by Strategy6 (the previous function). The input file consists of the draw IDs for the hit situations. Otherwise, the user will manually input the filter settings and the drawings IDs.

Program name: StrategyHit6.

For more info, read:  

-   [_**Manual, Tutorial of <u>Lottery Software</u>, Lotto Apps**_](https://saliu.com/forum/lotto-book.html)
-   [Filtering, Filters in Lottery Software, Lotto Software](https://saliu.com/filters.html).

\* Option 'L = Lexicographic Combinations'  
\* Option 'R = Randomized Combinations'  
This is the ultimate goal of LotWon lotto software: Generate winning lotto combinations. We keep records of past lotto draws (maintain data files), then analyze the data files and generate the winning reports. Then, we analyze the W reports and select filter values for our lotto strategies. We finally apply the lotto strategies to the combination generators.

Each lotto combination generating program has several functions of its own.

\* L -- Program name: Lexico6 – it generates 6-number lotto combinations in LEXICOGRAPHICAL ORDER. That is, the program starts the generation at lexicographic index #1 (e.g. 1, 2, 3, 4, 5, 6) and ends at the last index in the lotto set (e.g. 44, 45, 46, 47, 48, 49).  
Functions in this program:  
N = Normal Lexicographic - NO ranges - NO favorites

-   generates every combination in the lotto set, from lexicographical index #1 to the last index;

1 = 1 favorite number - NO ranges

-   generates every combination in the lotto set, from lexicographical index #1 to the last index; also, each and every combination will contain one FAVORITE number chosen by the software user;

2 = 2 favorite numbers - NO ranges

-   generates every combination in the lotto set, from lexicographical index #1 to the last index; also, each and every combination will contain two FAVORITE numbers chosen by the software user;

R = Combinations between positional RANGES

-   generates every lotto combination by positional RANGES; e.g. numbers in 1st position between 1 and 15; numbers in 2nd position between 5 and 26; numbers in 6th position between 33 and 49;

P = PURGE an output file of combinations

-   takes an output lotto combination file previously generated and eliminates additional combinations by applying further filtering.

\* R -- Program name: Combine6 – it generates 6-number lotto combinations in RANDOMIZED MANNER. That is, the program starts and ends the generation anywhere in the lotto set, instead of lexicographically.  
Functions in this program:  
0 = NO favorite numbers, NO shuffle

-   generates randomized lotto combinations, without any favorite numbers, or clusters (shuffled combinations);

1 = 1 favorite number - NO shuffle

-   generates randomized lotto combinations; also, each and every random combination will contain one FAVORITE number chosen by the software user;

2 = 2 favorite numbers - NO shuffle

-   generates randomized lotto combinations; also, each and every random combination will contain two FAVORITE numbers chosen by the software user;

S = SHUFFLE numbers (ALL #s in game)

-   generates ALL lotto numbers in one cluster or group, 6 numbers per each line; e.g. a 6-49 lotto game will have clusters of 9 lines (combinations), 6 numbers per line; the last line will repeat 3 numbers from previous combinations.

\* Both lotto programs above, as well as Wheel6, Combine6-10 (plus others) have two more functions that also eliminate unwanted combinations:  

-   INNER filters (they eliminate around 98% of all combinations -- enable it rarely);
-   LIE elimination: You noticed that, more often than not, your lotto output files do NOT have winning numbers -- not 6 winners, not 5, not 4, not 3... You can open the LIE file and generate combinations that do NOT contain groups of lotto numbers existent in the LIE file.

For more info, read:  

-   [Professors Play All Lotto Numbers and Win the Jackpot](https://saliu.com/all-lotto-numbers.html)

\* Option 'G = Pairing Reports, Custom Grids, Lotto Combinations'  
This program generates a series of reports on the pairings of the lotto-6 game; it also generates pairings in the manner of lotto-6 combination generators in Bright6.  
The _**Reports**_ option will show how many times each pairing came out; it also shows the winning pair reports in the manner of the W/MD files created by Report6.

The program is especially useful in conjunction with the LIE (REVERSED) strategy feature present in all lotto combination generators. The pairings do not lead to winning 6-number lotto combinations the very next draw, far more often than not.

We can see that no lotto drawing has all top 5 pairings only. We can generate lotto combinations for the top 15 or 20 top pairings — and we will be wrong for sure. That output file qualifies as a _**LIE**_ file. We can apply, without possibility of error, the ID6 LIE filter. In a vast majority of cases, even ID5 will be a very safe filter. Sometimes, even ID4 will be a safe filter.

The filters ID3 or even less ID2 can still offer winning combinations. Filter ID1 can never be applied. The grid files always contain all numbers in the lotto game. If we create the _pure wonder-grid_ with the top-5 pairings, it will not hit the jackpot in thousands of drawings. The ID1 filter always eliminates all combinations. Maybe ID2 will generate 4- or 5-winning-number combinations. It is worth trying, though — there aren't many combinations to play. If too many, we can apply more filters by _**Purge**_ (in Lexico6).

Also, you can create ANY PAIRING GRID FILE in Super Utilities. You simply copy and paste ANY AMOUNT OF NUMBERS from the file entitled PAIRS6. Or, you can create directly a file named TOP6. Copy and paste has the advantage of creating top files which are not necessarily the top pairs. For example, you copy and paste the numbers corresponding to the pairs in the range 15 – 30. Just open an empty Notepad file …

Program name: PairGrid6.  
Replaces: SkipPair6 (still in the package — just type its name at a command prompt).

For more info, read:

-   [Software News: Lotto, Lottery, Horse Racing, Pairs Programs, LIE Strategy](https://saliu.com/software-news.html).
-   [Lotto, Lottery Strategy in Reverse: Not-to-Win Leads to Not-to-Lose or WIN](https://saliu.com/reverse-strategy.html) (2011)
-   [Lottery Pairs and Repeat Probability](https://saliu.com/forum/lottery-pairs.html)

\* Option 'F = Wheels from Files'  
This program takes an input file of lotto-6 combinations and converts the combinations to 'k of 6' lotto wheel format - from '1 of 6' to '5 of 6'; also, a number of combinations can be input in a random manner, without any wheeling.

The function is useful if you want to reduce the number of lotto combinations in an output file previously generated. For example, you generated thousands of combinations in Combine6 with light filtering; you want to play only a handful of lotto combinations that have no more than k numbers in common (lotto wheeling); evidently, you settle for a lower-tier lottery prize.

Program name: WheelIn6.

For more info, read:  

-   [Lotto Wheels for Lotto Games Drawing 5, 6, or 7 Numbers: Balanced and Randomized](https://saliu.com/lotto_wheels.html)
-   [Software to Verify Lotto Wheels for Missing Combinations; Generate Reduced Lotto Systems](https://saliu.com/check-wheels.html)
-   [Lotto Wheeling Software, Winning Report Generator](https://saliu.com/bbs/messages/wheel.html).

\* Option 'B = Birthday Paradox Strategies'  
I had written an original essay touching a few issues of creating winning systems from the Birthday Paradox, or the probability of repetition. Such systems would apply to games of chance such as lotto, lottery, roulette ... and more. There are lottery players and gamblers who now realize how important the probability of repetition is.

Can such mathematical knowledge be applied to gambling, especially lottery? I was skeptical when I first heard about it and was asked about it. The difficulty of achieving thorough understanding of this phenomenon was caused by a parameter I call _number of elements_. Indeed, the roulette numbers repeat. You can see them all the time, if the casinos turn on the electronic displays known as the marquees. But is there a rule, a mathematical formula that enables us to calculate the repeat probability?

I thought more deeply on this repetition fact. For example, I look at a sequence of 8 roulette numbers as an eight-element string. The degree of certainty is better than 50% that such string should contain one repetition (duplication). One of the eight numbers should be a repeat with a 50-50 chance. The same is true about lottery drawings. In this case, the element is the index of the combination (or set) drawn. Every lotto combination, for example, is defined by an index or lexicographical order, or lexicographic rank.

With this new knowledge in mind, I studied some real data: Lottery drawings and roulette spins. I was somehow surprised to discover that repetition occurs close to that cutoff point of the 50-50 chance! I should also point out that the strength of the analysis and system creation is stronger at the beginning of the game. For lottery and lotto, the beginning is clear: A game starts with the first drawing of a game format.

Program names: Collisions, BirthdayParadox.

For more info, read:  

-   [Applications of the Birthday Paradox: Lottery, Lotto, Roulette](https://saliu.com/birthday-paradox.html)
-   [The Birthday Paradox: Combinatorics, Probability of Duplication, Coincidences, Collisions, Repetition](https://saliu.com/birthday.html).

\* Option 'S = Super Utilities'  
This piece of software bundles a lot of utilities for 6-number lotto games. Each function in turn has several features of its own.

S = Files (SIM-6, Count Lines)

-   Simulate a SIM-6 file
-   Count lines in text files

D = Duplicates: Strip and Wheel

-   The function retrieves the input data, finds duplicate lines, and eliminates them.

F = Frequency Reporting by Number

-   The function counts how many times each lotto-6 number came out in the specified range of past drawings. Then, it plots a 'skip chart' - drawings between hits. Also, the function saves to two files the 'most frequent pairings' (BEST6) and the 'least frequent pairings' (WORST6).

W = Check for Winners

-   The function checks for groups of 2, 3, 4, and 5 winning lotto-6 numbers in two manners: 1) an OUTPUT file against a data file with REAL draws; 2) POOLS of numbers against a data file with real lotto drawings.

T = Sort or Add-up Data Files

-   Sort your DATA-6 file in ASCENDING order

-   Add-up the numbers in each lotto combination in a data file to SUM TOTALS
    
    G = Generate Combinations, Favorites, Least-Number-Groups
    
    -   The function generates lotto-6 combinations lexicographically. You can play FAVORITE numbers: 1, 2, 3, 4; or NO favorites. You can also eliminate (least) singles, pairings, triples, quads. This function does not require a lotto data file.
    
    M = Make/Break/Position (D6, Positional Ranges, Break Long Lines to 6-Number Combinations)
    
    -   Make D6 without LEAST6
    -   Make D6 with LEAST6
    -   Make D6 with LEAST & BEST6
    -   Break 5+ combinations to 6-number lines
    -   Generate lotto combinations by positional ranges (positional limits)
    
    1 = Singles Rundown
    
    -   Calculate the frequency of every single in a 6-number lotto game;
    -   sort the singles by frequency in descending order;
    -   create a 'least single file' (singles that have not come out in the specified range of lottery drawings).
    
    2 = Pairs Rundown
    
    -   Calculate the frequency of every pair (pairing) in a 6-number lotto game;
    -   sort the pairs by frequency in descending order;
    -   create a 'least pair file' (pairs that have not come out in the specified range of lotto drawings).
    
    3 = Triplets Rundown
    
    -   Calculate the frequency of every triplet in a 6-number lotto game;
    -   sort the triplets by frequency in descending order;
    -   create a 'least triplet file' (triplets that have not come out in the specified range of lottery drawings).
    
    4 = Quadruplets Rundown
    
    -   Calculate the frequency of every quadruplet in a 6-number lotto game;
    -   sort the quadruplets by frequency in descending order;
    -   create a 'least quadruplet file' (quadruplets that have not come out in the specified range of lotto drawings).
    
    5 = Quintets Rundown
    
    -   Calculate the frequency of every quintet in a 6-number lotto game;
    -   sort the quintets by frequency in descending order; ~ create a 'least quintet file' (quintets that have not come out in the specified range of lotto drawings).

Program name: SoftwareLotto6.

For more info, read:  

-   [Lotto Software for Singles, Pairs, Triples, Quadruples, Quintuples, Sextuples](https://saliu.com/gambling-lottery-lotto/lotto-software.htm).

![Book #2 represents GENERAL-PURPOSE lottery, lotto, gambling software, including 6-number lotto games.](https://saliu.com/ScreenImgs/lotto-b61.gif)

\* The majority of the programs in this menu represent GENERAL-PURPOSE lottery and gambling software. The only exception is Wheel6 (option W = Wheels On-the-Fly). It generates lotto wheels in the manner of randomized combinations (Combine6).

\* Option 'S = Sort Data Files'  
It sorts the lotto data files in ASCENDING order; it only formats nicely the pick-3, pick-4 and horseracing files.

This function is a great utility in conjunction with the data files. The numbers in every combination (line) in a lotto data file must be sorted in ascending order.

This task can be also performed specifically for 6-number lotto data files in Super Utilities (Main Menu), option T = Sort or Add-up Data Files.

Program name: Sorting.

Sorting is packed with very useful features, including database tasks of files in text (ASCII) format. You can sort entire text files vertically, without in-line sorting. Also you can sort vertically on columns (fields). The columns can be numerical and/or alphanumeric (text only, or a mixture of numbers and text).

Did you know that sorting can be fun … as fun as dancing, for example? Watch this on YouTube:

-   [Insert-sort with Romanian folk dance](https://www.youtube.com/watch?v=ROalU379l3U).

\* Option 'C = Check Winners'  
Check for winning numbers in OUTPUT files against REAL drawing lotto files. The combinations to play were saved first to output text files by the combination generators.

This task can be also performed specifically for 6-number lotto data files in Super Utilities (Main Menu), option W = Check for Winners.

Program name: Winners.

\* Option 'R = Shuffle or Randomize Elements'  
Shuffle lotto combinations files (text files); then go to the line equal to the probability median (FFG = 50%). The program can also shuffle numbers in a highly randomized manner. There is a plethora of randomization functions in this program! The program can generate lotto combinations that mimic the official lottery drawings. Included are modules for Powerball, Mega Millions, Euromillions.

Program name: Shuffle.

For more info, read:

-   [Random Numbers: Algorithms, Shuffle, Randomize, Software](https://saliu.com/random-numbers.html);
-   [Greatly Improved Shuffle, Randomize](https://forums.saliu.com/shuffle-randomize-software.html).

\* Option 'M = Sum-up Lottery Data Files and Games'  
The program calculates the number of lottery combinations that add-up to a sum-total. It also calculates the sums of each draw in lottery files, plus root sum, and standard deviation. You can generate such lottery combinations and save them. The program creates summary reports for the game: Every sum-total and its amount of combinations, plus percentages.

Program name: Sums.

For more info, read:

-   [Basics of a Lottery, Lotto Strategy Based on: Sums (Sum-Totals); Odd or Even; Low or High Numbers](https://saliu.com/strategy.html)
-   [Lottery, Lotto Sums, Sum-Totals](https://saliu.com/forum/lottery-sums.html)
-   [Lotto, Lottery Software to Calculate Sum-Totals, Odd-Even, Low-High Patterns](https://saliu.com/bbs/messages/626.html).

\* Option 'K = Create Lotto, Lottery, and Gambling Systems'  
This program creates lottery and gambling systems based on two or three consecutive skips; the most recent skips make it into the system.

Program name: SkipSystem.

For more info, read:

-   [**<u>Lottery Skip Systems</u>**](https://saliu.com/skip-strategy.html): _**Lotto, Powerball, Mega Millions, Euromillions**_.

\* Option 'F = Rank Lottery Numbers by Frequency'  
This program generates frequency reports two ways: 1.- Regardless of position; 2.- Position by position. The lotto numbers are ranked by frequency in descending order, from hot, to mild, to cold.

Program name: FrequencyRank.

For more info, read:

-   [Lottery Strategy, Systems Based on Number Frequency](https://saliu.com/frequency-lottery.html)

\* Option 'V = Verify Data Files'  
This program parses the lottery data files to make sure they comply with the format required by LotWon lotto software.

Program name: Parsel.

For more info, read:

-   [Software to Correct Errors in Lottery, Lotto Results Files](https://saliu.com/bbs/messages/2.html).

\* Option 'D = Skips, Decades, Frequencies'  
The program shows the lotto-6 drawings as strings of skips, high/low, odd/even, increase/decrease from previous draw. The program also generates a lotto-decade report and a report of frequencies from 3 groups: hot, mild, cold. You can use the skips, high/low, odd/even, decades, frequencies as filters in the lotto combination generators or the purge function. The lotto software can check if or when a strategy hit in the past. The 'Strategy Hits' function reports how many combinations a particular lotto strategy would have generated in winning situations.

Program name: SkipDecaFreq6.

For more info, read:

-   [Lotto Decades: Software, Reports, Analysis, Strategies](https://saliu.com/decades.html)
-   [Statistical Frequency Reports for Pennsylvania 5/39 Lotto](https://saliu.com/frequency-reports.html)
-   [Lottery, Lotto, Combinations, Tables of Frequency Systems](https://saliu.com/frequency-tables.html)

\* Option 'T = Cross-Checking Lottery Strategies'  
The program writes to disk the lines of specified indexes in a file, usually a strategy file created by STRATEGY\* lottery software. You created the W\*.\* files in the Command Prompt LotWon lottery software. You also generated the statistical reports in 'MDIEditor And Lotto WE'. You then created the strategy file for the stats in 'MDIEditor And Lotto WE'. You want to see the same line numbers in WS\*.\* files for a more comprehensive lotto-6 strategy.

Program name: FileLines.

For more info, read:

-   [Cross-Reference Lottery, Lotto Strategy Files](https://saliu.com/cross-lines.html)

\* Option 'U = Text File Reverser'  
The program reverses the order in text files: the bottom becomes the top. Useful in arranging the lottery data files in the order required by LotWon lotto software. Uncooperative lottery sites publish lottery histories (drawings, results) in an unnatural order: The most recent drawing goes to the bottom, instead of the TOP. LotWon lottery software requires starting with the most recent draw, and go all the way down to the oldest drawing in that lottery game (bottom of file).

Program name: UpDown.

For more info, read:

-   [Software to Reverse Order in Lottery, Lotto Results, Drawings Files](https://saliu.com/bbs/messages/539.html)
-   [The Definitive File Reverser, Shuffler, and Text Viewer Software](https://saliu.com/programming.html).

\* Option 'B = Concatenate Text Files, Make Big File'  
This function takes a number of text (ASCII) files and concatenates them; i.e. it puts all the files into one. The function is useful when you need to combine multiple output lotto files (OUT6) or LIE6 files and work with one files, instead of working with the files step by step (e.g. PURGING output lottery files).

Program name: Internal function in B6.

\* Option 'W = Randomized Lotto Wheels On-the-Fly'  
Generate lotto-6 combinations which assure a minimum guarantee - '5 of 6' to '1 of 6'. You can use it to generate lotto systems (lotto wheels) for 10, 12, 18, 20, 30, etc. numbers. Better still, you should use this program to wheel ALL the numbers in your lotto game.

Functions in this program:  

0 = NO favorite numbers

-   generates randomized lotto wheels, without any favorite numbers;

1 = 1 favorite number

-   generates randomized lotto wheels; also, each and every wheeled random combination will contain one FAVORITE number chosen by the software user;

2 = 2 favorite numbers

-   generates randomized lotto wheels; also, each and every wheeled random combination will contain two FAVORITE numbers chosen by the software user.
    
    Program name: Wheel6.
    

For more info, read:

-   [Lotto Wheels for Lotto Games Drawing 5, 6, or 7 Numbers: Free, Balanced, and Randomized](https://saliu.com/lotto_wheels.html)
-   [Software to Verify Lotto Wheels for Missing Combinations; Generate Reduced Lotto Systems](https://saliu.com/check-wheels.html)
-   [Lotto Wheeling Software, Winning Report Generator](https://saliu.com/bbs/messages/wheel.html).

\* Option 'G = Work with User's Number Groups'  
A big lotto program that works with groups of numbers: odd, even, low, high, frequency numbers, sums or sum-totals.

This program has a plethora of functions and its own Web page.

Program name: UserGroups6.

For more info, read:

-   [Lotto Software for Groups of Numbers: Odd, Even, Low, High, Sums, Frequency](https://saliu.com/lotto-groups.html)

![Menu 3 represents generators of lotto 6 combinations from groups or pools of numbers.](https://saliu.com/ScreenImgs/lotto-b62.gif)

\* Some of these functions represent standalone programs. Other functions belong to older programs, but the names of the functions were less obvious to some software users. For example, several users asked me how to generate lotto combinations from groups or pools of numbers. There are no programs with relevant names. But the functions are well represented in large programs that provide a plethora of functions! In this particular example, the combination generators from pools or groups of numbers belong to the utility software. The older version was Util632. The newer and more potent incarnation is SoftwareLotto6.

\* Option 'N = 6-# Combos from Pools of Numbers'  
Generate 6-number lotto combinations from pools or groups of numbers.

Program name: SoftwareLotto6, option M: Make/Break/Position.

The groups of lottery numbers can be listed in files, in one line or multiple lines. For example, SkipSystem created for you a pool of lotto numbers for the 6/49 game. The file in text format consists of one line of 12 numbers. You want to generate lotto-6 combinations from those 12 numbers. Total of lotto combinations of 12 numbers taken 6 at a time is 924. In SoftwareLotto6, you select option M: Make/Break/Position. Then, option Break, then option 2 = All 6 Numbers Equally. The function will generate your 924 lotto combinations in a matter of seconds.

The same function can also generate lotto combinations from multiple lines of 6+ numbers each. For example, you had 49 lines, for each of your lotto 6/49 game; each line has 10 other lotto numbers, as the best pairs of each of the 49 numbers. Since the lines will have common numbers, your lotto combinations will still be unique. My lottery software takes care also of eliminating duplicate combinations. The Position feature is even more potent.

You can apply in this function a powerful filter: the Least feature. Actually, you will eliminate all pairings in a special file WORST6 created by the same SoftwareLotto6 (option F: Frequency). It is recommended now, in strong terms, to use WORST6 instead of LEAST6.

For more info, read:

-   [**<u>Lottery Skip Systems</u>**](https://saliu.com/skip-strategy.html): _**Lotto, Powerball, Mega Millions, Euromillions**_
-   [Lotto, Lottery Software, Utilities](https://saliu.com/lottery-utility.html)
-   [The Lottery Wonder-Grid Revisited: New Lotto Pairing Research, Software](https://saliu.com/bbs/messages/grid.html)
-   [Analysis of Best Ranges for Lotto Number Frequency, Lottery Pairs, Pairings](https://saliu.com/lottery-lotto-pairs.html).

\* Option 'P = 6-# Combinations by Positions'  
Generate 6-number lotto combinations from 6 lines of numbers representing the 6 positions in a lotto-6 combination.

Program name: SoftwareLotto6, option M: Make/Break/Position.

You can generate lotto combinations based on positions or positional ranges. If you run the statistical functions of my lottery software (plenty of them!) you will see that the lotto numbers are strongly biased regarding the position. You can read at SALIU.COM a lot about ranges or positional ranges in lotto. Just search. You will see living proof that the lotto numbers follow the Fundamental Formula of Gambling (FFG). Each position has lotto numbers based on the FFG median. Just look at the new Powerball game format (started in January 2009). In 36 drawings, only 19 out of the 59 Powerball regular numbers have come out in the 1st position. The numbers 2 and 7 came out 5 times apiece; the Powerball regular numbers 1,3,5,6, 8 came out 3 times each. Meanwhile, numbers 24 to 59 have not hit yet. For the 5th position, only 18 numbers have hit, especially in the range 50 to 59. You can use the same option M: Make/Break/Position in SoftwareLotto6 software, but select Position/Ranges.

You can apply in this function a powerful filter: the Least feature. Actually, you will eliminate all pairings in a special file WORST6 created by the same SoftwareLotto6 (option F: Frequency). It is recommended now, in strong terms, to use WORST6 instead of LEAST6.

For more info, read:

-   [Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies](https://saliu.com/Newsgroups.htm)
-   [**<u>Lottery Skip Systems</u>**](https://saliu.com/skip-strategy.html): _**Lotto, Powerball, Mega Millions, Euromillions**_
-   [Lotto, Lottery Software, Utilities](https://saliu.com/lottery-utility.html)
-   [Lottery Wonder-Grid Revisited: New Lotto Pairing Research, Software](https://saliu.com/bbs/messages/grid.html)
-   [Analysis of Best Ranges for Lotto Number Frequency, Lottery Pairs, Pairings](https://saliu.com/lottery-lotto-pairs.html).

\* Option 'F = 6# Combos from 3 Frequency Groups'  
Generate 6-number lotto combinations based on frequency: "hot" digits, "mild" digits, and "cold" numbers. Run first the Frequency Reporting module, and then the combination generator (lexicographic or random).

Program name: SkipDecaFreq6, options: L = Combinations, Lexico; R = Random Combinations. Then, select the screen pertaining to the filters based on the 3 lotto frequency groups.

For more info, read:

-   [Lottery Strategy, Systems Based on Number Frequency](https://saliu.com/frequency-lottery.html).

\* Option 'D = 6-Number Combinations by Lotto Decades'  
Generate 6-number lotto combinations decade by decade; e.g. a 6/49 lotto game has 5 decades, from single digits to numbers beginning at 40.

Program name: SkipDecaFreq6, options: L = Combinations, Lexico; R = Random Combinations. Then, select the screen pertaining to the filters based on the lotto decades.

For more info, read:

-   [Lotto Decades: Software, Reports, Analysis, Strategies](https://saliu.com/decades.html).

\* Option 'B = Break Down Lines of 6+ Numbers'  
Takes one or more lines consisting of numbers and breaks down each line into smaller number-groups: from 1-number groups to groups of 7 numbers per combination (line). For example, a line with 6 or more numbers can be broken into unique (no-repeat): single numbers, 2-number groups (pairs), 3-number groups (triples), 4-number groups (quads), 5-number groups (quintets), and 6-number groups (sextets).

Program name: BreakDownNumbers, option: 6 = Groups of 6 numbers per combonation.

For more info, read:

-   [Lotto Strategy, Software: 12-numbers Combinations Wheeled to 6-number Lotto Games](https://saliu.com/12-number-lotto-combinations.html).

\* Option 'R = Generate BRIGHT-12 Report'  
Generate a special report for lotto-6 regarding the SKIPS of the subgroups of numbers: Singles (Ones), Pairs, Triples, Quads, Quintets, Sextet.

Program name: LottoGroupSkips6.

For more info, read:

-   [Lotto Strategy, Software: 12-numbers Combinations Wheeled to 6-number Lotto Games](https://saliu.com/12-number-lotto-combinations.html).

\* Option 'T = Generate 12-Number Combinations'  
Generate random combinations for lotto-6, but 12 numbers per combination. Both the minimum and the maximum levels of the following filters are applicable in the latest version of a tough program: ONES, PAIRS, TRIPLES, QUADS, QUINTETS, and SEXTET. High values for the minimum levels of the filters will visibly slow down the combination generating processes.

Program name: Combine12.

For more info, read:

-   [Lotto Strategy, Software: 12-numbers Combinations Wheeled to 6-number Lotto Games](https://saliu.com/12-number-lotto-combinations.html).

\* Option '7 = 7x7 Clusters for 6/49 Lotto'  
Generate random combinations for 6/49 lotto, but 7-by-7 clusters, like Shuffle in Combine6; data must still be in the 6-numbers-per-line format as in the Bright6 programs. The following filters are applicable in this type of software: PAIRS, TRIPLES, QUADS, QUINTETS, SEXTET. The FILTERS for this app are reported by LottoGroupSkips6.

Program name: Cluster49-7.

For more info, read:

-   [Lotto Software: 7-by-7-Number Matrices (Perfect Squares) in Lotto 6 / 49 Games](https://saliu.com/7by7-lotto-6-49.html).

\* Option 'L = Purge Numbers from LIE Files'  
This purge function is also very useful for a little known REVERSED strategy; i.e. in conjunction with LIE files. A LIE file is predicted to lose or to have NO HITS next lottery drawing (or a few drawings in the near future). For example, a 1000-line LIE file has NO 4 winners; you would set the QUAD filter to 1000. But first you need to press Y (for yes) to enable the specific LIE function when the prompt appears on screen.

Program names: Combine6, Combine6-12, Combine12, Lexico6, Wheel6, SkipDecaFreq6.

For more info, read:

-   [Lotto Strategy, Software: 10-numbers Combinations Wheeled to 5-number Lotto Games](https://saliu.com/lotto-10-5-combinations.html)
-   [Lotto, Lottery Strategy in Reverse: Not-to-Win Leads to Not-to-Lose](https://saliu.com/reverse-strategy.html).

![Menu 4: Software generates all types of lotto combinations, including all 6/49 games.](https://saliu.com/ScreenImgs/lotto-b63.gif)

\* Some of the programs in this menu represent GENERAL-PURPOSE lottery and gambling software.

\* Option 'P = Generate All Possible Type of Sets'  
This software generates ALL possible types of sets: Exponents, permutations, arrangements, combinations - and Powerball, Mega Millions, Euromillions combinations. The software generates the sets in lexicographical order or randomly. The sets can be numerical or be composed of words.  
An example of exponents (N=3, M=3): 111,112,113,121,122,123,131,132, etc.  
An example of permutations (N=3): 123, 132, 213, 231, 312, 321.  
An example of arrangements (N=3, M=2): 12, 13, 21, 23, 31, 32.  
An example of combinations (N=3, M=2): 12, 13, 23.

Program name: PermuteCombine.

For more info, read:

-   [Combinatorics: Permutations, Combinations, Factorial, Exponents Generate](https://saliu.com/permutations.html)
-   [Comprehensive Generating: Exponents, Permutations, Arrangements, Combinations, Powerball, Mega Millions, Euromillions, Horseracing](https://saliu.com/forum/numbers-words.html).

\* Option 'L = Software for Lexicographic Order'  
The program finds the lexicographical order (index or rank) of a given set and conversely finds the set for a specified index (rank, or numeral, or lexicographic order). Applicable to these set types: Exponents, permutations, arrangements, combinations, Powerball (5+1), and Euromillions (5+2) combinations.

Program name: LexicographicSets.

For more info, read:

-   [Lexicographical Order: Index, Rank, Algorithms, Combinations, Permutations](https://saliu.com/lexicographic.html)
-   [Lexicographical Order: Lotto, Powerball, Mega Millions, Euromillions](https://saliu.com/forum/lexicographical.html).

\* Option 'B = Generate Combinations inside FFG Median Bell'  
This software generates combinations in the FFG median zone and inside the bell (Gauss) curve. The program can be used for: pick-3 4 lotteries, horse racing, lotto-5, -6, -7, Powerball, Mega Millions '5+1', Euromillions '5+2', roulette, sports betting, and soccer pools.

Program name: BellCurveGenerator.

For more info, read:

-   [Median Bell Random Number, Combination Generator](https://saliu.com/median_bell.html)
-   [Winning Combinations Come Predominantly from Inside the FFG Median Bell](https://saliu.com/random-picks.html).

\* Option 'F = Lexico-Index File'  
This program takes a lotto data file (drawings) and adds indexes to the corresponding combinations in the file. The indexes are calculated based on the combination lexicographical order or index for that lotto game.

Program name: DrawIndex.

For more info, read:

-   [Combination Lexicographical Order, Index of Lotto, Lottery Drawings Files](https://saliu.com/combination.html)
-   [Combinations Generator: Any Lotto, Powerball, Mega Millions, Euromillions, Horseracing, Two-In-One Lotto Games](https://saliu.com/combinations.html).

\* Option 'O = Probability, Odds Calculator'  
The probability software calculates all the ODDS of any lotto game, including Powerball/Mega Millions and Euromillions games. For example, the odds of a lotto-49 game drawing 6 numbers: '0 of 6'; '1 of 6'; '2 of 6'; '3 of 6'; '4 of 6'; '5 of 6'; '6 of 6'. The probability is calculated as EXACTLY and AT LEAST 'M of N'.

The 'Generalized' option calculates the odds for any two-in-one lotto games, including Powerball, Mega Millions, and Euromillions. The 'Horseracing' option calculates the odds for exactas (top 2 finishers), trifectas (top 3 finishers), superfectas (top 4 finishers), etc. The horse racing odds are calculated also as STRAIGHT and BOXED.

Program name: OddsCalc.

For more info, read:

-   [Calculate Odds, Probability to Win Lottery, Lotto, Powerball, Mega Millions, Euromillions](https://saliu.com/oddslotto.html)
-   [Probability, Odds, Formulae, Algorithm, Software Calculator](https://saliu.com/bbs/messages/266.html).

\* Option 'V = Universal Lotto Combination Generator'  
Lotto software generates combinations for absolutely any type of lotto game, plus horseracing straight sets. Specifically to this program, the combinations can be generated in steps. That is, the user has the choice to generate lotto combinations with constant gaps or skips between them. For example, starting at the very top of a combination set (the lexicographical order #1), then step 90, the following combination generated will have lexicographic order #91,…, and so on, to the very last combination in the lotto set.

Most certainly, no other lotto program can generate lotto combinations in steps. Furthermore, this incredible lotto software even generates lotto combinations within a range of numbers, or between any lexicographic order indexes (ranks).

Program name: Combinations.

For more info, read:

-   [Combinations Generator: Any Lotto, Powerball, Mega Millions, Euromillions, Horseracing, Two-In-One Lotto Games](https://saliu.com/combinations.html).

\* Option 'S = The Definitive Probability, Statistics, and Gambling Software'  
SuperFormula is the definitive software for statistics, probability, odds, gambling mathematics ... and much more. The functions are grouped in 12 categories. Each software category has its own detailed sub-categories. This unique application grew from the request by many people to create software to automate the calculations in the Fundamental Formula of Gambling (FFG). FFG discovered the most fundamental elements of theory of probability and also the Universe: The relation between the degree of certainty (DC), probability p, and number of trials N.

Program name: SuperFormula.

For more info, read:

-   [Formula Software for Statistics, Mathematics, Probability, Gambling](https://saliu.com/formula.html).

\* Option 'G = Wonder-Grid Checking for Lotto-6 Pairings'  
The lotto program checks the past performance of the GRID5 files. The program starts a number of draws back in the DATA-6 lotto file and creates 3 GRID5 files: for (N / 2), N, and (N \* 2) draws. Each range of analysis N creates its own report file (ChkGrid6.\*).

It can be used well in conjunction with the LIE (reversed lotto strategy) feature in the combination generators. The wonder-grid skips more lotto drawings compared to the winning situations.

Program name: GridCheck6.

For more info, read:

-   [Lottery Wonder-Grid Revisited: New Lotto Pairing Research, Software](https://saliu.com/bbs/messages/grid.html)
-   [Lotto Wonder-grid: Lottery Reports and Lotto Software](https://saliu.com/bbs/messages/9.html).

\* Option 'K = Generate the Lotto Pairing Reports'  
The program shows the lotto-6 draws as strings of skips, or shows the lotto-6 draws as strings of pairings, or decades. The 'pairing' routine can also generate GRID5 files, manually and from text files, for any number of pairings.

This old piece of software is even better than GridCheck to be used in conjunction with the LIE (reversed lotto strategy) feature in the combination generators.

Program name: GridRange6.

For more info, read:

-   [Lottery Pairs and Repeat Probability](https://saliu.com/forum/lottery-pairs.html)

\* Option 'W = Lotto Wheeling Software: Fill Out Lottery Wheels with Player's Lotto Picks'  
The ultimate lotto wheeling program takes a source wheel file and replaces the 'system numbers' with user's lotto picks to a destination file.

Program name: LottoWheeler.

For more info, read:

-   [Lotto Wheeling Software: Fill Out Lottery Wheels with Player's Lotto Picks](https://saliu.com/bbs/messages/857.html)

\* Option 'E = Lotto Wheels Based on Odds and Lexicographic Order'  
The special lotto wheeling software generates lotto wheels based on the lotto odds. The user selects first the numbers in the lotto game; e.g. 49, then 6. Next, the user selects the 'GUARANTEE'; e.g. 4 to indicate a guarantee such as '4 of 5', or '4 of 6', or '4 of 7', etc. Finally, you select the START lexicographic index; the program automatically calculates the lowest index and the highest lexicographic index possible.

Program name: LexicoWheels.

For more info, read:

-   [Wheels, Balanced Lotto Wheels, Lexicographic Order Index](https://saliu.com/bbs/messages/772.html).

\* Option 'C = Check Abbreviated Lotto Systems or Wheels'  
The special lotto wheeling software verifies the lotto wheels for missing combinations; if missing, the software will plug-in the lines needed to complete the lotto wheel. Also, the program generates original abbreviated lotto systems.

Program name: WheelCheck6.

For more info, read:

-   [Software to Verify Lotto Wheels for Missing Combinations; Generate Reduced Lotto Systems](https://saliu.com/check-wheels.html).

\* Option 'H = Play-Last-N-Draws as a Lotto Wheel'  
The lottery utilities check for winning numbers in files of real drawings. A data file will be checked against itself as if playing the last N draws before current draw. For example, check the wins when I play the last 57 draws in a 6/49 lotto game; the odds of '3 in 6' are 57 to 1; how many '3 of 6' hits and other hits in 100 lottery drawings?

Program name: CheckHits.

For more info, read:

-   [Wheeling All Lotto Numbers Formula: Play Last N Lottery Draws](https://saliu.com/wheel.html).

\* Option 'U = The Old Lotto-6 Utilities'  
This piece of software was superseded by Super Utilities (option S in the Main Menu). Just nostalgia, I guess!

Program name: Util632.

For more info, read:

-   [Lotto, Lottery Software, Utilities](https://saliu.com/lottery-utility.html).

![6-number 1 to 49 59 lotto software requires a one-time payment to download all applications, apps.](https://saliu.com/HLINE.gif)

Closely related pages at this Web site:-   **BRIGHTh3**: _**High-Powered Integrated**_ [**Horse Racing Software**](https://saliu.com/horseracing-software.html)
-   **BRIGHT5**: _**High-Powered**_ [**Software for 5-Number Lotto**](https://saliu.com/lotto5-software.html)
-   **BRIGHT3**: _**High-Powered**_ [**Software for 3-Digit Lottery**](https://saliu.com/lottery3-software.html)
-   **BRIGHT4**: _**High-Powered**_ [_**Software for 4-Digit Lotteries**_](https://saliu.com/lottery4-software.html).

## [<u>Principal Resources in Lottery, Software, Strategy, Systems, Lotto Wheeling</u>](https://saliu.com/content/lottery.html)

![Bright-6 lotto software consists of 4 big categories, each full of lottery software pieces.](https://saliu.com/HLINE.gif)

**[Home](https://saliu.com/index.htm) | [Search](https://saliu.com/Search.htm) | [New Writings](https://saliu.com/bbs/index.html) | [Fundamental Formula](https://saliu.com/formula.htm) | [Odds, Generator](https://saliu.com/calculator_generator.html) | [Contents](https://saliu.com/content/index.html) | [Forums](https://forums.saliu.com/) | [Sitemap](https://saliu.com/sitemap/index.html)**

![The best winning lotto 6 software for pick-6 games.](https://saliu.com/HLINE.gif)
