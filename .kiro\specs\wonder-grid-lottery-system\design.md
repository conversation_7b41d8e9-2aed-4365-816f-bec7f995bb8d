# Design Document

## Overview

The Wonder Grid Lottery System is a Julia-based implementation of <PERSON>'s mathematical lottery strategy specifically adapted for Pick 5 from 39 (Lotto 5/39) games. The system leverages statistical analysis of historical lottery data to identify optimal number combinations based on pairing frequencies and FFG (Fundamental Formula of Gambling) principles.

The architecture follows a modular design with clear separation of concerns, enabling efficient data processing, statistical analysis, strategy implementation, and result evaluation. The system is designed to handle large datasets (100,000+ lottery draws) while maintaining performance and providing comprehensive analytical capabilities.

## Architecture

### High-Level Architecture

The system follows a layered architecture pattern with the following main components:

```
┌─────────────────────────────────────────────────────────────┐
│                    User Interface Layer                     │
├─────────────────────────────────────────────────────────────┤
│                    Strategy Layer                           │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
│  │ Wonder Grid     │  │ LIE Elimination │  │ Backtesting │ │
│  │ Engine          │  │ Engine          │  │ Engine      │ │
│  └─────────────────┘  └─────────────────┘  └─────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                    Analysis Layer                           │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
│  │ FFG Calculator  │  │ Pairing Engine  │  │ Skip        │ │
│  │                 │  │                 │  │ Analyzer    │ │
│  └─────────────────┘  └─────────────────┘  └─────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                    Data Processing Layer                    │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
│  │ Data Validator  │  │ File Manager    │  │ Statistics  │ │
│  │                 │  │                 │  │ Engine      │ │
│  └─────────────────┘  └─────────────────┘  └─────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                    Data Storage Layer                       │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
│  │ Historical Data │  │ Simulation Data │  │ Results     │ │
│  │ Store           │  │ Store           │  │ Cache       │ │
│  └─────────────────┘  └─────────────────┘  └─────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### Core Architecture Principles

1. **Separation of Concerns**: Each layer has distinct responsibilities
2. **Modularity**: Components can be developed and tested independently
3. **Scalability**: Architecture supports processing large datasets (100,000+ draws)
4. **Extensibility**: New strategies and analysis methods can be added easily
5. **Performance**: Optimized for mathematical computations and data processing
#
# Components and Interfaces

### Data Processing Layer

#### DataValidator
**Purpose**: Validates and sanitizes lottery data input
**Key Functions**:
- Validates DATA5 format (exactly 5 numbers per line, range 1-39)
- Ensures chronological ordering (newest draws first)
- Detects and reports format inconsistencies
- Validates file integrity and completeness

```julia
struct DataValidator
    min_number::Int = 1
    max_number::Int = 39
    numbers_per_draw::Int = 5
end

function validate_data5_file(validator::DataValidator, filepath::String) -> ValidationResult
function validate_draw_format(validator::DataValidator, draw::Vector{Int}) -> Bool
function check_chronological_order(validator::DataValidator, draws::Vector{Vector{Int}}) -> Bool
```

#### FileManager
**Purpose**: Manages lottery data files and formats
**Key Functions**:
- Reads/writes DATA5, SIM-5, and D5 files
- Merges real and simulated data
- Handles large file operations efficiently
- Maintains file format consistency

```julia
struct FileManager
    data_directory::String
    max_file_size::Int
end

function read_data5_file(fm::FileManager, filepath::String) -> Vector{Vector{Int}}
function write_data5_file(fm::FileManager, filepath::String, draws::Vector{Vector{Int}})
function merge_data_files(fm::FileManager, real_file::String, sim_file::String) -> String
```

#### StatisticsEngine
**Purpose**: Core statistical calculations and analysis
**Key Functions**:
- Calculates frequency distributions
- Computes statistical measures (mean, median, variance)
- Generates probability distributions
- Performs correlation analysis

```julia
struct StatisticsEngine
    precision::Float64 = 1e-10
end

function calculate_frequency_distribution(engine::StatisticsEngine, data::Vector{Vector{Int}}) -> Dict{Int, Int}
function compute_statistical_summary(engine::StatisticsEngine, frequencies::Dict{Int, Int}) -> StatisticalSummary
```

### Analysis Layer

#### FFGCalculator
**Purpose**: Implements Fundamental Formula of Gambling calculations
**Key Functions**:
- Calculates FFG median values for each number
- Computes degree of certainty (DC) at various levels
- Provides probability calculations for skip analysis
- Supports dynamic recalculation as data updates

```julia
struct FFGCalculator
    degree_of_certainty::Float64 = 0.5  # 50% DC
    game_type::GameType = Lotto5_39()
end

function calculate_ffg_median(calc::FFGCalculator, number::Int, historical_data::Vector{Vector{Int}}) -> Float64
function compute_skip_probability(calc::FFGCalculator, current_skip::Int, median_skip::Float64) -> Float64
function is_favorable_timing(calc::FFGCalculator, number::Int, current_skip::Int) -> Bool
```

#### SkipAnalyzer
**Purpose**: Analyzes skip patterns for lottery numbers
**Key Functions**:
- Tracks skip sequences for each number
- Calculates current skip values
- Generates skip charts and visualizations
- Identifies optimal timing for key number selection

```julia
struct SkipAnalyzer
    historical_data::Vector{Vector{Int}}
    skip_cache::Dict{Int, Vector{Int}}
end

function calculate_skips(analyzer::SkipAnalyzer, number::Int) -> Vector{Int}
function get_current_skip(analyzer::SkipAnalyzer, number::Int) -> Int
function generate_skip_chart(analyzer::SkipAnalyzer, number::Int) -> SkipChart
function update_skips(analyzer::SkipAnalyzer, new_draw::Vector{Int})
```

#### PairingEngine
**Purpose**: Calculates and analyzes number pairing frequencies
**Key Functions**:
- Computes pairing frequencies for all number combinations
- Identifies top percentage pairings (10%, 25%, 50%)
- Generates wonder-grid files
- Supports dynamic pairing updates

```julia
struct PairingEngine
    historical_data::Vector{Vector{Int}}
    pairing_cache::Dict{Tuple{Int,Int}, Int}
end

function calculate_all_pairings(engine::PairingEngine) -> Dict{Tuple{Int,Int}, Int}
function get_top_pairings(engine::PairingEngine, key_number::Int, percentage::Float64) -> Vector{Int}
function generate_wonder_grid(engine::PairingEngine) -> WonderGrid
function update_pairings(engine::PairingEngine, new_draw::Vector{Int})
```

### Strategy Layer

#### WonderGridEngine
**Purpose**: Core implementation of Wonder Grid strategy
**Key Functions**:
- Selects optimal key numbers based on FFG analysis
- Generates combinations using top 25% pairings
- Calculates C(10,4) = 210 combinations per key number
- Ensures key number inclusion in all combinations

```julia
struct WonderGridEngine
    ffg_calculator::FFGCalculator
    skip_analyzer::SkipAnalyzer
    pairing_engine::PairingEngine
end

function select_key_numbers(engine::WonderGridEngine) -> Vector{Int}
function generate_combinations(engine::WonderGridEngine, key_number::Int) -> Vector{Vector{Int}}
function execute_strategy(engine::WonderGridEngine, key_number::Int) -> StrategyResult
```

#### LIEEliminationEngine
**Purpose**: Implements Loss Into Elimination strategy
**Key Functions**:
- Generates combinations expected not to win short-term
- Creates elimination filters from least frequent patterns
- Reduces combination sets through strategic elimination
- Integrates with Wonder Grid for cost optimization

```julia
struct LIEEliminationEngine
    pairing_engine::PairingEngine
    elimination_threshold::Float64 = 0.1  # Bottom 10%
end

function generate_lie_combinations(engine::LIEEliminationEngine, key_number::Int) -> Vector{Vector{Int}}
function apply_elimination_filter(engine::LIEEliminationEngine, combinations::Vector{Vector{Int}}) -> Vector{Vector{Int}}
function calculate_cost_savings(engine::LIEEliminationEngine, original_count::Int, filtered_count::Int) -> Float64
```

#### BacktestingEngine
**Purpose**: Evaluates strategy performance against historical data
**Key Functions**:
- Tests strategy performance over specified date ranges
- Calculates hit rates for different prize tiers
- Compares efficiency against random play
- Generates performance reports and statistics

```julia
struct BacktestingEngine
    historical_data::Vector{Vector{Int}}
    test_period::DateRange
end

function run_backtest(engine::BacktestingEngine, strategy::WonderGridStrategy, period::DateRange) -> BacktestResult
function calculate_hit_rates(engine::BacktestingEngine, combinations::Vector{Vector{Int}}, actual_draws::Vector{Vector{Int}}) -> HitRates
function compare_to_random(engine::BacktestingEngine, strategy_results::BacktestResult) -> EfficiencyComparison
```

## Data Models

### Core Data Structures

```julia
# Basic lottery draw representation
struct LotteryDraw
    numbers::Vector{Int}
    draw_date::Date
    draw_id::Int
end

# Wonder Grid configuration
struct WonderGridConfig
    key_number::Int
    top_pairings::Vector{Int}
    combination_count::Int
    ffg_median::Float64
    current_skip::Int
end

# Pairing frequency data
struct PairingFrequency
    pair::Tuple{Int,Int}
    frequency::Int
    percentage::Float64
    rank::Int
end

# Strategy execution result
struct StrategyResult
    key_number::Int
    combinations::Vector{Vector{Int}}
    generation_time::Float64
    estimated_cost::Float64
    expected_efficiency::Dict{String, Float64}
end

# Statistical analysis results
struct StatisticalSummary
    total_draws::Int
    frequency_distribution::Dict{Int, Int}
    mean_frequency::Float64
    median_frequency::Float64
    standard_deviation::Float64
end

# Skip analysis data
struct SkipChart
    number::Int
    skip_sequence::Vector{Int}
    current_skip::Int
    ffg_median::Float64
    is_favorable::Bool
end

# Backtesting results
struct BacktestResult
    strategy_name::String
    test_period::DateRange
    total_combinations::Int
    hit_rates::Dict{String, Float64}  # "3/5", "4/5", "5/5"
    efficiency_ratios::Dict{String, Float64}
    cost_analysis::CostAnalysis
end
```

### File Format Specifications

```julia
# DATA5 file format
struct DATA5Format
    numbers_per_line::Int = 5
    number_range::UnitRange{Int} = 1:39
    separator::String = " "
    line_ending::String = "\n"
    chronological_order::Bool = true  # newest first
end

# Wonder Grid file format
struct WonderGridFormat
    lines_per_number::Int = 39
    pairings_per_line::Int = 10  # top 25% ≈ 10 pairings
    format::String = "number: pairing1 pairing2 ... pairing10"
end
```

## Error Handling

### Error Types and Recovery Strategies

```julia
# Custom exception types
struct InvalidDataFormatError <: Exception
    message::String
    line_number::Int
    expected_format::String
end

struct InsufficientDataError <: Exception
    required_draws::Int
    available_draws::Int
    analysis_type::String
end

struct CalculationError <: Exception
    operation::String
    parameters::Dict{String, Any}
    underlying_error::Exception
end

# Error handling strategies
function handle_data_validation_error(error::InvalidDataFormatError)
    # Log error details
    # Provide user-friendly error message
    # Suggest corrective actions
    # Return validation result with error details
end

function handle_insufficient_data_error(error::InsufficientDataError)
    # Calculate minimum required data
    # Suggest data augmentation strategies
    # Offer reduced functionality options
    # Guide user to data sources
end

function handle_calculation_error(error::CalculationError)
    # Log technical details
    # Provide fallback calculations
    # Suggest parameter adjustments
    # Return partial results if possible
end
```

### Validation and Safety Measures

```julia
# Input validation
function validate_lottery_numbers(numbers::Vector{Int}) -> ValidationResult
    if length(numbers) != 5
        return ValidationResult(false, "Must contain exactly 5 numbers")
    end
    
    if !all(n -> 1 <= n <= 39, numbers)
        return ValidationResult(false, "Numbers must be between 1 and 39")
    end
    
    if length(unique(numbers)) != 5
        return ValidationResult(false, "Numbers must be unique")
    end
    
    return ValidationResult(true, "Valid")
end

# Data integrity checks
function verify_data_integrity(data::Vector{Vector{Int}}) -> IntegrityReport
    # Check for duplicates
    # Verify chronological order
    # Validate number ranges
    # Report anomalies
end

# Performance monitoring
function monitor_performance(operation::String, execution_time::Float64, memory_usage::Int)
    # Log performance metrics
    # Alert on performance degradation
    # Suggest optimizations
end
```

## Testing Strategy

### Unit Testing Framework

```julia
using Test

# Test data validation
@testset "Data Validation Tests" begin
    @testset "Valid DATA5 Format" begin
        valid_draw = [1, 15, 23, 31, 39]
        @test validate_lottery_numbers(valid_draw).is_valid == true
    end
    
    @testset "Invalid DATA5 Format" begin
        invalid_draw = [1, 15, 23, 31, 40]  # 40 is out of range
        @test validate_lottery_numbers(invalid_draw).is_valid == false
    end
end

# Test FFG calculations
@testset "FFG Calculator Tests" begin
    @testset "Median Calculation" begin
        calc = FFGCalculator()
        test_data = generate_test_data(1000)
        median = calculate_ffg_median(calc, 15, test_data)
        @test median > 0
        @test median < 100  # Reasonable range for Lotto 5/39
    end
end

# Test pairing analysis
@testset "Pairing Engine Tests" begin
    @testset "Pairing Frequency Calculation" begin
        engine = PairingEngine(generate_test_data(1000))
        pairings = calculate_all_pairings(engine)
        @test length(pairings) == binomial(39, 2)  # All possible pairs
    end
    
    @testset "Top Pairings Selection" begin
        engine = PairingEngine(generate_test_data(1000))
        top_pairings = get_top_pairings(engine, 15, 0.25)
        @test length(top_pairings) ≈ 10  # 25% of 38 other numbers
    end
end
```

### Integration Testing

```julia
@testset "Integration Tests" begin
    @testset "End-to-End Wonder Grid Strategy" begin
        # Load test data
        test_data = load_test_data("test_data5.txt")
        
        # Initialize engines
        wonder_grid = WonderGridEngine(
            FFGCalculator(),
            SkipAnalyzer(test_data),
            PairingEngine(test_data)
        )
        
        # Execute strategy
        key_numbers = select_key_numbers(wonder_grid)
        @test length(key_numbers) > 0
        
        combinations = generate_combinations(wonder_grid, key_numbers[1])
        @test length(combinations) == 210  # C(10,4)
        
        # Verify all combinations contain key number
        @test all(combo -> key_numbers[1] in combo, combinations)
    end
end
```

### Performance Testing

```julia
@testset "Performance Tests" begin
    @testset "Large Dataset Processing" begin
        large_dataset = generate_test_data(100_000)
        
        # Test data processing performance
        @time begin
            engine = PairingEngine(large_dataset)
            pairings = calculate_all_pairings(engine)
        end
        
        # Should complete within reasonable time (< 30 seconds)
        @test @elapsed(calculate_all_pairings(engine)) < 30.0
    end
    
    @testset "Memory Usage" begin
        # Monitor memory usage during large operations
        initial_memory = Base.gc_bytes()
        
        large_dataset = generate_test_data(100_000)
        engine = PairingEngine(large_dataset)
        calculate_all_pairings(engine)
        
        final_memory = Base.gc_bytes()
        memory_used = final_memory - initial_memory
        
        # Should not exceed reasonable memory limits
        @test memory_used < 1_000_000_000  # 1GB limit
    end
end
```

### Test Data Generation

```julia
function generate_test_data(num_draws::Int) -> Vector{Vector{Int}}
    """Generate realistic test data for Lotto 5/39"""
    draws = Vector{Vector{Int}}()
    
    for i in 1:num_draws
        # Generate 5 unique numbers between 1 and 39
        draw = sort(sample(1:39, 5, replace=false))
        push!(draws, draw)
    end
    
    return draws
end

function load_test_data(filename::String) -> Vector{Vector{Int}}
    """Load test data from file"""
    # Implementation for loading test data
end

function create_mock_historical_data() -> Vector{Vector{Int}}
    """Create mock historical data with known patterns for testing"""
    # Implementation for creating predictable test data
end
```

This completes the design document with comprehensive technical specifications for implementing the Wonder Grid Lottery System for Pick 5 from 39 games. The design covers all architectural layers, data models, error handling, and testing strategies needed for a robust implementation.