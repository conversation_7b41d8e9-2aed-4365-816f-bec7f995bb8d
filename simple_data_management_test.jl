# Simple Data Management Test
# Tests core data management functionality without file cleanup issues

using Test
using Dates

# Include necessary types and functions
include("src/types.jl")
include("src/file_management.jl")
include("src/validation.jl")

"""
Test data sorting (newest first) functionality
"""
function test_data_sorting()
    println("=== Data Sorting Verification ===")
    
    # Create test data with mixed dates
    test_data = [
        "2022-01-03,1,5,10,15,20",
        "2022-01-01,2,6,11,16,21", 
        "2022-01-05,3,7,12,17,22",
        "2022-01-02,4,8,13,18,23",
        "2022-01-04,5,9,14,19,24"
    ]
    
    # Write test file
    test_file = "temp_sort_test.csv"
    open(test_file, "w") do f
        for line in test_data
            println(f, line)
        end
    end
    
    # Read file using FileManager
    fm = FileManager()
    draws = read_data5_file(fm, test_file)
    
    # Verify sorting (newest first)
    expected_order = [
        Date(2022, 1, 5),
        Date(2022, 1, 4), 
        Date(2022, 1, 3),
        Date(2022, 1, 2),
        Date(2022, 1, 1)
    ]
    
    actual_order = [draw.draw_date for draw in draws]
    
    @test actual_order == expected_order
    println("✓ Data sorted correctly (newest first)")
    
    # Verify data integrity
    @test length(draws) == 5
    @test all(length(draw.numbers) == 5 for draw in draws)
    println("✓ Data integrity maintained during sorting")
    
    return true
end

"""
Test error handling for validation
"""
function test_validation_error_handling()
    println("\n=== Validation Error Handling ===")
    
    validator = DataValidator()
    
    # Test 1: Non-existent file
    result = validate_data5_file(validator, "non_existent_file.csv")
    @test !result.is_valid
    @test occursin("does not exist", result.message)
    println("✓ Non-existent file error handled correctly")
    
    # Test 2: Invalid lottery numbers
    invalid_numbers = [0, 5, 10, 15, 20]  # 0 is invalid
    result = validate_lottery_numbers(invalid_numbers)
    @test !result.is_valid
    @test occursin("between 1 and 39", result.message)
    println("✓ Invalid number range error handled correctly")
    
    # Test 3: Duplicate numbers
    duplicate_numbers = [1, 1, 10, 15, 20]  # Duplicate 1
    result = validate_lottery_numbers(duplicate_numbers)
    @test !result.is_valid
    @test occursin("unique", result.message)
    println("✓ Duplicate numbers error handled correctly")
    
    # Test 4: Wrong number count
    wrong_count = [1, 5, 10, 15]  # Only 4 numbers
    result = validate_lottery_numbers(wrong_count)
    @test !result.is_valid
    @test occursin("exactly 5", result.message)
    println("✓ Wrong number count error handled correctly")
    
    return true
end

"""
Test chronological order checking
"""
function test_chronological_order()
    println("\n=== Chronological Order Verification ===")
    
    validator = DataValidator()
    
    # Test 1: Correct order (newest first)
    correct_draws = [
        LotteryDraw([1, 5, 10, 15, 20], Date(2022, 1, 3), 1),
        LotteryDraw([2, 6, 11, 16, 21], Date(2022, 1, 2), 2),
        LotteryDraw([3, 7, 12, 17, 22], Date(2022, 1, 1), 3)
    ]
    
    @test check_chronological_order(validator, correct_draws)
    println("✓ Correct chronological order detected")
    
    # Test 2: Incorrect order (oldest first)
    incorrect_draws = [
        LotteryDraw([1, 5, 10, 15, 20], Date(2022, 1, 1), 1),
        LotteryDraw([2, 6, 11, 16, 21], Date(2022, 1, 2), 2),
        LotteryDraw([3, 7, 12, 17, 22], Date(2022, 1, 3), 3)
    ]
    
    @test !check_chronological_order(validator, incorrect_draws)
    println("✓ Incorrect chronological order detected")
    
    return true
end

"""
Test file format support
"""
function test_file_format_support()
    println("\n=== File Format Support ===")
    
    fm = FileManager()
    
    # Test numbers-only format
    numbers_only_data = [
        "1,5,10,15,20",
        "2,6,11,16,21",
        "3,7,12,17,22"
    ]
    
    numbers_file = "temp_numbers_test.csv"
    open(numbers_file, "w") do f
        for line in numbers_only_data
            println(f, line)
        end
    end
    
    draws = read_data5_file(fm, numbers_file)
    @test length(draws) == 3
    @test all(isa(draw.draw_date, Date) for draw in draws)  # Dates should be generated
    println("✓ Numbers-only format supported")
    
    return true
end

"""
Run simplified data management tests
"""
function run_simple_data_management_tests()
    println("Starting Simple Data Management Tests...")
    
    try
        test_data_sorting()
        test_validation_error_handling()
        test_chronological_order()
        test_file_format_support()
        
        println("\n🎉 All data management core functionality verified!")
        return true
    catch e
        println("\n❌ Data management tests failed: $e")
        return false
    end
end

# Run tests if this file is executed directly
if abspath(PROGRAM_FILE) == @__FILE__
    run_simple_data_management_tests()
end
