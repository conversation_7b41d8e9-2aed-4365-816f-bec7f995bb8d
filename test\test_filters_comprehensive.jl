# Comprehensive Filter Tests
# 過濾器綜合測試 - 全面測試所有過濾器的功能和性能

using Test
using Dates
using Statistics
using Random

# 引入必要的模組
include("../src/types.jl")
include("../src/filter_engine.jl")
include("../src/filters/one_filter.jl")
include("../src/filters/two_filter.jl")
include("../src/filters/three_filter.jl")
include("../src/filters/four_filter.jl")
include("../src/filters/five_filter.jl")
include("test_data_manager.jl")
include("test_configuration.jl")

# 簡單的採樣函數
function simple_sample(range::UnitRange{Int}, n::Int)::Vector{Int}
    available = collect(range)
    result = Int[]
    for _ in 1:n
        if isempty(available)
            break
        end
        idx = rand(1:length(available))
        push!(result, available[idx])
        deleteat!(available, idx)
    end
    return result
end

"""
單個過濾器功能測試
測試每個過濾器的基本功能
"""
function test_individual_filter_functionality(test_data::Vector{LotteryDraw})::Dict{String, Any}
    @testset "Individual Filter Functionality" begin
        engine = FilterEngine(test_data)
        results = Dict{String, Any}()
        filter_results = Dict{String, Any}()
        
        # 測試 ONE 過濾器
        try
            one_result = calculate_one_filter(engine, 1)
            @test isa(one_result, FilterResult)
            @test one_result.current_value >= 0
            @test !isnan(one_result.current_value)
            @test !isnan(one_result.expected_value)
            filter_results["ONE"] = Dict("status" => "passed", "result" => one_result)
        catch e
            @warn "ONE 過濾器測試失敗: $e"
            filter_results["ONE"] = Dict("status" => "failed", "error" => string(e))
        end
        
        # 測試 TWO 過濾器
        try
            two_result = calculate_two_filter(engine, [1, 2, 3])
            @test isa(two_result, FilterResult)
            @test two_result.current_value >= 0
            @test !isnan(two_result.current_value)
            filter_results["TWO"] = Dict("status" => "passed", "result" => two_result)
        catch e
            @warn "TWO 過濾器測試失敗: $e"
            filter_results["TWO"] = Dict("status" => "failed", "error" => string(e))
        end
        
        # 測試 THREE 過濾器
        try
            three_result = calculate_three_filter(engine, [1, 2, 3, 4])
            @test isa(three_result, FilterResult)
            @test three_result.current_value >= 0
            @test !isnan(three_result.current_value)
            filter_results["THREE"] = Dict("status" => "passed", "result" => three_result)
        catch e
            @warn "THREE 過濾器測試失敗: $e"
            filter_results["THREE"] = Dict("status" => "failed", "error" => string(e))
        end
        
        # 測試 FOUR 過濾器
        try
            four_result = calculate_four_filter(engine, [1, 2, 3, 4, 5])
            @test isa(four_result, FilterResult)
            @test four_result.current_value >= 0
            @test !isnan(four_result.current_value)
            filter_results["FOUR"] = Dict("status" => "passed", "result" => four_result)
        catch e
            @warn "FOUR 過濾器測試失敗: $e"
            filter_results["FOUR"] = Dict("status" => "failed", "error" => string(e))
        end
        
        # 測試 FIVE 過濾器
        try
            five_result = calculate_five_filter(engine, [1, 2, 3, 4, 5])
            @test isa(five_result, FilterResult)
            @test five_result.current_value >= 0
            @test !isnan(five_result.current_value)
            filter_results["FIVE"] = Dict("status" => "passed", "result" => five_result)
        catch e
            @warn "FIVE 過濾器測試失敗: $e"
            filter_results["FIVE"] = Dict("status" => "failed", "error" => string(e))
        end
        
        # 計算成功率
        successful_filters = count(result -> result["status"] == "passed", values(filter_results))
        total_filters = length(filter_results)
        success_rate = successful_filters / total_filters
        
        results["filter_results"] = filter_results
        results["success_rate"] = success_rate
        results["successful_filters"] = successful_filters
        results["total_filters"] = total_filters
        
        @test success_rate >= 0.8  # 要求 80% 以上的成功率
        
        println("  ✅ 過濾器功能測試: $(successful_filters)/$(total_filters) 通過")
        
        return results
    end
end

"""
過濾器組合測試
測試多個過濾器的組合使用
"""
function test_filter_combinations(test_data::Vector{LotteryDraw})::Dict{String, Any}
    @testset "Filter Combinations" begin
        engine = FilterEngine(test_data)
        results = Dict{String, Any}()
        combination_results = []
        
        # 測試組合 1：ONE + TWO
        try
            one_result = calculate_one_filter(engine, 1)
            two_result = calculate_two_filter(engine, [1, 2, 3])
            
            # 驗證結果的一致性
            @test isa(one_result, FilterResult)
            @test isa(two_result, FilterResult)
            
            combination_score = (one_result.current_value + two_result.current_value) / 2
            push!(combination_results, Dict(
                "combination" => "ONE+TWO",
                "status" => "passed",
                "score" => combination_score
            ))
        catch e
            @warn "ONE+TWO 組合測試失敗: $e"
            push!(combination_results, Dict(
                "combination" => "ONE+TWO",
                "status" => "failed",
                "error" => string(e)
            ))
        end
        
        # 測試組合 2：THREE + FOUR + FIVE
        try
            three_result = calculate_three_filter(engine, [1, 2, 3, 4])
            four_result = calculate_four_filter(engine, [1, 2, 3, 4, 5])
            five_result = calculate_five_filter(engine, [1, 2, 3, 4, 5])
            
            @test isa(three_result, FilterResult)
            @test isa(four_result, FilterResult)
            @test isa(five_result, FilterResult)
            
            combination_score = (three_result.current_value + four_result.current_value + five_result.current_value) / 3
            push!(combination_results, Dict(
                "combination" => "THREE+FOUR+FIVE",
                "status" => "passed",
                "score" => combination_score
            ))
        catch e
            @warn "THREE+FOUR+FIVE 組合測試失敗: $e"
            push!(combination_results, Dict(
                "combination" => "THREE+FOUR+FIVE",
                "status" => "failed",
                "error" => string(e)
            ))
        end
        
        # 測試組合 3：全部過濾器
        try
            all_results = []
            push!(all_results, calculate_one_filter(engine, 1))
            push!(all_results, calculate_two_filter(engine, [1, 2, 3]))
            push!(all_results, calculate_three_filter(engine, [1, 2, 3, 4]))
            push!(all_results, calculate_four_filter(engine, [1, 2, 3, 4, 5]))
            push!(all_results, calculate_five_filter(engine, [1, 2, 3, 4, 5]))
            
            for result in all_results
                @test isa(result, FilterResult)
                @test !isnan(result.current_value)
            end
            
            overall_score = mean([r.current_value for r in all_results])
            push!(combination_results, Dict(
                "combination" => "ALL_FILTERS",
                "status" => "passed",
                "score" => overall_score
            ))
        catch e
            @warn "全部過濾器組合測試失敗: $e"
            push!(combination_results, Dict(
                "combination" => "ALL_FILTERS",
                "status" => "failed",
                "error" => string(e)
            ))
        end
        
        # 計算組合成功率
        successful_combinations = count(result -> result["status"] == "passed", combination_results)
        total_combinations = length(combination_results)
        combination_success_rate = successful_combinations / total_combinations
        
        results["combination_results"] = combination_results
        results["combination_success_rate"] = combination_success_rate
        results["successful_combinations"] = successful_combinations
        results["total_combinations"] = total_combinations
        
        @test combination_success_rate >= 0.7  # 要求 70% 以上的組合成功率
        
        println("  ✅ 過濾器組合測試: $(successful_combinations)/$(total_combinations) 通過")
        
        return results
    end
end

"""
過濾器性能測試
測試過濾器的執行性能
"""
function test_filter_performance(test_data::Vector{LotteryDraw}, iterations::Int = 50)::Dict{String, Any}
    @testset "Filter Performance" begin
        engine = FilterEngine(test_data)
        results = Dict{String, Any}()
        
        # 預熱
        for _ in 1:5
            calculate_one_filter(engine, 1)
            calculate_two_filter(engine, [1, 2, 3])
        end
        
        # 測試各個過濾器的性能
        filter_performance = Dict{String, Dict{String, Float64}}()
        
        # ONE 過濾器性能
        one_times = Float64[]
        for _ in 1:iterations
            start_time = time()
            calculate_one_filter(engine, rand(1:39))
            execution_time = (time() - start_time) * 1000
            push!(one_times, execution_time)
        end
        filter_performance["ONE"] = Dict(
            "mean_ms" => mean(one_times),
            "median_ms" => median(one_times),
            "max_ms" => maximum(one_times)
        )
        
        # TWO 過濾器性能
        two_times = Float64[]
        for _ in 1:iterations
            test_numbers = sort(simple_sample(1:39, 3))
            start_time = time()
            calculate_two_filter(engine, test_numbers)
            execution_time = (time() - start_time) * 1000
            push!(two_times, execution_time)
        end
        filter_performance["TWO"] = Dict(
            "mean_ms" => mean(two_times),
            "median_ms" => median(two_times),
            "max_ms" => maximum(two_times)
        )
        
        # THREE 過濾器性能
        three_times = Float64[]
        for _ in 1:iterations
            test_numbers = sort(simple_sample(1:39, 4))
            start_time = time()
            calculate_three_filter(engine, test_numbers)
            execution_time = (time() - start_time) * 1000
            push!(three_times, execution_time)
        end
        filter_performance["THREE"] = Dict(
            "mean_ms" => mean(three_times),
            "median_ms" => median(three_times),
            "max_ms" => maximum(three_times)
        )
        
        # FOUR 過濾器性能
        four_times = Float64[]
        for _ in 1:iterations
            test_numbers = sort(simple_sample(1:39, 5))
            start_time = time()
            calculate_four_filter(engine, test_numbers)
            execution_time = (time() - start_time) * 1000
            push!(four_times, execution_time)
        end
        filter_performance["FOUR"] = Dict(
            "mean_ms" => mean(four_times),
            "median_ms" => median(four_times),
            "max_ms" => maximum(four_times)
        )
        
        # FIVE 過濾器性能
        five_times = Float64[]
        for _ in 1:iterations
            test_numbers = sort(simple_sample(1:39, 5))
            start_time = time()
            calculate_five_filter(engine, test_numbers)
            execution_time = (time() - start_time) * 1000
            push!(five_times, execution_time)
        end
        filter_performance["FIVE"] = Dict(
            "mean_ms" => mean(five_times),
            "median_ms" => median(five_times),
            "max_ms" => maximum(five_times)
        )
        
        results["filter_performance"] = filter_performance
        
        # 性能要求檢查
        for (filter_name, perf) in filter_performance
            @test perf["mean_ms"] < 100.0  # 每個過濾器應該小於 100ms
        end
        
        println("  ✅ 過濾器性能測試:")
        for (filter_name, perf) in filter_performance
            println("    - $(filter_name): $(round(perf["mean_ms"], digits=1))ms")
        end
        
        return results
    end
end

"""
過濾器邊界條件測試
測試過濾器在各種邊界條件下的表現
"""
function test_filter_boundary_conditions()::Dict{String, Any}
    @testset "Filter Boundary Conditions" begin
        results = Dict{String, Any}()
        boundary_tests = []
        
        # 測試 1：空數據
        empty_data = LotteryDraw[]
        try
            empty_engine = FilterEngine(empty_data)
            one_result = calculate_one_filter(empty_engine, 1)
            @test isa(one_result, FilterResult)
            push!(boundary_tests, Dict("test" => "empty_data", "status" => "passed"))
        catch e
            # 拋出異常也是可接受的
            push!(boundary_tests, Dict("test" => "empty_data", "status" => "passed"))
        end
        
        # 測試 2：單筆數據
        single_data = [LotteryDraw([1, 2, 3, 4, 5], Date(2022, 1, 1), 1)]
        try
            single_engine = FilterEngine(single_data)
            
            # 測試存在的號碼
            one_result = calculate_one_filter(single_engine, 1)
            @test isa(one_result, FilterResult)
            @test one_result.current_value >= 0
            
            # 測試不存在的號碼
            one_result_missing = calculate_one_filter(single_engine, 6)
            @test isa(one_result_missing, FilterResult)
            
            push!(boundary_tests, Dict("test" => "single_data", "status" => "passed"))
        catch e
            @warn "單筆數據測試失敗: $e"
            push!(boundary_tests, Dict("test" => "single_data", "status" => "failed"))
        end
        
        # 測試 3：超出範圍的號碼
        test_data = [LotteryDraw([1, 2, 3, 4, 5], Date(2022, 1, 1), 1)]
        try
            test_engine = FilterEngine(test_data)
            # 這應該拋出異常或返回合理的結果
            out_of_range_result = calculate_one_filter(test_engine, 0)
            push!(boundary_tests, Dict("test" => "out_of_range", "status" => "passed"))
        catch e
            # 拋出異常是預期的
            push!(boundary_tests, Dict("test" => "out_of_range", "status" => "passed"))
        end
        
        # 測試 4：重複號碼
        try
            test_engine = FilterEngine(test_data)
            duplicate_result = calculate_two_filter(test_engine, [1, 1, 2])
            push!(boundary_tests, Dict("test" => "duplicate_numbers", "status" => "passed"))
        catch e
            # 拋出異常是預期的
            push!(boundary_tests, Dict("test" => "duplicate_numbers", "status" => "passed"))
        end
        
        results["boundary_tests"] = boundary_tests
        results["total_boundary_tests"] = length(boundary_tests)
        results["passed_boundary_tests"] = count(test -> test["status"] == "passed", boundary_tests)
        
        println("  ✅ 邊界條件測試: $(results["passed_boundary_tests"])/$(results["total_boundary_tests"]) 通過")
        
        return results
    end
end

"""
執行完整的過濾器綜合測試
"""
function run_comprehensive_filter_tests(data_manager::TestDataManager)::Dict{String, Any}
    println("🧪 開始執行過濾器綜合測試...")
    
    comprehensive_results = Dict{String, Any}()
    
    # 使用中等大小的測試數據
    test_data = get_test_data(data_manager, "medium")
    
    try
        # 執行各項測試
        comprehensive_results["individual_functionality"] = test_individual_filter_functionality(test_data)
        comprehensive_results["filter_combinations"] = test_filter_combinations(test_data)
        comprehensive_results["performance"] = test_filter_performance(test_data)
        comprehensive_results["boundary_conditions"] = test_filter_boundary_conditions()
        
        # 計算整體評分
        functionality_score = comprehensive_results["individual_functionality"]["success_rate"]
        combination_score = comprehensive_results["filter_combinations"]["combination_success_rate"]
        boundary_score = comprehensive_results["boundary_conditions"]["passed_boundary_tests"] / 
                        comprehensive_results["boundary_conditions"]["total_boundary_tests"]
        
        # 性能測試的成功率（簡化評分）
        performance_score = 1.0  # 如果沒有異常就算成功
        
        overall_score = (functionality_score + combination_score + boundary_score + performance_score) / 4
        comprehensive_results["overall_score"] = overall_score
        
        println("\n📊 過濾器綜合測試結果:")
        println("  - 個別功能測試: $(round(functionality_score * 100, digits=1))%")
        println("  - 組合測試: $(round(combination_score * 100, digits=1))%")
        println("  - 邊界條件測試: $(round(boundary_score * 100, digits=1))%")
        println("  - 性能測試: $(round(performance_score * 100, digits=1))%")
        println("  - 整體評分: $(round(overall_score * 100, digits=1))%")
        
        if overall_score >= 0.95
            println("🎉 過濾器測試：優秀")
        elseif overall_score >= 0.90
            println("✅ 過濾器測試：良好")
        elseif overall_score >= 0.80
            println("⚠️ 過濾器測試：需要改進")
        else
            println("❌ 過濾器測試：需要重大修復")
        end
        
        return comprehensive_results
        
    catch e
        @error "過濾器綜合測試失敗: $e"
        comprehensive_results["error"] = string(e)
        return comprehensive_results
    end
end

# 導出主要函數
export test_individual_filter_functionality, test_filter_combinations
export test_filter_performance, test_filter_boundary_conditions
export run_comprehensive_filter_tests
