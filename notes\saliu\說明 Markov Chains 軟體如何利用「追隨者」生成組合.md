Ion Saliu 的馬可夫鏈軟體主要透過分析歷史開獎數據中的「號碼追隨者」和「配對頻率」來生成樂透彩組合。

以下是其運作方式的詳細說明：

**1. 「追隨者」與「配對」的定義**

- **號碼追隨者 (Number Followers)**：這是馬可夫鏈概念的基礎。在樂透彩的歷史開獎中，如果某個號碼（例如 22）後面經常出現另一個或幾個特定號碼（例如 24 和 23），那麼這些號碼就被視為 22 的「追隨者」。Ion Saliu 將其馬可夫鏈軟體中的這個概念建立在這些「號碼追隨者」和「樂透配對」上。
- **樂透配對 (Lottery Pairs)**：Saliu 的「樂透奇蹟網格 (lotto wonder grid)」概念更為廣泛，它將在同一次開獎中出現的號碼進行配對。馬可夫鏈理論在樂透彩中的應用，被視為其「樂透奇蹟網格」的一個子集。

**2. 軟體準備與報告生成**

- **軟體套件**：Ion Saliu 的馬可夫鏈軟體屬於其「終極軟體 (Ultimate Software)」套件，包含 MarkovPick3 (Pick-3 彩票)、MarkovLotto5 (5 號樂透) 和 MarkovLotto6 (6 號樂透) 等程式。
- **前置步驟**：在生成任何組合之前，使用者必須先執行 `R = Report Pairs, Frequency (Hot)` 功能。若未執行，軟體將會觸發錯誤。
- **報告文件**：這個報告功能會自動建立四個關鍵的文字檔案，這些檔案是後續組合生成功能所必需的：
    - `MarkovNumbersL6`：包含所有號碼，從**熱門到冷門**排序。
    - `MarkovPairsPivL6`：包含所有配對，按熱門到冷門排序，並**帶有樞紐 (PIVOT)**。
    - `MarkovPairsNoPL6`：包含所有配對，按熱門到冷門排序，但**沒有樞紐**。
    - `MarkovFollowersL6`：包含所有樂透號碼的**馬可夫鏈追隨者**。
    - `MarkovLikePairsL6`：包含所有號碼的**類似追隨者的配對 (Followers-like Pairs)**。
    - 值得注意的是，`Followers` 是透過較長的配對分析範圍 (`ParpaluckP`) 計算的，而 `Follower-like Pairings` 則透過較短的數字頻率分析範圍 (`ParpaluckF`) 生成。這些檔案中若無追隨者或配對，可能顯示 `-1` 代表空行。

**3. 組合生成方法**

- 軟體提供多種組合生成方法：
    - **`M = Generate Combinations from Pairings as Markov Chains` (基於配對作為馬可夫鏈生成組合)**：這種方法生成的是**馬可夫風格的組合，但並非完全隨機**。號碼是從「類似追隨者的配對」檔案 (`MarkovLikePairsL6`) 中隨機選取。
    - **`C = Generate Combinations as Traditional Markov Chains` (作為傳統馬可夫鏈生成組合)**：這種方法遵循**傳統的馬可夫鏈生成模式**。程式會從追隨者列表 (`MarkovFollowersL6`) 中隨機選取號碼。
- **重複組合排除**：兩種馬可夫鏈風格的生成器都會**去除重複的組合**，確保使用者獲得獨特的樂透組合。例如，生成 1000 個 Pick-3 馬可夫鏈組合，最終可能只產生約 90 個獨特組合。

**4. 策略應用：逆向策略（LIE 消除）**

- Saliu 的馬可夫鏈軟體生成的大部分組合，例如基於熱門數字、各種配對或追隨者生成的組合，通常被建議作為**「LIE 消除」(LIE elimination)** 功能的良好候選。
- 「LIE 消除」是一種**「反向策略」**，其核心理念是生成**預計不會中獎**的組合，然後將這些組合從投注中排除，從而達到獲利的目的。Saliu 認為，透過這些馬可夫鏈方法生成的組合在實際的下一次開獎中，特別是接下來的 5-6 次開獎中，往往不會中得顯著獎金。
- 儘管 Saliu 挑戰了馬可夫鏈在樂透彩中的傳統適用性（認為樂透是純粹隨機，過去不影響未來），但他提出了**「整個宇宙都是隨機的，但隨機性有其規則」**，並主張**所有事件都是「隨機關聯的」**。他的「賭博基本公式 (Fundamental Formula of Gambling, FFG)」被視為「遊戲理論中最精確的工具」，並認為 FFG 在多個層面上超越了馬可夫鏈，因為 FFG 考量了過去事件對未來事件的影響。然而，他依然將馬可夫鏈的概念（如號碼追隨者和配對分析）納入其更全面的樂透分析框架中。

總之，馬可夫鏈軟體透過分析歷史開獎數據，識別號碼之間的「追隨」和「配對」模式，生成預計不會在短期內中獎的組合，並將其作為「LIE 消除」策略的基礎，以有效縮減投注範圍。