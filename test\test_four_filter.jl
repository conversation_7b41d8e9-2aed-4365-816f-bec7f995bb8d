# FOUR Filter Tests
# FOUR 過濾器測試

using Test
using Dates

# 引入必要的模組
include("../src/types.jl")
include("../src/filter_engine.jl")
include("../src/filters/four_filter.jl")

"""
創建測試用的彩票數據
"""
function create_test_lottery_data_for_quadruplets()::Vector{LotteryDraw}
    test_draws = [
        LotteryDraw([1, 2, 3, 4, 20], Date(2022, 1, 10), 10),  # 最新，包含四號組合 (1,2,3,4)
        LotteryDraw([5, 6, 11, 16, 21], Date(2022, 1, 9), 9),
        LotteryDraw([1, 2, 3, 17, 22], Date(2022, 1, 8), 8),   # 包含部分四號組合
        LotteryDraw([7, 8, 13, 18, 23], Date(2022, 1, 7), 7),
        LotteryDraw([1, 9, 14, 19, 24], Date(2022, 1, 6), 6),
        LotteryDraw([1, 2, 3, 4, 25], Date(2022, 1, 5), 5),   # 包含四號組合 (1,2,3,4)
        LotteryDraw([10, 12, 16, 21, 26], Date(2022, 1, 4), 4),
        LotteryDraw([6, 12, 17, 22, 27], Date(2022, 1, 3), 3),
        LotteryDraw([1, 5, 8, 11, 28], Date(2022, 1, 2), 2),
        LotteryDraw([15, 14, 19, 24, 29], Date(2022, 1, 1), 1),  # 最舊
    ]
    return test_draws
end

"""
測試四號組合生成功能
"""
function test_quadruplet_generation()
    @testset "Quadruplet Generation" begin
        # 測試正常情況
        numbers = [1, 2, 3, 4, 5]
        quadruplets = generate_quadruplets(numbers)
        expected_count = binomial(5, 4)  # C(5,4) = 5
        @test length(quadruplets) == expected_count
        
        # 驗證具體的四號組合
        expected_quadruplets = [
            (1, 2, 3, 4), (1, 2, 3, 5), (1, 2, 4, 5), 
            (1, 3, 4, 5), (2, 3, 4, 5)
        ]
        @test all(quad in expected_quadruplets for quad in quadruplets)
        
        # 測試邊界條件
        @test isempty(generate_quadruplets([1, 2, 3]))  # 只有三個號碼
        @test isempty(generate_quadruplets([1]))        # 只有一個號碼
        @test isempty(generate_quadruplets(Int[]))      # 空數組
        
        # 測試四個號碼
        four_numbers = [5, 2, 8, 1]
        four_quadruplets = generate_quadruplets(four_numbers)
        @test length(four_quadruplets) == 1
        @test four_quadruplets[1] == (5, 2, 8, 1)  # 應該保持原順序
        
        println("✓ 四號組合生成測試通過")
    end
end

"""
測試四號組合頻率計算
"""
function test_quadruplet_frequency_calculation()
    @testset "Quadruplet Frequency Calculation" begin
        test_data = create_test_lottery_data_for_quadruplets()
        engine = FilterEngine(test_data)
        
        # 測試四號組合 (1,2,3,4) 的頻率 - 應該出現 2 次
        freq_1_2_3_4 = calculate_quadruplet_frequency(engine, (1, 2, 3, 4))
        @test freq_1_2_3_4 == 2
        
        # 測試不存在的四號組合
        freq_never = calculate_quadruplet_frequency(engine, (30, 35, 37, 39))
        @test freq_never == 0
        
        # 測試部分匹配的組合
        freq_partial = calculate_quadruplet_frequency(engine, (1, 2, 3, 17))
        @test freq_partial == 1  # 在第 8 次開獎中出現
        
        println("✓ 四號組合頻率計算測試通過")
    end
end

"""
測試四號組合 Skip 計算
"""
function test_quadruplet_skip_calculation()
    @testset "Quadruplet Skip Calculation" begin
        test_data = create_test_lottery_data_for_quadruplets()
        engine = FilterEngine(test_data)
        
        # 測試四號組合 (1,2,3,4) 的 Skip - 最後在索引 1 出現（最新）
        skip_1_2_3_4 = calculate_quadruplet_skip(engine, (1, 2, 3, 4))
        @test skip_1_2_3_4 == 0  # 最新開獎就有出現
        
        # 測試從未出現的四號組合
        skip_never = calculate_quadruplet_skip(engine, (30, 35, 37, 39))
        @test skip_never == length(test_data)
        
        println("✓ 四號組合 Skip 計算測試通過")
    end
end

"""
測試四號組合統計分析
"""
function test_four_combination_statistics()
    @testset "Four Combination Statistics" begin
        test_data = create_test_lottery_data_for_quadruplets()
        engine = FilterEngine(test_data)
        
        # 測試統計分析
        test_numbers = [1, 2, 3, 4, 5]
        stats = calculate_four_combination_statistics(engine, test_numbers)
        
        # 驗證統計結果結構
        @test haskey(stats, "total_quadruplets")
        @test haskey(stats, "appeared_quadruplets")
        @test haskey(stats, "never_appeared")
        @test haskey(stats, "appearance_rate")
        @test haskey(stats, "frequencies")
        @test haskey(stats, "average_frequency")
        
        @test stats["total_quadruplets"] == 5  # C(5,4) = 5
        @test stats["appeared_quadruplets"] >= 0
        @test stats["appearance_rate"] >= 0.0
        
        # 測試號碼不足的情況
        insufficient_stats = calculate_four_combination_statistics(engine, [1, 2, 3])
        @test haskey(insufficient_stats, "error")
        
        println("✓ 四號組合統計分析測試通過")
    end
end

"""
測試機率計算邏輯
"""
function test_four_combination_probability()
    @testset "Four Combination Probability" begin
        test_data = create_test_lottery_data_for_quadruplets()
        engine = FilterEngine(test_data)
        
        # 測試機率計算
        test_numbers = [1, 2, 3, 4, 5]
        prob_analysis = calculate_four_combination_probability(engine, test_numbers)
        
        # 驗證機率分析結構
        @test haskey(prob_analysis, "theoretical_probability")
        @test haskey(prob_analysis, "expected_frequency")
        @test haskey(prob_analysis, "actual_frequency")
        @test haskey(prob_analysis, "probability_ratio")
        @test haskey(prob_analysis, "is_above_expected")
        
        @test 0.0 <= prob_analysis["theoretical_probability"] <= 1.0
        @test prob_analysis["expected_frequency"] >= 0.0
        @test prob_analysis["actual_frequency"] >= 0.0
        
        println("✓ 機率計算邏輯測試通過")
    end
end

"""
測試 FOUR 過濾器計算
"""
function test_four_filter_calculation()
    @testset "FOUR Filter Calculation" begin
        test_data = create_test_lottery_data_for_quadruplets()
        engine = FilterEngine(test_data)
        
        # 測試包含多個四號組合的號碼組合
        test_numbers = [1, 2, 3, 4, 5]
        result = calculate_four_filter(engine, test_numbers)
        
        # 驗證結果結構
        @test result.filter_type == FOUR_FILTER
        @test occursin("FOUR_FILTER", result.filter_name)
        @test isa(result.current_value, Int)
        @test isa(result.expected_value, Float64)
        @test isa(result.is_favorable, Bool)
        @test 0.0 <= result.confidence_level <= 1.0
        @test isa(result.historical_values, Vector{Int})
        @test result.calculation_time >= 0.0
        
        # 驗證四號組合數量 - C(5,4) = 5
        @test result.current_value == 5
        
        println("✓ FOUR 過濾器計算測試通過")
        println("  - 當前四號組合數: $(result.current_value)")
        println("  - 期望頻率: $(round(result.expected_value, digits=4))")
        println("  - 是否有利: $(result.is_favorable)")
        println("  - 信心水準: $(round(result.confidence_level, digits=2))")
    end
end

"""
測試最有潛力四號組合識別
"""
function test_most_promising_quadruplets()
    @testset "Most Promising Quadruplets" begin
        test_data = create_test_lottery_data_for_quadruplets()
        engine = FilterEngine(test_data)
        
        # 獲取號碼 [1,2,3,4,5] 中最有潛力的四號組合
        test_numbers = [1, 2, 3, 4, 5]
        promising = get_most_promising_quadruplets(engine, test_numbers, 3)
        
        @test length(promising) == 3  # 應該有 3 個結果（要求前3個）
        @test all(haskey(quad, "potential_score") for quad in promising)
        
        # 驗證排序（潛力分數應該遞減）
        for i in 2:length(promising)
            @test promising[i-1]["potential_score"] >= promising[i]["potential_score"]
        end
        
        println("✓ 最有潛力四號組合識別測試通過")
    end
end

"""
測試 FOUR 過濾器統計摘要
"""
function test_four_filter_summary()
    @testset "FOUR Filter Summary" begin
        test_data = create_test_lottery_data_for_quadruplets()
        engine = FilterEngine(test_data)
        
        # 計算多個號碼組合的 FOUR 過濾器結果
        test_combinations = [
            [1, 2, 3, 4],
            [1, 2, 3, 4, 5],
            [5, 6, 7, 8, 9],
            [10, 11, 12, 13]
        ]
        
        results = FilterResult[]
        for combo in test_combinations
            result = calculate_four_filter(engine, combo)
            push!(results, result)
        end
        
        # 獲取統計摘要
        summary = get_four_filter_summary(results)
        
        # 驗證摘要結構
        @test haskey(summary, "total_combinations")
        @test haskey(summary, "favorable_combinations")
        @test haskey(summary, "average_confidence")
        @test haskey(summary, "average_quadruplet_count")
        @test haskey(summary, "quadruplet_count_distribution")
        
        @test summary["total_combinations"] == length(test_combinations)
        
        println("✓ FOUR 過濾器統計摘要測試通過")
        println("  - 總組合數: $(summary["total_combinations"])")
        println("  - 有利組合數: $(summary["favorable_combinations"])")
        println("  - 平均信心水準: $(round(summary["average_confidence"], digits=2))")
    end
end

"""
測試快取功能
"""
function test_four_filter_caching()
    @testset "FOUR Filter Caching" begin
        test_data = create_test_lottery_data_for_quadruplets()
        engine = FilterEngine(test_data, cache_enabled=true)
        
        test_numbers = [1, 2, 3, 4, 5]
        
        # 第一次計算
        result1 = calculate_four_filter(engine, test_numbers)
        cache_size_after_first = length(engine.filter_cache)
        
        # 第二次計算（應該使用快取）
        result2 = calculate_four_filter(engine, test_numbers)
        cache_size_after_second = length(engine.filter_cache)
        
        # 驗證快取工作
        @test cache_size_after_first >= 1
        @test cache_size_after_second == cache_size_after_first  # 快取大小不變
        @test result1.current_value == result2.current_value
        @test result1.expected_value == result2.expected_value
        
        println("✓ FOUR 過濾器快取功能測試通過")
    end
end

"""
測試錯誤處理
"""
function test_four_filter_error_handling()
    @testset "FOUR Filter Error Handling" begin
        test_data = create_test_lottery_data_for_quadruplets()
        engine = FilterEngine(test_data)
        
        # 測試號碼數量不足
        @test_throws ArgumentError calculate_four_filter(engine, [1, 2, 3])
        @test_throws ArgumentError calculate_four_filter(engine, [1])
        @test_throws ArgumentError calculate_four_filter(engine, Int[])
        
        # 測試無效號碼範圍
        @test_throws ArgumentError calculate_four_filter(engine, [0, 1, 2, 3])
        @test_throws ArgumentError calculate_four_filter(engine, [1, 2, 3, 40])
        
        # 測試空數據
        empty_engine = FilterEngine(LotteryDraw[])
        @test_throws ArgumentError calculate_four_filter(empty_engine, [1, 2, 3, 4])
        
        println("✓ FOUR 過濾器錯誤處理測試通過")
    end
end

"""
執行所有 FOUR 過濾器測試
"""
function run_four_filter_tests()
    println("🧪 開始執行 FOUR 過濾器測試...")
    
    try
        test_quadruplet_generation()
        test_quadruplet_frequency_calculation()
        test_quadruplet_skip_calculation()
        test_four_combination_statistics()
        test_four_combination_probability()
        test_four_filter_calculation()
        test_most_promising_quadruplets()
        test_four_filter_summary()
        test_four_filter_caching()
        test_four_filter_error_handling()
        
        println("\n🎉 所有 FOUR 過濾器測試通過！")
        return true
    catch e
        println("\n❌ FOUR 過濾器測試失敗: $e")
        return false
    end
end

# 如果直接執行此文件，運行測試
if abspath(PROGRAM_FILE) == @__FILE__
    run_four_filter_tests()
end
