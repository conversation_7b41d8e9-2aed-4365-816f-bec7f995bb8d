---
created: 2025-01-03T19:54:58 (UTC +08:00)
tags: [keno,strategy,system,wonder grid,lotto,software,lottery,pairs,numbers,<PERSON>,Duke of Gambling,]
source: https://saliu.com/bbs/messages/907.html
author: 
---

# Duke Keno Strategy, Systems, Best Pairs, Winning Numbers

> ## Excerpt
> Apply the Power Lotto Strategy to the Keno game. You pick a favorite keno lotto number and play its top 25% pairings with Duke lottery software.

---
Posted by [<PERSON>](mailto:<EMAIL>) on July 14, 2001.

In Reply to: [_**5% Frequency: The lowest lotto number in a lottery drawing is OVER 19**_](https://saliu.com/bbs/messages/898.html) posted by DEZ on July 12, 2001.

I am posting this idea in response to <PERSON>'<PERSON> and <PERSON><PERSON>'s call for ideas.

I need some help with this so I'm hoping that by sharing this in the spirit of mutual cooperation we all can benefit. This idea is an extension of Ion's _**Keller Lotto Strategy (Lottery Wonder Grid)**_ so all credit must go to him and also to DEZ for providing me with the encouragement to post it (I was afraid it would be shot down). I have emailed the idea to <PERSON>, and he believes it has merit.

The idea is to apply the _**Killer Lotto Strategy**_ to the Keno game. You would pick a favorite number and take its top 25% pairings. In 20/80 Keno game the top 25% would be 20 numbers. Since for each drawing there are 20 numbers picked, then in a certain amount of drawings all or most of the 20 numbers picked would be among the favorite's top 25% lottery pairs. So all you would have to do is choose 10 of these 20 numbers. Hopefully, in one of these draws this would be the jackpot ($500,000 in New York).

Also, if an abbreviated wheel could be designed to wheel the top 20 lotto numbers along with one favorite key number, then many lower tier prizes could possibly be won. Perhaps someone out there can design and share such a wheel.

That's the idea. I need help in finding good abbreviated keno wheels that can wheel 20 numbers with good win guarantees starting with 6 of 10. If anyone can design or post links to such keno lottery wheels it would be very helpful. Any tips on choosing a favorite number would also be helpful. Unfortunately, Ion is very busy or I'm sure he would have taken up the challenge.

![Keno systems from the top pairs of the best keno numbers can win half million dollars.](https://saliu.com/bbs/messages/HLINE.gif)

-   Without any doubt, the best software for Keno lotto is **MDIEditor Lotto WE**. On the menu bar, click _**Lotto**_, then _**Keno**_. The game is covered also in the _**Stats**_ and _**Filters**_ menus.

![The best lotto software analyzes keno drawings to generate winning numbers, systems.](https://saliu.com/ScreenImgs/keno.gif)

-   The best-ever Keno number generator resides here. It is free to download and run on your computer with no restrictions.
-   Click the button named _**Keno 10/80**_. It is highly recommended to run the function several times, before selecting the combinations to play. The chance is low that the first runs of the generator will come up with winning numbers in the near future. But you saved money because you didn't play those keno combinations.
-   Read: [_**Odds Calculator, Number Combination Generator**_ **for Lottery, Lotto, Powerball, Mega Millions, Euromillions, Keno**](https://saliu.com/gambling-lottery-lotto/odds-generator.html).

![The best Keno random numbers, combinations generator is free to download and run on your computer.](https://saliu.com/ScreenImgs/keno-generator.gif)

[

## <u>Resources in Lottery, Software, Systems, Lotto Wheels, Strategies</u>

](https://saliu.com/content/lottery.html)

-   The main page for [**Lottery, Software, Strategies**](https://saliu.com/LottoWin.htm)  
    Presenting software to create free winning lotto, lottery strategies, systems based on mathematics. Get your lotto systems or wheels, the best lottery, lotto software, combinations, winning numbers.
-   [_**Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies**_](https://saliu.com/Newsgroups.htm).  
    Read a genuine analysis of Excel spreadsheets applied to lottery and lotto software development, systems, and strategies.
-   [_**<u>MDIEditor Lotto WE</u>: Lottery Software Manual, Book, ebook, Help**_](https://saliu.com/MDI-lotto-guide.html).  
    ~ Also applicable to **LotWon** lottery, lotto software; plus Powerball, Mega Millions, Euromillions.
-   [_**Book, Tutorial, Manual of Lotto, Lottery Software, Programs**_](https://saliu.com/forum/lotto-book.html).
-   [**Skips Lottery, Gambling Systems**](https://saliu.com/skip-strategy.html): _**Lotto, Lottery, Powerball, Mega Millions, Euromillions, Horse Racing, Roulette, Football**_.
-   [_**Lottery Utility Software**_](https://saliu.com/lottery-utility.html): Pick-3, 4 Lottery, Lotto-5, 6, Powerball, Mega Millions, Euromillions.
-   [**Lotto wheels**](https://saliu.com/lotto_wheels.html) for lotto games drawing 5, 6, or 7 numbers  
    The most advanced theory of lotto wheels or reduced lottery systems. Get also original lotto wheels for lotto games drawing 5, 6, or 7 numbers: Balanced, randomized, and free.
-   Download [**the best lottery software**](https://saliu.com/infodown.html).

![The idea is to apply the Power Lotto Strategy to the Keno game.](https://saliu.com/bbs/messages/HLINE.gif)

**Follow-Ups**:  

-   [_**Lotto system: Play top 10% of lotto number pairs**_](https://saliu.com/bbs/messages/916.html) **Ion Saliu** _07/15/01._
-   [_**Lottery system, strategy: Lotto sum-total, sums parameter**_](https://saliu.com/bbs/messages/915.html) **DEZ** _07/15/01._
-   [_**Winning lotto numbers in the last 10 lottery drawings**_](https://saliu.com/bbs/messages/914.html) **DEZ** _07/15/01._
-   [_**Eliminate the last 4 or 5 lotto numbers to create a starting point for a lotto system**_](https://saliu.com/bbs/messages/913.html) **BigJer** _07/15/01._
-   [_**Lottery players use the sums of lotto combinations as system**_](https://saliu.com/bbs/messages/912.html) **DEZ** _07/14/01._
-   [_**Statistics of highest lotto 6-49 number UNDER 33, Sum-total UNDER 100**_](https://saliu.com/bbs/messages/910.html) **DEZ** _07/14/01._
-   [_**Lotto system: Eliminate lowest 5 and highest 5 lotto numbers**_](https://saliu.com/bbs/messages/909.html) **DEZ** _07/14/01_
-   [_**Lottery software generates fewer than 10 lotto combinations for jackpot**_](https://saliu.com/bbs/messages/899.html) **Ion Saliu** _07/13/01._

![The idea is to apply the Wonder-Grid lotto strategy to Keno lottery games drawing 20 numbers.](https://saliu.com/bbs/messages/HLINE.gif)

**[Home](https://saliu.com/index.htm) | [Search](https://saliu.com/Search.htm) | [New Writings](https://saliu.com/bbs/index.html) | [Software](https://saliu.com/infodown.html) | [Odds, Generator](https://saliu.com/calculator_generator.html) | [Contents](https://saliu.com/content/index.html) | [Forums](https://forums.saliu.com/) | [Sitemap](https://saliu.com/sitemap/index.html)**

![Winning lotto strategy, systems to Keno games was suggested by a player named Duke.](https://saliu.com/bbs/messages/HLINE.gif)
