---
created: 2025-07-24T21:26:40 (UTC +08:00)
tags: [lotto wheels,lottery systems,software,lottery wheeling,verify,cover,generate wheels,]
source: https://saliu.com/check-wheels.html
author: 
---

# Lotto Wheels Software, Cover, Missing Combinations, Systems

> ## Excerpt
> Run wheeling software to verify the lotto wheels for missing combinations. The user can also generate reduced lottery systems for 5 6-number lotto games.

---
Published September 9, 2004 (4 WE).

<big>• WheelCheck5</big> - lottery wheeling software for 5-number lotto.  
<big>• WheelCheck6</big> - lotto-wheel checking software for 6-number lotteries.  

### <u>1. Software to <i>Verify Lotto Wheels</i> for Missing Combinations and Generate Lotto-6 Systems</u>

The myths never die! The game of blackjack has the myth of card counting. The game of roulette has the myth of wheel bias. Lottery has the myth of _**lotto wheels**_. But is there anything without a myth? Isn't human history an endless mythology about queens and kings, gods and goddesses, heroes and champions? Oh, well, this post is about a more mundane business.

Even if I strongly advise against playing the lottery by using lotto wheels, I still receive messages, and requests, and suggestions to offer lotto wheeling software. Some like this message that plays with inflating the ego balloon:

_"Your monumental Saliu site have everything people want to know and play with lottery. There is an issue that the layman wishes to add to the treasure: COVER and VERIFY in lotto, that a very few have the monopoly to enjoy the performance. Please consider a way to popularize them."_

Yeah, right... monopoly!

Matter of fact, I had already offered freeware for lotto wheeling. Wheel632 is, unquestionably, the best software to generate lotto wheels. The software uses real filters to generate well-balanced wheels, with superior indexing. I also offer free lotto wheeling software: FillWheel, and, especially, LottoWheeler. The programs take any theoretical wheel and fill it with the selections (picks) of the player. There are gazillions of theoretical lotto wheels (a.k.a. _reduced lottery systems_) in this e-world! But there is a problem. How do we know precisely if a particular lotto wheel assures the minimal guarantee it says it covers? If there are holes in the wheel, how do we know what combinations are missing? And that's where WheelCheck6 steps in with authority.

The special lottery wheeling software is designed to work with wheels for lotto-5 and lotto-6 games. Say, you have a 10-number lotto wheel with the _4 of 6_ minimum guarantee, in 3 lines (combinations). Run CheckWheel6 with the _4 of 6_ option. Load the 3-line wheel. Set the _Four filter_ equal to 3. If the lotto wheel has holes, the program will generate them. If you add those combinations to the original wheel, the lotto system will be _full_; i.e. _100% coverage_. The minimum guarantee is assured in an efficient manner.

You might also want to see ALL missing combinations, without plugging the holes. Run WheelCheck6 with the _6 of 6_ option. Load the 3-line lotto wheel again. This time set all but one filter to zero (press _Enter_ without any number). The filter specific to the guarantee (_Four_, in this example) is set to number of lines in the input lotto wheel (3 in this case). The program will generate all _6 of 6_ combinations that are not covered.

### <u>2. Run <i>WheelCheck6</i>: Software to Create and Check Lotto Wheels for Coverage</u>

Nothing could be easier to use, and more logically organized than my lottery software. The program always starts by asking for a previously generated lotto wheel or system. That's the main purpose of this program. You won't always need to verify a lotto wheel. You can also generate your own reduced systems from scratch. Simply create this very simple text file, consisting of one line of 6 numbers: **1 2 3 4 5 6**  
That's exactly one line of 6 numbers separated by one or two blank spaces. You can use MDIEditor and Lotto WE, or any text editor, including Notepad. Save the file with a simple name, such as _6_.

[![Lottery Utilities Software, Tools, Lotto Wheels, Systems.](https://saliu.com/ScreenImgs/lotto-wheels.gif)](https://saliu.com/free-lotto-tools.html)

Let's say you want to create an 18-number lotto wheel with the _4 of 6_ minimum guarantee. Load the text file _6_. Select the _4 of 6_ option, and _18_ as how many lotto numbers to wheel. Do not type any number for the _Four filter_ — just press _Enter_. Accept the default name for the output file (you can choose any name you want, though; up to 255 characters in length). At the end of the run, the program will also indicate the amount of combinations generated. This is the raw method of generating reduced lotto-6 systems. The systems are larger, but highly balanced. All the numbers are treated equally to a maximum possible. The reduced lottery systems offer also good coverage as of _3 of 3, 3 of 4, 4 of 5,_ _5 of 6_, etc.

You can generate smaller lotto wheels by running **Check Wheel 6** in _multiple steps_. First, you can generate a _2 of 6_ lotto-6 wheel as per above. Next, load the _SYS18.26_ previously created wheel. Run the program for the _3 of 6_ option. Set the _Three filter_ to the number of lines in _SYS18.26_ (the program will tell how many lines the system file has). The result is the _SYS18.36_ system. Combine the two wheels: _SYS18.26_ and _SYS18.36_ in one system file. One method is to load both files in **MDIEditor Lotto WE or QEdit**. You can also work at the _**Command Prompt**_, using the **COPY** command to concatenate the two wheel files:

**COPY SYS18.26+SYS18.36 SYS18**

Run CheckWheel6 loading _SYS18_ as the startup file. Select the _4 of 6_ option. Set the _Four filter_ to the number of lines in _SYS18_. The result will be the file _SYS18.46_. Load that file in the text editor, and add to it the previous file _SYS18_. That is the complete 18-number, _4 of 6_ lotto wheel. It is smaller than the wheel generated in the single step (just loading the file _6_, and disabling all filters). Still, the smaller wheel assures 100% the minimum guarantee.

The best lotto wheeling method is running CheckWheel6 in conjunction with Wheel6 (component of **Bright/Ultimate** integrated software packages). Run first Wheel6 with the _3 of 6_ option. Name the output file _18-36.1_. When the program slows down significantly, stop it. Next, run the program for the _4 of 6_ option. Name the output file _18-46.1_. Run the program again for the same guarantee. Name the output file _18-46.2_. Concatenate all output files in one; e.g. Name the output file _18-46_. Run now CheckWheel6 for the _4 of 6_ guarantee. Load the startup wheel file _18-46_. Set the filter _Four_ to total lines in the file. Add to this output file the contents of _18-46_. You have an optimized 18-number wheel. It is highly balanced, as usually. But its content is superior, as the result of the inner proprietary lottery filters in Wheel6. That lotto wheel is also superior from the standpoint of the _lexicographical order_.

Most lotto wheels out there are absolutely lousy. Their only goal is to meet a minimal guarantee. But their lexicographic index is abominable. Their structure is also lousy, since no filtering is applied. On the other hand, the lotto wheels perform worse than other selection methods, including random play. Even the wheels generated by my software lag way behind other features of my software, especially _filtering_ and _pairing_. That's because groups of lotto numbers behave differently from range of drawings to range of drawings. The lotto wheels carry a lot of static. You certainly know what to do with your time and money.

<big>• WheelCheck5</big> does all of the above for the lotto 5 games (5-number wheels) — from _1 of 5_ to _5 of 5_.

![Verify, check lotto wheels and generate lotto-6 systems.](https://saliu.com/HLINE.gif)

[

## Resources in Lottery Software, Systems, Lotto Wheeling

](https://saliu.com/content/lottery.html)Lists the main pages on the subject of lottery, lotto, software, wheels and systems.

-   **[_**Lottery Software Tools, Lotto Wheeling Software Wheels**_](https://saliu.com/free-lotto-tools.html)** from the download site:
-   Wheel-632, Wheel-532, the best on-the-fly wheeling software; applies real lottery filtering.  
    ~ Superseded by the most powerful integrated packages
-   Pick532, Pick532 and, especially, the Bright software packages.  
    
-   Combinations, the universal combinations generator for any lotto, Keno, Powerball, Mega Millions, Euromillions;  
    
-   LexicoWheels, generator of lotto wheels based on lexicographical indexing; applies to any lotto, Keno, Powerball, Mega Millions, Euromillions or two-in-one lotto games.  
    
-   WheelCheck5, WheelCheck6, software to verify lotto wheels for missing combinations and generate lotto-5 and lotto6 systems.  
    
-   LottoWheeler, free wheeling software: fill out lotto wheels with player lotto numbers. Supersedes the old-time favorite FillWheel (still offered). The two pieces of software replace the theoretical lotto numbers in the SYS/WHEEL files by your picks (the lotto numbers you want to play).
-   Shuffle, SuperFormula to shuffle (randomize, randomly arrange) your lotto picks. Repeat the randomizing procedure several times. The randomized wheels can be used directly, without shuffling your lottery picks first.

-   The Main [_**Lotto, Lottery, Software**_](https://saliu.com/LottoWin.htm), Strategy, Wheeling Page.  
    Presenting software to create free winning lotto, lottery strategies, systems based on mathematics. Get your lotto systems or wheels, the best lottery, lotto software, combinations, winning numbers.
-   [_**Lotto Wheels for Lotto Games Drawing 5, 6, 7 Numbers**_](https://saliu.com/lotto_wheels.html).  
    The most advanced theory of lotto wheels or reduced lottery systems. Get also original lotto wheels for lotto games drawing 5, 6, or 7 numbers: Balanced, randomized, and free.
-   The myth of [_**Lotto Wheels, Abbreviated, Reduced Lottery Systems**_](https://saliu.com/bbs/messages/11.html).  
    Statistical analysis of real lottery data proves the negative effect of playing static (unbalanced or split) lotto wheels.
-   [_**Design Lotto Wheels Manually, Lotto Wheeling Software**_](https://saliu.com/lottowheel.html).
-   **Free** [_**Lottery Wheeling Software for Players of Lotto Wheels**_](https://saliu.com/bbs/messages/857.html).
-   Fill out lotto wheels with player's picks (numbers to play); presenting FillWheel, LottoWheeler lottery wheeling software, a.k.a. lotto wheeler.
-   WHEEL-632 available as the [_**Best On-The-Fly Wheeling Software**_](https://saliu.com/bbs/messages/wheel.html) — applies real lottery filtering.
-   [_**Software to Verify Lotto Wheels**_](https://saliu.com/check-wheels.html) for missing combinations and generate lotto 5, 6 abbreviated (reduced) systems.
-   [_**Copyright Lottery Numbers, Combinations, Lotto Wheels, Mathematical Relations, Formulas, Equations**_](https://saliu.com/copyright.html).
-   [_**Balanced Lotto Wheels, Lexicographical Order Lottery Wheeling**_](https://saliu.com/bbs/messages/772.html).
-   [_**Check WHEEL System, Lotto Wheels Winners**_](https://saliu.com/bbs/messages/90.html).
-   Download [**Lotto Wheels Software**](https://saliu.com/infodown.html) **Generators**:

![The best software to verify lotto wheels and systems for cover and guarantee: WheelCheck.](https://saliu.com/HLINE.gif)

**[Home](https://saliu.com/index.htm) | [Search](https://saliu.com/Search.htm) | [New Writings](https://saliu.com/bbs/index.html) | [Fundamental Formula](https://saliu.com/formula.htm) | [Odds, Generator](https://saliu.com/calculator_generator.html) | [Contents](https://saliu.com/content/index.html) | [Forums](https://forums.saliu.com/) | [Sitemap](https://saliu.com/sitemap/index.html)**

![Run free lottery software to verify the lotto wheels for missing combinations.](https://saliu.com/HLINE.gif)
