---
created: 2025-07-24T21:26:40 (UTC +08:00)
tags: [lotto,jackpot,all numbers,software,lottery,system,professors,UK,National Lottery,6 49,win,play,syndicate,lotto group,Sudoku,strategy,]
source: https://saliu.com/all-lotto-numbers.html
author: 
---

# All Lotto Numbers: Professors Win UK Lottery Jackpot Sudoku

> ## Excerpt
> 17 British Professors won the UK National Lottery 6/49 lotto jackpot. Syndicate of professors played sixline lotto: All numbers in 649 lottery game.

---
<big>•</big> It happened on October 21, 2006 in The United Kingdom National Lottery. It generated some buzz around the world because, reportedly, the lotto players were also professors! The players themselves stated that they used mathematics in designing their strategy. Actually, only one or two of the lottery club members were real professors...

They had organized a lottery syndicate. The syndicate consisted of 17 staff members at Bradford University and College in Britain. The group started playing lotto together since 1994. They hadn't had spectacular success up until this year. They applied their winning strategy (_playing all the lotto numbers_) for the past four years only.

_"We just thought that if all the numbers are in use, we must have a good chance of winning and it has proved so, though you never really think it will happen to you, "_ the syndicate leader said.

Apparently, they played 17 tickets (17 lotto-6 combinations) in the _6 of 49_ lotto game played in Britain. That amount of tickets - 17 - looks somehow odd (pun intended)!

<big>•</big> I'll tell you the real mathematical story right here. I have been doing similar lottery strategies since the early 1990s. I call this type of lotto playing **SHUFFLE**. Nobody contests the fact that I was the first lottery analyst to come up with the idea and also the software. Matter of fact, my lotto shuffling software is still the only one sporting the feature.

![Lotto software of random combinations has multiple options, including shuffling.](https://saliu.com/ScreenImgs/random-lotto-options.gif)

![Lotto shuffling software uses most filters, but filtering must not be too tight.](https://saliu.com/ScreenImgs/lotto-clusters-filters.gif)

![Lotto software generates group combinations that consist of all numbers in a lotto game.](https://saliu.com/ScreenImgs/all-lotto-numbers.gif)

It resembles shuffling a deck of cards. All the cards remain in the deck. In my shuffling method, all lotto numbers remain in the “deck”, as it were. We play the numbers in 9 groups of 6 numbers apiece (the case of _6 from 49_ lottery game). If we play only 8 tickets, we miss one number: 49. Thus, we add lotto number 49 in the 9th line, and then complete the ticket by adding five numbers previously selected. The simplest method is shown - but it is strongly recommended **not to play** such tickets!

1,2,3,4,5,6  
7,8,9,10,11,12  
37,38,39,40,41,42  
...  
49, 1, 8, 15, 22, 30  

I also released special software to handle **lotto shuffling**. The programs have been freeware since no later than 1994:

SHUF-5  
SHUF-6

The two lotto programs are no longer available as standalone as they were superseded by far more potent functions. The **shuffling** feature was added to far more powerful software. The improvement went even higher: The **Bright** lotto software packages. Beginning 2015, the **Ultimate Lottery Software** applications came to fruition.

_Shuffling all lotto numbers_ is present in the **Combine** programs. Here is a good example of playing all lotto 6/49 numbers as generated by **Combine6** (the **S = Shuffle** option):

Cluster #1:  
25 43 41 47 32 3  
2 20 12 8 33 14  
27 31 24 16 36 22  
6 48 15 9 4 29  
13 11 1 46 17 23  
7 18 34 44 42 19  
10 26 35 38 5 49  
39 21 37 45 28 40  
30 25 20 24 4 23  

The **cluster** represents the group that contains all the lotto _6 of 49_ numbers, which are unique, except for the last ticket. The last combination repeats 5 of the lotto numbers previously generated: 1 number from line #1, 1 from line 2, 1 from line 3, 1 from line 4, and 1 from line #5. By the way, none of the lines in the cluster above repeat any 6-number groups from past lotto drawings. Past drawings do not repeat in hundreds of thousands, even millions of combinations sometimes — in _delta_ format inclusively.

-   In a vast majority of cases, no past lotto combination will repeat in hundreds of thousands of drawings, including _simulated_ drawings (combinations in the _SIM_ files). Just look at the column _Del6_ in the _W6.1_ report. The _median_ is over _780,000_. The _Del6_ filter eliminates past combinations in the _D6_ data file in _delta_ format. That is, the program eliminates not only _straight_ sets (exact 6-number groups), but also combinations with the same _differences_ (deltas) between adjacent numbers.
-   We must employ at least the _Del6_ filter as we deal with possibly _trillions_ of distinct clusters. The staggering amount of unique clusters (matrices) also requires patience when generating lotto combinations by the **Shuffle** function.

<big>•</big> I am NOT saying that the British syndicate used my lottery strategy. I did NOT expressly give any player or group of players exact strategies of applying the lotto shuffle. But they might have had knowledge of the **shuffle** feature in my lotto software. Chances are good for that happening. My lottery software has a pretty large following worldwide. A number of visitors are referred to my Web site via searches such as _sixline lotto_ or _ion saliu six-line lottery groups_ or _lotto shuffle_ or _play all lotto numbers_.

Of course, IP addresses can be matched in the Web server log files for quite some time back. I want to stress that my software was totally free — no strings attached — basically until 2007. It was free — you won, all winnings were yours. Besides, the “shuffle” strategy applied by the professors is quite illogical. Why 17 tickets? Just **9** 6-number combinations were needed. If they played two clusters, then 18 lotto tickets should have been played. Looks like they eliminated the last line in the second cluster so that every player played one ticket! Lucky they didn't eliminate the winning ticket!

Or, perhaps, they were very efficient! A lotto 6/49 cluster consists of **8** 6-number combinations. So they added two clusters to have 16 full 6-liners. The line #9 in each cluster consists of one number. They put together the two numbers in the last lines and added 4 more unique numbers. Thus, they came up with 17 tickets or one ticket per player. Payment-wise, that is; they played the tickets together.

<big>•</big> The story reopened my appetite for my shuffling strategy. Granted, the early computers were inept at handling this type of combination generating strategy. The computer must be adequately fast to handle such a daunting task. Generating all the lotto clusters in lexicographic order involves very complex mathematics and very difficult computer science. Even the fast computers of today allow for random generation only; hence the term **shuffle**.

I run a faster computer now. I can still use a faster PC! The lotto combination generator is slow even for cases with multiple-hit strategies. The good news is that the computers are getting much faster by the year.

I have a lottery data file (_D6_) with over 12,000,000 combinations (12 million for a 6/49 lottery game). Most of you know that a D6 file consists of lotto-6 real drawings (past winning numbers) on top of thousands or millions of _SIM_ulated lotto draws. I remember a time when the personal computers could barely handle data files with only 100 drawings!

My lotto software has this drawback that I fixed in version 4.0 of **MDIEditor And Lotto WE**: _**Inner filters**_. The _**inner filters**_ cannot be disabled by the user. The frequency allowed by the inner filters is adequately statistics-wise - but not 100%. The combination generators can eliminate all combinations in the D6 file - and then some. _Deltas_ (differences between adjacent lotto numbers) are applied to the past drawings as well. For example, this is a combination in the _D6_ lottery file (_real_ results plus _simulated_ combinations):

6, 13, 26, 33, 38, 48

The following combinations will be eliminated (plus more):

6, 13, 26, 33, 38, 48  
5, 12, 25, 32, 37, 47  
7, 14, 27, 34, 39, 49

-   The filter that eliminates **all** past combinations in the D6 file is also based on _deltas_, even tougher than in my formerly free lottery software. That filter is successful 50%+ of the time for an amount of over 700,000 lines in D6 (referring to the _6/49_ case). The filter is named _Del6_. I set the filter _Del6 minimum = 700000_ — I do see lotto _"sudokus"_ now. I had not seen clusters for such a filter setting prior to November 2015! Hooray! Hurrahs!
-   In the case of the Pennsylvania Lottery _5 of 43_ lotto game, my _D5_ data file has over 4 million combinations. The filter for this game is named _Del5_ — it eliminates 5-number groups in _delta_ format. That filter is successful in 50%+ cases (1 in 2 draws) for an amount of over 50,000 lines in D5. _50,000_ represents the median of the filter. I see lotto clusters in a reasonably short amount of time for _Del5 minimum = 50000_.
-   If the generating process is too slow (i.e. no lotto combos for a long time), stop the program and lower the level of _Del5_ or _Del6_ until you do see lotto clusters. Select clusters to play from the end of the output files (where you saved the shuffled clusters).
    -   The problem with some filters was triggered by the ordering of the lines in the cluster (matrix). The 6-number lines were NOT sorted in ascending order as in other combination-generating methods in my software. I had decided to NOT sort the cluster lines because applying the _shuffle_ feature would have never worked with lotto games. It has always worked with pick (digit) games because they are never sorted (as the order is of the essence in such lottery games).
    -   In November of 2015 I decided to make changes... again. I noticed how much faster the computers are — and they get faster month after month. As per the above, the _**shuffle**_ function in my lotto 5/6 software does work better with high (tight) levels of the _Del5_ and _Del6_ filters.
    -   The only filters that do NOT work with the _**shuffle**_ feature are those that eliminate _individual_ numbers. This lottery feature must work with **all lotto numbers in the game**. The following lottery filters will not work with **shuffle**: _One, Any1, Ver1_. A screen in the program warns the user that certain filters must not be enabled. You too can figure out by enabling filters with very loose settings that usually generate lots of combosnations very fast. If you see no combos in a long time — those filters do not work with **shuffle**.
-   I discovered more evidence that the time of running is very important. That is, the probability to generate the winning combinations in the first few combinations is very low. I noticed with these types of runs: 1827 combinations and 914 combinations (actually, they were components of clusters). No duplicates encountered.
-   The amount of winning combinations in the longest run is two times better than random play. The amount of winning combinations in the longest run is FOUR times better than the results in the low run (by comparison to the odds of winning).
-   That way, I will say that patience is rewarded, especially for a strategy such as **lotto shuffle**. The University syndicate was definitely rewarded.
-   I think of another way of beating them odds. Go back, say, 100 real drawings in your data file. It means you delete the top 100 lines in your file and save it under a new name (e.g. _Data-6.2_). Create also a new D6 file (e.g. _D6.2_). Generate clusters by running the **shuffle** function.
-   Sort the output file in ascending order as you do with your data files. The clusters are saved to disk unordered. Check now the output file against your original results file, specifically against the 100 drawings that you deleted before running shuffle. If you do not see any major lottery prize (e.g. jackpot wins or second tier prizes), it is a good time to play clusters from that output file.
-   It is like saving money for 100 lottery drawings, or in the neighborhood of two years. Keep in mind, axiomatic one, the British University lottery syndicate played for longer...
    
    The winning lotto numbers were: 15, 18, 23, 31, 37, 49.  
    The combination passes the _inner filters_ in _**LotWon**_ lotto software or the _inner filters_ in **MDIEditor Lotto**.  
    IS
    
    ![Lotto Sudoku or Sudoku Lotto?](https://saliu.com/HLINE.gif)
    
    <big>•</big> That puzzle or game that I don't care about — Sudoku — certainly shows resemblance to my lotto clusters. (No debate, the _Sudoku_ thing came big to life a few years after my lotto clustering or shuffling software was offered as shareware in 1990-1991. _Sudoku_ came to prominence in 2005. All the numbers in the cluster (or _9x9 Sudoku grid_) are unique. My old-timer lotto buddies did not miss the resemblance. Of course, they wanted me to write the best software there is to generate every possible _Sudoku_ grid (one trillion?) But what for?! I'd rather write software to generate every possible lotto cluster — the effort can be worth millions, if not billions, of dollars over time.
    
    The number of unique lotto clusters (groups containing all lotto numbers, all unique) is gigantically higher than total possible _Sudoku_ grids. Today's computers are simply babies who can't walk, while asked to run Olympic dash races against adults! I offered some accurate calculations regarding the number of possible lotto wheels _transpositions_ (rotations, etc.) Read:
    
    -   [_**Copyright © lotto, lottery numbers, combinations, wheels, mathematical relations, formulae, equations**_](https://saliu.com/copyright.html).
    
    The calculation of total lotto clusters involves the factorial (e.g. 49!). Today's computers can only generate random lotto clusters. Generating lexicographic clusters would take many, many years. My probability program **Shuffle** can also generate lotto clusters. The probability software also _shuffles_ all lotto numbers and randomly arranges them in one vector (line). The 49-number vector, for example, can be broken into 7 lines of 7 numbers each. Thus, a 49-number vector was converted to a 7-by-7 matrix.
    
    My combinatorial software **PermuteCombine** might be the only program today capable of generating every possible Sudoku grid. Actually, the _Arrangements_ function in **PermuteCombine** is capable of such a daunting task (provided that a capable computer is available).
    
    If you want to play with _Sudoku_ — why so often? Wouldn't be a whole loto more rewarding to play with the _all-lotto-number_ clusters? You can even eliminate 6-number combinations that came out in the past: The probability for their repeating is negligible in a lifetime. Meanwhile, playing all-lotto-number clusters offers a far-better-than-negligible chance to win big money.
    
    ![There is a connection between lotto and Sudoku.](https://saliu.com/HLINE.gif)[](https://saliu.com/content/lottery.html)
    
    ## [<u>Resources in Lottery Software, Lotto Wheeling</u>](https://saliu.com/content/lottery.html)
    
    -   [_**Lotto Software**_: **7-by-7-Number Combinations** _**(Matrix, Cluster) in Lotto 6 / 49 Games**_](https://saliu.com/7by7-lotto-6-49.html).  
        An improvement over the lottery strategy presented here: shuffling or randomizing the lotto 6/49 numbers. Generate perfect squares or matrices of 7 lotto numbers per combination, 7 lines in total.
    -   High-Powered [_**Lotto-6 Software**_](https://saliu.com/lotto6-software.html).
    -   The Main [_**Lotto, Lottery, Software, Strategy**_](https://saliu.com/LottoWin.htm) Page.  
        Presenting software to create free winning lotto, lottery strategies, systems based on mathematics. Get your lotto systems or wheels, the best lottery, lotto software, combinations, winning numbers.
    -   [_**Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies**_](https://saliu.com/Newsgroups.htm).  
        Read a genuine analysis of Excel spreadsheets applied to lottery and lotto developing of software, systems, and strategies. Combining Excel analysis with powerful lottery and lotto software programmed by this author, _Parpaluck_.
    -   [_**<u>MDIEditor Lotto WE</u>: Tutorial, Software User Guide, Manual, Book, ebook**_](https://saliu.com/MDI-lotto-guide.html).  
        ~ Also applicable to LotWon lottery, lotto software; plus Powerball, Mega Millions, Euromillions.
    -   [_**Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps, Programs**_](https://saliu.com/forum/lotto-book.html).
    -   _"My kingdom for a good lotto tutorial!"_ [_**Lotto, Lottery Strategy Tutorial**_](https://saliu.com/bbs/messages/818.html).
    -   [_**Documentation, Help: MDIEditor Lotto WE, Lottery Software, Strategy Tutorial**_](https://saliu.com/mdi_lotto.html).
    -   [**<u>Lottery Skip Systems</u>**](https://saliu.com/skip-strategy.html): _**Lotto, Powerball, Mega Millions, Euromillions**_.
    -   [_**Lottery Utility Software**_](https://saliu.com/lottery-utility.html): Pick-3, 4 Lottery, Lotto-5, 6, Powerball, Mega Millions, Euromillions.
    -   Practical [_**lotto filtering in lottery software**_](https://saliu.com/filters.html).
    -   [_**Jackpot Lottery Strategy: 12-Number Combinations, Lotto-6 Wheels, Pioneer Software**_](https://saliu.com/lotto-jackpot-lost.html).
    -   Download [**lottery software, lotto software**](https://saliu.com/infodown.html).
    
    ![University Professors played sixline lotto: All the numbers in the 6-49 game.](https://saliu.com/HLINE.gif)
    
    **[Home](https://saliu.com/index.htm) | [Search](https://saliu.com/Search.htm) | [New Writings](https://saliu.com/bbs/index.html) | [Fundamental Formula](https://saliu.com/formula.htm) | [Odds, Generator](https://saliu.com/calculator_generator.html) | [Contents](https://saliu.com/content/index.html) | [Forums](https://forums.saliu.com/) | [Sitemap](https://saliu.com/sitemap/index.html)**
    
    ![Lottery software generates all numbers in lotto games in Sudoku grids like.](https://saliu.com/images/HLINE.gif)
