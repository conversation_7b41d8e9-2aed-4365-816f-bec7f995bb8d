# 計算邏輯測試與比較文檔

## 概述

本文檔比較 Julia 專案中的計算邏輯與 Ion Saliu 原始文件中描述的計算邏輯，識別差異並提供修正建議。

## 1. Skip（跳躍）計算邏輯比較

### 原始文件邏輯
根據 `notes/saliu/解釋「skip」概念在彩票軟體中的應用與兩種計算方式的異同.md`：

1. **MDIEditor Lotto WE 方式**：跳躍次數 = 兩次命中之間完全錯過的抽獎次數
   - 範例：號碼在第1次和第3次命中，跳躍值 = 1（第2次被跳過）
   - 「跳躍值為7」意味著號碼已經有8次開獎未中

2. **SkipSystem 方式**：跳躍次數 = 兩次命中之間經過的抽獎總數（包括最後一次命中）
   - 範例：號碼在第1次和第3次命中，跳躍值 = 2（3-1=2）
   - 與 FFG 理論更一致

### 當前實現檢查
**需要檢查的文件**：
- `src/skip_analyzer.jl` 中的 `get_current_skip()` 函數
- `src/ffg_calculator.jl` 中的相關計算

**潛在問題**：
- [ ] 確認當前實現使用哪種計算方式
- [ ] 驗證是否與 FFG 理論一致
- [ ] 檢查過濾器設定是否需要減1調整

## 2. FFG 中位數計算邏輯比較

### 原始文件邏輯
根據 Julia 文檔中的 `cholesky` 函數和 FFG 理論：

1. **FFG 中位數**：表示事件再次發生所需的試驗次數
2. **確定性程度 (DC)**：通常使用 1/e ≈ 0.368 或其他值
3. **計算公式**：N = FFG_median，不需要額外減1

### 當前實現檢查
**需要檢查的文件**：
- `src/ffg_calculator.jl` 中的 `calculate_ffg_median()` 函數
- `test_ffg_calculator.jl` 中的測試邏輯

**從測試文件觀察到**：
```julia
ffg_median = calculate_ffg_median(calc_dc, test_number, data)
skip_prob = compute_skip_probability(calc_dc, current_skip, ffg_median)
```

**潛在問題**：
- [ ] 確認 FFG 中位數計算公式的正確性
- [ ] 驗證不同 DC 值的處理邏輯
- [ ] 檢查機率計算是否符合原始理論

## 3. 過濾器計算邏輯比較

### 原始文件邏輯
根據需求文件，過濾器應該：

1. **ONE, TWO, THREE, FOUR, FIVE, SIX 過濾器**：計算特定條件的歷史值
2. **中位數、平均值、標準差**：統計分析指標
3. **過濾效率**：減少組合數量的效果

### 當前實現檢查
**需要檢查的文件**：
- `src/filter_engine.jl` 中的各種過濾器實現
- 統計計算函數

**潛在問題**：
- [ ] 確認各過濾器的計算邏輯
- [ ] 驗證統計指標的計算方式
- [ ] 檢查過濾效率的評估方法

## 4. Wonder Grid 配對頻率計算

### 原始文件邏輯
Wonder Grid 應該：

1. **配對頻率**：計算每個數字與其他數字一起出現的頻率
2. **關鍵號碼選擇**：基於配對頻率的算法
3. **頂級配對篩選**：選擇最常見的配對組合

### 當前實現檢查
**需要檢查的文件**：
- `src/wonder_grid_engine.jl` 中的配對計算
- `src/markov_analyzer.jl` 中的相關邏輯

**潛在問題**：
- [ ] 確認配對頻率計算的準確性
- [ ] 驗證關鍵號碼選擇算法
- [ ] 檢查篩選邏輯的實現

## 5. 數據排序與處理邏輯

### 原始文件邏輯
根據需求文件：

1. **數據排序**：最新開獎結果在檔案頂端
2. **數據合併**：真實數據與模擬數據的整合
3. **錯誤檢查**：格式驗證和異常處理

### 當前實現檢查
**需要檢查的文件**：
- `src/data_manager.jl` 中的數據處理邏輯

**潛在問題**：
- [ ] 確認數據排序的正確性
- [ ] 驗證合併邏輯的實現
- [ ] 檢查錯誤處理的完整性

## 測試建議

### 1. Skip 計算測試
```julia
# 測試兩種 skip 計算方式
function test_skip_calculation_methods()
    # 創建測試數據
    # 驗證 MDIEditor 方式 vs SkipSystem 方式
    # 確認與 FFG 理論的一致性
end
```

### 2. FFG 中位數測試
```julia
# 測試 FFG 中位數計算
function test_ffg_median_calculation()
    # 使用已知數據驗證計算結果
    # 測試不同 DC 值的影響
    # 確認機率計算的正確性
end
```

### 3. 過濾器邏輯測試
```julia
# 測試各種過濾器的計算邏輯
function test_filter_calculations()
    # 驗證 ONE, TWO, THREE 等過濾器
    # 測試統計指標計算
    # 確認過濾效率評估
end
```

## 修正優先級

1. **高優先級**：Skip 計算邏輯的一致性
2. **中優先級**：FFG 中位數計算的準確性
3. **低優先級**：過濾器細節的優化

## 後續行動

1. 詳細檢查每個計算模組的實現
2. 創建對應的單元測試
3. 與原始理論進行數值驗證
4. 修正發現的邏輯差異