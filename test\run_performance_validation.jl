# Wonder Grid Lottery System - 性能驗證和基準測試
# 驗證系統是否達到所有性能目標

using Test
using Dates
using Statistics
using BenchmarkTools

println("⚡ Wonder Grid Lottery System - 性能驗證測試")
println("=" ^ 60)

# 性能目標定義
const PERFORMANCE_TARGETS = Dict(
    "skip_calculation_ms" => 10.0,      # Skip 計算 < 10ms
    "pairing_calculation_ms" => 20.0,   # 配對計算 < 20ms
    "wonder_grid_generation_ms" => 5000.0,  # Wonder Grid 生成 < 5s
    "memory_usage_mb" => 100.0,         # 記憶體使用 < 100MB
    "cache_hit_rate" => 0.7,            # 快取命中率 > 70%
    "parallel_efficiency" => 0.6,       # 並行效率 > 60%
    "memory_savings_percentage" => 80.0, # 記憶體節省 > 80%
    "system_initialization_ms" => 2000.0 # 系統初始化 < 2s
)

# 測試數據大小
const TEST_DATA_SIZES = Dict(
    "small" => 100,
    "medium" => 1000,
    "large" => 5000
)

# 生成性能測試數據
function generate_performance_test_data(size::Int)
    draws = LotteryDraw[]
    base_date = Date(2020, 1, 1)
    
    for i in 1:size
        Random.seed!(i)  # 確保可重現性
        numbers = sort(sample(1:39, 5, replace=false))
        date = base_date + Day(i-1)
        push!(draws, LotteryDraw(numbers, date, i))
    end
    
    return draws
end

println("📊 準備性能測試數據...")
test_datasets = Dict{String, Vector{LotteryDraw}}()
for (size_name, size_value) in TEST_DATA_SIZES
    test_datasets[size_name] = generate_performance_test_data(size_value)
    println("   $size_name 數據集: $size_value 筆記錄")
end

# 載入系統
include("../src/wonder_grid_system.jl")

@testset "性能驗證測試套件" begin
    
    @testset "系統初始化性能" begin
        println("\n🚀 測試系統初始化性能...")
        
        for (size_name, dataset) in test_datasets
            println("  測試 $size_name 數據集初始化...")
            
            # 測量初始化時間
            init_time = @elapsed begin
                system = WonderGridSystem(dataset)
            end
            
            init_time_ms = init_time * 1000
            target_ms = PERFORMANCE_TARGETS["system_initialization_ms"]
            
            @test init_time_ms < target_ms
            
            println("    初始化時間: $(round(init_time_ms, digits=2))ms (目標: < $(target_ms)ms)")
            
            if init_time_ms < target_ms
                println("    ✅ 初始化性能達標")
            else
                println("    ❌ 初始化性能未達標")
            end
        end
    end
    
    @testset "Skip 計算性能" begin
        println("\n⚡ 測試 Skip 計算性能...")
        
        for (size_name, dataset) in test_datasets
            println("  測試 $size_name 數據集 Skip 計算...")
            
            system = WonderGridSystem(dataset)
            test_numbers = [1, 5, 10, 15, 20]
            
            # 基準測試
            benchmark_result = @benchmark begin
                for number in $test_numbers
                    calculate_skip($system, number)
                end
            end
            
            avg_time_ms = mean(benchmark_result.times) / 1_000_000  # 轉換為毫秒
            target_ms = PERFORMANCE_TARGETS["skip_calculation_ms"]
            
            @test avg_time_ms < target_ms
            
            println("    平均時間: $(round(avg_time_ms, digits=2))ms (目標: < $(target_ms)ms)")
            println("    記憶體分配: $(benchmark_result.allocs) 次")
            
            if avg_time_ms < target_ms
                println("    ✅ Skip 計算性能達標")
            else
                println("    ❌ Skip 計算性能未達標")
            end
        end
    end
    
    @testset "配對分析性能" begin
        println("\n🤝 測試配對分析性能...")
        
        for (size_name, dataset) in test_datasets
            println("  測試 $size_name 數據集配對分析...")
            
            system = WonderGridSystem(dataset)
            test_pairs = [(1, 2), (5, 10), (15, 20)]
            
            # 基準測試
            benchmark_result = @benchmark begin
                for (num1, num2) in $test_pairs
                    calculate_pairing_frequency($system, num1, num2)
                end
            end
            
            avg_time_ms = mean(benchmark_result.times) / 1_000_000
            target_ms = PERFORMANCE_TARGETS["pairing_calculation_ms"]
            
            @test avg_time_ms < target_ms
            
            println("    平均時間: $(round(avg_time_ms, digits=2))ms (目標: < $(target_ms)ms)")
            
            if avg_time_ms < target_ms
                println("    ✅ 配對分析性能達標")
            else
                println("    ❌ 配對分析性能未達標")
            end
        end
    end
    
    @testset "Wonder Grid 生成性能" begin
        println("\n🎯 測試 Wonder Grid 生成性能...")
        
        # 只測試中等和大型數據集
        for size_name in ["medium", "large"]
            dataset = test_datasets[size_name]
            println("  測試 $size_name 數據集 Wonder Grid 生成...")
            
            system = WonderGridSystem(dataset)
            grid_size = 30
            
            # 測量生成時間
            generation_time = @elapsed begin
                grid = generate_wonder_grid(system, grid_size)
            end
            
            generation_time_ms = generation_time * 1000
            target_ms = PERFORMANCE_TARGETS["wonder_grid_generation_ms"]
            
            @test generation_time_ms < target_ms
            
            println("    生成時間: $(round(generation_time_ms, digits=2))ms (目標: < $(target_ms)ms)")
            
            if generation_time_ms < target_ms
                println("    ✅ Wonder Grid 生成性能達標")
            else
                println("    ❌ Wonder Grid 生成性能未達標")
            end
        end
    end
    
    @testset "記憶體使用性能" begin
        println("\n💾 測試記憶體使用性能...")
        
        for (size_name, dataset) in test_datasets
            println("  測試 $size_name 數據集記憶體使用...")
            
            # 測量記憶體使用
            memory_before = Base.gc_bytes()
            system = WonderGridSystem(dataset)
            
            # 執行一些操作
            for i in 1:10
                calculate_skip(system, rand(1:20))
                calculate_pairing_frequency(system, rand(1:20), rand(1:20))
            end
            
            memory_after = Base.gc_bytes()
            memory_used_mb = (memory_after - memory_before) / (1024 * 1024)
            target_mb = PERFORMANCE_TARGETS["memory_usage_mb"]
            
            @test memory_used_mb < target_mb
            
            println("    記憶體使用: $(round(memory_used_mb, digits=2))MB (目標: < $(target_mb)MB)")
            
            if memory_used_mb < target_mb
                println("    ✅ 記憶體使用性能達標")
            else
                println("    ❌ 記憶體使用性能未達標")
            end
        end
    end
    
    @testset "快取系統性能" begin
        println("\n💾 測試快取系統性能...")
        
        dataset = test_datasets["medium"]
        system = WonderGridSystem(dataset)
        test_numbers = [1, 2, 3, 4, 5]
        
        # 執行重複計算以觸發快取
        println("  執行重複計算以建立快取...")
        
        # 第一輪：冷快取
        for number in test_numbers
            calculate_skip(system, number)
        end
        
        # 第二輪：熱快取
        for number in test_numbers
            calculate_skip(system, number)
        end
        
        # 檢查快取性能
        if system.optimized_engine !== nothing
            stats = get_engine_statistics(system.optimized_engine)
            cache_hit_rate = stats["cache_hit_rate"]
            target_rate = PERFORMANCE_TARGETS["cache_hit_rate"]
            
            @test cache_hit_rate >= target_rate
            
            println("    快取命中率: $(round(cache_hit_rate * 100, digits=1))% (目標: ≥ $(round(target_rate * 100, digits=1))%)")
            
            if cache_hit_rate >= target_rate
                println("    ✅ 快取系統性能達標")
            else
                println("    ❌ 快取系統性能未達標")
            end
        else
            println("    ⚠️ 優化引擎未啟用，跳過快取測試")
        end
    end
    
    @testset "記憶體節省性能" begin
        println("\n📦 測試記憶體節省性能...")
        
        dataset = test_datasets["large"]
        
        # 測試緊湊數據結構的記憶體節省
        optimized_engine = OptimizedFilterEngine(dataset, use_compact_data=true)
        stats = get_engine_statistics(optimized_engine)
        
        if haskey(stats, "compact_data")
            compact_stats = stats["compact_data"]
            savings_percentage = compact_stats["memory_savings_percentage"]
            target_percentage = PERFORMANCE_TARGETS["memory_savings_percentage"]
            
            @test savings_percentage >= target_percentage
            
            println("    記憶體節省: $(round(savings_percentage, digits=1))% (目標: ≥ $(target_percentage)%)")
            println("    原始大小: $(round(compact_stats["original_size_bytes"] / 1024, digits=1))KB")
            println("    緊湊大小: $(round(compact_stats["memory_usage_bytes"] / 1024, digits=1))KB")
            
            if savings_percentage >= target_percentage
                println("    ✅ 記憶體節省性能達標")
            else
                println("    ❌ 記憶體節省性能未達標")
            end
        else
            println("    ⚠️ 緊湊數據結構未啟用")
        end
    end
    
    @testset "並行計算性能" begin
        println("\n🚀 測試並行計算性能...")
        
        if Threads.nthreads() > 1
            dataset = test_datasets["large"]
            test_numbers = collect(1:20)
            
            println("  執行並行性能測試...")
            println("    可用執行緒數: $(Threads.nthreads())")
            
            # 序列基準
            sequential_time = @elapsed begin
                sequential_results = Dict{Int, Int}()
                for number in test_numbers
                    sequential_results[number] = calculate_skip_sequential(dataset, number)
                end
            end
            
            # 並行測試
            parallel_time = @elapsed begin
                parallel_results = calculate_all_skips_parallel(dataset, test_numbers)
            end
            
            # 計算並行效率
            speedup = sequential_time / parallel_time
            efficiency = speedup / Threads.nthreads()
            target_efficiency = PERFORMANCE_TARGETS["parallel_efficiency"]
            
            @test efficiency >= target_efficiency
            
            println("    序列時間: $(round(sequential_time * 1000, digits=2))ms")
            println("    並行時間: $(round(parallel_time * 1000, digits=2))ms")
            println("    加速比: $(round(speedup, digits=2))x")
            println("    並行效率: $(round(efficiency * 100, digits=1))% (目標: ≥ $(round(target_efficiency * 100, digits=1))%)")
            
            if efficiency >= target_efficiency
                println("    ✅ 並行計算性能達標")
            else
                println("    ❌ 並行計算性能未達標")
            end
        else
            println("  ⚠️ 跳過並行計算測試（需要多執行緒環境）")
        end
    end
    
    @testset "整體系統性能" begin
        println("\n🎯 測試整體系統性能...")
        
        dataset = test_datasets["medium"]
        system = WonderGridSystem(dataset)
        
        println("  執行綜合性能測試...")
        
        # 綜合工作負載測試
        total_time = @elapsed begin
            # Skip 計算
            for i in 1:20
                calculate_skip(system, rand(1:20))
            end
            
            # 配對分析
            for i in 1:10
                num1, num2 = rand(1:20), rand(1:20)
                calculate_pairing_frequency(system, num1, num2)
            end
            
            # Wonder Grid 生成
            generate_wonder_grid(system, 10)
        end
        
        total_time_ms = total_time * 1000
        
        println("    綜合測試時間: $(round(total_time_ms, digits=2))ms")
        
        # 獲取性能報告
        performance_report = get_performance_report(system)
        
        if haskey(performance_report, "performance")
            perf_data = performance_report["performance"]
            if haskey(perf_data, "performance_grade")
                grade = perf_data["performance_grade"]
                println("    系統性能等級: $grade")
                
                @test grade in ["優秀", "良好"]
                
                if grade == "優秀"
                    println("    ✅ 整體系統性能優秀")
                elseif grade == "良好"
                    println("    ✅ 整體系統性能良好")
                else
                    println("    ❌ 整體系統性能需要改進")
                end
            end
        end
        
        # 執行健康檢查
        health_status = system_health_check(system)
        @test health_status == true
        
        if health_status
            println("    ✅ 系統健康檢查通過")
        else
            println("    ❌ 系統健康檢查失敗")
        end
    end
end

println("\n🎉 性能驗證測試完成！")

# 生成性能驗證報告
println("\n📊 生成性能驗證報告...")

function generate_performance_report()
    println("\n📋 Wonder Grid Lottery System - 性能驗證報告")
    println("=" ^ 60)
    
    # 測試環境信息
    println("🖥️ 測試環境:")
    println("   Julia 版本: $(VERSION)")
    println("   執行緒數: $(Threads.nthreads())")
    println("   CPU 核心: $(Sys.CPU_THREADS)")
    println("   總記憶體: $(round(Sys.total_memory() / (1024^3), digits=1))GB")
    
    # 性能目標達成情況
    println("\n🎯 性能目標達成情況:")
    
    target_results = Dict{String, Bool}()
    
    try
        # 創建測試系統
        test_system = WonderGridSystem(test_datasets["medium"])
        
        # Skip 計算性能
        skip_time = @elapsed calculate_skip(test_system, 1)
        skip_time_ms = skip_time * 1000
        skip_target_met = skip_time_ms < PERFORMANCE_TARGETS["skip_calculation_ms"]
        target_results["Skip 計算"] = skip_target_met
        println("   Skip 計算: $(skip_target_met ? "✅" : "❌") $(round(skip_time_ms, digits=2))ms")
        
        # 記憶體使用
        memory_before = Base.gc_bytes()
        temp_system = WonderGridSystem(test_datasets["small"])
        memory_after = Base.gc_bytes()
        memory_used_mb = (memory_after - memory_before) / (1024 * 1024)
        memory_target_met = memory_used_mb < PERFORMANCE_TARGETS["memory_usage_mb"]
        target_results["記憶體使用"] = memory_target_met
        println("   記憶體使用: $(memory_target_met ? "✅" : "❌") $(round(memory_used_mb, digits=2))MB")
        
        # 快取性能
        if test_system.optimized_engine !== nothing
            # 觸發快取
            for i in 1:5
                calculate_skip(test_system, i)
                calculate_skip(test_system, i)  # 重複計算
            end
            
            stats = get_engine_statistics(test_system.optimized_engine)
            cache_hit_rate = stats["cache_hit_rate"]
            cache_target_met = cache_hit_rate >= PERFORMANCE_TARGETS["cache_hit_rate"]
            target_results["快取系統"] = cache_target_met
            println("   快取命中率: $(cache_target_met ? "✅" : "❌") $(round(cache_hit_rate * 100, digits=1))%")
            
            # 記憶體節省
            if haskey(stats, "compact_data")
                compact_stats = stats["compact_data"]
                savings = compact_stats["memory_savings_percentage"]
                savings_target_met = savings >= PERFORMANCE_TARGETS["memory_savings_percentage"]
                target_results["記憶體節省"] = savings_target_met
                println("   記憶體節省: $(savings_target_met ? "✅" : "❌") $(round(savings, digits=1))%")
            end
        end
        
        # 並行計算（如果可用）
        if Threads.nthreads() > 1
            test_numbers = [1, 2, 3, 4, 5]
            
            seq_time = @elapsed begin
                for num in test_numbers
                    calculate_skip_sequential(test_datasets["medium"], num)
                end
            end
            
            par_time = @elapsed begin
                calculate_all_skips_parallel(test_datasets["medium"], test_numbers)
            end
            
            efficiency = (seq_time / par_time) / Threads.nthreads()
            parallel_target_met = efficiency >= PERFORMANCE_TARGETS["parallel_efficiency"]
            target_results["並行計算"] = parallel_target_met
            println("   並行效率: $(parallel_target_met ? "✅" : "❌") $(round(efficiency * 100, digits=1))%")
        end
        
        # 整體評估
        total_targets = length(target_results)
        met_targets = count(values(target_results))
        success_rate = (met_targets / total_targets) * 100
        
        println("\n📈 整體性能評估:")
        println("   達標項目: $met_targets / $total_targets")
        println("   達標率: $(round(success_rate, digits=1))%")
        
        if success_rate >= 90
            println("   🏆 性能等級: 優秀")
        elseif success_rate >= 80
            println("   ✅ 性能等級: 良好")
        elseif success_rate >= 70
            println("   ⚠️ 性能等級: 一般")
        else
            println("   ❌ 性能等級: 需要改進")
        end
        
        # 建議
        println("\n💡 性能優化建議:")
        if !get(target_results, "Skip 計算", true)
            println("   - 考慮進一步優化 Skip 計算算法")
        end
        if !get(target_results, "記憶體使用", true)
            println("   - 啟用更積極的記憶體管理策略")
        end
        if !get(target_results, "快取系統", true)
            println("   - 調整快取大小和策略")
        end
        if !get(target_results, "並行計算", true)
            println("   - 優化並行任務分割和負載平衡")
        end
        
        if success_rate >= 90
            println("\n🎉 恭喜！系統性能完全達到生產級別要求！")
        elseif success_rate >= 80
            println("\n✅ 系統性能良好，可以進行生產部署。")
        else
            println("\n⚠️ 建議在生產部署前進行性能優化。")
        end
        
    catch e
        println("❌ 性能驗證過程中出現錯誤: $e")
    end
end

# 生成報告
generate_performance_report()
