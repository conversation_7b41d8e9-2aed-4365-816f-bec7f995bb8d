#!/usr/bin/env julia

# Test enhanced BacktestingEngine functionality

using Pkg
Pkg.activate(".")

include("src/WonderGridLotterySystem.jl")
using .WonderGridLotterySystem
using StatsBase
using Dates

# Generate realistic test data with known patterns
function generate_realistic_test_data(num_draws::Int = 1000)
    draws = Vector{LotteryDraw}()
    base_date = Date(2020, 1, 1)
    
    # Create some bias towards certain numbers for more realistic testing
    hot_numbers = [5, 12, 18, 23, 31, 37]  # These appear more frequently
    cold_numbers = [2, 9, 16, 25, 34]      # These appear less frequently
    
    for i in 1:num_draws
        draw_date = base_date + Day(i)
        
        if rand() < 0.3  # 30% chance of hot number draw
            selected = sample(hot_numbers, min(2, length(hot_numbers)), replace=false)
            remaining = sample(setdiff(1:39, selected), 5 - length(selected), replace=false)
            numbers = sort([selected; remaining])
        elseif rand() < 0.1  # 10% chance of cold number draw
            selected = sample(cold_numbers, 1, replace=false)
            remaining = sample(setdiff(1:39, selected), 4, replace=false)
            numbers = sort([selected; remaining])
        else
            numbers = sort(sample(1:39, 5, replace=false))
        end
        
        draw = LotteryDraw(numbers, draw_date, i)
        push!(draws, draw)
    end
    
    return draws
end

println("=== Enhanced BacktestingEngine Testing ===")

# Create test data
println("\n1. Creating realistic test data...")
test_data = generate_realistic_test_data(500)
println("Generated $(length(test_data)) test draws from $(test_data[1].draw_date) to $(test_data[end].draw_date)")

# Initialize backtesting engine
println("\n2. Initializing BacktestingEngine...")
start_date = test_data[1].draw_date
end_date = test_data[end].draw_date
backtest_engine = BacktestingEngine(test_data, start_date, end_date)

println("Backtest engine initialized for period: $start_date to $end_date")
println("Historical data count: $(length(backtest_engine.historical_data))")

# Initialize Wonder Grid engine for strategy testing
println("\n3. Setting up Wonder Grid strategy...")
raw_data = [draw.numbers for draw in test_data]
wonder_grid_engine = WonderGridEngine(raw_data)

# Select key numbers
key_numbers = select_key_numbers(wonder_grid_engine)
println("Found $(length(key_numbers)) favorable key numbers: $(key_numbers[1:min(5, length(key_numbers))])")

if !isempty(key_numbers)
    key_number = key_numbers[1]
    println("Using key number: $key_number")
    
    # Test basic backtesting
    println("\n4. Testing basic strategy backtesting...")
    try
        # Generate Wonder Grid combinations
        wg_combinations = generate_combinations(wonder_grid_engine, key_number)
        println("Generated $(length(wg_combinations)) Wonder Grid combinations")
        
        # Run basic backtest
        backtest_result = run_backtest(backtest_engine, wg_combinations, "Wonder Grid Test")
        
        println("Basic Backtest Results:")
        println("  Strategy: $(backtest_result.strategy_name)")
        println("  Total combinations: $(backtest_result.total_combinations)")
        println("  Hit rates:")
        println("    3/5: $(round(backtest_result.hit_rates["3/5"], digits=4))")
        println("    4/5: $(round(backtest_result.hit_rates["4/5"], digits=4))")
        println("    5/5: $(round(backtest_result.hit_rates["5/5"], digits=4))")
        println("  Efficiency ratios:")
        println("    3/5: $(round(backtest_result.efficiency_ratios["3/5"], digits=2))x")
        println("    4/5: $(round(backtest_result.efficiency_ratios["4/5"], digits=2))x")
        println("    5/5: $(round(backtest_result.efficiency_ratios["5/5"], digits=2))x")
        
    catch e
        println("Error in basic backtesting: $e")
    end
    
    # Test Wonder Grid specific backtesting
    println("\n5. Testing Wonder Grid specific backtesting...")
    try
        wg_backtest_result = run_wonder_grid_backtest(backtest_engine, wonder_grid_engine, key_number)
        
        println("Wonder Grid Backtest Results:")
        println("  Strategy: $(wg_backtest_result.strategy_name)")
        println("  Hit rates: 3/5=$(round(wg_backtest_result.hit_rates["3/5"], digits=4)), " *
                "4/5=$(round(wg_backtest_result.hit_rates["4/5"], digits=4)), " *
                "5/5=$(round(wg_backtest_result.hit_rates["5/5"], digits=4))")
        
    catch e
        println("Error in Wonder Grid backtesting: $e")
    end
    
    # Test Wonder Grid with LIE backtesting
    println("\n6. Testing Wonder Grid + LIE backtesting...")
    try
        lie_backtest_result = run_wonder_grid_lie_backtest(backtest_engine, wonder_grid_engine, key_number)
        
        println("Wonder Grid + LIE Backtest Results:")
        println("  Strategy: $(lie_backtest_result.strategy_name)")
        println("  Total combinations: $(lie_backtest_result.total_combinations)")
        println("  Hit rates: 3/5=$(round(lie_backtest_result.hit_rates["3/5"], digits=4)), " *
                "4/5=$(round(lie_backtest_result.hit_rates["4/5"], digits=4)), " *
                "5/5=$(round(lie_backtest_result.hit_rates["5/5"], digits=4))")
        
    catch e
        println("Error in LIE backtesting: $e")
    end
    
    # Test comparative backtesting
    println("\n7. Testing comparative backtesting...")
    try
        # Prepare multiple strategies
        wg_combinations = generate_combinations(wonder_grid_engine, key_number)
        lie_analysis = generate_combinations_with_lie_analysis(wonder_grid_engine, key_number)
        random_combinations = generate_random_combinations(210)
        
        strategies = Dict{String, Vector{Vector{Int}}}(
            "Wonder Grid" => wg_combinations,
            "Wonder Grid + LIE" => lie_analysis["filtered_combinations"],
            "Random Play" => random_combinations
        )
        
        comparative_results = run_comparative_backtest(backtest_engine, strategies)
        
        println("Comparative Backtest Results:")
        for (strategy_name, result) in comparative_results
            println("  $strategy_name:")
            println("    Combinations: $(result.total_combinations)")
            println("    3/5 rate: $(round(result.hit_rates["3/5"], digits=4))")
            println("    4/5 rate: $(round(result.hit_rates["4/5"], digits=4))")
            println("    5/5 rate: $(round(result.hit_rates["5/5"], digits=4))")
        end
        
    catch e
        println("Error in comparative backtesting: $e")
    end
    
    # Test detailed hit rate analysis
    println("\n8. Testing detailed hit rate analysis...")
    try
        wg_combinations = generate_combinations(wonder_grid_engine, key_number)
        detailed_analysis = calculate_detailed_hit_rates(backtest_engine, wg_combinations, backtest_engine.historical_data)
        
        println("Detailed Hit Rate Analysis:")
        println("  Total draws analyzed: $(detailed_analysis["summary"]["total_draws"])")
        println("  Total combinations: $(detailed_analysis["summary"]["total_combinations"])")
        
        best_combo, best_score = detailed_analysis["summary"]["best_combination"]
        worst_combo, worst_score = detailed_analysis["summary"]["worst_combination"]
        
        println("  Best combination: $best_combo (score: $best_score)")
        println("  Worst combination: $worst_combo (score: $worst_score)")
        
        # Show some draw analysis
        draw_keys = collect(keys(detailed_analysis["draw_analysis"]))
        if length(draw_keys) >= 3
            println("  Sample draw analysis:")
            for i in 1:3
                draw_info = detailed_analysis["draw_analysis"][draw_keys[i]]
                println("    Draw $(draw_keys[i]): $(draw_info["draw_numbers"]) -> $(draw_info["total_winners"]) winners")
            end
        end
        
    catch e
        println("Error in detailed analysis: $e")
    end
    
    # Test strategy vs random comparison
    println("\n9. Testing strategy vs random comparison...")
    try
        wg_combinations = generate_combinations(wonder_grid_engine, key_number)
        comparison_result = run_strategy_vs_random_backtest(backtest_engine, wg_combinations, "Wonder Grid")
        
        println("Strategy vs Random Comparison:")
        println("  Strategy superior: $(comparison_result["summary"]["strategy_superior"])")
        println("  Best improvement tier: $(comparison_result["summary"]["best_improvement_tier"])")
        println("  Average improvement: $(round(comparison_result["summary"]["average_improvement"], digits=2))%")
        
        println("  Improvement by tier:")
        for (tier, improvement) in comparison_result["improvement_metrics"]
            println("    $tier: $(round(improvement, digits=2))%")
        end
        
    catch e
        println("Error in strategy vs random comparison: $e")
    end
    
    # Test time series backtesting (if enough data)
    if length(test_data) >= 60
        println("\n10. Testing time series backtesting...")
        try
            wg_combinations = generate_combinations(wonder_grid_engine, key_number)
            time_series_result = run_time_series_backtest(backtest_engine, wg_combinations, 30)
            
            if haskey(time_series_result, "analysis_summary")
                summary = time_series_result["analysis_summary"]
                println("Time Series Analysis:")
                
                if haskey(summary, "trends")
                    trends = summary["trends"]
                    println("  Trends:")
                    println("    3/5: $(trends["3/5"]["trend"]) (mean: $(round(trends["3/5"]["mean"], digits=4)))")
                    println("    4/5: $(trends["4/5"]["trend"]) (mean: $(round(trends["4/5"]["mean"], digits=4)))")
                    println("    5/5: $(trends["5/5"]["trend"]) (mean: $(round(trends["5/5"]["mean"], digits=4)))")
                end
                
                if haskey(summary, "stability_analysis")
                    stability = summary["stability_analysis"]
                    println("  Most stable tier: $(stability["most_stable"])")
                    println("  Overall stability: $(round(stability["overall_stability"], digits=3))")
                end
            end
            
        catch e
            println("Error in time series backtesting: $e")
        end
    else
        println("\n10. Skipping time series test (insufficient data)")
    end
    
    # Test engine statistics
    println("\n11. Testing engine statistics...")
    try
        stats = get_backtest_engine_stats(backtest_engine)
        
        println("Engine Statistics:")
        println("  Test period: $(stats["test_period"])")
        println("  Historical data count: $(stats["historical_data_count"])")
        println("  Total backtests run: $(stats["performance_metrics"]["total_backtests_run"])")
        println("  Average backtest time: $(round(stats["performance_metrics"]["average_backtest_time"], digits=4)) seconds")
        println("  Cache entries: $(stats["cache_entries"])")
        
    catch e
        println("Error getting engine statistics: $e")
    end
    
else
    println("No favorable key numbers found - cannot test backtesting functionality")
end

println("\n=== Enhanced BacktestingEngine Testing Complete ===")