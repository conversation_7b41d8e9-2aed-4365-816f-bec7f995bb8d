---
created: 2025-07-24T21:26:41 (UTC +08:00)
tags: [lottery,lotto,strategy,pairs,pairings,theory,number,frequency,combination,win,odds,random,]
source: https://saliu.com/forum/lottery-pairs.html
author: 
---

# Lottery Pairs, <PERSON>to Frequency, Lotto Wonder Grid

> ## Excerpt
> Lotto Wonder Grid is theory, strategy, system in lottery based on number frequency in recent drawings, number pairs appearance from hot to cold.

---
[![Buy the best software: Lottery, Powerball, Mega Millions, gambling roulette, blackjack, sports.](https://saliu.com/images/software-lottery-gambling.gif)](https://saliu.com/membership.html)  

## <u>Lottery Pairs And Repeat Probability</u>  
<u><i>The Science of Positive Discrimination</i></u>

## By <PERSON>, _★ Founder of: Lottery Mathematics, Lotto Programming Science_

![The Best Pick 4 software ever - Super Pick-4 lotto.](https://saliu.com/forum/HLINE.gif)

Authored on September 21 & Notvember 7, 2007.

<big>I.</big>  
Lottery pairing represents one of my first theories (and strategies) in the field of lottery. I made it public in 2001 as the [lotto wonder grid](https://saliu.com/bbs/messages/638.html). The theory was based on the Fundamental Formula of Gambling (FFG).

The _top 10%_ of the pairings represents 25% of total frequencies for each number. The _top_ is defined as the most frequent part in a pairing string (starting from left to right). In the case above (still a lotto 6/69 game) the _top 10%_ means the first 7 pairs 10% of 69 is approximately 7). The first 10% pairs for lotto number 1 add-up to 24 (25.3% of all 95 cases for that lotto number). 4+4+4+3+ 3+ 3+ 3 = 24.

Conversely, the _bottom 10%_ pairs sum-up to 0 (zero).

The _top 25%_ of the pairings represents 50% of total frequencies for each number. That is, the 25% segment of the most frequent pairs account for 50% of the entire pairing frequency for any given lotto number.

The _top 50%_ of the pairings represents 75% of total frequencies for each number. That is, the first half of the pairs (from left to right) accounts for 75% of the entire pairing frequency for any given lotto number.

Again, I repeat the very important point here. My analysis of the lotto pairings is not as thorough as other domains of my gambling mathematics. But this is an open debate and we should be able to find the fault, if it exists. So, I assume that the pairing percentages above hold true for any lotto game, at any given point in a lotto history. Based on the premise, I decide:

1) to play a favorite lotto number; AND

2) play only its top 25% pairs.

In a hypothetical case, I will play number 1, since it shows as the last skip a value between 0 and 5. For a lotto 6/49 game, I will also play the most frequent 12 pairs (25% of the remaining 48 lotto numbers). Lotto number 1 comes out with its top 25% pairs in 50% of its appearances. The chance is 1/2 that the lotto number will come out with such pairings – WHEN it hits!

Every combination I play will contain the number 1 (the favorite). The remaining 5 numbers will consist of combinations of the 12 numbers in the most frequent 25% pairs. Total combinations of 12 taken 5 at a time: C(12,5) = 792.

In the second variation, every lotto number in the game was played with its top 5 pairs. For example, the wonder grid in a 6/49 lotto game looked like:

```
<span size="5" face="Courier New" color="#c5b358"> 1   13  16  28  42  14 
 2   13  34  46  48  11 
 3   39  31  32  6  26 
 4   10  24  5  11  13 
 5   28  19  29  15  36 
 6   18  4  11  3  20 
....
 39   3  28  19  4  32 
 40   22  5  9  19  1 
 41   13  49  5  27  36 
...
 46   21  43  10  2  17 
 47   48  35  37  31  4 
 48   34  35  9  10  38 
 49   4  10  17  26  30 
</span>
```

The _**wonder grid**_ has always shown improvement in odds compared to playing lotto numbers randomly. The lotto-5 games have shown staggering improvements in odds, depending on the insertion (start) point in the data (results) file. The pick-3 lottery wonder-grid is even more successful.

<big>II.</big>  
Problems still remain. I never hide behind my fingers. I always tackle the problems. Lotto-6 shows very low results regarding the jackpot. Lotto-5 does not show solid consistency at hitting the jackpot. The same is true about pick 3 & 4 lotteries. Although the wonder grid strategy is advantageous overall, the players have a problem with its consistency. I am dissatisfied as well.

If you check back a wonder strategy, its results are simply staggering, including lotto 6/49 games (or more terrible odds). But that is not backward compatibility: It is a form of curve fitting. What gives?

When we check backwards a wonder grid, we reach winning situations when the pairs at that time where NOT among the TOP-5! They became top-5 after several drawings. Also, the current top-5 pairs will hit in the near future: But not all of them simultaneously. In most cases, it is a combination of 3 pairs from the top 5. The newly released SkipPair (part of the Bright integrated packages) shows the pairing ranks in relation to the previous drawing. You will notice lotto-6 shows rarely occurrences of the top-5 pairs. Again, the pairing reports are calculated only in relation to the previous lotto draw. The wonder-grid was meant to mature within a drawing cycle.

The way I started this theory/strategy helped me understand better the concept. I first wrote about it in this article: [_**Brief history of my lottery, lotto experience**_](https://saliu.com/bbs/messages/532.html).

I came to the United States in April of 1985 WEB. I started work on a tree nursery and orchard. One day, a Porto Rican gave me some lottery materials and tickets. He said he had a hunch I was a lottery expert! I could not drive at that time and I rode a bicycle to work. The Porto Rican would give me sometimes a ride to the food store. But first we would stop by a lottery agency and buy lotto tickets. We won the very first two tries, “ 4 out of 6”. I used the same non-computer system I used in Romania. I worked as an economist in the early 1980's. _Communist economist_ was really an oxymoron. I didn't have serious work to do, or nobody took seriously an economist's job.

So, I spent time, at work, developing gambling systems. The soccer pools represented the number one job. Secondary, I analyzed the lotto games as well. My system was based on the most frequent pairs, triads and 4-number groups. Two of my colleagues, an economist and an accountant, saw the numbers I came up with. They played the numbers without even asking me to participate. They won serious money, by local standards. Several months' worth of income, if not one year!

Incidentally, I was badly cheated in both cases. The Romanians played secretly. The Porto Rican and his girl friend short-changed me, as I discovered later. It was pretty hard to come up with the pools of numbers manually. Just checking two dozen past drawings is a pain in the neck. Then, more pain: Shuffle (randomize) the pools of numbers manually! I always shuffled the numbers several times before wheeling them (it's about standard deviation!)

My strategy was not a pure wonder grid. I did not consider the lotto pairs strictly. What I did: I selected the most frequent lotto numbers in a recent amount of past drawings. I considered pairings, indeed. I also considered 3-number grouping. My method, however, considered the number grouping in multiple drawings, not just draw by draw. The strategy is more closely related to the lottery number frequency. I presented here: [_**Lottery Strategy, Systems Based On Number Frequency**_](https://saliu.com/frequency-lottery.html).

My selecting lotto numbers was somehow pair related. I selected top pairs that had come out very recently. Those numbers might not have been highly paired in the remote past. Essentially, I was selecting numbers with good frequency in the recent past. Such numbers do tend to repeat more often in the near future. It is in accordance with the Fundamental Formula of Gambling.

<big>III.</big>  
The wonder-grid or the lottery pairing theory is still valid mathematically. But we need apply the correct perspective. The top pairing is a particular case of the lottery frequency in the context of the recent past.

The new SkipPair software creates very useful reports. Combine the reports with the WORST files. You will notice situations when the worst-5, even the worst-10 lotto pairs do not show up in a drawing. Like in this drawing in Pennsylvania lotto 6/49:

```
<span size="5" face="Courier New" color="#c5b358"> 54    2  4 11 14 20   7 13 15 23 25   5  9 17 23 26
 6  9 15 22 27   11 16 22 31 <u>37</u>   6 12 19 23 31
</span>
```

The last lotto pair was index-<u>37</u>. The last (the worst) pair index is 48 in lotto 6/49. Thus, the worst 11 pairs could have been eliminated. You can create such a WORST-6 file with Util-632 (updated in the Bright software package). An 11-pair WORST6 file generates lotto combinations not very often! Only a few lotto numbers do not represent the worst pairing for other lotto numbers! Only lotto numbers with good skips (good frequency in the recent past) do not interfere with other numbers.

The worst pair index (48, in this case) is not very frequent. That was the purpose of the initial WORST-6 file: Eliminate the very worst lotto pairings (the last pair index). You can see now, that it is even better to wait a little longer and eliminate many more worst-pairings. The worst lottery pairings, too, have the same mathematical foundation (FFG). They consist of lotto numbers with bad skips (far above the FFG median).

<big>IV-4.</big> Positive Discrimination  
Frankly, I had very high expectations regarding the lottery pairings. I was very consistent in the beginnings, when I worked the system manually. But I hadn't hit the jackpot. And that's where most complaints come from. The top lotto pairs in the GRID files do not hit the jackpot in the near future. Yet, checking backwards, the GRID shows a large number of jackpot hits! What gives?

Unhappiness (regardless of its object) is the source of evolution more often than not. I came back to the drawing board. I had a glass of something new to me: Sangria red wine. If you don't know, it is a mix of red wine and orange juice, approximately. It is so soft, that it just increases the flow of your creative juice, as it were! To make it short, it helped me a great deal in discovering the science of positive discrimination.

I said to myself: _“Super crocodilule, your well intended egalitarianism impeded your vision in the lottery pairing theory!”_ Indeed, things are not created equal. Granted, Randomness Almighty insures that every thing, gigantic or invisibly small, has a fair shot at being. Randomness Almighty makes sure that no thing is favored by prior decision. Yet, some things get the upper hand by favoring themselves!

Just look at sports. Ideally, the rules and officiating make sure that all contestants have a fair chance. But only one can be the champion! Only one can be the best! The same is true about the lottery numbers. The probability is extremely high that the lottery numbers will not be equal in frequency. The percentages tend towards equality — but there is no equality in absolute terms. Regardless how long we track the numbers, some tend to run away with the frequency prize. The biased is far stronger for shorter ranges (e.g. numbers of drawings).

And thus I came up with the concept of <u>positive discrimination</u>. It is discrimination based on merit. Discrimination is bad when based on things other than merit.

Normally, we associate discrimination with discrimination based on hatred. It is bad to discriminate against other people simply because they have a different skin color or because they pray to the wrong gods or goddesses. But are there right skin colors or right gods? NOT! NON! The skin colors or religions are randomly equal.

We must not be indiscriminant, however. That is as bad as negative discrimination. The societies, for example, apply positive discrimination when dealing with criminals. The criminals are not, and should not, be treated equally to the law-abiding citizens. The same is true about our enemies. It would be insane and self-destructive to treat your enemies equally to your friends. In my case, for example. Some well-behaved people are shocked at the harsh measures (including harsh language) I take against those who attack me. My message board is a live example of hatred directed towards me with the intent to bring me down or, at least, cause disruption in my life. Positive discrimination is, therefore, a necessity of life.

The wonder grid (the top lotto pairings) is indiscriminant. The lottery numbers, however, are positively discriminated. Some numbers are more equal than others, paraphrasing George Orwell's animals (computing beasts) in his farm! The wonder grid is wasteful of resources because it treats equally all lotto numbers. We know that the lottery numbers are not equal, especially in the short term. We call such short terms trends.

Even in case III-3 above: The WORST pairings still treat the lottery numbers equally. Every lotto number has an equal number of bad pairings. The difference is sharp. The worst 10 pairings of frequent lottery numbers could be better than the best 10 pairings of infrequent lotto numbers! As I said, it is wasteful of resources. We play bad pairings while discarding of good lottery pairs!

And thus we have arrived at the best terminus in lottery pairings: LEAST. The least pairings are the most efficient method of eliminating lottery pairs. The LEAST files consist of lotto pairings with low frequency (normally a frequency of 0). In this case, the lottery numbers will be discriminated against based on merit. The numbers with a good frequency trend will have most of their pairs in play. The numbers in a bad trend will be only slightly present in the output. You can check (manually only, at this time) lottery drawings and the LEAST files. There are drawings with no pairs with zero-frequency come out. Applying the LEAST file to the combination generators can eliminate a huge number of combinations.

Of course, the size of the LEAST files is strongly influenced by the parpaluck (the range of statistical analysis). A short parpaluck creates a large LEAST file. It seems to me that a parpaluck equal to N\*2 (the double of the largest lotto number in the game) creates the optimal LEAST file.

## [<u>Resources in Lottery Software, Systems, Lotto Wheeling</u>](https://saliu.com/content/lottery.html)

-   The Main [_**Lotto, Lottery, Software, Strategy**_](https://saliu.com/LottoWin.htm) Page.  
    Presenting software to create free winning lotto, lottery strategies, systems based on mathematics. Get your lotto systems or wheels, the best lottery, lotto software, combinations, winning numbers.
-   [Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies](https://saliu.com/Newsgroups.htm).  
    Read a genuine analysis of Excel spreadsheets applied to lottery and lotto developing of software, systems, and strategies. Combining Excel analysis with powerful lottery and lotto software programmed by this author, _Parpaluck_.
-   A User's [_**<u>MDIEditor Lotto WE</u>: Lottery Software Manual, Book, ebook, Help**_](https://saliu.com/MDI-lotto-guide.html).  
    ~ Also applicable to LotWon lottery, lotto software; plus Powerball, Mega Millions, Euromillions.
-   [_**Manual, Tutorial of <u>Lottery Software</u>, Lotto Apps**_](https://saliu.com/forum/lotto-book.html).
-   _"My kingdom for a good lotto tutorial!"_ [Lotto, Lottery Strategy Tutorial](https://saliu.com/bbs/messages/818.html).
-   [_**Documentation, Help: MDIEditor Lotto WE, Lottery Software, Strategy Tutorial**_](https://saliu.com/mdi_lotto.html).
-   [**<u>Lottery Skip Systems</u>**](https://saliu.com/skip-strategy.html): _**Lotto, Powerball, Mega Millions, Euromillions, Horse Racing, Roulette, Football**_.
-   [**Lottery Utility Software**](https://saliu.com/lottery-utility.html): _**Pick-3, 4 Lottery, Lotto-5, 6, Powerball/Mega Millions/Thunderball, Euromillions**_.
-   [_**Lottery lotto filtering in software**_](https://saliu.com/filters.html).
-   [**<u>Lotto wheels</u>**](https://saliu.com/lotto_wheels.html) _**for lotto games drawing 5, 6, or 7 numbers**_.  
    The most advanced theory of lotto wheels or reduced lottery systems. Get also original lotto wheels for lotto games drawing 5, 6, or 7 numbers: Balanced, randomized, and free.
-   [_**Updates to the Bright lotto, lottery, horse racing software**_](https://saliu.com/forum/software-updates.html) bundles.  
    Updates to several **Bright** software packages: pick-3 and pick-4 lotteries, horse-racing, 5- and 6-number lotto games.
-   _**Download**_ [**the best lottery software**](https://saliu.com/infodown.html).

![The lottery wonder-grid shows improvement in odds compared to playing lotto numbers randomly.](https://saliu.com/forum/HLINE.gif)

**[Home](https://saliu.com/index.htm) | [Search](https://saliu.com/Search.htm) | [Help](https://saliu.com/Help.htm) | [New Writings](https://saliu.com/bbs/index.html) | [Software](https://saliu.com/infodown.html) | [Odds, Generator](https://saliu.com/calculator_generator.html) | [Contents](https://saliu.com/content/index.html) | [Forums](https://forums.saliu.com/) | [Sitemap](https://saliu.com/sitemap/index.html)**

![Wonder Grid is a theory and strategy in lotto and lottery based on number frequency in recent drawings.](https://saliu.com/forum/HLINE.gif)
