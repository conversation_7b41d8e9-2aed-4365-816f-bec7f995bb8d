# Wonder Grid Lottery System - 正確使用示範
# 展示如何避免命名衝突並正確使用系統

using Dates

println("🎯 Wonder Grid Lottery System - 正確使用示範")
println("=" ^ 60)

# ==========================================
# 使用模組前綴方式 (推薦用於已有衝突的環境)
# ==========================================

# 載入本地模組
include("src/WonderGridLotterySystem.jl")
import .WonderGridLotterySystem as WGLS

println("✅ 使用模組前綴 WGLS 載入系統")

# 創建測試數據
test_data = [
    WGLS.LotteryDraw([1, 8, 15, 22, 29], Date(2023, 1, 1), 1),
    WGLS.LotteryDraw([3, 12, 18, 25, 31], Date(2023, 1, 2), 2),
    WGLS.LotteryDraw([5, 9, 16, 23, 35], Date(2023, 1, 3), 3),
    WGLS.LotteryDraw([2, 11, 19, 27, 33], Date(2023, 1, 4), 4),
    WGLS.LotteryDraw([7, 14, 21, 28, 36], Date(2023, 1, 5), 5),
]

println("📊 創建了 $(length(test_data)) 筆測試數據")

# ==========================================
# 核心分析功能展示
# ==========================================

println("\n🔧 初始化分析組件")

# 創建分析器
skip_analyzer = WGLS.SkipAnalyzer(test_data)
ffg_calculator = WGLS.FFGCalculator(0.5)  # 50% 確定度
pairing_engine = WGLS.PairingEngine(test_data)

println("✅ Skip 分析器已初始化")
println("✅ FFG 計算器已初始化 (確定度: 50%)")
println("✅ 配對引擎已初始化")

# ==========================================
# Skip 分析展示
# ==========================================

println("\n📈 Skip 分析結果")
println("-" ^ 40)

key_numbers = [1, 8, 15, 22, 29]
println("🎯 分析關鍵號碼: $(join(key_numbers, ", "))")

for number in key_numbers
    # 計算當前 Skip 值
    current_skip = WGLS.get_current_skip(skip_analyzer, number)
    
    # 計算 FFG 中位數
    ffg_median = WGLS.calculate_ffg_median(ffg_calculator, number, test_data)
    
    # 判斷是否有利
    is_favorable = current_skip <= ffg_median
    
    # 計算機率
    probability = WGLS.compute_skip_probability(ffg_calculator, current_skip, ffg_median)
    
    status = is_favorable ? "✅" : "❌"
    println("  號碼 $number: Skip=$current_skip, FFG=$(round(ffg_median, digits=2)), 機率=$(round(probability, digits=3)) $status")
end

# ==========================================
# 配對分析展示
# ==========================================

println("\n🤝 配對分析結果")
println("-" ^ 40)

# 計算所有配對
all_pairings = WGLS.calculate_all_pairings(pairing_engine)
println("📊 總配對數: $(length(all_pairings))")

# 獲取頂級配對
if !isempty(all_pairings)
    sorted_pairings = sort(collect(all_pairings), by=x->x[2], rev=true)
    top_count = min(5, length(sorted_pairings))
    
    println("\n🏆 頂級配對 (前 $top_count 名):")
    for (i, (pair, freq)) in enumerate(sorted_pairings[1:top_count])
        println("  $i. $(pair[1])-$(pair[2]): $freq 次")
    end
end

# ==========================================
# 便利函數展示
# ==========================================

println("\n🔧 便利函數展示")
println("-" ^ 40)

# 定義快速分析函數
function quick_analysis(numbers)
    results = []
    for number in numbers
        skip = WGLS.get_current_skip(skip_analyzer, number)
        ffg = WGLS.calculate_ffg_median(ffg_calculator, number, test_data)
        favorable = skip <= ffg
        
        push!(results, (
            number = number,
            skip = skip,
            ffg = round(ffg, digits=2),
            favorable = favorable
        ))
    end
    return results
end

# 使用便利函數
analysis_results = quick_analysis([1, 2, 3, 4, 5])

println("📊 快速分析結果:")
for result in analysis_results
    status = result.favorable ? "✅" : "❌"
    println("  號碼 $(result.number): Skip=$(result.skip), FFG=$(result.ffg) $status")
end

# ==========================================
# 使用建議
# ==========================================

println("\n💡 使用建議")
println("-" ^ 40)

println("""
🎯 最佳實踐:

1. 🆕 新會話使用:
   # 重新啟動 Julia
   julia> using WonderGridLotterySystem
   julia> # 直接使用所有功能

2. 🏷️ 當前會話使用:
   julia> import WonderGridLotterySystem as WGLS
   julia> # 使用 WGLS.功能名稱

3. 🎯 選擇性導入:
   julia> using WonderGridLotterySystem: get_current_skip, calculate_ffg_median
   julia> import WonderGridLotterySystem as WGLS  # 用於類型

4. 📝 腳本開發:
   # 在 .jl 文件中直接使用
   using WonderGridLotterySystem
   # 您的分析代碼

⚠️ 重要提醒:
- 警告不影響功能運行
- 推薦在新 Julia 會話中使用
- 模組前綴是安全的替代方案
""")

println("\n🎉 正確使用示範完成！")
println("💡 選擇最適合您的使用方式")
