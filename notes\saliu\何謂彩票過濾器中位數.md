彩票過濾器中的「中位數」（Median）是一個關鍵的統計參數，它在 Ion Saliu 的彩票數學理論，特別是「賭博基本公式」（Fundamental Formula of Gambling, FFG）中佔有核心地位。

以下是關於彩票過濾器中位數的解釋：

- **定義**：
    
    - 彩票過濾器中位數是指在彩票統計報告中，表示某一特定過濾器數值序列的**中間值**。它不同於平均值 (Average)，而是當數據點依大小排序後，位於最中間的那個數值。
    - 在「賭博基本公式 (FFG)」的語境下，這個概念被稱為 **FFG 中位數**。FFG 中位數代表了**在給定單一事件機率 (p) 的情況下，該事件以 50% 的確定性程度 (Degree of Certainty, DC) 至少發生一次所需的試驗次數 (N)**。
        - 例如，如果某個彩票過濾器的 FFG 中位數是 16，這表示在 50% 的情況下，過去 17 次開獎（包含最新一次）的號碼池中將包含所有六個中獎號碼。這種 50% 的確定性是經過數學精確計算的，而非平均值。
- **計算與確定**：
    
    - **軟體自動計算**：像 **SuperFormula** 這樣的專用軟體可以自動計算 FFG 中位數及相關的確定性程度。
    - **手動確定**：使用者也可以透過將過濾器報告（例如 LotWon 軟體生成的 WS 檔案）中的數值欄位進行排序來手動確定中位數，中位數即為排序後的中間點數值。
- **意義與應用**：
    
    - **策略設定基礎**：中位數是設定彩票過濾器等級的重要參考依據。透過觀察過濾器中位數，可以判斷哪些數值是「正常範圍」內的，進而設定過濾器的最小值和最大值。
    - **「跳躍系統」(Skips Systems) 的核心**：在分析號碼的「跳躍」（即兩次中獎之間的間隔）時，中位數特別有用。如果某個號碼的當前跳躍次數小於或等於其跳躍中位數，則被認為是較佳的投注時機。
    - **提高中獎機率**：根據 FFG 中位數設定的策略，通常能產生更少的組合數量，同時保持更高的中獎命中頻率。例如，有研究指出，選擇當前跳躍值落在 FFG 中位數範圍內的樂透號碼，中獎機率比純隨機選擇高出七倍。
    - **揭示數據行為**：中位數可以幫助揭示彩票號碼的動態行為，例如在輪盤賭中，一個數字傾向於在小於或等於其 FFG 中位數的旋轉次數內重複出現。
    - **排除低概率組合**：彩票策略會利用中位數來篩選掉不常見的組合，例如設定過濾器等級超出中位數正常範圍（中位數的三到五倍）來大量減少組合。