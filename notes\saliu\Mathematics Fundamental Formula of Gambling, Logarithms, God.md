---
created: 2025-01-03T19:52:16 (UTC +08:00)
tags: [mathematics,formula,gambling,<PERSON>,<PERSON>,logarithm,certainty,god,formulae,absurdity,equation,probabilities,probability,number,trials,math,maths,formulas,median,]
source: https://fr.saliu.com/formula.htm
author: 
---

# Mathematics Fundamental Formula of Gambling, Logarithms, God

> ## Excerpt
> Fundamental Formula of Gambling is the essence of gambling mathematics. The probability formula is a precise instrument in theory of games, gambling, randomness, inexistence of God.

---
![Logarithms play an important role in mathematics; the Universe is shaped by logarithmic function.](https://fr.saliu.com/HLINE.gif)

### I. [Logic of Fundamental Formula of Gambling](https://fr.saliu.com/formula.htm#Formula)  
II. [The Mathematical Solution: _Divine_ Logarithm](https://fr.saliu.com/formula.htm#Logarithm)  
III. [Mathematics of the God Concept: _Formula of Absurdity_](https://fr.saliu.com/formula.htm#God)

• _Why God Fears Mathematics, While Einstein Hates Gambling..._

IV. [Insider Information: It's All in Our Reason](https://fr.saliu.com/formula.htm#Reason)  
V. [Mathematics of _Ion Saliu's Paradox_ or _Problem of N Trials_](https://fr.saliu.com/formula.htm#Paradox)  
VI. [Software: _Divine_ Tool to Further Empower Reason](https://fr.saliu.com/formula.htm#Software)  
VII. [Links and Resources Regarding Mathematics, Probability](https://fr.saliu.com/formula.htm#Mathematics)

![Study algorithms and software to calculate Fundamental Formula of Gambling.](https://fr.saliu.com/images/standard-deviation-calculator.gif)

## <u>1. <u>Logical Steps, Algorithm</u> to the Fundamental Formula of Gambling</u>

First captured by the _WayBack Machine_ (_web.archive.org_) on May 10, 2000.

**_"Let no one enter here who is ignorant of mathematics."_  
(The frontispiece of Plato's Academy)**

**_"The most important questions of life are, for the most part, really only problems of probability."_  
(Pierre Simon Marquis de Laplace, "Théorie Analytique des Probabilités")**

Here is how I arrived to, by now, famous [_**Fundamental Formula of Gambling (FFG)**_](https://fr.saliu.com/Saliu2.htm). When laypersons say: _"It is so simple,"_ it always represents undeniable mathematics; therefore, undeniable Truth. I thought I had worked it out on my own, because the formula starts with the very essence of probability theory: **p = n/N**, or reduced to a **p = 1/N** mathematical relation. A truth becomes (almost) self–evident when a number of people think **independently** of the same thing. But this human law must be the most undeniable of them all: <u>No Truth is self–evident AND no human thinks totally independently of others</u>.

![Online gambling and Internet casino gambling are fraudulent; they defy mathematics.](https://fr.saliu.com/HLINE.gif)

My first step got my feet wet in my pick 3 lottery software pond. _"Probably win,"_ that's what I had thought.

I rationalized in this manner. The probability of any 3-digit combination is 1/1000. Therefore, I had expected that the repeat (skip) _median_ of a long series of pick-3 drawings would be 500. It would be similar to coin tossing, where the median of p=1/2 series is 1. In other words, the median of a long series of coin tosses is 1. To my surprise, the repeat median of long series of pick-3 drawings was not 500. It was closer to 700.

I checked it for series of 1000 real drawings and also randomly generated drawings. Then I checked the median against series of 10000 (10 thousand) drawings. The median of the skip was always close to 700. Do not confuse it for the median combination in the set. That value of the median is, in fact, either 499 or 500. The correct expression is _4,9,9_ or _4 9 9_ or _5,0,0_ (three separate digits).

What is that median useful for, anyway? Among other properties, the skip median (or median skip) shows that, on the average, a pick-3 combination hits in a number of drawings. Any pick-3 combination hits within 692 drawings in at least 50% of the cases. Equivalently, if you play one pick-3 combination, there is a 50%+ chance it will hit within 692 drawings, or it will repeat no later than 692 drawings. The chance is also (almost) 50% that you will have to wait more than 692 drawings for your number to hit.

I studied theory of probability (gambling mathematics too!) in high school and in college. Some things get imprinted on our minds. Such information becomes part of our axioms. An axiom is a self-evident truth, a truth that does not necessitate demonstration. We operate with axioms in a manner of automatic thinking.

So, I was analyzing mathematically long pick-3 series, where p=1/1000. Next, I wrote the probability formula of a single pick-3 number to hit two consecutive drawings: p = 1/1000 x 1/1000 = 1/1000000 (1 in 1 million). I have never found useful to work with very, very small numbers in probability.

How about the reverse? The probability of a particular pick-3 number NOT to hit is p = (1 – 1/1000) = 999/1000 = 0.999. This is a very large number. It is almost certain that my pick-3 combination will not hit the very first time I play it.

How about not hitting two times in a row? P = (1 – 1/1000) <sup> 2</sup> = 0.999 to the power of 2 = 0.998. Still, a very large number! I reversed the approach one more time: What is the opposite of not hitting a number of consecutive drawings? It is winning _within_ a number of consecutive drawings.

The knowledge was inside my head. Unconsciously, I used Socrates' dialectical method of _delivering_ the truth. (His mother delivered babies.) I also followed steps in _De Moivre formula_. At this point, I had this relation:

**_1 – (1 – p) <sup> N</sup>_**

where N represents the number of consecutive drawings.

I thought that for N = 500 drawings, the expression above should give the median, or a probability of 50%. So, I calculated 1 – (1 – p) <sup> N</sup> = 1 – (1 – 1/1000) <sup> 500</sup> = 1 – (0.999) <sup> 500</sup> = 0.3936 = 39.36%. Thus, my relation became:

0.3936 = 1 – (1 – 1/1000) <sup> 500</sup>

I made N = 692. I obtained the value:  
1 – (1 – 1/1000) <sup> 692</sup> = 1 - 0.5004 = 49.96% (very close to 50%).

Next step: I made N = 693. I obtained the degree of certainty:  
1 – (1 – 1/1000) <sup> 693</sup> = 1 - 0.4999 = 50.01% (very, very close to 50%).

Thus, the parameter I call the _FFG median_ is between 692 and 693 for the pick-3 lottery.

![Formula of gambling applies the mathematics of logarithms to many real-life phenomena.](https://fr.saliu.com/HLINE.gif)

## <u>2. The Mathematical Solution: Divine Logarithms</u>

I concluded I should not make more assumptions. What if I don't think I know what N should be for the median (50%), or for any other chance, which I simply called the _**degree of certainty**_? I realized I had the liberty to select whatever degree of certainty I wanted to, and only had to calculate N. The relationship became:

**_DC = 1 – (1 – p) <sup> N</sup>_**

Then:

**_(1 – p) <sup> N</sup> = 1 – DC_**

The equation can be solved using logarithms:

![The fundamental probability formula of logarithms can be calculated in scientific software.](https://fr.saliu.com/ScreenImgs/FFG1.jpg)

The only unknown is _N: the number of consecutive drawings (or trials) that an event of probability p will appear at least once with the degree of certainty DC_.

The rest is history. I called the relation **_“The Fundamental Formula of Gambling”_** almost automatically. Unintentionally, it might sound cocky. Just refer to it as **_FFG_**.

Nothing comes with absolute certainty, but to a degree of certainty! That's mathematics, and that's the only TRUTH.

![The Mathematics of the God concept: Absolute Certainty is Absurdity.](https://fr.saliu.com/HLINE.gif)

## <u>3. Mathematics of the <i>God</i> Concept: The Formula of <i>Absurdity</i></u>

Unfortunately, O glorious sons and daughters of Logos and Axioma, the idea of _God_ is a mathematical absurdity! It saddened me first, but we must come to grip with reality. The humans fictionalize because we feel we can't live without the comfort of absolute certainty.

\* Some humans with mathematical skills will stumble upon an _error_, when the degree of certainty DC is set to 100%. There is no absolute certainty in the Universe (or probability equal to 1). It leads to an absurdity: Calculating the number of events necessary for an event of probability p to appear with a degree of certainty equal to 100%. It is absurd. No other qualifications apply, such as _impossible_ or _erroneous formula_. Just remember the relation we had before considering the _**degree of certainty**_! We dealt with _the probability of losing N consecutive times_: (1 – p) <sup> N</sup>. In this relation, no N can lead to zero (1 – 100%). Not even _minus Infinity_! A computer program should trap the error and ask the user to enter a DC less than 100% (things like 99.99999999…%).\*

![Mathematics of God proves the concept to be an absurdity based on universality of randomness.](https://fr.saliu.com/HLINE.gif)

**_Some profound thoughts surrounding this mathematical expression and the false error.  
The Fundamental Formula of Gambling (FFG) proves that **_absolute certainty_** is a mathematical absurdity. If we set the degree of certainty DC=1 (or 100%), FFG leads to a mathematical absurdity. God is **_Absolute Certainty_**, therefore absolute absurdity. I can only imagine de Moivre's reaction when this thought might have crossed his mind: _"Certainty is absurd! How can God be True?"_ It was the 17th century, and the 21st has just a little changed for the better…_**

_Aporia_ is a special form of absurdity. A Sophist philosopher — Zeno of Elea — constructed a most famous _aporia_. It is known as the _Paradox of Achilles and the tortoise_. Read the first philosophical, logical, and mathematical solution: [_**Zeno's Paradox**_](https://fr.saliu.com/aporia.html): _Achilles Can't Outrun the Tortoise?_

It was very easy to apply _**FFG**_ mathematics to many types of lottery and gambling games. It is at the foundation of several high probability gambling systems I designed: roulette, blackjack, horseracing, and lottery. It works with stocks, too. There is significant _randomness_ in stock evolution. Many stockbrokers came to terms with the reality that all stocks fluctuate in an undeniably random fashion. I am surprised how many brokerage firms have visited my site!

![Formula of gambling refers to mathematics, logic, reason, degree of certainty, randomness.](https://fr.saliu.com/HLINE.gif)

## <u>4. Insider Information: It's All in Our <i>Reason</i></u>

In the year of grace 2001 my memory dug out a real gem. I wrote about it in a post on my message board: [_**Cool stories of the Truth**_](https://fr.saliu.com/bbs/messages/862.html).

_“I found another treasure: A little book in Romanian. Don't they say great things come in small packages? It couldn't be truer than in this case. The book was **The Certainties of Hazard** by French academician Marcel Boll. The book was first published in French in 1941. My 100-page copy was the 1978 Romanian edition. It all came to life, like awakening from a dream. The book presented a table very similar to the table on my Fundamental Gambling Formula page. Then, in small print, the footnote: “The reader who is familiar with logarithms will remark immediately that N is the result of the mathematics formula: _N=log(1-pi)/log(1-p)_.”_

That's what I call the **Fundamental Formula of Gambling**, indeed! Actually, the author, Marcel Boll did not want to take credit for it. Abraham de Moivre largely developed the formula. Then I remembered more clearly about de Moivre and his formula from my school years. Abraham de Moivre himself probably did not want to take credit for the formula. As a matter of fact, the relation only deals with one element: the probability of N consecutive successes (or failures). Everybody knows, that's _p <sup> N</sup>_ (p raised to the power of N). It's like an axiom, a self-evident truth. Accordingly, nobody can take credit for an axiom. I thought Pascal deserves the most credit for establishing **_p = n / N_**. From there, it's easy to establish **_p <sup> N</sup>_**. And give birth to so many more worthy numerical relations.

![Formula of Gambling is related to mathematics of Ion Saliu's Paradox.](https://fr.saliu.com/HLINE.gif)

## <u>5. Mathematics of <i>Ion Saliu's Paradox</i> or <i>Problem of N Trials</i></u>

Another look at one of the steps leading to the _**Fundamental Formula of Gambling**_:

**_1 – DC = (1 – p)<sup>N</sup>_**

We can express the probability as _p = 1 / N_; e.g. the probability of getting one point face when rolling a die is _1 in 6_ or _p = 1 / 6_; the probability of getting one roulette number is _1 in 38_ or _p = 1 / 38_. It is common sense that if we repeat the event N times we expect one success. That might be true for an extraordinarily large number of trials. If we repeat the event N times, we are NOT guaranteed to win. If we play roulette 38 consecutive spins, the chance to win is significantly less than 1!

**_1 – DC = (1 – 1 / N) <sup> N</sup>_**

I noticed a mathematical limit. I saw clearly: **_lim((1 – (1 / N)) <sup> N</sup>)_** is equal to **_1 / e_** (**_e_** represents the base of the natural logarithm or approximately 2.71828182845904...). Therefore:

**_1 – DC = 1 / e_**

and

**_DC = 1 — (1 / e)_**

The limit of **1 — (1 / e)** is equal to approximately **0.632120558828558...**

I tested for N = 100,000,000... N = 500,000,000 ... N = 1,000,000,000 (one billion) trials. The results ever so slightly decrease, approaching the limit … but never _surpassing_ the limit!

When N = 100,000,000, then DC = .632120560667764...  
When N = 1,000,000,000, then DC = .63212055901829...

(Calculations performed by **SuperFormula**, function _C = Degree of Certainty (DC)_, then option _1 = Degree of Certainty (DC)_, then option _2 = The program calculates p_.)

If the probability is _p = 1 / N_ and we repeat the event _N_ times, the degree of certainty _DC_ is _1 — (1 / e)_, when _N_ tends to _infinity_. I named this relation _**Ion Saliu's Paradox of N Trials**_.

Soon after I published my page on _Theory of Probability, Ion Saliu Paradox_ (in 2004), the adverse reactions were instantaneous. I even received multiple hostile emails from the same individual! Basically, they considered my _(1 / e)_ discovery as idiocy! _“You are mathematically challenged”_, they were cursing! Guess, what? I saw in 2012 an edited page of _Wikipedia (e constant)_ where my _(1 / e)_ discovery is considered correct mathematics. Of course, they do not give me credit for that. Nor do they demonstrate mathematically the _(1 / e)_ relation — because they don't know the demonstration (as of March 21, 2012)!

<big>•</big> You can see the mathematical proof right here, for the first time. I created a PDF file with nicely formatted equations:

-   [_**Mathematics of Ion Saliu Paradox**_](https://fr.saliu.com/IonSaliuParadox.pdf).

How long is _in the long run_? Or, how big is _the law of BIG numbers_? Ion Saliu's paradox of N trials makes it easy and clear. Let's repeat the number of trials in M multiples of N; e.g. play one roulette number in two series of 38 numbers each. The formula becomes:

**_1 – DC = (1 – 1 / N)<sup>NM</sup> = {(1 – 1 / N)<sup>N</sup>}<sup>M</sup> = (1 / e)<sup>M</sup>_**

Therefore, the degree of certainty becomes:

**_DC = 1 – (1 / e)<sup>M</sup>_**

If M tends to infinity, **_(1 / e)<sup>M</sup>_** tends to zero, therefore the degree of certainty tends to 1 (certainty, yes, but not in a philosophical sense!)

Actually, relatively low values of M make the degree of certainty very, very nearly 100%. For example, if M = 20, DC = 99.9999992%. If M = 50, the PCs of the day calculate DC = 100%. Of course, they can't approximate more than 18 decimal positions! Let's say we want to know how long it will take for all pick-3 lottery combinations to be drawn. The computers say that all 1000 pick-3 sets will come out within 50,000 drawings with a degree of certainty virtually equal to 100%.

_Ion Saliu's Paradox of N Trials_ refers to _randomly generating one element at a time from a set of N elements_. There is a set of N distinct elements (e.g. lotto numbered-balls from 1 to 49). We randomly generate (or draw) 1 element at a time for a total of N drawings (number of trials). The result will be around _63% unique_ elements and around _37% duplicates_ (more precisely named _repeats_).

Let's look at the probability situation from a different angle. _What is the probability to randomly generate N elements at a time and ALL N elements be unique?_

Let's say we have 6 dice (since a die has 6 faces or elements); we throw all 6 dice at the same time (a perfectly random draw). What is the probability that all 6 faces will be unique (i.e. from 1 to 6 in any order)? Total possible cases is calculated by the _Saliusian sets_ (or _exponents_): 6<sup>6</sup> (6 to the power of 6) or 46656. Total number of favorable cases is represented by _permutations_. The _permutations_ are calculated by the factorial: 6! = 720. We calculate the probability of 6 unique point-faces on all 6 dice by dividing permutations to exponents: _720 / 46656 = 1 in 64.8_.

We can generalize to N elements randomly drawn N at a time. The probability of all N elements be unique is equal to _permutations over exponents_. A precise formula reads:

<big>Probability of unique N elements = N! / N<sup>N</sup></big>

I created another type of probability software that randomly generates unique numbers from N elements. I even offer the source code (totally free), plus the [_**algorithms of random number generation**_](https://fr.saliu.com/random-numbers.html) (algorithm #2). For most situations, only the computer software can generate N random elements from a set of N distinct items. Also, only the software can generate _Ion Saliu's sets (exponents)_ when N is larger than even 5. Caveat: today's computers are not capable of handling very large _Saliusian sets_!

I wrote also software to simulate _Ion Saliu's Paradox of N Trials_:  
**OccupancySaliuParadox**, mathematical software also calculating the _Classical Occupancy Problem_.

Read more on my Web page: [_**Theory of Probability: Introduction, Formulae, Software, Algorithms**_.](https://fr.saliu.com/theory-of-probability.html)

[](https://fr.saliu.com/theory-of-probability.html)![The best mathematics software is free for all intelligent members.](https://fr.saliu.com/HLINE.gif)

## <u>6. Software: The Divine Tool to Further Empower Reason</u>

I wrote software to handle the _Fundamental Formula of Gambling (FFG)_ and its reverse: Anti-FFG or the _Degree of Certainty_. There are situations when we want to calculate the _Degree of Certainty_ that an event of probability _p_ will appear at least once within a _number of trials N_. As a matter of fact, this method offers a more precise correlation between an _integer_ number of trials and a degree of certainty DC expressed as a floating-point number. Furthermore, the program can determine the probability from a data series! The number of elements in the data series is known (N). Sorting the data series can determine the median: The degree of certainty DC equal to 50%!

[![Pay Membership, Download Software: Lottery, Gambling, Roulette, Sports, Powerball, Mega Millions.](https://fr.saliu.com/ScreenImgs/mathematics-formula.gif)](https://fr.saliu.com/membership.html)

FORMULA calculates several mathematical, probability, and statistics functions: Binomial distribution; standard deviation; hypergeometric distribution; odds (probability) for lotto, lottery, and gambling; normal probability rule; sums and mean average; randomization (shuffle); etc.  
The 16-bit software was superseded by **SuperFormula**. _**Super Formula**_ also calculates the _Binomial Distribution Formula (BDF),_ the _Binomial Standard Deviation (BSD)_, _Statistical Standard Deviation (BSD)_ — and then some.

• Download [_**Software: Science, Mathematics, Statistics, Lexicographic, Combinatorial**_](https://fr.saliu.com/free-science.html): **SuperFormula, FORMULA, OccupancySaliuParadox** from the software downloads site - membership is necessary (a most reasonable fee).

I assembled all my mathematics, probability, statistics, combinatorics programs in a special software package named **Scientia**.

![The best mathematics, probability, logarithm, algorithm, scientific software is Scientia.](https://fr.saliu.com/ScreenImgs/probability.gif)

![Probability Book has profound philosophical implications, like God existence, which is an absurdity.](https://fr.saliu.com/probability-book-Saliu.jpg) Read Ion Saliu's first book in print: [**_Probability Theory, Live!_**](https://fr.saliu.com/probability-book.html)  
~ Discover profound philosophical implications of the **_Formula of TheEverything_**, including Mathematics, formulas, gambling, lottery, software, computer programming, logarithm function, the absurdity of God concept.

![Read the Web pages regarding mathematics, formula of gambling and randomness.](https://fr.saliu.com/HLINE.gif)[](https://fr.saliu.com/content/probability.html)

## [<u>Resources in Theory of Probability, Mathematics, Statistics, Combinatorics, Software</u>](https://fr.saliu.com/content/probability.html)

See a comprehensive directory of the pages and materials on the subject of theory of probability, mathematics, statistics, combinatorics, plus software.

-   [_**Theory of Probability**_](https://fr.saliu.com/theory-of-probability.html): Best introduction, formulae, algorithms, software.
-   [<u><i>Bayes' Theorem</i>, Conditional Probabilities, Simulation; Relation to <i>Ion Saliu's Paradox</i></u>](https://fr.saliu.com/bayes-theorem.html).
-   [_**Birthday Paradox**_](https://fr.saliu.com/birthday.html) is a particular case of _exponential sets_ (sets with duplicate elements); software to calculate, generate any form of _Birthday Paradox_ cases.
-   [_**Probability: Pairing, Couple Swapping, Hypergeometric Distribution**_](https://fr.saliu.com/pairings.html).
-   [Mathematics of _**Monty Hall Paradox**_, _**Classical Occupancy Problem**_; _**Ion Saliu's Paradox**_](https://fr.saliu.com/monty-paradox.html).
-   [_**Randomness, Degree of Randomness, Degrees of Certainty, True Random Numbers**_](https://fr.saliu.com/bbs/messages/683.html).
-   [_**Probability Player Index N Draws Ticket Number N**_](https://fr.saliu.com/bbs/messages/998.html).
-   [_**Calculate, Generate Exponents, Permutations**_](https://fr.saliu.com/permutations.html) (Factorial), Arrangements, Combinations.
-   [_**PI Day, Divine Proportion, Golden Proportion, Golden Number**_](https://fr.saliu.com/bbs/messages/958.html), PHI, Fibonacci.
-   [_**Censorship Ideology Religion Money Jealousy Piracy Hatred Ban Internet**_](https://fr.saliu.com/censorship.html).
-   Read all about the famous [Fundamental Formula of Gambling](https://fr.saliu.com/Saliu2.htm).
-   [_**Almighty Number, Randomness, Universe, God**_](https://fr.saliu.com/almighty_number.html), Order, History, Repetition.  
    The Fundamental Formula of Gambling (FFG) may well be the Ultimate Formula of The Everything.
-   [_**Press Release: Mathematical Proof of the Absurdity of the God Concept**_](https://fr.saliu.com/press-release-god.html).

The following pages at this website offer more special mathematical solutions, functions and formulas, especially in combinatorics. There are algorithms and special software to calculate and generate permutations, exponents, combinations, both for numbers and words. Also, lexicographical order or index can be easily calculated for a large variety of sets.

-   [_**Lexicographic, Lexicographical Order**_](https://fr.saliu.com/lexicographic.html), Index, Ranking, Sets: Permutations, Exponential Sets, Combinations.
-   [_**Combination Lexicographic Order, Rank, Index**_](https://fr.saliu.com/bbs/messages/10.html): Comprehensive and Fast Algorithm.
-   [_**Combination Sequence Number**_](https://fr.saliu.com/bbs/messages/871.html) or Lexicographic Order Debates.
-   [_**Combination Sequence Number or Lexicographic Order**_](https://fr.saliu.com/bbs/messages/348.html): Mathematical Algorithms.
-   [_**Combination**_ _1,2,3,4,5,6_: _**Probability, Odds, Statistics**_](https://fr.saliu.com/bbs/messages/961.html).
-   There are no naturally-occurring [_**Geometric Shapes or Perfect Forms on Earth**_](https://fr.saliu.com/bbs/messages/636.html), indeed in the Universe, to justify a _super intelligent, divine force_ in Cosmos.  
    
-   Only humans create perfect shapes (geometric as in geometry) in an attempt to have _relative control over randomness_.
-   Download the [**best software**](https://fr.saliu.com/infodown.html).

![Thanks for reading my essay on mathematics, probability, God inexistence, randomness, gambling.](https://fr.saliu.com/HLINE.gif)

**[Home](https://fr.saliu.com/index.htm) | [Search](https://fr.saliu.com/Search.htm) | [New Writings](https://fr.saliu.com/bbs/index.html) | [Odds, Generator](https://fr.saliu.com/calculator_generator.html) | [Contents](https://fr.saliu.com/content/index.html) | [Forums](https://forums.saliu.com/) | [Sitemap](https://fr.saliu.com/sitemap/index.html)**

![Thanks for visiting the best site of mathematics, probability, logarithms, formulas, randomness.](https://fr.saliu.com/HLINE.gif)
