# FIVE Filter Tests
# FIVE 過濾器測試

using Test
using Dates

# 引入必要的模組
include("../src/types.jl")
include("../src/filter_engine.jl")
include("../src/filters/five_filter.jl")

"""
創建測試用的彩票數據
"""
function create_test_lottery_data_for_quintuplets()::Vector{LotteryDraw}
    test_draws = [
        LotteryDraw([1, 2, 3, 4, 5], Date(2022, 1, 10), 10),  # 最新
        LotteryDraw([6, 7, 8, 9, 10], Date(2022, 1, 9), 9),
        LotteryDraw([1, 2, 3, 4, 11], Date(2022, 1, 8), 8),   # 與第一個相似（4個相同）
        LotteryDraw([12, 13, 14, 15, 16], Date(2022, 1, 7), 7),
        LotteryDraw([1, 2, 3, 17, 18], Date(2022, 1, 6), 6),  # 與第一個相似（3個相同）
        LotteryDraw([1, 2, 3, 4, 5], Date(2022, 1, 5), 5),   # 與第一個完全相同
        LotteryDraw([19, 20, 21, 22, 23], Date(2022, 1, 4), 4),
        LotteryDraw([24, 25, 26, 27, 28], Date(2022, 1, 3), 3),
        LotteryDraw([29, 30, 31, 32, 33], Date(2022, 1, 2), 2),
        LotteryDraw([34, 35, 36, 37, 38], Date(2022, 1, 1), 1),  # 最舊
    ]
    return test_draws
end

"""
測試五號組合生成功能
"""
function test_quintuplet_generation()
    @testset "Quintuplet Generation" begin
        # 測試正常情況
        numbers = [1, 2, 3, 4, 5, 6]
        quintuplets = generate_quintuplets(numbers)
        expected_count = binomial(6, 5)  # C(6,5) = 6
        @test length(quintuplets) == expected_count
        
        # 驗證具體的五號組合
        expected_quintuplets = [
            (1, 2, 3, 4, 5), (1, 2, 3, 4, 6), (1, 2, 3, 5, 6),
            (1, 2, 4, 5, 6), (1, 3, 4, 5, 6), (2, 3, 4, 5, 6)
        ]
        @test all(quint in expected_quintuplets for quint in quintuplets)
        
        # 測試邊界條件
        @test isempty(generate_quintuplets([1, 2, 3, 4]))  # 只有四個號碼
        @test isempty(generate_quintuplets([1]))           # 只有一個號碼
        @test isempty(generate_quintuplets(Int[]))         # 空數組
        
        # 測試五個號碼
        five_numbers = [5, 2, 8, 1, 9]
        five_quintuplets = generate_quintuplets(five_numbers)
        @test length(five_quintuplets) == 1
        @test five_quintuplets[1] == (5, 2, 8, 1, 9)  # 應該保持原順序
        
        println("✓ 五號組合生成測試通過")
    end
end

"""
測試組合唯一性檢查
"""
function test_combination_uniqueness()
    @testset "Combination Uniqueness" begin
        test_data = create_test_lottery_data_for_quintuplets()
        engine = FilterEngine(test_data)
        
        # 測試重複組合 [1,2,3,4,5] - 應該出現 2 次
        uniqueness_duplicate = check_combination_uniqueness(engine, [1, 2, 3, 4, 5])
        @test !uniqueness_duplicate["is_unique"]
        @test uniqueness_duplicate["exact_matches"] == 2
        
        # 測試唯一組合 [6,7,8,9,10] - 應該出現 1 次（不是唯一的）
        uniqueness_unique = check_combination_uniqueness(engine, [6, 7, 8, 9, 10])
        @test !uniqueness_unique["is_unique"]  # 出現過 1 次，所以不是唯一的
        @test uniqueness_unique["exact_matches"] == 1
        
        # 測試從未出現的組合
        uniqueness_never = check_combination_uniqueness(engine, [11, 12, 13, 14, 15])
        @test uniqueness_never["is_unique"]
        @test uniqueness_never["exact_matches"] == 0  # 在測試數據中沒有出現過
        
        # 測試相似組合檢查
        @test uniqueness_duplicate["similar_matches"] >= 0
        
        println("✓ 組合唯一性檢查測試通過")
    end
end

"""
測試重複組合分析
"""
function test_duplicate_combinations_analysis()
    @testset "Duplicate Combinations Analysis" begin
        test_data = create_test_lottery_data_for_quintuplets()
        engine = FilterEngine(test_data)
        
        # 分析重複組合
        analysis = analyze_duplicate_combinations(engine)
        
        # 驗證分析結果結構
        @test haskey(analysis, "total_combinations")
        @test haskey(analysis, "unique_combinations")
        @test haskey(analysis, "duplicate_combinations")
        @test haskey(analysis, "uniqueness_rate")
        @test haskey(analysis, "max_repetitions")
        @test haskey(analysis, "most_frequent_combinations")
        
        @test analysis["total_combinations"] == length(Set([sort(draw.numbers) for draw in test_data]))
        @test analysis["max_repetitions"] >= 1
        @test 0.0 <= analysis["uniqueness_rate"] <= 1.0
        
        println("✓ 重複組合分析測試通過")
        println("  - 總組合數: $(analysis["total_combinations"])")
        println("  - 唯一組合數: $(analysis["unique_combinations"])")
        println("  - 重複組合數: $(analysis["duplicate_combinations"])")
        println("  - 唯一性比率: $(round(analysis["uniqueness_rate"], digits=2))")
    end
end

"""
測試重複機率計算
"""
function test_repetition_probability()
    @testset "Repetition Probability" begin
        test_data = create_test_lottery_data_for_quintuplets()
        engine = FilterEngine(test_data)
        
        # 計算重複機率
        repetition_prob = calculate_repetition_probability(engine)
        
        @test 0.0 <= repetition_prob <= 1.0
        
        # 空數據的情況
        empty_engine = FilterEngine(LotteryDraw[])
        empty_prob = calculate_repetition_probability(empty_engine)
        @test empty_prob == 0.0
        
        println("✓ 重複機率計算測試通過")
        println("  - 重複機率: $(round(repetition_prob, digits=3))")
    end
end

"""
測試 FIVE 過濾器計算
"""
function test_five_filter_calculation()
    @testset "FIVE Filter Calculation" begin
        test_data = create_test_lottery_data_for_quintuplets()
        engine = FilterEngine(test_data)
        
        # 測試重複組合
        duplicate_combo = [1, 2, 3, 4, 5]
        result_duplicate = calculate_five_filter(engine, duplicate_combo)
        
        # 驗證結果結構
        @test result_duplicate.filter_type == FIVE_FILTER
        @test occursin("FIVE_FILTER", result_duplicate.filter_name)
        @test isa(result_duplicate.current_value, Int)
        @test isa(result_duplicate.expected_value, Float64)
        @test isa(result_duplicate.is_favorable, Bool)
        @test 0.0 <= result_duplicate.confidence_level <= 1.0
        @test isa(result_duplicate.historical_values, Vector{Int})
        @test result_duplicate.calculation_time >= 0.0
        
        # 測試唯一組合
        unique_combo = [11, 12, 13, 14, 15]
        result_unique = calculate_five_filter(engine, unique_combo)
        
        # 唯一組合應該有更高的唯一性分數
        @test result_unique.current_value >= result_duplicate.current_value
        
        println("✓ FIVE 過濾器計算測試通過")
        println("  - 重複組合唯一性分數: $(result_duplicate.current_value)")
        println("  - 唯一組合唯一性分數: $(result_unique.current_value)")
        println("  - 重複組合是否有利: $(result_duplicate.is_favorable)")
        println("  - 唯一組合是否有利: $(result_unique.is_favorable)")
    end
end

"""
測試最佳唯一組合建議
"""
function test_best_unique_combinations()
    @testset "Best Unique Combinations" begin
        test_data = create_test_lottery_data_for_quintuplets()
        engine = FilterEngine(test_data)
        
        # 獲取候選號碼的最佳唯一組合
        candidate_numbers = [1, 2, 3, 4, 5, 6]
        best_combos = get_best_unique_combinations(engine, candidate_numbers, 3)
        
        @test length(best_combos) == 3  # 應該有 3 個結果
        @test all(haskey(combo, "recommendation_score") for combo in best_combos)
        
        # 驗證排序（推薦分數應該遞減）
        for i in 2:length(best_combos)
            @test best_combos[i-1]["recommendation_score"] >= best_combos[i]["recommendation_score"]
        end
        
        # 測試號碼不足的情況
        insufficient_combos = get_best_unique_combinations(engine, [1, 2, 3, 4], 3)
        @test isempty(insufficient_combos)
        
        println("✓ 最佳唯一組合建議測試通過")
    end
end

"""
測試歷史相似性檢查
"""
function test_historical_similarity()
    @testset "Historical Similarity" begin
        test_data = create_test_lottery_data_for_quintuplets()
        engine = FilterEngine(test_data)
        
        # 檢查組合的歷史相似性
        test_combo = [1, 2, 3, 4, 5]
        similarity = check_historical_similarity(engine, test_combo)
        
        # 驗證相似性分析結構
        @test haskey(similarity, "combination")
        @test haskey(similarity, "similarity_distribution")
        @test haskey(similarity, "exact_matches")
        @test haskey(similarity, "four_matches")
        @test haskey(similarity, "three_matches")
        @test haskey(similarity, "total_analyzed")
        
        @test similarity["total_analyzed"] == length(test_data)
        @test similarity["exact_matches"] >= 0
        
        # 測試錯誤處理
        error_result = check_historical_similarity(engine, [1, 2, 3, 4])
        @test haskey(error_result, "error")
        
        println("✓ 歷史相似性檢查測試通過")
        println("  - 完全匹配: $(similarity["exact_matches"])")
        println("  - 四號匹配: $(similarity["four_matches"])")
        println("  - 三號匹配: $(similarity["three_matches"])")
    end
end

"""
測試 FIVE 過濾器統計摘要
"""
function test_five_filter_summary()
    @testset "FIVE Filter Summary" begin
        test_data = create_test_lottery_data_for_quintuplets()
        engine = FilterEngine(test_data)
        
        # 計算多個組合的 FIVE 過濾器結果
        test_combinations = [
            [1, 2, 3, 4, 5],
            [6, 7, 8, 9, 10],
            [11, 12, 13, 14, 15],
            [16, 17, 18, 19, 20]
        ]
        
        results = FilterResult[]
        for combo in test_combinations
            result = calculate_five_filter(engine, combo)
            push!(results, result)
        end
        
        # 獲取統計摘要
        summary = get_five_filter_summary(results)
        
        # 驗證摘要結構
        @test haskey(summary, "total_combinations")
        @test haskey(summary, "favorable_combinations")
        @test haskey(summary, "unique_combinations")
        @test haskey(summary, "average_confidence")
        @test haskey(summary, "average_uniqueness_score")
        @test haskey(summary, "uniqueness_distribution")
        
        @test summary["total_combinations"] == length(test_combinations)
        
        println("✓ FIVE 過濾器統計摘要測試通過")
        println("  - 總組合數: $(summary["total_combinations"])")
        println("  - 有利組合數: $(summary["favorable_combinations"])")
        println("  - 唯一組合數: $(summary["unique_combinations"])")
        println("  - 平均信心水準: $(round(summary["average_confidence"], digits=2))")
    end
end

"""
測試快取功能
"""
function test_five_filter_caching()
    @testset "FIVE Filter Caching" begin
        test_data = create_test_lottery_data_for_quintuplets()
        engine = FilterEngine(test_data, cache_enabled=true)
        
        test_combination = [1, 2, 3, 4, 5]
        
        # 第一次計算
        result1 = calculate_five_filter(engine, test_combination)
        cache_size_after_first = length(engine.filter_cache)
        
        # 第二次計算（應該使用快取）
        result2 = calculate_five_filter(engine, test_combination)
        cache_size_after_second = length(engine.filter_cache)
        
        # 驗證快取工作
        @test cache_size_after_first >= 1
        @test cache_size_after_second == cache_size_after_first  # 快取大小不變
        @test result1.current_value == result2.current_value
        @test result1.expected_value == result2.expected_value
        
        println("✓ FIVE 過濾器快取功能測試通過")
    end
end

"""
測試錯誤處理
"""
function test_five_filter_error_handling()
    @testset "FIVE Filter Error Handling" begin
        test_data = create_test_lottery_data_for_quintuplets()
        engine = FilterEngine(test_data)
        
        # 測試號碼數量不正確
        @test_throws ArgumentError calculate_five_filter(engine, [1, 2, 3, 4])
        @test_throws ArgumentError calculate_five_filter(engine, [1, 2, 3, 4, 5, 6])
        @test_throws ArgumentError calculate_five_filter(engine, Int[])
        
        # 測試無效號碼範圍
        @test_throws ArgumentError calculate_five_filter(engine, [0, 1, 2, 3, 4])
        @test_throws ArgumentError calculate_five_filter(engine, [1, 2, 3, 4, 40])
        
        # 測試空數據
        empty_engine = FilterEngine(LotteryDraw[])
        @test_throws ArgumentError calculate_five_filter(empty_engine, [1, 2, 3, 4, 5])
        
        println("✓ FIVE 過濾器錯誤處理測試通過")
    end
end

"""
執行所有 FIVE 過濾器測試
"""
function run_five_filter_tests()
    println("🧪 開始執行 FIVE 過濾器測試...")
    
    try
        test_quintuplet_generation()
        test_combination_uniqueness()
        test_duplicate_combinations_analysis()
        test_repetition_probability()
        test_five_filter_calculation()
        test_best_unique_combinations()
        test_historical_similarity()
        test_five_filter_summary()
        test_five_filter_caching()
        test_five_filter_error_handling()
        
        println("\n🎉 所有 FIVE 過濾器測試通過！")
        return true
    catch e
        println("\n❌ FIVE 過濾器測試失敗: $e")
        return false
    end
end

# 如果直接執行此文件，運行測試
if abspath(PROGRAM_FILE) == @__FILE__
    run_five_filter_tests()
end
