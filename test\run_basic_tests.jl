# Basic Test Runner
# 基礎測試運行器 - 運行基本的綜合測試

using Test

# 引入所有必要的模組
include("../src/types.jl")
include("../src/filter_engine.jl")
include("../src/filters/one_filter.jl")
include("../src/filters/two_filter.jl")
include("../src/filters/three_filter.jl")
include("../src/filters/four_filter.jl")
include("../src/filters/five_filter.jl")
include("../src/statistics/basic_stats.jl")
include("../src/skip_analyzer.jl")

# 引入測試模組
include("test_configuration.jl")
include("test_data_manager.jl")
include("test_skip_calculation_comprehensive.jl")

"""
運行基礎功能測試
"""
function run_basic_functionality_tests(engine)::Dict{String, Any}
    println("🔧 執行基礎功能測試...")
    
    results = Dict{String, Any}()
    
    @testset "Basic Functionality Tests" begin
        # 測試引擎基本功能
        info = get_engine_info(engine)
        @test haskey(info, "data_points")
        @test info["data_points"] > 0
        
        # 測試快取功能
        original_cache_size = length(engine.filter_cache)
        clear_cache!(engine)
        @test length(engine.filter_cache) == 0
        
        # 測試過濾器啟用/停用
        @test is_filter_enabled(engine, ONE_FILTER)
        disable_filter!(engine, ONE_FILTER)
        @test !is_filter_enabled(engine, ONE_FILTER)
        enable_filter!(engine, ONE_FILTER)
        @test is_filter_enabled(engine, ONE_FILTER)
        
        results["basic_tests_passed"] = true
    end
    
    println("  ✅ 基礎功能測試完成")
    return results
end

"""
運行過濾器測試
"""
function run_filter_tests(engine)::Dict{String, Any}
    println("🔍 執行過濾器測試...")
    
    results = Dict{String, Any}()
    filter_results = Dict{String, Bool}()
    
    @testset "Filter Tests" begin
        # 測試 ONE 過濾器
        try
            one_result = calculate_one_filter(engine, 1)
            @test one_result.filter_type == ONE_FILTER
            @test one_result.current_value >= 0
            filter_results["ONE_FILTER"] = true
        catch e
            @warn "ONE 過濾器測試失敗: $e"
            filter_results["ONE_FILTER"] = false
        end
        
        # 測試 TWO 過濾器
        try
            two_result = calculate_two_filter(engine, [1, 2, 3])
            @test two_result.filter_type == TWO_FILTER
            @test two_result.current_value >= 0
            filter_results["TWO_FILTER"] = true
        catch e
            @warn "TWO 過濾器測試失敗: $e"
            filter_results["TWO_FILTER"] = false
        end
        
        # 測試 THREE 過濾器
        try
            three_result = calculate_three_filter(engine, [1, 2, 3, 4])
            @test three_result.filter_type == THREE_FILTER
            @test three_result.current_value >= 0
            filter_results["THREE_FILTER"] = true
        catch e
            @warn "THREE 過濾器測試失敗: $e"
            filter_results["THREE_FILTER"] = false
        end
        
        # 測試 FOUR 過濾器
        try
            four_result = calculate_four_filter(engine, [1, 2, 3, 4, 5])
            @test four_result.filter_type == FOUR_FILTER
            @test four_result.current_value >= 0
            filter_results["FOUR_FILTER"] = true
        catch e
            @warn "FOUR 過濾器測試失敗: $e"
            filter_results["FOUR_FILTER"] = false
        end
        
        # 測試 FIVE 過濾器
        try
            five_result = calculate_five_filter(engine, [1, 2, 3, 4, 5])
            @test five_result.filter_type == FIVE_FILTER
            @test five_result.current_value >= 0
            filter_results["FIVE_FILTER"] = true
        catch e
            @warn "FIVE 過濾器測試失敗: $e"
            filter_results["FIVE_FILTER"] = false
        end
    end
    
    results["filter_results"] = filter_results
    results["passed_filters"] = count(values(filter_results))
    results["total_filters"] = length(filter_results)
    
    println("  ✅ 過濾器測試完成: $(results["passed_filters"])/$(results["total_filters"]) 通過")
    return results
end

"""
運行統計測試
"""
function run_statistics_tests(engine)::Dict{String, Any}
    println("📊 執行統計測試...")
    
    results = Dict{String, Any}()
    
    @testset "Statistics Tests" begin
        # 測試基礎統計函數
        test_data = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
        
        @test calculate_median(test_data) == 5.5
        @test calculate_mean(test_data) == 5.5
        @test calculate_std_dev(test_data) ≈ 3.0276503540974917
        
        # 測試頻率分佈
        freq_dist = calculate_frequency_distribution(test_data)
        @test length(freq_dist) == 10
        @test all(count == 1 for count in values(freq_dist))
        
        # 測試引擎統計
        engine_stats = get_engine_statistics(engine)
        @test haskey(engine_stats, "total_draws")
        @test haskey(engine_stats, "hot_numbers")
        @test haskey(engine_stats, "cold_numbers")
        
        results["statistics_tests_passed"] = true
    end
    
    println("  ✅ 統計測試完成")
    return results
end

"""
運行快取測試
"""
function run_cache_tests(engine)::Dict{String, Any}
    println("💾 執行快取測試...")
    
    results = Dict{String, Any}()
    
    @testset "Cache Tests" begin
        # 清除快取
        clear_cache!(engine)
        initial_cache_size = length(engine.filter_cache)
        
        # 執行一些計算來填充快取
        calculate_one_filter(engine, 1)
        calculate_two_filter(engine, [1, 2, 3])
        
        cache_size_after = length(engine.filter_cache)
        @test cache_size_after > initial_cache_size
        
        # 測試快取命中（只檢查功能，不檢查性能）
        result1 = calculate_one_filter(engine, 1)  # 應該使用快取

        # 清除快取並重新計算
        clear_cache!(engine)
        result2 = calculate_one_filter(engine, 1)  # 應該重新計算

        # 結果應該一致
        @test result1.current_value == result2.current_value
        @test result1.expected_value == result2.expected_value
        
        results["cache_tests_passed"] = true
    end
    
    println("  ✅ 快取測試完成")
    return results
end

"""
運行數據一致性測試
"""
function run_data_consistency_tests(engine)::Dict{String, Any}
    println("🔍 執行數據一致性測試...")
    
    results = Dict{String, Any}()
    
    @testset "Data Consistency Tests" begin
        # 測試數據完整性
        @test !isempty(engine.historical_data)
        
        # 測試數據格式
        for draw in engine.historical_data[1:min(10, length(engine.historical_data))]
            @test length(draw.numbers) == 5
            @test all(1 <= n <= 39 for n in draw.numbers)
            @test draw.numbers == sort(draw.numbers)
            @test length(unique(draw.numbers)) == 5
        end
        
        # 測試日期順序
        if length(engine.historical_data) > 1
            for i in 2:min(10, length(engine.historical_data))
                @test engine.historical_data[i-1].draw_date >= engine.historical_data[i].draw_date
            end
        end
        
        results["consistency_tests_passed"] = true
    end
    
    println("  ✅ 數據一致性測試完成")
    return results
end

"""
運行邊界測試
"""
function run_boundary_tests(engine)::Dict{String, Any}
    println("🔬 執行邊界測試...")
    
    results = Dict{String, Any}()
    
    @testset "Boundary Tests" begin
        # 測試邊界號碼
        @test_throws ArgumentError calculate_one_filter(engine, 0)
        @test_throws ArgumentError calculate_one_filter(engine, 40)
        
        # 測試空組合
        @test_throws ArgumentError calculate_two_filter(engine, Int[])
        @test_throws ArgumentError calculate_three_filter(engine, [1, 2])
        @test_throws ArgumentError calculate_four_filter(engine, [1, 2, 3])
        @test_throws ArgumentError calculate_five_filter(engine, [1, 2, 3, 4])
        
        # 測試過多號碼
        @test_throws ArgumentError calculate_five_filter(engine, [1, 2, 3, 4, 5, 6])
        
        results["boundary_tests_passed"] = true
    end
    
    println("  ✅ 邊界測試完成")
    return results
end

"""
運行快速測試套件
"""
function run_quick_test_suite()
    println("🚀 開始執行快速測試套件...")

    # 創建快速測試配置
    config = create_quick_test_configuration()

    # 創建測試數據管理器
    data_manager = TestDataManager(config.data)

    # 生成測試數據
    test_data = get_test_data(data_manager, "small")
    global engine = FilterEngine(test_data, cache_enabled=true)
    
    # 執行基本測試
    results = Dict{String, Any}()
    
    try
        results["basic"] = run_basic_functionality_tests(engine)
        results["filters"] = run_filter_tests(engine)
        results["statistics"] = run_statistics_tests(engine)
        results["cache"] = run_cache_tests(engine)
        results["consistency"] = run_data_consistency_tests(engine)
        results["boundary"] = run_boundary_tests(engine)
        
        # 執行 Skip 計算測試
        results["skip_comprehensive"] = run_comprehensive_skip_tests(data_manager)
        
        println("\n🎉 快速測試套件執行完成！")
        
        # 計算成功率
        passed_tests = 0
        total_tests = 0
        
        for (test_name, test_result) in results
            if test_name != "skip_comprehensive"
                if haskey(test_result, "basic_tests_passed") && test_result["basic_tests_passed"]
                    passed_tests += 1
                elseif haskey(test_result, "filter_results")
                    passed_tests += test_result["passed_filters"]
                    total_tests += test_result["total_filters"] - 1  # 減去已計算的
                elseif haskey(test_result, "statistics_tests_passed") && test_result["statistics_tests_passed"]
                    passed_tests += 1
                elseif haskey(test_result, "cache_tests_passed") && test_result["cache_tests_passed"]
                    passed_tests += 1
                elseif haskey(test_result, "consistency_tests_passed") && test_result["consistency_tests_passed"]
                    passed_tests += 1
                elseif haskey(test_result, "boundary_tests_passed") && test_result["boundary_tests_passed"]
                    passed_tests += 1
                end
                total_tests += 1
            end
        end
        
        success_rate = total_tests > 0 ? passed_tests / total_tests : 0.0
        println("📊 測試成功率: $(round(success_rate * 100, digits=1))%")
        
        return results
        
    catch e
        @error "快速測試套件執行失敗: $e"
        return Dict("error" => string(e))
    finally
        # 清理測試數據
        cleanup_test_data!(data_manager)
    end
end

# 如果直接執行此文件，運行快速測試
if abspath(PROGRAM_FILE) == @__FILE__
    run_quick_test_suite()
end

# 導出主要函數
export run_quick_test_suite, run_basic_functionality_tests, run_filter_tests
export run_statistics_tests, run_cache_tests, run_data_consistency_tests, run_boundary_tests
