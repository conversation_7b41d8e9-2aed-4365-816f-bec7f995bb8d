## Ion Saliu 彩票與賭博理論：核心主題與重要事實

Ion Saliu 的作品以其在彩票、樂透和賭博策略方面的數學方法而聞名，強調以機率論和統計學為基礎，利用專有軟體優化遊戲玩法。

### I. 核心理論與概念

- **賭博基本公式 (FFG)**：Saliu 工作的基石。「FFG 發現了機率論和宇宙中最基本的元素：確定度 DC、機率 p 和試驗次數 N 之間的關係。」(0004)。該公式計算特定事件 (機率為 _p_) 在 _N_ 次試驗中發生的確定度，或計算達到某確定度所需的試驗次數。它被認為是「博弈論中最精確的工具」(0004)。
- **Ion Saliu 的 N 次試驗悖論 (Ion Saliu's Paradox of N Trials)**：此悖論指出，若事件機率為 1/N，則在 N 次試驗中，該事件發生的確定度趨近於 1 – (1/e)（約 63.2%），而非 100% (0004, 0009)。這強調了即使在理想情況下，也無法達到絕對確定性。
- **機率與統計的應用**：Saliu 強調，儘管單一事件的機率是固定的，但統計趨勢和歷史數據對於預測未來事件至關重要。「與普遍的看法相反，過去的開獎在任何機會遊戲中都算數」(0006)。他的方法旨在利用這些統計數據來提高獲勝機會。
- **連續現象 (Streaks) 與跳過 (Skips)**：這些是 Saliu 賭博理論的核心。
- **跳過 (Skip)**：「跳過只是特定彩票號碼（數字）兩次中獎之間開獎的次數。」(0005)。它衡量了數字在再次出現之前「閒置」的開獎次數 (0006)。
- **連續現象 (Streak)**：連續現象代表連續的輸贏情況。Saliu 認為，「賭博的科學是連續現象的科學」(0008)。他的軟體和策略追蹤這些連續現象，以確定最佳的下注時間和金額。
- **FFG 中位數 (FFG Median)**：「每個樂透號碼在所有中獎情況的 50% 以上，會在等於 FFG 中位數的開獎次數後重複。」(0005)。這是一個關鍵的統計閾值，用於濾波器設定和策略制定。例如，輪盤賭中，單零輪盤的 FFG 中位數為 25 次旋轉 (0007)。

### II. 彩票策略與濾波器

Saliu 的彩票策略主要圍繞著使用「濾波器」來減少需玩的組合數量，從而提高贏得大獎的機率。

- **濾波器 (Filters)**：彩票濾波器是「消除 LotWon 彩票和彩票軟體生成過程中的組合的參數。」(0001)。它們本質上是限制條件。
- **最低級別 (Minimum Level)**：「最低級別僅允許高於該級別的彩票組合。它消除了組合範圍內的所有內容。」(0001)。
- **最高級別 (Maximum Level)**：「最高級別只允許低於該級別的彩票組合。它消除了所有不在組合範圍內的內容。」(0001)。
- **濾波器效率 (Filter Efficiency)**：「效率是指濾波器消除的彩票組合數量。」(0001)。濾波器值越高，其效率越高 (0001)。
- **濾波器設定 (Filter Setting)**：Saliu 建議將濾波器設定在「正常範圍」之外，該範圍由相應彩票軟體濾波器的中位數決定 (0001)。嚴格的濾波器設定（例如，中位數乘以 3、4 或 5，或除以 3、4 或 5）可以顯著減少組合數量 (0001)。
- **三種主要策略類型 (Three Main Strategy Types)**：基於支點濾波器 (pivot filter) 級別，主要有三種類型的樂透策略 (0001)：
- **最低值 (Minimum Values)**：應用濾波器的最大級別。
- **最高值 (Highest Values)**：應用濾波器的最低級別。
- **中位數 (Median)**：應用濾波器的最低和最高級別。
- **反向彩票策略 (Reverse Lottery Strategy) / LIE 消除 (LIE Elimination)**：「彩票漏獎比中獎更頻繁。我們反向操作策略：故意設定樂透或彩票濾波器，使其在下一次開獎中不會中獎，從而從損失中獲利。」(0002)。這涉及生成已知不會中獎的組合，然後將其從可能的組合中剔除 (0002)。這種方法「幾乎是自動 LIE 消除的候選者」(0001)。
- **策略組合 (Combining Strategies)**：Saliu 鼓勵組合不同層級和不同程式的濾波器，以創建更強大的策略 (0001)。他還建議同時玩多個策略，而不是一次只玩一個 (0001)。
- **資料檔案的重要性 (Importance of Data Files)**：Saliu 強調準確和全面的歷史開獎資料檔案對於統計分析和策略制定至關重要 (0001, 0006)。他建議使用大型模擬資料檔案（例如，100,000 行以上）來進行濾波器分析 (0006)。

### III. 專有軟體與工具

Ion Saliu 開發了多款彩票、樂透和賭博軟體，以實施其數學理論和策略。

- **MDIEditor Lotto WE**：「MDIEditor Lotto WE 是歷史上最全面的彩票賭博軟體。」(0006, 0012)。它支援多種遊戲，包括 Pick-3、Pick-4、賽馬、樂透 5、樂透 6、樂透 7、強力球、超級百萬和歐洲百萬 (0006)。該軟體提供隨機組合生成、統計分析和報告、優化組合生成、中獎組合檢查和策略檢查等功能 (0006)。
- **Bright / Ultimate Software 系列**：這些是 Saliu 最先進的彩票軟體應用程式 (0001)。它們包括 **Bright3** (Pick-3)、**Bright4** (Pick-4)、**Bright5** (Lotto-5)、**Bright6** (Lotto-6) 等，以及 **Ultimate Software** (0001, 0010)。
- **SuperFormula**：「SuperFormula 是用於統計、機率、賠率、賭博數學…以及更多功能的權威軟體。」(0004)。它根據 FFG 計算 N、p 和 DC，並執行二項式分佈、標準差等各種機率計算 (0004)。
- **SkipSystem (跳過系統)**：此軟體基於跳過值創建彩票系統和策略，特別是根據 FFG 中位數下的跳過數 (0005, 0009)。
- **Combine6-12.exe / Bright12.exe**：生成 6 號樂透遊戲中的 12 號組合，然後將其輪盤化為 6 號組合 (0013, 0014)。
- **Cluster49-7.exe / Bright49.exe**：生成 6/49 樂透的 7x7 數字組合矩陣 (0013, 0007)。
- **Super Utilities**：一款多功能實用程式，用於處理組合、生成報告和執行各種數據操作 (0011)。
- **命令行軟體 (Command Prompt Software)**：Saliu 的許多早期和部分高級軟體都以命令行界面運行 (例如 LotWon 系列)，他強調其速度和可靠性優於圖形界面 (GUI) (0001, 0011)。
- **資料檔案管理工具**：包括 **PARSEL**（檢查資料檔案的正確性）、**UpDown**（反轉檔案中開獎的順序）和 **SORTING**（對開獎或組合進行排序）(0006, 0013)。

### IV. 賭博遊戲與策略應用

Saliu 的理論和軟體不僅適用於彩票，也適用於各種賭博遊戲。

- **輪盤賭 (Roulette)**：Saliu 認為輪盤賭中存在「偏差」(bias)，並開發了基於重複數字、車輪偏見和 FFG 中位數下跳過值的策略 (0007)。他批評許多昂貴的輪盤系統，聲稱它們是詐欺或效果不如隨機遊戲 (0007)。
- **二十一點 (Blackjack)**：Saliu 應用 FFG 和連續現象理論來開發二十一點策略，包括「心理二十一點系統」(Blackjack Mental System) (0008, 0007)。他還開發了精確計算爆牌機率和賭場優勢的軟體 (0013)。
- **百家樂 (Baccarat)**：Saliu 認為百家樂是賭場中最公平的遊戲之一，並提倡基於連續現象和跳過的策略 (0008)。他甚至提出了「超級百家樂」(Super Baccarat) 的新遊戲規則，以增加玩家的參與度 (0008)。
- **骰寶 (Sic Bo)**：Saliu 分析了骰寶的單數下注 (Single Dice Bet)，並透過 FFG 證明其賭場優勢幾乎為零，使其成為賭場中最佳的下注之一 (0008)。
- **賭場戰爭 (Casino War)**：Saliu 提出 FFG 可以應用於賭場戰爭，透過追蹤輸贏連續現象來制定策略，類似於輪盤賭的紅/黑下注 (0008)。
- **賽馬 (Horse Racing)**：Saliu 將賽馬視為一種數字彩票，並為三連勝 (trifectas) 和四連勝 (superfectas) 開發了統計分析、濾波器報告和優化組合生成軟體 (0006, 0015)。

### V. 其他重要見解與主張

- **對 AI 聊天機器人的看法**：Saliu 對 AI 持懷疑態度，稱其為「矛盾修飾」(oxymoron)。他對 OpenAI 的 ChatGPT 和 Bing Chat 進行了測試，發現 Bing Chat 更準確全面，因為它能即時訪問網路 (0012)。他批評 Google Bard 在事實方面存在嚴重錯誤 (0012)。
- **對「真理」的哲學觀點**：Saliu 認為「真理」是獨立於人類情感和感知的，並且是「關係」(relations)，最常見的是「數值關係」(numerical) (0011)。他提出「人類的根本罪過是將自己與真理等同起來」(0011)。
- **批評其他賭博系統和網站**：Saliu 猛烈抨擊那些兜售昂貴、無效賭博系統的人，並聲稱許多線上賭場存在作弊行為 (0007, 0016)。他認為這些是賭場為詆毀其數學系統而採取的陰謀的一部分 (0007)。
- **資料檔案格式與管理**：Saliu 強調彩票資料檔案應按降序排列（最新開獎在頂部），並且不應包含空白行、日期或獎金資訊 (0001, 0006, 0011)。他建議定期檢查資料檔案是否有錯誤 (0006, 0011)。
- **輪盤賭輪盤與彩票輪盤**：Saliu 批評傳統的彩票輪盤 (lotto wheels) 會降低贏得大獎的機會，尤其在強力球等遊戲中 (0009)。他傾向於隨機選擇與輪盤線數相等的組合，並使用其專有軟體中的特定輪盤 (例如 12 號輪盤)，因為它們基於紮實的數學基礎 (0014, 0009)。
- **「Wonder Grid」策略**：這是一種基於數字最佳配對頻率的彩票策略，對於贏得較高獎項（尤其是頭獎）比隨機遊戲更有效 (0011)。

Ion Saliu 的作品提供了一套全面的彩票和賭博方法論，深深植根於機率論和統計學。他強調數據分析、濾波器應用和反向策略的重要性，並開發了專有軟體來支持這些複雜的方法。