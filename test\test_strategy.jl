using Test
using SaliuSystem0009.Strategy

@testset "Strategy.jl tests" begin
    # Test Filter struct
    filter = Filter("NumberRange", 1, 49)
    @test filter.name == "NumberRange"
    @test filter.min_level == 1
    @test filter.max_level == 49

    # Test Strategy struct
    strategy = Strategy("MyStrategy", [filter])
    @test strategy.name == "MyStrategy"
    @test length(strategy.filters) == 1
    @test strategy.filters[1] == filter

    # Test create_strategy function
    new_strategy = create_strategy("AnotherStrategy", [Filter("OddEven", 3, 3)])
    @test new_strategy.name == "AnotherStrategy"
    @test length(new_strategy.filters) == 1

    # Test save_strategy function (requires a dummy file path)
    dummy_strategy_path = "dummy_strategy.txt"
    save_strategy(new_strategy, dummy_strategy_path)
    @test isfile(dummy_strategy_path)
    
    # Verify content (simple check)
    content = read(dummy_strategy_path, String)
    @test occursin("Strategy: AnotherStrategy", content)
    @test occursin("Filter: OddEven, Min: 3, Max: 3", content)

    # Clean up dummy file
    rm(dummy_strategy_path)
end