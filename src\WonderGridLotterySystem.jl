module WonderGridLotterySystem

using CSV
using DataFrames
using Dates
using Statistics
using StatsBase
using Random
using Combinatorics

# Core data types
export LotteryDraw, WonderGridConfig, PairingFrequency, StrategyResult
export StatisticalSummary, Ski<PERSON>Chart, BacktestResult, ValidationResult
export DATA5Format, WonderGridFormat, IntegrityReport, HitRates
export EfficiencyComparison, CostAnalysis

# Core components
export DataValidator, FileManager, StatisticsEngine
export FFGCalculator, SkipAnalyzer, PairingEngine
export WonderGridEngine, LIEEliminationEngine, BacktestingEngine
export PerformanceReporter, PerformanceReport, CombinationGenerator

# Core functions
export validate_lottery_numbers, validate_data5_file, validate_data5_file_flexible, check_chronological_order, validate_draw_format
export read_data5_file, read_data5_raw, write_data5_file, merge_data_files, verify_data_integrity
export read_sim5_file, write_sim5_file, read_d5_file, write_d5_file
export check_file_accessibility, get_file_info, backup_file, cleanup_backups
export calculate_frequency_distribution, compute_statistical_summary
export calculate_probability_distribution, calculate_correlation, chi_square_test
export calculate_variance_stats, calculate_percentiles, identify_outliers
export analyze_hot_cold_numbers, calculate_moving_averages, perform_data_quality_tests
export calculate_ffg_median, compute_skip_probability, is_favorable_timing
export calculate_theoretical_ffg_median, calculate_ffg_median_with_dc, update_ffg_calculations!
export get_ffg_analysis, find_optimal_key_numbers, calculate_ffg_confidence_interval, validate_ffg_calculations
export calculate_skips, get_current_skip, generate_skip_chart, update_skips
export calculate_skip_statistics, get_favorable_numbers, get_skip_summary
export find_longest_skips, find_shortest_skips, analyze_skip_distribution
export predict_favorable_numbers, generate_skip_report, compare_skip_patterns
export calculate_all_pairings, get_top_pairings, generate_wonder_grid, update_pairings
export get_sorted_pairings, get_pairing_frequency, get_number_pairings, analyze_pairing_distribution
export get_top_pairs, get_bottom_pairs, get_pairs_in_frequency_range, get_pairing_percentiles
export verify_pairing_distribution_rules, get_memory_usage_stats, clear_caches!, rebuild_caches!
export identify_top_pairings, identify_top_10_percent_pairings, identify_top_25_percent_pairings, identify_top_50_percent_pairings
export verify_25_percent_rule, verify_25_percent_rule_all_numbers, verify_10_percent_rule, verify_10_percent_rule_all_numbers
export create_pairing_quality_ranking, generate_wonder_grid_file, export_wonder_grid_to_file
export get_best_wonder_grid_numbers, analyze_pairing_concentration
export select_key_numbers, generate_combinations, execute_strategy
export evaluate_key_numbers, rank_key_numbers, select_key_numbers_auto, select_key_numbers_filtered
export analyze_key_number, compare_key_numbers, calculate_frequency_score, calculate_stability_score
export get_all_pairings_for_number
export update_engine!, get_engine_status, clear_engine_caches!, validate_engine_integrity
export select_key_numbers_with_ffg_analysis, get_comprehensive_key_analysis
export generate_combinations_with_percentage, generate_multiple_key_combinations, get_combination_statistics
export execute_multiple_strategies, execute_optimal_strategy, get_strategy_recommendations
export generate_standard_combinations, generate_flexible_combinations, generate_efficient_combinations
export generate_quality_combinations, generate_cost_optimized_combinations, generate_batch_combinations
export generate_validated_combinations, get_generation_statistics, clear_generation_cache!
export optimize_combination_generation, generate_combinations_advanced, get_combination_generation_analysis
export generate_lie_combinations, apply_elimination_filter, calculate_cost_savings, find_key_number
export generate_combinations_with_lie_elimination, generate_combinations_with_lie_analysis
export generate_multiple_combinations_with_lie, execute_wonder_grid_with_lie
export compare_wonder_grid_strategies, optimize_lie_threshold
export run_backtest, calculate_hit_rates, compare_to_random
export run_wonder_grid_backtest, run_wonder_grid_lie_backtest, run_comparative_backtest
export calculate_detailed_hit_rates, calculate_efficiency_analysis, calculate_cost_analysis
export run_time_series_backtest, run_strategy_vs_random_backtest
export generate_random_combinations, get_backtest_engine_stats, clear_backtest_caches!
export generate_test_data, load_test_data, display_strategy_results, display_skip_chart, display_pairing_analysis
export generate_performance_report, display_performance_report, generate_comparative_report, display_comparative_analysis, export_performance_report

# Include all module files
include("types.jl")
include("validation.jl")
include("file_management.jl")
include("statistics.jl")
include("ffg_calculator.jl")
include("skip_analyzer.jl")
include("pairing_engine.jl")
include("wonder_grid_engine.jl")
include("combination_generator.jl")
include("lie_elimination.jl")
include("backtesting.jl")
include("performance_reporting.jl")
include("utils.jl")

end # module