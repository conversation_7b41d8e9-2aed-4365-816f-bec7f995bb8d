# Filter Cache Management
# 過濾器快取管理 - 多層快取系統

using Dates

"""
多層快取系統
提供 L1（熱數據）、L2（溫數據）、L3（冷數據）三層快取
"""
mutable struct MultiLevelCache
    l1_cache::Dict{String, Any}           # 第一層：熱數據快取（最常用）
    l2_cache::Dict{String, Any}           # 第二層：溫數據快取（中等使用）
    l3_cache::Dict{String, Any}           # 第三層：冷數據快取（較少使用）
    access_count::Dict{String, Int}       # 訪問計數
    last_access::Dict{String, DateTime}   # 最後訪問時間
    cache_stats::Dict{String, Int}        # 快取統計
    max_l1_size::Int                      # L1 快取最大大小
    max_l2_size::Int                      # L2 快取最大大小
    max_l3_size::Int                      # L3 快取最大大小
    
    function MultiLevelCache(
        max_l1_size::Int = 1000,
        max_l2_size::Int = 5000,
        max_l3_size::Int = 10000
    )
        new(
            Dict{String, Any}(),
            Dict{String, Any}(),
            Dict{String, Any}(),
            Dict{String, Int}(),
            Dict{String, DateTime}(),
            Dict{String, Int}(
                "l1_hits" => 0,
                "l2_hits" => 0,
                "l3_hits" => 0,
                "misses" => 0,
                "evictions" => 0
            ),
            max_l1_size,
            max_l2_size,
            max_l3_size
        )
    end
end

"""
智能快取獲取
根據訪問模式自動在不同層級間移動數據
"""
function get_cached_value(cache::MultiLevelCache, key::String, compute_func::Function)
    # 更新訪問統計
    cache.access_count[key] = get(cache.access_count, key, 0) + 1
    cache.last_access[key] = now()
    
    # 檢查 L1 快取
    if haskey(cache.l1_cache, key)
        cache.cache_stats["l1_hits"] += 1
        return cache.l1_cache[key]
    end
    
    # 檢查 L2 快取
    if haskey(cache.l2_cache, key)
        cache.cache_stats["l2_hits"] += 1
        value = cache.l2_cache[key]
        
        # 提升到 L1（如果訪問頻繁）
        if cache.access_count[key] >= 3
            delete!(cache.l2_cache, key)
            cache.l1_cache[key] = value
            manage_l1_cache_size!(cache)
        end
        
        return value
    end
    
    # 檢查 L3 快取
    if haskey(cache.l3_cache, key)
        cache.cache_stats["l3_hits"] += 1
        value = cache.l3_cache[key]
        
        # 提升到 L2
        delete!(cache.l3_cache, key)
        cache.l2_cache[key] = value
        manage_l2_cache_size!(cache)
        
        return value
    end
    
    # 快取未命中，計算新值
    cache.cache_stats["misses"] += 1
    value = compute_func()
    
    # 儲存到 L1 快取
    cache.l1_cache[key] = value
    manage_l1_cache_size!(cache)
    
    return value
end

"""
管理 L1 快取大小
當超過限制時，將較少使用的項目降級到 L2
"""
function manage_l1_cache_size!(cache::MultiLevelCache)
    if length(cache.l1_cache) <= cache.max_l1_size
        return
    end
    
    # 獲取所有 L1 快取項目的訪問統計
    l1_keys = collect(keys(cache.l1_cache))
    
    # 按訪問頻率和最近訪問時間排序
    sort!(l1_keys, by = key -> (
        -cache.access_count[key],  # 訪問次數（降序）
        cache.last_access[key]     # 最近訪問時間（升序）
    ))
    
    # 移動最少使用的項目到 L2
    items_to_move = length(l1_keys) - cache.max_l1_size + 100  # 多移動一些以減少頻繁操作
    
    for i in (length(l1_keys) - items_to_move + 1):length(l1_keys)
        key = l1_keys[i]
        value = cache.l1_cache[key]
        delete!(cache.l1_cache, key)
        cache.l2_cache[key] = value
        cache.cache_stats["evictions"] += 1
    end
    
    manage_l2_cache_size!(cache)
end

"""
管理 L2 快取大小
當超過限制時，將較少使用的項目降級到 L3
"""
function manage_l2_cache_size!(cache::MultiLevelCache)
    if length(cache.l2_cache) <= cache.max_l2_size
        return
    end
    
    l2_keys = collect(keys(cache.l2_cache))
    
    # 按最近訪問時間排序
    sort!(l2_keys, by = key -> cache.last_access[key])
    
    # 移動最舊的項目到 L3
    items_to_move = length(l2_keys) - cache.max_l2_size + 200
    
    for i in 1:min(items_to_move, length(l2_keys))
        key = l2_keys[i]
        value = cache.l2_cache[key]
        delete!(cache.l2_cache, key)
        cache.l3_cache[key] = value
        cache.cache_stats["evictions"] += 1
    end
    
    manage_l3_cache_size!(cache)
end

"""
管理 L3 快取大小
當超過限制時，刪除最舊的項目
"""
function manage_l3_cache_size!(cache::MultiLevelCache)
    if length(cache.l3_cache) <= cache.max_l3_size
        return
    end
    
    l3_keys = collect(keys(cache.l3_cache))
    
    # 按最近訪問時間排序
    sort!(l3_keys, by = key -> cache.last_access[key])
    
    # 刪除最舊的項目
    items_to_remove = length(l3_keys) - cache.max_l3_size + 500
    
    for i in 1:min(items_to_remove, length(l3_keys))
        key = l3_keys[i]
        delete!(cache.l3_cache, key)
        delete!(cache.access_count, key)
        delete!(cache.last_access, key)
        cache.cache_stats["evictions"] += 1
    end
end

"""
清除所有快取
"""
function clear_all_cache!(cache::MultiLevelCache)
    empty!(cache.l1_cache)
    empty!(cache.l2_cache)
    empty!(cache.l3_cache)
    empty!(cache.access_count)
    empty!(cache.last_access)
    
    # 重置統計
    cache.cache_stats["l1_hits"] = 0
    cache.cache_stats["l2_hits"] = 0
    cache.cache_stats["l3_hits"] = 0
    cache.cache_stats["misses"] = 0
    cache.cache_stats["evictions"] = 0
end

"""
獲取快取統計資訊
"""
function get_cache_statistics(cache::MultiLevelCache)::Dict{String, Any}
    total_hits = cache.cache_stats["l1_hits"] + cache.cache_stats["l2_hits"] + cache.cache_stats["l3_hits"]
    total_requests = total_hits + cache.cache_stats["misses"]
    
    hit_rate = total_requests > 0 ? total_hits / total_requests : 0.0
    
    return Dict(
        "l1_size" => length(cache.l1_cache),
        "l2_size" => length(cache.l2_cache),
        "l3_size" => length(cache.l3_cache),
        "total_size" => length(cache.l1_cache) + length(cache.l2_cache) + length(cache.l3_cache),
        "l1_hits" => cache.cache_stats["l1_hits"],
        "l2_hits" => cache.cache_stats["l2_hits"],
        "l3_hits" => cache.cache_stats["l3_hits"],
        "misses" => cache.cache_stats["misses"],
        "evictions" => cache.cache_stats["evictions"],
        "hit_rate" => hit_rate,
        "l1_hit_rate" => total_requests > 0 ? cache.cache_stats["l1_hits"] / total_requests : 0.0,
        "memory_efficiency" => Dict(
            "l1_utilization" => length(cache.l1_cache) / cache.max_l1_size,
            "l2_utilization" => length(cache.l2_cache) / cache.max_l2_size,
            "l3_utilization" => length(cache.l3_cache) / cache.max_l3_size
        )
    )
end

"""
專用的 Skip 計算快取
"""
mutable struct SkipCalculationCache
    skip_sequences::Dict{Int, Vector{Int}}
    current_skips::Dict{Int, Int}
    last_update::Dict{Int, Int}           # 記錄最後更新的數據版本
    data_version::Int                     # 數據版本號
    
    SkipCalculationCache() = new(Dict(), Dict(), Dict(), 0)
end

"""
獲取快取的 Skip 序列
"""
function get_cached_skip_sequence(cache::SkipCalculationCache, number::Int, data_version::Int)::Union{Vector{Int}, Nothing}
    if haskey(cache.skip_sequences, number) && 
       haskey(cache.last_update, number) && 
       cache.last_update[number] == data_version
        return cache.skip_sequences[number]
    end
    return nothing
end

"""
快取 Skip 序列
"""
function cache_skip_sequence!(cache::SkipCalculationCache, number::Int, sequence::Vector{Int}, data_version::Int)
    cache.skip_sequences[number] = copy(sequence)
    cache.last_update[number] = data_version
    cache.data_version = data_version
end

"""
FFG 計算專用快取
"""
mutable struct FFGCalculationCache
    theoretical_medians::Dict{Float64, Float64}  # DC -> 理論中位數
    empirical_medians::Dict{Tuple{Int, Int}, Float64}  # (number, data_hash) -> 經驗中位數
    probability_cache::Dict{Tuple{Int, Float64}, Float64}  # (skip, median) -> 機率
    
    FFGCalculationCache() = new(Dict(), Dict(), Dict())
end

"""
獲取快取的理論 FFG 中位數
"""
function get_cached_theoretical_median(cache::FFGCalculationCache, dc::Float64)::Union{Float64, Nothing}
    return get(cache.theoretical_medians, dc, nothing)
end

"""
快取理論 FFG 中位數
"""
function cache_theoretical_median!(cache::FFGCalculationCache, dc::Float64, median::Float64)
    cache.theoretical_medians[dc] = median
end

"""
配對計算專用快取
"""
mutable struct PairingCache
    pair_frequencies::Dict{Tuple{Int, Int}, Int}
    pairing_distributions::Dict{Int, Dict{String, Any}}  # number -> distribution
    wonder_grid_cache::Dict{String, Vector{Int}}
    last_update_version::Int
    
    PairingCache() = new(Dict(), Dict(), Dict(), 0)
end

"""
獲取快取的配對頻率
"""
function get_cached_pairing_frequency(cache::PairingCache, pair::Tuple{Int, Int}, data_version::Int)::Union{Int, Nothing}
    if cache.last_update_version == data_version && haskey(cache.pair_frequencies, pair)
        return cache.pair_frequencies[pair]
    end
    return nothing
end

"""
快取配對頻率
"""
function cache_pairing_frequency!(cache::PairingCache, pair::Tuple{Int, Int}, frequency::Int, data_version::Int)
    cache.pair_frequencies[pair] = frequency
    cache.last_update_version = data_version
end

"""
清除過期的快取項目
基於時間和訪問頻率清理快取
"""
function cleanup_expired_cache!(cache::MultiLevelCache, max_age_hours::Int = 24)
    cutoff_time = now() - Hour(max_age_hours)
    expired_keys = String[]
    
    # 收集過期的鍵
    for (key, last_access_time) in cache.last_access
        if last_access_time < cutoff_time
            push!(expired_keys, key)
        end
    end
    
    # 刪除過期項目
    for key in expired_keys
        delete!(cache.l1_cache, key)
        delete!(cache.l2_cache, key)
        delete!(cache.l3_cache, key)
        delete!(cache.access_count, key)
        delete!(cache.last_access, key)
    end
    
    @info "清理了 $(length(expired_keys)) 個過期快取項目"
end

"""
預熱快取
為常用的計算預先載入快取
"""
function warmup_cache!(cache::MultiLevelCache, common_keys::Vector{String}, compute_funcs::Dict{String, Function})
    @info "開始快取預熱..."
    
    for key in common_keys
        if haskey(compute_funcs, key)
            try
                get_cached_value(cache, key, compute_funcs[key])
            catch e
                @warn "預熱快取項目失敗: $key" error=e
            end
        end
    end
    
    @info "快取預熱完成"
end

# 導出主要結構和函數
export MultiLevelCache, SkipCalculationCache, FFGCalculationCache, PairingCache
export get_cached_value, clear_all_cache!, get_cache_statistics
export cleanup_expired_cache!, warmup_cache!
export get_cached_skip_sequence, cache_skip_sequence!
export get_cached_theoretical_median, cache_theoretical_median!
export get_cached_pairing_frequency, cache_pairing_frequency!
