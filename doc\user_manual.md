# Wonder Grid Lottery System - 用戶手冊

## 📖 目錄

1. [系統概述](#系統概述)
2. [安裝指南](#安裝指南)
3. [配置說明](#配置說明)
4. [基本使用](#基本使用)
5. [高級功能](#高級功能)
6. [性能優化](#性能優化)
7. [故障排除](#故障排除)
8. [最佳實踐](#最佳實踐)

---

## 系統概述

Wonder Grid Lottery System 是一個基於 Ion Saliu 彩票理論的高性能分析系統，提供完整的彩票數據分析和預測功能。

### 🎯 主要特色

- **完整的 Saliu 過濾器**：ONE, TWO, THREE, FOUR, FIVE 過濾器
- **高性能優化**：92.9% 記憶體節省，多層快取系統
- **並行計算**：多執行緒和分散式計算支援
- **智能調優**：自動性能參數優化
- **實時監控**：性能監控和警報系統

### 🏗️ 系統架構

```
Wonder Grid Lottery System
├── 核心引擎 (FilterEngine)
├── 優化引擎 (OptimizedFilterEngine)
├── 並行計算 (ParallelComputing)
├── 快取系統 (MultiLevelCache)
├── 記憶體管理 (MemoryPool)
├── 性能監控 (PerformanceMonitor)
└── 自動調優 (AutoTuner)
```

---

## 安裝指南

### 系統需求

| 組件 | 最低需求 | 推薦配置 |
|------|----------|----------|
| Julia | 1.8+ | 1.11+ |
| RAM | 4GB | 8GB+ |
| CPU | 2核心 | 4核心+ |
| 儲存 | 1GB | 2GB+ |

### 安裝步驟

#### 1. 安裝 Julia

**Windows:**
```powershell
# 下載並安裝 Julia
winget install julia -s msstore
```

**macOS:**
```bash
# 使用 Homebrew
brew install julia
```

**Linux:**
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install julia

# 或下載官方二進制文件
wget https://julialang-s3.juliacomputing.com/bin/linux/x64/1.11/julia-1.11.0-linux-x86_64.tar.gz
```

#### 2. 下載系統

```bash
git clone https://github.com/your-repo/wonder-grid-lottery-system.git
cd wonder-grid-lottery-system
```

#### 3. 驗證安裝

```bash
# 啟動 Julia（啟用多執行緒）
julia -t auto

# 在 Julia REPL 中測試
julia> include("src/wonder_grid_system.jl")
julia> println("✅ Wonder Grid Lottery System 安裝成功！")
```

---

## 配置說明

### 基本配置

系統提供多種配置選項來優化性能：

#### 1. 引擎配置

```julia
# 標準配置
engine = FilterEngine(historical_data)

# 優化配置
opt_engine = OptimizedFilterEngine(
    historical_data,
    use_compact_data = true,     # 啟用緊湊數據（節省 92.9% 記憶體）
    enable_caching = true,       # 啟用快取系統
    auto_cleanup = true          # 自動清理
)
```

#### 2. 快取配置

```julia
# 自定義快取大小
cache = MultiLevelCache(
    l1_max_items = 200,          # L1 快取項目數
    l2_max_items = 2000,         # L2 快取項目數
    l3_max_items = 20000,        # L3 快取項目數
    l1_max_size = 2 * 1024 * 1024,    # L1 快取大小 (2MB)
    l2_max_size = 20 * 1024 * 1024,   # L2 快取大小 (20MB)
    l3_max_size = 200 * 1024 * 1024   # L3 快取大小 (200MB)
)
```

#### 3. 並行配置

```julia
# 檢查並行能力
capabilities = get_parallel_capabilities()
println("可用執行緒: $(capabilities["available_threads"])")

# 啟動時配置執行緒數
# julia -t 4        # 使用 4 個執行緒
# julia -t auto     # 自動檢測最佳執行緒數
```

### 環境變量

可以通過環境變量配置系統行為：

```bash
# 設置默認執行緒數
export JULIA_NUM_THREADS=4

# 設置記憶體限制
export JULIA_MAX_MEMORY=8G

# 啟用性能監控
export WONDER_GRID_MONITORING=true
```

---

## 基本使用

### 數據準備

#### 1. 數據格式

系統支援多種數據格式：

```julia
# 基本格式
draw = LotteryDraw([1, 5, 10, 15, 20], Date(2023, 1, 1), 1)

# 從 CSV 載入
using CSV, DataFrames

function load_csv_data(filename::String)
    df = CSV.read(filename, DataFrame)
    draws = LotteryDraw[]
    
    for row in eachrow(df)
        numbers = [row.num1, row.num2, row.num3, row.num4, row.num5]
        date = Date(row.date)
        push!(draws, LotteryDraw(numbers, date, row.draw_id))
    end
    
    return draws
end
```

#### 2. 數據驗證

```julia
function validate_lottery_data(draws::Vector{LotteryDraw})
    for (i, draw) in enumerate(draws)
        # 檢查號碼範圍
        if any(n -> n < 1 || n > 39, draw.numbers)
            error("第 $i 筆數據號碼超出範圍: $(draw.numbers)")
        end
        
        # 檢查號碼數量
        if length(draw.numbers) != 5
            error("第 $i 筆數據號碼數量不正確: $(length(draw.numbers))")
        end
        
        # 檢查重複號碼
        if length(unique(draw.numbers)) != 5
            error("第 $i 筆數據包含重複號碼: $(draw.numbers)")
        end
    end
    
    println("✅ 數據驗證通過：$(length(draws)) 筆記錄")
end
```

### 基本分析

#### 1. Skip 分析

```julia
# 單個號碼 Skip 分析
function analyze_single_number(engine, number::Int)
    result = calculate_one_filter(engine, number)
    
    println("號碼 $number 分析結果:")
    println("  當前 Skip: $(result.current_value)")
    println("  期望 Skip: $(round(result.expected_value, digits=2))")
    
    return result
end

# 批次 Skip 分析
function analyze_multiple_numbers(engine, numbers::Vector{Int})
    results = Dict{Int, FilterResult}()
    
    for number in numbers
        results[number] = calculate_one_filter(engine, number)
    end
    
    # 按 Skip 值排序
    sorted_results = sort(collect(results), by=x->x[2].current_value)
    
    println("Skip 分析結果（按 Skip 值排序）:")
    for (number, result) in sorted_results
        println("  號碼 $number: Skip = $(result.current_value)")
    end
    
    return results
end
```

#### 2. 配對分析

```julia
# 配對頻率分析
function analyze_pairing_frequency(engine, num1::Int, num2::Int)
    if isa(engine, OptimizedFilterEngine)
        freq = calculate_pairing_frequency_optimized(engine, num1, num2)
    else
        # 對於標準引擎，需要手動計算
        freq = 0
        for draw in engine.historical_data
            if num1 in draw.numbers && num2 in draw.numbers
                freq += 1
            end
        end
    end
    
    total_draws = length(engine.historical_data)
    percentage = (freq / total_draws) * 100
    
    println("配對分析 ($num1, $num2):")
    println("  配對次數: $freq")
    println("  配對率: $(round(percentage, digits=2))%")
    
    return freq
end

# 熱門配對分析
function find_hot_pairs(engine, numbers::Vector{Int}, top_n::Int = 10)
    pairs = []
    
    for i in 1:length(numbers)
        for j in i+1:length(numbers)
            freq = analyze_pairing_frequency(engine, numbers[i], numbers[j])
            push!(pairs, ((numbers[i], numbers[j]), freq))
        end
    end
    
    # 排序並取前 N 個
    sorted_pairs = sort(pairs, by=x->x[2], rev=true)
    hot_pairs = sorted_pairs[1:min(top_n, length(sorted_pairs))]
    
    println("\n🔥 熱門配對 Top $top_n:")
    for (i, (pair, freq)) in enumerate(hot_pairs)
        println("  $i. $(pair[1])-$(pair[2]): $freq 次")
    end
    
    return hot_pairs
end
```

### 過濾器使用

#### 1. ONE 過濾器（Skip 分析）

```julia
# 基本用法
result = calculate_one_filter(engine, 1)

# 批次處理
function batch_one_filter(engine, numbers::Vector{Int})
    results = []
    for number in numbers
        result = calculate_one_filter(engine, number)
        push!(results, (number, result.current_value))
    end
    return sort(results, by=x->x[2])  # 按 Skip 值排序
end
```

#### 2. TWO 過濾器（配對分析）

```julia
# 分析一組號碼的配對情況
numbers = [1, 2, 3, 4, 5]
result = calculate_two_filter(engine, numbers)

println("配對分析結果:")
println("  配對數量: $(result.current_value)")
println("  期望值: $(round(result.expected_value, digits=2))")
```

#### 3. 高階過濾器

```julia
# THREE 過濾器（三重組合）
three_result = calculate_three_filter(engine, [1, 2, 3, 4, 5])

# FOUR 過濾器（四重組合）
four_result = calculate_four_filter(engine, [1, 2, 3, 4, 5])

# FIVE 過濾器（五重組合）
five_result = calculate_five_filter(engine, [1, 2, 3, 4, 5])
```

---

## 高級功能

### 並行計算

#### 1. 並行 Skip 計算

```julia
# 並行計算所有號碼的 Skip 值
parallel_results = calculate_all_skips_parallel(historical_data)

# 處理結果
successful_results = Dict{Int, Int}()
failed_results = []

for (number, result) in parallel_results
    if result.success
        successful_results[number] = result.result
    else
        push!(failed_results, (number, result.error_message))
    end
end

println("✅ 成功計算: $(length(successful_results)) 個號碼")
println("❌ 失敗計算: $(length(failed_results)) 個號碼")
```

#### 2. 分散式 Wonder Grid 生成

```julia
# 生成 Wonder Grid
grid_results = generate_wonder_grid_distributed(historical_data, 50)

wonder_grid = grid_results["wonder_grid"]
individual_results = grid_results["individual_results"]

println("🎯 Wonder Grid 生成完成:")
println("  網格大小: $(length(wonder_grid)) 行")
println("  分析號碼: $(length(individual_results)) 個")

# 顯示前 5 行
for (i, row) in enumerate(wonder_grid[1:min(5, length(wonder_grid))])
    println("  第 $i 行: $(join(row, ", "))")
end
```

### 性能監控

#### 1. 實時監控

```julia
# 開始監控
monitor = PerformanceMonitor()

# 記錄操作
start_timer!(monitor, "skip_calculation")
result = calculate_one_filter(engine, 1)
duration = end_timer!(monitor, "skip_calculation")

println("操作耗時: $(round(duration, digits=2))ms")

# 獲取統計
stats = get_metric_statistics(monitor, "skip_calculation")
println("平均耗時: $(round(stats["mean"], digits=2))ms")
```

#### 2. 系統性能摘要

```julia
# 獲取全局性能摘要
summary = get_global_performance_summary()

println("📊 系統性能摘要:")
println("  性能等級: $(summary["performance_grade"])")
println("  平均 Skip 計算時間: $(round(summary["avg_skip_time_ms"], digits=2))ms")
println("  快取命中率: $(round(summary["cache_hit_rate"] * 100, digits=1))%")
println("  監控時長: $(round(summary["monitoring_duration_hours"], digits=2)) 小時")
```

### 自動調優

#### 1. 啟用自動調優

```julia
# 執行自動調優
tuning_performed = perform_auto_tuning!()

if tuning_performed
    println("✅ 自動調優已執行")
    
    # 獲取調優報告
    report = get_global_tuning_report()
    
    println("📈 調優結果:")
    println("  性能改進: $(round(report["performance_improvement"], digits=3))")
    println("  調優會話: $(report["tuning_sessions"])")
    println("  最新評分: $(round(report["latest_score"], digits=3))")
else
    println("ℹ️ 暫時不需要調優")
end
```

#### 2. 自定義調優參數

```julia
# 創建自定義調優器
custom_tuner = AutoTuner(
    30,    # 調優間隔（分鐘）
    0.15   # 學習率
)

# 手動執行調優
auto_tune_performance!(custom_tuner, GLOBAL_PERFORMANCE_MONITOR)
```

---

## 性能優化

### 記憶體優化

#### 1. 啟用緊湊數據結構

```julia
# 創建緊湊數據集
compact_dataset = create_compact_dataset(historical_data)

# 檢查記憶體節省
stats = get_compact_dataset_stats(compact_dataset)
println("📦 記憶體優化:")
println("  原始大小: $(round(stats["memory_usage_bytes"] / (1024*1024), digits=2))MB")
println("  節省記憶體: $(round(stats["memory_saved_bytes"] / (1024*1024), digits=2))MB")
println("  節省比例: $(round(stats["memory_savings_percentage"], digits=1))%")

# 驗證數據完整性
integrity_ok = verify_data_integrity(historical_data, compact_dataset)
println("  數據完整性: $(integrity_ok ? "✅" : "❌")")
```

#### 2. 記憶體池管理

```julia
# 檢查記憶體池狀態
pool_stats = get_all_pool_statistics(GLOBAL_MEMORY_POOL)

println("🔄 記憶體池統計:")
println("  總池數: $(pool_stats["summary"]["total_pools"])")
println("  總分配: $(pool_stats["summary"]["total_allocations"])")
println("  總重用: $(pool_stats["summary"]["total_reuses"])")
println("  重用率: $(round(pool_stats["summary"]["overall_reuse_rate"] * 100, digits=1))%")

# 手動清理
cleaned = auto_cleanup!(GLOBAL_MEMORY_POOL)
println("  清理項目: $cleaned")
```

### 快取優化

#### 1. 快取統計

```julia
# 檢查引擎快取狀態
if isa(engine, OptimizedFilterEngine)
    stats = get_engine_statistics(engine)
    
    println("💾 快取統計:")
    println("  整體命中率: $(round(stats["cache_hit_rate"] * 100, digits=1))%")
    
    # 各層快取詳情
    for cache_type in ["skip_cache", "ffg_cache", "pairing_cache"]
        if haskey(stats, cache_type)
            cache_stats = stats[cache_type]
            println("  $cache_type 命中率: $(round(cache_stats["hit_rate"] * 100, digits=1))%")
        end
    end
end
```

#### 2. 快取調優

```julia
# 清空快取重新開始
if isa(engine, OptimizedFilterEngine)
    clear_cache!(engine.skip_cache.cache)
    clear_cache!(engine.ffg_cache.cache)
    clear_cache!(engine.pairing_cache.cache)
    println("✅ 快取已清空")
end
```

---

## 故障排除

### 常見問題

#### 1. 性能問題

**問題**: 計算速度慢
```julia
# 檢查執行緒數
println("當前執行緒數: $(Threads.nthreads())")
# 解決方案: 使用 julia -t auto 重新啟動

# 檢查快取命中率
stats = get_engine_statistics(engine)
println("快取命中率: $(round(stats["cache_hit_rate"] * 100, digits=1))%")
# 解決方案: 如果命中率低，增加快取大小
```

**問題**: 記憶體使用過高
```julia
# 檢查是否啟用緊湊數據
info = get_optimized_engine_info(engine)
println("使用緊湊數據: $(info["use_compact_data"])")
# 解決方案: 啟用緊湊數據結構

# 手動清理記憶體
cleaned = auto_cleanup!(GLOBAL_MEMORY_POOL)
println("清理了 $cleaned 個項目")
```

#### 2. 數據問題

**問題**: 數據載入失敗
```julia
# 檢查數據格式
function debug_data_format(draws::Vector{LotteryDraw})
    for (i, draw) in enumerate(draws[1:min(5, length(draws))])
        println("第 $i 筆: $(draw.numbers) @ $(draw.draw_date)")
        
        # 檢查常見問題
        if length(draw.numbers) != 5
            println("  ❌ 號碼數量錯誤: $(length(draw.numbers))")
        end
        
        if any(n -> n < 1 || n > 39, draw.numbers)
            println("  ❌ 號碼超出範圍: $(draw.numbers)")
        end
        
        if length(unique(draw.numbers)) != 5
            println("  ❌ 包含重複號碼: $(draw.numbers)")
        end
    end
end
```

#### 3. 計算錯誤

**問題**: 計算結果異常
```julia
# 啟用詳細日誌
function debug_calculation(engine, number::Int)
    println("🔍 調試號碼 $number 的計算:")
    
    # 檢查歷史數據
    occurrences = []
    for (i, draw) in enumerate(engine.historical_data)
        if number in draw.numbers
            push!(occurrences, i)
        end
    end
    
    println("  出現位置: $occurrences")
    println("  最後出現: $(isempty(occurrences) ? "從未" : occurrences[end])")
    println("  當前 Skip: $(isempty(occurrences) ? length(engine.historical_data) : length(engine.historical_data) - occurrences[end])")
    
    # 執行計算
    result = calculate_one_filter(engine, number)
    println("  計算結果: $(result.current_value)")
    
    return result
end
```

### 診斷工具

#### 1. 系統健康檢查

```julia
function system_health_check(engine)
    println("🏥 系統健康檢查:")
    
    # 檢查數據完整性
    data_ok = true
    try
        validate_lottery_data(engine.historical_data)
        println("  ✅ 數據完整性: 正常")
    catch e
        println("  ❌ 數據完整性: 異常 - $e")
        data_ok = false
    end
    
    # 檢查引擎狀態
    if isa(engine, OptimizedFilterEngine)
        info = get_optimized_engine_info(engine)
        println("  ✅ 引擎類型: 優化版本")
        println("  📊 數據點數: $(info["data_points"])")
        println("  💾 緊湊數據: $(info["compact_data_available"] ? "啟用" : "禁用")")
        println("  🔄 快取: $(info["enable_caching"] ? "啟用" : "禁用")")
    else
        println("  ⚠️ 引擎類型: 標準版本（建議使用優化版本）")
    end
    
    # 檢查性能
    summary = get_global_performance_summary()
    grade = summary["performance_grade"]
    println("  📈 性能等級: $grade")
    
    return data_ok
end
```

#### 2. 性能分析

```julia
function performance_analysis(engine)
    println("📊 性能分析:")
    
    # 測試計算性能
    test_numbers = [1, 5, 10, 15, 20]
    times = []
    
    for number in test_numbers
        start_time = time()
        calculate_one_filter(engine, number)
        execution_time = (time() - start_time) * 1000
        push!(times, execution_time)
    end
    
    avg_time = mean(times)
    max_time = maximum(times)
    min_time = minimum(times)
    
    println("  平均計算時間: $(round(avg_time, digits=2))ms")
    println("  最大計算時間: $(round(max_time, digits=2))ms")
    println("  最小計算時間: $(round(min_time, digits=2))ms")
    
    # 性能評級
    if avg_time < 10
        println("  🎉 性能評級: 優秀")
    elseif avg_time < 50
        println("  ✅ 性能評級: 良好")
    elseif avg_time < 100
        println("  ⚠️ 性能評級: 一般")
    else
        println("  ❌ 性能評級: 需要優化")
    end
    
    return avg_time
end
```

---

## 最佳實踐

### 1. 數據管理

- **數據驗證**: 載入數據後立即驗證格式和完整性
- **數據備份**: 定期備份重要的歷史數據
- **增量更新**: 使用增量方式更新數據，避免重新載入全部數據

### 2. 性能優化

- **使用優化引擎**: 優先使用 `OptimizedFilterEngine`
- **啟用緊湊數據**: 對於大數據集，啟用緊湊數據結構
- **多執行緒**: 使用 `julia -t auto` 啟動以獲得最佳並行性能
- **定期清理**: 定期清理快取和記憶體池

### 3. 監控和維護

- **性能監控**: 定期檢查系統性能指標
- **自動調優**: 啟用自動調優以持續優化性能
- **日誌記錄**: 記錄重要操作和錯誤信息
- **定期測試**: 運行測試套件驗證系統功能

### 4. 安全考慮

- **輸入驗證**: 始終驗證用戶輸入的數據
- **錯誤處理**: 妥善處理異常情況
- **資源限制**: 設置適當的記憶體和計算資源限制

---

**📚 更多信息請參考：**
- [API 參考文檔](api_reference.md)
- [快速入門指南](quick_start.md)
- [安裝指南](installation_guide.md)
- [故障排除指南](troubleshooting.md)
