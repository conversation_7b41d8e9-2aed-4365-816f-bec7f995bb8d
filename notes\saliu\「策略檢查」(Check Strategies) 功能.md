作者 Ion Saliu 的樂透軟體中，「篩選器」是優化選號的核心工具，而「策略檢查」(Check Strategies) 功能則是評估這些篩選器組合在過去開獎中表現的關鍵。這個功能的主要目的是幫助使用者了解特定篩選器設定的組合在歷史開獎中命中了多少次，從而提煉出更有效的投注策略。

以下是「策略檢查」功能如何運作及利用「篩選器」優化選號的闡述：

### 篩選器的基礎與目的

篩選器本質上是**「限制」**，用於在樂透軟體生成組合時，淘汰不符合預設條件的組合。它們可以設定「最低級別」（只允許高於該級別的組合）或「最高級別」（只允許低於該級別的組合）。這樣做的目標是**大幅減少需要投注的樂透組合數量，以期提高中獎機率並降低投注成本**。

### 「策略檢查」功能的工作方式

1. **統計報告的生成與分析**：
    
    - 在使用「策略檢查」功能之前，使用者需要先透過如 `Bright`、`Ultimate Software` 或 `MDIEditor Lotto WE` 等軟體生成多種統計報告，例如 `W*` 和 `MD*` 報告。這些報告會顯示各種篩選器的統計參數，如**中位數 (median)**、平均值 (average) 和標準差 (standard deviation)。
    - 報告中的**中位數是設定篩選器等級的關鍵**，它代表了資料串列的中間值，有 50% 的數值在中位數或以下，50% 在中位數或以上。
    - 建議生成 1000 期（甚至 2000 期）的開獎報告，以發現更多有利的策略，因為只有足夠大的資料量才能揭示罕見但有價值的篩選級別。
2. **設定篩選器組合為「策略」**：
    
    - **策略**本質上就是一組或多組篩選器設定。例如，如果某個篩選器的中位數是 4，一個策略可以設定為 `minimum = 4` 和 `maximum = 5`。
    - 可以將少數篩選器設定為其正常範圍之外的「嚴格等級」（例如，中位數乘以 3、4 甚至 5；或除以 3、4 甚至 5），這可以消除大量的樂透組合，但這種嚴格設定不應在每次開獎時都使用，因為它們很少出現。
    - 作者還建議同時使用多個策略層。
3. **分析策略的歷史表現**：
    
    - 「策略檢查」功能（在 `Bright`/`Ultimate` 軟體中是 `C` 選項，或 `F3` 功能鍵；在 `MDIEditor Lotto WE` 中透過選單 `Check` > `Strategies` 進入）會分析篩選器設定組合在過去開獎中的表現。
    - 它會顯示該特定樂透策略在過去開獎中命中了多少次。例如，一個特定的策略在過去 100 期開獎中可能命中了 24 次。
    - 軟體也會生成策略文件（例如 `ST6.000`），記錄篩選器設定。
    - 還可以透過 `Strategy Hits` 功能（例如 `F4` 或 `H` 選項）查看在策略命中的情況下，該策略會生成多少組合。
4. **趨勢觀察與「跳躍」分析**：
    
    - 篩選器報告還會顯示其值相對於前一次開獎是升高 (`+`) 還是降低 (`-`) 的趨勢。通常在 2 或 3 個連續的相同趨勢後會反轉。這可以用來預測下一次抽獎的趨勢，並據此設定最小或最大等級。
    - 「策略檢查」功能還會顯示策略的**跳躍圖 (skip chart)**。跳躍（skip）是指一個號碼兩次出現之間的間隔次數。例如，如果策略的中位數跳躍值為 5，那麼只有當目前的跳躍值小於或等於 5 時才應使用該策略。這有助於在合適的「結果周期」(cycle of fruition) 內投注，避免在等待期過長時浪費資金。
5. **「逆向策略」(LIE Elimination)**：
    
    - 作者的軟體還引入了一種獨特的「逆向策略」(Reversed Strategy)，也稱為 `LIE` 消除。
    - 這種策略是**故意設定預計不會中獎的篩選器**，從而消除大量低機率組合，以減少投注成本並從「虧損」中獲利。其核心思想是邏輯定律「否定的否定就是肯定」。
    - `LIE` 消除功能適用於多種篩選器，例如 `ONE`、`TWO`、`THREE`、`FOUR`、`FIVE`、`SIX` (依據重複號碼組數量過濾)，以及 `skips` (跳躍)、`decades` (十年組)、`last digits` (個位數) 和 `3-group frequencies` (三群頻率) 等。此外，`Delta` 過濾器也可作為 `LIE` 消除的良好候選。

### 優化選號的策略組合

作者的軟體鼓勵多種策略的結合，而非單一策略：

- **多策略並行**：作者建議不要只玩一個策略，而是同時玩多個策略層，甚至合併多個輸出檔案，即使有重複項也不清除，因為這些策略有時會同時生效。
- **精確資料依賴**：篩選器分析的準確性**極度依賴於足夠大的資料檔案** (`D*` 檔案)，這些檔案通常由真實開獎結果 (`DATA-*`) 和模擬開獎結果 (`SIM-*`) 合併而成，且模擬檔案必須經過隨機化處理以避免錯誤報告。
- **策略調整與優化**：
    - 如果初步策略產生的組合過多，可以透過進一步「收緊」篩選器來減少組合數量。
    - 「策略檢查」結果有助於識別其他「異常」或「寶石」篩選器，進一步精煉策略。
    - 「清除」(Purge) 功能可以用於在生成大量組合後，應用額外的篩選器來淘汰不想要的組合，進一步減少投注數量。
    - `FileLines` 等工具可以交叉引用不同軟體平台生成的策略文件，將它們合併為更全面的策略。

### 相關數學理論與軟體工具

作者的彩票策略和軟體基於其數學理論，尤其是**賭博基本公式 (Fundamental Formula of Gambling, FFG)**。FFG 定義了確定性程度 (DC)、機率 (p) 和試驗次數 (N) 之間的關係，被認為是遊戲理論中最精確的工具，它考慮了先前事件對未來事件的影響。FFG 也用於計算篩選器的**理論中位數**，作為判斷趨勢和設定篩選器的依據。

**主要的軟體工具包括：**

- **Bright** 和 **Ultimate Software** 系列：整合性軟體包，包含執行上述所有功能所需的程式。
- **MDIEditor Lotto WE**：功能全面的彩票軟體，用於資料管理、統計分析、報告生成和組合生成。
- **Super Utilities**：包含多種實用工具，如模擬檔案生成、統計報告、組合生成以及 `Make/Break/Position` 功能，對於建立大型 `D*` 檔案和處理組合至關重要。
- **SortFilterReports** 和 **Sorting**：專門用於對濾鏡報告或任何 ASCII 文件中的數字列進行排序。

總之，透過「策略檢查」功能，玩家可以根據歷史數據回溯測試篩選器組合的表現，並結合趨勢分析、中位數、跳躍值及逆向策略等多種進階方法，以**智慧地排除大量低機率組合，從而大幅減少投注成本並提升潛在的中獎機會**。