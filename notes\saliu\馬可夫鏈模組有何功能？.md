馬可夫鏈模組在彩票軟體中，特別是由 Ion Saliu 開發的程式，扮演著分析彩票號碼間跟隨關係與配對頻率模式的關鍵角色，並以此生成不同類型的彩票組合。

以下是馬可夫鏈模組的主要功能：

- **理論基礎**：
    
    - 馬可夫鏈分析基於號碼的「跟隨者」(followers) 和「配對」(pairs) 概念。儘管「賭博基本公式 (FFG)」在某些方面超越了馬可夫鏈，但馬可夫鏈因其考慮了**過往事件對未來事件的影響**而獨具意義。
    - 該模組的開發旨在幫助發現號碼之間的**相關性模式**。
- **專用軟體**：
    
    - Ion Saliu 開發了一系列專門應用馬可夫鏈的軟體，包括針對 Pick-3 的 **MarkovPick3**、Pick-4 的 **MarkovPick4**、5 號樂透的 **MarkovLotto5**、6 號樂透的 **MarkovLotto6**，以及賽馬的三連勝 (trifecta) 遊戲的 **MarkovHorses3**。
    - 這些程式透過其主選單中的「M = 馬可夫鏈、配對、跟隨者」(M = Markov Chains, Pairs, Followers) 功能執行。
- **報告與資料分析**：
    
    - 在使用任何組合生成功能之前，必須先執行「R = 報告配對、頻率 (熱門)」(R = Report Pairs, Frequency (Hot)) 功能。
    - 系統會自動生成多種報告檔案，這些文字檔案是其他功能生成組合所需的：
        - **MarkovNumbersL6**：所有號碼按照「熱門」到「冷門」的順序排列。
        - **MarkovPairsPivL6**：所有配對按照「熱門」到「冷門」的順序，並帶有「樞紐」(PIVOT)。
        - **MarkovPairsNoPL6**：所有配對按照「熱門」到「冷門」的順序，不帶「樞紐」。
        - **MarkovFollowersL6**：所有號碼的馬可夫鏈「跟隨者」列表。
        - **MarkovLikePairsL6**：「跟隨者式配對」(Followers-like Pairs) 列表。
    - 報告功能也支援產生「跟隨者」和「跟隨者式配對」的空行（表示沒有跟隨者或配對），以 -1 表示。
    - 程式提供分析範圍或「parpalucks」的預設值，但使用者也可以自定義參數。
- **組合生成方法**：
    
    - 此類軟體提供五種組合生成方法：
        - **H = 熱門號碼組合** (Combinations of Hot Numbers)：根據報告中「熱門到冷門」的號碼生成組合。這些組合通常是「謊言消除 (LIE elimination)」的良好候選者，因為它們在真實開獎中可能錯過 1 到 3 個中獎號碼。
        - **P = 從帶有樞紐的配對生成組合** (Generate Combinations from Pairings with PIVOT)：根據頻率矩陣中的號碼和配對範圍生成組合，其中每個號碼都是「樞紐」(pivot)，即它會出現在所有生成的組合中。這些組合也適合用於「LIE 消除」功能。
        - **N = 從熱門到冷門配對生成組合** (Generate Combinations from HOT-to-COLD Pairings)：從「熱門到冷門」的號碼和配對範圍生成組合。這些組合同樣是「LIE 消除」的良好候選者。
        - **M = 從配對作為馬可夫鏈生成組合** (Generate Combinations from Pairings as Markov Chains)：從「跟隨者式配對」檔案中隨機選擇號碼來生成組合。這些組合也適合「LIE 消除」功能。
        - **C = 作為傳統馬可夫鏈生成組合** (Generate Combinations as Traditional Markov Chains)：從「跟隨者」列表中隨機選擇號碼來生成組合。這些組合也適合「LIE 消除」功能，特別是在下一期或接下來 5-6 期內不太可能中大獎。
    - 兩種馬可夫鏈風格的生成器都會去除重複項，確保使用者只獲得獨特的彩票組合。
- **策略檢查與過濾**：
    
    - 馬可夫鏈軟體已新增了「策略檢查」(Strategy Checking) 功能。
    - 生成的大部分組合通常是「不必要」的，因此軟體會利用「清除 (Purge)」和「謊言消除 (LIE Elimination)」功能來減少可玩的彩票數量。
    - **LIE 消除**：這是一種反向策略，透過故意設定預期不會中獎的過濾器來剔除低機率組合。例如，所有跳躍值都非常大，或者頻率極高或極低的號碼組合，都可能成為 LIE 檔案的候選。
- **效能與應用**：
    
    - 軟體在分析歷史開獎數據時，其效能有所提升。
    - 它也支持生成彩票組合，使其可以與 `MDIEditor Lotto WE` 等其他軟體結合使用，透過 `Purge` 功能進一步精簡組合。