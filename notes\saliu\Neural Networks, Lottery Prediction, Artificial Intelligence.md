---
created: 2025-07-24T21:26:40 (UTC +08:00)
tags: [neural networking,neural networks,artificial intelligence,AI,prediction,lottery,lotto,systems,strategy,theory,]
source: https://saliu.com/neural-networking-lottery.html
author: 
---

# Neural Networks, Lottery Prediction, Artificial Intelligence

> ## Excerpt
> Neural networking, neural networks, artificial intelligence AI can be successfully applied to predicting lottery, lotto winning as proved beyond doubt.

---
-   Published on August 29, 2018.  
    
-   First captured by the _WayBack Machine_ (_web.archive.org_) on January 12, 2020.

The probability problem many persons still have is the <u>overlapping of contradicting concepts</u>: **Certainty** and **Randomness**. Everything is random, inquisitively axiomatic ones, indeed, the Universe. _The\_Everything_ comes in [_**degrees of certainty**_](https://saliu.com/Saliu2.htm). Various degrees of certainty determine various [_**degrees of randomness**_](https://saliu.com/bbs/messages/683.html).

The confusion goes further by using a fancy concept: _Neural networking_. Even _fancier_: _Artificial intelligence (AI)_. In fact, all those theories do one thing. They try to find relations between elements of a system, relations that took place in the past. From those past relations (more accurately named _stats_), this type of theories (AI, etc.) try to _predict_ future relations between the same elements.

Methinks it all this fancy stuff started with the _Markov Chains_ theory. That theory is simplistic, however. It bases the analysis on a reduced number of elements that have higher degrees of certainty DC to occur. The system (container) of such elements is considered _non-random_. Again, contradiction in terms, as [_**everything is random, mathematically proven**_](https://saliu.com/formula.htm) beyond reasonable doubt.

And that explains why you hear crying-out-loud: _"Neural networking, artificial intelligence AI cannot be applied to lottery, because the lottery is totally (?) random!"_

Well, I got good news for you. _"Neural networking"_ does work with the lottery as far as more successful prediction is possible based on statistics (what happened in the past). If you stay here, you can read (and surely replicate) a case where neural networking applied to a lotto game beat random play by a factor of 37. Yes, the _"AI"_ prediction strategy was 37 times better than simply selecting lotto numbers randomly!

-   In all fairness, many... intelligent people consider _"artificial intelligence"_ a misnomer. That is, "artificial" entities, computers mainly, are <u>not</u> intelligent — never were, never will. I totally agree. <u>Intelligence is a human attribute entirely.</u>
-   The feats presented here are the fruits of my intelligence. I accomplished the feats without any help from computers earlier in my activity. Forget about them _"neural networks"_!
-   To further stress the fairness factor, the computers helped me a great deal nevertheless. The computers are marvelous tools, much faster and more accurate than yours truly. Still, the computers could have not come across any of these discoveries by themselves.
-   Therefore I coined a more axiological concept: _**Axiomatic Intelligence**_ or _**AxI**_.
-   Methinks the artificial intelligence thingy is the fruit of a film (a great work of art, mind you): _"2001: A Space Odyssey"_ by Stanley Kubrick. _"Open the pod bay doors, **HAL**."_ Please read my concluding notes.

![Neural networking, artificial intelligence originated in super computer HAL of 2001 Space Odyssey.](https://saliu.com/HLINE.gif)

Inquisitively axiomatic ones, you are right and wrong about this one thing. Indeed, I've never mentioned _**neural networking**_ as one of my lottery strategies or methods. On the other hand, I might as well be the first individual to apply such a method to lottery games. I was an \[idle\] economist in the early 1980s in Communist Romania. I did it for two other economists with the expectation they would share the winnings with me — without asking me to participate financially (it wasn't even much!)

I did it again in 1985 soon after I was resettled as a refugee to the United States. I was shortchanged again, but not as badly as in Romania!

I did write briefly about the two (actually, three) events: [_**Jackpot Lottery Strategy: 12-Number Combinations, Lotto-6 Wheels, Pioneer Software**_](https://saliu.com/lotto-jackpot-lost.html).

-   _"The very first method was based on number frequency and pairing. I would check the lotto numbers for the past 20 drawings or so. I would select numbers with good frequencies and also well paired with one another. I would group the numbers 3 at a time, being careful not to group 3 numbers that also came together in one of the past 20 drawings."_
-   _"And thus I arrived as a refugee to the United States in 1985. One day, one of my farm fellow workers gave me lottery materials (a list of past drawings, tickets for various games, etc.) He invited me to play with his group. The Porto Rican later told me that he had figured out I was a very smart guy, therefore I might be able to figure out how to win the lottery! I am not kidding! We won the very first time I played (the third prize, _4 of 6_ in 6/40 lotto). Each one of them played my lotto combinations as well. Everybody was happy. We played again next drawing — and won again!"_

I pretty much abandoned the _neural networking_ method because of the _lotto wheels_. I am not a fan of the lotto wheels as they aim at lower-tier prizes — and they prevalently achieve that goal. Therefore, the lotto wheels deprive the player of winning the lotto jackpot in an overwhelming majority of situations. <u>Mind you, the 9-number and 12-number lotto-6 wheels I applied are absolutely the best regardless of game format.</u> The 2 lotto wheels are capable of the highest leverage in this regard. Ultimately, they have the highest degree of certainty (chance) to hit even the jackpot!

In _Computerland USA_ I devised a totally new lottery strategy: _**Eliminate groups of numbers (patterns) from previous lottery drawings**_. The \[personal\] computer would take care of the entire process — no more the error-prone "paper-and-pencil" tedium. I expanded the _elimination_ (or _filtering_) method way up to the... _ionosphere_, as a few pundits put it!

Then, back to the future again... the _neural networking_ strategy. During the hot summer of the year of grace 2018 — and while watching the FIFA World Cup hosted by _Mother Russia_ and Trump's _Father-Figure_ Putin — I came up with a new idea of generating lottery combinations. The numbers, especially in jackpot lotto games, should be **weighed** based on their frequency. For example, I generate the frequency reports (as in my 6/49 game in the Pennsylvania Lottery).

My 6-49 lotto drawings file is named _PA-6_. The first test of this nature was published in the Google group dedicated to the lottery: [_**Odds Calculator, Number Combination Generator for Lottery, Lotto, Powerball, Mega Millions, Euromillions**_](https://groups.google.com/forum/#!topic/rec.gambling.lottery/VUB7H9NbNW4). There is a lot of stuff in that thread... just scroll down to the posts dated July 23, 26, 2018.

The last drawing in the file was _2 20 26 32 44 46_ (7/22/2018). The _parpaluck_ (_range of analysis_, or _number of draws to do the reporting for_) is very important. I set it to _25_ or _half of total numbers in the lotto game_. It proves to offer the best winning ratio for the derived lottery systems. Here you have the reports created by that great piece of lottery and gambling software b.k.a. **FrequencyRank** —

![Lottery software helps neural networking, artificial intelligence AI learn patterns from the past.](https://saliu.com/ScreenImgs/frequency-lottery.gif)

The statistical (frequency) reports are of the essence. Generating the reports is a breeze… provided that they are done by computers running the right software.

![The lotto-6 numbers ranked by frequency regardless of position are part of neural nets.](https://saliu.com/images/neural-nets-lotto-1.gif)

The lotto numbers sorted by frequency, from the _hottest_ (most frequent) to the _coldest_ (least frequent)

24 38 10 20 21 1 30 9 41 26 2 31 34 35 36 12 13 42 46 33 14 16 25 17 39 19 3 43 32 47 49 29 8 40 4 15 6 7 18 27 5 44 45 23 37 28 11 48 22

![The lotto-6 numbers ranked by frequency by position create effective neural networks.](https://saliu.com/images/neural-nets-lottery-2.gif)

The lotto numbers sorted by frequency, from the _hottest_ (most frequent) to the _coldest_ (least frequent) in the respective <u>position</u>

-   1 2 10 4 7 9 3 6 8 12 20 24 13 14 15 16 17 18 19 11 21 22 23 5 25 26 27 28 29 30 31 32 33 34 35 36 37 38 39 40 41 42 43 44 45 46 47 48 49
-   13 12 9 14 21 24 5 6 8 15 16 19 20 3 10 26 27 31 7 4 2 22 23 1 25 11 17 28 29 30 18 32 33 34 35 36 37 38 39 40 41 42 43 44 45 46 47 48 49
-   24 19 16 25 30 32 17 18 10 20 21 12 13 26 27 14 9 34 1 2 11 22 23 3 4 5 15 28 29 6 31 7 33 8 35 36 37 38 39 40 41 42 43 44 45 46 47 48 49
-   30 24 20 31 34 23 17 25 26 28 29 18 15 32 33 21 35 38 41 4 5 22 6 7 8 9 27 10 11 12 13 14 1 16 2 36 37 3 39 40 19 42 43 44 45 46 47 48 49
-   36 38 40 42 31 33 34 35 17 37 21 39 24 41 29 43 44 46 19 20 2 22 23 3 25 26 27 28 4 30 5 32 6 7 8 9 10 11 12 13 14 15 16 1 45 18 47 48 49
-   41 46 47 49 42 43 38 39 35 26 33 45 13 14 15 16 17 18 19 20 21 22 23 24 25 1 27 28 29 30 31 32 2 34 3 36 37 4 5 40 6 7 8 44 9 10 11 48 12

I created strings consisting of the lotto numbers with the best frequency rankings. I selected the top-35 lotto numbers for _frequency regardless of position_. <u>Positional</u> analysis: I selected only the lotto numbers with _frequency larger than zero_ in each position. Total possible combinations _Regardless of Position_ is close to total possible combinations in _Positional_ style.

24 38 10 20 21 1 30 9 41 26 2 31 34 35 36 12 13 42 46 33 14 16 25 17 39

1 2 10 4 7 9 3 6 8 12 20 24  
13 12 9 14 21 24 5 6 8 15 16 19 20 3 10 26 27 31  
24 19 16 25 30 32 17 18 10 20 21 12 13 26 27 14 9 34  
30 24 20 31 34 23 17 25 26 28 29 18 15 32 33 21 35 38 41  
36 38 40 42 31 33 34 35 17 37 21 39 24 41 29 43 44 46  
41 46 47 49 42 43 38 39 35 26 33 45

Axioman/axiowoman, those two systems do hit the jackpot within 30 drawings in a 6/49 lotto game! The best software for the task of checking winning numbers is another great piece of lottery software: **Super Utilities**, main menu of **Bright / Ultimate Software**, function _W = Check for Winners_. The software offers multiple options, including _groups (pools) of numbers_. The player doesn't have to generate combinations first, and then check them for winners against real lottery draws. You can check the groups directly, axio. It is very fast that way. The _2 20 26 32 44 46_ draw was followed by 34 drawings in my analysis. You can replicate this study any time, in any lotto game, with any lottery drawings. My data files are virtually totally accurate (they do not include the infamous _pre-test drawings_ the lotteries worldwide are... infamously notorious for!)

![Checking lottery numbers for winners is the method to validate neural networking.](https://saliu.com/images/neural-networks-lotto-3.gif)

![Applying positional statistics improves the efficiency of lottery neural networks.](https://saliu.com/images/neural-networks-lottery-4.gif)

YES! The lotto jackpot is there 3 times! I also checked how the top-25 numbers fared. You might have seen Gaitsa's lotto strategy based on playing the top-half of the lotto numbers based on frequency. I always give credit where credit is due, including to a pioneer in the lottery field named [_**Gail Howard**_](https://saliu.com/bbs/messages/278.html). The weakness of her methodology was the _parpaluck_. She did the analysis for all drawings in the game, even regardless of game format!

_"That's bad and sad... tremendously bad,"_ the same Donald _Caligula_ Trump Trump (gotta be at least the power of 2) would tell you in private (please don't record him secretly... I'd never do that — ever!) In other words, Gail Howard did the frequency report by combining all formats of the game. For example, when I came to the United States, the game I played in Pennsylvania was _6 of 40_, then it changed to _6/48_, and then to _6 of 69_ and currently _6 of 49_. I work now only with a _data_ file (_results_ or _real-drawings_) for the _6 of 49_ game. And, as you have noticed by now, I never use all drawings in the file as the _parpaluck_.

So, I performed a _non-positional_ analysis for the top-25 lotto numbers based on frequency. Here is the winning report:

![There is no real artificial intelligence AI, in lottery or elsewhere; that's science fiction.](https://saliu.com/images/artificial-intelligence-lotto-5.gif)

Again, the lotto jackpot was there within 30 drawings. In Romania, the AI (neural networking) strategy hit right away, in the very next drawing. In that situation, I selected 12 lotto numbers based on frequency and pairing. The 12-number [_**super lotto wheel**_](https://saliu.com/lottowheel.html) I applied also hit _6 of 6_ immediately. In the 1985 Pennsylvania cases, I selected only 9 numbers (amount determined by budget restrictions). I did pick correctly _5 of 6_ winners in both situations. The 9-number lotto wheel I played offered the _4 of 6_ prize (actually, the minimum assurance was _4 of 5_).

No doubt, my _neural networking strategy_ hits the jackpot. However, I don't think the **degree of certainty DC** is high enough. Therefore, I applied this form of _Artificial Intelligence_ (**AI**) as my proprietary (and by now famous) _**LIE Elimination Lottery Strategy**_. I did change my mind this 2018 Summer nonetheless. Just look at that system consisting of 177100 _combosnations_ (a favourite term in my _lottospeak_!) It beats the odds by a factor of 2. Can your system do better? NOT!! Not to mention, the system gave you 5 second-tier prizes and 6 third-tier payouts — while waiting for that grandiose jackpot we all dream about!

Selecting the lotto numbers by frequency AND pairing reduces the numbers to play and increases the system efficiency by orders of magnitude. Granted, one would have to wait a little longer for fruition. But I know you are mindful: Winning just one jackpot is enough — it is a life-changer!

![We must test how neural network prediction fares by checking future lottery drawings for winners.](https://saliu.com/ScreenImgs/super-utilities-lottery.gif)

They say AI (artificial intelligence) or neural networking is about _<u>learning</u>_. That is, software programs _"learn"_ from the past. In lottery, the _Markov Chains_ theory is also about _"learning"_. In what I present on this page, it is more about _human learning_. I checked the number frequencies and I determined that certain elements showed better appearances in the recent lottery drawings (_recent past_). I can do also a _pairing_ analysis and a _triplet_ (3-number groups) analysis. I can select the "best" lotto numbers to play for each type of analysis. Or, I can combine the two analyses and come up with numbers that might have a better chance to win in the near future. I did just that. The software in the image above can do many types of numeric analyses.

The _pairing_ report has the best lotto pairs at the top.

![Only humans have intelligence and it can be applied successfully to predicting lotto.](https://saliu.com/images/artificial-intelligence-lottery-6.gif)

I put the best pairings together so that I get 12 lottery numbers to play (thinking also of applying that _super lotto wheel_).

Next, I checked for winners as I did here for individual (single) numbers.

![The best term or concept is axiomatic intelligence AxI working in lotto prediction.](https://saliu.com/images/axiomatic-intelligence-lotto-7.gif)

Wins: three _4 of 6_ hits.

The _triple_ report has the best lotto triplets at the top.

![The neural nets can group lottery numbers in pairs and also triplets for successful forecasting.](https://saliu.com/images/axiomatic-intelligence-lottery-8.gif)

I put the best pairings together so that I get 12 lottery numbers to play (thinking also of applying that _super lotto wheel_).

Next, I checked for winners as I did here for the pairings.

![One great neural networking strategy for lottery is playing 12 lotto numbers in wheeling.](https://saliu.com/images/best-neural-networks-9.gif)

Wins: two _4 of 6_ hits.

Here is the interesting thing. I put together the lotto numbers in the pairing pool and the triplet group. This time, I wanted a total of 18 numbers to apply another great lotto wheel (also available at this site and presented earlier on this page).

![This is the best real neural net strategy applied to lottery; jackpot within 20 draws.](https://saliu.com/images/lottery-neural-nets-10.gif)

The winning is fantastic in this situation: <u>22 times better than random play!</u> Since the jackpot win came within 20 drawings, the success ratio is even better. **<u>It beats random play by a factor of 37.</u>** Evidently, there is no guarantee that the wheel would render the jackpot combination in the situation the pool of numbers did hit the jackpot. The excellent lotto wheel for 18 numbers in 51 lines only guarantees _4 of 6_ minimum. Hence, my reticence toward lotto wheels! It must be frustrating to have all 6 winners in your pool... and lose the lotto jackpot!

I also reinforce the idea of my proprietary _**LIE Elimination**_ (or _**Reversed Lottery Strategy**_). As you can see in the reports, none of the system hit a _4 of 6_ in the very next drawing (_in draw 34_). I would _**LIE Eliminate**_ the combinations from all these systems created here applying an aggressive _LieID=4_. I won't go wrong even going with the same combinations for the next 5 draws.

![Neural networking, artificial intelligence in lottery also works with random combination generating.](https://saliu.com/HLINE.gif)

All the above neural networking deals with lottery outcome in **lexicographical order**. There is also the other side: **random combinations**. Instead of generating all the combinations in lexicographical order, the player has the option of generating random combinations. I just checked all my software library — it is HUGE! There is plenty of software that generates lottery combinations in lexicographical order from groups of numbers — either _regardless of position_ or _positional_ strings. The best known application is probably **SkipSystem**, function _G: Generate Combinations from Systems_. The app can generate combos from any strings of numbers, not only the FFG systems it creates.

As for random combinations, I could only find two programs: **Range-5** and **Range-6**. They are part of the **Ultimate Software** packages, the 5- and 6-number lotto games only. I thought of upgrading the all-encompassing **Skip System**. It would be of great service especially to players of the humongous-odds games Powerball/Mega Millions/Euromillions games. Problem was, that magnificent application would grow way too big for a _**command-prompt**_ software program.

As you read in the Google group I referred you to, I've had the brilliant idea of generating **random** lotto combinations from strings of **weighed** lotto numbers based on their frequencies. As I showed in the thread, playing 100 combos assures, on average, 2+ hits winning _3 of 6/49_ prizes. I know, it isn't much... but there are good chances at the jackpot. As you can see, each lotto number appears as many times as its frequency. For example, lotto #1 hit 5 times within the _parpaluck_ (draw range). Therefore, #1 is listed 5 times in its respective position. The more times a number is listed, the higher the chance it will be drawn by the random number generator (as the ones I created for picking values from a list or string of numbers).

-   1 1 1 1 1 2 2 2 2 10 10 10 7 7 4 4 9 9 3 3 6 8 12 20 24
-   13 13 13 12 12 9 9 14 14 21 21 24 24 5 6 8 15 16 19 20 3 10 26 27 31
-   24 24 24 19 19 16 16 25 25 30 30 32 32 17 18 10 20 21 12 13 26 27 14 9 34
-   30 30 30 24 24 20 20 31 31 34 34 23 17 25 26 28 29 18 15 32 33 21 35 38 41
-   36 36 36 36 38 38 38 40 40 42 42 31 33 34 35 17 37 21 39 24 41 29 43 44 46
-   41 41 41 46 46 46 47 47 47 49 49 49 42 42 43 43 38 38 39 39 35 35 26 33 45

At this time of writing, however, my _**lottery neural networking**_ theory can be applied manually only. Writing software isn't a walk in the park, axios! (Yes, it's me who [created all those concepts derived from _axiomatic: axio, axios, axiomatics_](https://www.facebook.com/Parpaluck/posts/10156708533469049). But I do not claim copyright protection for them, or for the royalty-names I create and grant to all sorts of persons. In my book, _axiomatic_ means _undeniably truthful_.) I always announce new software titles (updates too) on the **New Writings** you can see on all footers.

![There is no artificial intelligence, really: Only humans can make discoveries and program computers.](https://saliu.com/HLINE.gif)

### Conclusion: _"Close the page doors, HAL!"_

I've had disputes, arguments with strong advocates (more like political supporters) of "artificial intelligence". To me, most of the AI advocates sound like cult followers. They are definitely the "brain children" of HAL! They strongly believe that competent _neural networks_ (still a misnomer in the _artificial intelligence_ category) are capable of learning the lottery strategies I analyzed here. Furthermore, the computers should be able to "learn" from me and go further, much further.

I challenged the advocates to take a small step and "improve" or "take further" any of my lottery strategies. Or, any task their "neural net" software might have and learn a simple fact I would ask. Of course, the _artificial intelligence_ or _neural networking_ software is incapable of any such feats without proper implementation of special algorithms.

In the case of the lotto strategy you see right above this section, I would have loved computers (or anybody else's software) to take me one step further. That didn't happen, because my software didn't have the specific algorithms. So, what I did is I followed the very first AI methodology I started this article with. I used my natural intelligence again. Nothing artificial, albeit I took advantage of my artificial aides (computers, that is) as if they were _"workhorses"_.

Users of my lottery software had similar ideas years ago, soon after I went public with my lottery theories and programs. I have always stressed that repeating the process of generating random numbers increases the degree of certainty of hitting the jackpot combination. And that is true even in the case of my free random-number generators. In the case of my random-generating programs that employ filters, however, the degree of certainty is higher by factors of magnitude.

I remember one software user wrote to me something along these lines: _"Hey, Parpaluck, axiomatic one! I applied a few safe filters in your program and let the soft run for a few hours \[the computers were far slower back in the day\]. I noticed several repeating combos in a run of around 5000 combinations. Curious thing, there was a jackpot winner amongst the repeated combos!"_

The user of my lottery software simply discovered the... discovery using his own intelligence, not "artificial intelligence". Nonetheless, his computer did help him in his discovery. Only the computers can generate millions of lotto combinations in a reasonable time, while filtering out unwanted millions of lines (impossible-to-play tickets). Then, the computers can check for winners in a very short time.

Everybody can use my totally-free random lotto combinations generators — both _online_ (Internet Explorer only) and _off-line_. A module can generate 1000 combinations (lines) at a time. You can select all lines in the textbox and save to a text file (you can use **Notepad++** for the task). You can repeat the procedure some 100 times (takes a few minutes) and save to the same text file by appending (until you get some 100,000 lines).

At this point, you can employ another great freebie: **CMSort**. It works at the _**command prompt**_. Use the setting _D=Duplicate1.file_. The small app will sort the text file you saved in **Notepad++**, will remove the duplicates, and will save the duplicates to _D=Duplicate1.file_. When you open that file again in **Notepad++** you will notice that some combinations repeat more than once. You can repeat the **CMSort** procedure several times. Say, you want to play only the lotto combinations that repeated 5 times or more. Thusly, sort _D=Duplicate1.file_ and save the 2+ repeats to _D=Duplicate2.file_, etc. The more times a combination repeats, the higher its chance to win.

Evidently, my registered software works better by orders of magnitude since <u>it applies a plethora of filters</u>. Still, even the _free_ case-scenario above does offer players better chances at hitting the jackpots. In the case of humongous-odds lotto games (Powerball, Mega Millions), repeat the generating procedures maybe 1000+ times. It takes a while, but then you can play the same sets of repeated combinations for longer periods of time (lottery drawings). You should check that none of the free repeated combinations had a jackpot win in the history of your game of choice.

-   "Learning by the neural nets" is a misnomer. Alas, the computers CANNOT learn anything! They can only learn what the programmer teaches them. And the "learning" is totally dependent on the data fed to the "artificial intelligence (AI)" software. And the "rules" imposed by the programmer are strongly specific to the "field of knowledge" the AI was designed for.
-   Artificial intelligence is **software** (as complex as it may be). Software consists of algorithms. "Learning" is only achieved via the algorithms. The more comprehensive the set of algorithms, the more successful the "neural network" is — in predicting, that is. Prediction is the main purpose of "AI".
-   The “artificial intelligence” misnomer means a special type of computer programming. I call it _**gigantic-database software**_. I think it originated in _**dBaseIII+**_. It was a programming platform as well, with <u>intelligent queries</u>. “Artificial intelligence” put the legendary software… on steroids! More and more powerful computers have been able to process huger and huger amounts of data, faster and faster. <u><i>"AI"</i> is a form of brute-force data-processing.</u> Google put it so eloquently in one of their TV ads: _**“Know what your data knows”**_.
-   Neural nets sound a lot like science fiction. Reality is far behind, however. The programmers of artificial intelligence software do NOT teach the computers _how to learn_; the software teaches the computers _**what** to do based on programmer's knowledge_. Otherwise, we would see a gigantic software app that learns everything that **all** humans know... and then some.
-   The "artificial intelligence" thingy came to "life" after a cult was created by a great film, albeit in the _science fiction_ genre: _"2001: A Space Odyssey"_ by Stanley Kubrick. _HAL_, the impressive computer, was "more intelligent" than all humans combined! That's, well, just science fiction, axios!
-   If a computer program beats the heck out of you chess player, it is so because good chess softwares have huge libraries of moves and millions of games already played! Intelligent algorithms direct the program to select the-next-move with the highest rate of success in past data. The chess programs are 100% human creations. There are huge differences between chess software apps — the best programmers create the best applications. The best super-super computers create... NOTHING!
-   [<u><i><b>Chess: The only case where AI is the closest to real INTELLIGENCE (human-like)</b></i></u>](https://www.facebook.com/Parpaluck/posts/pfbid02MgjsXVjiLWzEpNpWpQoTxkncumNcbDD5CBx8BayzKz3cW9dwSUu5kbrafRkqhTZgl).

_“Believing in artificial intelligence (AI) literally is like getting drunk with illusions (ILL) metaphorically.”_ – Ion Saliu, 3:14, 6:18.

![Neural networking, neural networks, artificial intelligence applied to predicting lottery, lotto.](https://saliu.com/HLINE.gif)

[

## Resources in Lottery Software, Systems, Strategies, Neural Networking

](https://saliu.com/content/lottery.html)

-   Given the enormity of the lottery phenomenon, a lot has to be written about it from the scientific viewpoint. I have written a lot about lottery myself. I only offer an abbreviated list of my Web pages on the topic of **mathematics of lottery strategy**.
-   [<u><i><b>Filters in Lottery Software, Lotto Software</b></i></u>](https://saliu.com/filters.html).
-   [<u><i><b>Lotto Strategy: Sums, Odd Even, Low High Numbers</b></i></u>](https://saliu.com/strategy.html).
-   [**<u>Lottery Skip Systems</u>**](https://saliu.com/skip-strategy.html): _**Lotto, Powerball, Mega Millions, Euromillions, Horse Racing, Roulette, Football**_.
-   [_**Lotto, Lottery <u>Strategy in Reverse</u>: Not-to-Win Leads to Not-to-Lose or WIN**_](https://saliu.com/reverse-strategy.html).
-   [**Theory, Analysis of _Deltas_ in Lotto, Lottery Software, Strategy, Systems**](https://saliu.com/delta-lotto-software.html).
-   [<u><i><b>Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies</b></i></u>](https://saliu.com/Newsgroups.htm).
-   [_**Software to Generate Lotto Combinations with Favorite Lottery Numbers in Fixed Positions**_](https://saliu.com/favorite-lottery-numbers-positions.html).
-   [_**Artificial Intelligence, AI Essay: Ion Saliu's Philosophy of Politics**_](https://saliu.com/AI-political-systems-Ion-Saliu.html).
-   [_**Artificial Intelligence, AI Chatbots, Ion Saliu**_](https://saliu.com/ai-chatbots-ion-saliu.html).
-   [**Lottery Mathematics, Lotto Mathematics, Math, Maths**](https://saliu.com/gambling-lottery-lotto/lottery-math.htm): Probability, Appearance, Repeat, Affinity, Number Affiliation, Lotto Wheels, Lottery Systems, Strategies.
-   [_**Lottery, Software, Systems, Science, Mathematics**_](https://saliu.com/lottery.html).
-   [_**Lottery, Software, Systems, Science, Mathematics**_](https://software.saliu.com/lottery-strategy-lotto-strategies.html).
-   [_**Lottery Prediction Using Neural Networks**_](https://groups.google.com/forum/#!topic/comp.ai.neural-nets/vnz8DKlBQqM)
-   [_**Lottery Numbers: Loss, Cost, Drawings, House Advantage, Edge**_](https://saliu.com/lottery-numbers-loss.html)
    -   Playing random lottery numbers or favorite numbers guarantees losses because of the _house edge_. Only lottery strategies, systems, special software can win with consistency and make a profit.
    -   [_**Online Random Number Generator: Lotto, Powerball, Mega Millions, Lottery, Horse Racing, Roulette, Sports Betting, Euromillions**_](https://saliu.com/generator.html).
    -   [_**Offline Odds Calculator, Number Combination Generator for Lottery, Lotto, Powerball, Mega Millions, Euromillions, Horses, Sports, Roulette**_](https://saliu.com/gambling-lottery-lotto/odds-generator.html).
-   Take a tour of the [**software**](https://saliu.com/infodown.html) download site.

![Ion Saliu devised neural networking, AI strategies for lottery and lotto long before anyone else.](https://saliu.com/HLINE.gif)

**[Home](https://saliu.com/index.htm) | [Search](https://saliu.com/Search.htm) | [New Writings](https://saliu.com/bbs/index.html) | [Fundamental Formula](https://saliu.com/formula.htm) | [Odds, Generator](https://saliu.com/calculator_generator.html) | [Contents](https://saliu.com/content/index.html) | [Forums](https://forums.saliu.com/) | [Sitemap](https://saliu.com/sitemap/index.html)**

![Neural networking strategies in lottery are based on lotto number frequency, pairing in past draws.](https://saliu.com/HLINE.gif)
