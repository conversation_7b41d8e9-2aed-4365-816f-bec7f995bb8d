---
created: 2025-07-23T18:23:44 (UTC +08:00)
tags: [MDIEditor Lotto WE,software,lotto,lottery,Powerball,Keno,gambling,horse racing,pick 3 4,statistics,combinations,numbers,Euromillions,Mega Millions,]
source: https://saliu.com/mdi_lotto.html
author: 
---

# Documentation, Help: MDIEditor Lotto WE, Lottery Software

> ## Excerpt
> Documentation, F1 help for the best software to win lotto, lottery, Powerball, Mega Millions, Euromillions, Keno, gambling, horse racing.

---
<big>• Current version: <i>4.0_WE</i> ~ Final ~ October 2006.</big>  
Follow the links and read the comprehensive manual and ebook of the latest version.  
Read the official page of MDIEditor And Lotto WE:  
[_**<u>MDIEditor Lotto WE</u>: Tutorial, Software User Guide, Manual, Book**_](https://saliu.com/MDI-lotto-guide.html).

![Run the best software for lotto, lottery, Powerball, Mega Millions, Euromillions: MDIEditor.](https://saliu.com/ScreenImgs/mdi-lotto-software.gif)

1.1) Two much requested options were added:  
~ enable or disable the inner filters: a user's choice!  
~ enable or disable the elimination of any number of past drawings: a user's choice!

This version _1.3 WE_ wipes out the crippled _0.1 WE_ incarnation of _**MDIEditor Lotto WE**_. Although many users appreciated the opportunity to work with a potent application, I received also complaints. For one, some users faced installation problems. But the most serious complaint was put this way: I played the temptation game. _**MDIEditor and Lotto 0.1 WE**_ displayed very promising features (e.g. powerful filters) only to disable them! Perhaps I intended first to get people to order the licensed version. But my business intention had no sadistic underlying. Honestly, I forgot months ago that I wanted to sell an application, or any software, for that matter.

I made a promise a few years back. I promised I would offer free software in exchange for new ideas in lottery, lotto, gambling. To be sure, I received a lot of intentional garbage. Nonetheless, I received some worthy ideas. At one point, it became impossible to keep my promise. I would have had to spend my entire time emailing freeware! Downloading freeware from an FTP site does not affect my time.

I also wanted to remain fair to those who paid for my software. I believe a balance of fairness is still in place. Those who paid for my software already gained a head start. They will be using other programs of mine that are not freeware — and possibly will never be free. In fairness to others: Quite a few people would have paid for my software, but they will never have the financial means.

<u>This program is very well documented now.</u> The help facility covers every menu item. There are also several other topics in the _Help_ menu, especially a lottery tutorial. Using _**MDIEditor and Lotto WE**_ should be as easy as a breeze, bar a negligible percentage of users whom I would not be able to help no matter what I do. The one real difficulty is <u>setting the filters</u> (i.e. using _strategies_). The topic would easily fill a voluminous book.

Just following the links on this page, plus the ensuing cross referencing, copying and pasting the relevant information — and you'll end up with a large number of book-size pages! And that's the best way to use the strategy feature of this application. Copy and paste all relevant pages, then rewrite the material using your own words.

Keep in mind that there is a countless number of possible **strategies**; i.e. <u>filter settings</u>. The median of each filter could be a strategy pivot. If, for example, the median is 4, you'll notice that several of the filter values reach 3, or 4, or 5. One strategy would be setting the respective filter as follows: _minimum = 4 and maximum = 5_. Such a setting describes a filter value equal to 4 in the corresponding filter column of the statistical report. To set a filter between 3 and 5: _minimum = 3 and maximum = 6_.

• _Some filters, such as _Ion5_, can take very high values, but they can go even higher! Read carefully the tips at the end of this document._

The _Stats_ menu leads to the modules that calculate the filter values in past draws. The routine names are self-describing: pick-3, pick-4, horse racing, lotto-5, lotto-6, lotto-7, Powerball, Mega Millions, Euromillions, and Keno.

The combination generating modules can be accessed in the _Digit_ and _Lotto_ menus. The _Digit_ menu generates combinations for: pick 3, pick 4, and horse racing. The _Lotto_ menu generates combinations for: lotto-5, lotto-6, lotto-7, Powerball _5+1_, Powerball _6+1_, Euromillions, Keno.

_Horses 3_ refers to _trifectas_ or _triactors_ (the top three finishers). _Horses 4_ refers to _superfectas_ (the top four finishers). The superfecta wager is offered rarely, and only at races with at least 9 horses. Therefore, you need to keep records for races with 9 or more horses.

It is recommended to run first the statistical functions to get a general idea of the filters. Next, run the _Random_ combination generation routines, without setting any filter. Don't write any number in the input form; just click OK to generate _Optimized Random Combinations_.

It may me a good idea to uninstall all previous versions of _**MDIEditor and Lotto**_. It's one way to eliminate some potential problems (related to the Windows registry). You need to copy your data files (and lotto wheels, if you wish) to a temporary folder. Then uninstall the old installations, and finally install this version of _**MDIEditor Lotto WE**_. After the application installed successfully, move your old data files (and wheels, if applicable) to the new _**MDIEditor Lotto WE**_ <u>folder</u>.

I noticed one problem — it could be specific to my system, though. I use several horse racing software packages, all my own creation. The packages use several .INI files. To avoid problems, I run first the randomized combinations modules, then the _sequential combinations_ functions.

You can generate also other types of random combinations: coin tossing, dice rolling, and roulette. All such hidden combination types are available in the _Lotto_ menu, the <u>Odds + Random</u> functions.

-   Coin tosses: _The biggest number = 2_; Numbers per combination = 1. You can decide that _1_ represents _heads_ and _2_ represents _tails_.
-   Rolling one dye: _The biggest number = 6_; Numbers per combination = 1. For multiple dice, run the function in separate files. Click the _New_ toolbar button to create empty windows.
-   Roulette spins, single zero: _The biggest number = 37_; Numbers per combination = 1. Number _37_ signifies _0_.
-   Roulette spins, double zero: _The biggest number = 38_; Numbers per combination = 1. Number _37_ signifies _0_; number **38** signifies **00**.

Other features are fully implemented: _sorting_ data files, _checking strategies_, _checking winners_, and _checking for duplicates_ in output files. The odds calculators and random generators are fully implemented, including for Powerball and horse-racing. A special _horse racing 4_ app was added to handle the highly paying _superfecta_ betting. The superfectas (_the top 4 finishers in order_) are offered more and more at the U.S. tracks. The bet pays quite often in the 5-figure range!

The statistical modules create also the _**wonder grid**_. The _**wonder-grid**_ consists of each lottery number/digit and its top pairings. For example, the _**wonder grid**_ of a lotto 6/49 game consists of 49 lines. Each line starts with each lotto number, followed by its top 5 (most frequent) pairings.

The software implements a very sophisticated _randomizing_ feature. It is very much user-specific, in addition to time specific. The random seed most probably will not repeat in a lifetime. Somebody compared the new random results with the UK lotto-6 draws. Statistically, the two types of combinations were very consistent to each other. There was even a problem: The random generating modules eliminate all jackpot-winning combinations in the data file. For example, the program will not generate lotto 6 combinations that are also present in the data file.

The program requires a data file with at least 10,000 combinations (lines). You only have a few hundred real draws available. Not to worry. The program creates simulated files as well. You'll add the real drawings to the top of the simulated data files. The program generates simulated combinations that will happen to be future jackpot winners—quite often! You will not get the jackpot winner at such moments. It is a necessary trade-off—play as few combinations as possible. The software employs other _invisible_ filters — built-in or innate filters. They are not to be discussed. They are employed for the same purpose of working with as few combinations as possible. No one should expect winning the big one every time one runs _**MDIEditor Lotto WE**_.

You can use _**MDIEditor Lotto WE**_ in conjunction with all my software that's legally installed on your system. The feature in point is _**Purge**_. You can generate combinations in the _**command prompt <u>LotWon</u>**_ and _**purge**_ the output file in _**MDIEditor Lotto WE**_ — and vice versa. You can generate also combinations in a very special probability program: _**BellCurveGenerator**_.

Suffice to say for now that the combinations inside the _FFG median bell_ come out clearly more often than combinations outside the _FFG median bell_. One real case: sports betting. I have been choosing randomly 5-games in the NFL. The combinations inside the FFG median bell represent less than 40% of total combinations. Still, they hit 7 out of the first 9 weeks in the NFL 2003 season! Sports-betting is not covered in _**MDIEditor Lotto WE**_. So, you can generate as many combinations as you want by running _**_Bell Curve Generator_**_. Then, purge the output file by using _**MDIEditor Lotto WE**_ and/or _**command prompt <u>LotWon</u>**_.

There are still lotto wheels bundled with _**MDIEditor Lotto WE**_. You may have noticed that I have a negative opinion about the lotto wheels. Please read one of the pages on lotto wheels and also download damage-control lotto wheels or reduced lotto systems:  
[**Lotto wheels**](https://saliu.com/lotto_wheels.html), _**reduced lottery systems**_: balanced, randomized, <u>free</u>.

![Best Lottery Software: Lotto, Loto, Lottery, Loterie, Powerball, Keno, Gambling, Horse Racing.](https://saliu.com/HLINE.gif)

• Tip

Many users view the _Ion\_5_ filter as the key to a goldmine. They see values such as _417_. They think immediately of setting _Ion5_ as follows:  
~ minimum level = 417  
~ maximum level = 418.  

They run the program and get no combination at all after days and nights of continuous running! There is no bug in _**MDIEditor and Lotto WE**_. A repeating value of 417 for _Ion\_5_ indicates an insufficient size of the data file. The 417 value is not to be relied on as far a maximum level is concerned. Most likely, _Ion5_ goes a lot higher. You should not use _Max\_Ion5=418_ under these circumstances! That's why your computer doesn't generate any combination. It is very, very rare for a value of _Ion5_ to reach _EXACTLY 417_. The maximum values should be used only with very large data files. Any time you see a value higher than 100 (e.g. 417 or 1000) repeating more than a dozen times, it should raise a red flag. The _D6_ data file is too small (real lotto draws + SIMulated combinations).

Create a very large SIMulated data file — I recommend at least 100,000 (one hundred thousand) combinations (lines). You can use the random modules in _**MDIEditor Lotto**_ or/and the _DOS_ (_command prompt_) editions of **LotWon** lottery software. The same is valid for _**WHEEL\_632**_. I created _SIM_ files of over 200,000 lines, then I purged them. It's best to work with clean data files (that is, files without duplicate combinations). I encountered _FivS_ and _FivR_ values of over 100,000 (one hundred thousand). Such extreme levels of filtering have a devastating effect on the lotto odds.

![Run the best free software for lotto, lottery, Powerball, Mega Millions, Euromillions.](https://saliu.com/HLINE.gif)

## [Resources in Lottery, Software, Systems, Lotto Wheels, Strategies](https://saliu.com/content/lottery.html)

-   The Main [_**Lotto, Lottery, Software, Strategies**_](https://saliu.com/LottoWin.htm) Page.  
    Presenting software to create free winning lotto, lottery strategies, systems based on mathematics. Get your lotto systems or wheels, the best lottery, lotto software, combinations, winning numbers.
-   [_**Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies**_](https://saliu.com/Newsgroups.htm).  
    Read a genuine analysis of Excel spreadsheets applied to lottery and lotto developing of software, systems, and strategies. Combining Excel analysis with powerful lottery and lotto software programmed by this author, _Parpaluck_.
-   [_**<u>MDIEditor Lotto WE</u>: Tutorial, Software User Guide, Manual, Book, ebook**_](https://saliu.com/MDI-lotto-guide.html).  
    ~ Also applicable to LotWon lottery, lotto software; plus Powerball, Mega Millions, Euromillions.
-   [_**Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps, Programs**_](https://saliu.com/forum/lotto-book.html).
    
    _**Pages dedicated to help, instructions, filters, strategies for the best lotto programs and lottery software in the world:**_
    
-   [_**MDI Editor Lotto**_ Is the Best Lotto Lottery Software; You Be Judge](https://saliu.com/bbs/messages/623.html).
-   [_**Filters, Restrictions, Elimination, Reduction in Lotto, Lottery Software**_](https://saliu.com/bbs/messages/42.html).
-   [_**Step-By-Step Guide to Lotto, Lottery Filters in Software**_](https://saliu.com/bbs/messages/569.html).
-   [_**Basic Manual for Lotto Software, Lottery Software**_](https://saliu.com/bbs/messages/818.html).
-   [_**<u>Vertical or Positional</u> Filters in Lottery Software**_](https://saliu.com/bbs/messages/838.html).
-   [_**Beginner's Basic Steps to**_ **LotWon**](https://saliu.com/bbs/messages/896.html) _**Lottery Software, Lotto Software**_.
-   [**Dynamic** or **Static** _**Filters: Lottery Software, Lotto Analysis, Mathematics**_](https://saliu.com/bbs/messages/919.html).
-   [**<u>Lottery Skip Systems</u>**](https://saliu.com/skip-strategy.html): _**Lotto, Powerball, Mega Millions, Euromillions**_.
-   [_**Lotto Strategy: Lottery Sums, Odd Even, Low High Numbers**_](https://saliu.com/strategy.html).
-   [**Lottery Utility Software**](https://saliu.com/lottery-utility.html)_**: Pick-3, 4 Lottery, Lotto-5, 6, Powerball, Mega Millions, Thunderball, Euromillions.**_
-   [_**Filters in Lottery Software, Lotto Software**_](https://saliu.com/filters.html).
-   [_**Lottery Strategy, Systems Based on Number Frequency**_](https://saliu.com/frequency-lottery.html)
-   [_**Lotto Decades: Software, Reports, Analysis, Strategies**_](https://saliu.com/decades.html)
-   [_**Analysis of Best Ranges for Lotto Number Frequency, Lottery Pairs, Pairings**_](https://saliu.com/lottery-lotto-pairs.html).
-   [_**Lotto Software for Groups of Numbers: Odd, Even, Low, High, Sums, Frequencies**_](https://saliu.com/lotto-groups.html).
-   [**Pick-3 Lottery Strategy Software, System, Method**](https://saliu.com/STR30.htm)_: Play Pairs in the Last 100 Draws._
-   _"The Start Is the Hardest Part"_: [_**Play a Lotto Strategy, Lottery Strategies**_](https://forums.saliu.com/lottery-strategies-start.html).
-   <u>Download</u> [_**Lottery Software, Lotto Programs, Apps**_](https://saliu.com/infodown.html).

![MDIEditor Lotto super lottery application was released with fanfare.](https://saliu.com/HLINE.gif)

**[Home](https://saliu.com/index.htm) | [Search](https://saliu.com/Search.htm) | [New Writings](https://saliu.com/bbs/index.html) | [Fundamental Formula](https://saliu.com/formula.htm) | [Odds, Generator](https://saliu.com/calculator_generator.html) | [Contents](https://saliu.com/content/index.html) | [Forums](https://forums.saliu.com/) | [Sitemap](https://saliu.com/sitemap/index.html)**

![MDIEditor Lotto covers all lotto games, Powerball, Mega Millions, pick lottery games, horses.](https://saliu.com/HLINE.gif)
