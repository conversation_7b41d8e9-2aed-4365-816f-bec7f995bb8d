using WonderGridLotterySystem
using Statistics
using Dates

println("Testing Enhanced Key Number Selection Algorithm")
println("=" ^ 50)

# Load lottery data
fm = FileManager()
data = read_data5_file(fm, "data/fan5.csv")
println("Loaded $(length(data)) lottery draws")

# Create Wonder Grid engine
engine = WonderGridEngine(data)

# Test enhanced key number evaluation
println("\nTesting Enhanced Key Number Evaluation:")
println("=" ^ 50)

evaluated_numbers = evaluate_key_numbers(engine)
println("Found $(length(evaluated_numbers)) favorable key numbers with detailed evaluation")

# Show top 10 evaluated numbers
println("\nTop 10 Key Numbers (by composite score):")
println("Rank | Number | Score | Skip Ratio | Pairing | Probability | Frequency")
println("-" ^ 75)

for (i, (number, score, metrics)) in enumerate(evaluated_numbers[1:min(10, length(evaluated_numbers))])
    println("$(lpad(i, 4)) | $(lpad(number, 6)) | $(lpad(round(score, digits=3), 5)) | " *
            "$(lpad(round(metrics["skip_ratio"], digits=2), 10)) | " *
            "$(lpad(round(metrics["pairing_strength"], digits=2), 7)) | " *
            "$(lpad(round(metrics["skip_probability"], digits=2), 11)) | " *
            "$(lpad(round(metrics["frequency_score"], digits=2), 9))")
end

# Test automatic key number selection
println("\n" * "=" ^ 50)
println("Testing Automatic Key Number Selection:")
println("=" ^ 50)

auto_selected = select_key_numbers_auto(engine, 5)
println("Automatically selected top 5 key numbers: $(join(auto_selected, ", "))")

# Test filtered key number selection
println("\nTesting Filtered Key Number Selection:")
filtered_numbers = select_key_numbers_filtered(engine, 
                                             max_skip_ratio=0.5, 
                                             min_pairing_strength=0.8,
                                             min_probability=0.6,
                                             max_numbers=8)
println("Filtered key numbers (strict criteria): $(join(filtered_numbers, ", "))")

# Test detailed analysis for top key numbers
println("\n" * "=" ^ 50)
println("Detailed Analysis of Top 3 Key Numbers:")
println("=" ^ 50)

for (i, number) in enumerate(auto_selected[1:min(3, length(auto_selected))])
    println("\n$i. Detailed Analysis for Key Number $number:")
    analysis = analyze_key_number(engine, number)
    
    println("  Status: $(analysis["recommendation"])")
    println("  Current Skip: $(analysis["current_skip"])")
    println("  FFG Median: $(round(analysis["ffg_median"], digits=2))")
    println("  Skip Ratio: $(round(analysis["skip_ratio"], digits=2))")
    println("  Skip Probability: $(round(analysis["skip_probability"], digits=2))")
    println("  Top Pairings: $(join(analysis["top_pairings"], ", "))")
    println("  Potential Combinations: $(analysis["potential_combinations"])")
    println("  Frequency Score: $(round(analysis["frequency_score"], digits=2))")
    println("  Stability Score: $(round(analysis["stability_score"], digits=2))")
end

# Test key number comparison
println("\n" * "=" ^ 50)
println("Key Number Comparison Test:")
println("=" ^ 50)

comparison_numbers = auto_selected[1:min(5, length(auto_selected))]
comparison = compare_key_numbers(engine, comparison_numbers)

println("Comparing numbers: $(join(comparison_numbers, ", "))")
println("Best overall candidate: $(comparison["best_overall"])")
println("Best favorable candidate: $(comparison["best_favorable"])")
println("Favorable count: $(comparison["favorable_count"])/$(length(comparison_numbers))")

# Test ranking functionality
println("\n" * "=" ^ 50)
println("Key Number Ranking Test:")
println("=" ^ 50)

ranked_numbers = rank_key_numbers(engine, 15)
println("Top 15 Ranked Key Numbers:")
println("Rank | Number | Composite Score")
println("-" ^ 30)

for (i, (number, score)) in enumerate(ranked_numbers)
    println("$(lpad(i, 4)) | $(lpad(number, 6)) | $(lpad(round(score, digits=3), 15))")
end

println("\n" * "=" ^ 50)
println("Enhanced Key Number Selection Test Complete!")
println("=" ^ 50)