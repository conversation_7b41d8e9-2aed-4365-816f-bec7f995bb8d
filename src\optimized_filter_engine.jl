# Optimized Filter Engine
# 優化過濾器引擎 - 整合快取、記憶體池和緊湊數據結構

using Dates

# 引入基礎類型
include("types.jl")

# 引入優化組件
include("cache/multi_level_cache.jl")
include("memory/memory_pool.jl")
include("data/compact_structures.jl")

"""
優化過濾器引擎
"""
mutable struct OptimizedFilterEngine
    # 數據存儲
    historical_data::Vector{LotteryDraw}
    compact_data::Union{CompactLotteryDataset, Nothing}
    compact_accessor::Union{CompactDataAccessor, Nothing}
    
    # 快取系統
    skip_cache::SkipCalculationCache
    ffg_cache::FFGCalculationCache
    pairing_cache::PairingCache
    
    # 快取一致性
    cache_manager::CacheConsistencyManager
    data_hash::String
    
    # 性能統計
    stats::Dict{String, Any}
    
    # 配置選項
    use_compact_data::Bool
    enable_caching::Bool
    auto_cleanup::Bool
    
    function OptimizedFilterEngine(
        historical_data::Vector{LotteryDraw};
        use_compact_data::Bool = true,
        enable_caching::Bool = true,
        auto_cleanup::Bool = true
    )
        # 創建快取系統
        skip_cache = SkipCalculationCache()
        ffg_cache = FFGCalculationCache()
        pairing_cache = PairingCache()
        
        # 創建快取管理器
        cache_manager = CacheConsistencyManager()
        data_hash = generate_data_hash(get_data_version(cache_manager), length(historical_data))
        
        # 創建緊湊數據（如果啟用）
        compact_data = nothing
        compact_accessor = nothing
        if use_compact_data
            try
                compact_data = create_compact_dataset(historical_data)
                compact_accessor = create_compact_accessor(compact_data)
                
                # 驗證數據完整性
                if !verify_data_integrity(historical_data, compact_data)
                    @warn "緊湊數據完整性驗證失敗，禁用緊湊模式"
                    compact_data = nothing
                    compact_accessor = nothing
                    use_compact_data = false
                end
            catch e
                @warn "創建緊湊數據失敗: $(e)，禁用緊湊模式"
                compact_data = nothing
                compact_accessor = nothing
                use_compact_data = false
            end
        end
        
        # 初始化統計
        stats = Dict{String, Any}(
            "cache_hits" => 0,
            "cache_misses" => 0,
            "calculations_performed" => 0,
            "memory_pool_reuses" => 0,
            "last_cleanup" => now()
        )
        
        new(
            historical_data,
            compact_data,
            compact_accessor,
            skip_cache,
            ffg_cache,
            pairing_cache,
            cache_manager,
            data_hash,
            stats,
            use_compact_data,
            enable_caching,
            auto_cleanup
        )
    end
end

"""
優化的 Skip 計算
"""
function calculate_skip_optimized(engine::OptimizedFilterEngine, number::Int)::Int
    # 檢查快取
    if engine.enable_caching
        cached_skip = get_skip_cached(engine.skip_cache, number, engine.data_hash)
        if cached_skip !== nothing
            engine.stats["cache_hits"] += 1
            return cached_skip
        end
        engine.stats["cache_misses"] += 1
    end
    
    # 執行計算
    skip_value = if engine.use_compact_data && engine.compact_accessor !== nothing
        calculate_skip_from_compact(engine.compact_accessor, number)
    else
        calculate_skip_from_regular(engine.historical_data, number)
    end
    
    # 存入快取
    if engine.enable_caching
        put_skip_cached!(engine.skip_cache, number, engine.data_hash, skip_value)
    end
    
    engine.stats["calculations_performed"] += 1
    return skip_value
end

"""
從緊湊數據計算 Skip
"""
function calculate_skip_from_compact(accessor::CompactDataAccessor, number::Int)::Int
    for i in 1:length(accessor.dataset.draws)
        if contains_number_at(accessor, i, number)
            return i - 1
        end
    end
    return length(accessor.dataset.draws)
end

"""
從普通數據計算 Skip
"""
function calculate_skip_from_regular(data::Vector{LotteryDraw}, number::Int)::Int
    for (i, draw) in enumerate(data)
        if number in draw.numbers
            return i - 1
        end
    end
    return length(data)
end

"""
優化的配對頻率計算
"""
function calculate_pairing_frequency_optimized(engine::OptimizedFilterEngine, num1::Int, num2::Int)::Int
    # 確保 num1 <= num2
    if num1 > num2
        num1, num2 = num2, num1
    end
    
    # 檢查快取
    if engine.enable_caching
        cached_freq = get_pairing_cached(engine.pairing_cache, num1, num2, engine.data_hash)
        if cached_freq !== nothing
            engine.stats["cache_hits"] += 1
            return cached_freq
        end
        engine.stats["cache_misses"] += 1
    end
    
    # 執行計算
    frequency = if engine.use_compact_data && engine.compact_accessor !== nothing
        calculate_pairing_from_compact(engine.compact_accessor, num1, num2)
    else
        calculate_pairing_from_regular(engine.historical_data, num1, num2)
    end
    
    # 存入快取
    if engine.enable_caching
        put_pairing_cached!(engine.pairing_cache, num1, num2, engine.data_hash, frequency)
    end
    
    engine.stats["calculations_performed"] += 1
    return frequency
end

"""
從緊湊數據計算配對頻率
"""
function calculate_pairing_from_compact(accessor::CompactDataAccessor, num1::Int, num2::Int)::Int
    frequency = 0
    for i in 1:length(accessor.dataset.draws)
        numbers = get_numbers_at(accessor, i)
        if num1 in numbers && num2 in numbers
            frequency += 1
        end
    end
    return frequency
end

"""
從普通數據計算配對頻率
"""
function calculate_pairing_from_regular(data::Vector{LotteryDraw}, num1::Int, num2::Int)::Int
    frequency = 0
    for draw in data
        if num1 in draw.numbers && num2 in draw.numbers
            frequency += 1
        end
    end
    return frequency
end

"""
優化的 ONE 過濾器
"""
function calculate_one_filter_optimized(engine::OptimizedFilterEngine, number::Int)::FilterResult
    current_skip = calculate_skip_optimized(engine, number)
    
    # 使用記憶體池獲取臨時向量來計算統計
    temp_skips = get_temp_vector(Int, 100)
    
    try
        # 計算歷史 Skip 值
        if engine.use_compact_data && engine.compact_accessor !== nothing
            calculate_historical_skips_compact!(temp_skips, engine.compact_accessor, number)
        else
            calculate_historical_skips_regular!(temp_skips, engine.historical_data, number)
        end
        
        # 計算期望值
        expected_value = if !isempty(temp_skips)
            sum(temp_skips) / length(temp_skips)
        else
            Float64(current_skip)
        end
        
        return FilterResult(current_skip, expected_value, "ONE")
        
    finally
        # 返回臨時向量到池中
        return_temp_vector!(temp_skips)
        engine.stats["memory_pool_reuses"] += 1
    end
end

"""
從緊湊數據計算歷史 Skip 值
"""
function calculate_historical_skips_compact!(skips::Vector{Int}, accessor::CompactDataAccessor, number::Int)
    empty!(skips)
    last_occurrence = 0
    
    for i in 1:length(accessor.dataset.draws)
        if contains_number_at(accessor, i, number)
            if last_occurrence > 0
                push!(skips, i - last_occurrence - 1)
            end
            last_occurrence = i
        end
    end
end

"""
從普通數據計算歷史 Skip 值
"""
function calculate_historical_skips_regular!(skips::Vector{Int}, data::Vector{LotteryDraw}, number::Int)
    empty!(skips)
    last_occurrence = 0
    
    for (i, draw) in enumerate(data)
        if number in draw.numbers
            if last_occurrence > 0
                push!(skips, i - last_occurrence - 1)
            end
            last_occurrence = i
        end
    end
end

"""
優化的 TWO 過濾器
"""
function calculate_two_filter_optimized(engine::OptimizedFilterEngine, numbers::Vector{Int})::FilterResult
    if length(numbers) < 2
        return FilterResult(0, 0.0, "TWO")
    end
    
    # 使用記憶體池獲取臨時向量
    temp_pairs = get_temp_vector(Tuple{Int,Int}, 20)
    
    try
        # 生成所有配對
        empty!(temp_pairs)
        for i in 1:length(numbers)
            for j in i+1:length(numbers)
                push!(temp_pairs, (numbers[i], numbers[j]))
            end
        end
        
        # 計算配對頻率
        total_frequency = 0
        for (num1, num2) in temp_pairs
            frequency = calculate_pairing_frequency_optimized(engine, num1, num2)
            total_frequency += frequency
        end
        
        # 計算期望值（簡化）
        expected_value = Float64(total_frequency) / length(temp_pairs)
        
        return FilterResult(total_frequency, expected_value, "TWO")
        
    finally
        return_temp_vector!(temp_pairs)
        engine.stats["memory_pool_reuses"] += 1
    end
end

"""
更新數據並使快取失效
"""
function update_data!(engine::OptimizedFilterEngine, new_data::Vector{LotteryDraw})
    # 更新數據
    engine.historical_data = new_data
    
    # 更新緊湊數據
    if engine.use_compact_data
        try
            engine.compact_data = create_compact_dataset(new_data)
            engine.compact_accessor = create_compact_accessor(engine.compact_data)
        catch e
            @warn "更新緊湊數據失敗: $e"
            engine.compact_data = nothing
            engine.compact_accessor = nothing
        end
    end
    
    # 使快取失效
    update_data_version!(engine.cache_manager)
    engine.data_hash = generate_data_hash(get_data_version(engine.cache_manager), length(new_data))
    
    # 清空快取
    clear_cache!(engine.skip_cache.cache)
    clear_cache!(engine.ffg_cache.cache)
    clear_cache!(engine.pairing_cache.cache)
end

"""
執行自動清理
"""
function auto_cleanup!(engine::OptimizedFilterEngine)
    if !engine.auto_cleanup
        return
    end
    
    current_time = now()
    last_cleanup = engine.stats["last_cleanup"]
    
    if current_time - last_cleanup >= Minute(15)  # 每15分鐘清理一次
        # 清理記憶體池
        cleaned = auto_cleanup!(GLOBAL_MEMORY_POOL)
        
        engine.stats["last_cleanup"] = current_time
        
        if cleaned > 0
            @info "自動清理完成，清理了 $cleaned 個記憶體池項目"
        end
    end
end

"""
獲取引擎統計信息
"""
function get_engine_statistics(engine::OptimizedFilterEngine)::Dict{String, Any}
    stats = copy(engine.stats)
    
    # 添加快取統計
    stats["skip_cache"] = get_cache_statistics(engine.skip_cache.cache)
    stats["ffg_cache"] = get_cache_statistics(engine.ffg_cache.cache)
    stats["pairing_cache"] = get_cache_statistics(engine.pairing_cache.cache)
    
    # 添加記憶體池統計
    stats["memory_pool"] = get_all_pool_statistics(GLOBAL_MEMORY_POOL)
    
    # 添加緊湊數據統計
    if engine.compact_data !== nothing
        stats["compact_data"] = get_compact_dataset_stats(engine.compact_data)
    end
    
    # 計算整體性能指標
    total_requests = stats["cache_hits"] + stats["cache_misses"]
    stats["cache_hit_rate"] = total_requests > 0 ? stats["cache_hits"] / total_requests : 0.0
    
    return stats
end

"""
獲取引擎信息
"""
function get_optimized_engine_info(engine::OptimizedFilterEngine)::Dict{String, Any}
    return Dict(
        "data_points" => length(engine.historical_data),
        "use_compact_data" => engine.use_compact_data,
        "enable_caching" => engine.enable_caching,
        "auto_cleanup" => engine.auto_cleanup,
        "data_version" => get_data_version(engine.cache_manager),
        "compact_data_available" => engine.compact_data !== nothing,
        "memory_savings_bytes" => engine.compact_data !== nothing ? 
            get_compact_dataset_stats(engine.compact_data)["memory_saved_bytes"] : 0
    )
end

# 導出主要函數和結構
export OptimizedFilterEngine
export calculate_skip_optimized, calculate_pairing_frequency_optimized
export calculate_one_filter_optimized, calculate_two_filter_optimized
export update_data!, auto_cleanup!, get_engine_statistics, get_optimized_engine_info
