---
created: 2025-07-23T18:23:53 (UTC +08:00)
tags: [lottery,mathematics,math,software,systems,system,strategy,strategies,software,wheels,designs,professors,university,treatise,formulae,formula,theorem,odds,]
source: https://saliu.com/gambling-lottery-lotto/lottery-math.htm
author: 
---

# Lottery Mathematics, Lotto Mathematics, Lottery Math

> ## Excerpt
> Lottery mathematics, lotto math: Theory, probabilities, odds, systems, wheels, designs, software, strategies greatly improve players' chances to win big.

---
![Learn lotto mathematics, odd, groups of lottery numbers, lotto wheels, designs, systems.](https://saliu.com/gambling-lottery-lotto/Line-1.gif)

### I. [Protomathematics of Lottery](https://saliu.com/gambling-lottery-lotto/lottery-math.htm#Protomath)  
II. [Mathematics of Lottery](https://saliu.com/gambling-lottery-lotto/lottery-math.htm#Mathematics)  
III. [Lottery Mathematics Links, Resources, Software](https://saliu.com/gambling-lottery-lotto/lottery-math.htm#Links)

![This is the introduction to lottery mathematics combined with the best lotto software.](https://saliu.com/gambling-lottery-lotto/HLINE.gif)

## <u>1. Protomathematics of Lottery</u>

First captured by the _WayBack Machine_ (_web.archive.org_) on July 15, 2008.

Probably, protomathematics of lottery starts with _K. J. Nurmela_ and _P. R. J. Östergård_ at the Helsinki University of Technology. Their publication is titled _"Constructing covering designs by simulated annealing"_. You can read this PDF document:  
citeseerx.ist.psu.edu/viewdoc/download?doi=10.1.1.48.3798&rep=rep1&type=pdf

The first attempt was followed by Pak Ching Li and G. H. John Van Rees at the University of Manitoba, Canada. The main scientific paper is titled: _"New Constructions of Lotto Designs"_. You can find and read two online PDF documents:  
cs.umanitoba.ca/~vanrees/upbd.pdf  
cs.umanitoba.ca/~vanrees/fau4.pdf

They all deal with _lotto designs_ and techniques for constructing lotto designs and determining upper bounds for _L(n, k, p, t)_. Lotto design sounds more…academic than the laypersons term! The academia people also use another fancy term: _covering designs_. The lotto designs are better known by laypersons as _lotto wheels_ or _abbreviated (reduced) lotto systems_.

A _lotto wheel_ is simply a set of a _reduced number of lotto combinations_ that assure a certain _minimum guarantee_ at the cost of forsaking the highest prize.

The would-be lottery mathematicians attempt to discover formulas for constructing the tightest possible lotto wheels. The professors come up with all kinds of approximations that look like mathematical formulae (by highly symbolized notation and heavy dosage of jargon). The margin of error, however, is as large as the area of Canada!

You might want to know the original _mathematics of approximation_, especially as applied in theory of probability. You might have heard of _Chebyshev's inequality_. I give a most simplified, but most comprehensible, interpretation of the famous inequality. For example, the approximation inequality guarantees that the probability to get heads in 10 tosses is higher than 75%. The assessment is correct, but the margin of approximation is gigantic. Au contraire, the _**Fundamental Formula of Gambling (FFG)**_ restricts the approximation to the _minimum minimorum_. As in the example above: The degree of certainty of 75% to get heads requires just 2 tosses. For a number of tosses equal to 10, the degree of certainty reaches 99.9%.

The professors analyze one lotto wheel, a famous one by now: _49 lotto-6 numbers with the 3 of 6 minimum guarantee_. In cuckoo speak: _LD(49, 6, 6, 3)=163_. That is, the guarantee of the lotto wheel for 6 from 49 numbers is satisfied in 163 lines (or 163 6-number combinations). The flaw is evident right from the start. It is not one group of 49 numbers, but two separate groups of numbers: 22 and 27, respectively. None of the numbers in the first group meets with any number in the second group.

The task of analyzing a flawed lotto wheel raised to the status of lottery mathematics sounds funny to me. But, hey, we got to start somewhere! Many things start like that! The lottery professors even invoke a true mathematician, Euler, in their research. They sow the pages with a plethora of theorems and formulae.

We have also met at this website a special probability formula: _hypergeometric distribution_. The hypergeometric probability distribution formula calculates the probabilities of getting _k of m in p from n numbers_. In layperson's words: The probability or odds to hit _3 (k) of 6 (m)_ winners, when I play a pool of _18 (p)_ numbers in a lotto game with a total of _49 (n)_ numbers in the field. The answer in this particular example is _1 in 3.8_ (or approximately once every 4 lotto drawings). But if we only play a single combination of 6 numbers instead of an 18-number pool, the _3 in 6_ odds are 1/56.66 (or approximately once every 57 lottery draws). The calculations are easily available to everybody by running the online random generator, odds calculator at my truly… mathematical site (see the footer)!

The wrong assumption of the lottery professors is the possibility of constructing a lotto wheel (lotto design sound better?) consisting of 57 lines (6-number combinations) to satisfy the _3 in 6_ minimal guarantee. That will never happen! No matter how many highly symbolized formulae and theorems a university paper would print! _57_ here is not probability, is not degree of certainty: It is number of trials! These are the three fundamental elements of random phenomena, including lottery and lotto games. The three elements are embodied in the _**Fundamental Formula of Gambling (FFG): Probability (p), degree of certainty (DC), and number of trials (N)**_.

We don't need a lotto design to hit the lottery according to the hypergeometric distribution formula. Just play 57 unique 6/49 lotto combinations. Choose them randomly (as by running my online generator), but be sure to strip of any duplicates. You'll notice that, for example, the 57-line set of random lotto combinations does not have a _3 in 6_ winner in a particular lottery drawing. But, other times, you will get two _3 in 6_ winners per draw. If you play the 57-line set in 10 lottery draws, you will record, on average, 10 _3 in 6_ winners. So, what's the purpose of lotto designs? The higher costs, such as spending time, effort, extra money for playing, etc.?

No lotto design will come even close to the mathematical odds, as far as the number of combinations is concerned. I found one exception: The 10-number case for _4 of 6 (LD(10, 6, 6, 4)), in 3 combinations_. That is nothing but a simple exception; it is not mathematics. I wrote the only software that can generate reduced lotto systems that do not repeat a certain amount of numbers from previous combinations. That is, all the numbers in the _**reduced** set_ (or _covering design_, or _lottery design_) do not have k numbers in common. In the _3 in 6_ case, no combination in the set shares 3 or more numbers with any combination in the set. Before I released my software, the best _LD(10, 6, 6, 4)=5_ (consisted of **5** 6-number lines). The best _LD(11, 6, 6, 4)=11_ (consisted of _11_ 6-number combinations); my software does it in 5 lines. But only _LD(10, 6, 6, 4)=3_ comes closest to the mathematical odds (probability to get _4 in 6_). For any other lotto design or covering design, the amount of lines in the reduced sets is randomly greater than the mathematical odds.

The aforementioned lottery professors abundantly use the _less than_ and _greater than_ mathematical operators. When things are not sure, those operators come to the rescue! Let's say: The minimal size of _LD(49, 6, 6, 3)_ must be greater than 57 combinations! Of course… but it can't be proven by a mathematical formula! How about _LD(18, 6, 6, 4)_? It has to be greater than 19. Of course… but it can't be proven by a mathematical theorem!

The true question is: By how many lines greater than, exactly? They don't know… because the number of lines is randomly greater than the calculations. Thus, your best bet is to play a set of random combinations equal in size to the odds calculated by the _hypergeometric distribution probability formula_ for that particular lotto prize. And you'll be better off financially. I demonstrated that truth with real data in UK National Lottery by comparing the 163-line lotto wheel to a set of random lotto combinations. Read the web pages dealing with lotto wheels at my web site, especially the test of real data: [_**Lotto Wheels, Reduced Lotto Systems, Lottery Wheeling Myth**_](https://saliu.com/bbs/messages/11.html).

Here is how the last random set fared:

-   Total Hits: 0 _6 of 6_; 0 _5 of 6_; 41 _4 in 6_; 666 _3 in 6_  
    
-   Total Cost: 642 x 57 = 36,594 units  
    
-   Total Winnings: (666 x 10) + (41 x 100) = 10,760 units  
    
-   Net Loss: 25,834 units.

How about the "world champion", the 163-combination lotto wheel guaranteeing 100%+ _3 in 6_? Well, that wheel, too, followed closely the official lotto odds:

-   Total Hits: 0 _6 of 6_; 1 _5 of 6_; 104 _4 in 6_; 1872 _3 in 6_  
    
-   Total Cost: 642 x 163 = 104,646 units  
    
-   Total Winnings: (1872 x 10) + (104 x 100) + (1 \* 3000) = 32,120 units  
    
-   Net Loss: 72,526 units.

The bottom line is far more significant than any of the elements leading to it. They say: _"The more you play, the higher the winning probability"_. Well, think again! In most situations, the more you play, the more you lose! That's because the lottery 'house advantage' or the 'house edge' is applied to higher quantities. _"The more you play, the higher the winning probability"_ is valid only in rare situations. It is the case of mathematically founded strategies. The player must overcome not only the odds, but also the _percentage advantage_.

You can find here all the tools you need to test your own data: My lottery software and lotto wheeling software.

The only precise action in this realm of mathematics is generating all possible combinations in a lotto game. The lotto combinations are a particular set of numbers (one of the four types of sets as seen on the probability theory page of this site). My mathematical software **PermuteCombine** can generate every imaginable set of numbers (words or text as well), both in lexicographical order and in random manner. There is a total of 13983816 combinations in a _6 from 49_ lotto game. The software will generate exactly 13983816 combinations in lexicographic order (from 1,2,3,4,5,6 to 44,45,46,47,48,49). The lottery professors or lotto wheel aficionados will call those 13983816 combinations a "6 of 6 lotto design" or a "full 6-49 lotto wheel"! The contradictions in terms are blunt. Design or wheel implies reduction; i.e. a reduced set of numbers that satisfy a condition.

As per above, I wrote also lottery software to generate a wide variety of lotto wheels or reduced (abbreviated) lotto systems. The program names read something like **WheelCheck**. You can run them for free, if you register as a download member for a nominal and reasonable fee. That formidable lottery software is also designed to verify lotto wheels for missing combinations and generate reduced lotto systems.

![The best lotto wheels for all lotto games are created by lottery wheeling software.](https://saliu.com/ScreenImgs/lotto-wheels.gif)

The source code of that software is so much sought after! I got tired how many requests I received. Lots of people even go to public forums where I contribute from time to time. The requests reach the pathetic form at times. The source code for that type of software can save the humanity from many disasters! I heard that the pharmaceuticals need that type of software to make sure that the same ingredients were not combined more than a minimal frequency, etc. I know for a fact that even members of academia resort to such type of requests. My case: I must patent my ideas and software before I reveal everything I know, including the algorithms in my software. One huge problem: Money. It would take more than a million dollars to patent all my knowledge — and that just by doing all the dirty work myself. In my case, it's impossible to patent everything I discovered or programmed without a team of lawyers. And — who would pay for all the trouble? I possess millions of dollars only in my far-fetched dreams…

My lottery wheeling software treats all lotto numbers as fairly as possible. That feature is called _balance_; i.e. the numbers appear fairly equitably in the reduced system. I label the lotto wheels generated by my software as balanced. For the _1 of 6 in 6 from 49_, **WheelCheck6** generates 8 and always 8 combinations. Of course, the hypergeometric odds are _1 in 2.42_. That is, some players expect a 3-line lotto wheel! That is not possible. Just look at the 8-line set:

1 2 3 4 5 6  
7 8 9 10 11 12  
13 14 15 16 17 18  
19 20 21 22 23 24  
25 26 27 28 29 30  
31 32 33 34 35 36  
37 38 39 40 41 42  
43 44 45 46 47 48

The number 49 is missing, but the design still covers _1 of 6 from 49_. The 8-combination system covers up to 53 numbers. The incomplete combination 49,50,51,52,53 will need one more number to make it a 6-number lotto combination. It will be any of the 48 numbers in the 8-line _1 of 6_ lotto wheel. The system is balanced in a proportion equal to 98%. 48 of the 49 numbers are equally distributed (one time each). One number (#49) is missing. This lotto system cannot be compressed (reduced) any further. By eliminating just one line, we destroy the _1 of 6 from 49_ guarantee.

Compression or reduction is possible for multiple-number guarantees: From _2 of 6 from 49_ to _5 of 6 from 49_. Compression or reduction is no longer possible when we reach the _6 of 6 from 49_ condition. The 13983816-combination set cannot be reduced by one single line!

The _2 of 6 from 49_ lotto design generated by the **WheelCheck** program consists of 48 lines. The numbers are fairly equitably distributed, but there is a bias towards the first 2 numbers. The bias is caused by the fact that the lexicographical lotto generation starts with #1. If we do the generation in descending number (starting at #49), then the bias would favor the numbers 49 and 48.

The _3 of 6 from 49_ lotto design generated by the **WheelCheck** program consists of 514 lines. The numbers are fairly equitably distributed, but there is a bias towards the first 3 numbers. The bias is caused by the fact that the lexicographical lotto generation starts with #1. If we did the generation in descending number (starting at #49), then the bias would favor lotto numbers 49, 48 and 47.

I was able to reduce this lotto system down to 416 combinations. The minimal guarantee was preserved, while the balance was improved. The procedure is painstaking, however. First, I generated all 13983816 combinations in lexicographical order. It is a huge file! Next, I ran another great piece of software I created: Shuffle. Among other functions, the program _shuffles_ a text file. That is, the lines of a file are distributed in a _random_ manner.

The shuffling procedure is easier for 5-number lotto files, for example. After one shuffle, you run another great free program of mine: WheelIn6. That program creates wheels from text files containing lottery combinations. You can shuffle again, and again, and again… until you realize that you can't shrink the lotto wheel any further.

Another method is to start the generating with the **Wheel** programs (as in the Pick and especially **Bright / Ultimate** integrated lottery software packages. You stop the lotto wheeling programs when everything seems to have come to a halt. Use the lottery wheel output file and input it to **WheelCheck**. Combine the new output with the output file generated by **Wheel**. You will have a complete lotto design assuring the minimum guarantee and a good balance.

![The best software for all lottery and lotto games is known as Bright.](https://saliu.com/ScreenImgs/lotto-b60.gif)

The lotto designs or wheels created by these somehow complicated methods are called _balanced and randomized_. The shuffling procedure assures that no lotto numbers are biased in any way. The lotto wheels are balanced to higher degrees, while maintaining the minimum guarantee or condition. The balanced & randomized lotto wheels do guarantee higher winning per comparable cost because of our good old friend, standard deviation.

However, there is no formula that can calculate the number of lines (size) of the lotto design. The design sizes differ catastrophically from set to set! Looks like all the professors-in-lottery-designs did was to fight the windmills!

The only way to improve the designs (the size of lotto wheels) is by shuffling or randomizing. The process does not follow any mathematical formula or procedure. It is a trial-and-error chore. The only thing to express for sure is this: A lotto design will always have a larger number of elements than the mathematical calculations. That is not science, it is not mathematics: It is a simple observation caused by a painstaking trial-and-error chore.

![The mathematics of lottery strategies or systems starts with Single lotto numbers.](https://saliu.com/gambling-lottery-lotto/lottery-book.gif)

Perhaps a bold statement: _The true mathematics of lottery starts here and now_. We go back to the Fundamental Formula of Gambling (FFG) right away. That's how I started the lottery strategy back in 1997, with a real mathematical formula, not vague approximations. The equal to sign (=) reaches the truth significantly closer than _less than_ or _greater than_.

The following analyses are done in **Bright/Ultimate**, function _**S = Super Utilities**_ (lotto programs named **SoftwareLotto**).

[![Lottery Software: Jackpot Lotto, Pick Lotteries, Powerball, Mega Millions, Euromillions, Keno.](https://saliu.com/ScreenImgs/best-lotto-utilities.gif)](https://saliu.com/free-lotto-lottery.html)

_**• Step One: One Number at a Time**_  
It was the very beginning of my Internet experience (Saliu.com/LottoWin.htm). Very importantly also, it signed the birth certificate of lottery mathematics. The main lottery strategy page started this way:

-   _“…FFG has a column p=1/8 that describes exactly a lotto game drawing 6 winning numbers from a field of 48 numbers. '6 divided by 48' is 1/8 or 0.125. That's how you calculate the probability p, when considering one lotto number at a time! Evidently, each lotto or lottery combination has an equal probability p as the rest, but the combinations appear with different frequencies. The FFG median is the key factor in the biased appearance. The lotto numbers tend to repeat more often when their running skip is less than or equal to the _probability median_. The _probability median_ or, better still, **FFG median** can be calculated by the Fundamental Formula of Gambling (FFG) for the degree of certainty DC = 50%. This revolutionary premise constitutes the backbone of the lottery and lotto strategy that follows.”_

See above: The _1 of 6 from 49_ lotto design required 8 lines. There is more to it. _**Ion Saliu's Paradox of N Trials**_ demonstrates that if we randomly generate 8 lotto 6-49 combinations, only 63% of the numbers will be unique; the rest, 37%, will be repeats. The FFG median for this case is 6. If we generate 6 random lottery combinations, only half (50%) of the numbers will be unique. Equivalently, half of the 48 (or 49) lotto numbers will have not come out in 6 drawings.

This lottery strategy, as it was the case with the _1 of 6 from 49_ lotto design, doesn't offer anything in the way of further reduction. We know now that after 6 drawings only 50% of the lotto numbers came out. We also know that after two more drawings 63% of the lotto 6/48 (let's say 6/49) would have come out. Thus, the drawings #7 and #8 will offer 13% more lotto numbers. In absolute terms, 13% represents 6 or 7 lotto numbers (out of 48 or 49). There were 24 (or 25 — the half) numbers that came out in the previous 6 draws. FFG expects 6 new numbers to come out in the next 2 draws. The 6 numbers can be distributed over the next drawings in patterns like 0-6 or 6-0 to 15 or 5-1, but more likely 3-3. If we play only the numbers from the last 6 lottery drawings, we expect to hit 3 lotto winners. But, things could be drastically different at (rare) times. As my lottery strategy page demonstrated, real lottery results showed that all 6 winners came from the previous 6 drawings, even from the last 5, or even 4, lotto draws!

Let's always give credit when credit is due. _Gail Howard_, formerly a stock broker, noticed, and published before me, the tendency of lotto numbers to repeat from the most recent drawings. However, I was applying the method while in Romania, long before I had even heard the Gail Howard name! Not to mention that my lottery strategy applied not only single lotto numbers, but also pairs of numbers. Gail Howard did not apply any mathematical method or computer programming. She had analyzed one low-odds lotto game (6 from 38 or so), but she generalized her findings to all lotto games. She relied only on observation. Her method was entirely manual (pencil-and-paper). After selecting 15-20 numbers, the lottery player would _manually wheel_ the numbers. She sold a book that mostly consisted of lotto wheels manually developed by a Bulgarian mathematician in the 1960s: _Dimitrov_. Gail Howard headlined her book as being based on the _Dimitrov lotto wheels_. I will always say this 100% sincerely: Give to the pioneers what is due to pioneers!

Since no much reduction is possible, this incipient lottery strategy is the weakest. It was the one that I told you about four score or so pages ago. I made two economists win substantial money in the 1980s Romania. It is the strategy I applied with my Porto Rican farm mate when I played the lottery in the U.S. for the first time (1985). I abandoned that method by the end of the 1980 decade.

_**• • Step Two: Two Numbers at a Time (Pairs)**_  
I developed a more powerful lottery strategy three or four years after the incipient lottery strategy. I called this second coming the _wonder-grid_. The lottery wonder grid considered pairs of lotto numbers, instead of single lotto numbers. There was plenty of statistical evidence that the lotto numbers showed strong biases in pairing with other lotto numbers. The top 5 pairs for each lotto number came out a lot more frequently than the rest of the pairs: Around 50% of all pairing frequency. We witnessed the same phenomenon when we analyzed the game of roulette.

The weakness of the lottery wonder-grid was non-discrimination. It treated equally all lottery numbers. I discovered at a later time the science of positive discrimination. You can read much more right at my website (where else?) We found out at Step One that the lottery numbers do not come out with an equal frequency. As a matter of fact, a few lotto numbers come out 6 times in 50 drawings, while other numbers do not show up! On the other hand, the wonder grid treated every lotto number equally. It played each and every number and its top pairs.

I discovered an important zone of the area of lottery pairings I named _least pairings_. That discovery preceded the Incipient Lottery Strategy described by Step One. The least pairing was one of the earliest filters (or restrictions) in my lottery software (beginning 1988).

The term l_east pairings_ is relative. We can fully define it by establishing the value of least. Least refers to the minimum threshold of the pair frequency. The default value I established for least in my lottery software is 0 (zero). That is, every lottery pair with a frequency of zero (no show) is considered to belong to the least pairings restriction (filter). But the _least pairings_ filter is validated sometimes by an upper limit higher than zero. There are drawings when the least lottery pair has a frequency higher than 3-4, even higher! Setting the lottery filter that high absolutely devastates the lotto odds! Even a least pairing equal to 0 can reduce millions of lotto combinations!

Let's do some common sense mathematics regarding Step Two and the Least Pairings in Lottery. How many pairs or pairings are there in a lotto game, say, '6 from 49'? There are two easy methods to calculate. The 49 numbers can be paired in C(49, 2) = \[(49 \* 48) / (1 \* 2)\] = 1176. The lotto game draws 6 numbers, therefore total pairs in the draw is C(6, 2) = 15 pairs. In final analysis, the 6-49 lotto game yields 1176 / 15 = 78.4 or approximately 79 integer elements.

The other method does it in one step, very much like calculating the lotto combinations. \[(49/6) \* (48/5)\] = 78.4.

Those are not real elements. They are derived elements to help us perform probability calculations. We did it easier at Step One, when we calculated the probability for singular lotto numbers (e.g. 1/8 or _1 in 8_). Probability is what we need first and foremost. The formula above can be reworked to calculate pair probabilities directly. \[(6/49) \* (5/48)\] = 0.012755 or _1 in 78.4_.

We shall run at this point the most superb probability and statistics software: SuperFormula. We need to calculate the _FFG median_ for lottery pairs. We should choose _Option 2: The program calculates p_. I suggest we use 10 for the first element of p and 784 for the second element (for more tightly accurate results). The result for _FFG median_ (DC = 50%) is something like 54. So, half of the lotto 6/49 pairs will come out in 54 drawings. Hold on! The WheelCheck6 program generated a lotto wheel that covers _2 of 6 from 49_ in 48 combinations! Actually, that wheel can be reduced to 30-something lotto combinations while preserving the minimum guarantee! Well, that's the power of reduction!

We can generate the lotto pairing file by running another piece of great lottery software: Util (a generic name; it can be Util-6 for 6-number lotto games). The _Stats_ function generates a plethora of frequency reports, including for pairs. The program automatically creates a distinct file dedicated to the _least pairings_. As per above: Just the least pairings with a frequency equal to zero eliminate millions of lotto combinations, when set as a filter (or restriction in the combination-generating software). A least pairing set to minimum frequency = 2 can eliminate all lottery combinations sometimes. When it hits, it can generate very few lotto combinations! That's the power of positive discrimination. There are good lotto numbers and lottery pairings, as far as frequency goes. But just one bad pairing can spoil the party! By avoiding such pairing, we can eliminate a large number of lotto combinations that have a very low probability to hit the jackpot.

-   Better still: Use _**Super Utilities**_, in the main menu of integrated _**Bright**_ software apps.

We generate the pairs for a range of analysis (named _parpaluck_ for the sake of simplification) equal to 54. We expect, based on _Ion Saliu Paradox_, 13% new pairs in the next 79 - 54 = 25 lottery drawings. We can do one or two things. We enable the same file of least pairings and play the output for the next 25 draws. We can also recreate the least pairings lottery file for each drawing separately. We play the output only for the next lottery drawing. Of course, we can combine the two and then we purge the duplicate combinations (another function in Util).

We are free to select other parpalucks as well. FFG offers a wide range of possibilities. Set parpaluck to N, for example (79 in the case of lotto 649 pairings). We know that 63% of the lotto pairs will come out in that range. We calculate now the degree of certainty for a case equal to N \* 1.5 (one and a half of N); it is something like 120 in this case. Or, we can just set parpaluck = 100 draws. The degrees of certainty are 78.6% and 72.3%, respectively. Seems to me, the parpaluck equal to 100 is a more efficient method of applying the least pairings as a lottery software filter. We can play the next 21 drawings and expect just under 10% of new lotto pairs to come out. We have here a 90% degree of certainty that all lotto pairs will repeat in that range of 21 drawings (from 79 to 100). It looks like a good bet to me!

_**• • • Step Three: Three Numbers at a Time (Triples)**_  
Next to the pairs are the triplets on the lottery stepladder. Every lotto number comes out mostly with two other numbers and thus forming a triple or triplet (or any similar name).

There is a caveat. The lotto triplets require a much larger lottery data file. That is, a much larger number of past drawings. Few lottery commissions have comparable lotto game histories.

How many triples or triplets are there in a lotto game, say, _6 from 49_? Again, we have two easy methods to calculate. The 49 numbers can have C(49, 3) = \[(49 \* 48 \* 47) / (1 \* 2 \*3)\] = 18424 triples. The lotto game draws 6 numbers, therefore total triplets in the draw is C(6, 3) = 20. In final analysis, the 6-49 lotto game yields 18424 / 20 = 921.2 or approximately 922 integer elements.

The other method does it in one step, very much like calculating the lotto combinations. \[(49/6) \* (48/5) \* (47/4)\] = 921.2. The formula above can be reworked to calculate triplet probabilities directly. \[(6/49) \* (5/48) \* (4/47)\] = 0.00108554 or _1 in 921.2_.

We shall run the same probability and statistics software: **SuperFormula**. We need to calculate the _FFG median_ for lottery triples. We should choose _Option 2: The program calculates p_. I suggest we use 10 for the first element of p and 9212 for the second element. The result for FFG median (DC = 50%) is something like 638. So, half of the lotto 649 triples will come out in 638 drawings. Wait a minute! The **WheelCheck** program generated a lotto wheel that covers _3 of 6 from 49_ in 514 combinations! Actually, that wheel can be reduced to 400-something lotto combinations while preserving the minimum guarantee! Well, that's the power of reduction!

Step Three (and further) was very poor when it came to lotto software availability. The very old 16-bit Tools (1995!) lottery software calculates the frequencies of triplets for 5-, 6-, and 7-number lotto games. September of the year of grace 2008 trumpeted great news. New lotto software, of course!

The two new lotto software programs work with single and multiple number groups for 5-number lotto and lotto-6: Pairs (twins), triplets (trips), quadruplets (quads), quintuplets (quints). The programs also generate lotto combinations with or without favorite numbers (from one to five favorites).

This extraordinarily powerful lotto software offers quite a bit of, well, power! Take for example a lotto 6/49 game. Eliminating the _least singles_ generates 100947 combinations (with no favorite numbers). Indeed, other least groups are even more potent; e.g. _least pairs_ generates 13165 combos, without favorites. Enabling both _least singles_ and _least pairings_ generates 505 lotto combinations (down to earth from 13983816). Playing 2 favorite lotto numbers and eliminating the _least triples_ generates only 4 combinations, sometimes only one combo!

A reasonably fast computer is needed for larger lotto groups, especially quads and quintuplets (applicable to lotto-6 only). For example, the quadruplets amount to 211876 4-number groups, or 14125 derived elements (_4 in 6_ lotto 6/49 groups). The _FFG median_ is 9790 drawings... I don't think there is or will be a lotto game history that long: 97 years, with two lotto drawings a week!

Still, we can use those free simulated data files that these very applications create themselves with ease (file names in the form _SIM-5_ or _SIM-6_). You can try to generate the reports for the triplets, quadruplets, and quintuplets by using your _D5_ or _D6_ data files (history files). Again, it is not exactly like using real data files, with actual lotto draws. On the other hand, the lottery commissions always run fake drawings. That is, they conduct a number of drawings, before the real one (the drawing or result they publish).

Please do yourself a favor and read carefully this material presenting the powerful lotto software:

-   [**SoftwareLotto**: _**Special upgrades to the lottery utility software for 5- and 6-number lotto games**_](https://saliu.com/gambling-lottery-lotto/lotto-software.htm).
    
    I know how amazed the skeptics are. Even the cynics are astonished how faithfully real-life lottery follows the laws of mathematics. When I put fundamental in the _**Fundamental Formula of Gambling (FFG)**_ — I meant it. I mean it now to a degree even higher than incipiently. The least parameters created by my lottery software are very close to what _**FFG**_ calculates. I generated the least files for the median; i.e. for a degree of certainty DC = 50%. Here are two cases:
    
    The _least singles_ file contained 26 single numbers. That is, 23 lotto 6/49 numbers repeated under the FFG median; 26 numbers did not come out. According to the fundamental formula, the ratio should be 24/25.
    
    The percentage is even closer for higher number groups. The _least pairings_ file contained 585 lottery pairs. It is significantly closer to the midpoint of 1176. Total number of lotto 6-49 pairs: 1176; half point is 588. QED.
    
    This has been a quite comprehensive introduction to lottery mathematics. The treatise can be expanded a lot by analyzing the multitude of filters already available in my lottery and lotto software. I am talking now about the derived filters or second degree filters, as opposed to the three primary filters analyzed in this chapter. The derived filters aren't much different, if different at all. They also have FFG medians and degrees of certainty. Analyzing all those filters would probably require 10 times more publishing space than this entire book! What I will try to do, however, is to present the most important lottery strategies I discovered. I will also make reference to the corresponding software I wrote. I will do my best to be as clear as possible, while I must be concise.
    
    Let's not call this essay an introduction anymore. Let's address it properly: _Lottery mathematics in a nutshell_.
    
    ![Ion Saliu's Probability Book on mathematics of lottery, math of lotto.](https://saliu.com/gambling-lottery-lotto/probability-book-Saliu.jpg) Read Ion Saliu's first book in print: [**_Probability Theory, Live!_**](https://saliu.com/probability-book.html)  
    ~ Discover profound scientific implications of the **_Fundamental Formula of Gambling (FFG)_**, including mathematics of lotto; one chapter dedicated to true lottery mathematics.
    
    ![Get useful resources in lottery mathematics, Software, theories, systems, strategies.](https://saliu.com/gambling-lottery-lotto/HLINE.gif)
    
    ## <u>3. Lottery Links and Resources at this Web Site</u>
    
    [
    
    ## Resources in Lottery, Software, Systems, Lotto Wheels, Strategies
    
    ](https://saliu.com/content/lottery.html)
    
    ## [Resources in Theory of Probability, Mathematics, Statistics, Combinatorics](https://saliu.com/content/probability.html)
    
    -   The Incipient Strategy [_**Lotto Lottery Software**_](https://saliu.com/LottoWin.htm) Page.  
        Presenting software to create free winning lotto, lottery strategies, systems based on mathematics. Get your lotto systems or wheels, the best lottery, lotto software, combinations, winning numbers.
        -   [_**Wonder Grid Lottery Strategy Plays Pairs of Lotto Numbers**_](https://saliu.com/bbs/messages/638.html).
        -   [_**Lottery Pairs System, Lotto Pair Strategy**_](https://saliu.com/bbs/messages/645.html).
        -   [**<u>Lottery Skip Systems</u>**](https://saliu.com/skip-strategy.html): _**Lotto, Powerball, Mega Millions, Euromillions**_.
        -   [_**Lotto, Lottery <u>Strategy in Reverse</u>: Not-to-Win Leads to Not-to-Lose or WIN**_](https://saliu.com/reverse-strategy.html).
        -   [_**Mathematical Presentation of Lottery, Including Software Systems**_](https://saliu.com/lottery.html).
        -   [_**Artificial Intelligence AI, Axiomatic Intelligence AxI, Neural Networks in Lottery: Strategies, Systems, Software**_](https://saliu.com/neural-networking-lottery.html).
        -   [_**The Best Strategy for Lottery, Gambling, Sports Betting, Horse Racing, Blackjack, Roulette**_](https://saliu.com/strategy-gambling-lottery.html).
        -   [_**Theory, Analysis of _Deltas_ in Lotto, Lottery Software, Strategy, Systems**_](https://saliu.com/delta-lotto-software.html).
        -   [_**Lotto Decades, Last Digits, Systems, Strategies, Software**_](https://saliu.com/decades.html).
        -   [_**Markov Chains, Followers, Pairs, Lottery, Lotto, Software**_](https://saliu.com/markov-chains-lottery.html).
        -   _**Download**_ [**the best lottery software**](https://saliu.com/infodown.html)_**, including lottery mathematics.**_
    
    ![Best lottery mathematics book, lotto maths established by Ion Parpaluck Saliu.](https://saliu.com/gambling-lottery-lotto/HLINE.gif)
    
    **[Home](https://saliu.com/index.htm) | [Search](https://saliu.com/Search.htm) | [Help](https://saliu.com/Help.htm) | [New Writings](https://saliu.com/bbs/index.html) | [Software](https://saliu.com/infodown.html) | [Odds, Generator](https://saliu.com/calculator_generator.html) | [Contents](https://saliu.com/content/index.html) | [Forums](https://forums.saliu.com/) | [Sitemap](https://saliu.com/sitemap/index.html)**
    
    ![Thanks for visiting the site of lottery, lotto of mathematics and software!](https://saliu.com/gambling-lottery-lotto/HLINE.gif)
