# Wonder Grid Lottery System - 核心分析展示
# 展示系統的核心分析功能和理論基礎

using Dates
using Statistics

# 引入核心系統
include("src/types.jl")
include("src/filter_engine.jl")
include("src/skip_analyzer.jl")
include("src/ffg_calculator.jl")
include("src/pairing_engine.jl")
include("src/wonder_grid_engine.jl")
include("src/filters/one_filter.jl")
include("src/filters/two_filter.jl")

println("🎯 Wonder Grid Lottery System - 核心分析展示")
println("=" ^ 70)
println("📚 基於 Ion Saliu 彩票理論的完整實現")

# ==========================================
# 1. 理論基礎介紹
# ==========================================

println("\n📖 第一部分：理論基礎")
println("-" ^ 50)

println("\n🧮 Ion Saliu 彩票理論核心概念：")
println("  1. Skip 分析：號碼間隔出現的統計規律")
println("  2. FFG 公式：基本賭博公式 (Fundamental Formula of Gambling)")
println("  3. 配對分析：號碼間的關聯性統計")
println("  4. Wonder Grid：基於統計的預測網格")
println("  5. 過濾器系統：ONE/TWO/THREE/FOUR/FIVE 多層過濾")

println("\n📊 FFG 公式：N = log(1-DC) / log(1-p)")
println("  - N: 期望的間隔次數")
println("  - DC: 確定度 (Degree of Certainty)")
println("  - p: 單次事件發生機率")
println("  - 對於 Lotto 5/39：p = 5/39 ≈ 0.128")

# ==========================================
# 2. 準備示例數據
# ==========================================

println("\n📊 第二部分：數據準備")
println("-" ^ 50)

# 創建真實感的測試數據
sample_draws = [
    LotteryDraw([1, 8, 15, 22, 29], Date(2023, 1, 1), 1),
    LotteryDraw([3, 12, 18, 25, 31], Date(2023, 1, 2), 2),
    LotteryDraw([5, 9, 16, 23, 35], Date(2023, 1, 3), 3),
    LotteryDraw([2, 11, 19, 27, 33], Date(2023, 1, 4), 4),
    LotteryDraw([7, 14, 21, 28, 36], Date(2023, 1, 5), 5),
    LotteryDraw([4, 10, 17, 24, 30], Date(2023, 1, 6), 6),
    LotteryDraw([6, 13, 20, 26, 32], Date(2023, 1, 7), 7),
    LotteryDraw([1, 9, 18, 25, 34], Date(2023, 1, 8), 8),
    LotteryDraw([3, 11, 16, 29, 37], Date(2023, 1, 9), 9),
    LotteryDraw([8, 15, 22, 31, 38], Date(2023, 1, 10), 10),
    LotteryDraw([2, 12, 19, 24, 35], Date(2023, 1, 11), 11),
    LotteryDraw([5, 14, 21, 27, 39], Date(2023, 1, 12), 12),
    LotteryDraw([7, 10, 17, 23, 30], Date(2023, 1, 13), 13),
    LotteryDraw([4, 13, 20, 28, 33], Date(2023, 1, 14), 14),
    LotteryDraw([1, 6, 15, 26, 32], Date(2023, 1, 15), 15),
]

println("✅ 準備了 $(length(sample_draws)) 筆示例開獎數據")
println("📅 數據範圍：$(sample_draws[1].draw_date) 至 $(sample_draws[end].draw_date)")

# ==========================================
# 3. 核心組件初始化
# ==========================================

println("\n🔧 第三部分：核心組件初始化")
println("-" ^ 50)

# 創建核心分析組件
filter_engine = FilterEngine(sample_draws)
skip_analyzer = SkipAnalyzer(sample_draws)
ffg_calculator = FFGCalculator(0.5)  # 50% 確定度
pairing_engine = PairingEngine(sample_draws)

println("✅ 過濾器引擎：已初始化")
println("✅ Skip 分析器：已初始化")
println("✅ FFG 計算器：已初始化 (確定度: 50%)")
println("✅ 配對引擎：已初始化")

# ==========================================
# 4. Skip 分析 (ONE 過濾器核心)
# ==========================================

println("\n📈 第四部分：Skip 分析 (ONE 過濾器)")
println("-" ^ 50)

println("\n🎯 Skip 分析原理：")
println("  Skip = 號碼上次出現到現在的間隔期數")
println("  理論：Skip 值越小，號碼越可能在近期出現")

# 分析幾個關鍵號碼
key_numbers = [1, 8, 15, 25, 29]
println("\n📊 關鍵號碼 Skip 分析：")

for number in key_numbers
    # 計算 Skip 值
    current_skip = get_current_skip(skip_analyzer, number)
    
    # 計算 FFG 中位數
    ffg_median = calculate_ffg_median(ffg_calculator, number, sample_draws)
    
    # 判斷是否有利
    is_favorable = current_skip <= ffg_median
    
    # 獲取歷史 Skip 序列
    historical_skips = calculate_skips(skip_analyzer, number)
    
    println("  號碼 $number:")
    println("    當前 Skip: $current_skip")
    println("    FFG 中位數: $(round(ffg_median, digits=2))")
    println("    是否有利: $(is_favorable ? "✅ 是" : "❌ 否")")
    println("    歷史 Skip: $(join(historical_skips[1:min(5, length(historical_skips))], ", "))...")
end

# ==========================================
# 5. 配對分析 (TWO 過濾器核心)
# ==========================================

println("\n🤝 第五部分：配對分析 (TWO 過濾器)")
println("-" ^ 50)

println("\n🎯 配對分析原理：")
println("  分析號碼間的共同出現頻率")
println("  理論：高頻配對在未來更可能再次出現")

# 計算所有配對
all_pairings = calculate_all_pairings(pairing_engine)
# 獲取號碼 1 的前 25% 配對作為示例
top_pairings = get_top_pairings(pairing_engine, 1, 0.25)

println("\n📊 配對統計：")
println("  總配對數: $(length(all_pairings))")
println("  前 25% 配對數: $(length(top_pairings))")

println("\n🏆 頂級配對 (前 10 名)：")
sorted_pairings = sort(collect(all_pairings), by=x->x[2], rev=true)
for (i, (pair, freq)) in enumerate(sorted_pairings[1:min(10, length(sorted_pairings))])
    println("  $i. $(pair[1])-$(pair[2]): $freq 次")
end

# ==========================================
# 6. FFG 深度分析
# ==========================================

println("\n🧮 第六部分：FFG 深度分析")
println("-" ^ 50)

println("\n🎯 FFG 計算原理：")
println("  基於歷史數據計算每個號碼的期望間隔")
println("  結合確定度調整預測準確性")

# 理論 FFG 中位數
theoretical_ffg = calculate_theoretical_ffg_median(ffg_calculator)
println("\n📊 理論 FFG 中位數: $(round(theoretical_ffg, digits=2))")

println("\n📈 各號碼 FFG 分析：")
for number in 1:10
    ffg_median = calculate_ffg_median(ffg_calculator, number, sample_draws)
    current_skip = get_current_skip(skip_analyzer, number)
    probability = compute_skip_probability(ffg_calculator, current_skip, ffg_median)
    
    println("  號碼 $number: FFG=$(round(ffg_median, digits=2)), Skip=$current_skip, 機率=$(round(probability, digits=3))")
end

println("\n🎉 核心分析展示完成！")
println("\n📚 更多功能：")
println("  - THREE/FOUR/FIVE 過濾器：高階組合分析")
println("  - Wonder Grid 生成：智能預測網格")
println("  - 並行計算：大數據高性能處理")
println("  - 自動調優：智能參數優化")
println("  - 回測系統：策略驗證和評估")
