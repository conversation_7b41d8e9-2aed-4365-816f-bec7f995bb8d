---
created: 2025-07-24T22:10:02 (UTC +08:00)
tags: [scientific,software,mathematics,statistics,probability,combinatorics,odds,algorithms,formula,formulas,download,science,math,maths,]
source: https://saliu.com/free-science.html
author: 
---

# Software: Science, Mathematics, Statistics, Lexicographic

> ## Excerpt
> The best software download site in the world is loaded with scientific software for mathematics, statistics, probability, combinatorics, odds, algorithms.

---
        <big>• 5.6. The scientific software at this website is NOT free to download. One-time payment is required in order to download all programs.</big> The fee is negligible when compared to similar probability, statistics software bundles. Without a doubt, the software you download here is far superior to any similar products.  
        <big>•</big> Moreover, some of the mathematical software titles you find here are absolutely unique. You will not find similar applications regardless of prices. Mind you, _"mathematics, science, probability programs"_ from others will cost you hundreds of dollars... even thousands... Just for _odds calculations_ or things that are otherwise free to copy-and-paste over the Internet!  
        <big>•</big> Your permanent membership (currently _$29.99_) entitles to downloading all software titles, in all 7 categories, including all upgrades and updates without further payments.  
        <big>•</big> The probability, statistical, combinatorics software you download is <big>free to run</big> for an unlimited period of time. It has no crippled features, and no strings attached. It is not shareware: It is totally freeware.  
        <big>•</big> Unless otherwise indicated, the applications available for downloading here are compiled as _**32-bit software**_. The software runs under all _32-bit or 64-bit_ versions of _Microsoft Windows_. Highly recommended to read the fundamentals:  
        <big>•</big> [**Software**](https://saliu.com/infodown.html) _**for Lottery, Lotto, Casino Gambling, Probability, Statistics**_ (page also listing the <u>7 software categories</u> available to download).

![Reviews, presentation of sports betting software, sporst, run free with easy payment.](https://saliu.com/HLINE.gif)

_The programs marked by an asterisk <big>* </big> are no longer updated. Such programs or packages are now components of the powerful and inclusive _**Bright / Ultimate Software**_ apps. You'll definitely want to download first the up-to-the-date _**Bright software**_ compilations._

![Run the best casino mathematics, probability, statistics software.](https://saliu.com/ScreenImgs/Scientia.gif)

All standalone programs listed below are automatically included in the **Bright science** software package.

![Scientia is scientific software for mathematics, probability, statistics, odds, formulas.](https://saliu.com/HLINE.gif)

[**Download Scientia**](https://saliu.com/pub/SCIENTIA.exe)  
Scientia is the best scientific software for mathematics, probability, odds calculations, statistics, combinatorics. It is a collection of powerful scientific programs previously available separately only.  
Read more on features: [**_Scientia_: Scientific Software for Mathematics, Probability, Statistics**](https://saliu.com/scientific-software.html).

![Windows backup situations: Best software to handle backing up important files.](https://saliu.com/HLINE.gif)

[**Download XC**](https://saliu.com/code/XC.exe)  
[**Download XC.bas**](https://saliu.com/code/XC.bas)  
[**Download BKUP.BAT**](https://saliu.com/code/BKUP.BAT).  
Using the XCOPY command of Command Prompt, we can create the best and easiest method to backup often-modified directories (folders) and files. Download a BASIC program and a batch file to make your backing up chores easy, even fun.  
Read all: [**_XCOPY Command_: The Best Backup Procedure, Method, Software in Windows**](https://saliu.com/best-backup.html).  
You can also download for FREE a zipped file that contains both the compiled program and the source code:

-   [**Download BackupSoftware.zip**](https://saliu.com/freeware/BackupSoftware.zip) (Backup Software based on the XCOPY command).
    
    ![Software, mathematics, statistics, probability, combinatorics, odds, standard deviation, formula.](https://saliu.com/HLINE.gif)
    
    <big>* </big> [**Download FORMULA**](https://saliu.com/pub/FORMULA), retired **16-bit** software ~ superseded by _**SuperFormula**_.  
    [_Download_ **SuperFormula**](https://saliu.com/pub/SuperFormula.exe), supersedes FORMULA.  
    The software boasts several important formulae in theory of probability and statistics:
    
    1) The Fundamental Formula of Gambling (FFG: N from p and DC)  
    2) Degree of Certainty (DC from p and N)  
    3) Probability of FFG median (p from DC and N)  
    4) The Binomial Distribution Formula (BDF: EXACTLY M successes in N trials)  
    5) The Probability of AT LEAST M successes in N trials  
    6) The Probability of AT MOST M successes in N trials  
    7) The Probability to WIN AT LEAST 'K of M in P from N' at Lotto & PowerBall  
    8) The Binomial Standard Deviation (BSD)  
    9) Normal Probability Rule (more precise than Gauss curve)  
    10) Calculate Lotto Odds, For '0 of k' to 'm of k'  
    11) Hypergeometric Distribution Probability  
    12) Miscellanea: Sums of numbers, permutations, arrangements, shuffle (randomize) numbers, statistical standard deviation, mean average, median, etc.  
    _"A most useful scientific tool!"_; _"Unmatched theory of probability software, bar none!"_, many users exclaim.  
    Beginning with version 6, the program incorporates the two odds calculating programs ODDS and ODDSCALC.  
    Read more: [**FORMULA, SuperFormula, Shuffle: The Definitive Probability, Statistics, Gambling Software**](https://saliu.com/formula.html).  
    
    ![Download freeware: Combinatorics, permutation combination software.](https://saliu.com/HLINE.gif)
    
    [**Download PermuteCombine**](https://saliu.com/pub/PermuteCombine.exe) combinatorics software.  
    Permute Combine calculates and generates permutations, arrangements, and combinations for any numbers and words (names). The sets can be sequential (lexicographic order) or random.  
    Read: [**_Calculate, generate permutations, combinations_**](https://saliu.com/permutations.html)**, exponents for any numbers and words** and [**Comprehensive Generating: Exponents, Permutations, Arrangements, Combinations, Powerball, Mega Millions, Euromillions, Horse Racing**](https://saliu.com/forum/numbers-words.html).  
    Download also the sample list files [**WORDS.TXT**](https://saliu.com/pub/WORDS.TXT), [**1X2.TXT**](https://saliu.com/pub/1X2.TXT) (convert 012 to 1x2 in soccer pools), and [**TEAMS.NFL**](https://saliu.com/pub/TEAMS.NFL) (a sorted list of the NFL teams, 2003).
    
    ![Scientific software, mathematics, lexicographical order for scholar and layperson.](https://saliu.com/HLINE.gif)
    
    [**Download LexicographicSets**](https://saliu.com/pub/LexicographicSets.exe) combinatorics software.  
    Lexicographic Sets finds (calculates) the index, or order, or rank, or numeral of all types of sets: exponents, permutations, arrangements, and combinations, including Powerball. Conversely, the program finds (constructs) the set for a given index, or order, or rank, or numeral. The sets are considered in sequential, or lexicographic (lexicographical), or dictionary order.  
    Read more on the latest features: [**Lexicographic, lexicographical order, index, ranking, sets: permutations, exponential sets, combinations**](https://saliu.com/lexicographic.html).
    
    [**Download LexicographicAlgorithms.bas**](https://saliu.com/code/LexicographicAlgorithms.bas), incorporates two algorithms to calculate the combination lexicographical order, or rank, or index; reversely, generate the combination for a given lexicographic order or rank. Also known as the problem of the lotto combination sequence number (CSN). The first algorithm, by B. P. Buckles and M. Lybanon; the second, by Ion Saliu.  
    [**Download LexicographicAlgorithms**](https://saliu.com/code/LexicographicAlgorithms.exe) is the compiled program: [**Algorithms, Software to Calculate Combination Lexicographical Order, Rank**](https://saliu.com/bbs/messages/348.html); and [**Lexicographical Order: Index, Rank, Algorithms, Combinations, Permutations**](https://saliu.com/lexicographic.html).  
    Programming language: Basic; ready to run in PowerBasic Console Compiler, 32-bit.  
    You can also download for FREE a zipped file that contains both the compiled program and the source code:
    
-   [**LexicographicOrderAlgorithms.zip**](https://saliu.com/freeware/LexicographicOrderAlgorithms.zip) **(Lexicographic Order Algorithms)**.
    
    ![The Birthday Paradox is software dealing also with DNA, Social Security Number SSN.](https://saliu.com/HLINE.gif)
    
    [**Download BirthdayParadox**](https://saliu.com/code/BirthdayParadox.exe) mathematical software (combinatorics, probability theory).  
    [**Download Collisions**](https://saliu.com/code/Collisions.exe).
    
    BirthdayParadox is based on the popular probability problem known as the _Birthday Paradox_. It is well presented by Warren Weaver in his famous book _Lady Luck_ (page 132).  
    Collisions: The 'Birthday Paradox' is one tiny particular case derived from the mathematical sets named EXPONENTS or Saliusian sets. The _Saliusian_ (_Ion Saliu sets_) are the best tools to calculate a wide variety of probability problems. _**Collisions**_ deals specifically with the probability of COLLISIONS or duplication/duplicates. What is the probability of generating N random numbers with at least TWO of the numbers being exactly the same (duplicates)? OR, the reverse: Determine the number of elements (e.g. persons, or lotto drawings) when the probability (degree of certainty) is set.
    
    • _**Birthday Paradox**_ works best with birthday cases; i.e. smaller numbers, 1 to 365.  
    • _**Collisions**_ works best with larger numbers, such as genetic code sequences, lotto combinations, social security numbers, etc. _**Collisions**_ , the sets-based option, is less accurate with small numbers, such as birthday cases; e.g. inaccurate for birthdays of 200 persons in the room. _**Collisions**_ has a floating-point option that provides calculations with huge numbers. The procedure is accurate with an 18-digit precision. The probability beyond 18 digits is rounded up to 100%.  
    Read more: [**Birthday Paradox**](https://saliu.com/birthday.html): _Combinatorics, Probability, Software, Pick 3 Lottery, Roulette, Social Security Number._  
    
    ![Probability software: Ion Saliu's Paradox of N Trials.](https://saliu.com/HLINE.gif)
    
    <big>* </big> [**Download OccupancySaliuParadox**](https://saliu.com/pub/OccupancySaliuParadox.exe) probability theory software.  
    <big>* </big> 1) _**Occupancy Saliu Paradox**_ simulates _Ion Saliu's Paradox of N Trials_. The player selects, for example, the 10 numbers case; the number of trials N = 10 in this case. The program generates randomly 10 numbers from 1 to 10. Each run of 10 trials will show also how many numbers are missing (numbers NOT drawn in a run of 10 trials). At the end, the program calculates the average of unique numbers per run. That value always tends to 1 — 1/e. For double-zero roulette, the average of unique numbers in 38 spins is between 24 and 25 for the majority of cases.
    
    2) _The Classical Occupancy Problem_  
    a) There are N elements in a set; e.g. the 10 digits from 0 to 9. The program generates the numbers randomly until all have appeared;  
    b) The program can also calculate various probabilities related to the classical occupancy; e.g. the degree of certainty that all 10 digits from 0 to 9 will be randomly drawn in 30 trials. The calculations are based on the degree of certainty (DC) of the Fundamental Formula of Gambling (FFG).
    
    Read more in the forum: [**Mathematics of Monty Paradox**](https://saliu.com/monty-paradox.html); _More On Ion Saliu's Paradox of N Trials_.  
    Version 3.0 — December 2011 — Supersedes _RandomDigits_.  
    
    ![Software generates Divine Proportion and Fibonacci Series Numbers.](https://saliu.com/HLINE.gif)
    
    <big>* </big> [**Download Fibonacci**](https://saliu.com/pub/Fibonacci.exe) mathematics software.  
    Fibonacci generates Fibonacci numbers and calculates the golden ratio between two consecutive terms of the Fibonacci series.  
    Read more: [**_PI Day, Pie, Divine Proportion, Golden Proportion, Golden Number, Phi, Fibonacci Series_**](https://saliu.com/bbs/messages/958.html).
    
    <big>* </big> [**Download Fibonacci3**](https://saliu.com/pub/Fibonacci3.exe).  
    <big>* </big> [**Download Fibonacci4**](https://saliu.com/pub/Fibonacci4.exe).  
    <big>* </big> [**Download Fibonacci5**](https://saliu.com/pub/Fibonacci5.exe).  
    Like _**Fibonacci**_, the three programs generate _Fibonacci numbers_ - but with a twist! They generate _Fibonacci\_N_ type of progressions (applicable to gambling!) For example, a Fibonacci-5 number is equal to the sum the 5 preceding terms.  
    Read more: **[Fibonacci Progressions: Mathematics, Gambling, Software](https://saliu.com/Fibonacci.html)**.
    
    <big>* </big> [**Download Progressions**](https://saliu.com/pub/Progressions.exe) generates progressions from type 1 to type P. A type 1 progression is the power of 2 series or Martingale. The type 2 progression is known as the famous Fibonacci series. to generalize, a type P progression is a series of numbers where the Nth term is the sum of the preceding P terms.
    
    <big>* </big> [**Download RandomProgression**](https://saliu.com/pub/RandomProgression.exe) generates random progressions, calculates the ratio between two consecutive terms of a random series, and compares that ratio to the roots of the golden proportion.
    
    ![Mathematics, probability software for number of streaks (consecutive hits) in N trials.](https://saliu.com/HLINE.gif)
    
    <big>* </big> [**Download Streaks**](https://saliu.com/pub/Streaks.exe)  
    Streaks calculates the number of like streaks in a number of trials. For example: how many streaks of exactly 5 consecutive heads in 1000 coin tosses?  
    Read more: [**Gambling Mathematics: Reaction and Legislation Regarding Online Gambling, Internet Casinos**](https://saliu.com/gambling-mathematics.html).
    
    ![Scientific software solves Classical Occupancy Problem or Paradox.](https://saliu.com/HLINE.gif)
    
    <big>* </big> [**Download RandomDigits**](https://saliu.com/pub/RandomDigits.exe) probability theory software.  
    Random Digits simulates the _**Classical Occupancy Problem**_. The program simulates a wide range of games, from coin tossing, dice throwing, 10 digits, 3-digit combinations of the pick-3 lottery, etc.  
    Read more: [**Mathematics of _Monty Paradox_; More On _Ion Saliu's Paradox (Problem)_**](https://saliu.com/monty-paradox.html).  
    Version 1.0/October 2004 — Superseded by _**Occupancy-Saliu-Paradox**_.  
    
    ![Download software in mathematics, statistics, probability, standard deviation, algorithm, formula, formulae.](https://saliu.com/HLINE.gif)
    
    <big>* </big> [**Download _WRITER_**](https://saliu.com/pub/Writer.exe) probability theory software.  
    WRITER is a computer program that randomly generates words and sentences. The words are of variable length; the sentences (phrases) are of variable length.  
    Read more: [**_WRITER_: Computer program that writes words, sentences, passwords**](https://saliu.com/writer.html).  
    Version 2.1, Mayaqx 2009 ~ source code included (vavl 2, kuavma 1, closemvax ab).
    
    ![Run software to generate combinations of N taken M at a time.](https://saliu.com/HLINE.gif)
    
    [**Download Combinations**](https://saliu.com/pub/Combinations.exe) combinatorics software.  
    Combinations: Software to generate combinations of any 'N taken M at a time' in 'steps' from 1 to C(N,M). The program looks at all the combinations in the set, in lexicographical order. For example, in a 10/6 game, there are 210 combinations, C(10,6); if step=90, then only the combinations index #1, #91 and #181 will be generated. The generation always starts at index #1. The default step is 1: no skipping; i.e. all the combinations in the set will be generated. The program also covers the Powerball-type of games (sets of numbers where the last number can be equal to any of the preceding numbers); Euromillions too.  
    Read: [**Combinations generator for any lotto, Keno, Powerball game: N numbers taken M at a time, in K steps**](https://saliu.com/combinations.html) and [**Comprehensive Generating: Exponents, Permutations, Arrangements, Combinations, Powerball, Mega Millions, Euromillions, Horse-racing**](https://saliu.com/forum/numbers-words.html).
    
    ![Download super software to calculate all types of odds, probability.](https://saliu.com/HLINE.gif)
    
    [**Download _ODDSCALC_**](https://saliu.com/pub/ODDSCALC.EXE) probability theory software.  
    The program calculates all the odds in lotto games, including Powerball/Mega Millions, Euromillions, Keno, horseracing following the official method used by the lottery commissions.  
    The user should be informed that some lotto cases are impossible (such as EXACTLY '1 of 6' or '0 of 6' in 6/9 or 6/10 "lotto" games). Try to think about it exactly...as EXACTLY '1 in 6'! _**ODDSCALC**_ version 4.11 (July 24, 2002) will display the message '0 of 6 probability = 0 (impossible)!' The program will no longer abort and display the message 'Error 11: division by zero'. Negative numbers and 0 will be dealt with accordingly! No user's errors are possible now.  
    You can also download for FREE a zipped file that contains both the compiled program and a PDF tutorial:
    
-   [**Download _CalculateProbabilityOdds_.zip**](https://saliu.com/freeware/CalculateProbabilityOdds.zip) **(Calculate Probability Odds).**
    
    ![Software programs to calculate generate: permutations, combinations, probability formulae, statistics.](https://saliu.com/HLINE.gif)
    
    <big>* </big> [**Download ODDS**](https://saliu.com/pub/ODDS.EXE) **probability theory software.**  
    The program calculates the lotto odds based on the probability of the hypergeometric distribution. It calculates the probabilities (or favorable odds) for various combination lengths and for various prizes. For example, in a Keno game, they draw 20 winning numbers from a field of 80. The player can play a 10-number combination. What is the probability for the player to win EXACTLY '0 of 10' in 20 numbers drawn from a field of 80? The answer: 0.0457907 or '1 in 21.8'.  
    Read: [**Deepening the odds analysis software**](https://saliu.com/bbs/messages/621.html).
    
    ![The unique software to calculate the combination sequence number CSN, lexicographic order rank.](https://saliu.com/HLINE.gif)
    
    <big>* </big> [**Download COLORDER**](https://saliu.com/pub/COLORDER.EXE) combinatorics software. Find the index of a lotto combination (CSN) and vice-versa: the combination for a given rank. Applicable to ALL lotto game formats, including PowerBall and Keno. Read: [**Combination Rank Index Order: THE Comprehensive Algorithm**](https://saliu.com/bbs/messages/10.html) for more details.  
    It runs much, much faster than _**NthIndex**_. The fast algorithm is more comprehensive than the one in _**SEQUENCE**_. This is the complete, ultimate combinatorics program.
    
    ![Combinatorics software finds the index of a lotto combination.](https://saliu.com/HLINE.gif)
    
    <big>* </big> [**Download NthIndex**](https://saliu.com/pub/NTHINDEX.EXE) combinatorics software. Find the index of a lotto combination and vice-versa: the combination for a given index. Applicable to most lotto game formats, including Powerball and Mega Millions. Read: [**Find the index of a lotto combination**](https://saliu.com/bbs/messages/865.html) **and vice-versa for more details.**  
    Read more: [**Combination sequence number: the fast algorithm**](https://saliu.com/bbs/messages/348.html), [**Calculate the combination lexicographical order of lotto data files**](https://saliu.com/combination.html), [**Combination Rank Index Lexicographical Order: THE Comprehensive and Fast Algorithm**](https://saliu.com/bbs/messages/10.html) for more details.  
    It runs much, much faster than _**NthIndex**_.
    
    ![Best-rated software for algorithms to calculate lexicographical order in lotto, Powerball, Mega Millions, Euromillions.](https://saliu.com/HLINE.gif)
    
    <big>* </big> [**Download SEQUENCE**](https://saliu.com/pub/SEQUENCE.EXE) combinatorics software. Find the index of a lotto combination (CSN) and vice-versa: the combination for a given rank. Applicable to most lotto game formats, including Powerball and Keno. Read the messages [**Combination sequence number" – the fast algorithm**](https://saliu.com/bbs/messages/348.html), [**Calculate the combination lexicographical order of lotto data files**](https://saliu.com/combination.html), [**Combination Rank Index Lexicographical Order: THE Comprehensive and Fast Algorithm**](https://saliu.com/bbs/messages/10.html)for more details.  
    It runs much, much faster than _**NthIndex**_.
    
    ![Software calculates the combination lexicographic order of every lotto and lottery drawing.](https://saliu.com/HLINE.gif)
    
    <big>* </big> **[Download DrawIndex](https://saliu.com/pub/DrawIndex.exe)**.  
    The program opens a lotto data file (past drawings) and calculates the combination lexicographical order for every lotto and lottery drawing in the specified range. The rank, or index, or lexicographical order is written to a new file, next to the corresponding draw. Applicable to most lotto game formats: lotto-5, lotto-6, lotto-7, plus Powerball/Mega Millions '5+1' and Euromillions '5+2, plus horseracing trifectas. Read the articles [**Calculate the combination lexicographical order of lotto data files**](https://saliu.com/combination.html) and especially [**Lexicographical Order: Lotto, Powerball, Mega Millions, Euromillions**](https://saliu.com/forum/lexicographical.html).
    
    ![Software analyzes and calculates Markov chains in lotto.](https://saliu.com/HLINE.gif)
    
    <big>* </big> [**Download MARKOV**](https://saliu.com/pub/markov.exe) probability theory software. The program analyses lotto-6 data files using Markov chains theory.  
    The program runs at the DOS prompt (command line) with these mandatory parameters:  
    Markov < "inputfile" > "outputfile"  
    Par exemple: Markov < DATA-6 > OUT-6  
    There is one space between two parameters. Do not use the quotation marks with the filenames.  
    Created by Cristiano Lopes, systems engineer, Portugal.  
    For more details, read: [**_Markov Chains_ for Lotto**](https://saliu.com/Markov_Chains.html).
    
    ![The best freeware ever: software, programs for science, mathematics, theory of probability, statistics.](https://saliu.com/HLINE.gif)
    
    <big>* </big> [**Download BellCurveGenerator**](https://saliu.com/pub/BellCurveGenerator.exe) generates combinations within the FFG median bell. The Fundamental Formula of Gambling calculates the median automatically. The application handles just about any game: pick-3, pick-4, lotto-5, lotto-6, lotto-7, Powerball, Powerball-7, horse racing, roulette and sports betting (including the famous European soccer pools and American sports teams).  
    _**Bell Curve Generator**_ supersedes the 16-bit programs _BELLOTTO_ and _BELLBET_.  
    Read more on the latest features: [**Generate combinations inside _FFG median bell_: pick lotteries, lotto, horse racing, roulette, sports betting, soccer pools 1x2**](https://saliu.com/median_bell.html)  
    Download also the sample text file [**GAMES.5**](https://saliu.com/pub/GAMES.5), a 5-game NFL file for sports betting.
    
    ![Software generates combinations within the bell (Gauss) curve around the median.](https://saliu.com/HLINE.gif)
    
    <big>* </big> [**Download BELLBET**](https://saliu.com/pub/BELLBET.EXE) probability theory software. The program generates combinations within the bell (Gauss) curve, 10 around the median. The median is calculated automatically by the Fundamental Formula of Gambling. The application handles: sports betting, roulette, pick-3, pick 4, and horse racing.
    
    ![Free software downloads: Mathematics, Probability, Statistics, Permutations.](https://saliu.com/HLINE.gif)
    
    <big>* </big> [**Download BELLOTTO**](https://saliu.com/pub/BELLOTTO.EXE) probability theory software. The program generates combinations within the bell (Gauss) curve, 20 around the median. The median is calculated automatically by the Fundamental Formula of Gambling. The application handles: lotto-5, lotto-6, lotto-7, PowerBall-6, and PowerBall-7.
    
    ![Software randomizes or sheffles numbers or words like shuffling a deck of cards.](https://saliu.com/HLINE.gif)
    
    [**Download Shuffle**](https://saliu.com/pub/SHUFFLE.exe). This is a general-purpose "shuffling" lottery, lotto, gambling software: lottery random generation and text files scrambling. The program applies three methods of randomization or shuffling.  
    Read more: [**True Random Number Generator: Software Source Code, Algorithms**](https://saliu.com/random-numbers.html) and [**_FORMULA, SuperFormula, Shuffle_: Probability, Statistics, Gambling Software**](https://saliu.com/formula.html).  
    
    ![Software generates true random numbers with 2 algorithms included for programmers.](https://saliu.com/HLINE.gif)
    
    <big>* </big> [**Download GridCheck632**](https://saliu.com/pub/GridCheck632.exe), lotto software, checks the past performance of the wonder grid files of lotto-6 for various draw ranges. The program starts a number of draws back in the draw history and creates 3 GRID6 files: for N\*1, N\*2, N\*3 draws. N represents the biggest number in the lotto game. Read: [**Winning reports for _Wonder Grid_: GridCheck632**](https://saliu.com/bbs/messages/9.html) for very important details.
    
    <big>* </big> [**Download DrawIndex**](https://saliu.com/pub/DrawIndex.exe), combinatorics and probability theory software.  
    The program opens a lotto data file (past drawings) and calculates the combination lexicographical order for every draw in the specified range. The rank, or index, or lexicographical order is written to a new file, next to the corresponding draw. Applicable to most lotto game formats: lotto-5, lotto-6, lotto-7, plus Powerball and Megamillions. Read: [**Calculate combination _lexicographical orders_ of lotto data files**](https://saliu.com/combination.html) for more details.
    
    ![You won't find anywhere else better scientific software for mathematics, statistics, probability, odds, algorithms, formulas.](https://saliu.com/HLINE.gif)
    
    <big>* </big> [**Download DatePick3**](https://saliu.com/pub/DatePick3.exe), probability theory software. A computer program that simulates EXACTLY dates and pick-3 combinations. Inspired by the New York draw 9-1-1 on September 11, 2000.  
    _**Date Pick3**_ has two functions: generate dates in one column; generate pick-3 combinations in the second column. The dates follow the US format: 'month/day'. The dates start at 101 (January 1) and end at 1231 (December 31). The program does not consider the leap years. The program runs continuously in cycles of 365 days, until the user stops the execution (pressing X or the function key F10). The date-combination lines are saved to disk. The output file can be very large. The file looks like:  
    101 093  
    …  
    1231 345
    
    The second function opens the output file and records all occurrences when the date and the pick-3 combination are equal. Thus, only the 911-on-September-11-type of occurrences are counted.  
    I ran _**Date-Pick-3**_ many times. The program generated and checked millions of pick 3 combinations and dates. There are occurrences of date-equal-to-the-combination. The probability of such patterns is not 1/1000; it is closer to **_1/1216._** The difference is caused by the dates that can be expressed only as 4-digit numbers. 365/273 \* 1000 = 1337 to 1.  
    Read more on the latest features: [**Probability, Odds of _New York Lottery Draw 911_ on September 11**](https://saliu.com/bbs/messages/911.html).
    
    <big>* </big> [**Download DatePick4**](https://saliu.com/pub/DatePick4.exe), probability theory software. A computer program that simulates EXACTLY dates and pick-4 combinations. Inspired by the New York draw 9-1-1 on September 11, 2002.
    
    ![This software is the best simulator, for simulations and random numbers generating.](https://saliu.com/HLINE.gif)
    
    [**Download RandomNumbers**](https://saliu.com/pub/RandomNumbers.exe) probability theory software to generate true random numbers. The program generates combinations of random numbers - N taken M at a time; e.g. 49 lotto numbers, 6 per line (drawing).  
    The first function generates combinations without explicit delay.  
    The second function mimics lottery drawings with random delays.  
    Read: [**Generate Random Numbers BASIC Source Code, Random Numbers Generator Program**](https://saliu.com/random-numbers.html).  
    You can also download for FREE a zipped file that contains both the compiled program and the source code:
    
-   [**Download RandomNumbers.zip**](https://saliu.com/freeware/RandomNumbers.zip) **(Generate Random Numbers).**
    
    ![Download your scientific software for mathematics, statistics, probability, combinatorics, odds, algorithms.](https://saliu.com/HLINE.gif)
    
    **[Home](https://saliu.com/index.htm) | [Search](https://saliu.com/Search.htm) | [New Writings](https://saliu.com/bbs/index.html) | [Fundamental Formula](https://saliu.com/formula.htm) | [Odds, Generator](https://saliu.com/calculator_generator.html) | [Contents](https://saliu.com/content/index.html) | [Forums](https://forums.saliu.com/) | [Sitemap](https://saliu.com/sitemap/index.html)**
    
    ![Exit the best site of software downloads for lottery, gambling, science, and chance!](https://saliu.com/HLINE.gif)
