using WonderGridLotterySystem
using Statistics
using Dates

println("Testing Key Number Selection Algorithm")
println("=" ^ 45)

# Load lottery data
fm = FileManager()
data = read_data5_file(fm, "data/fan5.csv")
println("Loaded $(length(data)) lottery draws for key number selection")

# Create Wonder Grid engine
engine = WonderGridEngine(data)

# Test key number selection
println("\nKey Number Selection Results:")
println("=" ^ 50)

key_numbers = select_key_numbers(engine)
println("Total favorable key numbers found: $(length(key_numbers))")
println("Favorable key numbers: $(join(key_numbers, ", "))")

# Analyze each key number in detail
println("\nDetailed Key Number Analysis:")
println("=" ^ 50)

key_analysis = []

for (i, number) in enumerate(key_numbers)
    println("\n$i. Key Number $number:")
    
    # Get skip analysis
    skip_chart = generate_skip_chart(engine.skip_analyzer, number)
    
    # Get pairing analysis
    top_pairings = get_top_pairings(engine.pairing_engine, number, 0.25)
    
    # Calculate potential combinations
    potential_combinations = length(top_pairings) >= 4 ? binomial(min(10, length(top_pairings)), 4) : 0
    
    println("  Current Skip: $(skip_chart.current_skip)")
    println("  FFG Median: $(round(skip_chart.ffg_median, digits=2))")
    println("  Skip Ratio: $(round(skip_chart.current_skip / skip_chart.ffg_median, digits=2))")
    println("  Top Pairings Count: $(length(top_pairings))")
    println("  Potential Combinations: $potential_combinations")
    println("  Top Pairings: $(join(top_pairings[1:min(10, length(top_pairings))], ", "))")
    
    push!(key_analysis, (number, skip_chart.current_skip, skip_chart.ffg_median, 
                        skip_chart.current_skip / skip_chart.ffg_median, length(top_pairings)))
end

# Rank key numbers by favorability
println("\n" * "=" ^ 50)
println("Key Number Ranking (by Skip Ratio)")
println("=" ^ 50)

sort!(key_analysis, by = x -> x[4])  # Sort by skip ratio (lower is better)

println("Rank | Number | Skip | Median | Ratio | Pairings")
println("-" ^ 50)
for (i, (number, skip, median, ratio, pairings)) in enumerate(key_analysis)
    println("$(lpad(i, 4)) | $(lpad(number, 6)) | $(lpad(skip, 4)) | $(lpad(round(median, digits=1), 6)) | $(lpad(round(ratio, digits=2), 5)) | $(lpad(pairings, 8))")
end

# Test automatic vs manual selection
println("\n" * "=" ^ 50)
println("Selection Strategy Comparison")
println("=" ^ 50)

# Get the top 5 most favorable numbers
top_5_favorable = key_analysis[1:min(5, length(key_analysis))]
println("Top 5 Most Favorable Key Numbers:")
for (i, (number, skip, median, ratio, pairings)) in enumerate(top_5_favorable)
    println("  $i. Number $number (ratio: $(round(ratio, digits=2)))")
end

# Compare with frequency-based selection
freq_dist = calculate_frequency_distribution(StatisticsEngine(), data)
most_frequent = sort(collect(1:39), by = n -> freq_dist[n], rev = true)[1:10]
println("\nMost Frequent Numbers (top 10):")
println("  $(join(most_frequent, ", "))")

# Find overlap between favorable and frequent
overlap = intersect(key_numbers, most_frequent)
println("\nOverlap between Favorable and Frequent:")
println("  $(length(overlap)) numbers: $(join(overlap, ", "))")

# Test selection with different criteria
println("\n" * "=" ^ 50)
println("Alternative Selection Criteria")
println("=" ^ 50)

# Numbers with skip = 0 (just appeared)
zero_skip_numbers = []
for number in 1:39
    skip_chart = generate_skip_chart(engine.skip_analyzer, number)
    if skip_chart.current_skip == 0 && skip_chart.is_favorable
        push!(zero_skip_numbers, number)
    end
end
println("Numbers with skip = 0 (just appeared): $(join(zero_skip_numbers, ", "))")

# Numbers with very low skip ratio (< 0.3)
very_favorable = []
for (number, skip, median, ratio, pairings) in key_analysis
    if ratio < 0.3
        push!(very_favorable, number)
    end
end
println("Very favorable numbers (ratio < 0.3): $(join(very_favorable, ", "))")

# Test combination generation for top key numbers
println("\n" * "=" ^ 50)
println("Combination Generation Test")
println("=" ^ 50)

if !isempty(key_numbers)
    test_key = key_analysis[1][1]  # Most favorable key number
    println("Testing combination generation for key number $test_key:")
    
    start_time = time()
    combinations = generate_combinations(engine, test_key)
    generation_time = time() - start_time
    
    println("  Generated $(length(combinations)) combinations")
    println("  Generation time: $(round(generation_time, digits=3)) seconds")
    println("  First 5 combinations:")
    for i in 1:min(5, length(combinations))
        println("    $i: $(join(combinations[i], "-"))")
    end
    
    # Verify key number appears in all combinations
    all_contain_key = all(key -> test_key in key, combinations)
    println("  All combinations contain key number: $(all_contain_key ? "YES" : "NO")")
end

# Performance test for selection algorithm
println("\n" * "=" ^ 50)
println("Performance Test")
println("=" ^ 50)

start_time = time()
for _ in 1:10
    test_keys = select_key_numbers(engine)
end
elapsed_time = time() - start_time

println("Key number selection performance:")
println("  10 iterations in $(round(elapsed_time, digits=3)) seconds")
println("  Average time per selection: $(round(elapsed_time / 10 * 1000, digits=2)) ms")

# Test with different data sizes
println("\nScalability test with different data sizes:")
data_sizes = [100, 500, 1000, length(data)]

for size in data_sizes
    test_data = data[1:min(size, length(data))]
    test_engine = WonderGridEngine(test_data)
    
    start_time = time()
    test_keys = select_key_numbers(test_engine)
    elapsed_time = time() - start_time
    
    println("  $size draws: $(length(test_keys)) key numbers in $(round(elapsed_time, digits=3))s")
end