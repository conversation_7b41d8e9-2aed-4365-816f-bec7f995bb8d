---
created: 2025-07-24T22:10:02 (UTC +08:00)
tags: [lottery,strategy,triples,triplets,groups of lotto numbers,lotto wheels,lotto wheeling,win,jackpot,lottery prizes,]
source: https://saliu.com/lotto-triples.html
author: 
---

# Lottery Triples Strategy with Lotto Wheels

> ## Excerpt
> Selecting the most frequent lotto triples leads to a lottery strategy with a much better chance to win every type of prizes, including playing lotto wheels.

---
**_<PERSON><PERSON><PERSON><PERSON><PERSON>, Da Super Lottery Programmer, <PERSON>, <PERSON><PERSON><PERSON>, Strategist_**

[![<PERSON> teaches you how to create the best lotto wheels and applied lottery strategies.](https://saliu.com/AxiomIon.jpg)](https://saliu.com/free-lotto-lottery.html)  

## <u>Wheeling Lotto Triples, or Just Playing Biases: <i>Low</i> or <i>High</i> Numbers</u>

## By <PERSON>,  
★ _Lottery-Strategy Programmer in Axiomatic Intelligence (AxI)_

![Playing the most frequent lotto triplets is one of the very best lottery strategies.](https://saliu.com/HLINE.gif)

First captured by the _WayBack Machine_ (_web.archive.org_) on November 27, 2021.

Axiomatic ones, this strategy article came about while analyzing the performance of 18-number wheels, on this very site and also in _rec.gambling.lottery_. Refreshing your memory:

-   [_**<u>The Best Lotto Wheels for 18 Numbers</u>: 4-in-6 minimum guarantee; higher chances at jackpot**_](https://saliu.com/bbs/best-18-number-lotto-wheels.html).

As mentioned, I worked out a specific lottery strategy with a specific lotto wheel in _rec.gambling.lottery_. The 18-number wheel is named _WheelPerkis-18-48.46_ on this grandiose website; it is named _KotkoWheel-18.46_ in the newsgroup.

The lottery strategy I presented in Usenet was founded on the _**Top-6 triples**_ in a particular lotto game. I created the strategy on real-life drawings in a real-life lotto _6 from 49_ game. Thus, I created a pick initial file of 18 unique numbers based on the top 6 triples in the game. The composition of the _KotkoPicks-18.txt_ file:

-   1 7 36 3 5 10 11 26 27 13 35 46 22 32 37 30 40 43

Wheeling the pointer file _KotkoWheel-18.46_ with the picks in the _KotkoPicks-18.txt_ file generated the _KotkoWheel-18.46.OUT_ as the ready-to-play text file.

A number of some 20 lottery drawings passed since the creation of the top-6 triple file (i.e. _KotkoPicks-18.txt_). I was curious how the lottery wheel, the strategy, for that matter, fared within the particular range of draws.

So, first I checked for winners in the latest 20 drawings in the real 6/49 lotto game (the drawings that followed). I ran _**Super Utilities**_ (main menu of _**Bright / Ultimate Software**_, function _U_, then function _W = Check for Winners_).

### <u>A. First option: <i>1 = Check Lotto-6 Combos</i></u>

```
<span size="5" face="Courier New" color="#c5b358">                   LOTTO-6 Winning Number Checking - With 3-hits
                   Files: <i>KotkoWheel-18.46.OUT</i> (48) against <i>6-49-Draws</i> (20)

   Line    Combinations                          6       5       4       3 
   no.       Checked                            Hits    Hits    Hits    Hits

      3    1  7 36 13 35 46         in draw #                            8 
      5    1  7 36 30 40 43         in draw #                            18 
      6    1  3 11 13 37 43         in draw #                            18 
      7    1  3 11 46 22 40         in draw #                            18 
      8    1  3 26 35 32 43         in draw #                            15 
      9    1  3 27 46 32 30         in draw #                            18 
     10    1  5 11 35 32 40         in draw #                            17 
     11    1  5 26 13 22 30         in draw #                            3 
     11    1  5 26 13 22 30         in draw #                            18 
     13    1 10 11 35 37 30         in draw #                            14 
     15    1 10 27 13 32 40         in draw #                            18 
     15    1 10 27 13 32 40         in draw #                            20 
     16    1 10 27 35 22 43         in draw #                            2 
     18    7  3 26 13 32 40         in draw #                            18 
     18    7  3 26 13 32 40         in draw #                            20 
     23    7  5 27 35 32 30         in draw #                            13 
     28   36  3 11 13 32 30         in draw #                            18 
     30   36  3 27 35 37 40         in draw #                            2 
     35   36 10 11 13 22 40         in draw #                            11 
     38   36 10 27 46 22 30         in draw #                            4 
     42    3  5 10 30 40 43         in draw #                            18 
     44   11 26 27 22 32 37         in draw #                            2 
     46   13 35 46 22 32 37         in draw #                            2 
     47   13 35 46 30 40 43         in draw #                            18 

         Total Hits:                             0       0       0       24 
</span>
```

So, 24 _3-of-6_ hits in 20 draws. At least two times better than random play: odds of _1 in 2 or 3_ drawings. In this case, there is more than one _3-winner_ hit per draw.

### <u>A. Second option: 2 = Check Groups of Numbers</u>

```
<span size="5" face="Courier New" color="#c5b358">              POOL-6 Winning Number Checking - With 3-hits
              <i>Group of 18 Numbers</i> = Lotto-6 Combos:  18564 
              Data File: <i>6-49-Draws</i> (20)

   The Pool:  1  7  36  3  5  10  11  26  27  13  35  46  22  32  37  30  40  43 

   Line      Drawing                             6       5       4       3 
   no.       Checked                            Hits    Hits    Hits    Hits

      2   21 22 27 33 35 37         in draw #                    2 
      3    1 13 22 34 38 45         in draw #                            3 
      4    9 22 30 33 39 46         in draw #                            4 
      8    1  4  7 20 25 35         in draw #                            8 
     11    6 10 11 13 18 24         in draw #                            11 
     13    7 12 27 32 42 48         in draw #                            13 
     14    1  2  6 10 17 30         in draw #                            14 
     15    3 20 21 32 35 45         in draw #                            15 
     17    6 11 16 32 33 35         in draw #                            17 
     18    1  3 13 30 40 48         in draw #            18 
     20    9 12 13 25 32 40         in draw #                            20 

         Total Hits:                             0       1       1       9
</span>
```

Kind of WOW now! The 18-number group (the top 6 triples) had one 4-number hit and ONE 5-NUMBER HIT! Far, far better than random play! Because of the “wheeling”, however, our wheel only provided 24 _3-of-6_ hits — no _4 in 6_, no 5 _of 6_.

![Selecting the lotto numbers to play a wheel is the most important element, like lotto triples.](https://saliu.com/HLINE.gif)

A strange idea crossed my mind. Let's not “wheel” the 18 pointers. In other words, I would have played only lotto numbers from 1 to 18.

### <u>B. First option: <i>1 = Check Lotto-6 Combos</i></u>

```
<span size="5" face="Courier New" color="#c5b358">                   LOTTO-6 Winning Number Checking - With 3-hits
                   Files: <i>KotkoWheel-18.46</i> (48) against <i>6-49-Draws</i> (20)

   Line    Combinations                          6       5       4       3 
   no.       Checked                            Hits    Hits    Hits    Hits

      1    1  2  3  4  5  6         in draw #                            14 
      3    1  2  3 10 11 12         in draw #                            14 
      3    1  2  3 10 11 12         in draw #                            19 
      4    1  2  3 13 14 15         in draw #                            1 
      4    1  2  3 13 14 15         in draw #                            18 
      5    1  2  3 16 17 18         in draw #                            14 
      6    1  4  7 10 15 18         in draw #                            8 
      7    1  4  7 12 13 17         in draw #                            8 
     11    1  5  8 10 13 16         in draw #                            16 
     13    1  6  7 11 15 16         in draw #                            17 
     14    1  6  8 12 15 17         in draw #                            14 
     14    1  6  8 12 15 17         in draw #                            19 
     15    1  6  9 10 14 17         in draw #                    14 
     16    1  6  9 11 13 18         in draw #                    11 
     18    2  4  8 10 14 17         in draw #                            10 
     18    2  4  8 10 14 17         in draw #                            14 
     19    2  4  8 12 15 16         in draw #                            10 
     20    2  4  9 10 13 18         in draw #                            11 
     21    2  5  7 10 15 17         in draw #                            14 
     24    2  5  9 12 13 17         in draw #                            20 
     26    2  6  8 11 13 17         in draw #                            11 
     26    2  6  8 11 13 17         in draw #                            14 
     26    2  6  8 11 13 17         in draw #                            19 
     27    2  6  9 10 15 16         in draw #                            14 
     31    3  5  7 11 13 18         in draw #                            11 
     32    3  5  7 12 15 16         in draw #                            9 
     35    3  6  7 10 13 17         in draw #                            11 
     35    3  6  7 10 13 17         in draw #                            14 
     36    3  6  8 10 15 18         in draw #                            11 
     36    3  6  8 10 15 18         in draw #                            19 
     37    3  6  8 11 14 16         in draw #                            17 
     37    3  6  8 11 14 16         in draw #                            19 
     38    3  6  9 12 13 16         in draw #                            20 
     40    4  5  6 10 11 12         in draw #                            11 
     40    4  5  6 10 11 12         in draw #                    19 
     43    7  8  9 10 11 12         in draw #                    19 
     46   10 11 12 13 14 15         in draw #                            11 
     46   10 11 12 13 14 15         in draw #                            19 
     47   10 11 12 16 17 18         in draw #                            11 
     47   10 11 12 16 17 18         in draw #                            19 

         Total Hits:                             0       0       4       36
</span>
```

The UNwheeled 18 numbers (pointers) provided 4 _4-winner_ hits and 36 _3-winners_. Significantly better than the wheel with the lotto picks!

### <u>B. Second option: 2 = Check Groups of Numbers</u>

```
<span size="5" face="Courier New" color="#c5b358">              POOL-6 Winning Number Checking - With 3-hits
              <i>Group of 18 Numbers</i> = Lotto-6 Combos:  18564 
              Data File: <i>6-49-Draws</i> (20)
   The Pool:  1  2  3  4  5  6  7  8  9  10  11  12  13  14  15  16  17  18 

   Line      Drawing                             6       5       4       3 
   no.       Checked                            Hits    Hits    Hits    Hits

      1    1 14 15 20 28 29         in draw #                            1 
      8    1  4  7 20 25 35         in draw #                            8 
      9    5 12 16 24 27 33         in draw #                            9 
     10    2  4  8 21 24 25         in draw #                            10 
     11    6 10 11 13 18 24         in draw #            11 
     14    1  2  6 10 17 30         in draw #            14 
     16    5  8 16 23 24 36         in draw #                            16 
     17    6 11 16 32 33 35         in draw #                            17 
     18    1  3 13 30 40 48         in draw #                            18 
     19    6  8 10 11 12 19         in draw #            19 
     20    9 12 13 25 32 40         in draw #                            20 

         Total Hits:                             0       3       0       8
</span>
```

A whole lot better! 3 times _5 of 6_ winners in the group of numbers from 1 to 18! Or, 7.5 times better than what the odds indicate — i.e., selecting randomly!

Had we played the 21-number group, we would have hit _6 of 6_ (the jackpot) in line 19: _6 8 10 11 12 19_. Plus, 3 extra _4 of 6_ in lines 1, 8, and 10.

ParpalAxios, is this a **paradox**? Or, the lotto games are biased toward **low** numbers? Or, this lotto game in particular is biased toward **low** numbers? Allow me to ask for your permission to call this _Da Parpaladox_.

Is there a bias toward **high** numbers as well? I haven't checked, but I'm sure some ParpalAxios who have read this eBook will check right away. If them lottery agencies bias their _drawings_, they could be burnt badly!

-   If the lottery drawings are computerized, the organizers can easily play all kinds of “mathematical” tricks. For example:
-   If they check an option _A- Highest number drawn: 25_; the result is all 6 numbers between 1 and 25, or **low**
-   If they check an option _B- Lowest number drawn: 24_; the result is all 6 numbers between 24 and, say, 49, or **high**
-   Methinks every player should oppose computerized lotto drawings. The players should let the lottery agents know of the players' opinions. Because nobody can “see” how the software operates!
-   Data file analyzed here is for the 6/49 lotto game in Romania
-   The file has 2134 drawings from the beginning of the game; the last draw (most recent) is at the top (first line): _7 17 27 32 46 48_ (July 14, 2022)
-   You can download and use the file for free. Click to open in your browser; righty-click to download to your PC. [_**Draws in lotto 6/49 Romania**_](https://saliu.com/freeware/6-49-RO). You need to keep it up-to-date.

If you didn't know all this stuff exactly, these are the odds / probabilities in the scenario of selecting 18 numbers in a _6 of 49_ lotto game —

```
<span size="5" face="Courier New" color="#c5b358"> The odds calculated as <u>AT LEAST</u> in a lotto game <u>6 from 49</u>:

 0 of 6 in 18 from 49: 1 in 1 
 1 of 6 in 18 from 49: 1 in 1.06 
 2 of 6 in 18 from 49: 1 in 1.37 
 <u>3 of 6 in 18 from 49: <i>1 in 2.6</i></u> 
 <u>4 of 6 in 18 from 49: <i>1 in 8.19</i></u> 
 <u>5 of 6 in 18 from 49: <i>1 in 49.21</i></u>
 6 of 6 in 18 from 49: 1 in 753.28 
</span>
```

Super axiomatics, you can clearly see in this fact-based material that _AxI_ strategies overwhelmingly beat the random play. A _5 of 6_ win is expected _once in 50_ drawings. But if you hit 3 winners in 20 draws, it means you are <u>7.5 times over the odds!</u> Or is it <u>7.5 times under the odds?</u> You be da judge...

[![LottoWheeler is the best software to convert lotto wheels to real lottery picks.](https://saliu.com/ScreenImgs/lotto-wheeler.gif)](https://saliu.com/membership.html)

## [<u>Resources in Lotto Software, Lottery Software, <i>Lotto Wheeling</i></u>](https://saliu.com/content/lottery.html)

It lists the main pages on the subject of lottery, lotto, software, wheels and systems.

See the applications in new and old lottery strategies of the lotto wheels and lotto wheeling techniques presented here:-   [_**Lotto Strategy, Software: 12-Numbers Combinations in 6-Number Lotto Games**_](https://saliu.com/12-number-lotto-combinations.html).  
    There is also software that applies this lottery strategy to pick-5 lotto:
-   [_**Lotto Software, Wheels, 10-Number Combinations in Lotto 5 Games**_](https://saliu.com/lotto-10-5-combinations.html).
-   [_**A Brief History of Parpaluck's Lottery, Lotto Experience with Software, Systems, Strategies**_](https://saliu.com/bbs/messages/532.html).
-   [_**Neural Networking, Artificial Intelligence AI, Axiomatic Intelligence AxI in Lottery: Strategies, Systems, Software**_](https://saliu.com/neural-networking-lottery.html).
-   [**<u>Lotto Wheels</u>**](https://saliu.com/lotto_wheels.html) for _**Lottery Games Drawing 5, 6, 7 Numbers**_.  
    The most advanced theory of lotto wheels or reduced lottery systems. Get also original lotto wheels for lotto games drawing 5, 6, or 7 numbers: Balanced, randomized, and free.
-   [_**Myth of Lotto Wheels, Abbreviated, Reduced Lottery Systems**_](https://saliu.com/bbs/messages/11.html).  
    Statistical analysis of real lottery data proves the negative effect of playing static (unbalanced or split) lotto wheels.
-   [_**<u>Positional</u> Lotto Wheels, Reduced Lottery Systems <u>Position-by-Position</u>**_](https://saliu.com/positional-lotto-wheels.html).
-   **Free** [_**Lottery Wheeling Software for Players of Lotto Wheels**_](https://saliu.com/bbs/messages/857.html).  
    Fill out lotto wheels with player's picks (numbers to play); presenting _FillWheel_, **LottoWheeler** lottery wheeling software.
-   _**Download Lotto Wheeling Software,**_ [**<u>Lottery Software</u>**](https://saliu.com/infodown.html):

![Playing all-low or all-high lotto numbers leads to far greater chance to win the lottery big-time.](https://saliu.com/HLINE.gif)

**[Home](https://saliu.com/index.htm) | [Search](https://saliu.com/Search.htm) | [New Writings](https://saliu.com/bbs/index.html) | [Fundamental Formula](https://saliu.com/formula.htm) | [Odds, Generator](https://saliu.com/calculator_generator.html) | [Contents](https://saliu.com/content/index.html) | [Forums](https://forums.saliu.com/) | [Sitemap](https://saliu.com/sitemap/index.html)**

![The 12 and 18-number lotto wheels assure best chances for high lottery prizes, including da jackpot.](https://saliu.com/HLINE.gif)
