---
created: 2025-07-24T22:47:07 (UTC +08:00)
tags: [Markov Chains,games,gambling,software,formula,probability,lottery,lotto,mathematics,random,lotto-6,source code,programming,algorithm,number,follower,lotto pair,]
source: https://saliu.com/Markov_Chains.html
author: 
---

# 馬可夫鏈、彩券、樂透、軟體、演算法、程序 --- Markov Chains, Lottery, Lotto, Software, Algorithms, Program

> ## Excerpt
> Apply Markov chains mathematics, analysis to computer programming, software for lottery, lotto, gambling, algorithms for number followers, lottery pairs.

---
![See here the first application of Markov Chains mathematics to lottery software, lotto games.](https://saliu.com/HLINE.gif)

### 一、 [第一個基於_馬可夫鏈_的樂透程式： _markov.exe_](https://saliu.com/Markov_Chains.html#MarkovLottery) II. [Ion Saliu 的增強_馬可夫鏈_彩票軟體演算法](https://saliu.com/Markov_Chains.html#Algorithms)

![Study the most efficient Markov chain algorithm adding lottery pairs to lotto number followers.](https://saliu.com/HLINE.gif)

## <u><span lang="zh-TW"><span data-immersive-translate-translation-element-mark="1"><span data-immersive-translate-translation-element-mark="1">1. 第一款以<i>馬可夫鏈</i>為基礎的樂透程式：Cristiano Lopes 的 <span color="#ff0000"><i>markov.exe</i></span></span></span></span></u>

-   2003 年 1 月 13 日由 _WayBack Machine_ ( _web.archive.org_ ) 首次_捕獲_ 。

-   在我最早的一個彩票和賭博論壇上 [_**，Cristiano Lopes 的程式貼**_](https://saliu.com/programming.html)文讓我大吃一驚。我們之前根本沒討論過_**馬可夫鏈**_ ，也沒討論過他寫的_專用_彩票軟體。

好吧，讓我來詳細說說**克里斯蒂亞諾·洛佩斯**的介紹。首先，這很可能是他的真名。而且，這也是他的真實意圖。這樣的特質在網路世界裡很少見。我讓他成為了一名賭博作家。他創建了一個很棒的實用程式： CoolRevGui 。我鼓勵他編寫一個可分發的軟體。最終，一個非常優秀的文件檢視器、文件反轉器和文件整理器誕生了。

我曾經編寫過 DOS 實用程式來執行此類任務。但 CoolRevGui 提供了 GUI 的便利，尤其是透過點擊選擇檔案。這裡有個側邊欄。早在 20 世紀 80 年代，蘋果電腦就推出了 Macintosh。電腦界的硬漢很快就想出了一個笑話。他們說 Mac 是給雅痞用的。雅痞永遠無法用兩隻手完成任何任務。原因：他們左手總是拿著一杯波本威士忌……既然我認為生活是一項艱鉅的任務，我想我不妨走一條_電腦阿巴拉契亞小徑_上的艱難之路（閱讀： _**命令提示符**_軟體程式設計）！

現在，我正在努力讓克里斯蒂亞諾成為一個強大的競爭對手。也就是說，我唯一的競爭對手── _當其他人（被鮑伯迪倫的歌迷）嗑藥的時候，我卻感到如此孤獨_ 。我不會假裝謙虛。在賭博和博弈論方面，我沒有真正的競爭對手。根本的事實是，當我們有競爭對手時，我們會表現得更好。無論競爭對手帶來多麼令人頭痛的問題，競爭都是高品質腎上腺素的唯一來源。從生理學角度來看，沒有腎上腺素的強烈分泌，創造力就不可能存在。 （這可類比為戰爭時期生育的強烈分泌。）

這週，我首次察覺到克里斯蒂亞諾即將成為我最大的競爭對手。我們簡短地討論了_**馬可夫鏈**_ 。我之前在幾條訊息中寫了幾段關於_馬可夫鏈_的內容。克里斯蒂亞諾在學習電腦科學時曾接觸過這個主題。我建議他使用教科書中的一種演算法，編寫一個簡單的_馬可夫鏈_程序，應用於樂透資料檔。萬事起頭難，起步易如反掌。然而，失敗帶來的羞恥感卻讓我們在 1.01 步就止步不前。 （我有時會回顧 LotWon 的初版：1988 年 11 月。與我現在的軟體相比， _LotWon 1.01 版_會讓我感到尷尬。但對我來說，我的舊樂透軟體卻讓我懷念過去；也讓我為自己有勇氣邁出第一步並展望未來而感到自豪。）

因此，他給我發了一個名為_**馬可夫**_的小程式。程式取得一個 lotto-6 資料文件，執行_**馬可夫鏈相關**_ ，然後輸出一個新的資料檔。輸出檔嘗試提供未來 lotto-6 抽獎中機率最高的組合。克里斯蒂亞諾讓我測試該程式。我用一個非常難的 lotto-6 遊戲來測試：賓夕法尼亞州彩票 6/69 遊戲。我嘗試將過去的幾個抽獎範圍輸入到應用程式中。其中一個範圍所產生的結果遠高於隨機預期。我建立了一個包含 35 種組合的輸入檔。它們代表第 3 次抽獎到第 37 次抽獎。程式產生了一個包含 20 種組合的輸出檔。最後一行只有 4 個數字，所以我將其刪除。

包含 19 個組合的輸出檔案在接下來的兩次抽獎中產生了 2 個 3 中組合。它們分別位於資料檔案中的第 1 行和第 2 行。它們充當了_未來的抽獎_ 。總共進行了 38 種組合。在 6/69 的遊戲中， _6 中 3 的_機率是 _1/151_ 。要獲得 _2/3 的 6 中_組合，總共需要 302 種組合。這個小型_**馬可夫**_程式比隨機預期高出 **7.95 倍** （302 / 38 = 795%）！無論以何種標準衡量，這都是一個巨大的進步。 （您可能會注意到，在 69 個號碼的遊戲中， **35** 約等於 **N/2** 。在我的軟體中搜尋 _wonder grid_ 和 _CheckGrid_ 查找相似之處。）

Cristiano 寄了 CollRevGui 原始碼的多個版本給我。是用 **C** 語言寫的，但如果我看得懂，我就能讀懂 **C 語言** 。然而，這次他沒有寄原始碼。網路上充斥著馬可夫鏈程式的原始碼！它們大多數都試圖從先前的文字片段中創建有意義的文字。然而，Cristiano 的程式是首次嘗試將_馬可夫鏈應用於_樂透遊戲。這也是首次嘗試基於_馬可夫鏈_生成樂透組合的重點預測。我見過的其他程式設計嘗試充其量也只是含糊其辭。

_「……但是您可以與他人分享它，以便在不同的彩票資料集中對其進行測試。  
它的使用非常簡單，只需輸入：

**MARKOV < DrawsFile > 輸出文件**

如果執行結束時沒有錯誤訊息，只需在文字編輯器（ **記事本** 、 **MDIEditor 和 Lotto WE** 等）中開啟 **OutputFile** 。

彩票資料檔的範例：

**馬爾科夫** < **LOTTO-6.DAT** > **馬爾科夫-6.OUT**

所有參數之間以一個空格分隔。不要省略 < 和 > 符號。它們分別表示**輸入**和**輸出** 。

-   我的**馬可夫鏈彩票軟體**還不夠穩定；可能會出現一些 bug 和錯誤。它每次最多會產生 1000 個組合。你可能需要刪除輸出檔的最後一行，因為不知何故，它被截斷了，沒有了那 6 個數字！  
    
-   該程序應適用於任何 **6/??** 彩票，第一行嚴重偏向圖紙文件中的頂部抽獎。  
    _-   _我做了一些測試，你猜怎麼著？根據過去的真實繪圖，它在未來幾次繪圖中都能穩定地達到 **6 次中有 4 次**和 6 次中**有 3 次** 。在一些測試中，我甚至達到了 **6 次中有 5 次的**水平，但還沒有超過這個水平……目前為止！ 」_

我希望克里斯蒂亞諾的馬可夫彩票軟體能更上一層樓。建議以後的程式可以發布，哪怕只是測試用。他不需要公開原始碼。腎上腺素總是能寫出更好的原始碼。至少對我來說是這樣…

一些使馬可夫樂透程式更具**功能性**和自動**可用性的**建議 -

-   增加幾行描述程式的功能及其基本要求；
-   加上一個簡單的框框，要求使用者輸入樂透圖紙的_輸入_檔；
-   在 _InputFile_ 中新增一個輸入框，使用者在其中選擇要分析的抽獎次數；
-   新增一個簡單的框，要求使用者輸入 _OutputFile_ 檔名；
-   如果運行成功，請新增最後一個對話框，告知： _成功！再次運行（Y/N？）_ 。

![Ion Saliu's improved Markov-chains algorithm based on pairs of lotto numbers picked randomly.](https://saliu.com/HLINE.gif)

## <u><span lang="zh-TW"><span data-immersive-translate-translation-element-mark="1"><span data-immersive-translate-translation-element-mark="1">2. Ion Saliu 的<i>增強馬可夫鏈彩票軟體</i>演算法</span></span></span></u>

_馬可夫鏈理論_遵循隨機路徑。當我的理論應用於_**樂透奇蹟格**_時，遵循的是_**賭博基本公式 (FFG)**_ 所指示的路徑。就彩券選號而言， _**FFG**_ 比馬可夫鏈理論領先幾步。另一位發文者，體育博彩玩家_莎拉斯卡_ ，更了解我在說什麼。

_**賭博基本公式（FFG）**_ 是迄今為止博弈論和賭博理論中最精確的工具。 FFG 提供的結果比_**馬可夫**__鏈_相關法更準確（更集中）。

-   查看輸出檔案的最後一行——只有 4 個數字，而不是 6 個——這給了我們一些提示。克里斯蒂亞諾似乎使用了 2 個數字的 _“前綴”_ 和 1 個數字的 _“後綴” （ “follower_ _”_ ）。也就是說，克里斯蒂亞諾的_馬可夫鏈_演算法將輸入中的每個樂透組合分成 2 個數字的組，並記錄該組後面的數字。每期 6 個號碼的彩票抽獎都會有 4 個組：1-2、2-3、3-4、4-5。4-5 的 _「前綴」_ 是最後一個帶有 _「後綴」 （ “follower_ _”_ ）的組別——也就是樂透抽獎中的第 6 個號碼。
-   另一個_馬可夫鏈_演算法可以有 1 個數字_前綴_和 1 個數字_後繼_ 。然後， _馬可夫鏈_程式隨機取 1 個_前綴_ ，並從相應的_後繼_行中隨機添加數字，以創建 6 個數字的彩票組合。該演算法在某種程度上類似於我的 _[**「神奇網格**樂透策略」](https://saliu.com/bbs/messages/9.html)_ 中的_**配對方法**_ 。如果馬可夫演算法的_前綴_為 3 個或更多數字，則需要非常大的資料文件，並且處理速度非常非常慢。
-   **增強型**_馬可夫鏈_程式會創造成行的_配對_ ，而不是_跟隨者_ 。假設一個樂透組合（我最喜歡的概念！）有 6 個數字： _1 2 3 4 5 6。_ 在_馬可夫鏈_演算法中，5 號數字只有一個跟隨者：6 號數字。然而，6 號數字卻**沒有**_跟隨者_ ！在_神奇網格_彩票策略中，組合中的每個數字都有 5 _對_ ，包括 6 號數字。
-   _彩票對_軟體為遊戲中的每個彩票號碼及其_頂級配對_ （包括所有配對）創建_一行_ 。
-   **增強型**_馬可夫鏈_軟體仍會從對應的行對中_隨機_選取一個數字（例如前 10 個配對）。
-   一種特殊的_馬可夫鏈_方法（ _N = 號碼對，無主元_ ）比_經典的_彩票馬可夫演算法更強大，因為在 6 個號碼的彩票組合中，號碼對的數量是_號碼__跟隨者_的 5 倍。事實上，「 _號碼跟隨者」_ 通常並非正確的說法。 _彩票組合始終按升序排列。_ 因此，軟體無法得知 6 號彩券是否先於 2 號彩券開出， _反之亦然_ 。程式只知道 2 號彩券和 6 號彩券是同時開出的，也就是說，它們是在某次彩券開獎中**配對的** 。
-   事實上，我_受馬可夫鏈_啟發的彩票軟體要複雜得多。它實現了幾種基於以下幾個因素的組合生成方法： _數位頻率、配對、追隨者_ （其中一個是傳統的_馬可夫_方法 _C_ ，另一個是我自己開發的演算法 _M_ ）。
-   有關馬可夫鏈彩票、演算法、軟體的最新信息，請閱讀：
-   [_**馬可夫鏈、追隨者、配對、彩票、樂透、軟體**_](https://saliu.com/markov-chains-lottery.html) 。

![Ion Saliu's Markov chains lotto software is more effective than regular Markov based on followers.](https://saliu.com/images/MarkovChainsLottery.gif)

不言而喻，這是馬可夫鏈演算法在彩票應用中最有效的函數 _——M = 從配對生成馬可夫鏈組合_ ：

![Ion Saliu's Markov chains lotto software is more effective than regular Markov based on followers.](https://saliu.com/images/MarkovChainsPairs.gif)

— 這是樂透馬可夫鏈演算法最有效的方法 — _F = 配對和最常見數字_ ：

![Ion Saliu's Markov chains lotto software is more effective than regular Markov based on followers.](https://saliu.com/images/MarkovChainsPairs.gif)

![Ion Saliu's Markov chains lotto software is more effective than regular Markov based on followers.](https://saliu.com/images/MarkovChainsPairsHotNumbers.gif)

-   從 Ion Saliu 的免費軟體網站下載 [**_markov.exe_ 樂透程式**](https://saliu.com/freeware/markov.exe) 。
-   該軟體是 **32 位元的** ，因此它可以在 **32 位元和 64 位元 Windows 的**_**命令提示字元**_下運行。它不能在 GUI Windows 中運行，因為它需要命令列參數。
-   首先閱讀_馬可夫鏈_彩票軟體的說明文件： [_**馬可夫彩票程式：幫助、說明**_](https://saliu.com/freeware/markov-lotto-help.html) 。

![Markov-chains software algorithms should consider pair of lotto numbers instead of followers.](https://saliu.com/HLINE.gif)

順便一提 -  
_**CoolRevGui**_ 是為 Windows **NT** （一個較老的平台）編寫的。它處理 32K 以下的檔案時運作良好。對於超過 32K 的文件，它可能也能正常工作，具體取決於作業系統及其組件。就我的情況以及其他一些情況而言，它無法正常處理非常大的文字檔案。

_**GUI**_ 版本經常會變更其物件和元件（例如_對話方塊_ ），從而造成不相容。另一方面，我的 32 位元_**命令提示字元**_彩票軟體在所有 32 位元和 64 位元 Windows 版本（從 _Windows 95_ 到 _Win 10）_ 上都能完美運作。幹得好，不言而喻！ Parpaluck，你應該得到**滿分 10 分** ，無論是在 _Windows 10、2015 年_還是之後的版本！

![Markov chains lottery software runs faster and better at Windows command prompt instead of GUI.](https://saliu.com/HLINE.gif)

## <u><span lang="zh-TW"><span data-immersive-translate-translation-element-mark="1"><span data-immersive-translate-translation-element-mark="1">機率論、數學、統計學、組合學資源</span></span></span></u>

-   [**機率論**](https://saliu.com/theory-of-probability.html) ： _**最佳介紹、公式、演算法、軟體**_ 。
-   _**下載**_ **BirthdayParadox、Collisions、PermuteCombine、Markov** 、 _**科學、機率、數學**_[軟體](https://saliu.com/infodown.html) 。
-   [_**賭博的基本公式**_](https://saliu.com/Saliu2.htm) 。
-   [_**賭博 基本 公式**_的 數學](https://saliu.com/formula.htm) .
-   [_**最佳賭場賭博系統：二十一點、輪盤、有限馬丁格爾投注、累進投注**_](https://saliu.com/occult-science-gambling.html) 。
-   [_**軟體，公式計算樂透賠率，超幾何分佈機率**_](https://saliu.com/oddslotto.html) （ _精確_和_至少_ ）。
-   [_**線上機率、賠率計算器**_](https://saliu.com/online_odds.html) ， _包括任何樂透、彩票_ 。
-   [_**軟體、計算樂透賠率的公式、超幾何分佈機率**_](https://saliu.com/bbs/messages/266.html) 。
-   [_**標準差、高斯、常態、二項分佈、分佈**_](https://saliu.com/formula.html)  
    計算：中位數、確定性程度、標準差、二項式、超幾何、平均值、總和、機率、幾率。
-   [_**樂透**_**<u>奇蹟網格 </u>** 、 _**超級樂透策略、系統**_ 。](https://saliu.com/bbs/messages/grid.html)
-   [_**隨機性、隨機性程度、確定性程度**_ 。](https://saliu.com/bbs/messages/683.html)
-   軟體介紹：
-   [_**CoolRevGui**_ ： _最終檔案反轉器、隨機播放器、文字檢視器軟體_](https://saliu.com/programming.html) 。
-   [_**UPDOWN**_ ： _彩票結果、文字檔案中的逆序_](https://saliu.com/bbs/messages/539.html) 。

![The first real lotto software to apply Markov Chains to lottery games for 6 winning numbers.](https://saliu.com/HLINE.gif)

**[首頁](https://saliu.com/index.htm) | [搜尋](https://saliu.com/Search.htm) | [新文章](https://saliu.com/bbs/index.html) | [基本公式](https://saliu.com/formula.htm) | [賠率產生器](https://saliu.com/calculator_generator.html) | [目錄](https://saliu.com/content/index.html) | [論壇](https://forums.saliu.com/) | [網站地圖](https://saliu.com/sitemap/index.html)**

![Read an article on gambling, mathematics, Markov chains for lottery, algorithms, programs.](https://saliu.com/HLINE.gif)
