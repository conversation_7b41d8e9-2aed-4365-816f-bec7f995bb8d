using Test
using SaliuSystem0009.Evaluator
using SaliuSystem0009.DataProcessor
using SaliuSystem0009.Strategy

@testset "Evaluator.jl tests" begin
    # Create dummy data
    drawings = [
        LotteryDrawing("2023-01-01", [1, 2, 3, 4, 5, 6]),
        LotteryDrawing("2023-01-02", [10, 11, 12, 13, 14, 15]),
        LotteryDrawing("2023-01-03", [20, 21, 22, 23, 24, 25]),
    ]

    # Test check_strategy with a NumberRange filter
    strategy_hit = Strategy("HitStrategy", [Filter("NumberRange", 1, 6)])
    @test check_strategy(strategy_hit, drawings) == 1 # Only the first drawing should hit

    strategy_no_hit = Strategy("NoHitStrategy", [Filter("NumberRange", 50, 60)])
    @test check_strategy(strategy_no_hit, drawings) == 0

    # Test generate_skip_chart (check if it runs without error and prints output)
    @test_logs (:info, r"Generating skip chart for strategy") generate_skip_chart(strategy_hit, drawings)

    # Test lie_elimination
    lie_strategy = Strategy("LIEStrategy", [Filter("NumberRange", 1, 10)])
    @test lie_elimination(lie_strategy, drawings) == 1 # Only the first drawing should be eliminated
end