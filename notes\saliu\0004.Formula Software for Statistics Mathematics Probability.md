---
created: 2024-12-29T16:50:15 (UTC +08:00)
tags: [FORMULA,software,standard deviation,binomial,mean,average,median,normal distribution,probability,mathematics,statistics,Gauss,curve,normal probability rule,gambling,certainty,]
source: https://saliu.com/formula.html
author: 
---

# Formula Software for Statistics Mathematics Probability

> ## Excerpt
> Programming Super Formula software, gambling, probability, statistical calculations in standard deviation, binomial distribution, odds, median, mean, average.

---
![Formula Super Software is the best in probability, statistics, gambling, standard deviation.](https://saliu.com/HLINE.gif)

### I. [The Definitive Probability, Statistics, Gambling Software](https://saliu.com/formula.html#Software)  
II. [Fundamental Formula of Gambling: Calculation (N)](https://saliu.com/formula.html#Formula)  
III. [Degree of Certainty (DC)](https://saliu.com/formula.html#Certainty)  
IV. [Probability of FFG Median (p)](https://saliu.com/formula.html#Median)  
V. [Binomial Distribution Formula: _EXACTLY M Successes in N Trials_](https://saliu.com/formula.html#Binomial)  
VI. [Probability of _AT LEAST M Successes in N Trials_](https://saliu.com/formula.html#Least)  
VII. [Probability of _AT MOST M Successes in N Trials_](https://saliu.com/formula.html#Most)  
VIII. [Probability (Odds) to _WIN AT LEAST K of M in P from N_](https://saliu.com/formula.html#Lotto)  
IX. [Binomial Standard Deviation](https://saliu.com/formula.html#Deviation)  
X. [Normal Probability Rule](https://saliu.com/formula.html#Probability)  
XI. [Calculate Lotto Odds as _EXACTLY M of N_](https://saliu.com/formula.html#Odds)  
XII. [Calculation of Lotto Odds Using Hypergeometric Distribution](https://saliu.com/formula.html#Hypergeometric)  
XIII. [Miscellanea: Statistical Standard Deviation, Sums of Numbers, Shuffle Files, Randomize Numbers](https://saliu.com/formula.html#Random)

![Probability, Statistics, Gambling Mathematics Software.](https://saliu.com/HLINE.gif)

## <u>1. The Definitive Probability, Statistics, Gambling Software</u>

Axiomatic one, **SuperFormula** is the definitive software for statistics, probability, odds, gambling mathematics… and much more. The functions are grouped in 12 categories. Each software category has its own detailed sub-categories. This unique application grew from the request by many people to create software to automate the calculations in the _**Fundamental Formula of Gambling (FFG)**_. _**FFG**_ discovered the most fundamental elements of theory of probability and also the Universe: The relation between the _**degree of certainty DC**_, _**probability p**_, and _**number of trials N**_.

[![Buy the best software: Lottery, Powerball, Mega Millions, gambling roulette, blackjack, sports.](https://saliu.com/ScreenImgs/super-formula-1.gif)](https://saliu.com/membership.html)

• **FORMULA**, version 8.1, April 22, 2003 ~ Retired 16-bit software.  
• **SuperFormula**, version 13.1, January 2011 ~ 32-bit software, supersedes FORMULA.  
• **Shuffle**, version 8.0 ~ March 2011.

![Calculate standard deviation, binomial distribution, odds, probabilities, median, mean, average.](https://saliu.com/images/standard-deviation-calculator.gif)

## <u>2. <i>Fundamental Formula of Gambling</i> (<i>FFG</i>: <i>N</i> from <i>p</i> and <i>DC</i>)</u>

This function applies the _**Fundamental Formula of Gambling (FFG)**_. It calculates the number of trials **N** necessary for an event of probability **p** to appear with the degree of certainty **DC**.  
For example, how many coin tosses are necessary to get at least one _heads_ (p = 1/2) with a degree of certainty equal to 99%? Answer: 7 tosses.

We may also call it the _Fundamental Formula of The Universe_ or the _Formula of The Everything_.

![Sign up to download the best software in its class, including mathematics, probability, statistics.](https://saliu.com/ScreenImgs/FFG1.jpg)

![Degree of certainty and probability calculated by Formula One software.](https://saliu.com/HLINE.gif)

## <u>3. <i>Degree of Certainty</i> (<i>DC</i> from <i>p</i> and <i>N</i>)</u>

This function calculates the degree of certainty DC necessary for the event of probability p to occur within N trials.  
For example, what is the degree of certainty to get at least one _heads_ (p = 1/2) within 10 tosses? Answer: 99.902%.  
The probability p simply shows the number of expected successes in one trial. It doesn't tell a whole lot when it comes to real-life probability situations or problems. For example, if we toss the coin twice, it does NOT mean we are guaranteed to get one _heads_. We could flip the coin ten times without seeing heads even once!

-   If p = 1 / N, we can discover an interesting relation between the degree of certainty DC and the number of trials N. The degree of certainty has a limit, when N tends to infinity. The limit of the degree of certainty DC is {1 — (1/e)} when N tends to infinity,  
    for an event of probability p = 1/N and a number of trials equal to N.  
    **e** represents the base of the natural logarithm and equals approximately **2.718281828...**  
    The limit {**1 — (1/e**)} is approximately **0.63212055...(63.2%)**  
    The [**_mathematical formula_**](https://saliu.com/formula.htm) is known as _**Ion Saliu's Paradox of N Trials**_.

![Probability, statistics, mathematics of median, average with Ion Saliu programs.](https://saliu.com/HLINE.gif)

## <u>4. Probability of <i>FFG Median</i> (<i>p</i> from <i>DC</i> and <i>N</i>)</u>

This function calculates the probability p when the degree of certainty DC and the number of trials N are known.  
There are situations when you have the statistical median of a series N; therefore the degree of certainty DC=50%; but you don't know the probability of the parameter p. The program calculates the probability p leading to a degree of certainty DC and a number of trials N.

For example, the winning reports created by LotWon software show a series of filters and their medians. If not calculated, you can use an editor such as QEdit and do a column blocking, then sort the column (filter) in descending order. The median represents the middle point of the sorted column. The median also represents the number of SKIPS (trials between hits) for a degree of certainty equal to 50%. I do not describe every filter in my software, so nobody can tell the probability of every filter.

You can determine the odds for a filter using this function of **Super Formula**. Other filters are described and thus their probabilities can be calculated in advance. They will prove the validity of the _**Fundamental Formula of Gambling**_. For example, the probability of _3 of 6_ in a 6/49 lotto is 1 in 57. FFG calculates the median for this situation (DC=50%) as 39. Take a real draw history, such as UK 6/49 lotto. Do the winning report for 500 past draws; sort in descending order the filter _Threes_ for layer 1. The median is 37 or closely around 39. Reciprocally, when you see a median equal to 37, you can determine the probability of the parameter as _1 in 54_ (very close to the real case of exactly _1 in 57_; it is, however, almost equal to _at least 1 in 53.nn_).

![Binomial Distribution Formula software helps with analyses in stochastic events.](https://saliu.com/HLINE.gif)

## <u>5. The <i>Binomial Distribution</i> Formula (<i>BDF</i>)</u>

The function calculates the probability _**BDF**_ of _**exactly M successes in N trials**_ for an event of probability **p**.

For example, we want to determine the probability of getting exactly 5 _heads_ in 10 tosses. We tossed the coin 7 times and recorded 5 _heads_. We toss the coin for the 8th time and get another _heads_ (the 6th). We must stop the tossing; the experiment failed. We can no longer get EXACTLY 5 _heads_ in 10 tosses. It is obvious that the previous events influenced the coin toss number 9.  
A sequence of events means that the events do not take place at the same time. They occur one after another.

The _**Binomial Distribution Formula**_ shows some interesting facts. For example, the probability to toss EXACTLY 1 _heads_ in 10 tosses is only 0.98%. It is quite difficult to get only 1 _heads_ and 9 _tails_ in 10 tosses.

-   The probability to toss EXACTLY 5 _heads_ in 10 tosses is 24.6%. It is not that usual to get exactly 5 _heads_ in 10 trials, even if the individual chance of _heads_ is 50%! We might have thought that we would get quite often 5 _heads_ and 5 _tails_ in 10 coin tosses. NOT! The chance is even slimmer to get 500 _heads_ and 500 _tails_ in 1000 tosses: 2.52%.
-   The probability to get 5 _heads_ in 5 tosses represents, actually, the probability of _5 heads in a row_ (3.125%).
-   There is a _data size limit_. The number of trials **N** must not be larger than _1754_. There will be an overflow if you use very large numbers. The factorials grow crazily high and fast!

The generalized formula for _**exactly M successes in N trials**_:

<big><b>BDF = C(N, M) * p<sup>M</sup> * (1 — p)<sup>N — M</sup></b></big>

**BDF** = probability, chance of _**exactly M successes in N trials**_;  
**p** = the individual _**probability**_ of the phenomenon (e.g. p = 0.5 to get _heads_ in coin tossing);  
**M** = the _**exact number of successes**_ (e.g. exactly 5 _tails_ in 10 coin tosses);  
**N** = the _**number of trials**_ (e.g. exactly 5 _tails_ in 10 trials).

![Calculate probability, odds, analyze statistics of M successes N trials cases.](https://saliu.com/HLINE.gif)

## <u>6. Probability of <i>AT LEAST M Successes in N Trials</i></u>

The function calculates the probability of _**at least M successes in N trials**_ for an event of probability **p**.

For example, we want to determine the probability of getting at least 4 _heads_ in 10 tosses. Logically, the following situations qualify as _success_: 4 _heads_; 5 _heads_; 6 _heads_; 7 _heads_; 8 _heads_; 9 _heads_; and 10 _heads_. Obviously, the probability is better than the _exactly 4 of 10_ case.  
There is a data type limit. The number of trials N must not be larger than _1754_. There will be an overflow if you use very large numbers.

![Formula software calculates large cases of the factorial function or operator.](https://saliu.com/HLINE.gif)

## <u>7. Probability for <i>AT MOST M Successes in N Trials</i></u>

The function calculates the probability of _**at most M successes in N trials**_ for an event of probability **p**.

For example, we want to determine the probability of getting at most 4 _heads_ in 10 tosses (no more than 4 in 10). In _at least M in N_ we look at the glass as being half full. Why not look at it from the pessimistic perspective: the glass can be empty sometimes (or present degrees of emptiness)! Logically, the following situations qualify as _success_: 4 _heads_; 3 _heads_; 2 _heads_; 1 _heads_; and 0 _heads_. The probability can be higher than the _exactly 4 of 10_ or _at least 4 of 10_ cases, but it won't be better from a player's perspective!

There is a _data size limit_. The number of trials **N** must not be larger than **1754**. There will be an overflow if you use very large numbers. Blame the _permutations_ (calculated by the _factorial_ operator _N!_) and the limitations of the computers…

![Run the most accurate and comprehensive software to calculate odds in lotto, lottery prizes.](https://saliu.com/HLINE.gif)

## <u>8. Probability (Odds) to Win <i>AT LEAST K of M in P from N</i> in Lotto, Powerball, Mega Millions</u>

The official lotto odds are calculated as _exactly K of M in P from N_. For example, in a lotto 6/49 game, the player must play exactly 6 numbers per ticket. The lottery commission draws 6 winning numbers from a field of 49. If the player plays only 6 numbers, the odds of getting exactly _3 of 6_ are _1 in 56.66_. The player can play combinations of 6 from a pool of 10 picks, for example. Now, the odds can be calculated as exactly _3 of 6 in 10 from 49_: _1 in 12.75_.

In real life the player gets a better deal, however. The commission does not oblige the players to _exactly_ situations. The real life situation is _at least K of M from N_. The commissions don't care if you play just 6 numbers, or play a pool of picks. They don't care if you expected _3 of 6_ hits, but hit _4 of 6_. They'll pay you for the highest prize per ticket. It is clear that _at least K of M from N_ is better than _exactly K of M from N_ from the player's perspective.

If the player plays 57 6-number random picks, the player should expect one _3 of 6_ hit. If playing 100 times 57 tickets, the expectation should be 100 _3 of 6_ hits. Sometimes, however, higher prizes can be hit. That's why the odds of getting _at least 3 of 6 from 49_ are _1 in 56.66_.

Many lotto wheel aficionados might broadcast screams of happiness. Cool down, Wheely! The previous calculations do not imply that 54 lines (combinations) will guarantee 100% in one draw a _3 of 6_ 49-number wheel! Calculating the minimum number of successes for a 100% guarantee is a totally different matter. It is a book in itself if one considers also the algorithm to generate the successes...

I wrote in the article [_**Fundamental Formula of Wheeling**_](https://saliu.com/wheel.html):  
_“… the probability of winning \[exactly\] **3 of 6** is **1 in 57**. FFG calculates the median for **p = 1/57** as **39.16**, rounded up to **40** for **DC = 50%**. How close is that figure (40) to reality in the UK 6/49 history file? The file I've been using for this analysis has 737 draws (contains a few so-called **extra** draws). The file has 733 regular draws from the beginning of the game to the draw of January 1, 2003. The median for the **3 of 6** case is around **39**.”_

The calculations are correct, as far as _standard deviation_ is concerned. The _FFG median_, however is precisely in accordance with the probability of _at least 3 of 6: 1 in 56_. It's right on the money! Forget about one standard deviation! I had always noticed this small discrepancy in all the data files I analyzed. The filter medians are always a few points lower than the FFG medians. Now it fully makes sense. The medians are the result of _at least K of M in N_ probability, NOT the _exactly K of M in N_ probability!

We may consider from now on the _**Fundamental Formula of Gambling**_ to be _the most precise instrument_ in games theory. There are a few posts at this web site dealing with _Markov chains:_ [_**Markov Chains in Lottery, Lotto**_](https://saliu.com/Markov_Chains.html).  
Searching on _Markov chains_ at Google yields close to 100,000 hits! The topic is hot! I stated, however, that _**FFG**_ outruns _**Markov**_ by several steps. Again, once and for all, the _**Fundamental Formula of Gambling**_ is be _the most precise instrument_ in games theory. Unlike _**Markov chains**_, _**FFG**_ considers previous events to be of the essence for future events. The events repeat precisely, according to the famed _**Fundamental Formula of Gambling**_.

![Probability of binomial standard deviation with formula software reveals hidden gambling truths.](https://saliu.com/HLINE.gif)

## <u>9. <i>Binomial Standard Deviation (BSD)</i></u>

This function calculates the binomial standard deviation for binomial events (i.e., experiments characterized by two and only two outcomes: win or loss; success or failure). This is the _theoretical_ or _expected_ value of the standard deviation. The standard deviation can also be calculated _post facto_: after the experiment. Its name is self-explanatory. You can see in the WS-3 reports generated by LotWon a standard deviation for every filter. A filter goes up and down from an average value. The standard deviation calculates the positive average of all deviations (fluctuations) from an average norm.

The _binomial standard deviation_ has great merit. It shows what fluctuation to expect. Before starting the coin toss, one can have an accurate idea of how many _heads_ will fall in a number of trials (tosses). Or how many winning hands one can expect playing 200 blackjack rounds.

![Normal Probability Rule Formula applied to stochastic events.](https://saliu.com/HLINE.gif)

## 10\. The Normal Probability Rule

The Normal Probability Rule is a more precise statistical instrument than Gauss curve (or the curve of the normal distribution).  
When we calculated the binomial standard deviation the result is a report like this one (for 100 coin tosses):

```
<span size="5" face="Courier New"><b>The standard deviation for an event of probability
p =  .5
in  100  binomial experiments is:
BSD =  5

The expected (theoretical) number of successes is: <i>50</i>

Based on the <i>Normal Probability Rule</i>:

· 68.2% of the successes will fall within 1 Standard Deviation
from  50 - i.e., between  <i>45 - 55</i>
·· 95.4% of the successes will fall within 2 Standard Deviations
from  50 - i.e., between  <i>40 - 60</i>
··· 99.7% of the successes will fall within 3 Standard Deviations
from  50 - i.e., between  <i>35 - 65</i>. 
</b></span>
```

I have been working thoroughly with pairing strategies, especially in the digit lotteries. I have encountered far better situations than offered by the traditional normal probability rule. That made me to take a different approach to calculating the normal probability rule. The traditional rule is based on Gauss or normal distribution curve. The keyword here is _curve,_ implying continuous. The lottery or gambling are _discrete,_ however. The _one size fits all_ approach leads to discrepancies. In the example above, the new _normal probability rule_ I am using gives:

72.87% of the successes will fall within 1 Standard Deviation  
from 0 - i.e., between 45 - 55  
One caveat, as in the binomial distribution probability case, there is a data limit: 1500 trials. It covers a pretty good range of lottery and gambling cases.

![The lottery odds calculated as exactly are worse than odds as at least regarding lotto prizes.](https://saliu.com/HLINE.gif)

## <u>11. Calculate <i>Lotto Odds As EXACTLY M of N</i></u>

Calculate the lotto odds, for _0 of k_ to _m of k_ and as _exactly_.  
For example, the odds in a lotto-49 game drawing 6 numbers: _0 of 6_; _1 of 6_; _2 of 6_; _3 of 6_; _4 of 6_; _5 of 6_; _6 of 6_.

The probability is calculated as _exactly M of N_ (not _at least M of N_). Sometimes such probability is 0: the event is impossible; for example _0 of 6_ or _1 of 6_ for the _6/10_ case.

This function handles any type of lotto game, including Keno, Mega Millions, and Powerball. This function incorporates the entire program **OddsCalc**. The program is still available as standalone, although the integrated **Scientia** package is highly recommended.

![Science is a collection of scientific programs in math, probability theory, statistics calculators.](https://saliu.com/ScreenImgs/Scientia.gif)

![Probability of Hypergeometric Distribution Software is followed by lottery commissions.](https://saliu.com/HLINE.gif)

## <u>12. Full Calculation of Lotto Odds Using <i>Hypergeometric Distribution Probability</i></u>

The hypergeometric distribution probability calculates the lotto odds as _EXACTLY K of M in P from N_. This function calculates all the odds of any lotto game, including Keno and Powerball, using the _hypergeometric distribution probability_ formula — the official method applied by the lottery commissions.  
For example: In a lotto-49 game drawing 6 winning numbers, what are the odds of getting exactly _1 of 6_ when the player plays 6 numbers, AND the lottery draws 6 numbers from 49? In the case above: the probability of _(1,6,6,49)_, or _(4,6,6,49)_, or _(10,10,20,80 - Keno)_...

This calculator helps to figure out the odds when playing combinations of various lengths and for various prizes. Many lottery players select 12, or 18, or 20, etc. numbers (_picks_) and apply so-called _lotto wheels_ or _abbreviated lottery systems_. It is very easy now to calculate the odds for getting 6 winning numbers when playing a pool of, say, 18 lotto numbers or picks.

![Calculate statistics, statistical standard deviation, mean average, sums, median, randomize shuffle.](https://saliu.com/HLINE.gif)

## <u>13. Miscellanea: Statistical Standard Deviation, Sums of Numbers, Shuffle Files, Randomize Numbers</u>

This is a collection of useful functions:  
\- sum-up all the numbers between N1 and N2;  
\- sum-up all the numbers in a data file;  
\- calculate N factorial (same as permutations of N);  
\- calculate arrangements of N taken M at a time;  
\- shuffle contiguous or non-contiguous numeros.

**A**. Sum-up all the numbers between N1 and N2  
For example: the sum of all numbers from 1 to 100 is 5050; from 33 to 100 is 4522. The function calculates instantly the sums of all the numbers from 1 to billions, or from billions to trillions, etc. It is much faster and more convenient than using that hand-held calculator.

**B**. Sum-up all the numbers in a data file; calculate statistical standard deviation  
This function calculates the sum of all the numbers previously saved in a data file in text format; e.g.  
17 34 2.5, 100, 99.99, 1000000, 71392.  
The file can have one number per line, or multiple numbers per line separated by comma, blank space(s), or combination of both. Do NOT leave any blank lines in the file! The function calculates also:  
_average_; _median_; _minimum_ and _maximum_; _statistical standard deviation_.

The Pennsylvania Cash-5 data file has 2604 drawings (my file). The average is 20.0095. Well, the game draws from 1 to 39 numbers. The theoretical average is (1+39)/2 = 20. My data file in Pennsylvania lottery pick-3 game has 3899 drawings. The average is 4.4817. The game draws 3 digits from 0 to 9. The theoretical average is (0+9)/2 = 4.5. My data file in Pennsylvania lottery pick-4 game has 6056 drawings. The average is 4.4964. The game draws 4 digits from 0 to 9. The theoretical average is (0+9)/2 = 4.5. My Pennsylvania lotto-6 data file has 475 drawings. The average is 34.2589. The game draws from 1 to 69 numbers. The theoretical average is (1+69)/2 = 35.

How about the more complicated game, Powerball? There was a Powerball game with 5 regular numbers from 1 to 49 plus one power ball from 1 to 42. The theoretical average is the weighted average: {(((1+49)/2)\*5) + ((1+42)/2)} / 6 = 24.4167. My PowerBall 49/5/42 data file has 514 draws. The average is 24.356. The current Powerball game has 5 regular numbers from 1 to 53 plus one power ball from 1 to 42. The theoretical average is the weighted average: {(((1+53)/2)\*5) + ((1+42)/2)} / 6 = 26.0833. My Powerball 53/5/42 data file has 54 draws. The average is 25.605. The actual average is close to the theoretical value even for a small amount of draws!

The standard deviation comes also very close to the calculations of the fundamental formula of gambling. My software takes into account such mathematical requirements. The combinations or sets follow the rules of probability theory and statistics. The combinations are always inside the Gauss bell (normal distribution curve). It's a major factor of optimization.

The numbers have varying frequencies, but the average comes very close to the theoretical value!  
See also the interesting case of the 1-2-3-4-5-6 type of combinations: [_**Lotto Combination 1 2 3 4 5 6 Probability, Odds**_](https://saliu.com/bbs/messages/961.html).

**C**. Calculate N factorial (same as permutations of N)  
This function calculates permutations of N or N factorial.  
N! = 1 x 2 x 3 x ... x N  
For example: permutations of 6 = 720 (1x2x3x4x5x6 = 720).  
N must not be larger than 1500—overflow danger!

**D**. Calculate arrangements of N taken M at a time  
This function calculates arrangements of N taken M at a time:  
A(N,M) = N x (N-1) x ... x (N - M + 1)  
For example: A(10,3) = 10 x 9 x 8 = 720.  
Use this function to calculate total _trifecta_ or _superfecta_ combinations in a horse race; e.g. 1,2,3; 4,2,7; 10,6,5, etc.

**E**. Shuffle Pools of Contiguous or Non-contiguous Numbers  
**Shuffle** can shuffle (randomly arrange) a group of contiguous numbers from 1 to N. If the user types 10 at the input prompt, the program will start a continuous process of randomizing 10 unique numbers, from 1 to 10. The sequences look like: 3, 7, 1, 9, 10, 4, 6, 5, 2, 8. The sequence 1, 2, 3, 4, 5, 6, 7, 8, 9, 10 is so rare that it is said only Almighty Number can generate it.  
There is another situation: groups of non-contiguous numbers, such as: 1, 44, 33, 55 77 22 66 99 13 111 49 29 25 9 54. Function XII of **Super Formula** can handle this type of randomizing situations as well. The groups of non-contiguous numbers can be typed at screen prompts. Or, the numbers can be saved to a text file first. The text file can be used as an input device to **SuperFormula**. The numbers must be separated by space(s), or commas, or combination of both. Also, the numbers can be placed on several lines, or in one column. The user chooses how many times to run the randomization process. The initial group of non-contiguous numbers can be arranged in any order, including sequential.

**Shuffle** randomizes (randomly arranges) numbers and text or numeric files. The file randomization is one of the most requested features from users of my software. The program applies three methods of randomization:  
~ _Horizontal_: shuffle the order of the items within the line, without changing the order of the lines in the file;  
~ _Vertical_: randomize the order of the lines in the file, without changing the order of the items within the line;  
~ Treat the file as a _single row/column_ of data: randomize all the items in the file, regardless of line and column.

The files can have uneven lines; i.e. unequal numbers of items.

The program can also generate combinations for all kinds of lotto games, including Powerball, Mega Millions, Euromillions. The combinations tend to be like in real-life: the software generates unordered random numbers.

![Shuffle is the best software to randomize numbers or files.](https://saliu.com/ScreenImgs/shuffle-1.gif)

![Probability Theory, Live is a great book by Ion Saliu, including mathematical, scientific software: Super Formula.](https://saliu.com/probability-book-Saliu.jpg) Read Ion Saliu's first book in print: [**_Probability Theory, Live!_**](https://saliu.com/probability-book.html)  
~ Founded on valuable mathematical discoveries with a wide range of scientific applications, including probability theory applied to scientific, statistical software: Super Formula.

![Statistics, gambling mathematics software was created by founder of theory of probability of life.](https://saliu.com/HLINE.gif)

[

## <u>Resources in Theory of Probability, Mathematics, Statistics, Standard Deviation, Software</u>

](https://saliu.com/content/probability.html)See a comprehensive directory of the pages and materials on the subject of theory of probability, mathematics, statistics, standard deviation, plus software.

-   The Best Introduction to [_**Standard Deviation**_](https://saliu.com/deviation.html): Theory, Algorithm, Software.
-   The Latest on: [_**Standard Deviation, Variance**_](https://saliu.com/standard_deviation.html), Variability, Fluctuation, Volatility, Variation, Dispersion, Median, Mean Average.
-   New FORMULA 3.1 [_**Software Calculates Standard Deviation in Elections, Politics**_](https://saliu.com/bbs/messages/547.html).
-   [_**Generate combinations inside the bell (Gauss) curve**_](https://saliu.com/bbs/messages/559.html), around the _FFG median_.
-   Upgrade to FORMULA: [_**Standard Deviation, Binomial Distribution**_](https://saliu.com/bbs/messages/269.html).
-   [_**Probability, odds, standard deviation, binomial software**_](https://saliu.com/bbs/messages/264.html).
-   [_**Software, formulas to calculate lotto odds using the hypergeometric distribution probability**_](https://saliu.com/bbs/messages/265.html).
-   [_**Probability, Odds, Formulae, Algorithm, Software Calculator**_](https://saliu.com/bbs/messages/266.html).
-   _**Download Mathematics, Standard Deviation, Statistics**_ [**<u>Software</u>**](https://saliu.com/infodown.html).
    
    Of interest:  
    
-   [_**Fundamental Formula of Gambling (FFG)**_](https://saliu.com/Saliu2.htm).
-   [_**Mathematics of Fundamental Formula of Gambling**_](https://saliu.com/formula.htm).

![Statistics, probability, gambling Software, formulae are free at this site.](https://saliu.com/images/lotto-software.gif)

**[Home](https://saliu.com/index.htm) | [Search](https://saliu.com/Search.htm) | [New Writings](https://saliu.com/bbs/index.html) | [Odds, Generator](https://saliu.com/calculator_generator.html) | [Contents](https://saliu.com/content/index.html) | [Forums](https://forums.saliu.com/) | [Sitemap](https://saliu.com/sitemap/index.html)**

![Thanks for visiting the site of statistics, probability, formula software.](https://saliu.com/HLINE.gif)
