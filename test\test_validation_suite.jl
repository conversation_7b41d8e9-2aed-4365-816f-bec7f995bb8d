# Validation Test Suite
# 驗證測試套件 - 驗證系統的理論正確性和實際表現

using Test
using Dates
using Statistics

# 引入所有必要的模組
include("../src/types.jl")
include("../src/filter_engine.jl")
include("../src/filters/one_filter.jl")
include("../src/filters/two_filter.jl")
include("../src/filters/three_filter.jl")
include("../src/filters/four_filter.jl")
include("../src/filters/five_filter.jl")
include("../src/skip_analyzer.jl")
include("../src/ffg_calculator.jl")
include("../src/pairing_engine.jl")
include("../src/statistics/basic_stats.jl")
include("test_data_manager.jl")
include("test_configuration.jl")

"""
Saliu 理論合規性測試
驗證系統是否符合 Saliu 彩票理論的基本原則
"""
function test_saliu_theory_compliance(test_data::Vector{LotteryDraw})::Dict{String, Any}
    @testset "Saliu Theory Compliance" begin
        results = Dict{String, Any}()
        compliance_tests = []
        
        engine = FilterEngine(test_data)
        skip_analyzer = SkipAnalyzer(test_data)
        ffg_calculator = FFGCalculator()
        
        # 測試 1：Skip 值的合理性
        try
            skip_values = []
            for number in 1:39
                skip = get_current_skip(skip_analyzer, number)
                push!(skip_values, skip)
            end
            
            # Skip 值應該在合理範圍內
            max_skip = maximum(skip_values)
            min_skip = minimum(skip_values)
            avg_skip = mean(skip_values)
            
            # 根據 Saliu 理論，Skip 值應該有一定的分佈特性
            skip_reasonable = (min_skip >= 0 && max_skip <= length(test_data) && avg_skip > 0)
            
            push!(compliance_tests, Dict(
                "test" => "skip_value_reasonableness",
                "status" => skip_reasonable ? "passed" : "failed",
                "details" => Dict(
                    "min_skip" => min_skip,
                    "max_skip" => max_skip,
                    "avg_skip" => avg_skip,
                    "data_length" => length(test_data)
                )
            ))
        catch e
            push!(compliance_tests, Dict(
                "test" => "skip_value_reasonableness",
                "status" => "failed",
                "error" => string(e)
            ))
        end
        
        # 測試 2：FFG 理論一致性
        try
            theoretical_median = calculate_theoretical_ffg_median(ffg_calculator)
            
            # 計算幾個號碼的經驗 FFG
            empirical_ffgs = []
            for number in [1, 5, 10, 15, 20]
                empirical_ffg = calculate_ffg_median(ffg_calculator, number, test_data)
                push!(empirical_ffgs, empirical_ffg)
            end
            
            avg_empirical = mean(empirical_ffgs)
            
            # 理論和經驗 FFG 應該在合理範圍內
            ffg_consistent = abs(theoretical_median - avg_empirical) / theoretical_median < 2.0  # 允許較大差異
            
            push!(compliance_tests, Dict(
                "test" => "ffg_theory_consistency",
                "status" => ffg_consistent ? "passed" : "failed",
                "details" => Dict(
                    "theoretical_median" => theoretical_median,
                    "avg_empirical" => avg_empirical,
                    "relative_difference" => abs(theoretical_median - avg_empirical) / theoretical_median
                )
            ))
        catch e
            push!(compliance_tests, Dict(
                "test" => "ffg_theory_consistency",
                "status" => "failed",
                "error" => string(e)
            ))
        end
        
        # 測試 3：過濾器邏輯一致性
        try
            test_numbers = [1, 2, 3, 4, 5]
            
            # ONE 過濾器：應該返回號碼 1 的當前 Skip
            one_result = calculate_one_filter(engine, 1)
            skip_result = get_current_skip(skip_analyzer, 1)
            
            one_consistent = one_result.current_value == skip_result
            
            # TWO 過濾器：應該計算配對數量
            two_result = calculate_two_filter(engine, test_numbers[1:3])
            two_reasonable = two_result.current_value >= 0 && two_result.current_value <= 3  # 最多 3 個配對
            
            filter_consistent = one_consistent && two_reasonable
            
            push!(compliance_tests, Dict(
                "test" => "filter_logic_consistency",
                "status" => filter_consistent ? "passed" : "failed",
                "details" => Dict(
                    "one_filter_skip" => one_result.current_value,
                    "skip_analyzer_skip" => skip_result,
                    "one_consistent" => one_consistent,
                    "two_filter_pairs" => two_result.current_value,
                    "two_reasonable" => two_reasonable
                )
            ))
        catch e
            push!(compliance_tests, Dict(
                "test" => "filter_logic_consistency",
                "status" => "failed",
                "error" => string(e)
            ))
        end
        
        # 計算合規性評分
        passed_tests = count(test -> test["status"] == "passed", compliance_tests)
        total_tests = length(compliance_tests)
        compliance_rate = passed_tests / total_tests
        
        results["compliance_tests"] = compliance_tests
        results["compliance_rate"] = compliance_rate
        results["passed_tests"] = passed_tests
        results["total_tests"] = total_tests
        
        @test compliance_rate >= 0.7  # 要求 70% 以上的合規性
        
        println("  ✅ Saliu 理論合規性: $(round(compliance_rate * 100, digits=1))%")
        return results
    end
end

"""
歷史回測驗證
使用歷史數據驗證系統預測的準確性
"""
function test_historical_backtest(test_data::Vector{LotteryDraw})::Dict{String, Any}
    @testset "Historical Backtest" begin
        results = Dict{String, Any}()
        backtest_results = []
        
        if length(test_data) < 10
            @warn "數據量太少，無法進行有效的歷史回測"
            results["backtest_results"] = []
            results["prediction_accuracy"] = 0.0
            return results
        end
        
        # 使用前 80% 的數據進行訓練，後 20% 進行測試
        train_size = Int(floor(length(test_data) * 0.8))
        train_data = test_data[1:train_size]
        test_data_subset = test_data[train_size+1:end]
        
        try
            # 基於訓練數據創建分析器
            train_engine = FilterEngine(train_data)
            train_skip_analyzer = SkipAnalyzer(train_data)
            train_ffg_calculator = FFGCalculator()
            
            # 對測試數據進行預測驗證
            for (i, actual_draw) in enumerate(test_data_subset)
                try
                    # 預測：找出 Skip 值最小的號碼（最可能出現）
                    predicted_numbers = []
                    skip_predictions = []
                    
                    for number in 1:39
                        current_skip = get_current_skip(train_skip_analyzer, number)
                        push!(skip_predictions, (number, current_skip))
                    end
                    
                    # 排序並選擇 Skip 最小的前 10 個號碼作為預測
                    sort!(skip_predictions, by = x -> x[2])
                    predicted_numbers = [x[1] for x in skip_predictions[1:10]]
                    
                    # 計算預測準確性
                    actual_numbers = Set(actual_draw.numbers)
                    predicted_set = Set(predicted_numbers)
                    
                    # 計算命中數量
                    hits = length(intersect(actual_numbers, predicted_set))
                    hit_rate = hits / length(actual_numbers)  # 命中率
                    
                    push!(backtest_results, Dict(
                        "draw_index" => i,
                        "actual_numbers" => collect(actual_numbers),
                        "predicted_numbers" => predicted_numbers,
                        "hits" => hits,
                        "hit_rate" => hit_rate
                    ))
                    
                catch e
                    push!(backtest_results, Dict(
                        "draw_index" => i,
                        "error" => string(e),
                        "hits" => 0,
                        "hit_rate" => 0.0
                    ))
                end
            end
            
            # 計算整體預測準確性
            total_hits = sum(result -> get(result, "hits", 0), backtest_results)
            total_possible = length(test_data_subset) * 5  # 每次開獎 5 個號碼
            overall_accuracy = total_hits / total_possible
            
            avg_hit_rate = mean([result["hit_rate"] for result in backtest_results if haskey(result, "hit_rate")])
            
            results["backtest_results"] = backtest_results
            results["prediction_accuracy"] = overall_accuracy
            results["avg_hit_rate"] = avg_hit_rate
            results["train_size"] = train_size
            results["test_size"] = length(test_data_subset)
            
            @test overall_accuracy >= 0.1  # 要求至少 10% 的預測準確性（隨機為 ~12.8%）
            
            println("  ✅ 歷史回測準確性: $(round(overall_accuracy * 100, digits=1))%")
            
        catch e
            @warn "歷史回測失敗: $e"
            results["backtest_results"] = []
            results["prediction_accuracy"] = 0.0
            results["error"] = string(e)
        end
        
        return results
    end
end

"""
數值精度測試
測試計算的數值精度和穩定性
"""
function test_numerical_precision(test_data::Vector{LotteryDraw})::Dict{String, Any}
    @testset "Numerical Precision" begin
        results = Dict{String, Any}()
        precision_tests = []
        
        engine = FilterEngine(test_data)
        ffg_calculator = FFGCalculator()
        
        # 測試 1：重複計算一致性
        try
            # 多次計算同一個過濾器，結果應該一致
            number = 1
            results_list = []
            for _ in 1:10
                result = calculate_one_filter(engine, number)
                push!(results_list, result.current_value)
            end
            
            # 所有結果應該相同
            all_same = all(r -> r == results_list[1], results_list)
            
            push!(precision_tests, Dict(
                "test" => "repeated_calculation_consistency",
                "status" => all_same ? "passed" : "failed",
                "details" => Dict(
                    "results" => results_list,
                    "all_same" => all_same
                )
            ))
        catch e
            push!(precision_tests, Dict(
                "test" => "repeated_calculation_consistency",
                "status" => "failed",
                "error" => string(e)
            ))
        end
        
        # 測試 2：FFG 計算精度
        try
            # 計算理論 FFG，應該是穩定的數值
            theoretical_ffg = calculate_theoretical_ffg_median(ffg_calculator)
            
            # 檢查是否為有效數值
            ffg_valid = !isnan(theoretical_ffg) && !isinf(theoretical_ffg) && theoretical_ffg > 0
            
            # 多次計算應該得到相同結果
            ffg_results = []
            for _ in 1:5
                ffg = calculate_theoretical_ffg_median(ffg_calculator)
                push!(ffg_results, ffg)
            end
            
            ffg_consistent = all(f -> abs(f - theoretical_ffg) < 1e-10, ffg_results)
            
            push!(precision_tests, Dict(
                "test" => "ffg_calculation_precision",
                "status" => (ffg_valid && ffg_consistent) ? "passed" : "failed",
                "details" => Dict(
                    "theoretical_ffg" => theoretical_ffg,
                    "ffg_valid" => ffg_valid,
                    "ffg_consistent" => ffg_consistent,
                    "ffg_results" => ffg_results
                )
            ))
        catch e
            push!(precision_tests, Dict(
                "test" => "ffg_calculation_precision",
                "status" => "failed",
                "error" => string(e)
            ))
        end
        
        # 測試 3：統計計算精度
        try
            test_values = [1.0, 2.0, 3.0, 4.0, 5.0]
            
            # 計算基本統計量
            mean_val = calculate_mean(test_values)
            median_val = calculate_median(test_values)
            std_val = calculate_std_dev(test_values)
            
            # 驗證計算結果
            expected_mean = 3.0
            expected_median = 3.0
            expected_std = sqrt(2.5)  # 手動計算的標準差
            
            mean_correct = abs(mean_val - expected_mean) < 1e-10
            median_correct = abs(median_val - expected_median) < 1e-10
            std_correct = abs(std_val - expected_std) < 1e-10
            
            stats_precise = mean_correct && median_correct && std_correct
            
            push!(precision_tests, Dict(
                "test" => "statistics_calculation_precision",
                "status" => stats_precise ? "passed" : "failed",
                "details" => Dict(
                    "calculated_mean" => mean_val,
                    "expected_mean" => expected_mean,
                    "calculated_median" => median_val,
                    "expected_median" => expected_median,
                    "calculated_std" => std_val,
                    "expected_std" => expected_std
                )
            ))
        catch e
            push!(precision_tests, Dict(
                "test" => "statistics_calculation_precision",
                "status" => "failed",
                "error" => string(e)
            ))
        end
        
        # 計算精度測試成功率
        passed_tests = count(test -> test["status"] == "passed", precision_tests)
        total_tests = length(precision_tests)
        precision_rate = passed_tests / total_tests
        
        results["precision_tests"] = precision_tests
        results["precision_rate"] = precision_rate
        results["passed_tests"] = passed_tests
        results["total_tests"] = total_tests
        
        @test precision_rate >= 0.8  # 要求 80% 以上的精度測試通過
        
        println("  ✅ 數值精度測試: $(round(precision_rate * 100, digits=1))%")
        return results
    end
end

"""
統計顯著性測試
測試結果的統計顯著性
"""
function test_statistical_significance(test_data::Vector{LotteryDraw})::Dict{String, Any}
    @testset "Statistical Significance" begin
        results = Dict{String, Any}()
        significance_tests = []
        
        if length(test_data) < 30
            @warn "數據量太少，無法進行有效的統計顯著性測試"
            results["significance_tests"] = []
            results["significance_rate"] = 0.0
            return results
        end
        
        skip_analyzer = SkipAnalyzer(test_data)
        
        # 測試 1：Skip 分佈的隨機性
        try
            # 收集所有號碼的 Skip 值
            skip_values = []
            for number in 1:39
                skip = get_current_skip(skip_analyzer, number)
                push!(skip_values, skip)
            end
            
            # 計算 Skip 值的統計特性
            mean_skip = mean(skip_values)
            std_skip = std(skip_values)
            
            # 檢查分佈是否合理（不應該所有值都相同）
            skip_variance = var(skip_values)
            has_variance = skip_variance > 0
            
            # 檢查是否有極端異常值
            max_skip = maximum(skip_values)
            reasonable_max = max_skip <= length(test_data)
            
            skip_distribution_reasonable = has_variance && reasonable_max
            
            push!(significance_tests, Dict(
                "test" => "skip_distribution_randomness",
                "status" => skip_distribution_reasonable ? "passed" : "failed",
                "details" => Dict(
                    "mean_skip" => mean_skip,
                    "std_skip" => std_skip,
                    "variance" => skip_variance,
                    "max_skip" => max_skip,
                    "data_length" => length(test_data)
                )
            ))
        catch e
            push!(significance_tests, Dict(
                "test" => "skip_distribution_randomness",
                "status" => "failed",
                "error" => string(e)
            ))
        end
        
        # 測試 2：號碼出現頻率的均勻性
        try
            # 統計每個號碼的出現次數
            number_counts = Dict{Int, Int}()
            for number in 1:39
                number_counts[number] = 0
            end
            
            for draw in test_data
                for number in draw.numbers
                    number_counts[number] += 1
                end
            end
            
            frequencies = collect(values(number_counts))
            mean_freq = mean(frequencies)
            std_freq = std(frequencies)
            
            # 計算變異係數（標準差/平均值）
            cv = std_freq / mean_freq
            
            # 變異係數不應該太大（完全隨機時約為 0.2-0.4）
            frequency_reasonable = cv < 1.0  # 允許較大的變異
            
            push!(significance_tests, Dict(
                "test" => "number_frequency_uniformity",
                "status" => frequency_reasonable ? "passed" : "failed",
                "details" => Dict(
                    "mean_frequency" => mean_freq,
                    "std_frequency" => std_freq,
                    "coefficient_of_variation" => cv,
                    "total_draws" => length(test_data)
                )
            ))
        catch e
            push!(significance_tests, Dict(
                "test" => "number_frequency_uniformity",
                "status" => "failed",
                "error" => string(e)
            ))
        end
        
        # 計算顯著性測試成功率
        passed_tests = count(test -> test["status"] == "passed", significance_tests)
        total_tests = length(significance_tests)
        significance_rate = passed_tests / total_tests
        
        results["significance_tests"] = significance_tests
        results["significance_rate"] = significance_rate
        results["passed_tests"] = passed_tests
        results["total_tests"] = total_tests
        
        @test significance_rate >= 0.6  # 要求 60% 以上的顯著性測試通過
        
        println("  ✅ 統計顯著性測試: $(round(significance_rate * 100, digits=1))%")
        return results
    end
end

"""
執行完整的驗證測試套件
"""
function run_validation_test_suite(data_manager::TestDataManager)::Dict{String, Any}
    println("🧪 開始執行驗證測試套件...")
    
    validation_results = Dict{String, Any}()
    
    # 使用大等大小的測試數據以獲得更好的統計特性
    test_data = get_test_data(data_manager, "medium")  # 使用中等大小避免過長執行時間
    
    try
        # 執行各項驗證測試
        validation_results["saliu_compliance"] = test_saliu_theory_compliance(test_data)
        validation_results["historical_backtest"] = test_historical_backtest(test_data)
        validation_results["numerical_precision"] = test_numerical_precision(test_data)
        validation_results["statistical_significance"] = test_statistical_significance(test_data)
        
        # 計算整體評分
        compliance_score = validation_results["saliu_compliance"]["compliance_rate"]
        backtest_score = validation_results["historical_backtest"]["prediction_accuracy"]
        precision_score = validation_results["numerical_precision"]["precision_rate"]
        significance_score = validation_results["statistical_significance"]["significance_rate"]
        
        # 加權平均（理論合規性和數值精度更重要）
        overall_score = (compliance_score * 0.3 + backtest_score * 0.2 + 
                        precision_score * 0.3 + significance_score * 0.2)
        validation_results["overall_score"] = overall_score
        
        println("\n📊 驗證測試套件結果:")
        println("  - Saliu 理論合規性: $(round(compliance_score * 100, digits=1))%")
        println("  - 歷史回測準確性: $(round(backtest_score * 100, digits=1))%")
        println("  - 數值精度: $(round(precision_score * 100, digits=1))%")
        println("  - 統計顯著性: $(round(significance_score * 100, digits=1))%")
        println("  - 整體評分: $(round(overall_score * 100, digits=1))%")
        
        if overall_score >= 0.85
            println("🎉 驗證測試：優秀")
        elseif overall_score >= 0.75
            println("✅ 驗證測試：良好")
        elseif overall_score >= 0.65
            println("⚠️ 驗證測試：需要改進")
        else
            println("❌ 驗證測試：需要重大修復")
        end
        
        return validation_results
        
    catch e
        @error "驗證測試套件失敗: $e"
        validation_results["error"] = string(e)
        validation_results["overall_score"] = 0.0
        return validation_results
    end
end

# 導出主要函數
export test_saliu_theory_compliance, test_historical_backtest
export test_numerical_precision, test_statistical_significance
export run_validation_test_suite
