# Wonder Grid Lottery System - 快速入門指南

## 🚀 5分鐘快速開始

歡迎使用 Wonder Grid Lottery System！這個指南將幫助您在 5 分鐘內開始使用這個強大的彩票分析系統。

---

## 📋 前置需求

- **Julia 1.8+** （推薦 1.11+）
- **多執行緒支援** （推薦使用 `julia -t auto` 啟動）
- **記憶體**: 最少 4GB RAM
- **作業系統**: Windows, macOS, Linux

---

## ⚡ 快速安裝

### 1. 下載系統

```bash
git clone https://github.com/your-repo/wonder-grid-lottery-system.git
cd wonder-grid-lottery-system
```

### 2. 啟動 Julia

```bash
# 啟用多執行緒以獲得最佳性能
julia -t auto
```

### 3. 載入系統

```julia
# 在 Julia REPL 中
include("src/wonder_grid_system.jl")
```

---

## 🎯 第一個範例

讓我們從一個簡單的範例開始：

### 1. 準備測試數據

```julia
using Dates

# 創建一些測試開獎數據
test_draws = [
    LotteryDraw([1, 5, 10, 15, 20], Date(2023, 1, 1), 1),
    LotteryDraw([2, 6, 11, 16, 21], Date(2023, 1, 2), 2),
    LotteryDraw([3, 7, 12, 17, 22], Date(2023, 1, 3), 3),
    LotteryDraw([4, 8, 13, 18, 23], Date(2023, 1, 4), 4),
    LotteryDraw([5, 9, 14, 19, 24], Date(2023, 1, 5), 5)
]

println("✅ 測試數據準備完成：$(length(test_draws)) 筆開獎記錄")
```

### 2. 創建過濾器引擎

```julia
# 創建標準過濾器引擎
engine = FilterEngine(test_draws)
println("✅ 過濾器引擎創建完成")

# 或者創建優化版本（推薦）
opt_engine = OptimizedFilterEngine(test_draws)
println("✅ 優化過濾器引擎創建完成")
```

### 3. 計算 Skip 值（ONE 過濾器）

```julia
# 計算號碼 1 的 Skip 值
result = calculate_one_filter(engine, 1)
println("號碼 1 的 Skip 值: $(result.current_value)")
println("期望值: $(round(result.expected_value, digits=2))")

# 使用優化版本
skip_value = calculate_skip_optimized(opt_engine, 1)
println("優化計算的 Skip 值: $skip_value")
```

### 4. 分析配對頻率（TWO 過濾器）

```julia
# 分析號碼 1, 2, 3 的配對情況
pairing_result = calculate_two_filter(engine, [1, 2, 3])
println("配對分析結果: $(pairing_result.current_value)")

# 計算特定配對的頻率
freq = calculate_pairing_frequency_optimized(opt_engine, 1, 2)
println("號碼 1 和 2 的配對頻率: $freq")
```

---

## 🔥 進階功能

### 並行計算

```julia
# 並行計算所有號碼的 Skip 值
parallel_results = calculate_all_skips_parallel(test_draws, [1, 2, 3, 4, 5])

for (number, result) in parallel_results
    if result.success
        println("號碼 $number: Skip = $(result.result)")
    else
        println("號碼 $number: 計算失敗")
    end
end
```

### 分散式 Wonder Grid 生成

```julia
# 生成 Wonder Grid
grid_results = generate_wonder_grid_distributed(test_draws, 20)
wonder_grid = grid_results["wonder_grid"]

println("🎯 Wonder Grid 生成完成:")
for (i, row) in enumerate(wonder_grid)
    println("第 $i 行: $(join(row, ", "))")
end
```

### 性能監控

```julia
# 獲取系統性能摘要
performance = get_global_performance_summary()
println("📊 系統性能:")
println("  - 性能等級: $(performance["performance_grade"])")
println("  - 快取命中率: $(round(performance["cache_hit_rate"] * 100, digits=1))%")
```

---

## 💾 使用真實數據

### 載入 CSV 數據

```julia
using CSV, DataFrames

# 假設您有 CSV 格式的開獎數據
# CSV 格式: date,draw_id,num1,num2,num3,num4,num5
function load_lottery_data(csv_file::String)
    df = CSV.read(csv_file, DataFrame)
    draws = LotteryDraw[]
    
    for row in eachrow(df)
        numbers = [row.num1, row.num2, row.num3, row.num4, row.num5]
        date = Date(row.date)
        draw_id = row.draw_id
        
        push!(draws, LotteryDraw(numbers, date, draw_id))
    end
    
    return draws
end

# 載入數據
# real_data = load_lottery_data("data/lottery_history.csv")
# real_engine = OptimizedFilterEngine(real_data)
```

### 批次分析

```julia
# 分析多個號碼的 Skip 值
function analyze_multiple_numbers(engine, numbers)
    results = Dict{Int, Int}()
    
    for number in numbers
        skip = calculate_skip_optimized(engine, number)
        results[number] = skip
    end
    
    return results
end

# 分析前 10 個號碼
hot_numbers = 1:10
skip_analysis = analyze_multiple_numbers(opt_engine, hot_numbers)

println("🔍 Skip 分析結果:")
for (number, skip) in sort(collect(skip_analysis), by=x->x[2])
    println("  號碼 $number: Skip = $skip")
end
```

---

## 🎛️ 系統配置

### 啟用自動調優

```julia
# 執行自動調優
tuning_performed = perform_auto_tuning!()
if tuning_performed
    println("✅ 自動調優已執行")
    
    # 查看調優報告
    tuning_report = get_global_tuning_report()
    println("📈 性能改進: $(round(tuning_report["performance_improvement"], digits=3))")
end
```

### 記憶體優化

```julia
# 檢查記憶體池統計
pool_stats = get_all_pool_statistics(GLOBAL_MEMORY_POOL)
println("🔄 記憶體池統計:")
println("  - 重用率: $(round(pool_stats["summary"]["overall_reuse_rate"] * 100, digits=1))%")
println("  - 總分配: $(pool_stats["summary"]["total_allocations"])")
```

### 快取配置

```julia
# 創建自定義快取配置的引擎
custom_engine = OptimizedFilterEngine(
    test_draws,
    use_compact_data = true,    # 啟用緊湊數據（節省 92.9% 記憶體）
    enable_caching = true,      # 啟用快取
    auto_cleanup = true         # 自動清理
)

# 檢查引擎統計
stats = get_engine_statistics(custom_engine)
println("📊 引擎統計:")
println("  - 快取命中率: $(round(stats["cache_hit_rate"] * 100, digits=1))%")
println("  - 記憶體節省: $(round(stats["compact_data"]["memory_savings_percentage"], digits=1))%")
```

---

## 🔧 故障排除

### 常見問題

#### 1. 性能較慢
```julia
# 檢查是否啟用多執行緒
println("可用執行緒數: $(Threads.nthreads())")
# 如果只有 1 個執行緒，請使用 julia -t auto 重新啟動
```

#### 2. 記憶體使用過高
```julia
# 啟用緊湊數據結構
engine = OptimizedFilterEngine(data, use_compact_data=true)

# 手動清理記憶體池
cleaned = auto_cleanup!(GLOBAL_MEMORY_POOL)
println("清理了 $cleaned 個記憶體池項目")
```

#### 3. 快取命中率低
```julia
# 檢查快取統計
cache_stats = get_cache_statistics(engine.skip_cache.cache)
println("L1 快取項目: $(cache_stats["l1_items"])")
println("命中率: $(round(cache_stats["hit_rate"] * 100, digits=1))%")

# 如果命中率低，可能需要更大的快取
```

---

## 📚 下一步

現在您已經掌握了基本用法，可以探索更多功能：

1. **閱讀 [API 參考文檔](api_reference.md)** 了解所有可用函數
2. **查看 [用戶手冊](user_manual.md)** 學習高級功能
3. **參考 [範例項目](examples/)** 查看實際應用案例
4. **閱讀 [性能調優指南](performance_tuning.md)** 優化系統性能

---

## 🆘 需要幫助？

- **文檔**: 查看 `doc/` 目錄下的完整文檔
- **範例**: 查看 `examples/` 目錄下的範例代碼
- **測試**: 運行 `julia test/run_all_tests.jl` 驗證系統
- **問題**: 查看 [故障排除指南](troubleshooting.md)

---

**🎉 恭喜！您已經成功開始使用 Wonder Grid Lottery System！**

這個系統提供了世界級的彩票分析功能，包括：
- ✅ 完整的 Saliu 過濾器實現
- ✅ 高性能優化（92.9% 記憶體節省）
- ✅ 並行和分散式計算
- ✅ 智能快取和自動調優
- ✅ 實時性能監控

開始探索這個強大的分析工具吧！
