---
created: 2025-07-23T18:21:25 (UTC +08:00)
tags: [winning,strategy,system,wonder grid,wonder-grid,lotto,software,lottery,systems,method,numbers,combinations,standard deviation,number,win,]
source: https://saliu.com/bbs/messages/9.html
author: 
---

# Lotto Wonder-grid Lottery Software Beats Random Play by 3 SD

> ## Excerpt
> Lotto software generates winning reports for lottery wonder grid, the top lotto pairings or pairs that beats random play by 3 standard deviations.

---
[![Download Best Software: Lottery, Lotto, Powerball, Mega Millions, Euromillions, Pick Digit, Keno..](https://saliu.com/images/lottery-software-systems.gif)](https://saliu.com/free-lotto-lottery.html)  

## Winning Reports for Lottery _Wonder-grid_: GridCheck Lotto Software ~  
The _Wonder Grid Lotto Strategy_ Beats Random Play by Over 3 Standard Deviations

## By <PERSON>, ★ _Founder of Lotto Mathematics_

![Lotto Wonder Grid beats random expectation by over 3 standard deviations.](https://saliu.com/bbs/messages/HLINE.gif)

Written on July 11, 2002; later updates.

• The lottery wonder grid is not surrounded by noise, but definitely is surrounded by interest. Maybe people keep quiet about it for a good reason. The _Search_ page of my Web site shows _wonder grid_ or _wonder-grid_ among the top keywords. I also receive emails with requests to detail the concept, and do so on an individual basis! Instead of emailing 100 on-an-individual-basis strategies, I decided to write lottery software to check the lotto wonder grid automatically. The wonder grid is a very complex matter. It would take days and big headaches to check the _magical wheel_ manually and for dozens of past lotto draws.

The new lotto software is entitled:  
GridCheck632, version 1.00, July 2002 - 32-bit _command prompt_ lottery application.

The newest version of the lotto software Bright\* has a component that checks how lotto wonder grids performed in the past. Menu #4, then _G = Grid Check_. It is highly recommended to use the latest version of this lotto software package.

[![Lottery Software: Jackpot Lotto, Pick Lotteries, Powerball, Mega Millions, Euromillions, Keno.](https://saliu.com/ScreenImgs/wonder-grid-check.gif)](https://saliu.com/membership.html)

GridCheck632 checks the past performance of the GRID6 files for various draw ranges. The program starts a number of draws back in the DATA-6 file and creates 3 GRID6 files: for N\*1, N\*2, N\*3 draws. N represents the biggest number in the game. By default, the program checks how the GRID6 files performed in the previous 100 draws (the span). Your data file must have at least {SPAN + (N\*3)} draws. If your lotto-6 game has 49 numbers and SPAN=100 (check the previous 100 draws), then your files must have at least _100 + 49\*3 = 247_ draws. The program will let you know what to do to overcome the error.

The program creates three GRID6 files in the background. The first one is a range equal to the biggest number in your lotto game (e.g. 49); the second file is created for a range equal to N\*2 (the biggest lotto number times 2); the third file is created for a range equal to N\*3 (the biggest lotto number times 3).  
The GRID6 checking reports will be saved to 3 disk files. Defaults: _ChkGrid6.N1_, _ChkGrid6.N2_, _ChkGrid6.N3_. They show how many hits (if any!) the corresponding grid had for a particular drawing. Here is a more clarifying example, with data for Pennsylvania 6/69 lotto game.

```
<span size="5" face="Courier New" color="#c5b358">                  GRID6.N2 Winning Lotto Number Checking


         ~ Lotto Draw # 37 :   2  4  8  11  16  53 
         ~ Files: GRID6.N2 ( 69 )  against PA-6 ( 37 )

   Line    Combinations                          6       5       4  
   no.       Checked                            Hits    Hits    Hits

     16   16  2  3 40 49 32         in draw #                    11 

         Total Hits:                             0       0       1 


         ~ Draw # 38:   1  9  31  45  55  59 
         ~ Files: GRID6.N2 ( 69 )  against PA-6 ( 38 )

   Line    Combinations                          6       5       4  
   no.       Checked                            Hits    Hits    Hits

      1    1 10 31 26 11 22         in draw #                    32 
     16   16  2  3 40 49 32         in draw #                    11 
     22   22  1 31 32 41 59         in draw #            32 

         Total Hits:                             0       1       2 


         ~ Draw # 39:   18  21  23  25  43  53 
         ~ Files: GRID6.N2 ( 69 )  against PA-6 ( 39 )

   Line    Combinations                          6       5       4  
   no.       Checked                            Hits    Hits    Hits

      1    1 10 31 26 11 22         in draw #                    32 
     16   16  2  3 40 49 32         in draw #                    11 
     22   22  1 31 32 41 59         in draw #            32 

         Total Hits:                             0       1       2 


         ~ Draw # 40: 7  8  19  36  40  45 
         ~ Files: GRID6.N2 ( 69 )  against PA-6 ( 40 )

   Line    Combinations                          6       5       4  
   no.       Checked                            Hits    Hits    Hits

      1    1 10 31 26 11 22         in draw #                    32 
     16   16  2  3 40 49 32         in draw #                    11 
     22   22  1 31 32 41 59         in draw #            32 

         Total Hits:                             0       1       2 


         ~ Lotto Draw # 41: 30  41  62  64  65  67 
         ~ Files: GRID6.N2 ( 69 )  against PA-6 ( 41 )

   Line    Combinations                          6       5       4  
   no.       Checked                            Hits    Hits    Hits

      1    1 10 31 26 11 22         in draw #                    32 
     16   16  2  3 40 49 32         in draw #                    11 
     22   22  1 31 32 41 59         in draw #            32 

         Total Hits:                             0       1       2 


         ~ Draw # 42: 16  29  42  45  50  58 
         ~ Files: GRID6.N2 ( 69 )  against PA-6 ( 42 )

   Line    Combinations                          6       5       4  
   no.       Checked                            Hits    Hits    Hits

      1    1 10 31 26 11 22         in draw #                    32 
     16   16  2  3 40 49 32         in draw #                    11 
     22   22  1 31 32 41 59         in draw #            32 

         Total Hits:                             0       1       2 


...

         ~ Lottery Draw # 44: 20  21  23  30  42  44 
         ~ Files: GRID6.N2 ( 69 )  against PA-6 ( 44 )

   Line    Combinations                          6       5       4  
   no.       Checked                            Hits    Hits    Hits

      1    1 10 31 26 11 22         in draw #                    32 
     16   16  2  3 40 49 32         in draw #                    11 
     22   22  1 31 32 41 59         in draw #            32 

         Total Hits:                             0       1       2 

...

         ~ Lotto Draw # 46: 2  20  22  34  43  47 
         ~ Files: GRID6.N2 ( 69 )  against PA-6 ( 46 )

   Line    Combinations                          6       5       4  
   no.       Checked                            Hits    Hits    Hits

      1    1 10 31 26 11 22         in draw #                    32 
     16   16  2  3 40 49 32         in draw #                    11 
     22   22  1 31 32 41 59         in draw #            32 

         Total Hits:                             0       1       2 


         ~ Draw # 47: 24  43  45  48  62  63 
         ~ Files: GRID6.N2 ( 69 )  against PA-6 ( 47 )

   Line    Combinations                          6       5       4  
   no.       Checked                            Hits    Hits    Hits

      1    1 10 31 26 11 22         in draw #                    32 
     16   16  2  3 40 49 32         in draw #                    11 
     22   22  1 31 32 41 59         in draw #            32 
         Total Hits:                             0       1       2 

....

         ~ Lotto Draw # 49: 7  16  23  32  63  64 
         ~ Files: GRID6.N2 ( 69 )  against PA-6 ( 49 )
   Line    Combinations                          6       5       4  
   no.       Checked                            Hits    Hits    Hits
      1    1 10 31 26 11 22         in draw #                    32 
     16   16  2  3 40 49 41         in draw #                    11 
     22   22  1 31 32 41 59         in draw #            32 

         Total Hits:                             0       1       2 


         ~ Lotto Draw # 50: 2  16  40  46  53  68 
         ~ Files: GRID6.N2 ( 69 )  against PA-6 ( 50 )

   Line    Combinations                          6       5       4  
   no.       Checked                            Hits    Hits    Hits

      1    1 10 31 26 11 22         in draw #                    32 
     16   16  3 49 33 40 41         in draw #                    11 
     22   22  1 31 32 41 59         in draw #            32 

         Total Hits:                             0       1       2 


         ~ Draw # 51: 2  10  27  33  45  55 
         ~ Files: Lotto GRID6.N2 ( 69 )  against PA-6 ( 51 )

   Line    Combinations                          6       5       4  
   no.       Checked                            Hits    Hits    Hits

      1    1 10 31 26 11 22         in draw #                    32 
     16   16  3 49 33 40 41         in draw #                    11 
     22   22  1 31 32 41 59         in draw #            32 

         Total Hits:                             0       1       2 

...

         ~ Lottery Draw # 53: 17  21  40  41  53  69 
         ~ Files: GRID6.N2 ( 69 )  against PA-6 ( 53 )

   Line    Combinations                          6       5       4  
   no.       Checked                            Hits    Hits    Hits

      1    1 10 31 26 11 22         in draw #                    32 
     16   16  3 49 26 33 34         in draw #                    11 
     22   22  1 31 32 41 59         in draw #            32 

         Total Hits:                             0       1       2 

</span>
```

The lotto draw range or the span of analysis is a most important element. We were not sure about the optimal value of the range. First, I said it was **N\*3**; then I said it must be **N**. It appears that the best range is **N\*2**. The range **N\*1** is also a good value. As of three-times-the-biggest-number, it probably creates an outdated grid file. A range beyond **N\*3** is useless. You can look at the three reports and see which one(s) fared the best in your lotto-6 game. Again, I'm (almost) convinced that the best ranges are **N\*2** followed by **N\*1**.  
The case above is the report for an **N\*2** grid file.

1) The program started at draw #2 and created a grid file for a range of 69\*2=118 draws. Therefore, the lotto software created the first wonder grid: the best 69 pairings in the range 2-120 past draws. The program stopped at draw #120 in the PA-6 data file. Next, the lotto program created a winning report for draw #1. Important fact: the program does not create a wonder grid for the current draw. It would be a case of 'curve fitting': the wonder grid would hit the jackpot every ten draws or so! But such case would not take place in reality! We must exclude the current draw from the range that creates the wonder grid. The winning report (almost the same as in UTIL632 or WINNERS) checks for _"future"_ drawings. In this first step, the _“future”_ consists of one draw only: #1. The winning report check for _6 of 6_ winners, _5 of 6_ winners, and _4 of 6_ winners. I excluded the _3 of 6_ prize from the reports. There are way too many lines! The reports would be too large and slower! Right now, it takes around one minute to generate the _lotto wonder grid_ checking software. Usually, there are no hits at position #1.

2) The second step. The program continues at draw #3 and creates a grid file for a range of 69\*2=118 draws. Therefore, the program created the second wonder grid: the best 69 pairings in the range 3-121 past draws. The program stopped at draw #121 in the PA-6 data file. Next, the program created a winning report for draws #1 to #2. In this second step, the _“future”_ consists of two draws: #1 and #2.

Usually, the wonder grid starts hitting after 5-6 real lottery drawings ('4 of 6'). Higher prizes require a wider gap between hits. In the case above, draw #38 registered one '5 of 6' hit. The streak continued uninterrupted to draw #53. Had I decided to play just before draw #38, I would have hit 5 winners after 6 draws. That is, draw #32 would have given me one ticket with 5 winners (also one ticket with 4 winners). Had I decided to play just before draw #53, I would have hit 5 winners after 53-32=21 lotto draws. That is, draw #32 would have given me one ticket with 5 winners (also one ticket with 4 winners). A good strategy would be to wait for 5 draws, then start playing for the next up to 35 draws (N/2). The skips are different from game to game. That's why the three grid reports are so useful.

•• How to figure out the validity and strength of various lottery strategies? The most common rule is to compare a strategy to random play. The benchmark is the _**normal probability rule**_:  
_99.7% of the successes will fall within 3 standard deviations from the expected (theoretical) number of successes._  
If a strategy beats the random expectation by more than 3 standard deviations, it surely has a solid foundation. The margin of error is less than 0.3%.  

Also previously, I presented a _static_ strategy that beats random selection by far more than 3 standard deviations. (See [FFG Median, Filtering, Probability, Jackpot](https://saliu.com/bbs/messages/923.html).)  
The game analyzed was Pennsylvania 6/69 lotto. The probability of hitting _6 of 6_ is _1 in 119877472_. The probability of winning _5 of 6_ is _1 in 317136_. The lottery software generated 10,000 combinations around the _FFG median_. I checked 350 real draws in the 6/69 lotto game. Therefore the number of trials was _350 \* 10000 = 3,500,000_.

The normal probability rule applied to the _6 of 6_ case is summarized by SuperFormula as follows:

```
<span size="5" face="Courier New" color="#c5b358">  The standard deviation for a lotto event of probability 
  p =  .00000001 (1 in 119877472)
  in  3500000  binomial experiments is: 
                     BSD =  .17
  The expected (theoretical) number of successes is: 0.29 

  Based on the Normal Probability Rule:

  • 68.2% of the successes will fall within 1 Standard Deviation
  from  0.29 - i.e., between  0.12  -  0.46 
  •• 95.4% of the successes will fall within 2 Standard Deviations
  from  0.29 - i.e., between  0  -  0.63 
  ••• 99.7% of the successes will fall within 3 Standard Deviations
  from  0.29 - i.e., between  0  -  0.80 
</span>
```

The lottery strategy I presented had one _6 of 6_ winner. It clearly beat the random expectation by **one extra standard deviation**!  
The _5 of 6_ case:  

```
<span size="5" face="Courier New" color="#c5b358">  The standard deviation for a lotto event of probability 
  p =  .00000315 (1 in 317136)
  in  3500000  binomial experiments is: 
                     BSD =  3.32
  The expected (theoretical) number of successes is: 11 

  Based on the Normal Probability Rule:

  • 68.2% of the successes will fall within 1 Standard Deviation
  from  11 - i.e., between  8  -  14 
  •• 95.4% of the successes will fall within 2 Standard Deviations
  from  11 - i.e., between  5  -  17 
  ••• 99.7% of the successes will fall within 3 Standard Deviations
  from  11 - i.e., between  2  -  20 
</span>
```

The lottery strategy I presented had 75 _5 of 6_ winners. It beat the random expectation handily: by **16 extra standard deviations**! (75 – 11 = 64; 64 / 3.32 = 19.27 standard deviations. 19.27 – 3 = 16.27 standard deviations beyond the level-3 required by the normal probability rule.)

The wonder grid consists of 69 combinations for a lotto 6/69 game. The wonder grid is expected to hit _5 of 6_ within 69/2 = 35 draws. In this case, number of trials is 69 x 35 = 2,415.

```
<span size="5" face="Courier New" color="#c5b358">
  The standard deviation for a lotto event of probability 
  p =  .00000315 (1 in 317136)
  in  2415  binomial experiments is: 
                     BSD =  0.09
  The expected (theoretical) number of successes is: 0.008 

  Based on the Normal Probability Rule:

  • 68.2% of the successes will fall within 1 Standard Deviation
  from  0.008 - i.e., between  0  -  0.098 
  •• 95.4% of the successes will fall within 2 Standard Deviations
  from  0.008 - i.e., between  0  -  0.188 
  ••• 99.7% of the successes will fall within 3 Standard Deviations
  from  0.008 - i.e., between  0  -  0.278
</span>
```

The lotto wonder grid also beat random expectation: by **8 extra standard deviations** (1 success – 0.008 = 0.992; 0.992 / 0.09 = 11 standard deviations. 11 – 3 = 8 standard deviations beyond the level-3 required by the normal probability rule.)  
The advantage of the _wonder grid_ lottery strategy or system is its much lower _COW_ (_cost of winning_).

## [Resources in Lottery, Software, Systems, Lotto Wheels, Strategies](https://saliu.com/content/lottery.html)

-   The Main [_**Lotto, Lottery, Software, Strategy, Systems**_](https://saliu.com/LottoWin.htm) Page.  
    Presenting software to create free winning lotto, lottery strategies, systems based on mathematics. Get your lotto systems or wheels, the best lottery, lotto software, combinations, winning numbers.
-   [Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies](https://saliu.com/Newsgroups.htm).  
    Read a genuine analysis of Excel spreadsheets applied to lottery and lotto developing of software, systems, and strategies. Combining Excel analysis with powerful lottery and lotto software programmed by this author, _Parpaluck_.
-   [_**<u>MDIEditor Lotto WE</u>: Lottery Software Manual, Book, ebook, Help**_](https://saliu.com/MDI-lotto-guide.html).  
    ~ Also applicable to LotWon lottery, lotto software; plus Powerball/Mega Millions, Euromillions.
-   [_**Manual, Tutorial of <u>Lottery Software</u>, Lotto Apps**_](https://saliu.com/forum/lotto-book.html).
-   "My kingdom for a good lotto tutorial!" [Lotto, Lottery Strategy Tutorial](https://saliu.com/bbs/messages/818.html).
-   [_**Documentation, Help: MDIEditor Lotto WE, Lottery Software, Strategy Tutorial**_](https://saliu.com/mdi_lotto.html).
-   [**Skips Lottery, Lotto, Gambling, Systems, Strategy**](https://saliu.com/skip-strategy.html)
-   [Lottery Utility Software](https://saliu.com/lottery-utility.html): Pick-3, 4 Lottery, Lotto-5, 6, Powerball/Mega Millions/Thunderball, Euromillions.
-   [Practical lottery and lotto filtering in software](https://saliu.com/filters.html).
-   [Analysis of Best Ranges for Lotto Number Frequency, Lottery Pairs, Pairings](https://saliu.com/lottery-lotto-pairs.html).  
    
-   [Updates to the Bright lotto, lottery, and horseracing software](https://saliu.com/forum/software-updates.html) bundles.  
    Updates to several Bright software packages: pick-3 and pick-4 lotteries, horseracing, 5- and 6-number lotto games.
-   [_Markov Chains_ Lottery Software, Lotto, Program, Algorithms](https://saliu.com/Markov_Chains.html).
-   Download [**lottery software, lotto software**](https://saliu.com/infodown.html).
    
    ![Lottery Wonder Grid reports a strategy to beat random play.](https://saliu.com/bbs/messages/HLINE.gif)
    
    ![Lotto systems of pairs beat random expectation consistently by 3 standard deviations.](https://saliu.com/bbs/messages/HLINE.gif)
    
    **[Home](https://saliu.com/index.htm) | [Search](https://saliu.com/Search.htm) | [New Writings](https://saliu.com/bbs/index.html) | [Software](https://saliu.com/infodown.html) | [Odds, Generator](https://saliu.com/calculator_generator.html) | [Contents](https://saliu.com/content/index.html) | [Forums](https://forums.saliu.com/) | [Sitemap](https://saliu.com/sitemap/index.html)**
    
    ![The wonder grid lotto strategy beats lottery random picks by better than 3 standard deviations.](https://saliu.com/bbs/messages/HLINE.gif)
