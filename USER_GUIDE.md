# Wonder Grid 彩票系統用戶指南
# Wonder Grid Lottery System User Guide

## 目錄 / Table of Contents

1. [系統介紹 / System Introduction](#系統介紹--system-introduction)
2. [安裝和設置 / Installation and Setup](#安裝和設置--installation-and-setup)
3. [快速開始 / Quick Start](#快速開始--quick-start)
4. [詳細使用說明 / Detailed Usage Instructions](#詳細使用說明--detailed-usage-instructions)
5. [配置選項 / Configuration Options](#配置選項--configuration-options)
6. [高級功能 / Advanced Features](#高級功能--advanced-features)
7. [故障排除 / Troubleshooting](#故障排除--troubleshooting)
8. [常見問題 / FAQ](#常見問題--faq)

---

## 系統介紹 / System Introduction

### Wonder Grid 策略原理 / Wonder Grid Strategy Principles

Wonder Grid 彩票系統基於Ion Saliu的彩票理論，專門為Pick 5 from 39 (Lotto 5/39)遊戲設計。系統的核心原理包括：

The Wonder Grid Lottery System is based on <PERSON>'s lottery theory, specifically designed for Pick 5 from 39 (Lotto 5/39) games. The core principles include:

1. **FFG計算 (Fundamental Formula of Gambling)**
   - 計算每個號碼的中位數跳躍值
   - 識別最佳時機選擇關鍵號碼
   - Calculate median skip values for each number
   - Identify optimal timing for key number selection

2. **配對頻率分析 / Pairing Frequency Analysis**
   - 分析號碼之間的配對頻率
   - 識別最具統計意義的號碼組合
   - Analyze pairing frequencies between numbers
   - Identify statistically significant number combinations

3. **LIE消除策略 / LIE Elimination Strategy**
   - 消除短期內不太可能中獎的組合
   - 降低投注成本同時保持策略有效性
   - Eliminate combinations unlikely to win in the short term
   - Reduce betting costs while maintaining strategy effectiveness

### 系統架構 / System Architecture

```
Wonder Grid System
├── 核心引擎 / Core Engines
│   ├── Wonder Grid Engine (數學策略 / Mathematical Strategy)
│   ├── LIE Elimination Engine (過濾系統 / Filtering System)
│   └── Backtesting Engine (回測驗證 / Historical Validation)
├── 優化層 / Optimization Layer
│   ├── Performance Optimization (性能優化)
│   └── Concurrent Processing (並發處理)
├── 分析報告 / Analysis & Reporting
│   ├── Statistical Analysis (統計分析)
│   └── Performance Reporting (性能報告)
└── 用戶界面 / User Interface
    ├── Interactive Mode (交互模式)
    ├── Batch Processing (批處理)
    └── Configuration Management (配置管理)
```

---

## 安裝和設置 / Installation and Setup

### 系統要求 / System Requirements

- **Julia版本 / Julia Version**: 1.6 或更高 / 1.6 or higher
- **操作系統 / Operating System**: Windows, macOS, Linux
- **內存 / Memory**: 最少4GB RAM / Minimum 4GB RAM
- **存儲空間 / Storage**: 最少1GB可用空間 / Minimum 1GB free space
- **多線程支持 / Multi-threading**: 可選，用於並發處理 / Optional, for concurrent processing

### 安裝步驟 / Installation Steps

1. **下載系統文件 / Download System Files**
   ```bash
   # 克隆或下載Wonder Grid系統文件
   # Clone or download Wonder Grid system files
   git clone [repository-url]
   cd wonder-grid-system
   ```

2. **驗證Julia安裝 / Verify Julia Installation**
   ```bash
   julia --version
   # 應該顯示Julia 1.6+版本
   # Should display Julia 1.6+ version
   ```

3. **檢查文件結構 / Check File Structure**
   ```
   wonder-grid-system/
   ├── wonder_grid_system.jl      # 主系統文件 / Main system file
   ├── launch_wonder_grid.jl      # 啟動器 / Launcher
   ├── README.md                  # 系統說明 / System documentation
   ├── USER_GUIDE.md             # 用戶指南 / User guide
   ├── src/                      # 核心模塊 / Core modules
   ├── test_*.jl                 # 測試套件 / Test suites
   └── wonder_grid_config.txt    # 配置文件 / Configuration file
   ```

4. **運行系統測試 / Run System Tests**
   ```bash
   julia launch_wonder_grid.jl test
   # 選擇 "1. Quick system test" 進行快速測試
   # Select "1. Quick system test" for quick validation
   ```

---

## 快速開始 / Quick Start

### 方法一：一鍵啟動 / Method 1: One-Click Launch

```bash
julia launch_wonder_grid.jl quick
```

這將：
- 使用默認配置初始化系統
- 加載示例歷史數據
- 使用關鍵號碼13執行Wonder Grid工作流程
- 生成並導出彩票組合

This will:
- Initialize system with default configuration
- Load sample historical data
- Execute Wonder Grid workflow with key number 13
- Generate and export lottery combinations

### 方法二：交互模式 / Method 2: Interactive Mode

```bash
julia launch_wonder_grid.jl interactive
```

然後按照菜單提示操作：
1. 選擇 "1. Execute Wonder Grid Workflow"
2. 輸入關鍵號碼 (1-39) 或按Enter自動選擇
3. 查看生成的組合和分析報告

Follow the menu prompts:
1. Select "1. Execute Wonder Grid Workflow"
2. Enter key number (1-39) or press Enter for auto-selection
3. Review generated combinations and analysis reports

### 方法三：主啟動器 / Method 3: Main Launcher

```bash
julia launch_wonder_grid.jl
```

這將顯示完整的啟動菜單，包括所有可用選項。

This displays the complete launcher menu with all available options.

---

## 詳細使用說明 / Detailed Usage Instructions

### 1. 系統初始化 / System Initialization

#### 配置設置 / Configuration Setup

```bash
julia launch_wonder_grid.jl config
```

選擇配置選項：
- **1. 交互式配置設置** - 逐步引導配置
- **2. 加載配置預設** - 使用預定義配置
- **3. 顯示當前配置** - 查看現有設置
- **4. 驗證配置文件** - 檢查配置有效性

Configuration options:
- **1. Interactive configuration setup** - Step-by-step guided configuration
- **2. Load configuration preset** - Use predefined configurations
- **3. Display current configuration** - View existing settings
- **4. Validate configuration file** - Check configuration validity

#### 配置預設 / Configuration Presets

- **beginner (初學者)**: 簡化設置，適合新用戶
- **standard (標準)**: 平衡配置，推薦使用
- **advanced (高級)**: 全面分析，包含所有功能
- **performance (性能)**: 優化速度和效率

### 2. 歷史數據管理 / Historical Data Management

#### 加載數據 / Loading Data

在主菜單中選擇 "2. Load Historical Data":

1. **生成示例數據 / Generate Sample Data**
   - 創建模擬的歷史彩票數據用於測試
   - 可指定生成的抽獎次數 (默認200次)

2. **從文件加載 / Load from File**
   - 支持DATA5格式文件
   - 每行包含5個號碼 (1-39範圍)
   - 按時間順序排列

3. **使用當前數據 / Use Current Data**
   - 繼續使用已加載的歷史數據

#### 數據格式要求 / Data Format Requirements

```
# DATA5格式示例 / DATA5 Format Example
1,5,12,23,39
3,7,15,28,35
2,11,19,31,37
4,9,16,25,33
6,13,20,29,38
```

要求 / Requirements:
- 每行恰好5個號碼 / Exactly 5 numbers per line
- 號碼範圍1-39 / Numbers in range 1-39
- 用逗號分隔 / Comma-separated
- 按時間順序 (最新在前) / Chronological order (newest first)

### 3. Wonder Grid工作流程 / Wonder Grid Workflow

#### 執行完整工作流程 / Execute Complete Workflow

選擇主菜單中的 "1. Execute Wonder Grid Workflow":

**步驟1: 關鍵號碼選擇 / Step 1: Key Number Selection**
- 手動輸入 (1-39) / Manual input (1-39)
- 自動選擇 (基於FFG分析) / Auto-selection (based on FFG analysis)

**步驟2: 組合生成 / Step 2: Combination Generation**
- 計算FFG序列 / Calculate FFG sequence
- 生成所有可能的5號碼組合 / Generate all possible 5-number combinations
- 顯示生成的組合數量 / Display number of combinations generated

**步驟3: LIE消除 (可選) / Step 3: LIE Elimination (Optional)**
- 分析歷史模式 / Analyze historical patterns
- 消除不太可能中獎的組合 / Eliminate unlikely winning combinations
- 顯示過濾前後的組合數量 / Show before/after combination counts

**步驟4: 回測分析 / Step 4: Backtesting Analysis**
- 使用歷史數據驗證策略 / Validate strategy against historical data
- 計算命中率 (3/5, 4/5, 5/5) / Calculate hit rates (3/5, 4/5, 5/5)
- 與隨機選擇比較效率 / Compare efficiency with random selection

**步驟5: 性能報告 / Step 5: Performance Reporting**
- 生成統計分析報告 / Generate statistical analysis report
- 計算理論與實際概率 / Calculate theoretical vs empirical probabilities
- 提供投資回報分析 / Provide return on investment analysis

**步驟6: 結果導出 / Step 6: Result Export**
- 多種格式導出 (CSV, TXT, JSON) / Export in multiple formats (CSV, TXT, JSON)
- 包含元數據和分析信息 / Include metadata and analysis information
- 自動生成文件名 / Auto-generate filenames

### 4. 批處理模式 / Batch Processing Mode

```bash
julia launch_wonder_grid.jl batch
```

批處理模式允許同時處理多個關鍵號碼：
- 自動處理預定義的關鍵號碼列表
- 生成每個關鍵號碼的完整報告
- 提供批處理摘要和比較分析

Batch processing mode allows processing multiple key numbers simultaneously:
- Automatically process predefined list of key numbers
- Generate complete reports for each key number
- Provide batch summary and comparative analysis

---

## 配置選項 / Configuration Options

### 策略參數 / Strategy Parameters

```julia
# 關鍵號碼設置 / Key Number Settings
config.key_number = 13                    # 手動指定關鍵號碼 / Manual key number
config.auto_key_selection = true          # 自動選擇關鍵號碼 / Auto key selection
config.key_selection_method = "optimal"   # 選擇方法 / Selection method
```

選擇方法選項 / Selection Method Options:
- **"optimal"**: 基於FFG分析的最優選擇
- **"random"**: 隨機選擇
- **"sequential"**: 順序選擇
- **"manual"**: 手動指定

### 分析參數 / Analysis Parameters

```julia
# 分析深度 / Analysis Depth
config.analysis_depth = "comprehensive"   # "basic", "standard", "comprehensive"

# LIE消除設置 / LIE Elimination Settings
config.include_lie_elimination = true     # 啟用LIE消除 / Enable LIE elimination
config.lie_threshold = 0.1                # LIE閾值 (0.0-1.0) / LIE threshold
```

### 性能參數 / Performance Parameters

```julia
# 並發處理 / Concurrent Processing
config.enable_parallel_processing = true  # 啟用並行處理 / Enable parallel processing
config.max_threads = 4                    # 最大線程數 / Maximum threads

# 緩存設置 / Cache Settings
config.cache_enabled = true               # 啟用緩存 / Enable caching
config.cache_size_mb = 100               # 緩存大小 (MB) / Cache size (MB)
```

### 輸出參數 / Output Parameters

```julia
# 導出格式 / Export Formats
config.output_format = ["csv", "txt", "json"]  # 支持的格式 / Supported formats
config.output_directory = "results"            # 輸出目錄 / Output directory
config.include_metadata = true                 # 包含元數據 / Include metadata
config.show_progress = true                    # 顯示進度 / Show progress
```

### 顯示參數 / Display Parameters

```julia
# 顯示模式 / Display Mode
config.display_mode = "detailed"              # "compact", "detailed", "interactive"
config.max_combinations_display = 100         # 最大顯示組合數 / Max combinations to display
config.color_output = true                    # 彩色輸出 / Color output
```

---

## 高級功能 / Advanced Features

### 1. 性能分析 / Performance Analysis

#### 單一關鍵號碼分析 / Single Key Analysis

在主菜單選擇 "4. Performance Analysis" → "1. Single key analysis":

```
輸入關鍵號碼: 13
正在運行關鍵號碼13的分析...

統計報告包括:
- 理論與實際概率比較
- 置信區間和顯著性檢驗
- 期望值分析和投資回報率
- 風險分析和性能指標
```

#### 比較分析 / Comparative Analysis

選擇 "2. Comparative analysis" 比較多個關鍵號碼的性能:

```
正在運行比較分析...
測試關鍵號碼: 7, 13, 21, 29

性能摘要:
關鍵號碼 | 3/5命中率 | 4/5命中率 | 5/5命中率 | 最佳比率 | 評估
   7    |   2.150%  |   0.180%  |   0.015%  |   3.2x   | 良好
  13    |   2.890%  |   0.240%  |   0.025%  |   4.1x   | 很好
  21    |   1.950%  |   0.160%  |   0.010%  |   2.8x   | 一般
  29    |   2.650%  |   0.220%  |   0.020%  |   3.8x   | 良好
```

#### 系統基準測試 / System Benchmark

選擇 "3. System benchmark" 測試系統性能:

```
基準測試結果:
加速因子: 2.35x
改進: 57.4%
標準引擎平均時間: 145.23 ms
優化引擎平均時間: 61.87 ms
```

### 2. 並發處理 / Concurrent Processing

#### 啟用並發處理 / Enable Concurrent Processing

```julia
# 在配置中啟用
config.enable_parallel_processing = true
config.max_threads = min(8, nthreads())
```

#### 並發性能優勢 / Concurrent Performance Benefits

- **組合生成**: 多線程工作分配
- **回測分析**: 並行處理組合-抽獎匹配
- **統計計算**: 並發統計分析
- **緩存管理**: 線程安全的緩存操作

### 3. 高級導出選項 / Advanced Export Options

#### 批量導出 / Batch Export

```julia
# 同時導出多種格式
exported_files = batch_export_combinations(
    combinations,
    "wonder_grid_key_13",
    ["csv", "txt", "json"],
    metadata
)
```

#### 自定義元數據 / Custom Metadata

```julia
metadata = Dict{String, Any}(
    "key_number" => 13,
    "generation_method" => "Wonder Grid",
    "lie_elimination" => true,
    "analysis_date" => string(Dates.now()),
    "total_combinations" => length(combinations),
    "user_notes" => "高性能配置測試"
)
```

### 4. 系統監控 / System Monitoring

#### 查看系統狀態 / View System Status

在主菜單選擇 "5. System Status":

```
Wonder Grid 系統狀態
========================================
系統信息:
  已初始化: true
  初始化時間: 1.234s
  Julia版本: 1.8.5
  可用線程: 8

配置:
  分析深度: comprehensive
  LIE消除: true
  並行處理: true
  輸出格式: csv, txt, json

數據狀態:
  歷史抽獎: 200
  日期範圍: 2020-01-01 to 2020-07-19

引擎狀態:
  Wonder Grid引擎: ✅ 就緒
  LIE引擎: ✅ 就緒
  回測引擎: ✅ 就緒
  優化引擎: ✅ 就緒
  並發引擎: ✅ 就緒

緩存狀態:
  總緩存項目: 45
  FFG緩存: 12 條目
  並發緩存: 33 條目
```

---

## 故障排除 / Troubleshooting

### 常見問題和解決方案 / Common Issues and Solutions

#### 1. "未生成組合" / "No combinations generated"

**問題**: 某些關鍵號碼可能不會生成組合
**原因**: 關鍵號碼的FFG序列可能不足以生成5號碼組合
**解決方案**:
```julia
# 嘗試不同的關鍵號碼
key_numbers = [7, 13, 21, 29, 31]
for key in key_numbers
    combinations = generate_combinations(engine, key)
    if !isempty(combinations)
        println("關鍵號碼 $key 生成了 $(length(combinations)) 個組合")
        break
    end
end
```

#### 2. "配置驗證失敗" / "Configuration validation failed"

**問題**: 配置文件包含無效設置
**解決方案**:
```bash
# 使用交互式配置設置
julia launch_wonder_grid.jl config
# 選擇 "1. Interactive configuration setup"

# 或使用預設配置
julia launch_wonder_grid.jl config
# 選擇 "2. Load configuration preset" → "standard"
```

#### 3. "歷史數據不足" / "Insufficient historical data"

**問題**: 加載的歷史數據少於最小要求
**解決方案**:
```julia
# 生成更多示例數據
julia launch_wonder_grid.jl
# 選擇 "2. Load Historical Data" → "1. Generate sample data"
# 輸入更大的數字，如 500
```

#### 4. 性能問題 / Performance Issues

**慢速組合生成 / Slow Combination Generation**:
```julia
# 啟用性能優化
config.enable_parallel_processing = true
config.cache_enabled = true
config.max_threads = min(8, nthreads())
```

**高內存使用 / High Memory Usage**:
```julia
# 減少批處理大小
config.cache_size_mb = 50  # 減少緩存大小
# 或使用基本分析深度
config.analysis_depth = "basic"
```

#### 5. 文件導出問題 / File Export Issues

**無法創建輸出文件 / Cannot Create Output Files**:
```julia
# 檢查輸出目錄權限
config.output_directory = "results"  # 確保目錄存在且可寫
```

**文件格式錯誤 / File Format Errors**:
```julia
# 使用支持的格式
config.output_format = ["csv", "txt", "json"]  # 僅使用支持的格式
```

### 診斷工具 / Diagnostic Tools

#### 快速系統測試 / Quick System Test

```bash
julia launch_wonder_grid.jl test
# 選擇 "1. Quick system test"
```

這將測試:
- 配置驗證
- 組合生成
- LIE消除 (如果可用)
- 回測功能
- 導出功能

#### 全面測試套件 / Comprehensive Test Suite

```bash
julia run_all_tests.jl
```

運行所有測試套件:
- 單元測試
- 集成測試
- 性能測試
- 系統驗證測試

---

## 常見問題 / FAQ

### Q1: 什麼是Wonder Grid策略？ / What is the Wonder Grid strategy?

**A**: Wonder Grid是基於Ion Saliu彩票理論的數學策略，通過分析號碼配對頻率和FFG計算來生成優化的彩票組合。它專門為Pick 5 from 39遊戲設計，旨在提高中獎概率。

**A**: Wonder Grid is a mathematical strategy based on Ion Saliu's lottery theory that generates optimized lottery combinations by analyzing number pairing frequencies and FFG calculations. It's specifically designed for Pick 5 from 39 games to improve winning odds.

### Q2: LIE消除如何工作？ / How does LIE elimination work?

**A**: LIE (Loss Into Elimination) 通過分析歷史模式識別短期內不太可能中獎的組合，然後從主要遊戲集合中消除這些組合，從而降低成本同時保持策略有效性。

**A**: LIE (Loss Into Elimination) works by analyzing historical patterns to identify combinations unlikely to win in the short term, then eliminating these combinations from the main playing set, reducing costs while maintaining strategy effectiveness.

### Q3: 系統需要多少歷史數據？ / How much historical data does the system need?

**A**: 系統建議至少50次歷史抽獎數據以進行有效分析。更多數據 (100-500次抽獎) 將提供更準確的統計分析和更可靠的回測結果。

**A**: The system recommends at least 50 historical draws for effective analysis. More data (100-500 draws) will provide more accurate statistical analysis and more reliable backtesting results.

### Q4: 如何選擇最佳關鍵號碼？ / How to choose the best key number?

**A**: 使用自動選擇模式 (config.auto_key_selection = true) 讓系統基於FFG分析選擇最優關鍵號碼。您也可以運行比較分析來評估多個關鍵號碼的性能。

**A**: Use auto-selection mode (config.auto_key_selection = true) to let the system choose optimal key numbers based on FFG analysis. You can also run comparative analysis to evaluate performance of multiple key numbers.

### Q5: 系統可以處理多大的數據集？ / How large datasets can the system handle?

**A**: 系統設計可處理100,000+彩票組合。使用性能優化和並發處理功能可以高效處理大型數據集。對於極大數據集，建議使用批處理模式。

**A**: The system is designed to handle 100,000+ lottery combinations. Use performance optimization and concurrent processing features for efficient handling of large datasets. For extremely large datasets, batch processing mode is recommended.

### Q6: 如何解釋統計報告？ / How to interpret statistical reports?

**A**: 統計報告包含多個關鍵指標:
- **命中率**: 3/5, 4/5, 5/5匹配的概率
- **效率比率**: 與隨機選擇相比的改進倍數
- **期望值**: 每次投注的預期回報
- **置信區間**: 結果的統計可信度範圍

**A**: Statistical reports contain several key metrics:
- **Hit Rates**: Probabilities of 3/5, 4/5, 5/5 matches
- **Efficiency Ratios**: Improvement multiplier compared to random selection
- **Expected Value**: Expected return per bet
- **Confidence Intervals**: Statistical reliability range of results

### Q7: 系統是否保證中獎？ / Does the system guarantee winning?

**A**: **不保證**。Wonder Grid系統是一個統計分析工具，旨在通過數學策略提高中獎概率，但不能保證中獎。彩票本質上是隨機的，任何策略都無法保證結果。

**A**: **No guarantees**. The Wonder Grid system is a statistical analysis tool designed to improve winning odds through mathematical strategies, but cannot guarantee wins. Lotteries are inherently random, and no strategy can guarantee results.

### Q8: 如何更新系統？ / How to update the system?

**A**: 系統是模塊化設計的。要更新:
1. 備份當前配置文件
2. 下載新版本文件
3. 運行系統測試驗證功能
4. 恢復您的自定義配置

**A**: The system is modularly designed. To update:
1. Backup current configuration files
2. Download new version files
3. Run system tests to verify functionality
4. Restore your custom configurations

### Q9: 可以自定義分析參數嗎？ / Can I customize analysis parameters?

**A**: 是的。系統提供廣泛的自定義選項:
- LIE閾值調整 (0.0-1.0)
- 分析深度選擇 (basic/standard/comprehensive)
- 並發處理設置
- 導出格式和元數據
- 顯示選項和進度指示器

**A**: Yes. The system provides extensive customization options:
- LIE threshold adjustment (0.0-1.0)
- Analysis depth selection (basic/standard/comprehensive)
- Concurrent processing settings
- Export formats and metadata
- Display options and progress indicators

### Q10: 如何獲得技術支持？ / How to get technical support?

**A**: 技術支持選項:
1. 查看故障排除部分
2. 運行系統診斷測試
3. 檢查測試結果以識別錯誤
4. 查閱源文件中的詳細文檔

**A**: Technical support options:
1. Check the troubleshooting section
2. Run system diagnostic tests
3. Review test results for error identification
4. Consult comprehensive documentation in source files

---

## 結論 / Conclusion

Wonder Grid彩票系統是一個功能強大且全面的工具，結合了先進的數學策略、統計分析和用戶友好的界面。通過遵循本用戶指南，您可以有效地使用系統的所有功能來分析彩票模式並生成優化的號碼組合。

The Wonder Grid Lottery System is a powerful and comprehensive tool that combines advanced mathematical strategies, statistical analysis, and user-friendly interfaces. By following this user guide, you can effectively utilize all system features to analyze lottery patterns and generate optimized number combinations.

記住，雖然系統使用先進的數學方法來提高中獎概率，但彩票仍然是一個機會遊戲。請負責任地遊戲，只投注您能承受損失的金額。

Remember that while the system uses advanced mathematical methods to improve winning odds, lottery remains a game of chance. Please play responsibly and only bet amounts you can afford to lose.

---

**Wonder Grid彩票系統 v1.0** - 先進的彩票分析和組合生成系統

**Wonder Grid Lottery System v1.0** - Advanced lottery analysis and combination generation system