---
created: 2025-07-24T21:26:41 (UTC +08:00)
tags: [strategy,system,strategies,systems,lottery,lotto,sum,sums,sum-totals,odd,even,low,high,software,eliminate,filter,filtering,combinations]
source: https://saliu.com/strategy.html
author: 
---

# Odd Even, Low High, Sums: Lottery Strategy, Lotto Systems

> ## Excerpt
> This lotto strategy is based on numbers that are even or odd, low or high, sums, sum-totals of lotto, lottery combinations. These are STATIC lottery filters.

---
![This is the best lottery strategy on even odd, low, high, sums, sum totals in lotto.](https://saliu.com/HLINE.gif)

### I. [Introduction to Lottery Strategies, Filtering, Number Grouping](https://saliu.com/strategy.html#Strategy)  
II. [Pick-3 Lottery Software for _Low_ or _High_, _Odd_ or _Even_ Digit Grouping](https://saliu.com/strategy.html#Pick3)  
III. [Lotto Software for _Low / High, Odd / Even_ Numbers, Plus _Lotto Skipping_](https://saliu.com/strategy.html#Lotto)  
IV. [True Lottery Filters, Filtering to Create the Best Lotto, Lottery Strategies, Systems](https://saliu.com/strategy.html#Software)  
V. [Essential Resources in Lotto, Lottery Strategy, Systems, Software](https://saliu.com/strategy.html#Links)

![Lottery lotto strategy, system on sum, odd, even, low, high numbers created by best software.](https://saliu.com/HLINE.gif)

° **SUMS** ° version 9.0, January 2011;  
° **UserGroups5/6** ° version 2.0, August 2011;  
° **BellCurveGenerator** ° version 10.1, September 16, 2003;  
° **Bright3/4** ° version 3.4, August 2011.  
The most comprehensive pick 3, 4 lottery software packages in the world — no lottery software comes remotely close.

## <u>1. Introductory Notes to Lottery Strategies, Filtering and Number Grouping</u>

I landed in a lottery message board named _Lottery Post_. Visitors to my Web site had difficulties downloading my unique lottery software. One problem caught my attention. My Web server had become extremely busy issuing _File not found_ errors (_404_). One poster at _lottery post_ had the bad idea of linking to one of my graphics files in his signature!

I followed the hot-linking referrers and arrived in a lottery post thread dealing with lottery systems and strategies. One poster raised the idea of my posting there. And so I posted a few of my own remarks on the concept of **lottery strategizing** (i.e. developing lottery or lotto strategies).

I was the very first lottery software developer to use the term _strategy_ to describe a set of filter values in my lottery software. People now give the term a broader meaning. Any method of picking lottery numbers, including random _quick-picks_, is a lottery strategy. I also was the first lottery analyst to use the term _filter or filtering_ in lotto software.

Years after I introduced the strategy and filtering, most other lotto software developers started to use the word “filters” in their software or systems. They consider as filters: _odd / even_ numbers, _low / high_ numbers, in addition to _sum-totals_. These are NOT lotto filters, and that's NOT lottery filtering. It's simply a way of grouping lotto numbers. There is a fixed group of even lotto numbers. The number of combinations in the group stays always the same. It's a large number.

_Grouping the lotto numbers by **odd/even** and/or **low/high** leads to an impractical number of lotto combinations to play._

You can find various formulas I coined at this web site. (Read: [_**Software, formulas to calculate lotto odds, <u>hypergeometric distribution probability</u>**_](https://saliu.com/oddslotto.html). Not to mention that the large fixed groups of _odd/even_, etc. lotto numbers are very STREAKY! A player (or groups of lottery players) could easily spend 100 million to win one million!

The lottery post thread I landed in dealt with the pick-3 sums as the pivot of a lottery strategy. There is a huge effort invested in the sum as a strategy! I got tired just reading a few posts. I asked myself: _"Self, why so much effort? Does it work better than purely random selection when playing the lottery?"_

[![Lottery Software: Jackpot Lotto, Pick Lotteries, Powerball, Mega Millions, Euromillions, Keno.](https://saliu.com/ScreenImgs/lottery-sums.gif)](https://saliu.com/free-lotto-lottery.html)

At least, using my lottery software **SUMS** could diminish the effort. The special lottery program covers a huge territory — just about every lottery game, including the Powerball, Mega Millions, Euromillions lotto games. Among other features, the program calculates the **sum-totals** and the **root-sums** of the draws in a lottery drawing file. Following is a fragment for the pick-3 results file in Pennsylvania lottery.

![Software calculates root sums, fadic addition for correct lottery strategies based on sums.](https://saliu.com/ScreenImgs/lottery-root-sums.gif)

The last column represents the _root sums_ or _Fadic addition_. That is, the regular sum-totals are reduced to single-digit numbers by adding the digits. For example, a regular sum-total like 97 is further operated on: 9 + 7 = 16; 1 + 6 = 7; the root sum of 97 is 7. The _Fadic addition_ feature is present in the latest version of the utility functions (_**Util\***_) in my lottery software.

Note that 13 and 14 have the highest representation combination-wise. Each sum-total consists of 75 pick-3 straight lottery sets. Lottery players consider the sum-total to be a lottery / lotto filter. In fact, it is not. This is what I call _grouping of numbers_. The 75 lottery combinations adding up to 13 represent a group of pick-3 straight combinations. The group never changes. Other types of grouping are based on _even_ or _odd_ numbers, _high_ or _low_ numbers, etc. A particular lottery number-group remains always the same — it can't be filter-reduced.

![This is the best lotto, lottery strategy on sums, sum, even odd, high low.](https://saliu.com/images/lottery-software.gif)

## <u>2. Pick-3 Lottery Software, <i>Low</i> or <i>High</i>, <i>Odd</i> or <i>Even</i> Digits</u>

I have such low regards for grouping the lottery numbers or digits by _sums_, _low/high_, _odd/even_! Nevertheless, my pick-3 free software offers the users the option to work with grouping the pick 3 digits. The 7th statistical report, W3.7 is the representation of grouping the pick-3 lottery digits by low/high, odd/even.

• Regarding the low/high grouping: 1 represents the low digits: 0, 1, 2, 3, 4; 2 represents the high digits: 5, 6, 7, 8, 9.  
A string like 121 represents a pick-3 drawing consisting of low-high-low; e.g. 2-7-4.  
• Regarding the odd/even grouping: 1 represents the odd digits: 1, 3, 5, 7, 9; 2 represents the even digits: 0, 2, 4, 6, 8.  
A string like 212 represents a pick-3 drawing consisting of even-odd-even; e.g. 4-3-8.

My pick-3 lottery software has a huge advantage over all other lotto programs that rely on grouping the lotto numbers. **Bright** offers strategy checking; it shows when a particular lotto strategy hit in the past, and also the skips of the hits. It is a tremendous visual help for the lottery player.

_**LotWon**_ pick-3 lottery software can generate every possible pick-3 combination based on a particular digit grouping. For example, a 121 grouping for low/high will generate all 125 pick-3 combinations; a 211 grouping for odd/even will generate all 125 pick-3 straight combinations. How about combining the two groupings? A grouping such as 121212 represents low-high-low & even-odd-even. SuperPower will generate 27 pick 3 straight combinations for that digit grouping.

The _all-low-all-odd_ digit setting (_111-111_) always generates _8_ straight pick-3 lotteries combinations. There are exactly two digits satisfying the condition: 1 and 3. Two to the power of three equals eight. 111, 113, … 331, 333.

The _all-high-all-even_ digit setting (_222-222_) always generates _8_ straight pick-3 combinations. There are exactly two lottery digits satisfying the condition: 6 and 8. Two to the power of three equals eight. 666, 668, … 886, 888.

The digit setting 111-111 hit 12 times in 1000 pick 3 drawings. But the digit setting 222-222 hit 7 times in 1000 pick-3 drawings. No further reduction is possible in this so-called lottery strategy. A TRUE lottery strategy must be put in place to achieve further reduction in the amount of tickets to play.

One quick _Saliusian strategy_ is on the same page with the **odd/even** and **high/low** groupings: the INCREASE/DECREASE filters. They are true lottery filters, because they are dependent on the results (drawings) file. You'll notice that, most of the time, the 111111 setting is accompanied by a 111 setting (all 3 digits decrease). The 222222 setting is accompanied by a 222 setting (all 3 digits increase) in a majority of the situations. Also, there are the skips. That is, there are gaps between the winning situations of the 111111 or 222222 settings.

• Note: The strategy checking for **odd-even, low-high, increase-decrease** is _now enabled in all my lottery software packages named **Bright\*** There is also potent lotto software dedicated to sums, frequencies, odd even, low high, groups of lotto numbers: **UserGroups 5/6**._

Grouping of lotto numbers or lottery digits is never sufficient. You can never reach efficient results by implementing just one or two number-grouping restrictions. Also, applying the sum-total as a restriction never leads to zero combinations. It's always 75 combinations, if a sum-total equal to 13 is imposed as a restrictive condition (a false lottery filter). Further filtering is required; true lottery filtering is needed. The good news is there are tools out there to truly improve lottery filtering. My software download site is a goldmine of lotto and lottery free-to-run software.

![This is the best lotto, lottery strategy software by founder of lotto mathematics.](https://saliu.com/images/lotto-software.gif)

## <u>3. Lotto Software for <i>Low-High</i>, <i>Odd-Even</i> Numbers, Plus <i>Skipping</i></u>

The pick-3 grouping of digits has a counterpart in lotto software. The lotto numbers can be grouped by such criteria as low/high, odd/even, increase/decrease. Here are exemplifying reports.

Interesting fact. The lotto number in the first position is LOW (1), virtually in every drawing. The lotto number in the last position is HIGH (2), virtually in every drawing. The lotto number in the second position is LOW (1) in the vast majority of the drawings. The lotto number in the second-to-last position is HIGH (2) in the vast majority of the drawings. That leaves one lotto-5 number to hustle with (or two middle numbers in the lotto-6 game).

```
<span size="5" face="Courier New" color="#c5b358">         LOTTO-5 <i>Low - High, Odd - Even</i> Report
         File: C:\LOTTERY\LOTTO5\LOTTO-5
         Draws Analyzed:  100 

 Line    L/H  L/H  L/H  L/H  L/H     O/E  O/E  O/E  O/E  O/E
  no      1    2    3    4    5       1    2    3    4    5 

  1 means LOW = 1 to 19               1 means ODD =  1  3  5 
  2 means HIGH =  20  to 39           2 means EVEN = 2  4  6 

    1     1    1    2    2    2       1    2    1    2    1 
    2     1    2    2    2    2       2    2    2    2    2 
    3     1    1    2    2    2       1    2    1    1    2 
    4     1    1    2    2    2       1    2    2    1    1 
    5     1    1    1    2    2       1    1    1    1    1
...
</span>
```

![Best lottery software for odd even, low high, sums, sum-totals, root sums, number increase decrease.](https://saliu.com/ScreenImgs/lottery-increase.gif)

The **increase / decrease** lotto setting is actually a **true** lotto filter (dynamic). The filter is **data-dependent**.

![The lotto skips are true filters for effective lottery strategies, systems.](https://saliu.com/ScreenImgs/lottery-skips-filters.gif)

The **skips** are true lotto / lottery filters. _Any_ represent the skips regardless of position, while _Pos_ reflect the skips by position (**vertically**; somehow like in the DIEditor Lotto application

Some numbers regarding the **even/odd - high/low** grouping for a lotto 5/39 game.

-   The setting all-low AND all-odd numbers always generates 252 lotto 5/39 combinations.
-   The setting all-high AND all-even numbers always generates 252 lotto 5/39 combinations.
-   The setting all-low AND all-even numbers always generates 126 lotto 5/39 combinations.
-   The setting all-high AND all-odd numbers always generates 252 lotto 5/39 combinations.
-   The setting low-high-low-high-low AND even-odd-even-odd-even (12121+21212) generates NO lotto 5/39 combinations.
-   The setting high-low-high-low-high AND odd-even-odd-even-odd (21212+12121) generates NO lotto 5/39 combinations.

The formulas on the _oddslotto.html_ page do NOT apply to this type of my lottery software. Those formulae applied to lotto combinations regardless of order or position. The formula for a case such as: all-even AND all-high summed-up ALL positional cases of a lotto combination. In the case of this type of lotto software, each odd-even AND low-high case is treated independently. Thus, {high-high-high} AND {even-even-even} is different from {high-even-high} AND {even-high-even}.

_The lottery software that accomplishes the feats above is **not freeware**. Look at some reasons why you won't get such software for free. There are lottery bandits out there, especially in cyber space. They call themselves lotto software developers or lottery systems developers. Actually, they do nothing but two despicable acts. The “systems authors” attempt to rip people off their hard-earned money. The “developers” exploit people's intense hope of striking it rich via winning the lottery. The second bad thing they do: The “systems authors” attempt to pirate my software and systems and/or steal my ideas._

_I received information that the pick-3 odd/even/high/low strategy I presented above was already pirated. The **W3.7** report in my **pick 3 lottery** software became a pencil-and-paper strategy sold for an outrageous fee! I heard that at least one “developer” simply pirated the W3.7 reporting. For a high price, the users have to do the hard work manually — and without strategy checking! I wrote about one of those “lottery systems authors” at this website: [_**Martingale Pick-3, Anyone? Flawed Lottery Systems by Steve Player**_](https://saliu.com/bbs/messages/188.html)._

If I had released the free _**pick 4 lottery**_ software, you would find pirated lottery pick-4 systems based on the odd/even/high/low strategy I presented above. Should I release as freeware the odd/even/high/low reporting software for the lotto-5 or lotto-6 games — more pirated lotto systems would invade cyber space. Lotto or lottery strategies independent from data (results, history) files are the easiest to pirate. And, of course, the pirates do not work for free. They honest players pay with an arm and a leg for lottery software or lotto systems the pirates stole!

Nota bene: I did release pick 3, 4 software big time! Download the integrated pick 3, 4 lottery software package PICK\*32 and especially <big>Bright*</big>.

![This lottery strategy is static because it always generates the same lotto combinations.](https://saliu.com/images/lottery-software.gif)

## <u>4. True Lottery Filters, Filtering to Create the Best Lotto, Lottery Strategies, Systems</u>

The true lottery filtering is as implemented in my lotto software (free to run forever, but downloading the software requires a reasonable membership fee). The filters yield groups of lottery combinations that vary from draw to drawing. Tight filters yield sometimes no combinations at all. That's the fundamental test of a filter. If a restriction does not lead, under some circumstances, to zero lotto combinations — the restricting condition is not a filter.

There is a _bias_ alright in randomness, including as applied to lotto and lottery. I discovered that any bias is determined by the _**Fundamental Formula of Gambling (FFG)**_. The winning lottery combinations come with preponderance from inside the area I call the _FFG median bell_. Read a presentation accompanied by real-life data: [_**Winning Gambling Combinations: Inside Median Bell (Gauss)**_](https://saliu.com/random-picks.html).

Some of the pick 3 lotto combinations adding up to 13 (or 14) belong inside the FFG median bell; but other combinations summing up to 13 are outside the FFG median bell! Run SUMS for sum-totals 12, 13, 14, 15. The streaking improves when more totals are used. The program lets you generate the combinations of one sum at a time. You can have output files you would name SUM-12, SUM-13, SUM-14, and SUM-15. You would have to combine them in a single output file, SUM3. You can use a text editor to load all lottery output files in one. But be careful not to leave any blank line in the final file. The error-free method requires some effort (typing at the command line):

**COPY SUM-12+SUM-13+SUM-14+SUM-15 SUM3** (an automatic function in the Bright software packages: _Concatenate files_).

Now, you are ready for filtering — real lottery filtering! The SUM3 file will be used as input to my _Command prompt_ software **LotWon** (or **SuperPower**) and my Windows **MDIEditor Lotto WE** application. You will purge SUM3; i.e. you will reduce the number of combinations in SUM-3 by enabling real filters. My lottery software has this special feature named _**Purge**_. It takes any text (plain vanilla) file of combinations and eliminates some of its content via true lottery filtering.

Of course, the lotto combinations restricted by _low/high, odd/even_ grouping can be further filtered as well. They can be also combined with combinations restricted by the sum grouping. Purging in lottery is a great thing, as opposed to political purging!

You need first the _winning reports_ (the statistical _WS_ files) generated by my lotto software. The reports show how the filters performed in the past. That is, the winning reports show what values the filters reached for every draw in the range of analysis. The winning reports have a great gambling feature notified by +/- (plus/minus). If a filter is larger than its previous state, a + is added; if the filter decreased from its previous draw, a - sign is added. You will notice that the lotto filters are preponderantly single-streaked; i.e. one + followed by one -, or one – followed by one +. You'll also notice that streaks longer than 3 are rare.

This is the truth. I am not gonna hide behind it. Only my lottery software is capable of true filtering. Some people voiced this concern. If everybody uses my lottery software only — the development would stop. Yes, they are right. I wish others developed lottery and gambling software in earnest. I, too, would benefit. But I would never ask for the source code, as others are (actually, some rudely attempt to rob me!) Unfortunately, there is no worthy lottery software out there to inspire me. The _other_ lotto software waste people's time with useless features such as wheeling, static frequency, grouping of numbers on high or low and odd or even. That's about it — and it leads to nowhere.

Even if such lottery software packages were completely free, you would waste a large amount of time for nothing. You will be better served by simply playing random lotto combinations. You can get the best in random generation by running my online random numbers/combinations generator. Better still, you should always keep in mind **Bell Curve Generator**.

![Resources, links to the best in lotto software, lottery strategies.](https://saliu.com/HLINE.gif)

[

## 5\. Resources in Lottery Software, Systems, Strategies, Lotto Wheeling

](https://saliu.com/content/lottery.html)

-   _**Introduction to**_ [**Lottery Mathematics**](https://saliu.com/gambling-lottery-lotto/lottery-math.htm): _**Probabilities, Appearance, Repeat, Affinity or Number Affiliation, Wheels, Systems, Strategies.**_
-   The Starting Strategy Page: [_**Lottery Software, Strategy, Systems**_](https://saliu.com/LottoWin.htm).  
    Presenting software to create free winning lotto, lottery strategies, systems based on mathematics. Get your lotto systems or wheels, the best lottery, lotto software, combinations, winning numbers.
-   [_**Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies**_](https://saliu.com/Newsgroups.htm).  
    Read a genuine analysis of Excel spreadsheets applied to lottery and lotto developing of software, systems, and strategies. Combining Excel analysis with powerful lottery and lotto software programmed by this author, _Parpaluck_.
-   [_**<u>MDIEditor Lotto WE</u>: Lottery Software Manual, Book, ebook, Help**_](https://saliu.com/MDI-lotto-guide.html).  
    ~ Also applicable to LotWon lottery, lotto software; plus Powerball, Mega Millions, Euromillions.
-   [_**Visual Tutorial, Book, Manual: Lottery Software, Lotto Apps, Programs**_](https://saliu.com/forum/lotto-book.html).
-   [_**Basic Manual for Lotto Software, Lottery Software**_](https://saliu.com/bbs/messages/818.html).
-   [_**Sum-Totals for Lottery, Lotto Games — Pick 3 4 Lotteries, Lotto 5, 6, Powerball, Mega Millions, Euromillions**_](https://saliu.com/forum/lottery-sums.html).
-   [_**Lotto Software for Groups of Numbers: Odd, Even, Low, High, Sums, Frequencies, User's Groups**_](https://saliu.com/lotto-groups.html).
-   [_**Lottery Software Sum-Totals, Sums: Lotto, Powerball, Mega Millions, Euromillions**_](https://saliu.com/bbs/messages/626.html).
-   [**<u>Lottery Skip Systems</u>**](https://saliu.com/skip-strategy.html): _**Lotto, Powerball, Mega Millions, Euromillions**_.
-   [_**Lotto, Lottery <u>Strategy in Reverse</u>: Not-to-Win Leads to Not-to-Lose or WIN**_](https://saliu.com/reverse-strategy.html).
-   [**Lottery Utility Software**](https://saliu.com/lottery-utility.html): _**Pick-3, 4 Lottery, Lotto-5, 6, Powerball, Mega Millions, Euromillions.**_
-   [_**Theory, Analysis of**_ **Deltas** _**in Lotto, Lottery Software, Strategy, Systems**_](https://saliu.com/delta-lotto-software.html).
-   [_**The Best Strategy for Lottery, Gambling, Sports Betting, Horse Racing, Blackjack, Roulette**_](https://saliu.com/strategy-gambling-lottery.html).
-   [_**Lotto Software for Groups of Numbers**_](https://forums.saliu.com/lotto-software-odd-even-low-high.html).
-   _"The Start Is the Hardest Part"_: [_**Play a Lotto Strategy, Lottery Strategies**_](https://forums.saliu.com/lottery-strategies-start.html).
-   [_**Neural Networking, Neural Networks, AI in Lottery, Lotto: Strategies, Systems, Software, History**_](https://saliu.com/neural-networking-lottery.html).
-   Download [**lottery software, lotto software**](https://saliu.com/infodown.html).

![Lottery, lotto systems based on sum-total, odd, even, low, high runs best software.](https://saliu.com/HLINE.gif)

**[Home](https://saliu.com/index.htm) | [Search](https://saliu.com/Search.htm) | [New Writings](https://saliu.com/bbs/index.html) | [Fundamental Formula](https://saliu.com/formula.htm) | [Odds, Generator](https://saliu.com/calculator_generator.html) | [Contents](https://saliu.com/content/index.html) | [Forums](https://forums.saliu.com/) | [Sitemap](https://saliu.com/sitemap/index.html)**

![You should play dynamic lottery filters as only Ion Saliu's software can do.](https://saliu.com/HLINE.gif)
