#!/usr/bin/env julia

"""
Comprehensive test suite for enhanced performance reporting system
Tests statistical analysis, theoretical vs empirical comparisons, and reporting features
"""

using Dates

# Include required modules
include("src/wonder_grid_engine.jl")
include("src/backtesting.jl")
include("src/performance_reporting.jl")

"""
Test the enhanced performance reporting system
"""
function test_enhanced_performance_reporting()
    println("🧪 Testing Enhanced Performance Reporting System")
    println("=" ^ 60)
    
    # Test 1: Statistical report generation
    println("\n📊 Test 1: Statistical Report Generation")
    test_statistical_report_generation()
    
    # Test 2: Theoretical vs empirical analysis
    println("\n📈 Test 2: Theoretical vs Empirical Analysis")
    test_theoretical_empirical_analysis()
    
    # Test 3: Confidence intervals and significance testing
    println("\n🔬 Test 3: Statistical Significance Testing")
    test_statistical_significance()
    
    # Test 4: Expected value analysis
    println("\n💰 Test 4: Expected Value Analysis")
    test_expected_value_analysis()
    
    # Test 5: Risk analysis
    println("\n⚠️  Test 5: Risk Analysis")
    test_risk_analysis()
    
    # Test 6: Comparative reporting
    println("\n📋 Test 6: Comparative Reporting")
    test_comparative_reporting()
    
    # Test 7: Export functionality
    println("\n💾 Test 7: Export Functionality")
    test_export_functionality()
    
    println("\n✅ Enhanced Performance Reporting Tests Complete!")
    println("=" ^ 60)
end

"""
Test statistical report generation
"""
function test_statistical_report_generation()
    try
        # Create test components
        engine = WonderGridEngine()
        backtesting_engine = BacktestingEngine()
        reporter = PerformanceReporter(engine, backtesting_engine)
        
        # Create sample test data
        test_draws = create_sample_draws(50)
        key_number = 7
        
        # Generate statistical report
        println("  Generating statistical report for key number $key_number...")
        statistical_report = generate_statistical_report(reporter, key_number, test_draws)
        
        # Verify report structure
        required_keys = ["performance_report", "theoretical_probabilities", "empirical_probabilities", 
                        "confidence_intervals", "significance_tests", "expected_value_analysis", 
                        "risk_analysis", "statistical_summary"]
        
        for key in required_keys
            if !haskey(statistical_report, key)
                error("Missing required key: $key")
            end
        end
        
        println("  ✅ Statistical report generated successfully")
        println("  📈 Overall improvement ratio: $(round(statistical_report["statistical_summary"]["overall_improvement_ratio"], digits=2))x")
        println("  🎯 Assessment: $(statistical_report["statistical_summary"]["overall_assessment"])")
        
    catch e
        println("  ❌ Error in statistical report generation: $e")
        rethrow(e)
    end
end

"""
Test theoretical vs empirical analysis
"""
function test_theoretical_empirical_analysis()
    try
        # Test theoretical probability calculations
        println("  Testing theoretical probability calculations...")
        theoretical_probs = calculate_theoretical_probabilities()
        
        # Verify theoretical probabilities are reasonable
        expected_total_combinations = binomial(39, 5)
        if abs(theoretical_probs["total_combinations"] - expected_total_combinations) > 1
            error("Incorrect total combinations calculation")
        end
        
        # Check that probabilities sum to reasonable value
        total_prob = sum(theoretical_probs[tier] for tier in ["3/5", "4/5", "5/5"])
        if total_prob <= 0 || total_prob > 1
            error("Theoretical probabilities out of valid range")
        end
        
        println("  ✅ Theoretical probabilities calculated correctly")
        println("  🎲 Total combinations: $(Int(theoretical_probs["total_combinations"]))")
        println("  📊 3/5 probability: $(round(theoretical_probs["3/5"] * 100, digits=4))%")
        println("  📊 4/5 probability: $(round(theoretical_probs["4/5"] * 100, digits=6))%")
        println("  📊 5/5 probability: $(round(theoretical_probs["5/5"] * 100, digits=8))%")
        
    catch e
        println("  ❌ Error in theoretical analysis: $e")
        rethrow(e)
    end
end

"""
Test statistical significance testing
"""
function test_statistical_significance()
    try
        # Create test data with known properties
        empirical_probs = Dict{String, Float64}(
            "3/5" => 0.05,   # 5% hit rate
            "4/5" => 0.01,   # 1% hit rate  
            "5/5" => 0.001   # 0.1% hit rate
        )
        
        theoretical_probs = calculate_theoretical_probabilities()
        sample_size = 100
        
        println("  Testing confidence interval calculations...")
        confidence_intervals = calculate_confidence_intervals(empirical_probs, sample_size)
        
        # Verify confidence intervals are reasonable
        for (tier, (lower, upper)) in confidence_intervals
            if lower < 0 || upper > 1 || lower > upper
                error("Invalid confidence interval for $tier: [$lower, $upper]")
            end
            
            # Check that empirical probability is within interval
            if empirical_probs[tier] < lower || empirical_probs[tier] > upper
                error("Empirical probability outside confidence interval for $tier")
            end
        end
        
        println("  ✅ Confidence intervals calculated correctly")
        
        println("  Testing significance tests...")
        significance_tests = perform_significance_tests(empirical_probs, theoretical_probs, sample_size)
        
        # Verify significance test structure
        for tier in ["3/5", "4/5", "5/5"]
            test = significance_tests[tier]
            if haskey(test, "z_score")
                if !haskey(test, "p_value") || !haskey(test, "interpretation")
                    error("Incomplete significance test for $tier")
                end
                
                # Check p-value is valid
                if test["p_value"] < 0 || test["p_value"] > 1
                    error("Invalid p-value for $tier: $(test["p_value"])")
                end
            end
        end
        
        println("  ✅ Significance tests completed successfully")
        
    catch e
        println("  ❌ Error in significance testing: $e")
        rethrow(e)
    end
end

"""
Test expected value analysis
"""
function test_expected_value_analysis()
    try
        empirical_probs = Dict{String, Float64}(
            "3/5" => 0.05,
            "4/5" => 0.01,
            "5/5" => 0.001
        )
        
        theoretical_probs = calculate_theoretical_probabilities()
        
        println("  Testing expected value calculations...")
        ev_analysis = calculate_expected_value_analysis(empirical_probs, theoretical_probs)
        
        # Verify expected value structure
        required_keys = ["prize_structure", "strategy_expected_value", "random_expected_value",
                        "expected_value_improvement", "strategy_roi_percentage", "break_even_analysis"]
        
        for key in required_keys
            if !haskey(ev_analysis, key)
                error("Missing expected value key: $key")
            end
        end
        
        # Check that expected values are reasonable
        if ev_analysis["strategy_expected_value"] < 0
            error("Negative strategy expected value")
        end
        
        if ev_analysis["random_expected_value"] < 0
            error("Negative random expected value")
        end
        
        println("  ✅ Expected value analysis completed")
        println("  💰 Strategy EV: \$$(round(ev_analysis["strategy_expected_value"], digits=4))")
        println("  🎲 Random EV: \$$(round(ev_analysis["random_expected_value"], digits=4))")
        println("  📈 EV Improvement: \$$(round(ev_analysis["expected_value_improvement"], digits=4))")
        
    catch e
        println("  ❌ Error in expected value analysis: $e")
        rethrow(e)
    end
end

"""
Test risk analysis
"""
function test_risk_analysis()
    try
        # Create mock performance report
        hit_rates = HitRates(0.05, 0.01, 0.001)
        cost_analysis = CostAnalysis(100, 1.0, 100.0)
        
        performance_report = PerformanceReport(
            "Test Strategy", 7, (Date("2023-01-01"), Date("2023-12-31")), 100, 100,
            hit_rates, Dict("3/5" => 5, "4/5" => 1, "5/5" => 0),
            Dict("3/5" => 2.0, "4/5" => 1.5, "5/5" => 1.0),
            EfficiencyComparison(Dict("3/5" => 2.0), Dict("3/5" => 0.05), Dict("3/5" => 0.025)),
            cost_analysis, 1.0, 2.0, 3.0, 1.0, 0.95,
            Dict("best_efficiency_ratio" => 2.0)
        )
        
        test_draws = create_sample_draws(100)
        
        println("  Testing risk analysis calculations...")
        risk_analysis = calculate_risk_analysis(performance_report, test_draws)
        
        # Verify risk analysis structure
        required_keys = ["volatility", "max_potential_loss", "value_at_risk_95", 
                        "risk_level", "risk_recommendation"]
        
        for key in required_keys
            if !haskey(risk_analysis, key)
                error("Missing risk analysis key: $key")
            end
        end
        
        # Check risk metrics are reasonable
        if risk_analysis["volatility"] < 0
            error("Negative volatility")
        end
        
        if risk_analysis["max_potential_loss"] < 0
            error("Negative maximum potential loss")
        end
        
        println("  ✅ Risk analysis completed")
        println("  📊 Volatility: $(round(risk_analysis["volatility"], digits=4))")
        println("  💸 Max potential loss: \$$(round(risk_analysis["max_potential_loss"], digits=2))")
        println("  ⚠️  Risk level: $(risk_analysis["risk_level"])")
        
    catch e
        println("  ❌ Error in risk analysis: $e")
        rethrow(e)
    end
end

"""
Test comparative reporting
"""
function test_comparative_reporting()
    try
        # Create test components
        engine = WonderGridEngine()
        backtesting_engine = BacktestingEngine()
        reporter = PerformanceReporter(engine, backtesting_engine)
        
        # Create sample test data
        test_draws = create_sample_draws(20)  # Smaller sample for faster testing
        key_numbers = [3, 7, 11]
        
        println("  Testing comparative report generation...")
        reports = generate_comparative_report(reporter, key_numbers, test_draws)
        
        # Verify we got reports for all key numbers
        if length(reports) != length(key_numbers)
            println("  ⚠️  Warning: Only $(length(reports))/$(length(key_numbers)) reports generated")
        else
            println("  ✅ All comparative reports generated successfully")
        end
        
        # Test comparative analysis display
        if !isempty(reports)
            println("  Testing comparative analysis display...")
            # Capture output (in real implementation, this would display)
            display_comparative_analysis(reports)
            println("  ✅ Comparative analysis displayed successfully")
        end
        
    catch e
        println("  ❌ Error in comparative reporting: $e")
        rethrow(e)
    end
end

"""
Test export functionality
"""
function test_export_functionality()
    try
        # Create test components
        engine = WonderGridEngine()
        backtesting_engine = BacktestingEngine()
        reporter = PerformanceReporter(engine, backtesting_engine)
        
        # Create sample test data
        test_draws = create_sample_draws(10)  # Small sample for testing
        key_number = 7
        
        println("  Testing statistical report export...")
        statistical_report = generate_statistical_report(reporter, key_number, test_draws)
        
        # Test CSV export
        csv_filename = "test_statistical_report.csv"
        export_statistical_report_csv(statistical_report, csv_filename)
        
        # Verify file was created
        if isfile(csv_filename)
            println("  ✅ CSV export successful: $csv_filename")
            
            # Clean up test file
            rm(csv_filename)
            println("  🧹 Test file cleaned up")
        else
            error("CSV file was not created")
        end
        
        # Test regular performance report export
        performance_report = statistical_report["performance_report"]
        txt_filename = "test_performance_report.txt"
        export_performance_report(performance_report, txt_filename)
        
        if isfile(txt_filename)
            println("  ✅ TXT export successful: $txt_filename")
            
            # Clean up test file
            rm(txt_filename)
            println("  🧹 Test file cleaned up")
        else
            error("TXT file was not created")
        end
        
    catch e
        println("  ❌ Error in export functionality: $e")
        rethrow(e)
    end
end

"""
Create sample lottery draws for testing
"""
function create_sample_draws(count::Int)::Vector{LotteryDraw}
    draws = LotteryDraw[]
    base_date = Date("2023-01-01")
    
    for i in 1:count
        # Generate random but valid lottery numbers
        numbers = sort(rand(1:39, 5))
        while length(unique(numbers)) != 5
            numbers = sort(rand(1:39, 5))
        end
        
        draw_date = base_date + Day(i * 3)  # Every 3 days
        push!(draws, LotteryDraw(draw_date, numbers))
    end
    
    return draws
end

"""
Test comprehensive statistical report display
"""
function test_comprehensive_display()
    println("\n🖥️  Testing Comprehensive Statistical Report Display")
    
    try
        # Create test components
        engine = WonderGridEngine()
        backtesting_engine = BacktestingEngine()
        reporter = PerformanceReporter(engine, backtesting_engine)
        
        # Create sample test data
        test_draws = create_sample_draws(30)
        key_number = 13
        
        println("  Generating and displaying comprehensive statistical report...")
        statistical_report = generate_statistical_report(reporter, key_number, test_draws)
        
        # Display the comprehensive report
        display_statistical_report(statistical_report)
        
        println("  ✅ Comprehensive display test completed")
        
    catch e
        println("  ❌ Error in comprehensive display: $e")
        rethrow(e)
    end
end

# Run the tests
if abspath(PROGRAM_FILE) == @__FILE__
    test_enhanced_performance_reporting()
    test_comprehensive_display()
end