using WonderGridLotterySystem
using Statistics
using Dates

println("Testing Wonder Grid Engine Core Functionality")
println("=" ^ 55)

# Load lottery data
fm = FileManager()
data = read_data5_file(fm, "data/fan5.csv")
println("Loaded $(length(data)) lottery draws for Wonder Grid engine testing")

# Create Wonder Grid engine
println("\nInitializing Wonder Grid Engine...")
start_time = time()
engine = WonderGridEngine(data)
init_time = time() - start_time
println("Engine initialized in $(round(init_time, digits=3)) seconds")

# Test component integration
println("\nComponent Integration Test:")
println("  FFG Calculator: $(typeof(engine.ffg_calculator))")
println("  Skip Analyzer: $(typeof(engine.skip_analyzer))")
println("  Pairing Engine: $(typeof(engine.pairing_engine))")

# Test key number selection
println("\n" * "=" ^ 55)
println("Key Number Selection Testing")
println("=" ^ 55)

start_time = time()
key_numbers = select_key_numbers(engine)
selection_time = time() - start_time

println("Key number selection completed in $(round(selection_time, digits=3)) seconds")
println("Total favorable key numbers: $(length(key_numbers))")
println("Key numbers: $(join(key_numbers, ", "))")

# Analyze key number quality
println("\nKey Number Quality Analysis:")
key_analysis = []

for number in key_numbers[1:min(10, length(key_numbers))]  # Analyze first 10
    skip_chart = generate_skip_chart(engine.skip_analyzer, number)
    top_pairings = get_top_pairings(engine.pairing_engine, number, 0.25)
    
    quality_score = (1.0 - skip_chart.current_skip / max(skip_chart.ffg_median, 1.0)) * length(top_pairings)
    
    push!(key_analysis, (number, skip_chart.current_skip, skip_chart.ffg_median, 
                        length(top_pairings), quality_score))
end

sort!(key_analysis, by = x -> x[5], rev = true)  # Sort by quality score

println("Top 10 Key Numbers by Quality Score:")
println("Rank | Number | Skip | Median | Pairings | Quality")
println("-" ^ 50)
for (i, (number, skip, median, pairings, quality)) in enumerate(key_analysis)
    println("$(lpad(i, 4)) | $(lpad(number, 6)) | $(lpad(skip, 4)) | $(lpad(round(median, digits=1), 6)) | $(lpad(pairings, 8)) | $(lpad(round(quality, digits=2), 7))")
end

# Test combination generation for multiple key numbers
println("\n" * "=" ^ 55)
println("Combination Generation Testing")
println("=" ^ 55)

test_keys = key_numbers[1:min(5, length(key_numbers))]  # Test first 5 key numbers
generation_results = []

for key_number in test_keys
    println("\nTesting key number $key_number:")
    
    start_time = time()
    combinations = generate_combinations(engine, key_number)
    gen_time = time() - start_time
    
    println("  Generated $(length(combinations)) combinations in $(round(gen_time, digits=3)) seconds")
    
    # Verify all combinations contain the key number
    all_contain_key = all(combo -> key_number in combo, combinations)
    println("  All combinations contain key number: $(all_contain_key ? "YES" : "NO")")
    
    # Verify combination format
    all_valid_format = all(combo -> length(combo) == 5 && all(n -> 1 <= n <= 39, combo), combinations)
    println("  All combinations valid format: $(all_valid_format ? "YES" : "NO")")
    
    # Check for duplicates
    unique_combinations = length(unique(combinations))
    println("  Unique combinations: $unique_combinations / $(length(combinations))")
    
    # Show sample combinations
    println("  Sample combinations:")
    for i in 1:min(5, length(combinations))
        println("    $i: $(join(sort(combinations[i]), "-"))")
    end
    
    push!(generation_results, (key_number, length(combinations), gen_time, all_contain_key, all_valid_format))
end

# Test strategy execution
println("\n" * "=" ^ 55)
println("Strategy Execution Testing")
println("=" ^ 55)

if !isempty(key_numbers)
    best_key = key_analysis[1][1]  # Best quality key number
    println("Executing Wonder Grid strategy for key number $best_key:")
    
    start_time = time()
    strategy_result = execute_strategy(engine, best_key)
    execution_time = time() - start_time
    
    println("\nStrategy Execution Results:")
    println("  Key number: $(strategy_result.key_number)")
    println("  Total combinations: $(length(strategy_result.combinations))")
    println("  Generation time: $(round(strategy_result.generation_time, digits=3)) seconds")
    println("  Total execution time: $(round(execution_time, digits=3)) seconds")
    println("  Estimated cost: \$$(strategy_result.estimated_cost)")
    
    println("\n  Expected efficiency ratios:")
    for (tier, ratio) in strategy_result.expected_efficiency
        println("    $tier: $(ratio)x better than random")
    end
    
    # Verify strategy result integrity
    println("\n  Strategy Result Verification:")
    println("    Key number in all combinations: $(all(combo -> best_key in combo, strategy_result.combinations) ? "YES" : "NO")")
    println("    All combinations unique: $(length(unique(strategy_result.combinations)) == length(strategy_result.combinations) ? "YES" : "NO")")
    println("    All combinations valid: $(all(combo -> length(combo) == 5 && all(n -> 1 <= n <= 39, combo), strategy_result.combinations) ? "YES" : "NO")")
end

# Test engine performance with different data sizes
println("\n" * "=" ^ 55)
println("Scalability Testing")
println("=" ^ 55)

data_sizes = [100, 500, 1000, length(data)]
scalability_results = []

for size in data_sizes
    test_data = data[1:min(size, length(data))]
    
    # Test engine initialization
    start_time = time()
    test_engine = WonderGridEngine(test_data)
    init_time = time() - start_time
    
    # Test key number selection
    start_time = time()
    test_keys = select_key_numbers(test_engine)
    selection_time = time() - start_time
    
    # Test combination generation for first key
    if !isempty(test_keys)
        start_time = time()
        test_combinations = generate_combinations(test_engine, test_keys[1])
        generation_time = time() - start_time
    else
        generation_time = 0.0
        test_combinations = []
    end
    
    push!(scalability_results, (size, init_time, selection_time, generation_time, 
                               length(test_keys), length(test_combinations)))
    
    println("Data size $size:")
    println("  Initialization: $(round(init_time, digits=3))s")
    println("  Key selection: $(round(selection_time, digits=3))s")
    println("  Combination generation: $(round(generation_time, digits=3))s")
    println("  Key numbers found: $(length(test_keys))")
    println("  Combinations generated: $(length(test_combinations))")
end

# Test memory usage estimation
println("\n" * "=" ^ 55)
println("Memory Usage Analysis")
println("=" ^ 55)

# Estimate memory usage for different components
pairing_memory = 741 * (sizeof(Tuple{Int,Int}) + sizeof(Int))  # All possible pairs
skip_memory = 39 * 100 * sizeof(Int)  # Estimated skip sequences
combination_memory = 210 * 5 * sizeof(Int)  # One key number's combinations

total_estimated = pairing_memory + skip_memory + combination_memory

println("Estimated memory usage:")
println("  Pairing cache: $(round(pairing_memory / 1024, digits=2)) KB")
println("  Skip sequences: $(round(skip_memory / 1024, digits=2)) KB")
println("  Combinations (per key): $(round(combination_memory / 1024, digits=2)) KB")
println("  Total estimated: $(round(total_estimated / 1024, digits=2)) KB")

# Test error handling
println("\n" * "=" ^ 55)
println("Error Handling Testing")
println("=" ^ 55)

# Test with invalid key number
println("Testing invalid key number (0):")
try
    invalid_combinations = generate_combinations(engine, 0)
    println("  Result: Generated $(length(invalid_combinations)) combinations")
catch e
    println("  Result: Error caught - $(typeof(e))")
end

# Test with out-of-range key number
println("Testing out-of-range key number (40):")
try
    invalid_combinations = generate_combinations(engine, 40)
    println("  Result: Generated $(length(invalid_combinations)) combinations")
catch e
    println("  Result: Error caught - $(typeof(e))")
end

# Performance summary
println("\n" * "=" ^ 55)
println("Performance Summary")
println("=" ^ 55)

total_keys = length(key_numbers)
if total_keys > 0 && !isempty(generation_results)
    avg_generation_time = mean([x[3] for x in generation_results])
    total_combinations_possible = total_keys * 210  # Theoretical maximum
    
    println("Wonder Grid Engine Performance:")
    println("  Engine initialization: $(round(init_time, digits=3)) seconds")
    println("  Key number selection: $(round(selection_time, digits=3)) seconds")
    println("  Average combination generation: $(round(avg_generation_time, digits=3)) seconds")
    println("  Total favorable key numbers: $total_keys")
    println("  Theoretical max combinations: $total_combinations_possible")
    println("  Performance rating: $(total_keys > 15 ? "Excellent" : total_keys > 10 ? "Good" : "Fair")")
end

println("\nWonder Grid Engine core functionality testing completed successfully!")