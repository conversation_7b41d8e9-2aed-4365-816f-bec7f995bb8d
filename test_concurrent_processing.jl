#!/usr/bin/env julia

"""
Comprehensive test suite for concurrent processing system
Tests parallel processing, thread safety, performance monitoring, and scaling capabilities
"""

using Dates
using Base.Threads

# Include required modules
include("src/wonder_grid_engine.jl")
include("src/backtesting.jl")
include("src/performance_optimization.jl")
include("src/concurrent_processing.jl")

"""
Test the concurrent processing system
"""
function test_concurrent_processing_system()
    println("🧪 Testing Concurrent Processing System")
    println("=" ^ 70)
    println("Available threads: $(nthreads())")
    
    # Test 1: Thread-safe data structures
    println("\n🔒 Test 1: Thread-Safe Data Structures")
    test_thread_safe_data_structures()
    
    # Test 2: Parallel combination generation
    println("\n⚡ Test 2: Parallel Combination Generation")
    test_parallel_combination_generation()
    
    # Test 3: Concurrent backtesting
    println("\n🏃 Test 3: Concurrent Backtesting")
    test_concurrent_backtesting()
    
    # Test 4: Thread-safe caching
    println("\n💾 Test 4: Thread-Safe Caching")
    test_thread_safe_caching()
    
    # Test 5: Performance monitoring
    println("\n📊 Test 5: Concurrent Performance Monitoring")
    test_concurrent_performance_monitoring()
    
    # Test 6: Thread pool management
    println("\n🏊 Test 6: Thread Pool Management")
    test_thread_pool_management()
    
    # Test 7: Scaling analysis
    println("\n📈 Test 7: Scaling Analysis")
    test_scaling_analysis()
    
    println("\n✅ Concurrent Processing Tests Complete!")
    println("=" ^ 70)
end

"""
Test thread-safe data structures
"""
function test_thread_safe_data_structures()
    try
        # Test ConcurrentPairingCalculator
        println("  Testing ConcurrentPairingCalculator...")
        
        calc = ConcurrentPairingCalculator()
        @assert calc.total_pairs[] == 0
        @assert calc.cache_hits[] == 0
        @assert calc.cache_misses[] == 0
        
        # Test thread-safe combination addition
        test_combinations = [
            [1, 5, 12, 23, 39],
            [3, 7, 15, 28, 35],
            [2, 11, 19, 31, 37]
        ]
        
        # Add combinations concurrently if multiple threads available
        if nthreads() > 1
            @threads for combo in test_combinations
                add_combination_concurrent!(calc, combo)
            end
        else
            for combo in test_combinations
                add_combination_concurrent!(calc, combo)
            end
        end
        
        @assert calc.total_pairs[] == 30  # 3 combinations × 10 pairs each
        
        # Test thread-safe frequency retrieval
        freq = get_pairing_frequency_concurrent(calc, 1, 5)
        @assert freq >= 0
        
        println("  ✅ ConcurrentPairingCalculator working correctly")
        
        # Test thread-safe FFG calculation
        println("  Testing thread-safe FFG calculation...")
        
        key_numbers = [7, 13, 21]
        ffg_results = Vector{Vector{Int}}(undef, length(key_numbers))
        
        if nthreads() > 1
            @threads for i in 1:length(key_numbers)
                ffg_results[i] = calculate_ffg_concurrent(key_numbers[i])
            end
        else
            for i in 1:length(key_numbers)
                ffg_results[i] = calculate_ffg_concurrent(key_numbers[i])
            end
        end
        
        # Verify all results are valid
        for (i, ffg) in enumerate(ffg_results)
            @assert !isempty(ffg)
            @assert all(n -> 1 <= n <= 39, ffg)
            @assert issorted(ffg)
        end
        
        println("  ✅ Thread-safe FFG calculation working correctly")
        
    catch e
        println("  ❌ Error in thread-safe data structures: $e")
        rethrow(e)
    end
end

"""
Test parallel combination generation
"""
function test_parallel_combination_generation()
    try
        println("  Testing ParallelCombinationGenerator...")
        
        key_number = 7
        generator = ParallelCombinationGenerator(key_number, min(4, nthreads()))
        
        @assert generator.key_number == key_number
        @assert !isempty(generator.ffg_numbers)
        @assert generator.num_threads <= nthreads()
        
        # Generate combinations in parallel
        combinations = generate_combinations_parallel(generator)
        
        @assert isa(combinations, Vector{Vector{Int}})
        
        if !isempty(combinations)
            # Verify all combinations are valid
            for combo in combinations
                @assert length(combo) == 5
                @assert all(n -> 1 <= n <= 39, combo)
                @assert length(unique(combo)) == 5  # No duplicates
                @assert issorted(combo)  # Should be sorted
            end
            
            # Verify no duplicate combinations
            unique_combinations = unique(combinations)
            @assert length(unique_combinations) == length(combinations)
            
            println("  ✅ Generated $(length(combinations)) valid combinations")
        else
            println("  ⚠️  No combinations generated (insufficient FFG numbers)")
        end
        
        println("  ✅ Parallel combination generation working correctly")
        
        # Test combination indices generation
        println("  Testing combination indices generation...")
        
        indices = combinations_indices(6, 3)
        expected_count = binomial(6, 3)  # Should be 20
        
        @assert length(indices) == expected_count
        @assert all(idx_list -> length(idx_list) == 3, indices)
        @assert all(idx_list -> all(i -> 1 <= i <= 6, idx_list), indices)
        
        println("  ✅ Combination indices generation working correctly")
        
    catch e
        println("  ❌ Error in parallel combination generation: $e")
        rethrow(e)
    end
end

"""
Test concurrent backtesting
"""
function test_concurrent_backtesting()
    try
        println("  Testing ParallelBacktestingEngine...")
        
        engine = ParallelBacktestingEngine(min(4, nthreads()), 50)
        @assert engine.num_threads <= nthreads()
        @assert engine.chunk_size == 50
        
        # Create test data
        test_combinations = [
            [1, 5, 12, 23, 39],
            [3, 7, 15, 28, 35],
            [2, 11, 19, 31, 37],
            [4, 9, 16, 25, 33],
            [6, 13, 20, 29, 38]
        ]
        
        test_draws = [
            LotteryDraw(Date("2023-01-01"), [1, 5, 12, 23, 39]),
            LotteryDraw(Date("2023-01-02"), [3, 7, 15, 28, 35]),
            LotteryDraw(Date("2023-01-03"), [10, 14, 18, 22, 26])
        ]
        
        # Run parallel backtest
        result = run_backtest_parallel(engine, test_combinations, test_draws)
        
        @assert isa(result, BacktestResult)
        @assert haskey(result.hit_rates, "3/5")
        @assert haskey(result.hit_rates, "4/5")
        @assert haskey(result.hit_rates, "5/5")
        
        # Verify hit rates are valid probabilities
        for (tier, rate) in result.hit_rates
            @assert 0.0 <= rate <= 1.0
        end
        
        println("  ✅ Parallel backtesting working correctly")
        
        # Test with empty data
        empty_result = run_backtest_parallel(engine, Vector{Vector{Int}}(), test_draws)
        @assert all(rate == 0.0 for rate in values(empty_result.hit_rates))
        
        println("  ✅ Empty data handling working correctly")
        
    catch e
        println("  ❌ Error in concurrent backtesting: $e")
        rethrow(e)
    end
end

"""
Test thread-safe caching
"""
function test_thread_safe_caching()
    try
        # Clear caches for clean test
        clear_concurrent_caches!()
        
        println("  Testing thread-safe caching...")
        
        key_numbers = [7, 13, 21, 7, 13, 21]  # Repeated for cache testing
        
        # Test concurrent FFG caching
        if nthreads() > 1
            @threads for key_number in key_numbers
                ffg = calculate_ffg_concurrent(key_number)
                @assert !isempty(ffg)
            end
        else
            for key_number in key_numbers
                ffg = calculate_ffg_concurrent(key_number)
                @assert !isempty(ffg)
            end
        end
        
        # Verify cache contains results
        cache_stats = get_concurrent_cache_stats()
        @assert cache_stats["ffg_cache_size"] >= 3  # Should have at least 3 unique keys
        
        println("  ✅ Thread-safe FFG caching working correctly")
        
        # Test concurrent pairing frequency caching
        calc = ConcurrentPairingCalculator()
        add_combination_concurrent!(calc, [1, 5, 12, 23, 39])
        
        test_pairs = [(1, 5), (5, 12), (12, 23), (1, 5), (5, 12)]  # Repeated pairs
        
        if nthreads() > 1
            @threads for (num1, num2) in test_pairs
                freq = get_pairing_frequency_concurrent(calc, num1, num2)
                @assert freq >= 0
            end
        else
            for (num1, num2) in test_pairs
                freq = get_pairing_frequency_concurrent(calc, num1, num2)
                @assert freq >= 0
            end
        end
        
        # Should have some cache hits
        @assert calc.cache_hits[] > 0
        
        println("  ✅ Thread-safe pairing frequency caching working correctly")
        
        # Test cache statistics
        final_cache_stats = get_concurrent_cache_stats()
        @assert haskey(final_cache_stats, "thread_count")
        @assert final_cache_stats["thread_count"] == nthreads()
        
        println("  ✅ Cache statistics working correctly")
        
    catch e
        println("  ❌ Error in thread-safe caching: $e")
        rethrow(e)
    end
end

"""
Test concurrent performance monitoring
"""
function test_concurrent_performance_monitoring()
    try
        println("  Testing ConcurrentPerformanceMonitor...")
        
        monitor = ConcurrentPerformanceMonitor()
        @assert monitor.start_time > 0
        @assert isa(monitor.operation_counts, Dict)
        @assert isa(monitor.timing_data, Dict)
        
        # Test concurrent operation timing
        operations = ["test_op_1", "test_op_2", "test_op_3"]
        
        if nthreads() > 1
            @threads for operation in operations
                start_time = start_operation_concurrent!(monitor, operation)
                sleep(0.01)  # Simulate work
                end_operation_concurrent!(monitor, operation, start_time)
            end
        else
            for operation in operations
                start_time = start_operation_concurrent!(monitor, operation)
                sleep(0.01)  # Simulate work
                end_operation_concurrent!(monitor, operation, start_time)
            end
        end
        
        # Verify timing was recorded
        for operation in operations
            @assert haskey(monitor.timing_data, operation)
            @assert haskey(monitor.operation_counts, operation)
            @assert monitor.operation_counts[operation][] >= 1
        end
        
        println("  ✅ Concurrent operation timing working correctly")
        
        # Test performance statistics
        stats = get_performance_stats_concurrent(monitor)
        @assert haskey(stats, "total_runtime")
        @assert haskey(stats, "memory_used_bytes")
        @assert haskey(stats, "operations")
        @assert haskey(stats, "thread_count")
        @assert stats["thread_count"] == nthreads()
        
        # Verify operation statistics
        for operation in operations
            if haskey(stats["operations"], operation)
                op_stats = stats["operations"][operation]
                @assert haskey(op_stats, "count")
                @assert haskey(op_stats, "average_time")
                @assert op_stats["count"] >= 1
            end
        end
        
        println("  ✅ Concurrent performance statistics working correctly")
        
    catch e
        println("  ❌ Error in concurrent performance monitoring: $e")
        rethrow(e)
    end
end

"""
Test thread pool management
"""
function test_thread_pool_management()
    try
        println("  Testing ThreadPoolManager...")
        
        # Create thread pool with limited threads
        pool_size = min(2, nthreads())
        manager = ThreadPoolManager(pool_size)
        
        @assert manager.max_threads == pool_size
        @assert manager.active_tasks[] == 0
        @assert !manager.shutdown_flag[]
        @assert length(manager.worker_tasks) == pool_size
        
        # Submit test tasks
        completed_tasks = Atomic{Int}(0)
        task_count = 5
        
        for i in 1:task_count
            task_func = function()
                sleep(0.01)  # Simulate work
                atomic_add!(completed_tasks, 1)
            end
            
            success = submit_task!(manager, task_func)
            @assert success == true
        end
        
        # Wait for completion
        completion_success = wait_for_completion(manager, 5.0)  # 5 second timeout
        @assert completion_success == true
        @assert completed_tasks[] == task_count
        
        println("  ✅ Thread pool task execution working correctly")
        
        # Test shutdown
        shutdown!(manager)
        @assert manager.shutdown_flag[] == true
        
        # Try to submit task after shutdown
        post_shutdown_success = submit_task!(manager, () -> nothing)
        @assert post_shutdown_success == false
        
        println("  ✅ Thread pool shutdown working correctly")
        
    catch e
        println("  ❌ Error in thread pool management: $e")
        rethrow(e)
    end
end

"""
Test scaling analysis
"""
function test_scaling_analysis()
    try
        println("  Testing ScalingAnalyzer...")
        
        max_threads = min(4, nthreads())  # Limit for testing
        analyzer = ScalingAnalyzer(max_threads)
        
        @assert !isempty(analyzer.thread_counts)
        @assert maximum(analyzer.thread_counts) <= max_threads
        @assert isempty(analyzer.performance_data)
        
        # Run scaling analysis with small iteration count
        key_number = 7
        iterations = 2  # Small number for testing
        
        performance_data = analyze_scaling_performance(analyzer, key_number, iterations)
        
        @assert !isempty(performance_data)
        @assert length(performance_data) == length(analyzer.thread_counts)
        
        # Verify performance data structure
        for (thread_count, data) in performance_data
            @assert haskey(data, "average_time")
            @assert haskey(data, "min_time")
            @assert haskey(data, "max_time")
            @assert haskey(data, "speedup")
            @assert haskey(data, "efficiency")
            
            @assert data["average_time"] > 0
            @assert data["speedup"] > 0
            @assert data["efficiency"] > 0
        end
        
        println("  ✅ Scaling analysis working correctly")
        
        # Test results display (capture output)
        original_stdout = stdout
        redirect_stdout(devnull)
        
        display_scaling_results(analyzer)
        
        redirect_stdout(original_stdout)
        
        println("  ✅ Scaling results display working correctly")
        
    catch e
        println("  ❌ Error in scaling analysis: $e")
        rethrow(e)
    end
end

"""
Test concurrent Wonder Grid engine integration
"""
function test_concurrent_engine_integration()
    println("\n🔧 Test: Concurrent Engine Integration")
    
    try
        key_number = 7
        engine = ConcurrentWonderGridEngine(key_number, min(4, nthreads()))
        
        @assert engine.num_threads <= nthreads()
        @assert engine.parallel_generator.key_number == key_number
        
        # Test concurrent combination generation
        combinations = generate_combinations_concurrent(engine, key_number)
        
        if !isempty(combinations)
            @assert isa(combinations, Vector{Vector{Int}})
            
            # Verify combinations are valid
            for combo in combinations[1:min(5, length(combinations))]  # Test first 5
                @assert length(combo) == 5
                @assert all(n -> 1 <= n <= 39, combo)
                @assert length(unique(combo)) == 5
            end
            
            println("  ✅ Concurrent combination generation working")
            
            # Test concurrent backtesting
            test_draws = [
                LotteryDraw(Date("2023-01-01"), [1, 5, 12, 23, 39]),
                LotteryDraw(Date("2023-01-02"), [3, 7, 15, 28, 35])
            ]
            
            # Use subset for testing
            test_combinations = combinations[1:min(10, length(combinations))]
            
            backtest_result = run_backtest_concurrent(engine, test_combinations, test_draws)
            
            @assert isa(backtest_result, BacktestResult)
            @assert all(0.0 <= rate <= 1.0 for rate in values(backtest_result.hit_rates))
            
            println("  ✅ Concurrent backtesting working")
            
            # Check performance monitoring
            perf_stats = get_performance_stats_concurrent(engine.performance_monitor)
            @assert haskey(perf_stats, "operations")
            @assert haskey(perf_stats, "thread_count")
            
            println("  ✅ Performance monitoring integration working")
        else
            println("  ⚠️  No combinations generated for testing")
        end
        
    catch e
        println("  ❌ Error in concurrent engine integration: $e")
        rethrow(e)
    end
end

"""
Test thread safety with stress testing
"""
function test_thread_safety_stress()
    println("\n💪 Test: Thread Safety Stress Testing")
    
    if nthreads() == 1
        println("  ⚠️  Skipping stress test - only 1 thread available")
        return
    end
    
    try
        # Stress test concurrent pairing calculator
        calc = ConcurrentPairingCalculator()
        
        # Generate many combinations concurrently
        stress_combinations = [
            [rand(1:39, 5) |> sort |> unique |> collect for _ in 1:100]...
        ]
        
        # Filter to valid combinations (5 unique numbers)
        valid_combinations = [combo for combo in stress_combinations if length(combo) == 5]
        
        if length(valid_combinations) >= 10
            @threads for combo in valid_combinations[1:10]  # Limit for testing
                add_combination_concurrent!(calc, combo)
            end
            
            @assert calc.total_pairs[] > 0
            println("  ✅ Concurrent pairing calculator stress test passed")
        end
        
        # Stress test concurrent caching
        key_numbers = [rand(1:39) for _ in 1:20]
        
        @threads for key_number in key_numbers
            ffg = calculate_ffg_concurrent(key_number)
            @assert !isempty(ffg) || key_number == 40  # 40 might produce empty FFG
        end
        
        cache_stats = get_concurrent_cache_stats()
        @assert cache_stats["total_cached_items"] > 0
        
        println("  ✅ Concurrent caching stress test passed")
        
    catch e
        println("  ❌ Error in thread safety stress testing: $e")
        rethrow(e)
    end
end

# Run the tests
if abspath(PROGRAM_FILE) == @__FILE__
    test_concurrent_processing_system()
    test_concurrent_engine_integration()
    test_thread_safety_stress()
end