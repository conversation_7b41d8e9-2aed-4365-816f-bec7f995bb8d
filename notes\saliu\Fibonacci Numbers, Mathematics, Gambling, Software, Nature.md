---
created: 2025-07-24T23:08:09 (UTC +08:00)
tags: [Fibonacci,progressions,series,sequences,Fibonacci numbers,mathematics,gambling,software,Golden Number,programs,Martingale,]
source: https://saliu.com/Fibonacci.html
author: 
---

# Fibonacci Numbers, Mathematics, Gambling, Software, Nature

> ## Excerpt
> Natural phenomena grow in proportions of Fibonacci Series, Fibonacci sequences, Fibonacci progressions, or Fibonacci numbers, gambling progressions.

---
Φ The Fibonacci Numbers are widely known all over the world, regardless of religion or ideological affiliation. Many natural phenomena grow in proportions following the _**Fibonacci Series (or Fibonacci sequences, or Fibonacci progressions)**_. The Fibonacci progression is omnipresent in Nature, indeed in the Universe. For example, the rabbits multiply in a Fibonacci progression. In fact, the Fibonacci series has its origin in that natural phenomenon.

Many people are familiar with a string of numbers such as this one: 1, 1, 2, 3, 5, 8, 13, 21, 34, 55, 89, 144, etc. The Nth term of the Fibonacci series is constructed as the sum of the two preceding terms; e.g. 55 = 21 + 34. The term #10 = the term #8 + the term #9.

There is a natural correlation between the _**Fibonacci numbers and the divine proportion, or the golden number PHI**_. The ratio between two consecutive numbers in the Fibonacci Series tends to the Golden Number Φ: 0.618… or 1.618… As in the example above:  
Fibonacci #9 / Fibonacci #10 = 34 / 55 = 0.61818…  
Fibonacci #10 / Fibonacci #9 = 55 / 34 = 1.61764…  
Any three Fibonacci numbers a, b, c satisfy the relation c = a + b. Thus, the ratio b/{a+b) tends to the golden ratio or golden number. The longer the Fibonacci series, the closer the ratio of two consecutive terms to the golden number PHI.

I wrote mathematical software to generate Fibonacci numbers and to calculate the golden ratio: Fibonacci. You can download it from my free downloads site; software category: 5.6. The software is freeware. One need not be a…golden child in order to run Fibonacci! The Fibonacci numbers grow very big after 50 terms or so. Try first the defaults: up to 1000 terms and screen display.

![Many natural phenomena grow in proportions following the Fibonacci Series, Fibonacci sequences, Fibonacci progressions, or Fibonacci numbers.](https://saliu.com/ScreenImgs/fibonacci.gif)

Furthermore, I expanded the concept. I created software to build a Fibonacci-like number by the following methods:

~ the Nth term is equal to the sum of the THREE preceding terms; name: Fibonacci\_3;  
~ the Nth term is equal to the sum of the FOUR previous terms; name: Fibonacci\_4;  
~ the Nth term is equal to the sum of the FIVE preceding terms; name: Fibonacci\_5.

The corresponding free programs are: Fibonacci3, Fibonacci4, Fibonacci5.

The correlation between these Fibonacci-like sequences and the Golden Ratio (PHI or Divine Proportion) is still strong. The difference is quite small: between 0.07 and 0.35. Here is a rundown.

~ The term # 50 of the Fibonacci\_3 series is 5742568741225  
~ The 1st root PHI\_0 of the Golden Ratio is .618033988749895  
~ The 2nd root PHI\_1 of the Golden Number is 1.61803398874989

The Fibonacci\_3 ratio between 3122171529233 / 5742568741225 = .543689012692076  
The difference between PHI\_0 and Fibonacci\_3 = 0.074344976057818

The Fibonacci\_3 ratio between 5742568741225 / 3122171529233 = 1.83928675521416  
The difference between Fibonacci\_3 and PHI\_1 = 0.221252766464266

~ The term # 100 of the Fibonacci\_4 series is 6.80359547771814E+27  
~ The 1st root PHI\_0 of the Golden Ratio is .618033988749895  
~ The 2nd root PHI\_1 of the Golden Proportion is 1.61803398874989

The Fibonacci\_4 ratio between 3.52963773111035E+27 / 6.80359547771814E+27 = .518790063675884  
The difference between PHI\_0 and Fibonacci\_4 = 0.099243925074011

The Fibonacci\_4 ratio between 6.80359547771814E+27 / 3.52963773111035E+27 = 1.92756197548292  
The difference between Fibonacci\_4 and PHI\_1 = 0.30952798673303

~ The term # 100 of the Fibonacci\_5 series is 2.97734844776513E+28  
~ The 1st root PHI\_0 of the Golden Ratio is .618033988749895  
~ The 2nd root PHI\_1 of the Divine Proportion is 1.61803398874989

The Fibonacci\_5 ratio between 1.51445922749492E+28 / 2.97734844776513E+28 = .508660391642004  
The difference between PHI\_0 and Fibonacci\_5 = 0.109373597107891

The Fibonacci\_5 ratio between 2.97734844776513E+28 / 1.51445922749492E+28 = 1.96594823664548  
The difference between Fibonacci\_5 and PHI\_1 = 0.34791424789559

![Applying Fibonacci Numbers, Sequences, Progressions to gambling betting.](https://saliu.com/HLINE.gif)

We can now expand the generalization to the maximum. We can generate progressions from type 1 to type P. A type 1 progression is the power of 2 series or Martingale. The type 2 progression is known as the famous Fibonacci series. To generalize, a type P progression is a series of numbers where the Nth term is the sum of the preceding P terms.

When P and N tend to infinity, the ratio between the Nth term and the (N-1)th term tends to 2; the reversed ratio tends to ½ or 0.5. Therefore, for large P and N, a progression tends to become the power of 2 series (the Martingale progression in betting).

Get the incredibly powerful program Progressions to generate any type of progression, from 1 to hundreds. If you enter negative values or zero, the software reverts to the default Fibonacci sequence. Also, the number of terms must be larger than the number identifying the type of the series. Again, the software takes care of that (defaults to N = P + 50).

The program shows the progressions starting at the 1st significant term: equal to (P-1).

~ The term # 1500 of the progression type 100 is: 2.72539942572167E+423  
~ The 1st root PHI\_0 of the Golden Ratio is .618033988749895  
~ The 2nd root PHI\_1 of the Golden Ratio is 1.61803398874989

The progression ratio between 1.36269971286084E+423 / 2.72539942572167E+423 = .5  
The difference between PHI\_0 and this ratio = 0.118033988749895

The progression ratio between 2.72539942572167E+423 / 1.36269971286084E+423 = 2  
The difference between PHI\_1 and this ratio = -0.381966011250105

![Fibonacci Numbers, Sequences, Progressions plus software, computer programs.](https://saliu.com/HLINE.gif)

Φ Φ This type of progression is almost as easy to memorize as the Martingale progressions. The new Fibonacci-like sequences are somehow harder to memorize - but not much harder. One can print out on paper; then tear the small piece of paper. You may want to copy-and-paste and then print the first terms of the Fibonacci series, from the original one to Fibonacci\_5. You can also use a different color for each line.

1 1 2 3 5 8 13 21 34 55 89 144 233 377 610 987 = Fibonacci  
1 1 2 4 7 13 24 44 81 149 274 504 927 = Fibonacci\_3  
1 1 1 3 6 11 21 41 79 152 293 565 1089 = Fibonacci\_4  
1 1 1 1 4 8 15 29 57 113 222 436 857 1685 = Fibonacci\_5  

1 2 4 8 16 32 64 128 256 512 1024 2048 = Martingale (power of 2 series)

I always consider that each sequence starts at 0. There is an important element in each sequence: the first significant number. They are colored in red in the progression list. For any Fibonacci\_N, the first significant number is equal to N-1. The real Fibonacci series is a type 2 (addition of two numbers).

Is there a type 1 progression? Sure is. The Nth term is equal to the previous term added to itself. Can you spell… Martingale? It starts with 1, followed by 1+1=2, 2+2=4,…. It is the famous power of 2 series.

It is also the natural progression in the computer world. I remember when the personal computers were based on 4-bit processors. The computers advanced steadily to 8-bit, 16-bit, 32-bit and reached now the 64-bit mark in processing power. The computing power doubles every 18 months, says _Moore's Law_. Another fundamental measurement in computing is represented by the _kilobyte_: 2 to the power of 10 = 1024.  
_1, 2, 4, 8, 16, 32, 64, 128, 256, 512, 1024, 2048_....

How about randomness? Is there a random progression? You bet there is. The only rule of a random progression is: The Nth term is greater than the previous term. Guess what I did? I wrote yet another computer program to work in the same manner as my Fibonacci software. The **RandomProgression** app generates random progressions, calculates the ratio between two consecutive terms of a random series, and compares that ratio to the roots of the golden proportion. When N tends to infinity, the ratio between two consecutive terms of a random progression tends to 1. That is, a _uniform series_.

Meanwhile, the ratios in the numerical (non-random) progressions analyzed before tend to 2. Evolution or development is incremental - mathematically and also philosophically. Ain't it like The Intelligent Designer loves mathematics way too much? Can't He spend more time with _Mrs. Intelligent Designer_? NOT! NON! Read about the [_**divine proportion**_](https://saliu.com/bbs/messages/958.html) in real romantic life.

~ The term # 200 of this random series is 9998644  
~ The 1st root PHI\_0 of the Golden Ratio is .618033988749895  
~ The 2nd root PHI\_1 of the Golden Ratio is 1.61803398874989

The random ratio between 9994492 / 9998644 = .999584743691244  
The difference between random and PHI\_0 = 0.38155075494135

The random ratio between 9998644 / 9994492 = 1.00041542881819  
The difference between PHI\_1 and random = 0.617618559931702

![Fibonacci Numbers, Sequences, Random Progressions in gambling, betting.](https://saliu.com/HLINE.gif)

Φ Φ Φ Using the Fibonacci series as betting progressions is similar to applying Martingale progressions. My easiest Martingale method is presented on the the [_**The Best Casino Gambling Systems: Blackjack, Roulette, Limited Martingale Betting, Progressions**_](https://saliu.com/occult-science-gambling.html) page. A much more complex method (consisting of several systems) is presented on the [The Super Roulette Strategy](https://saliu.com/best-roulette-systems.html) page.

The first global approach is to wait for the first loss - then start at the very first 1 in a Fibonacci sequence. Or, more aggressively, start at the first significant number of the respective Fibonacci progression.

The second global approach is to wait for the second or third single win - a sequence like LWLLWLW.... Then start at the very first 1 in a Fibonacci series. Or, more aggressively, start at the first significant number of the respective Fibonacci progression.

You can expand the two global approaches to work as my Martingale methods on the two aforementioned pages.

The traditional Fibonacci is the least aggressive. The Fibonacci 3 and 4 are the most aggressive, while Fibonacci\_5 turns more passive.

![Fibonacci Progressions, Numbers, Sequences, Gambling, Golden Number.](https://saliu.com/HLINE.gif)  

## [Resources in Theory of Probability, Mathematics, Statistics, Combinatorics, Software](https://saliu.com/content/probability.html)

See a comprehensive directory of the pages and materials on the subject of theory of probability, mathematics, statistics, combinatorics, plus software.

-   [Theory of Probability](https://saliu.com/theory-of-probability.html): Best introduction, formulae, algorithms, software.
-   [The Fundamental Formula of Gambling](https://saliu.com/Saliu2.htm) and Winning.
-   [Mathematics Of The Fundamental Formula Of Gambling](https://saliu.com/formula.htm).
-   [Combinatorics: Calculate, generate exponents, permutations](https://saliu.com/permutations.html), sets, arrangements, combinations for any numbers and words.
-   [Standard Deviation](https://saliu.com/deviation.html): Theory, Algorithm, Software.  
    Standard deviation: Basics, mathematics, statistics, formula, software, algorithm.
-   [Standard Deviation, Gauss, Normal, Binomial, Distribution.](https://saliu.com/formula.html)  
    Calculate: Median, degree of certainty, standard deviation, binomial, hypergeometric, average, sums, probabilities, odds.
-   [Caveats in Theory of Probability](https://saliu.com/probability-caveats.html).
-   [Pi Day, Pi, Divine Proportion, Golden Proportion, Golden Number, Phi, Fibonacci Series](https://saliu.com/bbs/messages/958.html).
-   [The Birthday Paradox: Combinatorics, Probability, Software, Pick 3 Lottery, Roulette.](https://saliu.com/birthday.html)
-   [_**Software: Science, Mathematics, Statistics, Lexicographic, Combinatorial**_](https://saliu.com/free-science.html).

[_**The Best-Ever Roulette Strategy, Systems**_](https://saliu.com/best-roulette-systems.html) based on mathematics of progressions and free-for-all.  
~ Yes, the one worth $50,000 ... or $100,000!  
~ An expanded version of the system presented on this page.

![Ion Saliu's Probability Book has valuable  philosophical implications.](https://saliu.com/probability-book-Saliu.jpg) Read Ion Saliu's first book in print: [**_Probability Theory, Live!_**](https://saliu.com/probability-book.html)  
~ Discover profound philosophical implications of the **_Formula of TheEverything_**, including Fibonacci phenomena.

![Read here all about Fibonacci numbers, progressions, formula, algorithm.](https://saliu.com/HLINE.gif)

**[Home](https://saliu.com/index.htm) | [Search](https://saliu.com/Search.htm) | [New Writings](https://saliu.com/bbs/index.html) | [Fundamental Formula](https://saliu.com/formula.htm) | [Odds, Generator](https://saliu.com/calculator_generator.html) | [Contents](https://saliu.com/content/index.html) | [Forums](https://forums.saliu.com/) | [Sitemap](https://saliu.com/sitemap/index.html)**

![Thanks for downloading my software for Fibonacci progressions!](https://saliu.com/HLINE.gif)
