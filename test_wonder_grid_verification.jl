# Wonder Grid Pairing Frequency Verification Test
# Tests pairing frequency calculations, key number selection algorithms, and top pairing filtering logic

using Test
using Dates
using Statistics

# Include necessary types and functions
include("src/types.jl")
include("src/pairing_engine.jl")
include("src/skip_analyzer.jl")
include("src/ffg_calculator.jl")
include("src/wonder_grid_engine.jl")

"""
Test pairing frequency calculation accuracy
"""
function test_pairing_frequency_calculation()
    println("=== Pairing Frequency Calculation Verification ===")
    
    # Create test data with known pairing patterns
    test_draws = [
        LotteryDraw([1, 2, 3, 4, 5], Date(2022, 1, 1), 1),   # Pairs: (1,2), (1,3), (1,4), (1,5), (2,3), (2,4), (2,5), (3,4), (3,5), (4,5)
        LotteryDraw([1, 2, 6, 7, 8], Date(2022, 1, 2), 2),   # Pairs: (1,2), (1,6), (1,7), (1,8), (2,6), (2,7), (2,8), (6,7), (6,8), (7,8)
        LotteryDraw([1, 3, 9, 10, 11], Date(2022, 1, 3), 3), # Pairs: (1,3), (1,9), (1,10), (1,11), (3,9), (3,10), (3,11), (9,10), (9,11), (10,11)
    ]
    
    engine = PairingEngine(test_draws)
    all_pairings = calculate_all_pairings(engine)
    
    # Verify specific pairing frequencies
    # Pair (1,2) should appear 2 times (draws 1 and 2)
    freq_1_2 = get_pairing_frequency(engine, 1, 2)
    @test freq_1_2 == 2
    println("✓ Pair (1,2) frequency: $freq_1_2 (expected: 2)")
    
    # Pair (1,3) should appear 2 times (draws 1 and 3)
    freq_1_3 = get_pairing_frequency(engine, 1, 3)
    @test freq_1_3 == 2
    println("✓ Pair (1,3) frequency: $freq_1_3 (expected: 2)")
    
    # Pair (2,3) should appear 1 time (draw 1 only)
    freq_2_3 = get_pairing_frequency(engine, 2, 3)
    @test freq_2_3 == 1
    println("✓ Pair (2,3) frequency: $freq_2_3 (expected: 1)")
    
    # Pair (5,6) should appear 0 times
    freq_5_6 = get_pairing_frequency(engine, 5, 6)
    @test freq_5_6 == 0
    println("✓ Pair (5,6) frequency: $freq_5_6 (expected: 0)")
    
    # Test total pairing count
    # Each draw has C(5,2) = 10 pairs, so 3 draws = 30 total pairings
    @test engine.total_pairings == 30
    println("✓ Total pairings: $(engine.total_pairings) (expected: 30)")
    
    return true
end

"""
Test top pairing identification logic
"""
function test_top_pairing_identification()
    println("\n=== Top Pairing Identification Verification ===")
    
    # Create test data where number 1 has clear top pairings
    test_draws = [
        LotteryDraw([1, 2, 10, 20, 30], Date(2022, 1, 1), 1),
        LotteryDraw([1, 2, 11, 21, 31], Date(2022, 1, 2), 2),
        LotteryDraw([1, 2, 12, 22, 32], Date(2022, 1, 3), 3),  # (1,2) appears 3 times
        LotteryDraw([1, 3, 13, 23, 33], Date(2022, 1, 4), 4),
        LotteryDraw([1, 3, 14, 24, 34], Date(2022, 1, 5), 5),  # (1,3) appears 2 times
        LotteryDraw([1, 4, 15, 25, 35], Date(2022, 1, 6), 6),  # (1,4) appears 1 time
    ]
    
    engine = PairingEngine(test_draws)
    
    # Test number 1's pairings
    number_1_pairings = get_number_pairings(engine, 1)
    println("Number 1 pairings: $(sort(collect(number_1_pairings), by=x->x[2], rev=true)[1:5])")
    
    # Verify top frequencies
    @test number_1_pairings[2] == 3  # (1,2) should be most frequent
    @test number_1_pairings[3] == 2  # (1,3) should be second
    @test number_1_pairings[4] == 1  # (1,4) should be third
    
    # Test top 25% pairings for number 1
    top_25_analysis = identify_top_25_percent_pairings(engine, 1)
    println("Top 25% analysis for number 1:")
    println("  Count: $(top_25_analysis["actual_count"])")
    println("  Coverage: $(round(top_25_analysis["frequency_coverage"], digits=2))%")

    # With 38 possible pairings for number 1, top 25% should be about 10 pairings
    expected_top_25_count = max(1, round(Int, 38 * 0.25))
    @test top_25_analysis["actual_count"] == expected_top_25_count
    
    println("✓ Top 25% pairing identification works correctly")
    
    return true
end

"""
Test key number selection algorithm
"""
function test_key_number_selection()
    println("\n=== Key Number Selection Algorithm Verification ===")
    
    # Create test data with some numbers having favorable skip patterns
    test_draws = [
        LotteryDraw([1, 5, 10, 15, 20], Date(2022, 1, 1), 1),
        LotteryDraw([2, 6, 11, 16, 21], Date(2022, 1, 2), 2),
        LotteryDraw([1, 7, 12, 17, 22], Date(2022, 1, 3), 3),  # Number 1 has skip = 2
        LotteryDraw([3, 8, 13, 18, 23], Date(2022, 1, 4), 4),
        LotteryDraw([4, 9, 14, 19, 24], Date(2022, 1, 5), 5),  # Current position
    ]
    
    # Create Wonder Grid engine
    skip_analyzer = SkipAnalyzer(test_draws)
    ffg_calculator = FFGCalculator()
    pairing_engine = PairingEngine(test_draws)
    
    wonder_grid = WonderGridEngine(
        ffg_calculator,
        skip_analyzer,
        pairing_engine,
        test_draws
    )
    
    # Test key number selection
    key_numbers = select_key_numbers(wonder_grid)
    println("Selected key numbers: $key_numbers")
    
    # Verify that selected numbers have favorable timing (current skip ≤ FFG median)
    for number in key_numbers
        current_skip = get_current_skip(skip_analyzer, number)
        ffg_median = calculate_ffg_median(ffg_calculator, number, test_draws)
        is_favorable = current_skip <= ffg_median
        
        println("  Number $number: skip=$current_skip, median=$(round(ffg_median, digits=2)), favorable=$is_favorable")
        @test is_favorable
    end
    
    println("✓ Key number selection algorithm works correctly")
    
    return true
end

"""
Test Wonder Grid generation and validation
"""
function test_wonder_grid_generation()
    println("\n=== Wonder Grid Generation Verification ===")
    
    # Create test data
    test_draws = [
        LotteryDraw([1, 2, 3, 4, 5], Date(2022, 1, 1), 1),
        LotteryDraw([1, 2, 6, 7, 8], Date(2022, 1, 2), 2),
        LotteryDraw([1, 3, 9, 10, 11], Date(2022, 1, 3), 3),
        LotteryDraw([2, 3, 12, 13, 14], Date(2022, 1, 4), 4),
    ]
    
    engine = PairingEngine(test_draws)
    
    # Generate Wonder Grid
    wonder_grid = generate_wonder_grid(engine)
    
    # Verify structure
    @test length(wonder_grid) == 39  # Should have entry for each number 1-39
    
    # Check that each number has top pairings
    for number in 1:39
        top_pairings = wonder_grid[number]
        @test isa(top_pairings, Vector{Int})
        
        # Top pairings should be reasonable (not more than 38 other numbers)
        @test length(top_pairings) <= 38
        
        # All pairing numbers should be valid and different from the key number
        for pairing_number in top_pairings
            @test 1 <= pairing_number <= 39
            @test pairing_number != number
        end
    end
    
    println("✓ Wonder Grid generation works correctly")
    
    # Test specific number's top pairings
    number_1_top = wonder_grid[1]
    println("Number 1 top pairings (first 5): $(number_1_top[1:min(5, length(number_1_top))])")
    
    return true
end

"""
Test pairing distribution analysis
"""
function test_pairing_distribution_analysis()
    println("\n=== Pairing Distribution Analysis Verification ===")
    
    # Create test data
    test_draws = [
        LotteryDraw([1, 2, 3, 4, 5], Date(2022, 1, 1), 1),
        LotteryDraw([6, 7, 8, 9, 10], Date(2022, 1, 2), 2),
        LotteryDraw([11, 12, 13, 14, 15], Date(2022, 1, 3), 3),
    ]
    
    engine = PairingEngine(test_draws)
    
    # Analyze pairing distribution
    distribution = analyze_pairing_distribution(engine)
    
    # Verify analysis structure
    required_keys = ["total_pairs", "total_occurrences", "mean_frequency", 
                    "median_frequency", "std_frequency", "min_frequency", "max_frequency"]
    
    for key in required_keys
        @test haskey(distribution, key)
    end
    
    # Verify values make sense
    @test distribution["total_pairs"] == 741  # C(39,2) = 741 possible pairs
    @test distribution["total_occurrences"] == 30  # 3 draws × 10 pairs each
    @test distribution["min_frequency"] == 0  # Some pairs never appear
    @test distribution["max_frequency"] == 1  # In this data, max frequency is 1
    
    println("✓ Pairing distribution analysis works correctly")
    println("  Total pairs: $(distribution["total_pairs"])")
    println("  Total occurrences: $(distribution["total_occurrences"])")
    println("  Mean frequency: $(round(distribution["mean_frequency"], digits=3))")
    
    return true
end

"""
Run all Wonder Grid verification tests
"""
function run_wonder_grid_verification_tests()
    println("Starting Wonder Grid Pairing Frequency Verification Tests...")
    
    try
        test_pairing_frequency_calculation()
        test_top_pairing_identification()
        test_key_number_selection()
        test_wonder_grid_generation()
        test_pairing_distribution_analysis()
        
        println("\n🎉 All Wonder Grid verification tests passed!")
        return true
    catch e
        println("\n❌ Wonder Grid verification tests failed: $e")
        return false
    end
end

# Run tests if this file is executed directly
if abspath(PROGRAM_FILE) == @__FILE__
    run_wonder_grid_verification_tests()
end
