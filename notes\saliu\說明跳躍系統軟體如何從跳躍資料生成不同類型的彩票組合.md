在彩票軟體中，跳躍 (skip) 系統的建構核心在於應用「Fundamental Formula of Gambling (FFG)」來分析歷史開獎數據，並從這些跳躍數據中生成不同類型的彩票組合。

以下說明跳躍系統軟體如何從跳躍資料生成不同類型的彩票組合：

### 1. 跳躍資料的來源與定義

「跳躍 (skip)」被定義為**某個號碼（或數字）在兩次中獎之間所錯過的開獎次數**。例如，如果一個樂透號碼在最近一次開獎中中獎，而它前一次中獎是在第三次開獎中，那麼跳躍值就是 2 (3 - 1 = 2)。這個跳躍值代表了該號碼上次中獎後，錯過了 2 次開獎才再次出現。

- **資料收集**：彩票軟體（例如 SkipSystem）會讀取彩票歷史開獎資料檔案，並為每個號碼計算其在每次開獎中的跳躍值。這些跳躍值會被整理成文字檔案，例如 `FFG*.SYS`。
- **報告呈現**：跳躍報告會顯示每個號碼的最近兩次甚至更多次的跳躍值。例如，「號碼 4」的跳躍資料可能顯示為「3 5 17 3 7 7 10 5 3 11 1 6 6」，其中最前面兩個數字 3 和 5 是最近的兩次跳躍值。

### 2. 基於 FFG 中位數的號碼池選擇

Fundamental Formula of Gambling (FFG) 是跳躍系統的數學基礎。FFG 理論指出，彩票號碼在**FFG 中位數**或更少次的開獎後重複出現的頻率更高。

- **FFG 中位數的計算**：FFG 中位數是指一個事件以 50% 的確定性 (DC) 至少發生一次所需的試驗次數 (N)。彩票軟體會自動計算每個過濾器（包括跳躍）的中位數，並在報告中顯示。
- **號碼池篩選**：SkipSystem 會根據 FFG 中位數自動建立「系統」，這些系統包含了「最有利的投注號碼池」。通常，策略會選擇那些**當前跳躍次數小於或等於 FFG 中位數的號碼**，因為它們被認為更有可能在近期內再次出現。這種方式可以顯著提高中獎機率，甚至在 6/49 樂透中提高七倍。

### 3. 生成不同類型的彩票組合

SkipSystem 及其相關軟體提供了多種方式從這些跳躍資料中生成彩票組合：

- **基於位置和非位置的跳躍系統**：
    
    - **不分位置 (Any Position)**：軟體可以生成不考慮號碼位置的單行組合。例如，在 Powerball 或 Mega Millions 這種 5+1 遊戲中，會生成兩行字串來表示常規號碼和特別號碼的跳躍情況。
    - **分位置 (Positional)**：對於像樂透這種每個號碼都有特定位置的遊戲，軟體可以為每個位置生成單獨的跳躍字串（多行字串），例如 5 個號碼的樂透遊戲會產生 5 行跳躍資料。
- **從系統檔案生成組合**：
    
    - SkipSystem 的 `S = Create the Skip Systems` 功能會創建 `FFG*.SYS` 檔案。然後，透過 `G = Generate Combinations from Systems` 功能，這些系統檔案可以作為輸入來生成彩票組合。
    - 軟體提供兩種生成模式：
        - **字典序 (lexicographical order)**：生成所有符合條件的組合。
        - **隨機生成 (random generation)**：生成隨機的組合。
        - 軟體也提供只計算組合數量而不實際生成的功能。
- **結合「偏好號碼在固定位置」的策略**：
    
    - SkipSystem 可以處理包含「偏好號碼在固定位置」的文字檔案來生成組合。這意味著用戶可以指定某些號碼必須出現在組合的特定位置。
    - 這個功能可以在 SkipSystem 的 `G: Generate Combinations from Systems` 模式下使用，或者在 Super Utilities 的 `M: Make/Break/Position` -> `4: Break 6+/Positional ranges` -> `3: Positional ranges` 功能下實現。
- **策略優化與組合縮減**：
    
    - 跳躍系統通常會生成大量的組合，其中包含許多「不必要」的組合。
    - 為了解決這個問題，軟體會結合 `Purge` 和 `LIE Elimination` (謊言消除) 功能。
    - **LIE Elimination** 是一種反向策略，旨在故意設定預計不會中獎的過濾器，從而將這些極不可能中獎的組合從投注池中剔除。例如，跳躍系統中 FFG 中位數附近的某些模式，或者所有跳躍值都過大或過小的組合，都可以成為 `LIE` 檔案的候選。
    - 透過這些過濾器，跳躍系統可以將彩票組合**大幅減少**到可玩的數量。

總之，跳躍系統軟體利用 FFG 理論對歷史跳躍資料進行深度分析，識別出具有高機率重複出現的號碼模式。然後，它根據這些模式，並結合強大的過濾與「謊言消除」功能，動態地生成和篩選出高效、優化的彩票組合，旨在提高玩家的中獎機會並控制投注成本。