#!/usr/bin/env julia

"""
Test runner for all Wonder Grid Lottery System test suites
Executes all individual test files and provides consolidated results
"""

using Dates

"""
Test suite registry with descriptions
"""
const TEST_SUITES = [
    ("test_comprehensive_unit_tests.jl", "Comprehensive Unit Tests", "All core components"),
    ("test_configuration_system.jl", "Configuration System", "Configuration and parameter management"),
    ("test_result_display.jl", "Result Display", "Display and export functionality"),
    ("test_performance_optimization.jl", "Performance Optimization", "Optimized data structures and caching"),
    ("test_concurrent_processing.jl", "Concurrent Processing", "Parallel processing and thread safety"),
    ("test_enhanced_performance_reporting.jl", "Performance Reporting", "Statistical analysis and reporting"),
    ("test_backtesting_engine.jl", "Backtesting Engine", "Backtesting and validation"),
    ("test_lie_elimination_comprehensive.jl", "LIE Elimination", "Lottery Information Elimination"),
    ("test_lie_wonder_grid_integration.jl", "LIE Integration", "LIE and Wonder Grid integration")
]

"""
Run all test suites and collect results
"""
function run_all_test_suites()
    println("🧪 Wonder Grid Lottery System - Complete Test Suite Runner")
    println("=" ^ 80)
    println("Starting comprehensive testing of all components...")
    println("Test execution time: $(Dates.now())")
    println("Julia version: $(VERSION)")
    println("Available threads: $(nthreads())")
    println()
    
    test_results = Dict{String, Dict{String, Any}}()
    execution_times = Dict{String, Float64}()
    
    total_start_time = time()
    
    for (i, (test_file, test_name, description)) in enumerate(TEST_SUITES)
        println("📋 Test Suite $i/$(length(TEST_SUITES)): $test_name")
        println("   Description: $description")
        println("   File: $test_file")
        
        if !isfile(test_file)
            println("   ❌ Test file not found: $test_file")
            test_results[test_name] = Dict(
                "status" => "not_found",
                "passed" => 0,
                "failed" => 1,
                "error" => "Test file not found"
            )
            execution_times[test_name] = 0.0
            continue
        end
        
        # Execute test suite
        suite_start_time = time()
        
        try
            println("   🚀 Executing test suite...")
            
            # Capture output to avoid cluttering main output
            original_stdout = stdout
            original_stderr = stderr
            
            # Create temporary output capture
            output_buffer = IOBuffer()
            error_buffer = IOBuffer()
            
            redirect_stdout(output_buffer)
            redirect_stderr(error_buffer)
            
            # Execute the test file
            test_result = try
                include(test_file)
                "success"
            catch e
                "error: $e"
            end
            
            # Restore output
            redirect_stdout(original_stdout)
            redirect_stderr(original_stderr)
            
            suite_execution_time = time() - suite_start_time
            execution_times[test_name] = suite_execution_time
            
            # Process results
            if test_result == "success"
                println("   ✅ Test suite completed successfully")
                println("   ⏱️  Execution time: $(round(suite_execution_time, digits=2)) seconds")
                
                test_results[test_name] = Dict(
                    "status" => "success",
                    "passed" => 1,  # Simplified - actual tests may have more detailed results
                    "failed" => 0,
                    "execution_time" => suite_execution_time
                )
            else
                println("   ❌ Test suite failed: $test_result")
                println("   ⏱️  Execution time: $(round(suite_execution_time, digits=2)) seconds")
                
                test_results[test_name] = Dict(
                    "status" => "failed",
                    "passed" => 0,
                    "failed" => 1,
                    "error" => test_result,
                    "execution_time" => suite_execution_time
                )
            end
            
        catch e
            suite_execution_time = time() - suite_start_time
            execution_times[test_name] = suite_execution_time
            
            println("   ❌ Test suite execution failed: $e")
            println("   ⏱️  Execution time: $(round(suite_execution_time, digits=2)) seconds")
            
            test_results[test_name] = Dict(
                "status" => "execution_error",
                "passed" => 0,
                "failed" => 1,
                "error" => string(e),
                "execution_time" => suite_execution_time
            )
        end
        
        println("   " * "-" ^ 60)
        println()
    end
    
    total_execution_time = time() - total_start_time
    
    # Generate consolidated report
    generate_consolidated_test_report(test_results, execution_times, total_execution_time)
    
    return test_results
end

"""
Generate consolidated test report
"""
function generate_consolidated_test_report(test_results::Dict{String, Dict{String, Any}}, 
                                         execution_times::Dict{String, Float64},
                                         total_time::Float64)
    println("📊 CONSOLIDATED TEST RESULTS")
    println("=" ^ 80)
    
    # Calculate overall statistics
    total_suites = length(test_results)
    successful_suites = count(r -> r["status"] == "success" for r in values(test_results))
    failed_suites = total_suites - successful_suites
    success_rate = total_suites > 0 ? (successful_suites / total_suites) * 100 : 0.0
    
    println("Overall Summary:")
    println("  Total Test Suites: $total_suites")
    println("  Successful Suites: $successful_suites")
    println("  Failed Suites: $failed_suites")
    println("  Success Rate: $(round(success_rate, digits=1))%")
    println("  Total Execution Time: $(round(total_time, digits=2)) seconds")
    println()
    
    # Detailed results table
    println("Detailed Results:")
    println("Suite Name                    | Status    | Time (s) | Notes")
    println("-" ^ 80)
    
    for (suite_name, results) in sort(collect(test_results))
        status_icon = results["status"] == "success" ? "✅" : "❌"
        status_text = results["status"] == "success" ? "PASS" : "FAIL"
        exec_time = round(results["execution_time"], digits=2)
        
        # Truncate long suite names
        display_name = length(suite_name) > 28 ? suite_name[1:25] * "..." : suite_name
        
        notes = ""
        if haskey(results, "error")
            error_msg = string(results["error"])
            notes = length(error_msg) > 20 ? error_msg[1:17] * "..." : error_msg
        end
        
        println("$(rpad(display_name, 29)) | $status_icon $(rpad(status_text, 7)) | $(rpad(exec_time, 8)) | $notes")
    end
    
    println()
    
    # Performance analysis
    if !isempty(execution_times)
        println("Performance Analysis:")
        fastest_suite = argmin(execution_times)
        slowest_suite = argmax(execution_times)
        avg_time = sum(values(execution_times)) / length(execution_times)
        
        println("  Fastest Suite: $fastest_suite ($(round(execution_times[fastest_suite], digits=2))s)")
        println("  Slowest Suite: $slowest_suite ($(round(execution_times[slowest_suite], digits=2))s)")
        println("  Average Time: $(round(avg_time, digits=2))s")
        println()
    end
    
    # Recommendations
    println("Recommendations:")
    if success_rate == 100.0
        println("  🎉 Perfect! All test suites passed successfully.")
        println("  ✅ The Wonder Grid system is ready for production use.")
    elseif success_rate >= 90.0
        println("  ✅ Excellent! Most test suites passed.")
        println("  🔍 Review failed suites for minor issues.")
    elseif success_rate >= 75.0
        println("  ⚠️  Good progress, but some issues need attention.")
        println("  🔧 Focus on fixing failed test suites.")
    elseif success_rate >= 50.0
        println("  ⚠️  Moderate success rate. Significant issues detected.")
        println("  🚨 Comprehensive review and fixes required.")
    else
        println("  ❌ Low success rate. Major issues detected.")
        println("  🚨 System requires extensive debugging and fixes.")
    end
    
    if failed_suites > 0
        println("\nFailed Test Suites:")
        for (suite_name, results) in test_results
            if results["status"] != "success"
                println("  ❌ $suite_name: $(results["status"])")
                if haskey(results, "error")
                    println("     Error: $(results["error"])")
                end
            end
        end
    end
    
    # Save detailed report to file
    save_detailed_report(test_results, execution_times, total_time, success_rate)
    
    println("\n" * "=" ^ 80)
    println("Test execution completed. Check 'consolidated_test_report.txt' for detailed results.")
end

"""
Save detailed report to file
"""
function save_detailed_report(test_results::Dict{String, Dict{String, Any}}, 
                            execution_times::Dict{String, Float64},
                            total_time::Float64, 
                            success_rate::Float64)
    report_filename = "consolidated_test_report.txt"
    
    open(report_filename, "w") do file
        println(file, "WONDER GRID LOTTERY SYSTEM - CONSOLIDATED TEST REPORT")
        println(file, "=" ^ 70)
        println(file, "Generated: $(Dates.now())")
        println(file, "Julia Version: $(VERSION)")
        println(file, "Available Threads: $(nthreads())")
        println(file, "Total Execution Time: $(round(total_time, digits=2)) seconds")
        println(file, "")
        
        # Executive summary
        total_suites = length(test_results)
        successful_suites = count(r -> r["status"] == "success" for r in values(test_results))
        failed_suites = total_suites - successful_suites
        
        println(file, "EXECUTIVE SUMMARY")
        println(file, "-" ^ 30)
        println(file, "Total Test Suites Executed: $total_suites")
        println(file, "Successful Test Suites: $successful_suites")
        println(file, "Failed Test Suites: $failed_suites")
        println(file, "Overall Success Rate: $(round(success_rate, digits=1))%")
        println(file, "")
        
        # Detailed results
        println(file, "DETAILED TEST RESULTS")
        println(file, "-" ^ 30)
        
        for (suite_name, results) in sort(collect(test_results))
            println(file, "")
            println(file, "Test Suite: $suite_name")
            println(file, "  Status: $(results["status"])")
            println(file, "  Execution Time: $(round(results["execution_time"], digits=3)) seconds")
            
            if haskey(results, "error")
                println(file, "  Error Details: $(results["error"])")
            end
            
            if haskey(results, "passed") && haskey(results, "failed")
                println(file, "  Tests Passed: $(results["passed"])")
                println(file, "  Tests Failed: $(results["failed"])")
            end
        end
        
        # Performance metrics
        if !isempty(execution_times)
            println(file, "")
            println(file, "PERFORMANCE METRICS")
            println(file, "-" ^ 30)
            
            sorted_times = sort(collect(execution_times), by=x->x[2])
            
            println(file, "Execution Times (sorted by duration):")
            for (suite_name, exec_time) in sorted_times
                println(file, "  $(rpad(suite_name, 35)): $(round(exec_time, digits=3))s")
            end
            
            avg_time = sum(values(execution_times)) / length(execution_times)
            println(file, "")
            println(file, "Average Execution Time: $(round(avg_time, digits=3)) seconds")
            println(file, "Total Test Time: $(round(sum(values(execution_times)), digits=3)) seconds")
        end
        
        # System information
        println(file, "")
        println(file, "SYSTEM INFORMATION")
        println(file, "-" ^ 30)
        println(file, "Operating System: $(Sys.KERNEL)")
        println(file, "CPU Threads Available: $(nthreads())")
        println(file, "Julia Compilation Mode: $(Base.JLOptions().compile_enabled)")
        
        # Test environment
        println(file, "")
        println(file, "TEST ENVIRONMENT")
        println(file, "-" ^ 30)
        println(file, "Working Directory: $(pwd())")
        println(file, "Test Files Location: Current directory")
        println(file, "Source Files Location: src/ directory")
        
        println(file, "")
        println(file, "=" ^ 70)
        println(file, "End of Consolidated Test Report")
    end
    
    println("📄 Detailed report saved to: $report_filename")
end

"""
Quick test runner for specific suites
"""
function run_specific_tests(test_names::Vector{String})
    println("🎯 Running Specific Test Suites")
    println("=" ^ 50)
    
    available_tests = Dict(suite[2] => suite[1] for suite in TEST_SUITES)
    
    for test_name in test_names
        if haskey(available_tests, test_name)
            test_file = available_tests[test_name]
            println("Running: $test_name ($test_file)")
            
            try
                include(test_file)
                println("✅ $test_name completed")
            catch e
                println("❌ $test_name failed: $e")
            end
            println()
        else
            println("❌ Test suite not found: $test_name")
            println("Available test suites:")
            for suite_name in keys(available_tests)
                println("  - $suite_name")
            end
            println()
        end
    end
end

"""
Interactive test runner
"""
function interactive_test_runner()
    println("🎮 Interactive Test Runner")
    println("=" ^ 40)
    
    println("Available test suites:")
    for (i, (_, test_name, description)) in enumerate(TEST_SUITES)
        println("  $i. $test_name - $description")
    end
    
    println("\nOptions:")
    println("  'all' - Run all test suites")
    println("  '1,2,3' - Run specific test suites by number")
    println("  'quit' - Exit")
    
    while true
        print("\nEnter your choice: ")
        choice = strip(readline())
        
        if lowercase(choice) == "quit"
            println("👋 Exiting interactive test runner")
            break
        elseif lowercase(choice) == "all"
            run_all_test_suites()
            break
        else
            try
                # Parse comma-separated numbers
                indices = [parse(Int, strip(s)) for s in split(choice, ",")]
                
                # Validate indices
                valid_indices = [i for i in indices if 1 <= i <= length(TEST_SUITES)]
                
                if !isempty(valid_indices)
                    selected_tests = [TEST_SUITES[i][2] for i in valid_indices]
                    run_specific_tests(selected_tests)
                    break
                else
                    println("❌ Invalid selection. Please enter numbers between 1 and $(length(TEST_SUITES))")
                end
            catch
                println("❌ Invalid input. Please enter 'all', numbers separated by commas, or 'quit'")
            end
        end
    end
end

# Main execution
if abspath(PROGRAM_FILE) == @__FILE__
    if length(ARGS) == 0
        # No arguments - run all tests
        run_all_test_suites()
    elseif ARGS[1] == "interactive"
        # Interactive mode
        interactive_test_runner()
    elseif ARGS[1] == "list"
        # List available tests
        println("Available test suites:")
        for (i, (test_file, test_name, description)) in enumerate(TEST_SUITES)
            println("  $i. $test_name")
            println("     File: $test_file")
            println("     Description: $description")
            println()
        end
    else
        # Run specific tests
        run_specific_tests(ARGS)
    end
end