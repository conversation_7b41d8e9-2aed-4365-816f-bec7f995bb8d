---
created: 2024-06-21T09:09:47 (UTC +08:00)
tags: [strategy,wonder,grid,wonder-grid,lotto,win,pairs,pairing,pairings,odds,software,lottery,lotto,system,systems,frequency,statistics,drawings,draws,number,numbers,jackpot,combinations]
source: https://saliu.com/bbs/messages/647.html
author: 
---

# Wonder Grid Lottery Strategy Hit Jackpot Belgium Lotto

> ## Excerpt
> The first lottery drawing I checked contained my favorite lotto number, 5 lotto numbers of top 25% pairs, lotto strategy I did not play in the Belgian lottery.

---
Posted by [<PERSON>](mailto:<EMAIL>) on March 29, 2001.

In Reply to: [_**Wonder Grid Lottery Strategy Plays Pairs of Lotto Numbers**_](https://saliu.com/bbs/messages/638.html) posted by <PERSON> on March 24, 2001.

Hi <PERSON>,

First of all, I'd like to say that I found your Util-6 program very useful. In my pairs6 file, from the last 6 combinations only one or two (maximum) show a frequency of zero (with 200 drawings analyzed). I do get 6 or so combs with a frequency of zero when just analyzing the last 100 drawings. Maybe I should stick to analyzing the last 100 drawings in the future as well (instead of 200). What d' you think ?

As for your new possible killer lotto strategy...

I checked out your strategy with one of my favorite numbers. the first drawing I checked containing my fav # also had 5 numbers out of my favorite number's top 25% pairs !

Too bad you didn't come up with this strategy a little earlier :)

![Wonder grid lottery strategy hit the lotto jackpot in Belgium Lottery.](https://saliu.com/bbs/messages/HLINE.gif)

## [Resources in Lottery Software, Systems, Lotto Wheeling](https://saliu.com/content/lottery.html)

-   Download [**lottery software, lotto software**](https://saliu.com/infodown.html).
    
    Follow Ups:  
    
-   [_**Wonder Grid Lottery Strategy Plays Pairs of Lotto Numbers**_](https://saliu.com/bbs/messages/638.html) **Ion Saliu** _3/24/2001._
-   [Optimal lotto drawings range to analyze for lottery wonder grid system](https://saliu.com/bbs/messages/663.html) **Ion Saliu** _4/07/2001._
-   [The optimal lottery drawings range to analyze - Illinois Lotto](https://saliu.com/bbs/messages/664.html) **Bill Smith** _4/09/2001._
-   [BELLOTTO, BELLBET, BETUS: Software to generate random combinations for lotto, lottery, gambling](https://saliu.com/bbs/messages/665.html) **Ion Saliu** _4/09/2001._
-   [_**Lottery Pairs System, Lotto Pair Strategy**_](https://saliu.com/bbs/messages/645.html) **lottoscorp** _3/28/2001._
-   [Likely winning lotto numbers to wheel by best lotto wheels software](https://saliu.com/bbs/messages/644.html) **KM** _3/27/2001._
-   [Lottery system: Lotto numbers, skips of lotto numbers, sum of gaps](https://saliu.com/bbs/messages/646.html) **BigJer** _3/28/2001._
-   [Lotto deltas in powerful, winning lottery software](https://saliu.com/bbs/messages/648.html) **KM** _3/29/2001._
-   [Lottery Analysis in the Spirit of Ramanujan – The Great Indian Mathematician](https://saliu.com/bbs/messages/641.html) **Ramanujan** _3/25/2001._

![Ion Saliu: Software, Programs, Apps, Systems, Strategies.](https://saliu.com/bbs/messages/HLINE.gif)

Comments:  

![Wonder grid lottery strategy hit the lotto jackpot in Belgium Lottery.](https://saliu.com/bbs/messages/HLINE.gif)

**[Home](https://saliu.com/index.htm) | [Search](https://saliu.com/Search.htm) | [New Writings](https://saliu.com/bbs/index.html) | [Software](https://saliu.com/infodown.html) | [Odds, Generator](https://saliu.com/calculator_generator.html) | [Contents](https://saliu.com/content/index.html) | [Forums](https://forums.saliu.com/) | [Sitemap](https://saliu.com/sitemap/index.html)**

![Ion Saliu: Software, Programs, Apps, Systems, Strategies.](https://saliu.com/bbs/messages/HLINE.gif)
