# Result display and export functionality for Wonder Grid Lottery System

using Dates
using Printf

"""
Result display and export manager
"""
struct ResultDisplayManager
    show_progress::Bool
    export_format::String
    output_directory::String
    
    function ResultDisplayManager(show_progress::Bool=true, export_format::String="csv", output_directory::String="results")
        # Create output directory if it doesn't exist
        if !isdir(output_directory)
            mkpath(output_directory)
        end
        new(show_progress, export_format, output_directory)
    end
end

"""
Progress indicator for long-running operations
"""
mutable struct ProgressIndicator
    total::Int
    current::Int
    start_time::Float64
    last_update::Float64
    description::String
    
    function ProgressIndicator(total::Int, description::String="Processing")
        new(total, 0, time(), time(), description)
    end
end

"""
Update progress indicator
"""
function update_progress!(progress::ProgressIndicator, current::Int=progress.current + 1)
    progress.current = current
    current_time = time()
    
    # Update every 0.5 seconds or at completion
    if current_time - progress.last_update > 0.5 || current == progress.total
        progress.last_update = current_time
        
        percentage = round(current / progress.total * 100, digits=1)
        elapsed = current_time - progress.start_time
        
        if current > 0
            estimated_total = elapsed * progress.total / current
            remaining = estimated_total - elapsed
            
            print("\r$(progress.description): $(current)/$(progress.total) ($(percentage)%) ")
            print("Elapsed: $(format_time(elapsed)) ETA: $(format_time(remaining))")
        else
            print("\r$(progress.description): Starting...")
        end
        
        if current == progress.total
            println("\n✅ $(progress.description) completed in $(format_time(elapsed))")
        end
        
        flush(stdout)
    end
end

"""
Format time duration for display
"""
function format_time(seconds::Float64)::String
    if seconds < 60
        return @sprintf("%.1fs", seconds)
    elseif seconds < 3600
        minutes = floor(seconds / 60)
        secs = seconds - minutes * 60
        return @sprintf("%.0fm %.0fs", minutes, secs)
    else
        hours = floor(seconds / 3600)
        minutes = floor((seconds - hours * 3600) / 60)
        return @sprintf("%.0fh %.0fm", hours, minutes)
    end
end

"""
Display lottery combinations in standard readable format
"""
function display_combinations(combinations::Vector{Vector{Int}}, title::String="Generated Combinations")
    println("=" ^ 60)
    println(title)
    println("=" ^ 60)
    
    if isempty(combinations)
        println("No combinations to display.")
        return
    end
    
    println("Total combinations: $(length(combinations))")
    println("Format: [Number 1, Number 2, Number 3, Number 4, Number 5]")
    println("-" ^ 60)
    
    # Display combinations in organized format
    for (i, combo) in enumerate(combinations)
        # Format numbers with consistent spacing
        formatted_combo = join([@sprintf("%2d", num) for num in combo], ", ")
        println("$(lpad(i, 4)): [$formatted_combo]")
        
        # Add spacing every 10 combinations for readability
        if i % 10 == 0 && i < length(combinations)
            println()
        end
    end
    
    println("=" ^ 60)
end

"""
Display combinations in compact grid format
"""
function display_combinations_grid(combinations::Vector{Vector{Int}}, title::String="Combinations Grid", columns::Int=5)
    println("=" ^ 80)
    println(title)
    println("=" ^ 80)
    
    if isempty(combinations)
        println("No combinations to display.")
        return
    end
    
    println("Total combinations: $(length(combinations)) | Grid format: $columns per row")
    println("-" ^ 80)
    
    for i in 1:columns:length(combinations)
        row_combinations = combinations[i:min(i + columns - 1, length(combinations))]
        
        # Print combination numbers
        combo_numbers = [lpad(j, 3) for j in i:min(i + columns - 1, length(combinations))]
        println("Combo: " * join(combo_numbers, "  "))
        
        # Print the actual combinations
        combo_strings = [join([@sprintf("%2d", num) for num in combo], ",") for combo in row_combinations]
        println("       " * join(["[$combo]" for combo in combo_strings], " "))
        println()
    end
    
    println("=" ^ 80)
end

"""
Display analysis results with clear formatting
"""
function display_analysis_results(results::Dict{String, Any}, title::String="Analysis Results")
    println("=" ^ 70)
    println(title)
    println("=" ^ 70)
    
    # Display key metrics
    if haskey(results, "key_number")
        println("🔑 Key Number: $(results["key_number"])")
    end
    
    if haskey(results, "total_combinations")
        println("📊 Total Combinations: $(results["total_combinations"])")
    end
    
    if haskey(results, "generation_time")
        println("⏱️  Generation Time: $(format_time(results["generation_time"]))")
    end
    
    if haskey(results, "analysis_date")
        println("📅 Analysis Date: $(results["analysis_date"])")
    end
    
    println("-" ^ 70)
    
    # Display statistical information if available
    if haskey(results, "statistics")
        stats = results["statistics"]
        println("📈 STATISTICAL SUMMARY")
        println("-" ^ 30)
        
        for (key, value) in stats
            if isa(value, Float64)
                println("  $(key): $(round(value, digits=4))")
            else
                println("  $(key): $(value)")
            end
        end
        println()
    end
    
    # Display performance metrics if available
    if haskey(results, "performance")
        perf = results["performance"]
        println("🚀 PERFORMANCE METRICS")
        println("-" ^ 30)
        
        for (key, value) in perf
            if isa(value, Float64)
                println("  $(key): $(round(value, digits=3))")
            else
                println("  $(key): $(value)")
            end
        end
        println()
    end
    
    # Display recommendations if available
    if haskey(results, "recommendations")
        println("💡 RECOMMENDATIONS")
        println("-" ^ 30)
        
        if isa(results["recommendations"], Vector)
            for (i, rec) in enumerate(results["recommendations"])
                println("  $i. $rec")
            end
        else
            println("  $(results["recommendations"])")
        end
        println()
    end
    
    println("=" ^ 70)
end

"""
Export combinations to CSV format
"""
function export_combinations_csv(combinations::Vector{Vector{Int}}, filename::String, 
                                metadata::Dict{String, Any}=Dict{String, Any}())
    open(filename, "w") do file
        # Write header with metadata
        println(file, "# Wonder Grid Lottery System - Generated Combinations")
        println(file, "# Generated: $(Dates.now())")
        
        for (key, value) in metadata
            println(file, "# $key: $value")
        end
        
        println(file, "#")
        
        # Write CSV header
        println(file, "Combination_ID,Number_1,Number_2,Number_3,Number_4,Number_5,Sum,Range,Odd_Count,Even_Count")
        
        # Write combinations with additional analysis
        for (i, combo) in enumerate(combinations)
            sum_numbers = sum(combo)
            range_numbers = maximum(combo) - minimum(combo)
            odd_count = count(n -> n % 2 == 1, combo)
            even_count = 5 - odd_count
            
            println(file, "$i,$(join(combo, ",")),$(sum_numbers),$(range_numbers),$(odd_count),$(even_count)")
        end
    end
    
    println("✅ Combinations exported to: $filename")
end

"""
Export combinations to TXT format (human readable)
"""
function export_combinations_txt(combinations::Vector{Vector{Int}}, filename::String,
                                metadata::Dict{String, Any}=Dict{String, Any}())
    open(filename, "w") do file
        println(file, "WONDER GRID LOTTERY SYSTEM - GENERATED COMBINATIONS")
        println(file, "=" ^ 60)
        println(file, "Generated: $(Dates.now())")
        
        for (key, value) in metadata
            println(file, "$key: $value")
        end
        
        println(file, "")
        println(file, "Total Combinations: $(length(combinations))")
        println(file, "Format: [Number 1, Number 2, Number 3, Number 4, Number 5]")
        println(file, "-" ^ 60)
        
        for (i, combo) in enumerate(combinations)
            formatted_combo = join([@sprintf("%2d", num) for num in combo], ", ")
            println(file, "$(lpad(i, 4)): [$formatted_combo]")
            
            # Add spacing every 10 combinations
            if i % 10 == 0 && i < length(combinations)
                println(file, "")
            end
        end
        
        println(file, "=" ^ 60)
        println(file, "End of combinations list")
    end
    
    println("✅ Combinations exported to: $filename")
end

"""
Export combinations to JSON format
"""
function export_combinations_json(combinations::Vector{Vector{Int}}, filename::String,
                                 metadata::Dict{String, Any}=Dict{String, Any}())
    # Create JSON structure
    json_data = Dict{String, Any}(
        "metadata" => merge(metadata, Dict(
            "generated_date" => string(Dates.now()),
            "total_combinations" => length(combinations),
            "format" => "Wonder Grid Lottery System"
        )),
        "combinations" => [
            Dict(
                "id" => i,
                "numbers" => combo,
                "sum" => sum(combo),
                "range" => maximum(combo) - minimum(combo),
                "odd_count" => count(n -> n % 2 == 1, combo),
                "even_count" => count(n -> n % 2 == 0, combo)
            ) for (i, combo) in enumerate(combinations)
        ]
    )
    
    # Write JSON (simple format since we're not importing JSON package)
    open(filename, "w") do file
        println(file, "{")
        println(file, "  \"metadata\": {")
        
        metadata_items = collect(json_data["metadata"])
        for (i, (key, value)) in enumerate(metadata_items)
            comma = i < length(metadata_items) ? "," : ""
            if isa(value, String)
                println(file, "    \"$key\": \"$value\"$comma")
            else
                println(file, "    \"$key\": $value$comma")
            end
        end
        
        println(file, "  },")
        println(file, "  \"combinations\": [")
        
        for (i, combo_data) in enumerate(json_data["combinations"])
            comma = i < length(json_data["combinations"]) ? "," : ""
            println(file, "    {")
            println(file, "      \"id\": $(combo_data["id"]),")
            println(file, "      \"numbers\": [$(join(combo_data["numbers"], ", "))],")
            println(file, "      \"sum\": $(combo_data["sum"]),")
            println(file, "      \"range\": $(combo_data["range"]),")
            println(file, "      \"odd_count\": $(combo_data["odd_count"]),")
            println(file, "      \"even_count\": $(combo_data["even_count"])")
            println(file, "    }$comma")
        end
        
        println(file, "  ]")
        println(file, "}")
    end
    
    println("✅ Combinations exported to: $filename")
end

"""
Export analysis results to comprehensive report
"""
function export_analysis_report(results::Dict{String, Any}, filename::String)
    open(filename, "w") do file
        println(file, "WONDER GRID LOTTERY SYSTEM - ANALYSIS REPORT")
        println(file, "=" ^ 70)
        println(file, "Generated: $(Dates.now())")
        println(file, "")
        
        # Export all result data
        for (section, data) in results
            println(file, "$(uppercase(section)):")
            println(file, "-" ^ 30)
            
            if isa(data, Dict)
                for (key, value) in data
                    if isa(value, Float64)
                        println(file, "  $key: $(round(value, digits=4))")
                    elseif isa(value, Vector) && length(value) <= 10
                        println(file, "  $key: [$(join(value, ", "))]")
                    else
                        println(file, "  $key: $value")
                    end
                end
            else
                println(file, "  $data")
            end
            
            println(file, "")
        end
        
        println(file, "=" ^ 70)
        println(file, "End of analysis report")
    end
    
    println("✅ Analysis report exported to: $filename")
end

"""
Batch export combinations in multiple formats
"""
function batch_export_combinations(combinations::Vector{Vector{Int}}, base_filename::String,
                                  formats::Vector{String}=["csv", "txt", "json"],
                                  metadata::Dict{String, Any}=Dict{String, Any}())
    
    println("📤 Starting batch export of $(length(combinations)) combinations...")
    
    exported_files = String[]
    
    for format in formats
        try
            filename = "$(base_filename).$(format)"
            
            if format == "csv"
                export_combinations_csv(combinations, filename, metadata)
            elseif format == "txt"
                export_combinations_txt(combinations, filename, metadata)
            elseif format == "json"
                export_combinations_json(combinations, filename, metadata)
            else
                println("⚠️  Unknown format: $format")
                continue
            end
            
            push!(exported_files, filename)
            
        catch e
            println("❌ Error exporting $format format: $e")
        end
    end
    
    println("✅ Batch export completed. Files created:")
    for file in exported_files
        println("  📄 $file")
    end
    
    return exported_files
end

"""
Display export summary
"""
function display_export_summary(exported_files::Vector{String}, combinations_count::Int)
    println("\n" * "=" ^ 60)
    println("EXPORT SUMMARY")
    println("=" ^ 60)
    
    println("📊 Total combinations exported: $combinations_count")
    println("📁 Files created: $(length(exported_files))")
    println()
    
    for file in exported_files
        if isfile(file)
            file_size = filesize(file)
            size_str = if file_size < 1024
                "$(file_size) bytes"
            elseif file_size < 1024^2
                "$(round(file_size / 1024, digits=1)) KB"
            else
                "$(round(file_size / 1024^2, digits=1)) MB"
            end
            
            println("  ✅ $file ($size_str)")
        else
            println("  ❌ $file (not found)")
        end
    end
    
    println("\n📍 All files saved in current directory")
    println("=" ^ 60)
end

"""
Interactive combination viewer
"""
function interactive_combination_viewer(combinations::Vector{Vector{Int}})
    if isempty(combinations)
        println("No combinations to view.")
        return
    end
    
    println("🔍 Interactive Combination Viewer")
    println("Commands: 'n' (next), 'p' (previous), 'g' (goto), 's' (search), 'q' (quit)")
    println("=" ^ 60)
    
    current_index = 1
    page_size = 10
    
    while true
        # Display current page
        start_idx = current_index
        end_idx = min(start_idx + page_size - 1, length(combinations))
        
        println("\nShowing combinations $start_idx-$end_idx of $(length(combinations)):")
        println("-" ^ 40)
        
        for i in start_idx:end_idx
            combo = combinations[i]
            formatted_combo = join([@sprintf("%2d", num) for num in combo], ", ")
            sum_combo = sum(combo)
            println("$(lpad(i, 4)): [$formatted_combo] (sum: $sum_combo)")
        end
        
        print("\nCommand (n/p/g/s/q): ")
        command = strip(readline())
        
        if command == "q"
            break
        elseif command == "n"
            if end_idx < length(combinations)
                current_index = end_idx + 1
            else
                println("Already at the end.")
            end
        elseif command == "p"
            if start_idx > 1
                current_index = max(1, start_idx - page_size)
            else
                println("Already at the beginning.")
            end
        elseif startswith(command, "g")
            try
                goto_num = parse(Int, strip(command[2:end]))
                if 1 <= goto_num <= length(combinations)
                    current_index = goto_num
                else
                    println("Invalid combination number. Range: 1-$(length(combinations))")
                end
            catch
                println("Invalid goto command. Use 'g <number>'")
            end
        elseif startswith(command, "s")
            try
                search_num = parse(Int, strip(command[2:end]))
                found_indices = [i for (i, combo) in enumerate(combinations) if search_num in combo]
                
                if !isempty(found_indices)
                    println("Number $search_num found in $(length(found_indices)) combinations:")
                    for (j, idx) in enumerate(found_indices[1:min(10, length(found_indices))])
                        combo = combinations[idx]
                        formatted_combo = join([@sprintf("%2d", num) for num in combo], ", ")
                        println("  $idx: [$formatted_combo]")
                    end
                    if length(found_indices) > 10
                        println("  ... and $(length(found_indices) - 10) more")
                    end
                else
                    println("Number $search_num not found in any combination.")
                end
            catch
                println("Invalid search command. Use 's <number>'")
            end
        else
            println("Unknown command. Use n/p/g/s/q")
        end
    end
    
    println("👋 Exiting combination viewer.")
end