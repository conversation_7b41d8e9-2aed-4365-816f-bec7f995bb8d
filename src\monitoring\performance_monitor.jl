# Performance Monitor
# 性能監控系統 - 追蹤和分析系統性能指標

using Dates
using Statistics

"""
性能指標結構
"""
mutable struct PerformanceMetric
    name::String
    value::Float64
    unit::String
    timestamp::DateTime
    category::String
    
    function PerformanceMetric(name::String, value::Float64, unit::String = "", category::String = "general")
        new(name, value, unit, now(), category)
    end
end

"""
性能監控器
"""
mutable struct PerformanceMonitor
    metrics::Vector{PerformanceMetric}
    start_time::DateTime
    active_timers::Dict{String, DateTime}
    thresholds::Dict{String, Float64}
    alerts::Vector{String}
    
    function PerformanceMonitor()
        new(
            Vector{PerformanceMetric}(),
            now(),
            Dict{String, DateTime}(),
            Dict{String, Float64}(),
            Vector{String}()
        )
    end
end

"""
記錄性能指標
"""
function record_metric!(monitor::PerformanceMonitor, name::String, value::Float64, unit::String = "", category::String = "general")
    metric = PerformanceMetric(name, value, unit, category)
    push!(monitor.metrics, metric)
    
    # 檢查閾值警告
    check_threshold_alert!(monitor, name, value)
end

"""
開始計時
"""
function start_timer!(monitor::PerformanceMonitor, timer_name::String)
    monitor.active_timers[timer_name] = now()
end

"""
結束計時並記錄
"""
function end_timer!(monitor::PerformanceMonitor, timer_name::String, category::String = "timing")::Float64
    if !haskey(monitor.active_timers, timer_name)
        @warn "計時器 '$timer_name' 未啟動"
        return 0.0
    end
    
    start_time = monitor.active_timers[timer_name]
    end_time = now()
    duration_ms = Dates.value(end_time - start_time)
    
    record_metric!(monitor, timer_name, duration_ms, "ms", category)
    delete!(monitor.active_timers, timer_name)
    
    return duration_ms
end

"""
設置性能閾值
"""
function set_threshold!(monitor::PerformanceMonitor, metric_name::String, threshold::Float64)
    monitor.thresholds[metric_name] = threshold
end

"""
檢查閾值警告
"""
function check_threshold_alert!(monitor::PerformanceMonitor, metric_name::String, value::Float64)
    if haskey(monitor.thresholds, metric_name)
        threshold = monitor.thresholds[metric_name]
        if value > threshold
            alert_msg = "性能警告: $metric_name = $value 超過閾值 $threshold"
            push!(monitor.alerts, alert_msg)
            @warn alert_msg
        end
    end
end

"""
獲取指標統計
"""
function get_metric_statistics(monitor::PerformanceMonitor, metric_name::String)::Dict{String, Any}
    matching_metrics = filter(m -> m.name == metric_name, monitor.metrics)
    
    if isempty(matching_metrics)
        return Dict("error" => "未找到指標: $metric_name")
    end
    
    values = [m.value for m in matching_metrics]
    
    return Dict(
        "count" => length(values),
        "mean" => mean(values),
        "median" => median(values),
        "min" => minimum(values),
        "max" => maximum(values),
        "std" => std(values),
        "latest" => values[end],
        "unit" => matching_metrics[1].unit,
        "category" => matching_metrics[1].category
    )
end

"""
獲取分類統計
"""
function get_category_statistics(monitor::PerformanceMonitor, category::String)::Dict{String, Any}
    category_metrics = filter(m -> m.category == category, monitor.metrics)
    
    if isempty(category_metrics)
        return Dict("error" => "未找到分類: $category")
    end
    
    # 按指標名稱分組
    metric_groups = Dict{String, Vector{Float64}}()
    for metric in category_metrics
        if !haskey(metric_groups, metric.name)
            metric_groups[metric.name] = Float64[]
        end
        push!(metric_groups[metric.name], metric.value)
    end
    
    # 計算每個指標的統計
    stats = Dict{String, Any}()
    for (name, values) in metric_groups
        stats[name] = Dict(
            "count" => length(values),
            "mean" => mean(values),
            "median" => median(values),
            "min" => minimum(values),
            "max" => maximum(values)
        )
    end
    
    return Dict(
        "category" => category,
        "total_metrics" => length(category_metrics),
        "unique_metric_names" => length(metric_groups),
        "metrics" => stats
    )
end

"""
獲取性能報告
"""
function get_performance_report(monitor::PerformanceMonitor)::Dict{String, Any}
    total_runtime = Dates.value(now() - monitor.start_time) / 1000.0  # 秒
    
    # 按分類統計
    categories = unique([m.category for m in monitor.metrics])
    category_stats = Dict{String, Any}()
    for category in categories
        category_stats[category] = get_category_statistics(monitor, category)
    end
    
    # 最近的指標
    recent_metrics = if length(monitor.metrics) > 10
        monitor.metrics[end-9:end]
    else
        monitor.metrics
    end
    
    return Dict(
        "total_runtime_seconds" => total_runtime,
        "total_metrics_recorded" => length(monitor.metrics),
        "active_timers" => length(monitor.active_timers),
        "total_alerts" => length(monitor.alerts),
        "categories" => category_stats,
        "recent_metrics" => [(m.name, m.value, m.unit, m.timestamp) for m in recent_metrics],
        "alerts" => monitor.alerts
    )
end

"""
清理舊指標
"""
function cleanup_old_metrics!(monitor::PerformanceMonitor, hours_to_keep::Int = 24)
    cutoff_time = now() - Hour(hours_to_keep)
    
    original_count = length(monitor.metrics)
    filter!(m -> m.timestamp >= cutoff_time, monitor.metrics)
    cleaned_count = original_count - length(monitor.metrics)
    
    # 清理舊警告
    if length(monitor.alerts) > 100
        monitor.alerts = monitor.alerts[end-49:end]  # 保留最近50個警告
    end
    
    return cleaned_count
end

"""
系統性能監控器
"""
mutable struct SystemPerformanceMonitor
    monitor::PerformanceMonitor
    auto_cleanup::Bool
    last_cleanup::DateTime
    
    function SystemPerformanceMonitor(auto_cleanup::Bool = true)
        monitor = PerformanceMonitor()
        
        # 設置默認閾值
        set_threshold!(monitor, "skip_calculation_time", 50.0)  # 50ms
        set_threshold!(monitor, "filter_calculation_time", 100.0)  # 100ms
        set_threshold!(monitor, "cache_miss_rate", 0.5)  # 50%
        set_threshold!(monitor, "memory_usage_mb", 500.0)  # 500MB
        
        new(monitor, auto_cleanup, now())
    end
end

"""
記錄 Skip 計算性能
"""
function record_skip_calculation!(sys_monitor::SystemPerformanceMonitor, number::Int, duration_ms::Float64, cache_hit::Bool)
    record_metric!(sys_monitor.monitor, "skip_calculation_time", duration_ms, "ms", "skip")
    record_metric!(sys_monitor.monitor, "skip_cache_hit", cache_hit ? 1.0 : 0.0, "", "cache")
    
    auto_cleanup_if_needed!(sys_monitor)
end

"""
記錄過濾器計算性能
"""
function record_filter_calculation!(sys_monitor::SystemPerformanceMonitor, filter_type::String, duration_ms::Float64)
    record_metric!(sys_monitor.monitor, "$(filter_type)_filter_time", duration_ms, "ms", "filter")
    record_metric!(sys_monitor.monitor, "filter_calculation_time", duration_ms, "ms", "filter")
    
    auto_cleanup_if_needed!(sys_monitor)
end

"""
記錄快取性能
"""
function record_cache_performance!(sys_monitor::SystemPerformanceMonitor, cache_type::String, hit_rate::Float64, size_mb::Float64)
    record_metric!(sys_monitor.monitor, "$(cache_type)_hit_rate", hit_rate, "", "cache")
    record_metric!(sys_monitor.monitor, "$(cache_type)_size", size_mb, "MB", "cache")
    
    auto_cleanup_if_needed!(sys_monitor)
end

"""
記錄記憶體使用
"""
function record_memory_usage!(sys_monitor::SystemPerformanceMonitor, component::String, usage_mb::Float64)
    record_metric!(sys_monitor.monitor, "$(component)_memory", usage_mb, "MB", "memory")
    record_metric!(sys_monitor.monitor, "memory_usage_mb", usage_mb, "MB", "memory")
    
    auto_cleanup_if_needed!(sys_monitor)
end

"""
自動清理（如果需要）
"""
function auto_cleanup_if_needed!(sys_monitor::SystemPerformanceMonitor)
    if !sys_monitor.auto_cleanup
        return
    end
    
    current_time = now()
    if current_time - sys_monitor.last_cleanup >= Hour(1)  # 每小時清理一次
        cleaned = cleanup_old_metrics!(sys_monitor.monitor, 24)  # 保留24小時
        sys_monitor.last_cleanup = current_time
        
        if cleaned > 0
            @info "性能監控自動清理: 清理了 $cleaned 個舊指標"
        end
    end
end

"""
獲取系統性能摘要
"""
function get_system_performance_summary(sys_monitor::SystemPerformanceMonitor)::Dict{String, Any}
    report = get_performance_report(sys_monitor.monitor)
    
    # 計算關鍵性能指標
    skip_stats = get_metric_statistics(sys_monitor.monitor, "skip_calculation_time")
    filter_stats = get_metric_statistics(sys_monitor.monitor, "filter_calculation_time")
    
    # 計算快取命中率
    cache_hits = filter(m -> m.name == "skip_cache_hit", sys_monitor.monitor.metrics)
    cache_hit_rate = if !isempty(cache_hits)
        sum(m.value for m in cache_hits) / length(cache_hits)
    else
        0.0
    end
    
    summary = Dict(
        "performance_grade" => calculate_performance_grade(skip_stats, filter_stats, cache_hit_rate),
        "avg_skip_time_ms" => get(skip_stats, "mean", 0.0),
        "avg_filter_time_ms" => get(filter_stats, "mean", 0.0),
        "cache_hit_rate" => cache_hit_rate,
        "total_alerts" => length(sys_monitor.monitor.alerts),
        "monitoring_duration_hours" => Dates.value(now() - sys_monitor.monitor.start_time) / (1000 * 3600)
    )
    
    return merge(report, summary)
end

"""
計算性能等級
"""
function calculate_performance_grade(skip_stats::Dict, filter_stats::Dict, cache_hit_rate::Float64)::String
    skip_time = get(skip_stats, "mean", 100.0)
    filter_time = get(filter_stats, "mean", 200.0)
    
    score = 0
    
    # Skip 計算性能評分
    if skip_time < 10.0
        score += 30
    elseif skip_time < 25.0
        score += 20
    elseif skip_time < 50.0
        score += 10
    end
    
    # 過濾器計算性能評分
    if filter_time < 50.0
        score += 30
    elseif filter_time < 100.0
        score += 20
    elseif filter_time < 200.0
        score += 10
    end
    
    # 快取命中率評分
    if cache_hit_rate > 0.8
        score += 40
    elseif cache_hit_rate > 0.6
        score += 30
    elseif cache_hit_rate > 0.4
        score += 20
    elseif cache_hit_rate > 0.2
        score += 10
    end
    
    if score >= 90
        return "A+ (優秀)"
    elseif score >= 80
        return "A (良好)"
    elseif score >= 70
        return "B (一般)"
    elseif score >= 60
        return "C (需要改進)"
    else
        return "D (需要優化)"
    end
end

# 全局系統性能監控器
const GLOBAL_PERFORMANCE_MONITOR = SystemPerformanceMonitor()

"""
便利函數：記錄 Skip 計算
"""
function monitor_skip_calculation!(number::Int, duration_ms::Float64, cache_hit::Bool)
    record_skip_calculation!(GLOBAL_PERFORMANCE_MONITOR, number, duration_ms, cache_hit)
end

"""
便利函數：記錄過濾器計算
"""
function monitor_filter_calculation!(filter_type::String, duration_ms::Float64)
    record_filter_calculation!(GLOBAL_PERFORMANCE_MONITOR, filter_type, duration_ms)
end

"""
便利函數：獲取性能摘要
"""
function get_global_performance_summary()::Dict{String, Any}
    return get_system_performance_summary(GLOBAL_PERFORMANCE_MONITOR)
end

# 導出主要函數和結構
export PerformanceMonitor, SystemPerformanceMonitor, GLOBAL_PERFORMANCE_MONITOR
export record_metric!, start_timer!, end_timer!, set_threshold!
export get_metric_statistics, get_category_statistics, get_performance_report
export cleanup_old_metrics!, record_skip_calculation!, record_filter_calculation!
export record_cache_performance!, record_memory_usage!
export get_system_performance_summary, monitor_skip_calculation!, monitor_filter_calculation!
export get_global_performance_summary
