# Wonder Grid Lottery System - 命名衝突解決方案

## 🚨 問題描述

當您在 Julia REPL 中使用 `using WonderGridLotterySystem` 時，可能會看到以下警告：

```julia
WARNING: using WonderGridLotterySystem.LotteryDraw in module Main conflicts with an existing identifier.
WARNING: using WonderGridLotterySystem.WonderGridConfig in module Main conflicts with an existing identifier.
# ... 更多警告
```

## ✅ 解決方案

### 方案 1: 重新啟動 Julia (推薦)

**最簡單且最有效的解決方案**：

```julia
# 1. 退出當前 Julia 會話
julia> exit()

# 2. 重新啟動 Julia
$ julia

# 3. 直接使用模組
julia> using WonderGridLotterySystem

# 4. 正常使用所有功能
julia> test_data = [
    LotteryDraw([1, 8, 15, 22, 29], Date(2023, 1, 1), 1),
    LotteryDraw([3, 12, 18, 25, 31], Date(2023, 1, 2), 2)
]

julia> analyzer = SkipAnalyzer(test_data)
julia> skip = get_current_skip(analyzer, 1)
```

### 方案 2: 使用模組前綴

如果不想重新啟動 Julia：

```julia
# 使用模組別名
julia> import WonderGridLotterySystem as WGLS

# 使用前綴訪問所有功能
julia> test_data = [
    WGLS.LotteryDraw([1, 8, 15, 22, 29], Date(2023, 1, 1), 1),
    WGLS.LotteryDraw([3, 12, 18, 25, 31], Date(2023, 1, 2), 2)
]

julia> analyzer = WGLS.SkipAnalyzer(test_data)
julia> skip = WGLS.get_current_skip(analyzer, 1)
julia> ffg = WGLS.calculate_ffg_median(WGLS.FFGCalculator(0.5), 1, test_data)
```

### 方案 3: 選擇性導入

只導入需要的函數，避免類型衝突：

```julia
# 只導入函數，不導入類型
julia> using WonderGridLotterySystem: 
    get_current_skip, calculate_skips, calculate_ffg_median,
    calculate_all_pairings, generate_wonder_grid

# 使用模組前綴訪問類型
julia> import WonderGridLotterySystem as WGLS

# 混合使用
julia> test_data = [WGLS.LotteryDraw([1, 8, 15, 22, 29], Date(2023, 1, 1), 1)]
julia> analyzer = WGLS.SkipAnalyzer(test_data)
julia> skip = get_current_skip(analyzer, 1)  # 直接使用導入的函數
```

### 方案 4: 便利包裝函數

創建便利函數來簡化使用：

```julia
julia> import WonderGridLotterySystem as WGLS

# 創建便利函數
julia> function quick_skip_analysis(data, numbers)
    analyzer = WGLS.SkipAnalyzer(data)
    calculator = WGLS.FFGCalculator(0.5)
    
    results = []
    for number in numbers
        skip = WGLS.get_current_skip(analyzer, number)
        ffg = WGLS.calculate_ffg_median(calculator, number, data)
        push!(results, (number=number, skip=skip, ffg=round(ffg, digits=2)))
    end
    return results
end

# 使用便利函數
julia> test_data = [
    WGLS.LotteryDraw([1, 8, 15, 22, 29], Date(2023, 1, 1), 1),
    WGLS.LotteryDraw([3, 12, 18, 25, 31], Date(2023, 1, 2), 2)
]

julia> results = quick_skip_analysis(test_data, [1, 8, 15])
```

## 🔧 實用工作流程

### 推薦的開發流程

```julia
# 1. 在新的 Julia 會話中開始
$ julia

# 2. 載入模組
julia> using WonderGridLotterySystem

# 3. 創建測試數據
julia> test_draws = [
    LotteryDraw([1, 8, 15, 22, 29], Date(2023, 1, 1), 1),
    LotteryDraw([3, 12, 18, 25, 31], Date(2023, 1, 2), 2),
    LotteryDraw([5, 9, 16, 23, 35], Date(2023, 1, 3), 3),
    LotteryDraw([2, 11, 19, 27, 33], Date(2023, 1, 4), 4),
    LotteryDraw([7, 14, 21, 28, 36], Date(2023, 1, 5), 5)
]

# 4. 創建分析器
julia> skip_analyzer = SkipAnalyzer(test_draws)
julia> ffg_calculator = FFGCalculator(0.5)
julia> pairing_engine = PairingEngine(test_draws)

# 5. 進行分析
julia> # Skip 分析
julia> for number in [1, 8, 15, 22, 29]
    skip = get_current_skip(skip_analyzer, number)
    ffg = calculate_ffg_median(ffg_calculator, number, test_draws)
    println("號碼 $number: Skip=$skip, FFG=$(round(ffg, digits=2))")
end

julia> # 配對分析
julia> pairings = calculate_all_pairings(pairing_engine)
julia> println("總配對數: $(length(pairings))")
```

### 腳本文件使用

創建 `.jl` 腳本文件：

```julia
# my_analysis.jl
using WonderGridLotterySystem

# 您的分析代碼
test_data = [
    LotteryDraw([1, 8, 15, 22, 29], Date(2023, 1, 1), 1),
    # ... 更多數據
]

analyzer = SkipAnalyzer(test_data)
# ... 分析邏輯
```

然後在新的 Julia 會話中運行：

```bash
$ julia my_analysis.jl
```

## ⚠️ 重要說明

1. **警告不影響功能**：這些警告只是提醒有命名衝突，不會影響系統正常工作
2. **推薦重新啟動**：最簡單的解決方案是重新啟動 Julia 會話
3. **模組前綴安全**：使用 `import ... as ...` 是最安全的方法
4. **選擇性導入**：只導入需要的函數可以避免大部分衝突

## 🎯 最佳實踐

1. **新項目**：總是在新的 Julia 會話中開始
2. **腳本開發**：使用 `.jl` 文件而不是 REPL 進行複雜分析
3. **模組管理**：使用 `import` 而不是 `using` 來避免命名空間污染
4. **函數封裝**：創建便利函數來簡化常用操作

## 📚 相關資源

- [Julia 模組系統文檔](https://docs.julialang.org/en/v1/manual/modules/)
- [Wonder Grid 快速開始指南](doc/quick_start.md)
- [API 參考文檔](doc/api_reference.md)

選擇最適合您工作流程的解決方案，推薦使用**方案 1（重新啟動）**獲得最佳體驗！
