using WonderGridLotterySystem

# Test flexible validation
println("Testing Flexible Data Validation")
println("=" ^ 40)

validator = DataValidator()

# Test flexible validation (non-strict chronology)
println("Testing flexible validation (warnings only for chronology):")
result = validate_data5_file_flexible(validator, "data/fan5.csv", strict_chronology=false)
println("Flexible validation result: $(result.is_valid)")
println("Message: $(result.message)")

println("\n" * "=" ^ 40)

# Test a few specific validation functions
println("Testing specific validation functions:")

# Test minimum data requirements
fm = FileManager()
data = read_data5_file(fm, "data/fan5.csv")

println("\nData requirements validation:")
for analysis_type in ["ffg_calculation", "pairing_analysis", "skip_analysis", "backtesting", "wonder_grid_strategy"]
    result = validate_minimum_data_requirements(data, analysis_type)
    println("$analysis_type: $(result.is_valid) - $(result.message)")
end

# Test sorted numbers validation
println("\nSorted numbers validation:")
result = validate_sorted_numbers(data[1:10])  # Test first 10 draws
println("First 10 draws sorted: $(result.is_valid) - $(result.message)")

println("\nValidation system implementation complete!")