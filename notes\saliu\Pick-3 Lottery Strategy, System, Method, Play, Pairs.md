---
created: 2025-07-24T21:26:40 (UTC +08:00)
tags: [pick,pick-3,strategy,strategies,software,play,method,methods,combinations,filter,eliminate,filters,pairs,digits,system,data,drawings,draws,numbers,winning,win,games,drawing,draw,skip,skips,]
source: https://saliu.com/STR30.htm
author: 
---

# Pick-3 Lottery Strategy, System, Method, Play, Pairs

> ## Excerpt
> Pick-3 lottery strategy systems are created by the most powerful lotto software ever. This lottery system is based on digit pairs not repeating in the last 100 drawings.

---
_**The following strategy and reports are created by my lotto, lottery software known as LotWon, SuperPower, MDIEditor Lotto WE. My current software works with any type of lotto and lottery games: Pick-3, pick-4, lotto-5, lotto-6, lotto-7, Powerball, Mega Millions, horse racing (both trifecta and superfecta), Keno, and Euromillions. The powerful application does thorough statistical analyses, then it generates optimized combinations based on user's lottery strategies. A strategy is a filter setting; the filters are plotted by the statistics modules of MDIEditor and Lotto. Dozens of applications are available to download from my software site (click the banner).**_

This particular pick-3 lottery strategy is based one single filter: **Pairs** (abbreviated **Pr**). It was done in an older version of **LotWon** pick 3 lottery software. The 2011 version is very potent: **Bright3** integrated pick-3 lottery software.

![Pick-3 lottery strategy, systems is created by the most powerful lotto software ever Bright, Ultimate.](https://saliu.com/ScreenImgs/pick31.gif)

```
<span size="5" face="Courier New" color="#c5b358">                   Daily 3 Lottery Winning Pattern - Layer 7
                   File: D3 ~ Date: 07-29-1999

 For the 1st condition-group:
       - 1 means LOW (digits 0 to 4, including);
       - 2 means HIGH (digits 5 to 9, including).
 For the 2nd condition-group:
       - 1 means ODD (digits 1, 3, 5, 7, 9);
       - 2 means EVEN (digits 0, 2, 4, 6, 8).
 For the 3rd condition-group:
       - 1 means LOWER than the previous digit;
       - 2 means HIGHER than the previous digit.

 Line Draw    HiLo  HiLo  HiLo    OdEv  OdEv  OdEv    InDe  InDe  InDe
 No.  ings     1     2     3       1     2     3       1     2     3  

   1  716      2     1     2       1     1     2       2     1     1 
   2  189      1     2     2       1     2     1       1     2     2 
   3  252      1     2     1       2     1     2       1     1     1 
   4  467      1     2     2       2     2     1       2     1     0 
   5  287      1     2     2       2     2     1       2     2     2 
   6  110      1     1     1       1     1     2       1     1     1 
   7  337      1     1     2       1     1     1       1     2     2 
   8  806      2     1     2       2     2     2       2     1     2 
   9  291      1     2     1       2     1     1       2     2     1 
  10  179      1     2     2       1     1     1       1     0     2 
  11  975      2     2     2       1     1     1       2     1     2 

         *** Frequency of 3-Condition Groups in 100 Lottery Drawings ***

    HiLo1 HiLo2 HiLo3
      1     1     1                     times:    14 
Skips:  5  14  14  1  8  0  0  13  10  7  3  6  1  0 
Sorted Skips:  0  0  0  1  1  3  5  6  7  8  10  13  14  14 

      1     1     2                     times:    11 
Skips:  6  6  0  7  13  1  4  0  5  33  6 
Sorted Skips:  0  0  1  4  5  6  6  6  7  13  33 

<span>      1     2     1                     times:    16</span> 
Skips:  2  5  2  3  3  1  3  27  0  3  1  3  7  14  0  9 
Sorted Skips:  <span>0  0  1  1  2  2  3  3  3  3  3  5</span>  7  9  14  27 

<span>      1     2     2                     times:    18</span> 
Skips:  1  1  0  4  2  5  5  3  13  12  5  6  2  2  3  4  5  7 
Sorted Skips:  <span>0  1  1  2  2  2  3  3  4  4  5  5  5  5</span>  6  7  12  13 

      2     1     1                     times:    10 
Skips:  23  6  0  17  6  6  2  0  7  10 
Sorted Skips:  0  0  2  6  6  6  7  10  17  23 

      2     1     2                     times:    10 
Skips:  0  6  9  9  1  10  0  10  12  13 
Sorted Skips:  0  0  1  6  9  9  10  10  12  13 

<span>      2     2     1                     times:    15</span> 
Skips:  26  6  0  10  5  5  1  9  0  5  3  1  7  2  2 
Sorted Skips:  <span>0  0  1  1  2  2  3  5  5  5</span>  6  7  9  10  26 

      2     2     2                     times:    6 
Skips:  10  5  15  6  37  21 
Sorted Skips:  5  6  10  15  21  37 
<p>
         *** Frequency of 3-Condition Groups in 100 Lottery Drawings ***

    OdEv1 OdEv2 OdEv3
      1     1     1                     times:    9 
Skips:  6  2  0  6  3  0  6  20  22 
Sorted Skips:  0  0  2  3  6  6  6  20  22 

<span>      1     1     2                     times:    19</span> 
Skips:  0  4  10  2  0  4  0  1  2  0  9  11  2  11  10  0  0  2  1 
Sorted Skips:  <span>0  0  0  0  0  0  1  1  2  2  2  2  4  4</span>  9  10  10  11  11 

      1     2     1                     times:    9 
Skips:  1  38  11  8  5  3  2  17  3 
Sorted Skips:  1  2  3  3  5  8  11  17  38 

      1     2     2                     times:    8 
Skips:  30  10  4  9  8  9  10  6 
Sorted Skips:  4  6  8  9  9  10  10  30 

      2     1     1                     times:    13 
Skips:  8  4  0  3  16  3  4  3  2  1  10  14  19 
Sorted Skips:  0  1  2  3  3  3  4  4  8  10  14  16  19 

<span>      2     1     2                     times:    15</span> 
Skips:  2  12  7  10  10  17  2  1  3  3  0  12  0  2  2 
Sorted Skips:  <span>0  0  1  2  2  2  2  3  3</span>  7  10  10  12  12  17 

      2     2     1                     times:    11 
Skips:  3  0  7  11  11  22  0  9  12  5  5 
Sorted Skips:  0  0  3  5  5  7  9  11  11  12  22 

<span>      2     2     2                     times:    16</span> 
Skips:  7  3  15  5  3  0  4  3  1  5  2  3  15  5  3  9 
Sorted Skips:  <span>0  1  2  3  3  3  3  3  4  5  5  5</span>  7  9  15  15 


<span>
Hint: play 3-groups with a high frequency. The median is 5, 
so play the lottery strategy for 0 to 5 drawings. If the strategy does not hit 
within 5 drawings, leave it alone. Wait until it hits again, then play it 
again from 0 to 5 lottery drawings. In most cases, there are consecutive hits 
for the same strategy. Also, if the previous skip was over 5, 
a skip under 5 will follow.</span>    


         *** Frequency of 6-Condition Groups in 100 Lottery Drawings ***

    HiLo1 HiLo2 HiLo3 OdEv1 OdEv2 OdEv3
      1     1     1     1     1     1   times:    1 
Skips:  73 
Sorted Skips:  73 

      1     1     1     1     1     2   times:    4 
Skips:  5  14  60  3 
Sorted Skips:  3  5  14  60 

      1     1     1     1     2     1   times:    1 
Skips:  92 
Sorted Skips:  92 

      1     1     1     1     2     2   times:    1 
Skips:  46 
Sorted Skips:  46 

      1     1     1     2     1     1   times:    2 
Skips:  35  12 
Sorted Skips:  12  35 

      1     1     1     2     1     2   times:    1 
Skips:  94 
Sorted Skips:  94 

      1     1     1     2     2     1   times:    1 
Skips:  95 
Sorted Skips:  95 

      1     1     1     2     2     2   times:    3 
Skips:  37  9  14 
Sorted Skips:  9  14  37 

...

      1     2     2     2     1     2   times:    2 
Skips:  68  28 
Sorted Skips:  28  68 

      1     2     2     2     2     1   times:    6 
Skips:  3  0  7  11  58  5 
Sorted Skips:  0  3  5  7  11  58 

      1     2     2     2     2     2   times:    2 
Skips:  55  22 
Sorted Skips:  22  55 

      2     1     1     1     1     1   times:    0 
Skips: 
Sorted Skips: 

      2     1     1     1     1     2   times:    1 
Skips:  31 
Sorted Skips:  31 

      2     1     1     1     2     1   times:    1 
Skips:  67 
Sorted Skips:  67 

      2     1     1     1     2     2   times:    4 
Skips:  30  25  18  10 
Sorted Skips:  10  18  25  30 
...

      2     2     1     2     2     2   times:    1 
Skips:  33 Sorted Skips:  33 

      2     2     2     1     1     1   times:    1 
Skips:  10 Sorted Skips:  10 

      2     2     2     1     1     2   times:    2 
Skips:  16  15 
Sorted Skips:  15  16 

      2     2     2     1     2     1   times:    0 
Skips: Sorted Skips: 

      2     2     2     1     2     2   times:    0 
Skips: Sorted Skips: 

      2     2     2     2     1     1   times:    2 
Skips:  39  59 
Sorted Skips:  39  59 

      2     2     2     2     1     2   times:    1 
Skips:  77 Sorted Skips:  77 

      2     2     2     2     2     1   times:    0 
Skips: Sorted Skips: 

      2     2     2     2     2     2   times:    0 
Skips: Sorted Skips: 


Following are the fragments of the reports and the winning lottery strategy 
presented in a later post in rec.lottery newsgroup.

                   * 3-DIGIT Lottery Winning Pattern - Layer 1 *
                   * File: D3; Date: 07-29-1999

 Line Draw  Strgt  Addt  All Pot  Vr   TV   Pr   Syn  Bun  Any   Val  Sum
 no.  ings    1     1     1   1    1    1    1    1    1    1     1    1 

 Median:   692    692   3   4    2    8    34   115  2    141   9    13 
 Average:  1068   970   5   3    3    9    45   174  3    209   17   20 
 StnDev:   931    826   7   4    4    9    41   174  4    260   11   27 

   1  716   1397+   283-  1-  0-   4+   6+  131+  610+  2-  137-  40+   6-
   2  189    233-  2741+  5+  3    2+   3-   10-  232+  3-  191+   8-  16-
   3  252    701+    89-  0-  3-   1+  13-   54+   12-  7+   27+  14-  18+
   4  467     20-  1203-  5+  6-   0-  20+   20-   19-  3+   20-  21+   0-
   5  287    803+  2463+  2-  9+   1-   6-   55-   55-  2    85+   7-   4-
   6  110    160-   345+  4-  0-   3-   7-  160+  159+  2-   42-  26+  42+
   7  337   1592+    36-  7+  2-   7-  10-   15-   97+  7-  193+  19-  10-
   8  806     19-  1364-  0-  6+   8+  19+   19-   18- 19+   19+  20-  17+
   9  291    133-  2187+  1-  1-   4+  10+  133+  132+  0    10-  40+   5-
  10  179   1611+   447- 13+  2+   0-   2-    8-    8-  0-  551+  10+  14+
  11  975    923+  2178+  7+  1+   2+   4+   58+  171+  4+   97+   6-   1-


              * Pick-3 Lottery Strategy Checking - Layer 1 
              File: W3.1; Date: 07-29-1999

         *** Frequency of the Strategy in 400 Drawings ***

 Tot1:     0 - 5000    Rev1:     0 - 5000
 All1:     0 - 5000    Pot1:     0 - 5000
 Vr_1:     0 - 5000    TV_1:     0 - 5000
 <span>Pr_1:   100 - 5000</span>    Syn1:     0 - 5000
 Bun1:     0 - 5000    Any1:     0 - 5000
 Val1:     0 - 5000    Sum1:     0 - 5000

 Times:  53 (number of wins in 400 lottery drawings)

 Skips:  4  2  24  6  1  7  1  4  6  31  21  3  1  2  7  9  0  14  9  15  4
 0  6  11  5  0  8  9  2  10  12  5  2  0  1  5  4  14  0  4  1  0  5  4  1
 6  2  5  5  5  26  4 

Sorted Skips:  <span>0  0  0  0  0  0  0  1  1  1  1  1  1  2  2  2  2  2  3  4  4
4  4  4  4  4  5  5  5  5  5  5  5</span>  6  6  6  6  7  7  8  9  9  9  10  11  12
14  14  15  21  24  26  31 
</p></span>
```

[

## <u>Resources in Lottery Software, Strategies, Systems, Lotto Wheeling</u>

](https://saliu.com/content/lottery.html)See a comprehensive directory of the pages and materials on the subject of lottery, software, systems, lotto wheels.

-   The Main [_**Lotto, Lottery, Strategy Software**_](https://saliu.com/LottoWin.htm), Page.  
    Presenting software to create free winning lotto, lottery strategies, systems based on mathematics. Get your lotto systems or wheels, the best lottery, lotto software, combinations, winning numbers.
-   [_**Lotto, Lottery Software, Excel Spreadsheets: Programming, Strategies**_](https://saliu.com/Newsgroups.htm).  
    Read a genuine analysis of Excel spreadsheets applied to lottery and lotto developing of software, systems, and strategies. Combining Excel analysis with powerful lottery and lotto software programmed by this author, _Parpaluck_.
-   [_**<u>MDIEditor Lotto WE</u>: Lottery Software Manual, Book, ebook, Help**_](https://saliu.com/MDI-lotto-guide.html).  
    ~ Also applicable to LotWon lottery, lotto software; plus Powerball, Mega Millions, Euromillions.
-   [_**Documentation, Help: MDIEditor Lotto WE, Lottery Software, Strategy Tutorial**_](https://saliu.com/mdi_lotto.html).
-   [_**MDI Editor Lotto**_ _Is the Best Lotto Lottery Software; You Be Judge_](https://saliu.com/bbs/messages/623.html).
-   [_**Filters, Restrictions, Elimination, Reduction in Lotto, Lottery Software**_](https://saliu.com/bbs/messages/42.html).
-   [_**Step-By-Step Guide to Lotto, Lottery Filters in Software**_](https://saliu.com/bbs/messages/569.html).
-   [_**Basic Manual for Lotto Software, Lottery Software**_](https://saliu.com/bbs/messages/818.html).
-   [**Vertical or Positional Filters** _**in Lottery Software**_](https://saliu.com/bbs/messages/838.html).
-   [_**Beginner's Basic Steps to**_ **LotWon** _**Lottery Software, Lotto Software**_](https://saliu.com/bbs/messages/896.html).
-   [**Dynamic, Static** _**Filters: Lottery Software, Lotto Analysis, Mathematics**_](https://saliu.com/bbs/messages/919.html).
-   [_**Winning Lottery Systems for**_ **Skips**](https://saliu.com/skip-strategy.html): Lotto, Powerball, Mega Millions, Euromillions, Horse Racing, Roulette, Football.
-   [_**Lottery Utility Software**_](https://saliu.com/lottery-utility.html): _Pick-3, 4 Lottery, Lotto-5, 6, Powerball, Mega Millions, Euromillions_.
-   [_**Filtering, Filters in Lottery Software, Lotto Software**_](https://saliu.com/filters.html).
-   [_**Lottery Strategy, Systems Based on Number Frequency**_](https://saliu.com/frequency-lottery.html)
-   [_**Lotto Decades: Software, Reports, Analysis, Strategies**_](https://saliu.com/decades.html)
-   [_**Analysis of Best Ranges for Lotto Number Frequency, Lottery Pairs, Pairings**_](https://saliu.com/lottery-lotto-pairs.html).
-   [_**Lotto Software for Groups of Numbers: Odd, Even, Low, High, Sums, Frequencies, User's Groups**_](https://saliu.com/lotto-groups.html).
-   _"The Start Is the Hardest Part"_: [_**Play Lottery Strategy, Lotto Strategies**_](https://forums.saliu.com/lottery-strategies-start.html).
-   Download [**Lottery Software, Programs, Applications**](https://saliu.com/infodown.html).

![Pick-3 Lottery Method Strategy System Software.](https://saliu.com/HLINE.gif)

Comments:  

![Pick-3 Lottery software, strategies, system.](https://saliu.com/HLINE.gif)

**[Home](https://saliu.com/index.htm) | [Search](https://saliu.com/Search.htm) | [New Writings](https://saliu.com/bbs/index.html) | [Fundamental Formula](https://saliu.com/formula.htm) | [Odds, Generator](https://saliu.com/calculator_generator.html) | [Contents](https://saliu.com/content/index.html) | [Forums](https://forums.saliu.com/) | [Sitemap](https://saliu.com/sitemap/index.html)**

![Pick-3 Lottery Method, Strategy, Systems.](https://saliu.com/HLINE.gif)
