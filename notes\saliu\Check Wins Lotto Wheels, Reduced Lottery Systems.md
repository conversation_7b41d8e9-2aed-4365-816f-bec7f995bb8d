---
created: 2025-07-24T22:10:02 (UTC +08:00)
tags: [check,winner,winners,hits,wheel,wheels,wheeling,abbreviated,reduced,lotto,systems,free,software,lotto,lottery,elimination,reduce,reduction,jackpot,purge]
source: https://saliu.com/bbs/messages/90.html
author: 
---

# Check Wins Lotto Wheels, Reduced Lottery Systems

> ## Excerpt
> Run special lotto wheeling software to check your lotto wheels for winners, winning numbers in a lottery drawing, missing lotto combinations from system.

---
Written on June 19, 2000.

• First, you must delete the header using any text editor. Delete all the text lines, from "Copyright 2000" ” to "416 Combinations" and the subsequent empty line. Make sure that the first combination in the lotto wheel: 1 10 15 22 29 48 becomes the first line in the file. The file must have 416 lines (the number of lotto combinations in the system). Save the file (perhaps under the same name).

Next, use UTIL-6, part of SuperPower lottery software (in LOTWON99 V10.99.02). Choose the option “Check for Winners” (press w).

\- Output file to check: WHEEL49.36  
\- Real data file: (your lotto6 file with real drawings, DATA6, or the filename you use)  
\- Drawings to check in the output file: 416  
\- Real drawings to check: depending on how many your DATA6 file has (100, 200, even 300)  
\- Save the report to disk (for example, CHECK6.49)  
\- Open the report (CHECK6.49) in a text editor to view it.

I used a data file sent by a user (he signed as Guy on my message board). In 100 real lotto drawings, the 49-number lotto wheel had 4 winning lotto numbers 40 times. In one drawing it offered two combinations with 4 winning numbers. Most importantly, it offered 5 winning numbers 3 times: drawings 1, 40, and 66.  
The report can be viewed at the download site (CHECK6.49).

Best of luck, guys and ladies!

Ion Saliu

PS  
••• Outdated material. For latest, read:  

-   [The myth of lotto wheels or abbreviated lotto systems,](https://saliu.com/bbs/messages/11.html) [The Fundamental Formula of Lotto Wheeling,](https://saliu.com/wheel.html) and [WHEEL632 and SUPER632 lotto-wheeling software available as freeware](https://saliu.com/bbs/messages/wheel.html).
    
    The wheels only make money for those who sell lottery software. The static lotto wheels and wheeling are the essence of all lottery software, except for LotWon. Some pushed the envelope so hard, that everything in lottery is now a...wheel! It is the only justification left for most lottery software packages. They say now _full wheels_. That's an oxymoron, a contradiction in terms. The _lotto wheels_ are, in fact, _reduced lotto systems_. Wheeling lottery numbers is a way of reducing the number of combinations to play, instead of playing all lotto numbers in a set. They can't have it both ways: _full_ and _reduced_!
    
    ![Check lotto wheels, reduced systems for wins.](https://saliu.com/bbs/messages/HLINE.gif)
    
    [
    
    ## Resources in Lottery, Software, Systems, Lotto Wheels, Strategies
    
    ](https://saliu.com/content/lottery.html)Lists the main pages on the subject of lottery, lotto, software, wheels and systems.
    
    -   [_**Lottery Software Tools, Lotto Wheeling Software Wheels**_](https://saliu.com/free-lotto-tools.html) from the download site:
    -   Wheel-632, Wheel-532, the best on-the-fly wheeling software; applies real lottery filtering.  
        ~ Superseded by the most powerful integrated packages
    -   Pick532, Pick532 and, especially, the Bright software packages.  
        
    -   Combinations, the universal combinations generator for any lotto, Keno, Powerball, Mega Millions, Euromillions;  
        
    -   LexicoWheels, generator of lotto wheels based on lexicographical indexing; applies to any lotto, Keno, Powerball, Mega Millions, Euromillions or two-in-one lotto games.  
        
    -   WheelCheck5, WheelCheck6, software to verify lotto wheels for missing combinations and generate lotto-5 and lotto6 systems.  
        
    -   LottoWheeler, free wheeling software: fill out lotto wheels with player lotto numbers. Supersedes the old-time favorite FillWheel (still offered). The two pieces of software replace the theoretical lotto numbers in the SYS/WHEEL files by your picks (the lotto numbers you want to play).
    -   Shuffle, SuperFormula to shuffle (randomize, randomly arrange) your lotto picks. Repeat the randomizing procedure several times. The randomized wheels can be used directly, without shuffling your lottery picks first.
    -   The Main [_**Lotto, Lottery, Software, Strategy, Systems**_](https://saliu.com/LottoWin.htm), Wheeling Page.  
        Presenting software to create free winning lotto, lottery strategies, systems based on mathematics. Get your lotto systems or wheels, the best lottery, lotto software, combinations, winning numbers.
    -   [**Lotto wheels**](https://saliu.com/lotto_wheels.html) _**for lottery games drawing 5 6 7 numbers**_.  
        The most advanced theory of lotto wheels or reduced lottery systems. Get also original lotto wheels for lotto games drawing 5, 6, or 7 numbers: Balanced, randomized, and free.
    -   [The myth of lotto wheels or abbreviated lotto systems](https://saliu.com/bbs/messages/11.html).  
        Statistical analysis of real lottery data proves the negative effect of playing static (unbalanced or split) lotto wheels.
    -   [WHEEL-632 available as free lotto wheeling software](https://saliu.com/bbs/messages/wheel.html) — the best on-the-fly wheeling software; applies real lottery filtering.
    -   Free [lottery software for players of lotto wheels](https://saliu.com/bbs/messages/857.html): Fill out lotto wheels with player's picks (numbers to play).
    -   [Software to verify lotto wheels](https://saliu.com/check-wheels.html) for missing combinations and generate lotto 5, 6 abbreviated (reduced) systems.
    -   [Wheels, balanced lotto wheels, lexicographic order](https://saliu.com/bbs/messages/772.html), index, number set.
    -   [Genuine Powerball wheels](https://saliu.com/powerball_wheels.html).
    -   [Genuine Mega Millions wheels](https://saliu.com/megamillions_wheels.html).
    -   [Genuine Euromillions wheels](https://saliu.com/euro_millions_wheels.html).
    
    ![Run special lotto wheeling software to check your lotto wheels for winners.](https://saliu.com/bbs/messages/HLINE.gif)
    
    Comments:  
    
    ![The lotto wheeling software to check all lotto wheels.](https://saliu.com/bbs/messages/HLINE.gif)
    
    **[Home](https://saliu.com/index.htm) | [Search](https://saliu.com/Search.htm) | [New Writings](https://saliu.com/bbs/index.html) | [Software](https://saliu.com/infodown.html) | [Odds, Generator](https://saliu.com/calculator_generator.html) | [Contents](https://saliu.com/content/index.html) | [Forums](https://forums.saliu.com/) | [Sitemap](https://saliu.com/sitemap/index.html)**
    
    ![Run special lotto wheeling software to check your lotto wheels for winners, winning numbers in a lottery drawing, missing lotto combinations from wheeling system.](https://saliu.com/bbs/messages/HLINE.gif)
