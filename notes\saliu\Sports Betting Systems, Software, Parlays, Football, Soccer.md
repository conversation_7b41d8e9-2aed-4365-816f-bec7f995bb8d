---
created: 2025-07-24T21:26:41 (UTC +08:00)
tags: [sports,betting,bet,spreadsheet,football,soccer,pools,NFL,calcio,toto,Excel,software,system,prognostication,prognosticating,1X2,statistics,]
source: https://saliu.com/betting.html
author: 
---

# Sports Betting Systems, Software, Parlays, Football, Soccer

> ## Excerpt
> The best sports bettor, prognosticator created software, systems, Excel spreadsheets for sport betting in soccer, calcio, football, NFL, pools, 1x2, parlays.

---
![Probability, gambling software, Excel spreadsheets in sports betting, soccer, calcio, football, NFL.](https://saliu.com/HLINE.gif)

### I. [_Statistical Method_: American Football Betting and Italian Soccer Pools (1, X, 2)](https://saliu.com/betting.html#statistics)  
II. [_Randomness_ Approach: Resemblance to Lottery Games](https://saliu.com/betting.html#random)  
III. [Software to Handle Sports Betting, Especially American Football and Soccer Pools](https://saliu.com/betting.html#software)  
IV. [Sports Betting Links, Resources](https://saliu.com/betting.html#links)

![The best sport bettor <PERSON> created software, systems, Excel spreadsheets for sport betting.](https://saliu.com/HLINE.gif)

## <u>1. The <i>Statistical Method</i>: American Football Betting and Italian Soccer Pools (1, X, 2)</u>

We know from my probability book that the past does count in all random events (in everything, that is). For one, the past is represented by the number of trials — one of the three essential elements of the Fundamental Formula of Gambling FFG. This is how I started my _gambling theory_. Statistical analyses can discover _tendencies_. A tendency is, simply put, a repetition of something across a number of trials.

Back in Romania, I loved to play soccer pools: _1, X, 2_. _1_ represents a win for the home team; _X_ is a tie (very rare in the American football; very common in the Italian calcio leagues); _2_ means a win for the visiting team. I needed to devise a strategy to give me the best results based on statistics. The system was best suited for the Italian soccer championships (_Serie A_ and _Serie B_). The Italian soccer games have a high level of unpredictability. In most other national championships, the results are more easily predictable (the home teams win in most cases).

I did not write standalone software for this type of gambling mathematics. Instead, I created two Excel 95 spreadsheets. They are still available from my website, although data is outdated now. The user can update all figures, however. Long live the Internet!

The European football (soccer) Excel spreadsheet is named _Toto1x2.xls_. It is based on real data from the 2001 Italian football championship _Serie A_.

The American professional football Excel spreadsheet is named _NFL2001.xls_. It is based on real data from the 2001 NFL regular season.

[![Sports Betting Software, Bet Spreadsheets: Buy Online, Best Prices.](https://saliu.com/ScreenImgs/bet-spreadsheet.gif)](https://saliu.com/free-sports-betting.html)

I will present now an abbreviated form of my system applied to the American football (specifically the NFL). As I do not offer a computer program at this time, you can apply this strategy manually (pencil and paper) or edit the spreadsheet.

You will keep records for each team, separated in two files (spreadsheets): Record _at home_ and record _on the road_. Using the overall records has much lower relevance in the American professional football. The betting is based on a parameter named point spread. A team is expected to win alright. But it must win by a specific margin of victory (the point spread or simply the spread).

You will work with these two very important elements: _Home Team At\_Home_ and _Visiting Team On\_The\_Road_.

In this abbreviated version, you will use only the results (win/loss) and the points scored/allowed. Add the wins for the home team at-home with the losses for the visiting team on-the-road. Do the percentage. Normally, a percentage over 75% is a good indicator for a home team win. The average scores can offer additional useful info. In the case bellow, the game was very close to call: Just a slight advantage for the Home-Team. But the Visiting-Team had a realistically good chance to win (based on _points scored_ and _points allowed_.

Using my strategy for the Italian soccer leagues, I predicted 9 even 10 (out of 13) games (70% or 77%). That is, I predicted with only one prognostication sign: 1, or X, or 2. Then I would use two signs for close games (1X, or 12, or X2), even 1X2. The soccer prediction is based on the _win/loss_ parameters. The scores are disregarded. Thus, the soccer betting appears to be simplified, compared to American football betting.

-   There are detailed steps on how to add data (on paper or in a spreadsheet) and how to do the calculations (or let the _macros_ do the calculations: [**Sports Betting, Gambling Theory of Sport, Bet Systems, Software, Spreadsheets**](https://saliu.com/sports_betting.html).

I lived as a refugee in Yugoslavia for eight months, between 1984 -1985. That's when I had the most success playing soccer pools. I helped also the Yugoslav office of the _United Nations High Commissioner for Refugees (UNHCR)_. I translated statements and documents from Romanian into English. I enjoyed doing the job. I helped others and I helped myself by practicing English and getting to know more about the _Homo Computing\_beast_ experience. It is the name I find to be most truthful for this extraordinary species.

UNHCR promised to pay me. I would hear _“Sutra!… Tomorrow (in Serbian)… Next week…”_ The main problem I had then: My shoes were real bad. Granted, they helped me to cross the border. But I had reached a point I needed a pair of better shoes. I turned to an old friend: Gambling. Specifically, the soccer pools. I won a couple of times playing the Yugoslav soccer championship. As in most countries, there were not many surprises to lead to big prizes. Then, a medieval, beautiful winter set in. Every country in Europe would use only the Italian soccer games for their pools. I hit the second prize. Actually, I predicted all the games, but I applied a _minus-1 system_ (a wheel). Anyway, the prize paid good money, especially by the standards of a shoe-challenged refugee.

I didn't have time to do anything, after cashing in the prize. Just about everybody in my “politically-correct” entourage wanted a big party. The refugees live under tremendous stress, in case you didn't know that. So, I said, yeah, let's do it, we need it! I thought everybody got drunk that night, but not everybody did. Because somebody stole all my money over night! Nothing was left in my pockets after the big party! Truth is, I arrived in America wearing bad shoes, perhaps to prove to myself the American dream. _"Give me your poor, your shoeless, etc."_

• • Let's change gears and take a look at a real-life game in the NFL and the stats for the two teams.

Predicted Result Based on Wins/Losses:

Win for Home Team: 3 (wins at home) + 2 (Visitor losses) = 5 of 8 (62%)

Win for Visiting Team: 1 (Home losses) + 2 (Road wins) = 3 of 8 (38%)

Home Team  
Average points scored: 18  
Average points allowed: 16

Visiting Team Average points scored: 20  
Average points allowed: 23

Predicted Score: 20 - 18 in favor of Home-Team.

The prediction for this NFL game was derived from 4 games only. The success ratio increases with the increase in the number of games. The results get better in the second half of the regular season (after _Week 8_). Using data from previous seasons is very often misleading. The teams in the American sports change constantly as a result of the _free agency_. Players move from one team to another. Some players have a real impact on the game.

The success (correctness, performance) rate of _**SPORTS.XLS**_, the Excel spreadsheet for sports betting, is around _2 out of 3_. Actually, it is higher at picking _straight-up winners_ or _pick-'em_. It can still hit at least 60% when applied to _point-spread_ betting, as in the American football.

A 60% success rate translates equivalently that a player can pick one game at a time and be successful in 60% of the cases.

If picking 2 games, the probability goes down to: .6 x .6 = .36 or 36%.

Picking three games: .6 ^ 3 = 21.6%.

A 5-team parlay can expect a success rate of .6 ^ 5 = 7.7%.

The most favorable case is for straight-up betting: One game only. The _house advantage (edge)_ is also the most favorable: 10%. That is, a 1-unit bet returns a .9-unit payout. Usually, American football bettors pay 110 dollars for a 100 dollar win.

A five-team parlay offers, at best, a 25% house advantage. Ten or more game-parlays go to over 90% in house edge! It is outrageous!

If betting straight-up 10 times (or weeks, as in NFL pro football), the player should expect 6 wins and 4 losses. The four losses result in 4 units. The 6 wins amount to +(6 x .9) = +5.4 units. The net win is 1.4 units. If betting $100 at a time, the net profit should be $140 over a period of 10 weeks. Just for fun, unless thousands are bet per game.

A probability of over 50% yields also longer winning streaks and shorter losing streaks. The mathematical player can draw a big advantage from that probability feature. The non-mathematical bettor should keep in mind that an over-50% probability tends to repeat in the very next trial; a bet increase is favorable. Conversely, a 60% probability event will not lose very often more than three consecutive trials; again, a bet increase after three consecutive losses is good money management.

My spreadsheet strategy applied to American football prognostication was pirated even at a Microsoft site! One visitor to my sports forum advised me of the incident.

Here is an excerpt from the Microsoft site (the URL is //office.microsoft.com/en-us/excel/HA011246011033.aspx?pid=CL100570):

-   _“Starting in row 36, columns C and D contain the team code number (listed in **B2:B33**) for the home and away team for each game. For example, the first game (listed in row 36) is the San Francisco 49ers (team 28) playing at the New York Giants (team 21). Column **E** contains the home team's score, and column **F** contains the visiting team's score. As you can see, the 49ers beat the Giants 16-13. I can now compute the outcome of each game (the number of points by which the home team beats the visiting team) by entering the formula **\=E36-F36** in cell **G36**.”_

That page was published several years <u>after</u> I released my sports betting theory and spreadsheets (year of grace **2000**).

![Randomness in sports betting resembles the lottery play, is the idea of the best-ever sports bettor.](https://saliu.com/HLINE.gif)

## <u>2. The Randomness Approach: Resemblance to Lottery Games</u>

How about _random_ play, like quick-picks in lottery? It could help the biased sports betting player. Many sport bettors have a bias towards favorite teams, including football. Reality is that only a tiny minority of teams are dominant. Therefore, most biased players are losers. They would be better off choosing the winners randomly. I offer free software to pick sports results in a random manner, like the lottery. This section lists the most important free programs that generate random sports picks—as _numbers_ and also _team names_.

A program I wrote in August 2003 has become a big success: _**PermuteCombine**_. The combinatorial software generates permutations, arrangements (some say permutations of N, M; like exactas and trifectas at horse racing), combinations (like in lotto), and exponents (like in the pick-3 lottery and soccer pools 1, X, 2).

The program generates numbers or _words_ (such as names of sports teams). There is a sample text file — _TEAMS.NFL_ — with all 32 NFL football teams. The option applicable to the American football is _Arrangements; Words; Random_. The option applicable to soccer pools (1, X, 2) is _Exponents, Words, Random_ (use the sample file _1X2.TXT_).

It's best to run a random option several times in a row. Common sense tells you that hitting in a first try is a very rare event in gambling. The high-eyebrows translation (probability theory) is: _**The degree of certainty DC rises with the increase in the number of trials N, while the probability p is always constant**_. Repeating the combination generating process affords to you compression of time and costs.

Here is an interesting fact I discovered. Please relate it to the role of _standard deviation_ in random events (the watchdog of randomness, chapter IV). I applied this NFL strategy live at my website. That is, I published the predictions days before the games were played. I recorded the results after the games ended. The new page was published with the new data. I had misses and I had _wins_. The project covered almost three regular seasons in the National Football League (NFL). The results were quite impressive: 27 wins out of 41 weeks. Success ratio: 65.9% (2 out of 3 times). See the page at SALIU.COM: _random-picks.html_.

I applied the strategy to five games. There are two outcomes in rapport to the betting line. Either choose the _favorite_ to cover the spread (win by the point spread); or bet against it (take the _underdog_ to defend the spread). Total amount of possible combinations is therefore 2^5 = 32.

The _**Fundamental Formula of Gambling**_ demonstrates that, in a majority of cases, the winning combinations will come from the _FFG median zone_. The _FFG median_ zone and the _normal (Gauss) bell_ share a large percentage of elements, but the areas are not identical. Percentage of 25 combinations within the FFG median bell: 38% (or 12 lines of 5 teams each, from index #11 to index #22).

My site also publishes the [_**schedule of NFL National Football League**_](https://saliu.com/NFL-Schedule.html) (I have pages starting with the 2004 season results). When this project was running, I copied the schedule from the official site of the NFL. Let's say that a particular week had 16 games scheduled. I ran _**PermuteCombine**_, the _Combinations, Numbers, Random_ option for 16 numbers, 5 numbers per combination. Total combinations to generate: 22 (the median calculated by the _**Fundamental Formula of Gambling**_ for a probability equal to 1/32). The combination #22 for that particular run was: _4, 5, 7, 8, 11_. In the case described at my site, you can see what games those 5 numbers represented in that week's schedule. The schedule starts with games #1 (of course!) and ends with game #16. For example, game #4 that week was Texans at Jaguars.

The winning combination for that particular week was:  
_Jaguars, Colts, Chargers, Vikings, 49ers,_  
or lexicographical index 20 (a winner — inside the _FFG median_ area 11-22).

The accounting or the auditing now:  
Total games to play every week: 12;  
Total weeks played: 41  
Total games played: 41 \* 12 = 492;  
Total winning weeks: 27;  
Payout for a winning 5-team parlay: 25;  
Total winnings: 27 \* 25 = 675;  
Net profit: 675 – 492 = 183.

Not long after my project, most bookies lowered the 5-team parlay payout to 20-to-1. The total winning would go down to _27 \* 20 = 540_. Still, a profit (48 betting units). Since the winning percentage is well above 50%, increasing the bet after a loss offers a great benefit overall. I would even start _martingaling_ after 3 losses in a row! There was one occurrence of three consecutive losses during the 2002 season. The Martingale betting could have started after just two consecutive losses and the profit margin would have been phenomenal!

![The best sports betting system in final tournaments like FIFA World Cup in soccer, European football.](https://saliu.com/HLINE.gif)

After the 2018 Russia World Cup in soccer, I published an unusual way to bet –

-   **<u>In the group stage, bet on the UNDERDOG in all matches.</u>**

In total: 48 matches. Bet 100 on each match. Total cost: 4800. Calculate payouts for 100-bets.

In 2018, matches like Germany – Mexico and Germany South Korea offered great payouts. There were more surprises along the way.

The Qatar 2022 FIFA World Cup strongly confirmed the validity of this betting approach. View a detailed thread in Google Groups:

-   [**_"The 2022 World Cup Best Team-Odds"_**](https://groups.google.com/g/rec.sport.soccer/c/f2B8MDSqA00/m/VJYlFw47AAAJ)

![Run specific software to handle sport bet, especially American football and soccer pools.](https://saliu.com/HLINE.gif)

## <u>3. Software to Handle Sports Betting, Especially American Football and Soccer Pools</u>

The sport betting area is the least covered by my software. Nonetheless, I still provide the largest collection of sports prognosticating software in the world. I list here the most significant programs that help you apply to sport betting events. I put together all my sports betting programs in the most powerful software collection: BrightS.

![Download software for sports betting, bet, systems, parlay, football, soccer by the greatest bettor.](https://saliu.com/ScreenImgs/football-sports.gif)

• _**Sports.xls**_, sports betting software; real data from the 2001 NFL season (Excel 95 spreadsheet).

• _**NFL2001.xls**_, sports betting software; real data from the 2001 NFL season (Excel 95 spreadsheet).

• _**Toto1x2.xls**_, sports betting software; real data from the 2001 Italian football championship Serie A (Excel 95 spreadsheet).

• _**SkipSystem**_: Gambling and lottery software to automatically create lotto, lottery, gambling, sport bet systems derived from skips and based on the _FFG median_.

• _**FrequencyRank**_: Software to generate frequency reports, including wins of the NFL teams (against the point spread).

• _**BetUS32**_, intelligent random combination generator for sports betting, the American way.

• _**AmBet**_, much like BetUS32—but with a twist! You can add W or L at the end of a team name based on the results of the previous week.

• _**BellCurveGenerator**_ generates combinations within the FFG median bell, including for NFL games and soccer pools (1x2).

The download location for sport bet software category is:[](https://saliu.com/free-sports-betting.html)

### [<u>Sports Betting Software, Betting Systems, Excel Spreadsheets</u>](https://saliu.com/free-sports-betting.html)

![Resources in American football, NFL, soccer, 1x2, odds, betting lines, predictions, results.](https://saliu.com/HLINE.gif)

[

## <u>4. Resources in Sports Betting: Theory, Mathematics, Excel Spreadsheet, Systems, Parlays</u>

](https://saliu.com/content/sports.html)See a comprehensive directory of the pages and materials on the subject of sports betting, software, systems, and parlays.

-   **BrightS**: _Powerful Integrated_ [_**Sports Betting Software**_](https://saliu.com/sports-software.html).  
    It bundles in a convenient system the most important pieces of sports betting software, accompanied by a visual tutorial.
-   [_**Results, Performance of SPORTS.XLS, Sports Betting Spreadsheet**_](https://saliu.com/football.html).
-   [_**Winning Combinations Inside FFG Median Bell**_](https://saliu.com/random-picks.html).
-   [_**Sports Prognosticating, Betting, Excel Spreadsheets**_](https://saliu.com/bbs/messages/382.html).
-   [_**Software to Generate 1X2 Systems for Soccer Pools**_](https://forums.saliu.com/software-1x2-football-soccer-pools.html).
-   [_**Caveats in Theory of Probability**_](https://saliu.com/probability-caveats.html).
-   Download [_**Software: Sports Betting, Bet Systems, Excel Spreadsheets, Parlays, 1x2**_](https://saliu.com/free-sports-betting.html).

![Gambling in sports betting, casinos, gamble to win: The best mathematical winning strategy.](https://saliu.com/HLINE.gif)

-   For the best mathematical casino gambling strategy (approach) for non-system players, read:  
    
-   [_**Casino, Casinos: Gambling**_](https://saliu.com/keywords/casino.htm), Gamble, Win, Winning in Casino.
-   [_**The Best Casino Gambling Systems: Blackjack, Roulette, Limited Martingale Betting, Progressions**_](https://saliu.com/occult-science-gambling.html):  
    ~ The _**Fundamental Formula of Gambling**_ and Limited-Step Martingale.
-   [_**The Best Strategy for Lottery, Gambling, Sports Betting, Horse Racing, Blackjack, Roulette**_](https://saliu.com/strategy-gambling-lottery.html).

![Learn sports betting, bet to win based on gambling mathematics with formulas.](https://saliu.com/HLINE.gif)

**[Home](https://saliu.com/index.htm) | [Search](https://saliu.com/Search.htm) | [New Writings](https://saliu.com/bbs/index.html) | [Fundamental Formula](https://saliu.com/formula.htm) | [Odds, Generator](https://saliu.com/calculator_generator.html) | [Contents](https://saliu.com/content/index.html) | [Forums](https://forums.saliu.com/) | [Sitemap](https://saliu.com/sitemap/index.html)**

![The founder of sports betting science created the best systems and software.](https://saliu.com/HLINE.gif)
