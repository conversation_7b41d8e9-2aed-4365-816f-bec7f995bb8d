# Skip analysis functions

"""
SkipAnalyzer for tracking skip patterns
"""
struct SkipAnalyzer
    historical_data::Vector{LotteryDraw}
    skip_cache::Dict{Int, Vector{Int}}
    
    SkipAnalyzer(data::Vector{LotteryDraw}) = new(data, Dict{Int, Vector{Int}}())
end

"""
Create SkipAnalyzer from raw data (Vector{Vector{Int}})
"""
function SkipAnalyzer(raw_data::Vector{Vector{Int}})
    # Convert raw data to LotteryDraw format
    draws = LotteryDraw[]
    for (i, numbers) in enumerate(raw_data)
        # Create sequential dates for raw data
        date = Date(2022, 1, 1) + Day(length(raw_data) - i)
        draw = LotteryDraw(numbers, date, i)
        push!(draws, draw)
    end
    
    return SkipAnalyzer(draws)
end

"""
Calculate skip sequence for a specific number using SkipSystem method
(consistent with FFG theory)

SkipSystem method: skip = total draws between hits (including the last hit)
This is more consistent with FFG theory than MDIEditor Lotto WE method.

Example: If number hits in draw 1 and draw 3, skip = 3 - 1 = 2
Returns skips in reverse chronological order: [current_skip, most_recent_skip, ..., oldest_skip]
"""
function calculate_skips(analyzer::<PERSON>p<PERSON>nal<PERSON><PERSON>, number::Int)::Vector{Int}
    if haskey(analyzer.skip_cache, number)
        return analyzer.skip_cache[number]
    end

    skips = Int[]
    occurrences = Int[]

    # First, find all occurrences
    for (i, draw) in enumerate(analyzer.historical_data)
        if number in draw.numbers
            push!(occurrences, i)
        end
    end

    # Calculate skips between consecutive occurrences
    for i in 2:length(occurrences)
        skip = occurrences[i] - occurrences[i-1] - 1
        push!(skips, skip)
    end

    # Reverse to get most recent skips first
    reverse!(skips)

    # The most recent occurrence is the first one in the list (smallest index)
    last_occurrence = isempty(occurrences) ? 0 : occurrences[1]

    # Add current skip (from last occurrence to now)
    if last_occurrence > 0
        # Current skip is the number of draws since the last occurrence
        # If last_occurrence is 1 (most recent), current skip is 0
        # If last_occurrence is 2, current skip is 1, etc.
        current_skip = last_occurrence - 1
        pushfirst!(skips, current_skip)  # Current skip goes first
    else
        # If number never appeared, current skip is the total number of draws
        pushfirst!(skips, length(analyzer.historical_data))
    end

    analyzer.skip_cache[number] = skips
    return skips
end

"""
Calculate skip sequence using MDIEditor Lotto WE method for comparison
(skip = number of draws completely missed between hits)

Example: If number hits in draw 1 and draw 3, skip = 3 - 1 - 1 = 1
Returns skips in reverse chronological order: [current_skip, most_recent_skip, ..., oldest_skip]
"""
function calculate_skips_mdi_method(analyzer::SkipAnalyzer, number::Int)::Vector{Int}
    skips = Int[]
    last_occurrence = 0

    for (i, draw) in enumerate(analyzer.historical_data)
        if number in draw.numbers
            if last_occurrence > 0
                # MDIEditor method: exclude the last hit from the count
                skip = i - last_occurrence - 1
                push!(skips, skip)
            end
            last_occurrence = i
        end
    end

    # Reverse to get most recent skips first
    reverse!(skips)

    # Add current skip (from last occurrence to now)
    if last_occurrence > 0
        current_skip = length(analyzer.historical_data) - last_occurrence
        pushfirst!(skips, current_skip)  # Current skip goes first
    end

    return skips
end

"""
Get current skip value for a number
"""
function get_current_skip(analyzer::SkipAnalyzer, number::Int)::Int
    skips = calculate_skips(analyzer, number)
    return isempty(skips) ? length(analyzer.historical_data) : skips[1]
end

"""
Generate skip chart for a number
"""
function generate_skip_chart(analyzer::SkipAnalyzer, number::Int)::SkipChart
    skips = calculate_skips(analyzer, number)
    current_skip = get_current_skip(analyzer, number)
    
    # Calculate FFG median
    ffg_calc = FFGCalculator()
    ffg_median = calculate_ffg_median(ffg_calc, number, analyzer.historical_data)
    
    # Check if timing is favorable
    is_favorable = current_skip <= ffg_median
    
    return SkipChart(
        number,
        skips,
        current_skip,
        ffg_median,
        is_favorable
    )
end

"""
Update skips with new draw data
"""
function update_skips(analyzer::SkipAnalyzer, new_draw::LotteryDraw)
    # Add new draw to historical data
    pushfirst!(analyzer.historical_data, new_draw)
    
    # Clear cache to force recalculation
    empty!(analyzer.skip_cache)
end

"""
Update skips with new raw draw data
"""
function update_skips(analyzer::SkipAnalyzer, new_draw::Vector{Int})
    # Create LotteryDraw from raw data
    new_date = analyzer.historical_data[1].draw_date + Day(1)
    new_id = analyzer.historical_data[1].draw_id + 1
    lottery_draw = LotteryDraw(new_draw, new_date, new_id)
    
    update_skips(analyzer, lottery_draw)
end

"""
Calculate skip statistics for a number
"""
function calculate_skip_statistics(analyzer::SkipAnalyzer, number::Int)::Dict{String, Float64}
    skips = calculate_skips(analyzer, number)
    
    if isempty(skips)
        return Dict{String, Float64}(
            "mean" => 0.0,
            "median" => 0.0,
            "std_dev" => 0.0,
            "min" => 0.0,
            "max" => 0.0,
            "count" => 0.0
        )
    end
    
    return Dict{String, Float64}(
        "mean" => mean(skips),
        "median" => median(skips),
        "std_dev" => std(skips),
        "min" => Float64(minimum(skips)),
        "max" => Float64(maximum(skips)),
        "count" => Float64(length(skips))
    )
end

"""
Get all numbers with favorable timing (current skip ≤ FFG median)
"""
function get_favorable_numbers(analyzer::SkipAnalyzer)::Vector{Int}
    favorable_numbers = Int[]
    ffg_calc = FFGCalculator()
    
    for number in 1:39
        current_skip = get_current_skip(analyzer, number)
        ffg_median = calculate_ffg_median(ffg_calc, number, analyzer.historical_data)
        
        if current_skip <= ffg_median
            push!(favorable_numbers, number)
        end
    end
    
    return sort(favorable_numbers)
end

"""
Get skip analysis summary for all numbers
"""
function get_skip_summary(analyzer::SkipAnalyzer)::Dict{Int, Dict{String, Any}}
    summary = Dict{Int, Dict{String, Any}}()
    ffg_calc = FFGCalculator()
    
    for number in 1:39
        current_skip = get_current_skip(analyzer, number)
        ffg_median = calculate_ffg_median(ffg_calc, number, analyzer.historical_data)
        skip_stats = calculate_skip_statistics(analyzer, number)
        
        summary[number] = Dict{String, Any}(
            "current_skip" => current_skip,
            "ffg_median" => ffg_median,
            "is_favorable" => current_skip <= ffg_median,
            "statistics" => skip_stats
        )
    end
    
    return summary
end

"""
Find numbers with longest current skips
"""
function find_longest_skips(analyzer::SkipAnalyzer, count::Int = 10)::Vector{Tuple{Int, Int}}
    skip_pairs = Tuple{Int, Int}[]
    
    for number in 1:39
        current_skip = get_current_skip(analyzer, number)
        push!(skip_pairs, (number, current_skip))
    end
    
    # Sort by skip length (descending)
    sort!(skip_pairs, by = x -> x[2], rev = true)
    
    return skip_pairs[1:min(count, length(skip_pairs))]
end

"""
Find numbers with shortest current skips
"""
function find_shortest_skips(analyzer::SkipAnalyzer, count::Int = 10)::Vector{Tuple{Int, Int}}
    skip_pairs = Tuple{Int, Int}[]
    
    for number in 1:39
        current_skip = get_current_skip(analyzer, number)
        push!(skip_pairs, (number, current_skip))
    end
    
    # Sort by skip length (ascending)
    sort!(skip_pairs, by = x -> x[2])
    
    return skip_pairs[1:min(count, length(skip_pairs))]
end

"""
Calculate skip distribution analysis
"""
function analyze_skip_distribution(analyzer::SkipAnalyzer)::Dict{String, Any}
    all_skips = Int[]
    skip_counts = Dict{Int, Int}()
    
    # Collect all skips from all numbers
    for number in 1:39
        skips = calculate_skips(analyzer, number)
        append!(all_skips, skips)
        
        # Count occurrences of each skip value
        for skip in skips
            skip_counts[skip] = get(skip_counts, skip, 0) + 1
        end
    end
    
    if isempty(all_skips)
        return Dict{String, Any}(
            "total_skips" => 0,
            "mean_skip" => 0.0,
            "median_skip" => 0.0,
            "most_common_skip" => 0,
            "skip_range" => (0, 0)
        )
    end
    
    # Find most common skip value
    most_common_skip = 0
    max_count = 0
    for (skip, count) in skip_counts
        if count > max_count
            max_count = count
            most_common_skip = skip
        end
    end
    
    return Dict{String, Any}(
        "total_skips" => length(all_skips),
        "mean_skip" => mean(all_skips),
        "median_skip" => median(all_skips),
        "std_dev_skip" => std(all_skips),
        "min_skip" => minimum(all_skips),
        "max_skip" => maximum(all_skips),
        "most_common_skip" => most_common_skip,
        "most_common_count" => max_count,
        "skip_distribution" => skip_counts
    )
end

"""
Predict next favorable numbers based on skip analysis
"""
function predict_favorable_numbers(analyzer::SkipAnalyzer, max_predictions::Int = 15)::Vector{Tuple{Int, Float64}}
    predictions = Tuple{Int, Float64}[]
    ffg_calc = FFGCalculator()
    
    for number in 1:39
        current_skip = get_current_skip(analyzer, number)
        ffg_median = calculate_ffg_median(ffg_calc, number, analyzer.historical_data)
        
        # Calculate favorability score (lower is better)
        if current_skip <= ffg_median
            # Favorable timing - score based on how close to median
            score = current_skip / ffg_median
        else
            # Not favorable - score > 1.0
            score = current_skip / ffg_median
        end
        
        push!(predictions, (number, score))
    end
    
    # Sort by score (ascending - lower scores are more favorable)
    sort!(predictions, by = x -> x[2])
    
    return predictions[1:min(max_predictions, length(predictions))]
end

"""
Generate comprehensive skip report for a specific number
"""
function generate_skip_report(analyzer::SkipAnalyzer, number::Int)::Dict{String, Any}
    if !(1 <= number <= 39)
        throw(ArgumentError("Number must be between 1 and 39"))
    end
    
    skips = calculate_skips(analyzer, number)
    current_skip = get_current_skip(analyzer, number)
    skip_stats = calculate_skip_statistics(analyzer, number)
    
    ffg_calc = FFGCalculator()
    ffg_median = calculate_ffg_median(ffg_calc, number, analyzer.historical_data)
    is_favorable = current_skip <= ffg_median
    
    # Calculate percentile of current skip
    percentile = 0.0
    if !isempty(skips)
        smaller_count = count(s -> s <= current_skip, skips)
        percentile = (smaller_count / length(skips)) * 100
    end
    
    return Dict{String, Any}(
        "number" => number,
        "current_skip" => current_skip,
        "ffg_median" => ffg_median,
        "is_favorable" => is_favorable,
        "skip_statistics" => skip_stats,
        "current_skip_percentile" => percentile,
        "total_occurrences" => length(skips),
        "skip_sequence" => skips[1:min(20, length(skips))],  # Last 20 skips
        "recommendation" => is_favorable ? "FAVORABLE - Consider for key number" : "NOT FAVORABLE - Skip for now"
    )
end

"""
Compare skip patterns between multiple numbers
"""
function compare_skip_patterns(analyzer::SkipAnalyzer, numbers::Vector{Int})::Dict{String, Any}
    if any(n -> !(1 <= n <= 39), numbers)
        throw(ArgumentError("All numbers must be between 1 and 39"))
    end
    
    comparison = Dict{String, Any}()
    ffg_calc = FFGCalculator()
    
    for number in numbers
        current_skip = get_current_skip(analyzer, number)
        ffg_median = calculate_ffg_median(ffg_calc, number, analyzer.historical_data)
        skip_stats = calculate_skip_statistics(analyzer, number)
        
        comparison[string(number)] = Dict{String, Any}(
            "current_skip" => current_skip,
            "ffg_median" => ffg_median,
            "is_favorable" => current_skip <= ffg_median,
            "mean_skip" => skip_stats["mean"],
            "skip_volatility" => skip_stats["std_dev"]
        )
    end
    
    # Find best candidate
    best_number = 0
    best_score = Inf
    
    for number in numbers
        current_skip = get_current_skip(analyzer, number)
        ffg_median = calculate_ffg_median(ffg_calc, number, analyzer.historical_data)
        
        if current_skip <= ffg_median
            score = current_skip / ffg_median  # Lower is better
            if score < best_score
                best_score = score
                best_number = number
            end
        end
    end
    
    comparison["best_candidate"] = best_number
    comparison["best_score"] = best_score
    
    return comparison
end