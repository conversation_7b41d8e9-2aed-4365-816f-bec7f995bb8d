# Wonder Grid Lottery System - API 參考文檔

## 📚 目錄

- [核心類型](#核心類型)
- [過濾器引擎](#過濾器引擎)
- [優化引擎](#優化引擎)
- [並行計算](#並行計算)
- [快取系統](#快取系統)
- [記憶體管理](#記憶體管理)
- [性能監控](#性能監控)
- [自動調優](#自動調優)

---

## 核心類型

### LotteryDraw

彩票開獎數據結構。

```julia
struct LotteryDraw
    numbers::Vector{Int}      # 開獎號碼 (1-39)
    draw_date::Date          # 開獎日期
    draw_id::Int             # 開獎編號
end
```

**參數：**
- `numbers`: 5個開獎號碼的向量，範圍 1-39
- `draw_date`: 開獎日期
- `draw_id`: 唯一的開獎編號

**範例：**
```julia
draw = LotteryDraw([1, 5, 10, 15, 20], Date(2023, 1, 1), 1)
```

### FilterResult

過濾器計算結果結構。

```julia
struct FilterResult
    current_value::Int       # 當前值
    expected_value::Float64  # 期望值
    filter_type::String      # 過濾器類型
end
```

**參數：**
- `current_value`: 當前計算的整數值
- `expected_value`: 統計期望值
- `filter_type`: 過濾器類型標識

---

## 過濾器引擎

### FilterEngine

主要的過濾器引擎，提供所有 Saliu 過濾器計算功能。

```julia
mutable struct FilterEngine
    historical_data::Vector{LotteryDraw}
end
```

#### 構造函數

```julia
FilterEngine(historical_data::Vector{LotteryDraw})
```

創建新的過濾器引擎實例。

**參數：**
- `historical_data`: 歷史開獎數據向量

**範例：**
```julia
engine = FilterEngine(historical_draws)
```

#### 主要方法

##### calculate_one_filter

```julia
calculate_one_filter(engine::FilterEngine, number::Int) -> FilterResult
```

計算 ONE 過濾器（Skip 值）。

**參數：**
- `engine`: FilterEngine 實例
- `number`: 要計算的號碼 (1-39)

**返回：**
- `FilterResult`: 包含當前 Skip 值和期望值

**範例：**
```julia
result = calculate_one_filter(engine, 1)
println("號碼 1 的 Skip 值: $(result.current_value)")
```

##### calculate_two_filter

```julia
calculate_two_filter(engine::FilterEngine, numbers::Vector{Int}) -> FilterResult
```

計算 TWO 過濾器（配對分析）。

**參數：**
- `engine`: FilterEngine 實例
- `numbers`: 要分析的號碼向量

**返回：**
- `FilterResult`: 包含配對統計結果

**範例：**
```julia
result = calculate_two_filter(engine, [1, 2, 3])
println("配對數量: $(result.current_value)")
```

##### calculate_three_filter

```julia
calculate_three_filter(engine::FilterEngine, numbers::Vector{Int}) -> FilterResult
```

計算 THREE 過濾器（三重組合分析）。

##### calculate_four_filter

```julia
calculate_four_filter(engine::FilterEngine, numbers::Vector{Int}) -> FilterResult
```

計算 FOUR 過濾器（四重組合分析）。

##### calculate_five_filter

```julia
calculate_five_filter(engine::FilterEngine, numbers::Vector{Int}) -> FilterResult
```

計算 FIVE 過濾器（五重組合分析）。

---

## 優化引擎

### OptimizedFilterEngine

高性能優化版本的過濾器引擎，整合快取、記憶體池和緊湊數據結構。

```julia
mutable struct OptimizedFilterEngine
    # 內部結構（詳細實現請參考源碼）
end
```

#### 構造函數

```julia
OptimizedFilterEngine(
    historical_data::Vector{LotteryDraw};
    use_compact_data::Bool = true,
    enable_caching::Bool = true,
    auto_cleanup::Bool = true
) -> OptimizedFilterEngine
```

創建優化的過濾器引擎。

**參數：**
- `historical_data`: 歷史開獎數據
- `use_compact_data`: 是否使用緊湊數據結構（默認：true）
- `enable_caching`: 是否啟用快取（默認：true）
- `auto_cleanup`: 是否自動清理（默認：true）

**範例：**
```julia
opt_engine = OptimizedFilterEngine(
    historical_draws,
    use_compact_data = true,
    enable_caching = true
)
```

#### 主要方法

##### calculate_skip_optimized

```julia
calculate_skip_optimized(engine::OptimizedFilterEngine, number::Int) -> Int
```

優化版本的 Skip 計算，支援快取和緊湊數據。

##### calculate_pairing_frequency_optimized

```julia
calculate_pairing_frequency_optimized(
    engine::OptimizedFilterEngine, 
    num1::Int, 
    num2::Int
) -> Int
```

優化版本的配對頻率計算。

##### get_engine_statistics

```julia
get_engine_statistics(engine::OptimizedFilterEngine) -> Dict{String, Any}
```

獲取引擎性能統計信息。

**返回：**
包含快取命中率、記憶體使用、性能指標等的字典。

---

## 並行計算

### 並行 Skip 計算

```julia
calculate_all_skips_parallel(
    historical_data::Vector{LotteryDraw}, 
    numbers::Vector{Int} = collect(1:39)
) -> Dict{Int, ParallelTaskResult{Int}}
```

並行計算多個號碼的 Skip 值。

**參數：**
- `historical_data`: 歷史數據
- `numbers`: 要計算的號碼列表（默認：1-39）

**返回：**
- 字典，鍵為號碼，值為 `ParallelTaskResult`

### 分散式 Wonder Grid 生成

```julia
generate_wonder_grid_distributed(
    historical_data::Vector{LotteryDraw}, 
    grid_size::Int = 100
) -> Dict{String, Any}
```

分散式生成 Wonder Grid。

**參數：**
- `historical_data`: 歷史數據
- `grid_size`: 網格大小

**返回：**
包含生成的 Wonder Grid 和統計信息的字典。

---

## 快取系統

### MultiLevelCache

三層快取系統（L1/L2/L3）。

```julia
mutable struct MultiLevelCache
    # 內部實現
end
```

#### 構造函數

```julia
MultiLevelCache(;
    l1_max_items::Int = 100,
    l2_max_items::Int = 1000,
    l3_max_items::Int = 10000,
    l1_max_size::Int = 1024 * 1024,
    l2_max_size::Int = 10 * 1024 * 1024,
    l3_max_size::Int = 100 * 1024 * 1024
) -> MultiLevelCache
```

#### 主要方法

##### get_from_cache

```julia
get_from_cache(cache::MultiLevelCache, key::String) -> Union{Any, Nothing}
```

從快取中獲取值。

##### put_in_cache!

```julia
put_in_cache!(
    cache::MultiLevelCache, 
    key::String, 
    value, 
    level::CacheLevel = L1_CACHE
)
```

將值存入快取。

##### get_cache_statistics

```julia
get_cache_statistics(cache::MultiLevelCache) -> Dict{String, Any}
```

獲取快取統計信息。

---

## 記憶體管理

### 記憶體池

#### get_temp_vector

```julia
get_temp_vector(::Type{T}, size::Int = 0) -> Vector{T} where T
```

從記憶體池獲取可重用向量。

#### return_temp_vector!

```julia
return_temp_vector!(vec::Vector{T}) -> Bool where T
```

將向量返回記憶體池。

#### get_temp_dict

```julia
get_temp_dict(::Type{K}, ::Type{V}) -> Dict{K,V} where {K,V}
```

從記憶體池獲取可重用字典。

### 緊湑數據結構

#### create_compact_dataset

```julia
create_compact_dataset(draws::Vector{LotteryDraw}) -> CompactLotteryDataset
```

創建緊湊數據集，節省 92.9% 記憶體。

#### verify_data_integrity

```julia
verify_data_integrity(
    original::Vector{LotteryDraw}, 
    compact_dataset::CompactLotteryDataset
) -> Bool
```

驗證緊湊數據的完整性。

---

## 性能監控

### PerformanceMonitor

性能監控器，追蹤系統性能指標。

#### record_metric!

```julia
record_metric!(
    monitor::PerformanceMonitor, 
    name::String, 
    value::Float64, 
    unit::String = "", 
    category::String = "general"
)
```

記錄性能指標。

#### start_timer! / end_timer!

```julia
start_timer!(monitor::PerformanceMonitor, timer_name::String)
end_timer!(monitor::PerformanceMonitor, timer_name::String) -> Float64
```

計時器功能。

#### get_performance_report

```julia
get_performance_report(monitor::PerformanceMonitor) -> Dict{String, Any}
```

獲取性能報告。

---

## 自動調優

### AutoTuner

自動調優系統，動態優化性能參數。

#### auto_tune_performance!

```julia
auto_tune_performance!(
    tuner::AutoTuner, 
    monitor::SystemPerformanceMonitor
) -> Bool
```

執行自動調優。

#### get_tuning_report

```julia
get_tuning_report(tuner::AutoTuner) -> Dict{String, Any}
```

獲取調優報告。

---

## 便利函數

### 全局實例

系統提供了幾個全局實例以便快速使用：

```julia
# 全局記憶體池
GLOBAL_MEMORY_POOL

# 全局性能監控器
GLOBAL_PERFORMANCE_MONITOR

# 全局自動調優器
GLOBAL_AUTO_TUNER
```

### 快速開始函數

```julia
# 快速獲取性能摘要
get_global_performance_summary() -> Dict{String, Any}

# 執行自動調優
perform_auto_tuning!() -> Bool

# 獲取調優報告
get_global_tuning_report() -> Dict{String, Any}
```

---

## 錯誤處理

所有 API 函數都包含適當的錯誤處理：

- **參數驗證**：檢查輸入參數的有效性
- **範圍檢查**：確保號碼在 1-39 範圍內
- **數據完整性**：驗證歷史數據的完整性
- **資源管理**：自動清理和錯誤恢復

---

## 性能考慮

- **快取策略**：使用三層快取系統提高性能
- **記憶體優化**：緊湊數據結構節省 92.9% 記憶體
- **並行計算**：支援多執行緒並行處理
- **自動調優**：動態優化性能參數

---

*更多詳細信息請參考源碼和用戶手冊。*
