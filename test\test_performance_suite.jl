# Performance Test Suite
# 性能測試套件 - 測試系統的性能表現和可擴展性

using Test
using Dates
using Statistics

# 引入所有必要的模組
include("../src/types.jl")
include("../src/filter_engine.jl")
include("../src/filters/one_filter.jl")
include("../src/filters/two_filter.jl")
include("../src/filters/three_filter.jl")
include("../src/filters/four_filter.jl")
include("../src/filters/five_filter.jl")
include("../src/skip_analyzer.jl")
include("../src/ffg_calculator.jl")
include("../src/pairing_engine.jl")
include("../src/statistics/basic_stats.jl")
include("test_data_manager.jl")
include("test_configuration.jl")

"""
基準性能測試
測試各個組件的基準性能
"""
function test_benchmark_performance(test_data::Vector{LotteryDraw})::Dict{String, Any}
    @testset "Benchmark Performance" begin
        results = Dict{String, Any}()
        benchmark_results = []
        
        # 創建所有組件
        engine = FilterEngine(test_data)
        skip_analyzer = SkipAnalyzer(test_data)
        ffg_calculator = FFGCalculator()
        pairing_engine = PairingEngine(test_data)
        
        # 基準測試 1：過濾器引擎性能
        try
            iterations = 100
            filter_times = Dict{String, Vector{Float64}}()
            
            # ONE 過濾器基準
            one_times = Float64[]
            for _ in 1:iterations
                start_time = time()
                calculate_one_filter(engine, rand(1:39))
                execution_time = (time() - start_time) * 1000
                push!(one_times, execution_time)
            end
            filter_times["ONE"] = one_times
            
            # TWO 過濾器基準
            two_times = Float64[]
            for _ in 1:iterations÷2  # 較少迭代，因為計算較重
                test_numbers = sort(rand(1:39, 3))
                start_time = time()
                calculate_two_filter(engine, test_numbers)
                execution_time = (time() - start_time) * 1000
                push!(two_times, execution_time)
            end
            filter_times["TWO"] = two_times
            
            # 計算統計
            filter_stats = Dict{String, Dict{String, Float64}}()
            for (filter_name, times) in filter_times
                filter_stats[filter_name] = Dict(
                    "mean_ms" => mean(times),
                    "median_ms" => median(times),
                    "p95_ms" => length(times) > 0 ? sort(times)[Int(ceil(0.95 * length(times)))] : 0.0,
                    "max_ms" => maximum(times),
                    "min_ms" => minimum(times)
                )
            end
            
            # 性能要求檢查
            one_acceptable = filter_stats["ONE"]["mean_ms"] < 10.0  # 平均小於 10ms
            two_acceptable = filter_stats["TWO"]["mean_ms"] < 50.0  # 平均小於 50ms
            
            push!(benchmark_results, Dict(
                "component" => "filter_engine",
                "status" => (one_acceptable && two_acceptable) ? "passed" : "failed",
                "stats" => filter_stats,
                "requirements_met" => Dict(
                    "ONE_filter" => one_acceptable,
                    "TWO_filter" => two_acceptable
                )
            ))
        catch e
            push!(benchmark_results, Dict(
                "component" => "filter_engine",
                "status" => "failed",
                "error" => string(e)
            ))
        end
        
        # 基準測試 2：Skip 分析器性能
        try
            iterations = 200
            skip_times = Float64[]
            
            for _ in 1:iterations
                start_time = time()
                get_current_skip(skip_analyzer, rand(1:39))
                execution_time = (time() - start_time) * 1000
                push!(skip_times, execution_time)
            end
            
            skip_stats = Dict(
                "mean_ms" => mean(skip_times),
                "median_ms" => median(skip_times),
                "p95_ms" => sort(skip_times)[Int(ceil(0.95 * length(skip_times)))],
                "max_ms" => maximum(skip_times)
            )
            
            skip_acceptable = skip_stats["mean_ms"] < 5.0  # 平均小於 5ms
            
            push!(benchmark_results, Dict(
                "component" => "skip_analyzer",
                "status" => skip_acceptable ? "passed" : "failed",
                "stats" => skip_stats,
                "requirements_met" => skip_acceptable
            ))
        catch e
            push!(benchmark_results, Dict(
                "component" => "skip_analyzer",
                "status" => "failed",
                "error" => string(e)
            ))
        end
        
        # 基準測試 3：FFG 計算器性能
        try
            iterations = 50
            ffg_times = Float64[]
            
            for _ in 1:iterations
                start_time = time()
                calculate_ffg_median(ffg_calculator, rand(1:39), test_data)
                execution_time = (time() - start_time) * 1000
                push!(ffg_times, execution_time)
            end
            
            ffg_stats = Dict(
                "mean_ms" => mean(ffg_times),
                "median_ms" => median(ffg_times),
                "p95_ms" => sort(ffg_times)[Int(ceil(0.95 * length(ffg_times)))],
                "max_ms" => maximum(ffg_times)
            )
            
            ffg_acceptable = ffg_stats["mean_ms"] < 100.0  # 平均小於 100ms
            
            push!(benchmark_results, Dict(
                "component" => "ffg_calculator",
                "status" => ffg_acceptable ? "passed" : "failed",
                "stats" => ffg_stats,
                "requirements_met" => ffg_acceptable
            ))
        catch e
            push!(benchmark_results, Dict(
                "component" => "ffg_calculator",
                "status" => "failed",
                "error" => string(e)
            ))
        end
        
        # 基準測試 4：配對引擎性能
        try
            iterations = 100
            pairing_times = Float64[]
            
            for _ in 1:iterations
                num1, num2 = rand(1:39), rand(1:39)
                if num1 != num2
                    start_time = time()
                    get_pairing_frequency(pairing_engine, num1, num2)
                    execution_time = (time() - start_time) * 1000
                    push!(pairing_times, execution_time)
                end
            end
            
            pairing_stats = Dict(
                "mean_ms" => mean(pairing_times),
                "median_ms" => median(pairing_times),
                "p95_ms" => sort(pairing_times)[Int(ceil(0.95 * length(pairing_times)))],
                "max_ms" => maximum(pairing_times)
            )
            
            pairing_acceptable = pairing_stats["mean_ms"] < 2.0  # 平均小於 2ms
            
            push!(benchmark_results, Dict(
                "component" => "pairing_engine",
                "status" => pairing_acceptable ? "passed" : "failed",
                "stats" => pairing_stats,
                "requirements_met" => pairing_acceptable
            ))
        catch e
            push!(benchmark_results, Dict(
                "component" => "pairing_engine",
                "status" => "failed",
                "error" => string(e)
            ))
        end
        
        # 計算基準性能成功率
        passed_tests = count(result -> result["status"] == "passed", benchmark_results)
        total_tests = length(benchmark_results)
        benchmark_success_rate = passed_tests / total_tests
        
        results["benchmark_results"] = benchmark_results
        results["benchmark_success_rate"] = benchmark_success_rate
        results["passed_tests"] = passed_tests
        results["total_tests"] = total_tests
        
        @test benchmark_success_rate >= 0.75  # 要求 75% 以上的基準測試通過
        
        println("  ✅ 基準性能測試: $(round(benchmark_success_rate * 100, digits=1))%")
        return results
    end
end

"""
可擴展性測試
測試系統在不同數據規模下的性能表現
"""
function test_scalability(data_manager::TestDataManager)::Dict{String, Any}
    @testset "Scalability Test" begin
        results = Dict{String, Any}()
        scalability_results = []
        
        # 測試不同規模的數據
        data_sizes = ["small", "medium", "large"]
        
        for size_category in data_sizes
            try
                test_data = get_test_data(data_manager, size_category)
                data_size = length(test_data)
                
                # 測試過濾器引擎的可擴展性
                start_time = time()
                engine = FilterEngine(test_data)
                creation_time = (time() - start_time) * 1000
                
                # 執行一些操作來測試性能
                start_time = time()
                for _ in 1:10
                    calculate_one_filter(engine, rand(1:39))
                end
                operation_time = (time() - start_time) * 1000
                
                # 測試 Skip 分析器的可擴展性
                start_time = time()
                skip_analyzer = SkipAnalyzer(test_data)
                skip_creation_time = (time() - start_time) * 1000
                
                start_time = time()
                for _ in 1:10
                    get_current_skip(skip_analyzer, rand(1:39))
                end
                skip_operation_time = (time() - start_time) * 1000
                
                # 計算每筆數據的平均處理時間
                avg_creation_time_per_record = creation_time / data_size
                avg_operation_time_per_call = operation_time / 10
                
                # 可擴展性要求：處理時間不應該隨數據量線性增長太快
                creation_scalable = avg_creation_time_per_record < 1.0  # 每筆數據小於 1ms
                operation_scalable = avg_operation_time_per_call < 10.0  # 每次操作小於 10ms
                
                push!(scalability_results, Dict(
                    "data_size_category" => size_category,
                    "data_size" => data_size,
                    "creation_time_ms" => creation_time,
                    "operation_time_ms" => operation_time,
                    "skip_creation_time_ms" => skip_creation_time,
                    "skip_operation_time_ms" => skip_operation_time,
                    "avg_creation_time_per_record" => avg_creation_time_per_record,
                    "avg_operation_time_per_call" => avg_operation_time_per_call,
                    "creation_scalable" => creation_scalable,
                    "operation_scalable" => operation_scalable,
                    "overall_scalable" => creation_scalable && operation_scalable
                ))
                
            catch e
                push!(scalability_results, Dict(
                    "data_size_category" => size_category,
                    "error" => string(e),
                    "overall_scalable" => false
                ))
            end
        end
        
        # 計算可擴展性成功率
        scalable_tests = count(result -> get(result, "overall_scalable", false), scalability_results)
        total_tests = length(scalability_results)
        scalability_rate = scalable_tests / total_tests
        
        results["scalability_results"] = scalability_results
        results["scalability_rate"] = scalability_rate
        results["scalable_tests"] = scalable_tests
        results["total_tests"] = total_tests
        
        @test scalability_rate >= 0.7  # 要求 70% 以上的可擴展性測試通過
        
        println("  ✅ 可擴展性測試: $(round(scalability_rate * 100, digits=1))%")
        return results
    end
end

"""
記憶體使用測試
測試系統的記憶體使用效率
"""
function test_memory_usage(test_data::Vector{LotteryDraw})::Dict{String, Any}
    @testset "Memory Usage Test" begin
        results = Dict{String, Any}()
        memory_tests = []
        
        # 測試 1：組件創建的記憶體使用
        try
            # 記錄創建前的記憶體使用（簡化測試）
            initial_objects = length(test_data)
            
            # 創建各個組件
            engine = FilterEngine(test_data)
            skip_analyzer = SkipAnalyzer(test_data)
            ffg_calculator = FFGCalculator()
            pairing_engine = PairingEngine(test_data)
            
            # 檢查組件是否正確創建（間接測試記憶體使用）
            engine_created = isa(engine, FilterEngine)
            skip_created = isa(skip_analyzer, SkipAnalyzer)
            ffg_created = isa(ffg_calculator, FFGCalculator)
            pairing_created = isa(pairing_engine, PairingEngine)
            
            all_created = engine_created && skip_created && ffg_created && pairing_created
            
            push!(memory_tests, Dict(
                "test" => "component_creation_memory",
                "status" => all_created ? "passed" : "failed",
                "details" => Dict(
                    "initial_data_size" => initial_objects,
                    "engine_created" => engine_created,
                    "skip_created" => skip_created,
                    "ffg_created" => ffg_created,
                    "pairing_created" => pairing_created
                )
            ))
        catch e
            push!(memory_tests, Dict(
                "test" => "component_creation_memory",
                "status" => "failed",
                "error" => string(e)
            ))
        end
        
        # 測試 2：大量操作的記憶體穩定性
        try
            engine = FilterEngine(test_data)
            
            # 執行大量操作，檢查是否有記憶體洩漏（簡化測試）
            operations_successful = 0
            total_operations = 100
            
            for i in 1:total_operations
                try
                    result = calculate_one_filter(engine, rand(1:39))
                    if isa(result, FilterResult)
                        operations_successful += 1
                    end
                catch
                    # 操作失敗可能表示記憶體問題
                    break
                end
            end
            
            # 如果大部分操作成功，認為記憶體使用穩定
            memory_stable = operations_successful >= total_operations * 0.9
            
            push!(memory_tests, Dict(
                "test" => "memory_stability_under_load",
                "status" => memory_stable ? "passed" : "failed",
                "details" => Dict(
                    "operations_successful" => operations_successful,
                    "total_operations" => total_operations,
                    "success_rate" => operations_successful / total_operations
                )
            ))
        catch e
            push!(memory_tests, Dict(
                "test" => "memory_stability_under_load",
                "status" => "failed",
                "error" => string(e)
            ))
        end
        
        # 計算記憶體測試成功率
        passed_tests = count(test -> test["status"] == "passed", memory_tests)
        total_tests = length(memory_tests)
        memory_success_rate = passed_tests / total_tests
        
        results["memory_tests"] = memory_tests
        results["memory_success_rate"] = memory_success_rate
        results["passed_tests"] = passed_tests
        results["total_tests"] = total_tests
        
        @test memory_success_rate >= 0.8  # 要求 80% 以上的記憶體測試通過
        
        println("  ✅ 記憶體使用測試: $(round(memory_success_rate * 100, digits=1))%")
        return results
    end
end

"""
並發性能測試
測試系統的並發處理能力（簡化版本）
"""
function test_concurrent_performance(test_data::Vector{LotteryDraw})::Dict{String, Any}
    @testset "Concurrent Performance Test" begin
        results = Dict{String, Any}()
        concurrent_tests = []
        
        # 測試 1：多個組件同時工作
        try
            # 創建多個相同的組件實例
            engines = [FilterEngine(test_data) for _ in 1:3]
            skip_analyzers = [SkipAnalyzer(test_data) for _ in 1:3]
            
            # 同時執行操作（簡化的並發測試）
            start_time = time()
            
            results_collected = []
            for i in 1:3
                try
                    # 每個實例執行不同的操作
                    engine_result = calculate_one_filter(engines[i], i)
                    skip_result = get_current_skip(skip_analyzers[i], i)
                    
                    push!(results_collected, Dict(
                        "instance" => i,
                        "engine_result" => engine_result.current_value,
                        "skip_result" => skip_result
                    ))
                catch e
                    push!(results_collected, Dict(
                        "instance" => i,
                        "error" => string(e)
                    ))
                end
            end
            
            total_time = (time() - start_time) * 1000
            
            # 檢查所有實例是否成功執行
            successful_instances = count(r -> !haskey(r, "error"), results_collected)
            concurrent_successful = successful_instances == 3
            
            push!(concurrent_tests, Dict(
                "test" => "multiple_instance_concurrent",
                "status" => concurrent_successful ? "passed" : "failed",
                "details" => Dict(
                    "total_time_ms" => total_time,
                    "successful_instances" => successful_instances,
                    "total_instances" => 3,
                    "results" => results_collected
                )
            ))
        catch e
            push!(concurrent_tests, Dict(
                "test" => "multiple_instance_concurrent",
                "status" => "failed",
                "error" => string(e)
            ))
        end
        
        # 計算並發測試成功率
        passed_tests = count(test -> test["status"] == "passed", concurrent_tests)
        total_tests = length(concurrent_tests)
        concurrent_success_rate = passed_tests / total_tests
        
        results["concurrent_tests"] = concurrent_tests
        results["concurrent_success_rate"] = concurrent_success_rate
        results["passed_tests"] = passed_tests
        results["total_tests"] = total_tests
        
        @test concurrent_success_rate >= 0.8  # 要求 80% 以上的並發測試通過
        
        println("  ✅ 並發性能測試: $(round(concurrent_success_rate * 100, digits=1))%")
        return results
    end
end

"""
執行完整的性能測試套件
"""
function run_performance_test_suite(data_manager::TestDataManager)::Dict{String, Any}
    println("🧪 開始執行性能測試套件...")
    
    performance_results = Dict{String, Any}()
    
    # 使用中等大小的測試數據
    test_data = get_test_data(data_manager, "medium")
    
    try
        # 執行各項性能測試
        performance_results["benchmark"] = test_benchmark_performance(test_data)
        performance_results["scalability"] = test_scalability(data_manager)
        performance_results["memory_usage"] = test_memory_usage(test_data)
        performance_results["concurrent"] = test_concurrent_performance(test_data)
        
        # 計算整體評分
        benchmark_score = performance_results["benchmark"]["benchmark_success_rate"]
        scalability_score = performance_results["scalability"]["scalability_rate"]
        memory_score = performance_results["memory_usage"]["memory_success_rate"]
        concurrent_score = performance_results["concurrent"]["concurrent_success_rate"]
        
        overall_score = (benchmark_score + scalability_score + memory_score + concurrent_score) / 4
        performance_results["overall_score"] = overall_score
        
        println("\n📊 性能測試套件結果:")
        println("  - 基準性能: $(round(benchmark_score * 100, digits=1))%")
        println("  - 可擴展性: $(round(scalability_score * 100, digits=1))%")
        println("  - 記憶體使用: $(round(memory_score * 100, digits=1))%")
        println("  - 並發性能: $(round(concurrent_score * 100, digits=1))%")
        println("  - 整體評分: $(round(overall_score * 100, digits=1))%")
        
        if overall_score >= 0.90
            println("🎉 性能測試：優秀")
        elseif overall_score >= 0.80
            println("✅ 性能測試：良好")
        elseif overall_score >= 0.70
            println("⚠️ 性能測試：需要改進")
        else
            println("❌ 性能測試：需要重大修復")
        end
        
        return performance_results
        
    catch e
        @error "性能測試套件失敗: $e"
        performance_results["error"] = string(e)
        performance_results["overall_score"] = 0.0
        return performance_results
    end
end

# 導出主要函數
export test_benchmark_performance, test_scalability
export test_memory_usage, test_concurrent_performance
export run_performance_test_suite
