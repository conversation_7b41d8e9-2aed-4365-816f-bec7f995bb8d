# Wonder Grid Lottery System - 完整回歸測試套件
# 確保所有功能正常工作，沒有回歸問題

using Test
using Dates

println("🔄 Wonder Grid Lottery System - 完整回歸測試")
println("=" ^ 60)

# 測試配置
const REGRESSION_TEST_CONFIG = Dict(
    "small_dataset_size" => 20,
    "medium_dataset_size" => 100,
    "large_dataset_size" => 500,
    "performance_threshold_ms" => 1000,
    "memory_threshold_mb" => 100,
    "parallel_efficiency_threshold" => 0.5
)

# 生成測試數據
function generate_regression_test_data(size::Int)
    draws = LotteryDraw[]
    base_date = Date(2020, 1, 1)
    
    for i in 1:size
        # 生成隨機但一致的號碼（使用固定種子）
        Random.seed!(i)
        numbers = sort(sample(1:39, 5, replace=false))
        date = base_date + Day(i-1)
        push!(draws, LotteryDraw(numbers, date, i))
    end
    
    return draws
end

println("📊 準備回歸測試數據...")
small_dataset = generate_regression_test_data(REGRESSION_TEST_CONFIG["small_dataset_size"])
medium_dataset = generate_regression_test_data(REGRESSION_TEST_CONFIG["medium_dataset_size"])
large_dataset = generate_regression_test_data(REGRESSION_TEST_CONFIG["large_dataset_size"])

println("✅ 測試數據準備完成")
println("   小數據集: $(length(small_dataset)) 筆")
println("   中數據集: $(length(medium_dataset)) 筆")
println("   大數據集: $(length(large_dataset)) 筆")

@testset "完整回歸測試套件" begin
    
    @testset "核心功能回歸測試" begin
        println("\n🎯 執行核心功能回歸測試...")
        
        # 載入系統
        include("../src/wonder_grid_system.jl")
        
        @testset "基本過濾器功能" begin
            # 測試標準過濾器引擎
            engine = FilterEngine(small_dataset)
            
            # ONE 過濾器測試
            result_1 = calculate_one_filter(engine, 1)
            @test isa(result_1, FilterResult)
            @test result_1.current_value >= 0
            
            # TWO 過濾器測試
            result_2 = calculate_two_filter(engine, [1, 2, 3])
            @test isa(result_2, FilterResult)
            @test result_2.current_value >= 0
            
            # THREE 過濾器測試
            result_3 = calculate_three_filter(engine, [1, 2, 3, 4, 5])
            @test isa(result_3, FilterResult)
            @test result_3.current_value >= 0
            
            println("✅ 基本過濾器功能回歸測試通過")
        end
        
        @testset "優化引擎功能" begin
            # 測試優化引擎
            optimized_engine = OptimizedFilterEngine(medium_dataset)
            
            # Skip 計算測試
            skip_1 = calculate_skip_optimized(optimized_engine, 1)
            @test isa(skip_1, Int)
            @test skip_1 >= 0
            
            # 配對頻率測試
            freq = calculate_pairing_frequency_optimized(optimized_engine, 1, 2)
            @test isa(freq, Int)
            @test freq >= 0
            
            # 引擎統計測試
            stats = get_engine_statistics(optimized_engine)
            @test isa(stats, Dict)
            @test haskey(stats, "cache_hit_rate")
            
            println("✅ 優化引擎功能回歸測試通過")
        end
        
        @testset "系統整合功能" begin
            # 測試完整系統
            system = WonderGridSystem(medium_dataset)
            
            # Skip 計算
            skip = calculate_skip(system, 1)
            @test isa(skip, Int)
            @test skip >= 0
            
            # 配對分析
            pairing = calculate_pairing_frequency(system, 1, 2)
            @test isa(pairing, Int)
            @test pairing >= 0
            
            # Wonder Grid 生成
            grid = generate_wonder_grid(system, 5)
            @test isa(grid, Vector{Vector{Int}})
            @test length(grid) == 5
            
            # 性能報告
            report = get_performance_report(system)
            @test isa(report, Dict)
            @test haskey(report, "system_info")
            
            # 健康檢查
            health = system_health_check(system)
            @test isa(health, Bool)
            
            println("✅ 系統整合功能回歸測試通過")
        end
    end
    
    @testset "性能回歸測試" begin
        println("\n⚡ 執行性能回歸測試...")
        
        @testset "Skip 計算性能" begin
            system = WonderGridSystem(large_dataset)
            
            # 測試單次計算性能
            start_time = time()
            result = calculate_skip(system, 1)
            execution_time = (time() - start_time) * 1000
            
            @test execution_time < REGRESSION_TEST_CONFIG["performance_threshold_ms"]
            @test isa(result, Int)
            
            println("  Skip 計算時間: $(round(execution_time, digits=2))ms")
        end
        
        @testset "批次處理性能" begin
            system = WonderGridSystem(large_dataset)
            test_numbers = [1, 5, 10, 15, 20]
            
            # 測試批次 Skip 計算
            start_time = time()
            results = Dict{Int, Int}()
            for number in test_numbers
                results[number] = calculate_skip(system, number)
            end
            batch_time = (time() - start_time) * 1000
            
            @test length(results) == length(test_numbers)
            @test batch_time < REGRESSION_TEST_CONFIG["performance_threshold_ms"] * length(test_numbers)
            
            println("  批次處理時間: $(round(batch_time, digits=2))ms")
        end
        
        @testset "Wonder Grid 生成性能" begin
            system = WonderGridSystem(medium_dataset)
            
            # 測試 Wonder Grid 生成性能
            start_time = time()
            grid = generate_wonder_grid(system, 20)
            generation_time = (time() - start_time) * 1000
            
            @test length(grid) == 20
            @test generation_time < REGRESSION_TEST_CONFIG["performance_threshold_ms"] * 5  # 允許更長時間
            
            println("  Wonder Grid 生成時間: $(round(generation_time, digits=2))ms")
        end
    end
    
    @testset "記憶體使用回歸測試" begin
        println("\n💾 執行記憶體使用回歸測試...")
        
        @testset "基本記憶體使用" begin
            # 測試系統記憶體使用
            memory_before = Base.gc_bytes()
            system = WonderGridSystem(medium_dataset)
            memory_after = Base.gc_bytes()
            
            memory_used_mb = (memory_after - memory_before) / (1024 * 1024)
            
            @test memory_used_mb < REGRESSION_TEST_CONFIG["memory_threshold_mb"]
            
            println("  系統記憶體使用: $(round(memory_used_mb, digits=2))MB")
        end
        
        @testset "記憶體池效率" begin
            system = WonderGridSystem(medium_dataset)
            
            # 執行操作以觸發記憶體池使用
            for i in 1:50
                calculate_skip(system, rand(1:20))
            end
            
            # 檢查記憶體池統計
            pool_stats = get_all_pool_statistics(GLOBAL_MEMORY_POOL)
            @test isa(pool_stats, Dict)
            @test haskey(pool_stats, "summary")
            
            # 檢查重用率
            if pool_stats["summary"]["total_allocations"] > 0
                reuse_rate = pool_stats["summary"]["overall_reuse_rate"]
                @test reuse_rate >= 0.0  # 至少不是負數
                println("  記憶體池重用率: $(round(reuse_rate * 100, digits=1))%")
            end
        end
        
        @testset "緊湊數據結構效率" begin
            # 測試緊湊數據結構的記憶體節省
            optimized_engine = OptimizedFilterEngine(medium_dataset, use_compact_data=true)
            stats = get_engine_statistics(optimized_engine)
            
            if haskey(stats, "compact_data")
                compact_stats = stats["compact_data"]
                @test compact_stats["memory_savings_percentage"] > 50  # 至少節省 50%
                
                println("  記憶體節省: $(round(compact_stats["memory_savings_percentage"], digits=1))%")
            end
        end
    end
    
    @testset "並行計算回歸測試" begin
        println("\n🚀 執行並行計算回歸測試...")
        
        if Threads.nthreads() > 1
            @testset "並行 Skip 計算" begin
                test_numbers = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
                
                # 序列計算基準
                start_time = time()
                sequential_results = Dict{Int, Int}()
                for number in test_numbers
                    sequential_results[number] = calculate_skip_sequential(medium_dataset, number)
                end
                sequential_time = time() - start_time
                
                # 並行計算
                start_time = time()
                parallel_results = calculate_all_skips_parallel(medium_dataset, test_numbers)
                parallel_time = time() - start_time
                
                # 驗證結果正確性
                @test length(parallel_results) == length(test_numbers)
                for number in test_numbers
                    @test haskey(parallel_results, number)
                    @test parallel_results[number].success
                    @test parallel_results[number].result == sequential_results[number]
                end
                
                # 驗證性能改進
                if parallel_time > 0 && sequential_time > 0
                    speedup = sequential_time / parallel_time
                    efficiency = speedup / Threads.nthreads()
                    
                    @test efficiency >= REGRESSION_TEST_CONFIG["parallel_efficiency_threshold"]
                    
                    println("  並行加速比: $(round(speedup, digits=2))x")
                    println("  並行效率: $(round(efficiency * 100, digits=1))%")
                end
            end
            
            @testset "分散式 Wonder Grid" begin
                # 測試分散式 Wonder Grid 生成
                start_time = time()
                grid_results = generate_wonder_grid_distributed(medium_dataset, 15)
                distributed_time = time() - start_time
                
                @test haskey(grid_results, "wonder_grid")
                @test haskey(grid_results, "completed_tasks")
                @test haskey(grid_results, "failed_tasks")
                
                wonder_grid = grid_results["wonder_grid"]
                @test length(wonder_grid) == 15
                @test grid_results["failed_tasks"] == 0
                
                println("  分散式生成時間: $(round(distributed_time * 1000, digits=2))ms")
                println("  完成任務: $(grid_results["completed_tasks"])")
            end
        else
            println("⚠️ 跳過並行計算測試（需要多執行緒環境）")
        end
    end
    
    @testset "快取系統回歸測試" begin
        println("\n💾 執行快取系統回歸測試...")
        
        @testset "快取命中率" begin
            optimized_engine = OptimizedFilterEngine(medium_dataset, enable_caching=true)
            
            # 執行重複計算以觸發快取
            test_numbers = [1, 2, 3, 4, 5]
            
            # 第一輪計算（冷快取）
            for number in test_numbers
                calculate_skip_optimized(optimized_engine, number)
            end
            
            # 第二輪計算（熱快取）
            for number in test_numbers
                calculate_skip_optimized(optimized_engine, number)
            end
            
            # 檢查快取統計
            stats = get_engine_statistics(optimized_engine)
            cache_hit_rate = stats["cache_hit_rate"]
            
            @test cache_hit_rate > 0.3  # 至少 30% 命中率
            
            println("  快取命中率: $(round(cache_hit_rate * 100, digits=1))%")
        end
        
        @testset "多層快取效率" begin
            # 測試多層快取系統
            optimized_engine = OptimizedFilterEngine(medium_dataset)
            
            # 執行不同類型的計算
            for i in 1:20
                calculate_skip_optimized(optimized_engine, rand(1:10))
                if i % 3 == 0
                    calculate_pairing_frequency_optimized(optimized_engine, rand(1:10), rand(1:10))
                end
            end
            
            stats = get_engine_statistics(optimized_engine)
            
            # 檢查各層快取
            for cache_type in ["skip_cache", "ffg_cache", "pairing_cache"]
                if haskey(stats, cache_type)
                    cache_stats = stats[cache_type]
                    @test haskey(cache_stats, "hit_rate")
                    @test haskey(cache_stats, "items")
                    
                    println("  $cache_type 命中率: $(round(cache_stats["hit_rate"] * 100, digits=1))%")
                end
            end
        end
    end
    
    @testset "錯誤處理回歸測試" begin
        println("\n❌ 執行錯誤處理回歸測試...")
        
        @testset "輸入驗證" begin
            system = WonderGridSystem(small_dataset)
            
            # 測試無效號碼
            @test_throws ArgumentError calculate_skip(system, 0)
            @test_throws ArgumentError calculate_skip(system, 40)
            @test_throws ArgumentError calculate_pairing_frequency(system, 0, 1)
            @test_throws ArgumentError calculate_pairing_frequency(system, 1, 40)
            
            # 測試無效 Wonder Grid 大小
            @test_throws ArgumentError generate_wonder_grid(system, 0)
            @test_throws ArgumentError generate_wonder_grid(system, -1)
        end
        
        @testset "系統狀態錯誤" begin
            # 測試未初始化系統
            uninitialized_system = WonderGridSystem()
            
            @test_throws ErrorException calculate_skip(uninitialized_system, 1)
            @test_throws ErrorException calculate_pairing_frequency(uninitialized_system, 1, 2)
            @test_throws ErrorException generate_wonder_grid(uninitialized_system, 5)
            @test_throws ErrorException get_performance_report(uninitialized_system)
        end
        
        @testset "數據完整性錯誤" begin
            # 測試空數據集
            @test_throws ArgumentError WonderGridSystem(LotteryDraw[])
            
            # 測試無效數據
            invalid_draw = LotteryDraw([1, 1, 2, 3, 4], Date(2023, 1, 1), 1)  # 重複號碼
            @test_throws ArgumentError FilterEngine([invalid_draw])
        end
    end
    
    @testset "向後兼容性測試" begin
        println("\n🔄 執行向後兼容性測試...")
        
        @testset "API 兼容性" begin
            # 測試舊版本 API 仍然可用
            engine = FilterEngine(small_dataset)
            
            # 測試舊的過濾器函數
            @test isa(calculate_one_filter(engine, 1), FilterResult)
            @test isa(calculate_two_filter(engine, [1, 2, 3]), FilterResult)
            @test isa(calculate_three_filter(engine, [1, 2, 3, 4, 5]), FilterResult)
            
            # 測試舊的數據結構
            @test isa(engine.historical_data, Vector{LotteryDraw})
            @test isa(engine.historical_data[1], LotteryDraw)
        end
        
        @testset "配置兼容性" begin
            # 測試默認配置仍然有效
            system = WonderGridSystem(small_dataset)
            @test system.initialized == true
            
            # 測試自定義配置
            custom_config = Dict{String, Any}("performance" => Dict("enable_caching" => false))
            custom_system = WonderGridSystem(small_dataset, config=custom_config)
            @test custom_system.initialized == true
        end
    end
end

println("\n🎉 完整回歸測試完成！")

# 生成回歸測試報告
println("\n📊 生成回歸測試報告...")

try
    # 創建最終測試系統
    final_test_system = WonderGridSystem(medium_dataset)
    
    # 執行綜合測試
    println("🔍 執行最終綜合驗證...")
    
    # 功能測試
    skip_test = calculate_skip(final_test_system, 1)
    pairing_test = calculate_pairing_frequency(final_test_system, 1, 2)
    grid_test = generate_wonder_grid(final_test_system, 5)
    
    # 性能測試
    start_time = time()
    for i in 1:10
        calculate_skip(final_test_system, rand(1:10))
    end
    performance_time = (time() - start_time) * 1000
    
    # 記憶體測試
    memory_before = Base.gc_bytes()
    temp_system = WonderGridSystem(small_dataset)
    memory_after = Base.gc_bytes()
    memory_usage = (memory_after - memory_before) / (1024 * 1024)
    
    # 健康檢查
    health_status = system_health_check(final_test_system)
    
    # 性能報告
    performance_report = get_performance_report(final_test_system)
    
    println("\n📋 回歸測試總結報告:")
    println("=" ^ 50)
    println("✅ 功能測試: 全部通過")
    println("✅ 性能測試: $(round(performance_time, digits=2))ms (10次操作)")
    println("✅ 記憶體測試: $(round(memory_usage, digits=2))MB")
    println("✅ 健康檢查: $(health_status ? "通過" : "失敗")")
    println("✅ 系統整合: 完全兼容")
    println("✅ 向後兼容: 完全支援")
    
    if Threads.nthreads() > 1
        println("✅ 並行計算: 正常運行")
    else
        println("⚠️ 並行計算: 未測試（單執行緒環境）")
    end
    
    # 清理
    cleanup_system!(final_test_system)
    
    println("\n🎯 回歸測試結論: 系統完全通過所有回歸測試")
    println("系統已準備好進行生產發布！")
    
catch e
    println("❌ 回歸測試失敗: $e")
    println("需要修復問題後重新測試")
    rethrow(e)
end
