using WonderGridLotterySystem
using Statistics
using Dates

println("Testing Top Pairing Identification System")
println("=" ^ 50)

# Load lottery data
fm = FileManager()
data = read_data5_file(fm, "data/fan5.csv")
println("Loaded $(length(data)) lottery draws for top pairing analysis")

# Create pairing engine
engine = PairingEngine(data)
all_pairings = calculate_all_pairings(engine)

# Test different percentage thresholds
println("\nTesting Different Percentage Thresholds")
println("=" ^ 60)

test_number = 29  # Use number 29 as test case (high frequency)
percentages = [0.10, 0.25, 0.50, 0.75]

println("Analysis for Number $test_number:")
for percentage in percentages
    top_pairings = get_top_pairings(engine, test_number, percentage)
    expected_count = round(Int, 38 * percentage)  # 38 other numbers
    
    println("  Top $(Int(percentage * 100))% pairings:")
    println("    Expected count: ~$expected_count")
    println("    Actual count: $(length(top_pairings))")
    println("    Pairings: $(join(top_pairings[1:min(10, length(top_pairings))], ", "))$(length(top_pairings) > 10 ? "..." : "")")
    
    # Calculate frequency coverage
    total_freq = 0
    for paired_number in top_pairings
        pair_key = test_number < paired_number ? (test_number, paired_number) : (paired_number, test_number)
        freq = get(all_pairings, pair_key, 0)
        total_freq += freq
    end
    
    # Calculate total frequency for all pairings of this number
    all_freq = 0
    for other_num in 1:39
        if other_num != test_number
            pair_key = test_number < other_num ? (test_number, other_num) : (other_num, test_number)
            freq = get(all_pairings, pair_key, 0)
            all_freq += freq
        end
    end
    
    coverage = round(100 * total_freq / all_freq, digits=1)
    println("    Frequency coverage: $(coverage)%")
    println()
end

# Verify the 25% rule across multiple numbers
println("Verifying 25% Rule Across All Numbers")
println("=" ^ 60)

coverage_results = []
for number in 1:39
    top_25_pairings = get_top_pairings(engine, number, 0.25)
    
    # Calculate frequency coverage
    top_freq = 0
    for paired_number in top_25_pairings
        pair_key = number < paired_number ? (number, paired_number) : (paired_number, number)
        freq = get(all_pairings, pair_key, 0)
        top_freq += freq
    end
    
    # Calculate total frequency
    total_freq = 0
    for other_num in 1:39
        if other_num != number
            pair_key = number < other_num ? (number, other_num) : (other_num, number)
            freq = get(all_pairings, pair_key, 0)
            total_freq += freq
        end
    end
    
    coverage = total_freq > 0 ? 100 * top_freq / total_freq : 0.0
    push!(coverage_results, (number, coverage, length(top_25_pairings)))
end

# Sort by coverage percentage
sort!(coverage_results, by = x -> x[2], rev = true)

println("Top 15 Numbers by 25% Pairing Coverage:")
for i in 1:15
    number, coverage, count = coverage_results[i]
    println("  $i. Number $number: $(round(coverage, digits=1))% coverage with $count pairings")
end

println("\nBottom 10 Numbers by 25% Pairing Coverage:")
for i in 1:10
    number, coverage, count = coverage_results[end-i+1]
    println("  $i. Number $number: $(round(coverage, digits=1))% coverage with $count pairings")
end

# Statistical analysis of coverage
coverages = [x[2] for x in coverage_results]
println("\n25% Rule Coverage Statistics:")
println("  Mean coverage: $(round(mean(coverages), digits=1))%")
println("  Median coverage: $(round(median(coverages), digits=1))%")
println("  Min coverage: $(round(minimum(coverages), digits=1))%")
println("  Max coverage: $(round(maximum(coverages), digits=1))%")
println("  Standard deviation: $(round(std(coverages), digits=1))%")

# Test pairing quality ranking
println("\n" * "=" ^ 60)
println("Pairing Quality Ranking System")
println("=" ^ 60)

# Create a comprehensive ranking system
ranking_data = []

for number in 1:39
    top_10_pairings = get_top_pairings(engine, number, 0.10)
    top_25_pairings = get_top_pairings(engine, number, 0.25)
    
    # Calculate various metrics
    top_10_freq = 0
    top_25_freq = 0
    total_freq = 0
    max_pair_freq = 0
    
    for other_num in 1:39
        if other_num != number
            pair_key = number < other_num ? (number, other_num) : (other_num, number)
            freq = get(all_pairings, pair_key, 0)
            total_freq += freq
            
            if other_num in top_10_pairings
                top_10_freq += freq
            end
            if other_num in top_25_pairings
                top_25_freq += freq
            end
            
            max_pair_freq = max(max_pair_freq, freq)
        end
    end
    
    # Calculate quality metrics
    top_10_coverage = total_freq > 0 ? 100 * top_10_freq / total_freq : 0.0
    top_25_coverage = total_freq > 0 ? 100 * top_25_freq / total_freq : 0.0
    concentration_ratio = total_freq > 0 ? max_pair_freq / (total_freq / 38) : 0.0
    
    push!(ranking_data, (number, top_10_coverage, top_25_coverage, concentration_ratio, max_pair_freq, total_freq))
end

# Sort by top 25% coverage
sort!(ranking_data, by = x -> x[3], rev = true)

println("Top 10 Numbers for Wonder Grid Strategy (by 25% coverage):")
println("Rank | Number | Top10% | Top25% | Concentration | MaxPair | TotalFreq")
println("-" ^ 70)
for i in 1:10
    number, top10, top25, conc, max_pair, total = ranking_data[i]
    println("$(lpad(i, 4)) | $(lpad(number, 6)) | $(lpad(round(top10, digits=1), 6))% | $(lpad(round(top25, digits=1), 6))% | $(lpad(round(conc, digits=2), 13)) | $(lpad(max_pair, 7)) | $(lpad(total, 9))")
end

# Test edge cases
println("\n" * "=" ^ 60)
println("Edge Case Testing")
println("=" ^ 60)

# Test with very high percentage (should return most numbers)
test_high = get_top_pairings(engine, 1, 0.95)
println("Top 95% pairings for number 1: $(length(test_high)) numbers")
println("Expected: ~36 numbers (95% of 38)")

# Test with very low percentage (should return few numbers)
test_low = get_top_pairings(engine, 1, 0.05)
println("Top 5% pairings for number 1: $(length(test_low)) numbers")
println("Expected: ~2 numbers (5% of 38)")

# Test consistency across multiple calls
println("\nConsistency Test:")
for i in 1:5
    pairings = get_top_pairings(engine, 10, 0.25)
    println("  Call $i: $(length(pairings)) pairings, first 5: $(join(pairings[1:5], ", "))")
end

# Performance test for top pairing identification
println("\n" * "=" ^ 60)
println("Performance Analysis")
println("=" ^ 60)

# Test performance across different percentages
percentages_test = [0.10, 0.25, 0.50, 0.75]
for percentage in percentages_test
    start_time = time()
    for number in 1:39
        pairings = get_top_pairings(engine, number, percentage)
    end
    elapsed_time = time() - start_time
    
    println("Top $(Int(percentage * 100))% extraction for all 39 numbers: $(round(elapsed_time, digits=3))s")
    println("  Average per number: $(round(elapsed_time / 39 * 1000, digits=2))ms")
end

# Memory usage analysis
println("\nMemory Usage Analysis:")
sample_pairings = get_top_pairings(engine, 1, 0.25)
estimated_memory_per_result = length(sample_pairings) * sizeof(Int)
total_memory_estimate = 39 * estimated_memory_per_result
println("  Estimated memory per result: $(estimated_memory_per_result) bytes")
println("  Total memory for all 39 numbers: $(round(total_memory_estimate / 1024, digits=2)) KB")

println("\nTop Pairing Identification System testing completed successfully!")