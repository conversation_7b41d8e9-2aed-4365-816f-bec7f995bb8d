---
created: 2025-07-24T21:26:41 (UTC +08:00)
tags: [lottery,lotto,software,programs,odd,even,low,high,numbers,groups,]
source: https://forums.saliu.com/lotto-software-odd-even-low-high.html
author: 
---

# Lotto Software Program Odd Even Low High Number Groups

> ## Excerpt
> Many lottery players requested lotto software to generate combinations based on low, high, odd, even, or other groups of lotto numbers.

---
![The lotto numbers can be divided in low or high and odd or even.](https://forums.saliu.com/HLINE.gif)

### I. [Software for Lotto Groups in High Demand](https://forums.saliu.com/lotto-software-odd-even-low-high.html#lotto)  
II. [Generate Lotto Combinations from Groups of Numbers](https://forums.saliu.com/lotto-software-odd-even-low-high.html#numbers)  
III. [New Lotto Programs: _UserGroups_](https://forums.saliu.com/lotto-software-odd-even-low-high.html#programs)  
IV. [Upgrades to Lotto Group Software](https://forums.saliu.com/lotto-software-odd-even-low-high.html#updates)  
V. [The Famous Lotto Strategy of _Perfect Balance_](https://forums.saliu.com/lotto-software-odd-even-low-high.html#strategy)

![Lotto groups software is the first to accurately generate combinations from groups of numbers.](https://forums.saliu.com/HLINE.gif)

## <u>1. Software for Lotto Groups in High Demand</u>

_Ion, Axiomatic One,

I'm a member of your software download (different username but I can email you my data). I have a question regarding your lotto software. I know other lotto players want the same thing I want. My game is 6/49 but it should work with other lottos.

I got 2 or 3 or more groups of numbers. I want to make combinations from all groups with restrictions. e.g. 1 or 2 or 3 numbers from one group, mixed with 1 or 2 or 3 numbers from second group etc. I should have 6 lotto numbers in every combination.

Your lotto software mixes groups of numbers by <u>frequency</u> or by <u>decades</u> or by <u>odd/even</u>. The program makes automatically the groups of numbers. I saw here a free program in **python** to generate combos from groups of numbers.

But I want to create my <u>own groups of numbers</u>. I make a Notepad file with 2 lines or 3 lines or more lines. If I want to play odd numbers separated from even numbers in one case:

1 3 5 7 13 33 43 49 (could be all odd numbers in the first line)  
2 4 6 8 12 32 42 48 (could be all even numbers in the second line)

The program should generate e.g. 3 numbers from line 1 mixed with 3 numbers from line 2. Or 2 numbers from line 1 mixed with 4 numbers from line 2. Or 0 and 6, or 6 and 0.

I can write also 3 groups of numbers: <u>hot</u>, <u>warm</u>, <u>cold</u> (from your **FrequencyRank** program). Again, I tell the program to mix 2 numbers from line 1, 2 numbers from line 2, 2 numbers from line 3; or combine different amounts and get 6 numbers in every combination.

I'm aware there will be lots of combinations, thousands and thousands. I can wheel the lotto combinations with your **WheelIn** programs. Those programs are ready to go. You need to write new programs to generate combinations from groups of numbers created by users, not by the programs.

I think it would be nice on your part to write and add such programs to your **Bright** software (really is bright!) I suggest you add the programs rather than expecting registered users to pay extra for your custom programming. Some users will return the favor and tell other people about your lottery software. I've done so and I'll continue. I can email you some posts I wrote about your software, systems etc. in other places. Sometimes my posts are erased, other messages are still there.

_

_Thank you, Parpaluck._

![The lottery players can create their groups of numbers, not only high, low, even, odd.](https://forums.saliu.com/HLINE.gif)

## <u>2. Generate Lotto Combinations from Groups of Numbers</u>

Generate lotto combinations from groups of numbers — by far, the most frequent software request I receive. There are posts here, in my previous lottery forums, on my oldest message board (_saliu.com/bbs/_). Most requests are sent via email as proposals for custom software. Of course, I've never seen a payment in advance!

Truth is, I added that notice on my _What's New_ page mostly to discourage such requests. I am way too busy with serious stuff. All those requests for custom software are not serious. Nobody would pay a penny. They all want my custom programs first — but nobody would ever pay! I am not a fool to be taken for a ride by suckers! Besides, there is a serious legal issue with custom software. I do not offer exclusive rights for any custom program I write. I am still the copyright owner. A few thickos might accuse me of “stealing” their ideas!

Hey, this wasn't meant to offend anyone. But look at all these ideas of lotto software to generate combinations from <u>groups of numbers</u>. Who can claim copyrights for that concept? My lottery software has about a dozen functions that generate lotto combinations from _groups_ or _pools_.

The **SkipDecaFreq** programs generate lottery _combosnations_ from _odd/even, low/high, decades, frequency groups_. Then, the _Make/Break/Position_ in **Super Utilities** (or **Util**\* utilities software) generate combinations from a variety of number groups.

Then, it's only my lottery software that offers wheeling lottery combinations from output files. My **Wheel In** programs can reduce millions of lotto combinations to hundreds of combos (wheeling dramatically reduces the chance at the jackpot).

<u>II.</u>  
The type of lotto programs to generate combinations from a user's groups of numbers resembles the decades function in **SkipDecadesFrequency**. The difference: **Skip Deca(des) Freq(uency)** creates the lotto groups automatically (decades only).

It isn't that easy to write such software. There is a limited number of groups that can be used. Then, how many numbers from each group can be used. There is a maximum of 5 or 6 decades for lotto-6; up to 6 numbers from each decade can be used.

Lottery players want also different groups of frequencies. Like 4 groups of 12 numbers (13 for the last frequency group in 6/49). Then, generate lotto combinations restricted to exactly 2 numbers from group 1 (_hottest_), 2 numbers from group 2 (_warm_), 1 number from group 3 (_lukewarm_), and 1 number from group 4 (_cold_). The total would be 679536 combinations. Wheel them down to some hundreds _4 of 6 guarantee_ combinations. You'll hear many shouting: _“That was my idea!”_

How about 2 groups: Line 1 with odd numbers, line 2 with even numbers. Lotto 6-49 has 25 odd numbers and 24 even numbers. Total combos for 3 odds, 3 evens: C(25, 3) \* C(24, 3) = 4655200. Lots of them combinations even after wheeling!

<u>III.</u>  
Of course, I got source code to adapt to such type of lotto software. What I have much less is time. In addition to very important preoccupations, there is the heat wave that we have experienced in this area for the past month.

An even more serious problem is <u>spamming</u>. My forum has been the target of vicious spamming beginning June and continuing in July (2011). It took an effort on my part to fight off spamming.

I'll post here if/when this type of lotto programs become available.

![The lotto software to generate combinations as even odd low high is not free.](https://forums.saliu.com/HLINE.gif)

## <u>3. New Lotto Programs: UserGroups</u>

Finally! After hundreds of requests, I wrote this lotto program... and saved many lottery players hundreds of thousands of dollars! As I said, I asked for big money to write one custom program! But who was serious to give me real money? The suckers and pathological liars, huh?! NOT! Only they “took an oath” to pay me!

Of course, I wasn't serious myself. 99% of visitors did not believe I would ask for $1000 to $10,000(!) to write custom programs! Thus, many conscious guys waited patiently. As they say: _“Fortune rewards the patient ones.”_

**UserGroups6** generates lotto combinations from a user's groups of numbers. The user has full control over the groups and the amount of lotto numbers from each group. The program allows the user to generate 6-number lotto combinations from his/her groups of lotto numbers; e.g. _even/odd_, or _low/high_, etc. The number of groups for lotto-6: from 2 to a maximum of 6 groups.

The program can also create some groups of numbers for the player: _odd/even, low/high, frequency groups_, and all lotto numbers in one line (e.g. from 1 to 49). The software user can select how many numbers from each group to become part of every combination; the numbers selected must add up to 6. Also, all lotto numbers in the game must appear in the file; e.g. the group file must have all 49 numbers of a 6/49 game.

Here is the main menu:

![The menu of the only lottery software program to generate combinations from user groups of numbers.](https://forums.saliu.com/UserGroups6.gif)

A short presentation follows. The registered users don't have to pay a penny for this program. At least, they can test it quickly and make good observations right here.

<u>II.</u>  
The most attractive function of the program is: _Create Your Own Groups of Numbers_. This is a very easy task. You type your lotto numbers on separate lines and save them to a text file.

\* A 6-number lotto game can have a minimum of 2 groups and a maximum of 6 groups. Thus, your number-group file will consist of 2, or 3, or 4, or 5, or 6 lines. Following is a sample file for a 6-49 lotto game: Freq6-49.txt. The file consists of 6 groups of lotto numbers ranked by frequency, from the hottest numbers (43, 48, 13, etc.) to the coldest (9, 23, 30, 47).

43 48 13 16 4 37 35 2  
28 33 10 32 19 7 42 5  
36 8 11 12 34 24 38 29  
41 26 31 45 1 21 18 49  
17 14 39 25 22 3 6 46  
15 44 40 20 27 9 23 30 47

It is best to separate the numbers by a blank space.

\*\* There is another important requirement. Your group file must contain ALL the numbers in your lotto game. For example, a 6/49 game must contain 49 numbers in the text file with your groups.

Let's say you want to play only 18 numbers, 9 odd and 9 even. You want to generate all lotto combinations and then wheel in the lines (with WheelIn or _**Super Utilities**_, _W = Duplicates: Strip & Wheel_). You load first the text file this program can create for you: OddEven49.txt.

**1 3 5 7 9 11 13 15 17 19 21 23 25 27 29 31 33 35 37 39 41 43 45 47 49  
2 4 6 8 10 12 14 16 18 20 22 24 26 28 30 32 34 36 38 40 42 44 46 48**

Press _Ctrl+Home_ to be sure you are at the top of the file. Press _Enter_ twice to create 2 blank lines. You already know what odd and what even numbers to play. Cut your odd numbers, one at a time, from the corresponding line and paste them to the 1st blank line. Do the same for the even numbers. Save As your new file to something like _OddEven18.txt_, will look exactly like this:

1 5 7 13 23 25 33 41 47  
6 8 14 16 18 26 30 38 48  
3 9 11 15 17 19 21 27 29 31 35 37 39 43 45 49  
2 4 10 12 20 22 24 28 32 34 36 40 42 44 46

You have all 49 lotto numbers in that file. You want to play 3 odds and 3 evens. When you generate the combinations, you will type the following at the corresponding prompts (_"How many numbers from group # n"_): 3, 3, 0, 0.

You can also create another file based on lotto number frequency or number skips. That file will have, say, 11 odd numbers and 7 even numbers. You want to play 4 odds and 2 evens: 4, 2, 0, 0.

The computer can create also a single-line file with all the numbers in your game; e.g. from 1 to 49, in sequence (AllNumbers49.txt). This program does not allow playing from one group of lotto numbers. Again, you can add a blank line at the top of the file. Cut and paste only the numbers you want to play and paste them to the top line. Say, you pasted another 18 numbers you want to play. Save the new file as Singles18.txt. Now you'll play 6 numbers from line #1 and no numbers (0) from line 2. Type at the corresponding prompts: 6, 0.

Most lottery players will wheel the lotto combinations generated. I tested for myself. The 3, 3, 0, 0 odd-and-even-even situation generated 7056 combosnations (for 18 lotto numbers). I shuffled the output file with _**Shuffle**_ (_Files, Vertically_). Then, I input the randomized (shuffled) file to _**Super Utilities**_. I wheel-stripped the file to some 60 combinations. It is a pretty good _4 of 6 guarantee_ lotto wheel, after only one shuffle.

These lotto programs are not offered as standalone any longer. They are components of the **Bright5** and **Bright6** lotto software packages: _Menu #2, option G = Work with User's Number Groups_.

By the way –

This program and its sibling for 5-number lotto will not be offered as standalone. Instead, they will be part of **Bright / <u>Ultimate</u>**. I must update the **Bright / <u>Ultimate</u>** packages accordingly. Not to mention that I discovered something in the **Super Utilities**. The latest version could trigger errors in _Make/Break/Position_. The options 1 and 2 of _Break strings of 5+/6+ numbers_ worked correctly before. Now, it triggers an error if the strings of numbers are of different lengths (e.g. 12, 7, 9, 10, etc.) No errors if the lines have equal amounts of lotto numbers.

I sez: _"No malice!"_ And you say: _"No hard feelings, axiomaticule!"_

I made some changes just before compiling **User Groups** and uploading. The G function, the most important one, did not work with the default (_Count Combinations Only_). But it worked OK with generating the lotto _combosnations_ (a favorite term in my _lottospeak_) to file. I uploaded the correct **EXE** file.

As an example, _3 odds + 3 evens_ file generates 4655200 lotto 6/49 combinations. But you can easily eliminate millions by employing just the _Del\_6_ filter. Then, a lotto wheel will be that much tighter, while preserving the guarantee with a high degree of certainty.

**<u>Error-Free New Programs for 5 -, 6-Number Lotto Games</u>**

Okay! One more time! **User Groups6** did NOT generate lotto combinations to file! I know, there is that subconscious thing that appears to raise obstacles on my way sometimes.

But I know for sure there were other things while I wrote the program. I had the worst system errors in several years. The errors were created while compiling the software. All Windows security utilities warned me several times of some threats. I did clean my PC a few times. Even just now, when I tried first to upload this new lotto software, I was unable to logon my FTP site. It's not only because the weather threatens to be stormy!

To lighten up, I uploaded also a similar program for lotto 5. This time, I tested both programs several times with all kinds of number groups. The combinations were counted correctly and they were saved to disk correctly.

Again, these lotto programs are no longer offered as standalone. They are components of the **Bright5** and **Bright6** lotto software suites: _Menu #2, option G = Work with User's Number Groups_.

![The number group software is component of the powerful Bright applications.](https://forums.saliu.com/HLINE.gif)

## <u>4. Upgrades to Yet Unreleased Software</u>

There was a delay in the release of the **User Groups** lotto programs. I added new features. The new lottery software is now available and error-free. It works like a charm. This new type of lotto software creates the perfect candidates for the LIE Elimination — a formidable feature in my lottery software.

![The software for groups of lottery numbers works with lotto 5 and lotto 6.](https://forums.saliu.com/UserGroups5-1.gif)

There is a report like the W function in the **Bright / <u>Ultimate</u>** software apps. It generates winning reports like this:

![The software for groups of lottery numbers works with lotto 5 and lotto 6.](https://forums.saliu.com/sums-odd-even.gif)

![The software for groups of lottery numbers works with lotto 5 and lotto 6.](https://forums.saliu.com/groups-stats.gif)

The default _parpaluck_ is now calculated by _**FFG**_ (e.g. 6 for 5-43 lotto). Also, the recommended next best _parpalucks_ are (default \* 2) and (default \* 3). They show a larger amount of 5 (or 6) in the frequency columns. That means a tremendous reduction of lotto combinations. Larger _parpalucks_ simply smooth the curve.

The combination-generating modules are the most difficult to code. But I have come up with new algorithms. Even more potential patents …

**<u>Important Updates to Lotto Software for <i>Low High, Odd Even, Sums, Frequency Groups</i></u>**

<big>UserGroups5</big> ~ version 3.0 ~ September 2011;  
<big>UserGroups6</big> ~ version 3.0 ~ September 2011.

These lotto programs are not offered as standalone. They are components of the **Bright 5** and **Bright 6** lotto software suites: _Menu #2, option G = Work with User's Number Groups_.

The latest versions add two very important functions:

1) <u>Check Strategy</u>  
2) <u>Strategy Hits in the Past</u>

For more read:

-   [_**Lotto Software for Groups of Numbers: Odd, Even, Low, High, Sums, Frequencies, User's Groups**_](https://saliu.com/lotto-groups.html).

![This is the best known strategy played by many lotto players – based on balance.](https://forums.saliu.com/HLINE.gif)

## <u>5. The Famous Lotto Strategy of Perfect Balance</u>

This appears to be the most famous "lotto strategy": _The perfect balance of the 6 numbers in a combination_. I placed the negative quotation marks because such a strategy is based on _static_ filters. You can see it on every lottery site (except for SALIU.COM). All them "lotto gurus" advise very loudly: _"Play BALANCED lotto combinations. Play balanced odd and even numbers, low and high numbers, plus balanced sum-totals!"_

The lotto strategy would be:

**(3 odd + 3 even) AND (3 low + 3 high) AND (sum-totals between 120 – 170)**.

Problem is, such a strategy always generates the same amount of lotto combinations because of the _static_ filters. Another problem, the lotto strategy has <u>skips</u>. The lottery gurus are unable to tell users what the <u>skips</u> are!

The static lottery strategies (such the one above) are better suited for the _LIE Elimination_ feature in the **Bright / <u>Ultimate</u>** software apps.

I did a test of static lotto strategies with my new type of lotto software (**User Groups**). I analyzed Pennsylvania's 6/49 lotto game for (_3 odd + 3 even_) AND (_3 low + 3 high_). The strategy recorded 61 hits in 500 drawings. Obviously, the results will be virtually the same (mathematically speaking) for any lotto game. I post the strategy at the end of this message.

I added the _frequency_ filters, which are _dynamic_. They certainly reduce the amount of combinations dramatically (but also reduce the number of hits). The _static_ filters are the same:

(_3 odd + 3 even_) AND (_3 low + 3 high_) AND (_sum-totals between 120 – 170_)

As such, that “lotto strategy” always generates 1,308,743 combinations in _6 of 49_ lotto.

-   If I add the 2-frequency groups 5 + 1, the program generates 104,572 6/49 lotto combinations.
-   If I add the 3-frequency groups 5 + 0 + 1, the program generates 7,058 lotto _6 of 49_ combinations.

![The software for groups of lottery numbers works with lotto 5 and lotto 6.](https://forums.saliu.com/groups-strategy.gif)

![Download your lotto software to generate combinations of numbers – odd or even, low or high.](https://forums.saliu.com/HLINE.gif)

 **[![Forums for gambling software, strategy, systems, winning in real life casinos like in Atlantic City.](https://forums.saliu.com/go-back.gif) Back to Forums Index](https://forums.saliu.com/index.html)        [Socrates Home](https://saliu.com/index.htm)  [Search](https://saliu.com/Search.htm)**

![Exit the best site of software, programs, strategies, mathematics of lotto for the world.](https://forums.saliu.com/HLINE.gif)
